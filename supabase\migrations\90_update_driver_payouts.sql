-- Update driver_payouts table to add payout_type field
ALTER TABLE driver_payouts
ADD COLUMN IF NOT EXISTS payout_type TEXT CHECK (payout_type IN ('amount', 'percentage')),
ADD COLUMN IF NOT EXISTS percentage DECIMAL(5, 2);

-- Set default payout_type for existing records
UPDATE driver_payouts SET payout_type = 'amount' WHERE payout_type IS NULL;

COMMENT ON COLUMN driver_payouts.payout_type IS 'Type of payout: fixed amount or percentage of fare';
COMMENT ON COLUMN driver_payouts.percentage IS 'Percentage value if payout_type is percentage';

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_driver_payouts_driver_id ON driver_payouts(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_payouts_ride_id ON driver_payouts(ride_id);
CREATE INDEX IF NOT EXISTS idx_driver_payouts_status ON driver_payouts(status);
