{"ast": null, "code": "export { FunctionsClient } from './FunctionsClient';\nexport { FunctionsError, FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';", "map": {"version": 3, "names": ["FunctionsClient", "FunctionsError", "FunctionsFetchError", "FunctionsHttpError", "FunctionsRelayError", "FunctionRegion"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/functions-js/dist/module/index.js"], "sourcesContent": ["export { FunctionsClient } from './FunctionsClient';\nexport { FunctionsError, FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion, } from './types';\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,cAAc,QAAS,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}