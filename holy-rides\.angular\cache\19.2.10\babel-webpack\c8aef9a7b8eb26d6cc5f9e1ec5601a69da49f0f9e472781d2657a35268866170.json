{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (yield import('@supabase/node-fetch')).Response;\n  }\n  return Response;\n});\nexport const recursiveToCamel = item => {\n  if (Array.isArray(item)) {\n    return item.map(el => recursiveToCamel(el));\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item;\n  }\n  const result = {};\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, c => c.toUpperCase().replace(/[-_]/g, ''));\n    result[newKey] = recursiveToCamel(value);\n  });\n  return result;\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "resolveFetch", "customFetch", "_fetch", "fetch", "args", "default", "resolveResponse", "Response", "recursiveToCamel", "item", "Array", "isArray", "map", "el", "Object", "entries", "for<PERSON>ach", "key", "new<PERSON>ey", "replace", "c", "toUpperCase"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/lib/helpers.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nexport const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n    if (typeof Response === 'undefined') {\n        // @ts-ignore\n        return (yield import('@supabase/node-fetch')).Response;\n    }\n    return Response;\n});\nexport const recursiveToCamel = (item) => {\n    if (Array.isArray(item)) {\n        return item.map((el) => recursiveToCamel(el));\n    }\n    else if (typeof item === 'function' || item !== Object(item)) {\n        return item;\n    }\n    const result = {};\n    Object.entries(item).forEach(([key, value]) => {\n        const newKey = key.replace(/([-_][a-z])/gi, (c) => c.toUpperCase().replace(/[-_]/g, ''));\n        result[newKey] = recursiveToCamel(value);\n    });\n    return result;\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,OAAO,MAAMO,YAAY,GAAIC,WAAW,IAAK;EACzC,IAAIC,MAAM;EACV,IAAID,WAAW,EAAE;IACbC,MAAM,GAAGD,WAAW;EACxB,CAAC,MACI,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACnCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KAAK,MAAM,CAAC,sBAAsB,CAAC,CAACN,IAAI,CAAC,CAAC;MAAEO,OAAO,EAAEF;IAAM,CAAC,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;EACrG,CAAC,MACI;IACDF,MAAM,GAAGC,KAAK;EAClB;EACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACvC,CAAC;AACD,OAAO,MAAME,eAAe,GAAGA,CAAA,KAAMzB,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAChF,IAAI,OAAO0B,QAAQ,KAAK,WAAW,EAAE;IACjC;IACA,OAAO,CAAC,MAAM,MAAM,CAAC,sBAAsB,CAAC,EAAEA,QAAQ;EAC1D;EACA,OAAOA,QAAQ;AACnB,CAAC,CAAC;AACF,OAAO,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;EACtC,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;IACrB,OAAOA,IAAI,CAACG,GAAG,CAAEC,EAAE,IAAKL,gBAAgB,CAACK,EAAE,CAAC,CAAC;EACjD,CAAC,MACI,IAAI,OAAOJ,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAKK,MAAM,CAACL,IAAI,CAAC,EAAE;IAC1D,OAAOA,IAAI;EACf;EACA,MAAMb,MAAM,GAAG,CAAC,CAAC;EACjBkB,MAAM,CAACC,OAAO,CAACN,IAAI,CAAC,CAACO,OAAO,CAAC,CAAC,CAACC,GAAG,EAAE9B,KAAK,CAAC,KAAK;IAC3C,MAAM+B,MAAM,GAAGD,GAAG,CAACE,OAAO,CAAC,eAAe,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACxFvB,MAAM,CAACsB,MAAM,CAAC,GAAGV,gBAAgB,CAACrB,KAAK,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOS,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}