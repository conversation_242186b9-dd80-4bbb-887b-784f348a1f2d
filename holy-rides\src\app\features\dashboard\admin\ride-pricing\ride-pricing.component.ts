import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';

import { RidePricingService } from '../../../../core/services/ride-pricing.service';
import { RidePricing } from '../../../../core/models/ride-pricing.model';

@Component({
  selector: 'app-ride-pricing',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatDividerModule
  ],
  templateUrl: './ride-pricing.component.html',
  styleUrls: ['./ride-pricing.component.scss']
})
export class RidePricingComponent implements OnInit {
  pricingForm: FormGroup;
  pricingConfigurations: RidePricing[] = [];
  displayedColumns: string[] = ['name', 'base_fare', 'distance_rate', 'time_rate', 'is_active', 'actions'];
  loading = false;
  activePricing: RidePricing | null = null;
  editMode = false;
  editingId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private ridePricingService: RidePricingService,
    private snackBar: MatSnackBar
  ) {
    this.pricingForm = this.fb.group({
      name: ['', [Validators.required]],
      base_fare: [5.00, [Validators.required, Validators.min(0)]],
      distance_rate: [1.50, [Validators.required, Validators.min(0)]],
      time_rate: [0.25, [Validators.required, Validators.min(0)]],
      is_active: [true]
    });
  }

  ngOnInit(): void {
    this.loadPricingConfigurations();
  }

  async loadPricingConfigurations(): Promise<void> {
    this.loading = true;
    try {
      this.pricingConfigurations = await this.ridePricingService.getAllPricing();
      this.activePricing = await this.ridePricingService.loadActivePricing();
    } catch (error) {
      console.error('Error loading pricing configurations:', error);
      this.snackBar.open('Failed to load pricing configurations', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  async onSubmit(): Promise<void> {
    if (this.pricingForm.invalid) {
      return;
    }

    this.loading = true;
    try {
      const formValue = this.pricingForm.value;

      if (this.editMode && this.editingId) {
        // Update existing pricing
        await this.ridePricingService.updatePricing(this.editingId, formValue);
        this.snackBar.open('Pricing configuration updated successfully', 'Close', { duration: 3000 });
      } else {
        // Create new pricing
        await this.ridePricingService.createPricing(formValue);
        this.snackBar.open('Pricing configuration created successfully', 'Close', { duration: 3000 });
      }

      // Reset form and reload data
      this.resetForm();
      await this.loadPricingConfigurations();
    } catch (error) {
      console.error('Error saving pricing configuration:', error);
      this.snackBar.open('Failed to save pricing configuration', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  editPricing(pricing: RidePricing): void {
    this.editMode = true;
    this.editingId = pricing.id;
    this.pricingForm.patchValue({
      name: pricing.name,
      base_fare: pricing.base_fare,
      distance_rate: pricing.distance_rate,
      time_rate: pricing.time_rate,
      is_active: pricing.is_active
    });
  }

  resetForm(): void {
    this.editMode = false;
    this.editingId = null;
    this.pricingForm.reset({
      name: '',
      base_fare: 5.00,
      distance_rate: 1.50,
      time_rate: 0.25,
      is_active: true
    });
  }

  async toggleActive(pricing: RidePricing): Promise<void> {
    this.loading = true;
    try {
      await this.ridePricingService.setActiveStatus(pricing.id, !pricing.is_active);
      await this.loadPricingConfigurations();
      this.snackBar.open(`Pricing configuration ${!pricing.is_active ? 'activated' : 'deactivated'} successfully`, 'Close', { duration: 3000 });
    } catch (error) {
      console.error('Error toggling active status:', error);
      this.snackBar.open('Failed to update pricing configuration', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  async deletePricing(id: string): Promise<void> {
    if (!confirm('Are you sure you want to delete this pricing configuration?')) {
      return;
    }

    this.loading = true;
    try {
      await this.ridePricingService.deletePricing(id);
      await this.loadPricingConfigurations();
      this.snackBar.open('Pricing configuration deleted successfully', 'Close', { duration: 3000 });
    } catch (error) {
      console.error('Error deleting pricing configuration:', error);
      this.snackBar.open('Failed to delete pricing configuration', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  calculateSampleFare(): number {
    const baseFare = this.pricingForm.get('base_fare')?.value || 0;
    const distanceRate = this.pricingForm.get('distance_rate')?.value || 0;
    const timeRate = this.pricingForm.get('time_rate')?.value || 0;
    
    // Sample calculation for a 10-mile, 20-minute ride
    const sampleDistance = 10;
    const sampleDuration = 20;
    
    return +(baseFare + (sampleDistance * distanceRate) + (sampleDuration * timeRate)).toFixed(2);
  }
}
