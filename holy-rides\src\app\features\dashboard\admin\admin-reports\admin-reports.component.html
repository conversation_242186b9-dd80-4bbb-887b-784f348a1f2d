<div class="reports-container">
  <!-- Report Controls -->
  <mat-card class="controls-card">
    <mat-card-header>
      <mat-card-title>Report Controls</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="controls-grid">
        <mat-form-field appearance="outline">
          <mat-label>Report Type</mat-label>
          <mat-select [(ngModel)]="reportType" (selectionChange)="generateReport()">
            <mat-option value="rides">Ride Statistics</mat-option>
            <mat-option value="revenue">Revenue Statistics</mat-option>
            <mat-option value="users">User Activity</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Start Date</mat-label>
          <input matInput [matDatepicker]="startPicker" [(ngModel)]="startDate">
          <mat-datepicker-toggle matIconSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>End Date</mat-label>
          <input matInput [matDatepicker]="endPicker" [(ngModel)]="endDate">
          <mat-datepicker-toggle matIconSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline" *ngIf="reportType === 'users'">
          <mat-label>User Role</mat-label>
          <mat-select [(ngModel)]="userRole">
            <mat-option value="">All Roles</mat-option>
            <mat-option value="rider">Riders</mat-option>
            <mat-option value="driver">Drivers</mat-option>
            <mat-option value="admin">Admins</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" *ngIf="reportType === 'rides'">
          <mat-label>Ride Status</mat-label>
          <mat-select [(ngModel)]="rideStatus">
            <mat-option value="">All Statuses</mat-option>
            <mat-option value="requested">Requested</mat-option>
            <mat-option value="assigned">Assigned</mat-option>
            <mat-option value="in-progress">In Progress</mat-option>
            <mat-option value="completed">Completed</mat-option>
            <mat-option value="canceled">Canceled</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="button-container">
          <button mat-raised-button color="primary" (click)="generateReport()">
            <mat-icon>refresh</mat-icon>
            Generate Report
          </button>
          <button mat-raised-button color="accent" (click)="exportReport()" [disabled]="!rideReport && !revenueReport && !userReport">
            <mat-icon>download</mat-icon>
            Export CSV
          </button>
          <button mat-raised-button (click)="printReport()" [disabled]="!rideReport && !revenueReport && !userReport">
            <mat-icon>print</mat-icon>
            Print
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Generating report...</p>
  </div>

  <!-- Ride Reports -->
  <div *ngIf="rideReport && !loading" class="report-content">
    <h2>Ride Statistics Report</h2>
    <p class="date-range">
      {{ startDate | date:'mediumDate' }} to {{ endDate | date:'mediumDate' }}
    </p>

    <div class="charts-grid">
      <!-- Ride Status Chart -->
      <app-chart-display
        [chartType]="'pie'"
        [chartData]="rideStatusChartData"
        [chartLabels]="rideStatusChartLabels"
        [chartTitle]="'Rides by Status'"
        [chartLegend]="true">
      </app-chart-display>

      <!-- Rides by Day Chart -->
      <app-chart-display
        [chartType]="'line'"
        [chartData]="ridesByDayChartData"
        [chartLabels]="ridesByDayChartLabels"
        [chartTitle]="'Rides per Day'"
        [chartLegend]="true">
      </app-chart-display>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Total Rides</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">{{ rideReport.ridesByStatus.requested + rideReport.ridesByStatus.assigned + rideReport.ridesByStatus.inProgress + rideReport.ridesByStatus.completed + rideReport.ridesByStatus.canceled }}</div>
        </mat-card-content>
      </mat-card>

      <mat-card>
        <mat-card-header>
          <mat-card-title>Average Distance</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">{{ rideReport.averageDistance.toFixed(2) }} miles</div>
        </mat-card-content>
      </mat-card>

      <mat-card>
        <mat-card-header>
          <mat-card-title>Average Duration</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">{{ rideReport.averageDuration.toFixed(2) }} minutes</div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Revenue Reports -->
  <div *ngIf="revenueReport && !loading" class="report-content">
    <h2>Revenue Statistics Report</h2>
    <p class="date-range">
      {{ startDate | date:'mediumDate' }} to {{ endDate | date:'mediumDate' }}
    </p>

    <div class="charts-grid">
      <!-- Revenue by Day Chart -->
      <app-chart-display
        [chartType]="'line'"
        [chartData]="revenueByDayChartData"
        [chartLabels]="revenueByDayChartLabels"
        [chartTitle]="'Revenue per Day'"
        [chartLegend]="true">
      </app-chart-display>

      <!-- Revenue by Status Chart -->
      <app-chart-display
        [chartType]="'pie'"
        [chartData]="revenueByStatusChartData"
        [chartLabels]="revenueByStatusChartLabels"
        [chartTitle]="'Revenue by Payment Status'"
        [chartLegend]="true">
      </app-chart-display>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Total Revenue</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">${{ revenueReport.totalRevenue.toFixed(2) }}</div>
        </mat-card-content>
      </mat-card>

      <mat-card>
        <mat-card-header>
          <mat-card-title>Paid Revenue</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">${{ revenueReport.revenueByStatus.paid.toFixed(2) }}</div>
        </mat-card-content>
      </mat-card>

      <mat-card>
        <mat-card-header>
          <mat-card-title>Pending Revenue</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">${{ revenueReport.revenueByStatus.pending.toFixed(2) }}</div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Top Drivers Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>Top Drivers by Revenue</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <table mat-table [dataSource]="revenueReport.topDriversByRevenue" class="mat-elevation-z2">
          <!-- Driver Name Column -->
          <ng-container matColumnDef="driverName">
            <th mat-header-cell *matHeaderCellDef>Driver</th>
            <td mat-cell *matCellDef="let driver">{{ driver.driverName }}</td>
          </ng-container>

          <!-- Revenue Column -->
          <ng-container matColumnDef="revenue">
            <th mat-header-cell *matHeaderCellDef>Revenue</th>
            <td mat-cell *matCellDef="let driver">${{ driver.revenue.toFixed(2) }}</td>
          </ng-container>

          <!-- Ride Count Column -->
          <ng-container matColumnDef="rideCount">
            <th mat-header-cell *matHeaderCellDef>Rides</th>
            <td mat-cell *matCellDef="let driver">{{ driver.rideCount }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="topDriversColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: topDriversColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- User Activity Reports -->
  <div *ngIf="userReport && !loading" class="report-content">
    <h2>User Activity Report</h2>
    <p class="date-range">
      {{ startDate | date:'mediumDate' }} to {{ endDate | date:'mediumDate' }}
    </p>

    <div class="charts-grid">
      <!-- Users by Day Chart -->
      <app-chart-display
        [chartType]="'line'"
        [chartData]="usersByDayChartData"
        [chartLabels]="usersByDayChartLabels"
        [chartTitle]="'User Activity per Day'"
        [chartLegend]="true">
      </app-chart-display>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
      <mat-card>
        <mat-card-header>
          <mat-card-title>New Users</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">{{ userReport.newUsers.total }}</div>
          <div class="summary-details">
            <span>Riders: {{ userReport.newUsers.riders }}</span>
            <span>Drivers: {{ userReport.newUsers.drivers }}</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card>
        <mat-card-header>
          <mat-card-title>Active Users</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="summary-value">{{ userReport.activeUsers.total }}</div>
          <div class="summary-details">
            <span>Riders: {{ userReport.activeUsers.riders }}</span>
            <span>Drivers: {{ userReport.activeUsers.drivers }}</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Top Riders Table -->
    <mat-card class="table-card">
      <mat-card-header>
        <mat-card-title>Top Riders by Activity</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <table mat-table [dataSource]="userReport.topRiders" class="mat-elevation-z2">
          <!-- Rider Name Column -->
          <ng-container matColumnDef="riderName">
            <th mat-header-cell *matHeaderCellDef>Rider</th>
            <td mat-cell *matCellDef="let rider">{{ rider.riderName }}</td>
          </ng-container>

          <!-- Ride Count Column -->
          <ng-container matColumnDef="rideCount">
            <th mat-header-cell *matHeaderCellDef>Rides</th>
            <td mat-cell *matCellDef="let rider">{{ rider.rideCount }}</td>
          </ng-container>

          <!-- Total Spent Column -->
          <ng-container matColumnDef="totalSpent">
            <th mat-header-cell *matHeaderCellDef>Total Spent</th>
            <td mat-cell *matCellDef="let rider">${{ rider.totalSpent.toFixed(2) }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="topRidersColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: topRidersColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- No Data Message -->
  <div *ngIf="!loading && !rideReport && !revenueReport && !userReport" class="no-data">
    <mat-icon>info</mat-icon>
    <p>No report data available. Please generate a report.</p>
  </div>
</div>
