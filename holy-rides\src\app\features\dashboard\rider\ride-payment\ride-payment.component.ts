import { Component, Input, OnInit, AfterViewInit, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { Ride } from '../../../../core/models/ride.model';
import { PaymentService } from '../../../../core/services/payment.service';
import { RideService } from '../../../../core/services/ride.service';
import { environment } from '../../../../../environments/environment';
import { loadStripe, StripeConstructor } from '@stripe/stripe-js';
import { AuthService } from '../../../../core/services/auth.service';

declare global {
  interface Window {
    Stripe?: StripeConstructor;
  }
}

@Component({
  selector: 'app-ride-payment',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatDividerModule
  ],
  template: `
    <mat-card *ngIf="ride">
      <mat-card-header>
        <mat-card-title>Ride Payment</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="payment-details">
          <div class="detail-row">
            <span class="label">Pickup:</span>
            <span class="value">{{ ride.pickup_location }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Destination:</span>
            <span class="value">{{ ride.dropoff_location }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Date:</span>
            <span class="value">{{ ride.pickup_time | date:'medium' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Status:</span>
            <span class="value status-badge" [ngClass]="'status-' + ride.status">{{ ride.status }}</span>
          </div>
          <div class="detail-row">
            <span class="label">Payment Status:</span>
            <span class="value status-badge" [ngClass]="'payment-' + (ride.payment_status || 'pending')">
              {{ ride.payment_status || 'pending' }}
            </span>
          </div>
          <div class="detail-row amount">
            <span class="label">Amount:</span>
            <span class="value">{{ '$' + getDisplayAmount() }}</span>
          </div>
        </div>

        <mat-divider *ngIf="canPay()" class="section-divider"></mat-divider>

        <div *ngIf="canPay() && !sdkLoaded" class="sdk-status">
          <p>Loading payment form...</p>
          <mat-spinner diameter="30"></mat-spinner>
        </div>

        <div *ngIf="canPay() && sdkLoaded" class="stripe-payment-form">
          <h3>Payment Information</h3>
          <p class="payment-instruction">Please enter your card details to complete the payment.</p>

          <div #cardElement class="card-element"></div>
          <div class="card-errors" *ngIf="cardError">{{ cardError }}</div>

          <div class="payment-actions">
            <button mat-raised-button color="primary"
                    [disabled]="processing || !cardComplete"
                    (click)="processPayment()">
              <mat-icon>payment</mat-icon>
              {{ processing ? 'Processing...' : 'Pay Now' }}
            </button>

            <div *ngIf="processing" class="processing-indicator">
              <mat-spinner diameter="24"></mat-spinner>
              <span>Processing your payment...</span>
            </div>
          </div>
        </div>

        <div class="payment-actions" *ngIf="canRequestRefund()">
          <button mat-raised-button color="warn"
                  [disabled]="processing"
                  (click)="requestRefund()">
            <mat-icon>money_off</mat-icon>
            {{ processing ? 'Processing...' : 'Request Refund' }}
          </button>

          <div *ngIf="processing" class="processing-indicator">
            <mat-spinner diameter="24"></mat-spinner>
            <span>Processing your request...</span>
          </div>
        </div>

        <div class="payment-result" *ngIf="paymentResult">
          <h3>Payment Result</h3>
          <div [ngClass]="paymentResult.success ? 'success-message' : 'error-message'">
            {{ paymentResult.success ? 'Payment successful!' : 'Payment failed: ' + paymentResult.error?.message }}
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    :host {
      display: block;
      margin: 20px;
    }

    .payment-details {
      margin-bottom: 20px;
    }

    .detail-row {
      display: flex;
      margin-bottom: 8px;
      align-items: center;
    }

    .label {
      font-weight: 500;
      width: 120px;
      color: rgba(0, 0, 0, 0.6);
    }

    .value {
      flex: 1;
    }

    .amount {
      font-size: 1.2em;
      font-weight: 500;
      margin-top: 16px;
    }

    .amount .value {
      color: #3f51b5;
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      text-transform: capitalize;
      font-size: 0.9em;
    }

    .status-requested {
      background-color: #ffeb3b;
      color: #000;
    }

    .status-assigned {
      background-color: #2196f3;
      color: white;
    }

    .status-in-progress {
      background-color: #ff9800;
      color: white;
    }

    .status-completed {
      background-color: #4caf50;
      color: white;
    }

    .status-canceled {
      background-color: #f44336;
      color: white;
    }

    .payment-pending {
      background-color: #ffeb3b;
      color: #000;
    }

    .payment-paid, .payment-completed {
      background-color: #4caf50;
      color: white;
    }

    .payment-failed {
      background-color: #f44336;
      color: white;
    }

    .payment-refunded {
      background-color: #9e9e9e;
      color: white;
    }

    .payment-actions {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
      margin-top: 16px;
    }

    .processing-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
    }

    .section-divider {
      margin: 24px 0;
    }

    .stripe-payment-form {
      margin-top: 24px;
    }

    .payment-instruction {
      margin-bottom: 16px;
      color: rgba(0, 0, 0, 0.6);
    }

    .card-element {
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 12px;
      background-color: white;
      margin-bottom: 16px;
    }

    .card-errors {
      color: #f44336;
      font-size: 0.9em;
      margin-bottom: 16px;
    }

    .sdk-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      margin: 24px 0;
    }

    .payment-result {
      margin-top: 24px;
      padding: 16px;
      border-radius: 4px;
      background-color: #f5f5f5;
    }

    .success-message {
      color: #4caf50;
      font-weight: 500;
    }

    .error-message {
      color: #f44336;
      font-weight: 500;
    }
  `]
})
export class RidePaymentComponent implements OnInit, AfterViewInit {
  @Input() ride!: Ride;
  @ViewChild('cardElement') cardElement!: ElementRef;
  @Output() paymentCompleted = new EventEmitter<void>();

  processing = false;
  stripe: any;
  card: any;
  sdkLoaded = false;
  cardError = '';
  cardComplete = false;
  paymentResult: any = null;

  constructor(
    private paymentService: PaymentService,
    private rideService: RideService,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {

  }


  async ngOnInit() {
    // If the ride doesn't have an amount, calculate it
    if (this.ride && !this.ride.amount && !this.ride.fare) {
      this.estimateFare();

    }

    // Load Stripe SDK if the ride can be paid

      this.loadStripeScript();
     const stripe = await this._loadStripe(environment.stripePublishableKey);
  }

  ngAfterViewInit(): void {
    // Card element will be initialized after Stripe script is loaded
    // and the view is initialized

      this.initializeCard();

  }
  async _loadStripe(key:string){

    const stripe = await loadStripe(key);
    this.sdkLoaded = true;
  }
  loadStripeScript(): void {
    if (window.Stripe) {
      this.initializeStripe();

      return;
    }

    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    script.onload = () => {
      this.initializeStripe();
    };
    document.body.appendChild(script);
  }

  initializeStripe(): void {
    if (!window.Stripe) {
      this.snackBar.open('Stripe SDK not available', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Initialize Stripe with the publishable key from environment
      this.stripe = window.Stripe(environment.stripePublishableKey);

      // Initialize card element after a short delay to ensure DOM is ready
      setTimeout(() => this.initializeCard(), 100);
    } catch (error) {
      console.error('Error initializing Stripe:', error);
      this.snackBar.open('Error initializing Stripe payments. Check your credentials.', 'Close', { duration: 5000 });
    }
  }

  initializeCard(): void {
    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {
      setTimeout(() => this.initializeCard(), 100);
      return;
    }

    try {
      const elements = this.stripe.elements();

      // Create card element
      this.card = elements.create('card', {
        style: {
          base: {
            iconColor: '#666EE8',
            color: '#31325F',
            fontWeight: 400,
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSize: '16px',
            '::placeholder': {
              color: '#CFD7E0'
            }
          }
        }
      });

      // Mount the card element
      this.card.mount(this.cardElement.nativeElement);

      // Handle card element errors
      this.card.on('change', (event: any) => {
        this.cardError = event.error ? event.error.message : '';
        this.cardComplete = event.complete;
      });

      this.sdkLoaded = true;
    } catch (error) {
      console.error('Error initializing Stripe card:', error);
      this.snackBar.open('Error initializing Stripe card form', 'Close', { duration: 5000 });
    }
  }

  calculateAmount(): number {
    // Default amount if no calculation is available
    return 15.00;
  }

  async estimateFare(): Promise<void> {
    if (!this.ride) return;

    try {
      const {fare} = await this.paymentService.estimateFare(
        this.ride.pickup_location,
        this.ride.dropoff_location
      );

      // Update the ride with the estimated fare
      await this.rideService.updateRide(this.ride.id, { fare });
    } catch (error) {
      console.error('Error estimating fare:', error);
    }
  }

  canPay(): boolean {
    if (!this.ride) return false;

    // Can pay if the ride is completed and payment is pending
    return (
      this.ride.status === 'completed' &&
      (!this.ride.payment_status || this.ride.payment_status === 'pending' || this.ride.payment_status === 'failed')
    );
  }

  canRequestRefund(): boolean {
    if (!this.ride) return false;

    // Can request refund if the payment status is paid or completed
    return this.ride.payment_status === 'paid' || this.ride.payment_status === 'completed';
  }

  async processPayment(): Promise<void> {
    if (!this.ride || !this.canPay() || !this.stripe || !this.card) return;

    this.processing = true;
    this.paymentResult = null;

    try {
      const amount = this.ride.amount || this.ride.fare || this.calculateAmount();

      // Create a payment intent
      // const { clientSecret } = await this.paymentService.createPaymentIntent(
      //   this.ride.id,
      //   amount
      // );

      // Create a payment method with the card details
      const { paymentMethod, error: paymentMethodError } = await this.stripe.createPaymentMethod({
        type: 'card',
        card: this.card,
      });

      if (paymentMethodError) {
        throw paymentMethodError;
      }
    if (paymentMethodError) {
        throw paymentMethodError;
      }
        let payment = {
        amount: amount * 100, // Stripe uses cents
        currency: 'usd',
        description: "Customer pamyment for ride",
        payment_method: paymentMethod.id
      };
        console.log(payment)
      // Step 2: Create a payment intent using Supabase Stripe edge function
      const { data, error } = await this.authService.supabase.functions.invoke('stripe', {
        body:payment });

      if (error) {
        console.error('Error creating payment intent:', error);
        throw new Error(`Failed to create payment intent: ${error.message}`);
      }
        console.log('Payment intent created:', data);

      if (!data || !data.client_secret) {
        throw new Error('No client secret returned from payment intent creation');
      }

      const clientSecret = data.client_secret;

      // Step 3: Confirm the payment with the client secret
      const { error: confirmError, paymentIntent } = await this.stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethod.id
      });

      if (confirmError) {
        throw confirmError;
      }

      // Payment succeeded
      this.paymentResult = {
        success: true,
        paymentIntent: paymentIntent
      };

      // Update the ride with payment details
      await this.rideService.updateRide(this.ride.id, {
        payment_status: 'paid',
        payment_id: paymentIntent.id,
        amount: amount
      });

      this.snackBar.open('Payment processed successfully!', 'Close', { duration: 3000 });

      // Emit event to notify parent component that payment is completed
      this.paymentCompleted.emit();

      // Realtime subscription will handle the ride update automatically
      // const updatedRide = await this.rideService.getRide(this.ride.id);
      // if (updatedRide) {
      //   this.ride = updatedRide;
      // }


    } catch (error: any) {
      console.error('Error processing payment:', error);

      this.paymentResult = {
        success: false,
        error: {
          message: error.message || 'An unknown error occurred'
        }
      };

      this.snackBar.open(`Payment error: ${error.message}`, 'Close', { duration: 5000 });
    } finally {
      this.processing = false;
    }
  }

  getDisplayAmount(): string {
    if (!this.ride) return '0';
    return (this.ride.amount || this.ride.fare || this.calculateAmount()).toString();
  }

  async requestRefund(): Promise<void> {
    if (!this.ride || !this.canRequestRefund()) return;

    this.processing = true;
    this.paymentResult = null;

    try {
      // Process the refund
      const success = await this.paymentService.processRefund(this.ride.id);

      if (success) {
        this.paymentResult = {
          success: true,
          refund: true
        };

        this.snackBar.open('Refund processed successfully!', 'Close', { duration: 3000 });

        // Emit event to notify parent component that refund is completed
        this.paymentCompleted.emit();

        // Realtime subscription will handle the ride update automatically
        // const updatedRide = await this.rideService.getRide(this.ride.id);
        // if (updatedRide) {
        //   this.ride = updatedRide;
        // }
      } else {
        this.paymentResult = {
          success: false,
          refund: true,
          error: {
            message: 'Failed to process refund'
          }
        };

        this.snackBar.open('Refund request failed. Please try again.', 'Close', { duration: 3000 });
      }
    } catch (error: any) {
      console.error('Error processing refund:', error);

      this.paymentResult = {
        success: false,
        refund: true,
        error: {
          message: error.message || 'An unknown error occurred'
        }
      };

      this.snackBar.open(`Refund error: ${error.message}`, 'Close', { duration: 5000 });
    } finally {
      this.processing = false;
    }
  }
}
