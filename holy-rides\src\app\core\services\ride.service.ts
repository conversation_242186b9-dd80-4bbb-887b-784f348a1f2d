import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { SupabaseClient, RealtimeChannel } from '@supabase/supabase-js';
import { BehaviorSubject, Observable } from 'rxjs';
import { Ride, RideFilter, RideStatus } from '../models/ride.model';
import { SmsService } from './sms.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class RideService implements OnDestroy {
  private _supabase: SupabaseClient;
  private ridesSubject = new BehaviorSubject<Ride[]>([]);
  rides$ = this.ridesSubject.asObservable();
  private rideSubscription: RealtimeChannel | null = null;

  // Expose supabase client for use in other components
  get supabase(): SupabaseClient {
    return this._supabase;
  }

  constructor(
    private smsService: SmsService,
    private authService: AuthService
  ) {
    this._supabase = authService.supabase;
    this.initializeRealTimeSubscription();
  }

  ngOnDestroy(): void {
    // Clean up realtime subscription
    if (this.rideSubscription) {
      console.log('🧹 Cleaning up rides realtime subscription');
      this.rideSubscription.unsubscribe();
      this.rideSubscription = null;
    }
  }

  private initializeRealTimeSubscription() {
    // Clean up existing subscription if any
    if (this.rideSubscription) {
      this.rideSubscription.unsubscribe();
    }

    // Subscribe to changes in the rides table
    this.rideSubscription = this._supabase
      .channel('rides-channel')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'rides' },
        async (payload) => {
          console.log('🔄 Realtime event received:', payload.eventType, payload.new || payload.old);

          // Refresh rides when there's a change
          try {
            await this.refreshRides();
            console.log('✅ Rides refreshed successfully after realtime event');
          } catch (error) {
            console.error('❌ Error refreshing rides after realtime event:', error);
          }
        }
      )
      .subscribe((status) => {
        console.log('📡 Realtime subscription status:', status);
        if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to rides realtime updates');
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Realtime subscription error, attempting to reconnect...');
          // Attempt to reconnect after a delay
          setTimeout(() => {
            this.initializeRealTimeSubscription();
          }, 5000);
        }
      });
  }

  private async refreshRides(): Promise<void> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      this.ridesSubject.next(data || []);
    } catch (error) {
      console.error('Error refreshing rides:', error);
      // Don't update subject on error to preserve last known good state
    }
  }

  async getAllRides(): Promise<Ride[]> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      this.ridesSubject.next(data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching all rides:', error);
      return this.ridesSubject.value; // Return cached data on error
    }
  }

  async getRidesByStatus(status: RideStatus): Promise<Ride[]> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error(`Error fetching rides with status ${status}:`, error);
      return [];
    }
  }

  async assignRideToDriver(rideId: string, driverId: string): Promise<boolean> {
    try {
      // First check if the ride is still in requested or assigned status
      const currentRide = await this.getRide(rideId);
      if (!currentRide || (currentRide.status !== 'requested' && currentRide.status !== 'assigned')) {
        throw new Error('Ride is no longer available for assignment');
      }

      const { error } = await this._supabase
        .from('rides')
        .update({
          driver_id: driverId,
          status: 'assigned',
          updated_at: new Date().toISOString()
        })
        .eq('id', rideId)
        .in('status', ['requested', 'assigned']); // Allow both statuses

      if (error) throw error;

      // Refresh rides data
      //await this.refreshRides();

      // Get the updated ride with the driver assigned
      const updatedRide = await this.getRide(rideId);
      if (updatedRide) {
        // Send SMS notifications to rider and driver
        // Use setTimeout to ensure ride assignment completes even if SMS sending takes time
        // This makes the SMS sending non-blocking for the ride assignment process
        setTimeout(async () => {
          try {
            await this.smsService.sendRideAssignmentNotifications(updatedRide, driverId);
          } catch (smsError) {
            // Log the error but don't fail the ride assignment
            console.error('Error sending SMS notifications:', smsError);
          }
        }, 0);

        // Log that notifications are being sent
        console.log(`Sending ride assignment notifications for ride ${rideId} to rider and driver ${driverId}`);
      }

      return true;
    } catch (error) {
      console.error('Error assigning ride to driver:', error);
      throw error; // Let caller handle the error
    }
  }

  async updateRideStatus(rideId: string, status: RideStatus): Promise<boolean> {
    try {
      // First check if the ride exists and status transition is valid
      const currentRide = await this.getRide(rideId);
      if (!currentRide) {
        throw new Error('Ride not found');
      }

      // Validate status transition
      if (!this.isValidStatusTransition(currentRide.status, status)) {
        throw new Error(`Invalid status transition from ${currentRide.status} to ${status}`);
      }

      const { error } = await this._supabase
        .from('rides')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) throw error;

      // Refresh rides data
      await this.refreshRides();

      // Get the updated ride to send notifications
      const updatedRide = await this.getRide(rideId);
      if (updatedRide) {
        // Send status update notifications in a non-blocking way
        setTimeout(async () => {
          try {
            if (status === 'canceled') {
              // Use the new enhanced cancellation notification
              await this.smsService.sendRideCancellationNotifications(updatedRide);
            } else if (status === 'completed') {
              // Send ride completion notification with payment instructions
              await this.smsService.sendRideCompletionNotification(updatedRide);
              // Send admin notification for completed ride
              await this.smsService.sendAdminRideStatusNotification(updatedRide, status);
            } else if (status === 'in-progress') {
              // Use existing status update notifications for in-progress
              await this.smsService.sendRideStatusUpdateNotifications(updatedRide, status);
              // Send admin notification for started ride
              await this.smsService.sendAdminRideStatusNotification(updatedRide, status);
            }
          } catch (smsError) {
            // Log the error but don't fail the status update
            console.error('Error sending status update notifications:', smsError);
          }
        }, 0);

        // Log that notifications are being sent
        console.log(`Sending ride status update (${status}) notifications for ride ${rideId}`);
      }

      return true;
    } catch (error) {
      console.error('Error updating ride status:', error);
      throw error; // Let caller handle the error
    }
  }

  private isValidStatusTransition(from: RideStatus, to: RideStatus): boolean {
    const transitions: { [key in RideStatus]: RideStatus[] } = {
      'requested': ['assigned', 'canceled'],
      'assigned': ['in-progress', 'canceled', 'requested'], // Allow driver to cancel assignment
      'in-progress': ['completed', 'canceled'],
      'completed': [],
      'canceled': []
    };

    return transitions[from]?.includes(to) || false;
  }

  async createRide(ride: Omit<Ride, 'id' | 'created_at' | 'updated_at'>): Promise<Ride> {
    try {
      // Set default payment status to pending if not provided
      const rideWithPaymentStatus = {
        ...ride,
        payment_status: ride.payment_status || 'pending'
      };

      const { data, error } = await this._supabase
        .from('rides')
        .insert([rideWithPaymentStatus])
        .select()
        .single();

      if (error) throw error;

      // Update local rides state
      const currentRides = this.ridesSubject.value;
      this.ridesSubject.next([...currentRides, data]);

      // Send booking notifications in a non-blocking way
      setTimeout(async () => {
        try {
          // Send confirmation to rider
       //   await this.smsService.sendRideBookingConfirmation(data);

          // Send notification to all drivers
        //  await this.smsService.sendRideBookingNotificationToDrivers(data);
        } catch (smsError) {
          // Log the error but don't fail the ride creation
          console.error('Error sending ride booking notifications:', smsError);
        }
      }, 0);

      console.log(`Sending ride booking notifications for ride ${data.id}`);

      return data;
    } catch (error) {
      console.error('Error creating ride:', error);
      throw error;
    }
  }

  async getUserRides(userId: string): Promise<Ride[]> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .eq('rider_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      this.ridesSubject.next(data);
      return data;
    } catch (error) {
      console.error('Error fetching user rides:', error);
      return [];
    }
  }

  async getDriverRides(driverId: string): Promise<Ride[]> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .eq('driver_id', driverId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching driver rides:', error);
      return [];
    }
  }

  async getAvailableRides(): Promise<Ride[]> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .eq('status', 'requested')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching available rides:', error);
      return [];
    }
  }

  async acceptRide(rideId: string, driverId: string): Promise<boolean> {
    return this.assignRideToDriver(rideId, driverId);
  }

  async startRide(rideId: string): Promise<boolean> {
    return this.updateRideStatus(rideId, 'in-progress');
  }

  async completeRide(rideId: string): Promise<boolean> {
    return this.updateRideStatus(rideId, 'completed');
  }

  async getRide(rideId: string): Promise<Ride | null> {
    try {
      const { data, error } = await this._supabase
        .from('rides')
        .select('*')
        .eq('id', rideId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching ride:', error);
      return null;
    }
  }

  async updateRide(rideId: string, updates: Partial<Ride>): Promise<boolean> {
    try {
      const { error } = await this._supabase
        .from('rides')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) throw error;

      // Send payment confirmation notification if payment status is updated to paid/completed
      if (updates.payment_status && (updates.payment_status === 'paid' || updates.payment_status === 'completed')) {
        setTimeout(async () => {
          try {
            const ride = await this.getRide(rideId);
            if (ride && updates.amount) {
              await this.smsService.sendPaymentConfirmationNotification(ride, updates.amount);
            }
          } catch (smsError) {
            console.error('Error sending payment confirmation notification:', smsError);
          }
        }, 0);
      }

      // Refresh rides
      await this.getAllRides();
      return true;
    } catch (error) {
      console.error('Error updating ride:', error);
      return false;
    }
  }

  async cancelRide(rideId: string): Promise<boolean> {
    return this.updateRideStatus(rideId, 'canceled');
  }

  async cancelDriverAssignment(rideId: string): Promise<boolean> {
    try {
      // First check if the ride exists and is assigned
      const currentRide = await this.getRide(rideId);
      if (!currentRide) {
        throw new Error('Ride not found');
      }

      if (currentRide.status !== 'assigned') {
        throw new Error('Ride is not in assigned status');
      }

      // Update the ride to remove driver assignment and set status back to requested
      const { error } = await this._supabase
        .from('rides')
        .update({
          driver_id: null,
          status: 'requested',
          updated_at: new Date().toISOString()
        })
        .eq('id', rideId);

      if (error) throw error;

      // Get the updated ride to send notifications
      const updatedRide = await this.getRide(rideId);
      if (updatedRide) {
        // Send cancellation notifications in a non-blocking way
        setTimeout(async () => {
          try {
            // Send notification to rider that driver cancelled
            await this.smsService.sendDriverCancellationNotification(updatedRide);
          } catch (smsError) {
            // Log the error but don't fail the cancellation
            console.error('Error sending driver cancellation notifications:', smsError);
          }
        }, 0);

        console.log(`Driver assignment cancelled for ride ${rideId}, ride returned to requested status`);
      }

      return true;
    } catch (error) {
      console.error('Error cancelling driver assignment:', error);
      throw error; // Let caller handle the error
    }
  }

  filterRides(rides: Ride[], filter: RideFilter): Ride[] {
    return rides.filter(ride => {
      // Filter by status if specified
      if (filter.status && ride.status !== filter.status) {
        return false;
      }

      // Filter by rider ID if specified
      if (filter.riderId && ride.rider_id !== filter.riderId) {
        return false;
      }

      // Filter by driver ID if specified
      if (filter.driverId && ride.driver_id !== filter.driverId) {
        return false;
      }

      // Filter by date range if specified
      if (filter.dateRange) {
        const rideDate = new Date(ride.created_at);
        const startDate = filter.dateRange.start;
        const endDate = filter.dateRange.end;

        if (rideDate < startDate || rideDate > endDate) {
          return false;
        }
      }

      return true;
    });
  }
}

