import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MessageService } from '../../../../core/services/message.service';
import { AuthService } from '../../../../core/services/auth.service';
import { RideService } from '../../../../core/services/ride.service';
import { RideChatComponent } from '../../../../shared/components/ride-chat/ride-chat.component';
import { Subscription } from 'rxjs';

interface MessageThread {
  id: string;
  ride_id: string;
  created_at: string;
  updated_at: string;
  rides: {
    rider_id: string;
    driver_id: string;
    pickup_location: string;
    dropoff_location: string;
  };
}

@Component({
  selector: 'app-messages',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    MatCardModule,
    MatListModule,
    MatDividerModule,
    MatIconModule,
    MatBadgeModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    RideChatComponent
  ],
  providers: [ActivatedRoute],
  template: `
    <div class="messages-container">
      <mat-card class="threads-card">
        <mat-card-header>
          <mat-card-title>Messages</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
          </div>
          
          <div *ngIf="!loading && threads.length === 0" class="no-threads">
            No message threads found.
          </div>
          
          <mat-list *ngIf="!loading && threads.length > 0">
            <mat-list-item *ngFor="let thread of threads" 
                          [class.active-thread]="selectedThreadId === thread.id"
                          (click)="selectThread(thread)">
              <div class="thread-item">
                <mat-icon class="thread-icon">forum</mat-icon>
                <div class="thread-details">
                  <div class="thread-title">
                    {{ thread.rides.pickup_location }} to {{ thread.rides.dropoff_location }}
                  </div>
                  <div class="thread-date">
                    {{ formatDate(thread.updated_at) }}
                  </div>
                </div>
              </div>
            </mat-list-item>
          </mat-list>
        </mat-card-content>
      </mat-card>
      
      <div class="chat-container" *ngIf="selectedThreadId && selectedRideId">
        <app-ride-chat [threadId]="selectedThreadId" [rideId]="selectedRideId"></app-ride-chat>
      </div>
      
      <div class="empty-chat" *ngIf="!selectedThreadId && !loading">
        <mat-icon class="empty-icon">chat</mat-icon>
        <p>Select a conversation to start messaging</p>
      </div>
    </div>
  `,
  styles: [`
    .messages-container {
      display: flex;
      height: calc(100vh - 150px);
      padding: 20px;
      gap: 20px;
    }
    
    .threads-card {
      width: 300px;
      overflow-y: auto;
    }
    
    .chat-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .loading-container {
      display: flex;
      justify-content: center;
      padding: 20px;
    }
    
    .no-threads {
      padding: 20px;
      text-align: center;
      color: rgba(0, 0, 0, 0.5);
    }
    
    .thread-item {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 8px 0;
    }
    
    .thread-icon {
      margin-right: 16px;
      color: #3f51b5;
    }
    
    .thread-details {
      flex: 1;
    }
    
    .thread-title {
      font-weight: 500;
    }
    
    .thread-date {
      font-size: 0.8em;
      color: rgba(0, 0, 0, 0.5);
    }
    
    .active-thread {
      background-color: rgba(63, 81, 181, 0.1);
    }
    
    .empty-chat {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: rgba(0, 0, 0, 0.5);
    }
    
    .empty-icon {
      font-size: 64px;
      height: 64px;
      width: 64px;
      margin-bottom: 16px;
    }
    
    @media (max-width: 768px) {
      .messages-container {
        flex-direction: column;
      }
      
      .threads-card {
        width: 100%;
        max-height: 200px;
      }
    }
  `]
})
export class MessagesComponent implements OnInit, OnDestroy {
  threads: MessageThread[] = [];
  selectedThreadId: string | null = null;
  selectedRideId: string | null = null;
  loading = false;
  private routeSubscription: Subscription | null = null;
  private threadsSubscription: Subscription | null = null;
  
  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private rideService: RideService,
    private route: ActivatedRoute
  ) {}
  
  ngOnInit() {
    this.loading = true;
    
    // Check if a thread ID was provided in the route
    this.routeSubscription = this.route.paramMap.subscribe((params: { get: (arg0: string) => any; }) => {
      const threadId = params.get('threadId');
      if (threadId) {
        this.selectedThreadId = threadId;
      }
      
      this.setupThreadsSubscription();
    });
  }
  
  ngOnDestroy() {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
    if (this.threadsSubscription) {
      this.threadsSubscription.unsubscribe();
    }
  }
  
  setupThreadsSubscription() {
    this.loading = true;
    if (this.threadsSubscription) {
        this.threadsSubscription.unsubscribe();
    }
    // Assuming messageService has a threads$ observable
    this.threadsSubscription = this.messageService.threads$.subscribe(threads => {
        this.threads = threads;
        if (this.selectedThreadId) {
            const selectedThread = this.threads.find(t => t.id === this.selectedThreadId);
            if (selectedThread) {
                this.selectedRideId = selectedThread.ride_id;
            } else {
                this.selectedThreadId = null;
                this.selectedRideId = null;
            }
        }
        this.loading = false;
    }, (error: any) => {
        console.error('Error loading threads:', error);
        this.loading = false;
    });
  }
  
  selectThread(thread: MessageThread) {
    this.selectedThreadId = thread.id;
    this.selectedRideId = thread.ride_id;
  }
  
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'long' });
    } else {
      return date.toLocaleDateString();
    }
  }
}
