{"ast": null, "code": "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n  constructor(url, headers = {}, fetch) {\n    super(url, headers, fetch);\n  }\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id) {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch);\n  }\n}", "map": {"version": 3, "names": ["StorageFileApi", "StorageBucketApi", "StorageClient", "constructor", "url", "headers", "fetch", "from", "id"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/StorageClient.js"], "sourcesContent": ["import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n    constructor(url, headers = {}, fetch) {\n        super(url, headers, fetch);\n    }\n    /**\n     * Perform file operation in a bucket.\n     *\n     * @param id The bucket id to operate on.\n     */\n    from(id) {\n        return new StorageFileApi(this.url, this.headers, id, this.fetch);\n    }\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAO,MAAMC,aAAa,SAASD,gBAAgB,CAAC;EAChDE,WAAWA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,KAAK,EAAE;IAClC,KAAK,CAACF,GAAG,EAAEC,OAAO,EAAEC,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACC,EAAE,EAAE;IACL,OAAO,IAAIR,cAAc,CAAC,IAAI,CAACI,GAAG,EAAE,IAAI,CAACC,OAAO,EAAEG,EAAE,EAAE,IAAI,CAACF,KAAK,CAAC;EACrE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}