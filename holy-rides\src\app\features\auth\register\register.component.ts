import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { AuthService, UserRole } from '../../../core/services/auth.service';
import { SmsService } from '../../../core/services/sms.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    RouterLink
  ],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  registerForm: FormGroup;
  error: string = '';
  loading: boolean = false;
  roles: { value: UserRole; label: string }[] = [
    { value: 'rider', label: 'Rider' },
    { value: 'driver', label: 'Driver' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private smsService: SmsService
  ) {
    this.registerForm = this.formBuilder.group({
      full_name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      phone: ['', [Validators.required]],
      role: ['', [Validators.required]]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(g: FormGroup) {
    return g.get('password')?.value === g.get('confirmPassword')?.value
      ? null
      : { mismatch: true };
  }

  /**
   * Generate welcome message based on user role
   */
  private getWelcomeMessage(role: UserRole): string {
    const baseMessage = `Welcome to the Holy Rides Transportation Family!
Thank you for signing up. You've opted in to receive text messages for important ride updates, promotions, and account notifications.`;

    const roleSpecificMessage = role === 'driver'
      ? 'Go ahead and log in now to start accepting rides at https://app.bookholyrides.com'
      : 'Go ahead and log in now to book your first ride at https://app.bookholyrides.com';

    const footer = `
Msg & data rates may apply. Message frequency varies.
To stop receiving messages, reply STOP. For help, reply HELP.
View our policy at: https://bookholyrides.com/?p=1163`;

    return `${baseMessage} ${roleSpecificMessage}${footer}`;
  }

  /**
   * Send welcome SMS to the newly registered user
   */
  private async sendWelcomeSms(phone: string, role: UserRole): Promise<void> {
    try {
      const welcomeMessage = this.getWelcomeMessage(role);
      await this.smsService.sendSms(phone, welcomeMessage);
      console.log('Welcome SMS sent successfully');
    } catch (error) {
      console.error('Failed to send welcome SMS:', error);
      // Don't throw error - we don't want to break registration if SMS fails
    }
  }

  async onSubmit() {
    if (this.registerForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';

    try {
      // Register the user
      const { error: registerError } = await this.authService.register(
        this.registerForm.value.email,
        this.registerForm.value.password,
        this.registerForm.value.role,
        this.registerForm.value.phone,
        this.registerForm.value.full_name
      );

      if (registerError) {
        this.error = registerError.message;
        return;
      }

      // Add a small delay to ensure profile is created
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Send welcome SMS to the new user
      if (this.registerForm.value.phone) {
        await this.sendWelcomeSms(
          this.registerForm.value.phone,
          this.registerForm.value.role
        );
      }

      // Auto login after successful registration
      const { error: loginError } = await this.authService.login(
        this.registerForm.value.email,
        this.registerForm.value.password
      );

      if (loginError) {
        this.error = loginError.message;
        return;
      }

      // Navigate to profile page after successful registration
      await this.router.navigate(['/auth/profile']);

    } catch (err: any) {
      this.error = err.message;
    } finally {
      this.loading = false;
    }
  }
}
