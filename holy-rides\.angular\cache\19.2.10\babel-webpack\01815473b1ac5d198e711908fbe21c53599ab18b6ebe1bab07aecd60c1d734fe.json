{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { expiresAt, looksLikeFetchResponse } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError } from './errors';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nfunction handleError(_x) {\n  return _handleError.apply(this, arguments);\n}\nfunction _handleError() {\n  _handleError = _asyncToGenerator(function* (error) {\n    if (!looksLikeFetchResponse(error)) {\n      throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n      // status in 500...599 range - server had an error, request might be retryed.\n      throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n      data = yield error.json();\n    } catch (e) {\n      throw new AuthUnknownError(_getErrorMessage(e), e);\n    }\n    if (typeof data === 'object' && data && typeof data.weak_password === 'object' && data.weak_password && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n      throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n    }\n    throw new AuthApiError(_getErrorMessage(data), error.status || 500);\n  });\n  return _handleError.apply(this, arguments);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json;charset=UTF-8'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  params.body = JSON.stringify(body);\n  return Object.assign(Object.assign({}, params), parameters);\n};\nexport function _request(_x2, _x3, _x4, _x5) {\n  return _request2.apply(this, arguments);\n}\nfunction _request2() {\n  _request2 = _asyncToGenerator(function* (fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n      headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n      qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = yield _handleRequest(fetcher, method, url + queryString, {\n      headers,\n      noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : {\n      data: Object.assign({}, data),\n      error: null\n    };\n  });\n  return _request2.apply(this, arguments);\n}\nfunction _handleRequest(_x6, _x7, _x8, _x9, _x0, _x1) {\n  return _handleRequest2.apply(this, arguments);\n}\nfunction _handleRequest2() {\n  _handleRequest2 = _asyncToGenerator(function* (fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n      result = yield fetcher(url, requestParams);\n    } catch (e) {\n      console.error(e);\n      // fetch failed, likely due to a network or CORS error\n      throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n      yield handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n      return result;\n    }\n    try {\n      return yield result.json();\n    } catch (e) {\n      yield handleError(e);\n    }\n  });\n  return _handleRequest2.apply(this, arguments);\n}\nexport function _sessionResponse(data) {\n  var _a;\n  let session = null;\n  if (hasSession(data)) {\n    session = Object.assign({}, data);\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in);\n    }\n  }\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      session,\n      user\n    },\n    error: null\n  };\n}\nexport function _sessionResponsePassword(data) {\n  const response = _sessionResponse(data);\n  if (!response.error && data.weak_password && typeof data.weak_password === 'object' && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.message && typeof data.weak_password.message === 'string' && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n    response.data.weak_password = data.weak_password;\n  }\n  return response;\n}\nexport function _userResponse(data) {\n  var _a;\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      user\n    },\n    error: null\n  };\n}\nexport function _ssoResponse(data) {\n  return {\n    data,\n    error: null\n  };\n}\nexport function _generateLinkResponse(data) {\n  const {\n      action_link,\n      email_otp,\n      hashed_token,\n      redirect_to,\n      verification_type\n    } = data,\n    rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n  const properties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type\n  };\n  const user = Object.assign({}, rest);\n  return {\n    data: {\n      properties,\n      user\n    },\n    error: null\n  };\n}\nexport function _noResolveJsonResponse(data) {\n  return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n  return data.access_token && data.refresh_token && data.expires_in;\n}", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "expiresAt", "looksLikeFetchResponse", "AuthApiError", "AuthRetryableFetchError", "AuthWeakPasswordError", "AuthUnknownError", "_getErrorMessage", "err", "msg", "message", "error_description", "error", "JSON", "stringify", "NETWORK_ERROR_CODES", "handleError", "_x", "_handleError", "apply", "arguments", "_asyncToGenerator", "includes", "status", "data", "json", "weak_password", "Array", "isArray", "reasons", "reduce", "a", "_getRequestParams", "method", "options", "parameters", "body", "params", "headers", "assign", "_request", "_x2", "_x3", "_x4", "_x5", "_request2", "fetcher", "url", "_a", "jwt", "qs", "query", "redirectTo", "queryString", "keys", "URLSearchParams", "toString", "_handleRequest", "noResolveJson", "xform", "_x6", "_x7", "_x8", "_x9", "_x0", "_x1", "_handleRequest2", "requestParams", "result", "console", "ok", "_sessionResponse", "session", "hasSession", "expires_at", "expires_in", "user", "_sessionResponsePassword", "response", "_userResponse", "_ssoResponse", "_generateLinkResponse", "action_link", "email_otp", "hashed_token", "redirect_to", "verification_type", "rest", "properties", "_noResolveJsonResponse", "access_token", "refresh_token"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/fetch.js"], "sourcesContent": ["var __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { expiresAt, looksLikeFetchResponse } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError, } from './errors';\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n    if (!looksLikeFetchResponse(error)) {\n        throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new AuthUnknownError(_getErrorMessage(e), e);\n    }\n    if (typeof data === 'object' &&\n        data &&\n        typeof data.weak_password === 'object' &&\n        data.weak_password &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n    }\n    throw new AuthApiError(_getErrorMessage(data), error.status || 500);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nexport async function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, { headers, noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, requestParams);\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nexport function _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = expiresAt(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nexport function _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nexport function _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nexport function _ssoResponse(data) {\n    return { data, error: null };\n}\nexport function _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nexport function _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n"], "mappings": ";AAAA,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAC/ED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACf,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAC/D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACpE,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAC1ER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACzB;EACJ,OAAOR,CAAC;AACZ,CAAC;AACD,SAASW,SAAS,EAAEC,sBAAsB,QAAQ,WAAW;AAC7D,SAASC,YAAY,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAS,UAAU;AAC1G,MAAMC,gBAAgB,GAAIC,GAAG,IAAKA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,iBAAiB,IAAIH,GAAG,CAACI,KAAK,IAAIC,IAAI,CAACC,SAAS,CAACN,GAAG,CAAC;AACrH,MAAMO,mBAAmB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAAC,SAC7BC,WAAWA,CAAAC,EAAA;EAAA,OAAAC,YAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,aAAA;EAAAA,YAAA,GAAAG,iBAAA,CAA1B,WAA2BT,KAAK,EAAE;IAC9B,IAAI,CAACV,sBAAsB,CAACU,KAAK,CAAC,EAAE;MAChC,MAAM,IAAIR,uBAAuB,CAACG,gBAAgB,CAACK,KAAK,CAAC,EAAE,CAAC,CAAC;IACjE;IACA,IAAIG,mBAAmB,CAACO,QAAQ,CAACV,KAAK,CAACW,MAAM,CAAC,EAAE;MAC5C;MACA,MAAM,IAAInB,uBAAuB,CAACG,gBAAgB,CAACK,KAAK,CAAC,EAAEA,KAAK,CAACW,MAAM,CAAC;IAC5E;IACA,IAAIC,IAAI;IACR,IAAI;MACAA,IAAI,SAASZ,KAAK,CAACa,IAAI,CAAC,CAAC;IAC7B,CAAC,CACD,OAAOpC,CAAC,EAAE;MACN,MAAM,IAAIiB,gBAAgB,CAACC,gBAAgB,CAAClB,CAAC,CAAC,EAAEA,CAAC,CAAC;IACtD;IACA,IAAI,OAAOmC,IAAI,KAAK,QAAQ,IACxBA,IAAI,IACJ,OAAOA,IAAI,CAACE,aAAa,KAAK,QAAQ,IACtCF,IAAI,CAACE,aAAa,IAClBC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,aAAa,CAACG,OAAO,CAAC,IACzCL,IAAI,CAACE,aAAa,CAACG,OAAO,CAAC9B,MAAM,IACjCyB,IAAI,CAACE,aAAa,CAACG,OAAO,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEjC,CAAC,KAAKiC,CAAC,IAAI,OAAOjC,CAAC,KAAK,QAAQ,EAAE,IAAI,CAAC,EAAE;MAC/E,MAAM,IAAIO,qBAAqB,CAACE,gBAAgB,CAACiB,IAAI,CAAC,EAAEZ,KAAK,CAACW,MAAM,EAAEC,IAAI,CAACE,aAAa,CAACG,OAAO,CAAC;IACrG;IACA,MAAM,IAAI1B,YAAY,CAACI,gBAAgB,CAACiB,IAAI,CAAC,EAAEZ,KAAK,CAACW,MAAM,IAAI,GAAG,CAAC;EACvE,CAAC;EAAA,OAAAL,YAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,MAAMY,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,KAAK;EAC7D,MAAMC,MAAM,GAAG;IAAEJ,MAAM;IAAEK,OAAO,EAAE,CAACJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,OAAO,KAAK,CAAC;EAAE,CAAC;EAC7G,IAAIL,MAAM,KAAK,KAAK,EAAE;IAClB,OAAOI,MAAM;EACjB;EACAA,MAAM,CAACC,OAAO,GAAG9C,MAAM,CAAC+C,MAAM,CAAC;IAAE,cAAc,EAAE;EAAiC,CAAC,EAAEL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,OAAO,CAAC;EACvJD,MAAM,CAACD,IAAI,GAAGvB,IAAI,CAACC,SAAS,CAACsB,IAAI,CAAC;EAClC,OAAO5C,MAAM,CAAC+C,MAAM,CAAC/C,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,EAAEF,UAAU,CAAC;AAC/D,CAAC;AACD,gBAAsBK,QAAQA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,SAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AAa7B,SAAAyB,UAAA;EAAAA,SAAA,GAAAxB,iBAAA,CAbM,WAAwByB,OAAO,EAAEb,MAAM,EAAEc,GAAG,EAAEb,OAAO,EAAE;IAC1D,IAAIc,EAAE;IACN,MAAMV,OAAO,GAAG9C,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAEL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,OAAO,CAAC;IACpG,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,GAAG,EAAE;MAC/DX,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUJ,OAAO,CAACe,GAAG,EAAE;IACtD;IACA,MAAMC,EAAE,GAAG,CAACF,EAAE,GAAGd,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,KAAK,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACrH,IAAId,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkB,UAAU,EAAE;MACtEF,EAAE,CAAC,aAAa,CAAC,GAAGhB,OAAO,CAACkB,UAAU;IAC1C;IACA,MAAMC,WAAW,GAAG7D,MAAM,CAAC8D,IAAI,CAACJ,EAAE,CAAC,CAACnD,MAAM,GAAG,GAAG,GAAG,IAAIwD,eAAe,CAACL,EAAE,CAAC,CAACM,QAAQ,CAAC,CAAC,GAAG,EAAE;IAC1F,MAAMhC,IAAI,SAASiC,cAAc,CAACX,OAAO,EAAEb,MAAM,EAAEc,GAAG,GAAGM,WAAW,EAAE;MAAEf,OAAO;MAAEoB,aAAa,EAAExB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwB;IAAc,CAAC,EAAE,CAAC,CAAC,EAAExB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,IAAI,CAAC;IAC9O,OAAO,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACyB,KAAK,IAAIzB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACyB,KAAK,CAACnC,IAAI,CAAC,GAAG;MAAEA,IAAI,EAAEhC,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;MAAEZ,KAAK,EAAE;IAAK,CAAC;EACrM,CAAC;EAAA,OAAAiC,SAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcqC,cAAcA,CAAAG,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,eAAA,CAAA/C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA8C,gBAAA;EAAAA,eAAA,GAAA7C,iBAAA,CAA7B,WAA8ByB,OAAO,EAAEb,MAAM,EAAEc,GAAG,EAAEb,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAE;IAC3E,MAAM+B,aAAa,GAAGnC,iBAAiB,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC1E,IAAIgC,MAAM;IACV,IAAI;MACAA,MAAM,SAAStB,OAAO,CAACC,GAAG,EAAEoB,aAAa,CAAC;IAC9C,CAAC,CACD,OAAO9E,CAAC,EAAE;MACNgF,OAAO,CAACzD,KAAK,CAACvB,CAAC,CAAC;MAChB;MACA,MAAM,IAAIe,uBAAuB,CAACG,gBAAgB,CAAClB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7D;IACA,IAAI,CAAC+E,MAAM,CAACE,EAAE,EAAE;MACZ,MAAMtD,WAAW,CAACoD,MAAM,CAAC;IAC7B;IACA,IAAIlC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwB,aAAa,EAAE;MACzE,OAAOU,MAAM;IACjB;IACA,IAAI;MACA,aAAaA,MAAM,CAAC3C,IAAI,CAAC,CAAC;IAC9B,CAAC,CACD,OAAOpC,CAAC,EAAE;MACN,MAAM2B,WAAW,CAAC3B,CAAC,CAAC;IACxB;EACJ,CAAC;EAAA,OAAA6E,eAAA,CAAA/C,KAAA,OAAAC,SAAA;AAAA;AACD,OAAO,SAASmD,gBAAgBA,CAAC/C,IAAI,EAAE;EACnC,IAAIwB,EAAE;EACN,IAAIwB,OAAO,GAAG,IAAI;EAClB,IAAIC,UAAU,CAACjD,IAAI,CAAC,EAAE;IAClBgD,OAAO,GAAGhF,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAEf,IAAI,CAAC;IACjC,IAAI,CAACA,IAAI,CAACkD,UAAU,EAAE;MAClBF,OAAO,CAACE,UAAU,GAAGzE,SAAS,CAACuB,IAAI,CAACmD,UAAU,CAAC;IACnD;EACJ;EACA,MAAMC,IAAI,GAAG,CAAC5B,EAAE,GAAGxB,IAAI,CAACoD,IAAI,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGxB,IAAI;EACnE,OAAO;IAAEA,IAAI,EAAE;MAAEgD,OAAO;MAAEI;IAAK,CAAC;IAAEhE,KAAK,EAAE;EAAK,CAAC;AACnD;AACA,OAAO,SAASiE,wBAAwBA,CAACrD,IAAI,EAAE;EAC3C,MAAMsD,QAAQ,GAAGP,gBAAgB,CAAC/C,IAAI,CAAC;EACvC,IAAI,CAACsD,QAAQ,CAAClE,KAAK,IACfY,IAAI,CAACE,aAAa,IAClB,OAAOF,IAAI,CAACE,aAAa,KAAK,QAAQ,IACtCC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,aAAa,CAACG,OAAO,CAAC,IACzCL,IAAI,CAACE,aAAa,CAACG,OAAO,CAAC9B,MAAM,IACjCyB,IAAI,CAACE,aAAa,CAAChB,OAAO,IAC1B,OAAOc,IAAI,CAACE,aAAa,CAAChB,OAAO,KAAK,QAAQ,IAC9Cc,IAAI,CAACE,aAAa,CAACG,OAAO,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEjC,CAAC,KAAKiC,CAAC,IAAI,OAAOjC,CAAC,KAAK,QAAQ,EAAE,IAAI,CAAC,EAAE;IAC/EgF,QAAQ,CAACtD,IAAI,CAACE,aAAa,GAAGF,IAAI,CAACE,aAAa;EACpD;EACA,OAAOoD,QAAQ;AACnB;AACA,OAAO,SAASC,aAAaA,CAACvD,IAAI,EAAE;EAChC,IAAIwB,EAAE;EACN,MAAM4B,IAAI,GAAG,CAAC5B,EAAE,GAAGxB,IAAI,CAACoD,IAAI,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGxB,IAAI;EACnE,OAAO;IAAEA,IAAI,EAAE;MAAEoD;IAAK,CAAC;IAAEhE,KAAK,EAAE;EAAK,CAAC;AAC1C;AACA,OAAO,SAASoE,YAAYA,CAACxD,IAAI,EAAE;EAC/B,OAAO;IAAEA,IAAI;IAAEZ,KAAK,EAAE;EAAK,CAAC;AAChC;AACA,OAAO,SAASqE,qBAAqBA,CAACzD,IAAI,EAAE;EACxC,MAAM;MAAE0D,WAAW;MAAEC,SAAS;MAAEC,YAAY;MAAEC,WAAW;MAAEC;IAAkB,CAAC,GAAG9D,IAAI;IAAE+D,IAAI,GAAGpG,MAAM,CAACqC,IAAI,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;EAC5L,MAAMgE,UAAU,GAAG;IACfN,WAAW;IACXC,SAAS;IACTC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC;EACD,MAAMV,IAAI,GAAGpF,MAAM,CAAC+C,MAAM,CAAC,CAAC,CAAC,EAAEgD,IAAI,CAAC;EACpC,OAAO;IACH/D,IAAI,EAAE;MACFgE,UAAU;MACVZ;IACJ,CAAC;IACDhE,KAAK,EAAE;EACX,CAAC;AACL;AACA,OAAO,SAAS6E,sBAAsBA,CAACjE,IAAI,EAAE;EACzC,OAAOA,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,UAAUA,CAACjD,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACkE,YAAY,IAAIlE,IAAI,CAACmE,aAAa,IAAInE,IAAI,CAACmD,UAAU;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}