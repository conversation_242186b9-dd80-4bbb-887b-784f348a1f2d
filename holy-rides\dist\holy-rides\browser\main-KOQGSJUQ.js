import{A as qi,a as _,b as q,c as Ii,d as bt,e as oe,f as Qe,g as Pe,p as Ki,q as Ui}from"./chunk-KJHHDGHU.js";import"./chunk-MS4AQ6UA.js";import{a as Qi,b as $i}from"./chunk-EMQER2I7.js";import{a as Vi}from"./chunk-EACHO2FA.js";import{d as zi}from"./chunk-ORDMVBIZ.js";import{a as We,b as Ge}from"./chunk-WSXVBUWR.js";import"./chunk-3MEMYPGR.js";import"./chunk-EO3PEVGH.js";import{a as pe,b as Bi}from"./chunk-553Y2ERR.js";import"./chunk-3VEHVC57.js";import"./chunk-Q34CP4BD.js";import{b as Oi,c as X,e as Ri}from"./chunk-3EEDYH74.js";import{J as Z}from"./chunk-AG3SD6JT.js";import{A as fe,Ac as Ci,B as we,Bd as Fi,Ea as St,Fa as gi,Fc as Ve,Gd as Li,H as Y,Ic as Ni,Jc as ki,Jd as $e,Kb as Ae,La as yi,Lc as Di,Na as vi,Pa as G,Rd as ji,T as re,Wa as Ue,Wb as wi,X as E,Xb as Ti,Z as U,aa as ui,ab as _i,bc as Ai,c as Be,ca as B,da as z,db as Si,f as ze,fb as Et,g as oi,ga as hi,ha as di,hb as qe,la as mi,ma as fi,mc as Pi,o as ai,p as li,q as be,qb as ne,ra as pi,rb as se,sb as je,vc as Mi,wa as Ke,wb as Ei,y as ci,yb as Te,zb as bi}from"./chunk-ST4QC4E3.js";import"./chunk-X5YLR3NI.js";import{a as me,c as si,i as F}from"./chunk-ODN5LVDJ.js";function Wi(n){return new E(3e3,!1)}function Hr(){return new E(3100,!1)}function Yr(){return new E(3101,!1)}function Xr(n){return new E(3001,!1)}function Zr(n){return new E(3003,!1)}function Jr(n){return new E(3004,!1)}function xi(n,e){return new E(3005,!1)}function Hi(){return new E(3006,!1)}function Yi(){return new E(3007,!1)}function Xi(n,e){return new E(3008,!1)}function Zi(n){return new E(3002,!1)}function Ji(n,e,t,i,r){return new E(3010,!1)}function er(){return new E(3011,!1)}function tr(){return new E(3012,!1)}function ir(){return new E(3200,!1)}function rr(){return new E(3202,!1)}function nr(){return new E(3013,!1)}function sr(n){return new E(3014,!1)}function or(n){return new E(3015,!1)}function ar(n){return new E(3016,!1)}function lr(n,e){return new E(3404,!1)}function en(n){return new E(3502,!1)}function cr(n){return new E(3503,!1)}function ur(){return new E(3300,!1)}function hr(n){return new E(3504,!1)}function dr(n){return new E(3301,!1)}function mr(n,e){return new E(3302,!1)}function fr(n){return new E(3303,!1)}function pr(n,e){return new E(3400,!1)}function gr(n){return new E(3401,!1)}function yr(n){return new E(3402,!1)}function vr(n,e){return new E(3505,!1)}function x(n){switch(n.length){case 0:return new oe;case 1:return n[0];default:return new Qe(n)}}function Pt(n,e,t=new Map,i=new Map){let r=[],s=[],o=-1,a=null;if(e.forEach(l=>{let c=l.get("offset"),u=c==o,h=u&&a||new Map;l.forEach((v,y)=>{let m=y,g=v;if(y!=="offset")switch(m=n.normalizePropertyName(m,r),g){case Pe:g=t.get(y);break;case q:g=i.get(y);break;default:g=n.normalizeStyleValue(y,m,g,r);break}h.set(m,g)}),u||s.push(h),a=h,o=c}),r.length)throw en(r);return s}function xe(n,e,t,i){switch(e){case"start":n.onStart(()=>i(t&&wt(t,"start",n)));break;case"done":n.onDone(()=>i(t&&wt(t,"done",n)));break;case"destroy":n.onDestroy(()=>i(t&&wt(t,"destroy",n)));break}}function wt(n,e,t){let i=t.totalTime,r=!!t.disabled,s=He(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,i??n.totalTime,r),o=n._data;return o!=null&&(s._data=o),s}function He(n,e,t,i,r="",s=0,o){return{element:n,triggerName:e,fromState:t,toState:i,phaseName:r,totalTime:s,disabled:!!o}}function O(n,e,t){let i=n.get(e);return i||n.set(e,i=t),i}function Mt(n){let e=n.indexOf(":"),t=n.substring(1,e),i=n.slice(e+1);return[t,i]}var tn=typeof document>"u"?null:document.documentElement;function Ye(n){let e=n.parentNode||n.host||null;return e===tn?null:e}function rn(n){return n.substring(1,6)=="ebkit"}var ae=null,Gi=!1;function _r(n){ae||(ae=nn()||{},Gi=ae.style?"WebkitAppearance"in ae.style:!1);let e=!0;return ae.style&&!rn(n)&&(e=n in ae.style,!e&&Gi&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in ae.style)),e}function nn(){return typeof document<"u"?document.body:null}function Ct(n,e){for(;e;){if(e===n)return!0;e=Ye(e)}return!1}function Nt(n,e,t){if(t)return Array.from(n.querySelectorAll(e));let i=n.querySelector(e);return i?[i]:[]}var sn=1e3,kt="{{",on="}}",Dt="ng-enter",Xe="ng-leave",Me="ng-trigger",Ce=".ng-trigger",Ot="ng-animating",Ze=".ng-animating";function W(n){if(typeof n=="number")return n;let e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:Tt(parseFloat(e[1]),e[2])}function Tt(n,e){switch(e){case"s":return n*sn;default:return n}}function Ne(n,e,t){return n.hasOwnProperty("duration")?n:an(n,e,t)}function an(n,e,t){let i=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,r,s=0,o="";if(typeof n=="string"){let a=n.match(i);if(a===null)return e.push(Wi(n)),{duration:0,delay:0,easing:""};r=Tt(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(s=Tt(parseFloat(l),a[4]));let c=a[5];c&&(o=c)}else r=n;if(!t){let a=!1,l=e.length;r<0&&(e.push(Hr()),a=!0),s<0&&(e.push(Yr()),a=!0),a&&e.splice(l,0,Wi(n))}return{duration:r,delay:s,easing:o}}function Sr(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function j(n,e,t){e.forEach((i,r)=>{let s=Je(r);t&&!t.has(r)&&t.set(r,n.style[s]),n.style[s]=i})}function J(n,e){e.forEach((t,i)=>{let r=Je(i);n.style[r]=""})}function ge(n){return Array.isArray(n)?n.length==1?n[0]:Ii(n):n}function Er(n,e,t){let i=e.params||{},r=Rt(n);r.length&&r.forEach(s=>{i.hasOwnProperty(s)||t.push(Xr(s))})}var At=new RegExp(`${kt}\\s*(.+?)\\s*${on}`,"g");function Rt(n){let e=[];if(typeof n=="string"){let t;for(;t=At.exec(n);)e.push(t[1]);At.lastIndex=0}return e}function ye(n,e,t){let i=`${n}`,r=i.replace(At,(s,o)=>{let a=e[o];return a==null&&(t.push(Zr(o)),a=""),a.toString()});return r==i?n:r}var ln=/-+([a-z0-9])/g;function Je(n){return n.replace(ln,(...e)=>e[1].toUpperCase())}function br(n,e){return n===0||e===0}function wr(n,e,t){if(t.size&&e.length){let i=e[0],r=[];if(t.forEach((s,o)=>{i.has(o)||r.push(o),i.set(o,s)}),r.length)for(let s=1;s<e.length;s++){let o=e[s];r.forEach(a=>o.set(a,et(n,a)))}}return e}function R(n,e,t){switch(e.type){case _.Trigger:return n.visitTrigger(e,t);case _.State:return n.visitState(e,t);case _.Transition:return n.visitTransition(e,t);case _.Sequence:return n.visitSequence(e,t);case _.Group:return n.visitGroup(e,t);case _.Animate:return n.visitAnimate(e,t);case _.Keyframes:return n.visitKeyframes(e,t);case _.Style:return n.visitStyle(e,t);case _.Reference:return n.visitReference(e,t);case _.AnimateChild:return n.visitAnimateChild(e,t);case _.AnimateRef:return n.visitAnimateRef(e,t);case _.Query:return n.visitQuery(e,t);case _.Stagger:return n.visitStagger(e,t);default:throw Jr(e.type)}}function et(n,e){return window.getComputedStyle(n)[e]}var Yt=(()=>{class n{validateStyleProperty(t){return _r(t)}containsElement(t,i){return Ct(t,i)}getParentElement(t){return Ye(t)}query(t,i,r){return Nt(t,i,r)}computeStyle(t,i,r){return r||""}animate(t,i,r,s,o,a=[],l){return new oe(r,s)}static \u0275fac=function(i){return new(i||n)};static \u0275prov=U({token:n,factory:n.\u0275fac})}return n})(),ce=class{static NOOP=new Yt},ue=class{};var cn=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),st=class extends ue{normalizePropertyName(e,t){return Je(e)}normalizeStyleValue(e,t,i,r){let s="",o=i.toString().trim();if(cn.has(t)&&i!==0&&i!=="0")if(typeof i=="number")s="px";else{let a=i.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&r.push(xi(e,i))}return o+s}};var ot="*";function un(n,e){let t=[];return typeof n=="string"?n.split(/\s*,\s*/).forEach(i=>hn(i,t,e)):t.push(n),t}function hn(n,e,t){if(n[0]==":"){let l=dn(n,t);if(typeof l=="function"){e.push(l);return}n=l}let i=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(i==null||i.length<4)return t.push(or(n)),e;let r=i[1],s=i[2],o=i[3];e.push(Tr(r,o));let a=r==ot&&o==ot;s[0]=="<"&&!a&&e.push(Tr(o,r))}function dn(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,i)=>parseFloat(i)>parseFloat(t);case":decrement":return(t,i)=>parseFloat(i)<parseFloat(t);default:return e.push(ar(n)),"* => *"}}var tt=new Set(["true","1"]),it=new Set(["false","0"]);function Tr(n,e){let t=tt.has(n)||it.has(n),i=tt.has(e)||it.has(e);return(r,s)=>{let o=n==ot||n==r,a=e==ot||e==s;return!o&&t&&typeof r=="boolean"&&(o=r?tt.has(n):it.has(n)),!a&&i&&typeof s=="boolean"&&(a=s?tt.has(e):it.has(e)),o&&a}}var Ir=":self",mn=new RegExp(`s*${Ir}s*,?`,"g");function Fr(n,e,t,i){return new Kt(n).build(e,t,i)}var Ar="",Kt=class{_driver;constructor(e){this._driver=e}build(e,t,i){let r=new Ut(t);return this._resetContextStyleTimingState(r),R(this,ge(e),r)}_resetContextStyleTimingState(e){e.currentQuerySelector=Ar,e.collectedStyles=new Map,e.collectedStyles.set(Ar,new Map),e.currentTime=0}visitTrigger(e,t){let i=t.queryCount=0,r=t.depCount=0,s=[],o=[];return e.name.charAt(0)=="@"&&t.errors.push(Hi()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(t),a.type==_.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,s.push(this.visitState(l,t))}),l.name=c}else if(a.type==_.Transition){let l=this.visitTransition(a,t);i+=l.queryCount,r+=l.depCount,o.push(l)}else t.errors.push(Yi())}),{type:_.Trigger,name:e.name,states:s,transitions:o,queryCount:i,depCount:r,options:null}}visitState(e,t){let i=this.visitStyle(e.styles,t),r=e.options&&e.options.params||null;if(i.containsDynamicStyles){let s=new Set,o=r||{};i.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{Rt(l).forEach(c=>{o.hasOwnProperty(c)||s.add(c)})})}),s.size&&t.errors.push(Xi(e.name,[...s.values()]))}return{type:_.State,name:e.name,style:i,options:r?{params:r}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let i=R(this,ge(e.animation),t),r=un(e.expr,t.errors);return{type:_.Transition,matchers:r,animation:i,queryCount:t.queryCount,depCount:t.depCount,options:le(e.options)}}visitSequence(e,t){return{type:_.Sequence,steps:e.steps.map(i=>R(this,i,t)),options:le(e.options)}}visitGroup(e,t){let i=t.currentTime,r=0,s=e.steps.map(o=>{t.currentTime=i;let a=R(this,o,t);return r=Math.max(r,t.currentTime),a});return t.currentTime=r,{type:_.Group,steps:s,options:le(e.options)}}visitAnimate(e,t){let i=yn(e.timings,t.errors);t.currentAnimateTimings=i;let r,s=e.styles?e.styles:bt({});if(s.type==_.Keyframes)r=this.visitKeyframes(s,t);else{let o=e.styles,a=!1;if(!o){a=!0;let c={};i.easing&&(c.easing=i.easing),o=bt(c)}t.currentTime+=i.duration+i.delay;let l=this.visitStyle(o,t);l.isEmptyStep=a,r=l}return t.currentAnimateTimings=null,{type:_.Animate,timings:i,style:r,options:null}}visitStyle(e,t){let i=this._makeStyleAst(e,t);return this._validateStyleAst(i,t),i}_makeStyleAst(e,t){let i=[],r=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of r)typeof a=="string"?a===q?i.push(a):t.errors.push(Zi(a)):i.push(new Map(Object.entries(a)));let s=!1,o=null;return i.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(o=a.get("easing"),a.delete("easing")),!s)){for(let l of a.values())if(l.toString().indexOf(kt)>=0){s=!0;break}}}),{type:_.Style,styles:i,easing:o,offset:e.offset,containsDynamicStyles:s,options:null}}_validateStyleAst(e,t){let i=t.currentAnimateTimings,r=t.currentTime,s=t.currentTime;i&&s>0&&(s-=i.duration+i.delay),e.styles.forEach(o=>{typeof o!="string"&&o.forEach((a,l)=>{let c=t.collectedStyles.get(t.currentQuerySelector),u=c.get(l),h=!0;u&&(s!=r&&s>=u.startTime&&r<=u.endTime&&(t.errors.push(Ji(l,u.startTime,u.endTime,s,r)),h=!1),s=u.startTime),h&&c.set(l,{startTime:s,endTime:r}),t.options&&Er(a,t.options,t.errors)})})}visitKeyframes(e,t){let i={type:_.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(er()),i;let r=1,s=0,o=[],a=!1,l=!1,c=0,u=e.steps.map(T=>{let A=this._makeStyleAst(T,t),N=A.offset!=null?A.offset:gn(A.styles),M=0;return N!=null&&(s++,M=A.offset=N),l=l||M<0||M>1,a=a||M<c,c=M,o.push(M),A});l&&t.errors.push(tr()),a&&t.errors.push(ir());let h=e.steps.length,v=0;s>0&&s<h?t.errors.push(rr()):s==0&&(v=r/(h-1));let y=h-1,m=t.currentTime,g=t.currentAnimateTimings,b=g.duration;return u.forEach((T,A)=>{let N=v>0?A==y?1:v*A:o[A],M=N*b;t.currentTime=m+g.delay+M,g.duration=M,this._validateStyleAst(T,t),T.offset=N,i.styles.push(T)}),i}visitReference(e,t){return{type:_.Reference,animation:R(this,ge(e.animation),t),options:le(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:_.AnimateChild,options:le(e.options)}}visitAnimateRef(e,t){return{type:_.AnimateRef,animation:this.visitReference(e.animation,t),options:le(e.options)}}visitQuery(e,t){let i=t.currentQuerySelector,r=e.options||{};t.queryCount++,t.currentQuery=e;let[s,o]=fn(e.selector);t.currentQuerySelector=i.length?i+" "+s:s,O(t.collectedStyles,t.currentQuerySelector,new Map);let a=R(this,ge(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=i,{type:_.Query,selector:s,limit:r.limit||0,optional:!!r.optional,includeSelf:o,animation:a,originalSelector:e.selector,options:le(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(nr());let i=e.timings==="full"?{duration:0,delay:0,easing:"full"}:Ne(e.timings,t.errors,!0);return{type:_.Stagger,animation:R(this,ge(e.animation),t),timings:i,options:null}}};function fn(n){let e=!!n.split(/\s*,\s*/).find(t=>t==Ir);return e&&(n=n.replace(mn,"")),n=n.replace(/@\*/g,Ce).replace(/@\w+/g,t=>Ce+"-"+t.slice(1)).replace(/:animating/g,Ze),[n,e]}function pn(n){return n?me({},n):null}var Ut=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function gn(n){if(typeof n=="string")return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){let i=t;e=parseFloat(i.get("offset")),i.delete("offset")}});else if(n instanceof Map&&n.has("offset")){let t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function yn(n,e){if(n.hasOwnProperty("duration"))return n;if(typeof n=="number"){let s=Ne(n,e).duration;return It(s,0,"")}let t=n;if(t.split(/\s+/).some(s=>s.charAt(0)=="{"&&s.charAt(1)=="{")){let s=It(0,0,"");return s.dynamic=!0,s.strValue=t,s}let r=Ne(t,e);return It(r.duration,r.delay,r.easing)}function le(n){return n?(n=me({},n),n.params&&(n.params=pn(n.params))):n={},n}function It(n,e,t){return{duration:n,delay:e,easing:t}}function Xt(n,e,t,i,r,s,o=null,a=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:i,duration:r,delay:s,totalTime:r+s,easing:o,subTimeline:a}}var De=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let i=this._map.get(e);i||this._map.set(e,i=[]),i.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},vn=1,_n=":enter",Sn=new RegExp(_n,"g"),En=":leave",bn=new RegExp(En,"g");function Lr(n,e,t,i,r,s=new Map,o=new Map,a,l,c=[]){return new qt().buildKeyframes(n,e,t,i,r,s,o,a,l,c)}var qt=class{buildKeyframes(e,t,i,r,s,o,a,l,c,u=[]){c=c||new De;let h=new jt(e,t,c,r,s,u,[]);h.options=l;let v=l.delay?W(l.delay):0;h.currentTimeline.delayNextStep(v),h.currentTimeline.setStyles([o],null,h.errors,l),R(this,i,h);let y=h.timelines.filter(m=>m.containsAnimation());if(y.length&&a.size){let m;for(let g=y.length-1;g>=0;g--){let b=y[g];if(b.element===t){m=b;break}}m&&!m.allowOnlyTimelineStyles()&&m.setStyles([a],null,h.errors,l)}return y.length?y.map(m=>m.buildKeyframes()):[Xt(t,[],[],[],0,v,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let i=t.subInstructions.get(t.element);if(i){let r=t.createSubContext(e.options),s=t.currentTimeline.currentTime,o=this._visitSubInstructions(i,r,r.options);s!=o&&t.transformIntoNewTimeline(o)}t.previousNode=e}visitAnimateRef(e,t){let i=t.createSubContext(e.options);i.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,i),this.visitReference(e.animation,i),t.transformIntoNewTimeline(i.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,i){for(let r of e){let s=r?.delay;if(s){let o=typeof s=="number"?s:W(ye(s,r?.params??{},t.errors));i.delayNextStep(o)}}}_visitSubInstructions(e,t,i){let s=t.currentTimeline.currentTime,o=i.duration!=null?W(i.duration):null,a=i.delay!=null?W(i.delay):null;return o!==0&&e.forEach(l=>{let c=t.appendInstructionToTimeline(l,o,a);s=Math.max(s,c.duration+c.delay)}),s}visitReference(e,t){t.updateOptions(e.options,!0),R(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let i=t.subContextCount,r=t,s=e.options;if(s&&(s.params||s.delay)&&(r=t.createSubContext(s),r.transformIntoNewTimeline(),s.delay!=null)){r.previousNode.type==_.Style&&(r.currentTimeline.snapshotCurrentStyles(),r.previousNode=at);let o=W(s.delay);r.delayNextStep(o)}e.steps.length&&(e.steps.forEach(o=>R(this,o,r)),r.currentTimeline.applyStylesToKeyframe(),r.subContextCount>i&&r.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let i=[],r=t.currentTimeline.currentTime,s=e.options&&e.options.delay?W(e.options.delay):0;e.steps.forEach(o=>{let a=t.createSubContext(e.options);s&&a.delayNextStep(s),R(this,o,a),r=Math.max(r,a.currentTimeline.currentTime),i.push(a.currentTimeline)}),i.forEach(o=>t.currentTimeline.mergeTimelineCollectedStyles(o)),t.transformIntoNewTimeline(r),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let i=e.strValue,r=t.params?ye(i,t.params,t.errors):i;return Ne(r,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let i=t.currentAnimateTimings=this._visitTiming(e.timings,t),r=t.currentTimeline;i.delay&&(t.incrementTime(i.delay),r.snapshotCurrentStyles());let s=e.style;s.type==_.Keyframes?this.visitKeyframes(s,t):(t.incrementTime(i.duration),this.visitStyle(s,t),r.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let i=t.currentTimeline,r=t.currentAnimateTimings;!r&&i.hasCurrentStyleProperties()&&i.forwardFrame();let s=r&&r.easing||e.easing;e.isEmptyStep?i.applyEmptyStep(s):i.setStyles(e.styles,s,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let i=t.currentAnimateTimings,r=t.currentTimeline.duration,s=i.duration,a=t.createSubContext().currentTimeline;a.easing=i.easing,e.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*s),a.setStyles(l.styles,l.easing,t.errors,t.options),a.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(a),t.transformIntoNewTimeline(r+s),t.previousNode=e}visitQuery(e,t){let i=t.currentTimeline.currentTime,r=e.options||{},s=r.delay?W(r.delay):0;s&&(t.previousNode.type===_.Style||i==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=at);let o=i,a=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!r.optional,t.errors);t.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{t.currentQueryIndex=u;let h=t.createSubContext(e.options,c);s&&h.delayNextStep(s),c===t.element&&(l=h.currentTimeline),R(this,e.animation,h),h.currentTimeline.applyStylesToKeyframe();let v=h.currentTimeline.currentTime;o=Math.max(o,v)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(o),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let i=t.parentContext,r=t.currentTimeline,s=e.timings,o=Math.abs(s.duration),a=o*(t.currentQueryTotal-1),l=o*t.currentQueryIndex;switch(s.duration<0?"reverse":s.easing){case"reverse":l=a-l;break;case"full":l=i.currentStaggerTime;break}let u=t.currentTimeline;l&&u.delayNextStep(l);let h=u.currentTime;R(this,e.animation,t),t.previousNode=e,i.currentStaggerTime=r.currentTime-h+(r.startTime-i.currentTimeline.startTime)}},at={},jt=class n{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=at;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,i,r,s,o,a,l){this._driver=e,this.element=t,this.subInstructions=i,this._enterClassName=r,this._leaveClassName=s,this.errors=o,this.timelines=a,this.currentTimeline=l||new lt(this._driver,t,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let i=e,r=this.options;i.duration!=null&&(r.duration=W(i.duration)),i.delay!=null&&(r.delay=W(i.delay));let s=i.params;if(s){let o=r.params;o||(o=this.options.params={}),Object.keys(s).forEach(a=>{(!t||!o.hasOwnProperty(a))&&(o[a]=ye(s[a],o,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let i=e.params={};Object.keys(t).forEach(r=>{i[r]=t[r]})}}return e}createSubContext(e=null,t,i){let r=t||this.element,s=new n(this._driver,r,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(r,i||0));return s.previousNode=this.previousNode,s.currentAnimateTimings=this.currentAnimateTimings,s.options=this._copyOptions(),s.updateOptions(e),s.currentQueryIndex=this.currentQueryIndex,s.currentQueryTotal=this.currentQueryTotal,s.parentContext=this,this.subContextCount++,s}transformIntoNewTimeline(e){return this.previousNode=at,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,i){let r={duration:t??e.duration,delay:this.currentTimeline.currentTime+(i??0)+e.delay,easing:""},s=new Vt(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,r,e.stretchStartingKeyframe);return this.timelines.push(s),r}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,i,r,s,o){let a=[];if(r&&a.push(this.element),e.length>0){e=e.replace(Sn,"."+this._enterClassName),e=e.replace(bn,"."+this._leaveClassName);let l=i!=1,c=this._driver.query(this.element,e,l);i!==0&&(c=i<0?c.slice(c.length+i,c.length):c.slice(0,i)),a.push(...c)}return!s&&a.length==0&&o.push(sr(t)),a}},lt=class n{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,i,r){this._driver=e,this.element=t,this.startTime=i,this._elementTimelineStylesLookup=r,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new n(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=vn,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,i]of this._globalTimelineStyles)this._backFill.set(t,i||q),this._currentKeyframe.set(t,q);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,i,r){t&&this._previousKeyframe.set("easing",t);let s=r&&r.params||{},o=wn(e,this._globalTimelineStyles);for(let[a,l]of o){let c=ye(l,s,i);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??q),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,i)=>{let r=this._styleSummary.get(i);(!r||t.time>r.time)&&this._updateStyle(i,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,i=this._keyframes.size===1&&this.duration===0,r=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,h)=>{u===Pe?e.add(h):u===q&&t.add(h)}),i||c.set("offset",l/this.duration),r.push(c)});let s=[...e.values()],o=[...t.values()];if(i){let a=r[0],l=new Map(a);a.set("offset",0),l.set("offset",1),r=[a,l]}return Xt(this.element,r,s,o,this.duration,this.startTime,this.easing,!1)}},Vt=class extends lt{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,i,r,s,o,a=!1){super(e,t,o.delay),this.keyframes=i,this.preStyleProps=r,this.postStyleProps=s,this._stretchStartingKeyframe=a,this.timings={duration:o.duration,delay:o.delay,easing:o.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:i,easing:r}=this.timings;if(this._stretchStartingKeyframe&&t){let s=[],o=i+t,a=t/o,l=new Map(e[0]);l.set("offset",0),s.push(l);let c=new Map(e[0]);c.set("offset",Pr(a)),s.push(c);let u=e.length-1;for(let h=1;h<=u;h++){let v=new Map(e[h]),y=v.get("offset"),m=t+y*i;v.set("offset",Pr(m/o)),s.push(v)}i=o,t=0,r="",e=s}return Xt(this.element,e,this.preStyleProps,this.postStyleProps,i,t,r,!0)}};function Pr(n,e=3){let t=Math.pow(10,e-1);return Math.round(n*t)/t}function wn(n,e){let t=new Map,i;return n.forEach(r=>{if(r==="*"){i??=e.keys();for(let s of i)t.set(s,q)}else for(let[s,o]of r)t.set(s,o)}),t}function Mr(n,e,t,i,r,s,o,a,l,c,u,h,v){return{type:0,element:n,triggerName:e,isRemovalTransition:r,fromState:t,fromStyles:s,toState:i,toStyles:o,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:h,errors:v}}var Ft={},ct=class{_triggerName;ast;_stateStyles;constructor(e,t,i){this._triggerName=e,this.ast=t,this._stateStyles=i}match(e,t,i,r){return Tn(this.ast.matchers,e,t,i,r)}buildStyles(e,t,i){let r=this._stateStyles.get("*");return e!==void 0&&(r=this._stateStyles.get(e?.toString())||r),r?r.buildStyles(t,i):new Map}build(e,t,i,r,s,o,a,l,c,u){let h=[],v=this.ast.options&&this.ast.options.params||Ft,y=a&&a.params||Ft,m=this.buildStyles(i,y,h),g=l&&l.params||Ft,b=this.buildStyles(r,g,h),T=new Set,A=new Map,N=new Map,M=r==="void",he={params:Br(g,v),delay:this.ast.options?.delay},Q=u?[]:Lr(e,t,this.ast.animation,s,o,m,b,he,c,h),k=0;return Q.forEach(D=>{k=Math.max(D.duration+D.delay,k)}),h.length?Mr(t,this._triggerName,i,r,M,m,b,[],[],A,N,k,h):(Q.forEach(D=>{let ee=D.element,de=O(A,ee,new Set);D.preStyleProps.forEach(te=>de.add(te));let ti=O(N,ee,new Set);D.postStyleProps.forEach(te=>ti.add(te)),ee!==t&&T.add(ee)}),Mr(t,this._triggerName,i,r,M,m,b,Q,[...T.values()],A,N,k))}};function Tn(n,e,t,i,r){return n.some(s=>s(e,t,i,r))}function Br(n,e){let t=me({},e);return Object.entries(n).forEach(([i,r])=>{r!=null&&(t[i]=r)}),t}var Qt=class{styles;defaultParams;normalizer;constructor(e,t,i){this.styles=e,this.defaultParams=t,this.normalizer=i}buildStyles(e,t){let i=new Map,r=Br(e,this.defaultParams);return this.styles.styles.forEach(s=>{typeof s!="string"&&s.forEach((o,a)=>{o&&(o=ye(o,r,t));let l=this.normalizer.normalizePropertyName(a,t);o=this.normalizer.normalizeStyleValue(a,l,o,t),i.set(a,o)})}),i}};function An(n,e,t){return new $t(n,e,t)}var $t=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,i){this.name=e,this.ast=t,this._normalizer=i,t.states.forEach(r=>{let s=r.options&&r.options.params||{};this.states.set(r.name,new Qt(r.style,s,i))}),Cr(this.states,"true","1"),Cr(this.states,"false","0"),t.transitions.forEach(r=>{this.transitionFactories.push(new ct(e,r,this.states))}),this.fallbackTransition=Pn(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,i,r){return this.transitionFactories.find(o=>o.match(e,t,i,r))||null}matchStyles(e,t,i){return this.fallbackTransition.buildStyles(e,t,i)}};function Pn(n,e,t){let i=[(o,a)=>!0],r={type:_.Sequence,steps:[],options:null},s={type:_.Transition,animation:r,matchers:i,options:null,queryCount:0,depCount:0};return new ct(n,s,e)}function Cr(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}var Mn=new De,Wt=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,i){this.bodyNode=e,this._driver=t,this._normalizer=i}register(e,t){let i=[],r=[],s=Fr(this._driver,t,i,r);if(i.length)throw cr(i);this._animations.set(e,s)}_buildPlayer(e,t,i){let r=e.element,s=Pt(this._normalizer,e.keyframes,t,i);return this._driver.animate(r,s,e.duration,e.delay,e.easing,[],!0)}create(e,t,i={}){let r=[],s=this._animations.get(e),o,a=new Map;if(s?(o=Lr(this._driver,t,s,Dt,Xe,new Map,new Map,i,Mn,r),o.forEach(u=>{let h=O(a,u.element,new Map);u.postStyleProps.forEach(v=>h.set(v,null))})):(r.push(ur()),o=[]),r.length)throw hr(r);a.forEach((u,h)=>{u.forEach((v,y)=>{u.set(y,this._driver.computeStyle(h,y,q))})});let l=o.map(u=>{let h=a.get(u.element);return this._buildPlayer(u,new Map,h)}),c=x(l);return this._playersById.set(e,c),c.onDestroy(()=>this.destroy(e)),this.players.push(c),c}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let i=this.players.indexOf(t);i>=0&&this.players.splice(i,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw dr(e);return t}listen(e,t,i,r){let s=He(t,"","","");return xe(this._getPlayer(e),i,s,r),()=>{}}command(e,t,i,r){if(i=="register"){this.register(e,r[0]);return}if(i=="create"){let o=r[0]||{};this.create(e,t,o);return}let s=this._getPlayer(e);switch(i){case"play":s.play();break;case"pause":s.pause();break;case"reset":s.reset();break;case"restart":s.restart();break;case"finish":s.finish();break;case"init":s.init();break;case"setPosition":s.setPosition(parseFloat(r[0]));break;case"destroy":this.destroy(e);break}}},Nr="ng-animate-queued",Cn=".ng-animate-queued",Lt="ng-animate-disabled",Nn=".ng-animate-disabled",kn="ng-star-inserted",Dn=".ng-star-inserted",On=[],zr={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},Rn={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},V="__ng_removed",Oe=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let i=e&&e.hasOwnProperty("value"),r=i?e.value:e;if(this.value=Fn(r),i){let s=e,{value:o}=s,a=si(s,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let i=this.options.params;Object.keys(t).forEach(r=>{i[r]==null&&(i[r]=t[r])})}}},ke="void",Bt=new Oe(ke),Gt=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,i){this.id=e,this.hostElement=t,this._engine=i,this._hostClassName="ng-tns-"+e,K(t,this._hostClassName)}listen(e,t,i,r){if(!this._triggers.has(t))throw mr(i,t);if(i==null||i.length==0)throw fr(t);if(!Ln(i))throw pr(i,t);let s=O(this._elementListeners,e,[]),o={name:t,phase:i,callback:r};s.push(o);let a=O(this._engine.statesByElement,e,new Map);return a.has(t)||(K(e,Me),K(e,Me+"-"+t),a.set(t,Bt)),()=>{this._engine.afterFlush(()=>{let l=s.indexOf(o);l>=0&&s.splice(l,1),this._triggers.has(t)||a.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw gr(e);return t}trigger(e,t,i,r=!0){let s=this._getTrigger(t),o=new Re(this.id,t,e),a=this._engine.statesByElement.get(e);a||(K(e,Me),K(e,Me+"-"+t),this._engine.statesByElement.set(e,a=new Map));let l=a.get(t),c=new Oe(i,this.id);if(!(i&&i.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(t,c),l||(l=Bt),!(c.value===ke)&&l.value===c.value){if(!Kn(l.params,c.params)){let g=[],b=s.matchStyles(l.value,l.params,g),T=s.matchStyles(c.value,c.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{J(e,b),j(e,T)})}return}let v=O(this._engine.playersByElement,e,[]);v.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=s.matchTransition(l.value,c.value,e,c.params),m=!1;if(!y){if(!r)return;y=s.fallbackTransition,m=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:c,player:o,isFallbackTransition:m}),m||(K(e,Nr),o.onStart(()=>{ve(e,Nr)})),o.onDone(()=>{let g=this.players.indexOf(o);g>=0&&this.players.splice(g,1);let b=this._engine.playersByElement.get(e);if(b){let T=b.indexOf(o);T>=0&&b.splice(T,1)}}),this.players.push(o),v.push(o),o}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,i)=>{this._elementListeners.set(i,t.filter(r=>r.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(i=>i.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let i=this._engine.driver.query(e,Ce,!0);i.forEach(r=>{if(r[V])return;let s=this._engine.fetchNamespacesByElement(r);s.size?s.forEach(o=>o.triggerLeaveAnimation(r,t,!1,!0)):this.clearElementCache(r)}),this._engine.afterFlushAnimationsDone(()=>i.forEach(r=>this.clearElementCache(r)))}triggerLeaveAnimation(e,t,i,r){let s=this._engine.statesByElement.get(e),o=new Map;if(s){let a=[];if(s.forEach((l,c)=>{if(o.set(c,l.value),this._triggers.has(c)){let u=this.trigger(e,c,ke,r);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,o),i&&x(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),i=this._engine.statesByElement.get(e);if(t&&i){let r=new Set;t.forEach(s=>{let o=s.name;if(r.has(o))return;r.add(o);let l=this._triggers.get(o).fallbackTransition,c=i.get(o)||Bt,u=new Oe(ke),h=new Re(this.id,o,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:o,transition:l,fromState:c,toState:u,player:h,isFallbackTransition:!0})})}}removeNode(e,t){let i=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let r=!1;if(i.totalAnimations){let s=i.players.length?i.playersByQueriedElement.get(e):[];if(s&&s.length)r=!0;else{let o=e;for(;o=o.parentNode;)if(i.statesByElement.get(o)){r=!0;break}}}if(this.prepareLeaveAnimationListeners(e),r)i.markElementAsRemoved(this.id,e,!1,t);else{let s=e[V];(!s||s===zr)&&(i.afterFlush(()=>this.clearElementCache(e)),i.destroyInnerAnimations(e),i._onRemovalComplete(e,t))}}insertNode(e,t){K(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(i=>{let r=i.player;if(r.destroyed)return;let s=i.element,o=this._elementListeners.get(s);o&&o.forEach(a=>{if(a.name==i.triggerName){let l=He(s,i.triggerName,i.fromState.value,i.toState.value);l._data=e,xe(i.player,a.phase,l,a.callback)}}),r.markedForDestroy?this._engine.afterFlush(()=>{r.destroy()}):t.push(i)}),this._queue=[],t.sort((i,r)=>{let s=i.transition.ast.depCount,o=r.transition.ast.depCount;return s==0||o==0?s-o:this._engine.driver.containsElement(i.element,r.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},xt=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,i){this.bodyNode=e,this.driver=t,this._normalizer=i}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(i=>{i.queued&&e.push(i)})}),e}createNamespace(e,t){let i=new Gt(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(i,t):(this.newHostElements.set(t,i),this.collectEnterElement(t)),this._namespaceLookup[e]=i}_balanceNamespaceList(e,t){let i=this._namespaceList,r=this.namespacesByHostElement;if(i.length-1>=0){let o=!1,a=this.driver.getParentElement(t);for(;a;){let l=r.get(a);if(l){let c=i.indexOf(l);i.splice(c+1,0,e),o=!0;break}a=this.driver.getParentElement(a)}o||i.unshift(e)}else i.push(e);return r.set(t,e),e}register(e,t){let i=this._namespaceLookup[e];return i||(i=this.createNamespace(e,t)),i}registerTrigger(e,t,i){let r=this._namespaceLookup[e];r&&r.register(t,i)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let i=this._fetchNamespace(e);this.namespacesByHostElement.delete(i.hostElement);let r=this._namespaceList.indexOf(i);r>=0&&this._namespaceList.splice(r,1),i.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,i=this.statesByElement.get(e);if(i){for(let r of i.values())if(r.namespaceId){let s=this._fetchNamespace(r.namespaceId);s&&t.add(s)}}return t}trigger(e,t,i,r){if(rt(t)){let s=this._fetchNamespace(e);if(s)return s.trigger(t,i,r),!0}return!1}insertNode(e,t,i,r){if(!rt(t))return;let s=t[V];if(s&&s.setForRemoval){s.setForRemoval=!1,s.setForMove=!0;let o=this.collectedLeaveElements.indexOf(t);o>=0&&this.collectedLeaveElements.splice(o,1)}if(e){let o=this._fetchNamespace(e);o&&o.insertNode(t,i)}r&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),K(e,Lt)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),ve(e,Lt))}removeNode(e,t,i){if(rt(t)){let r=e?this._fetchNamespace(e):null;r?r.removeNode(t,i):this.markElementAsRemoved(e,t,!1,i);let s=this.namespacesByHostElement.get(t);s&&s.id!==e&&s.removeNode(t,i)}else this._onRemovalComplete(t,i)}markElementAsRemoved(e,t,i,r,s){this.collectedLeaveElements.push(t),t[V]={namespaceId:e,setForRemoval:r,hasAnimation:i,removedBeforeQueried:!1,previousTriggersValues:s}}listen(e,t,i,r,s){return rt(t)?this._fetchNamespace(e).listen(t,i,r,s):()=>{}}_buildInstruction(e,t,i,r,s){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,i,r,e.fromState.options,e.toState.options,t,s)}destroyInnerAnimations(e){let t=this.driver.query(e,Ce,!0);t.forEach(i=>this.destroyActiveAnimationsForElement(i)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Ze,!0),t.forEach(i=>this.finishActiveQueriedAnimationOnElement(i)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(i=>{i.queued?i.markedForDestroy=!0:i.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(i=>i.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return x(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[V];if(t&&t.setForRemoval){if(e[V]=zr,t.namespaceId){this.destroyInnerAnimations(e);let i=this._fetchNamespace(t.namespaceId);i&&i.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Lt)&&this.markElementAsDisabled(e,!1),this.driver.query(e,Nn,!0).forEach(i=>{this.markElementAsDisabled(i,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((i,r)=>this._balanceNamespaceList(i,r)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let i=0;i<this.collectedEnterElements.length;i++){let r=this.collectedEnterElements[i];K(r,kn)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let i=[];try{t=this._flushAnimations(i,e)}finally{for(let r=0;r<i.length;r++)i[r]()}}else for(let i=0;i<this.collectedLeaveElements.length;i++){let r=this.collectedLeaveElements[i];this.processLeaveNode(r)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(i=>i()),this._flushFns=[],this._whenQuietFns.length){let i=this._whenQuietFns;this._whenQuietFns=[],t.length?x(t).onDone(()=>{i.forEach(r=>r())}):i.forEach(r=>r())}}reportError(e){throw yr(e)}_flushAnimations(e,t){let i=new De,r=[],s=new Map,o=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(d=>{u.add(d);let f=this.driver.query(d,Cn,!0);for(let p=0;p<f.length;p++)u.add(f[p])});let h=this.bodyNode,v=Array.from(this.statesByElement.keys()),y=Or(v,this.collectedEnterElements),m=new Map,g=0;y.forEach((d,f)=>{let p=Dt+g++;m.set(f,p),d.forEach(S=>K(S,p))});let b=[],T=new Set,A=new Set;for(let d=0;d<this.collectedLeaveElements.length;d++){let f=this.collectedLeaveElements[d],p=f[V];p&&p.setForRemoval&&(b.push(f),T.add(f),p.hasAnimation?this.driver.query(f,Dn,!0).forEach(S=>T.add(S)):A.add(f))}let N=new Map,M=Or(v,Array.from(T));M.forEach((d,f)=>{let p=Xe+g++;N.set(f,p),d.forEach(S=>K(S,p))}),e.push(()=>{y.forEach((d,f)=>{let p=m.get(f);d.forEach(S=>ve(S,p))}),M.forEach((d,f)=>{let p=N.get(f);d.forEach(S=>ve(S,p))}),b.forEach(d=>{this.processLeaveNode(d)})});let he=[],Q=[];for(let d=this._namespaceList.length-1;d>=0;d--)this._namespaceList[d].drainQueuedTransitions(t).forEach(p=>{let S=p.player,P=p.element;if(he.push(S),this.collectedEnterElements.length){let C=P[V];if(C&&C.setForMove){if(C.previousTriggersValues&&C.previousTriggersValues.has(p.triggerName)){let ie=C.previousTriggersValues.get(p.triggerName),L=this.statesByElement.get(p.element);if(L&&L.has(p.triggerName)){let Le=L.get(p.triggerName);Le.value=ie,L.set(p.triggerName,Le)}}S.destroy();return}}let $=!h||!this.driver.containsElement(h,P),I=N.get(P),H=m.get(P),w=this._buildInstruction(p,i,H,I,$);if(w.errors&&w.errors.length){Q.push(w);return}if($){S.onStart(()=>J(P,w.fromStyles)),S.onDestroy(()=>j(P,w.toStyles)),r.push(S);return}if(p.isFallbackTransition){S.onStart(()=>J(P,w.fromStyles)),S.onDestroy(()=>j(P,w.toStyles)),r.push(S);return}let ni=[];w.timelines.forEach(C=>{C.stretchStartingKeyframe=!0,this.disabledNodes.has(C.element)||ni.push(C)}),w.timelines=ni,i.append(P,w.timelines);let xr={instruction:w,player:S,element:P};o.push(xr),w.queriedElements.forEach(C=>O(a,C,[]).push(S)),w.preStyleProps.forEach((C,ie)=>{if(C.size){let L=l.get(ie);L||l.set(ie,L=new Set),C.forEach((Le,_t)=>L.add(_t))}}),w.postStyleProps.forEach((C,ie)=>{let L=c.get(ie);L||c.set(ie,L=new Set),C.forEach((Le,_t)=>L.add(_t))})});if(Q.length){let d=[];Q.forEach(f=>{d.push(vr(f.triggerName,f.errors))}),he.forEach(f=>f.destroy()),this.reportError(d)}let k=new Map,D=new Map;o.forEach(d=>{let f=d.element;i.has(f)&&(D.set(f,f),this._beforeAnimationBuild(d.player.namespaceId,d.instruction,k))}),r.forEach(d=>{let f=d.element;this._getPreviousPlayers(f,!1,d.namespaceId,d.triggerName,null).forEach(S=>{O(k,f,[]).push(S),S.destroy()})});let ee=b.filter(d=>Rr(d,l,c)),de=new Map;Dr(de,this.driver,A,c,q).forEach(d=>{Rr(d,l,c)&&ee.push(d)});let te=new Map;y.forEach((d,f)=>{Dr(te,this.driver,new Set(d),l,Pe)}),ee.forEach(d=>{let f=de.get(d),p=te.get(d);de.set(d,new Map([...f?.entries()??[],...p?.entries()??[]]))});let vt=[],ii=[],ri={};o.forEach(d=>{let{element:f,player:p,instruction:S}=d;if(i.has(f)){if(u.has(f)){p.onDestroy(()=>j(f,S.toStyles)),p.disabled=!0,p.overrideTotalTime(S.totalTime),r.push(p);return}let P=ri;if(D.size>1){let I=f,H=[];for(;I=I.parentNode;){let w=D.get(I);if(w){P=w;break}H.push(I)}H.forEach(w=>D.set(w,P))}let $=this._buildAnimation(p.namespaceId,S,k,s,te,de);if(p.setRealPlayer($),P===ri)vt.push(p);else{let I=this.playersByElement.get(P);I&&I.length&&(p.parentPlayer=x(I)),r.push(p)}}else J(f,S.fromStyles),p.onDestroy(()=>j(f,S.toStyles)),ii.push(p),u.has(f)&&r.push(p)}),ii.forEach(d=>{let f=s.get(d.element);if(f&&f.length){let p=x(f);d.setRealPlayer(p)}}),r.forEach(d=>{d.parentPlayer?d.syncPlayerEvents(d.parentPlayer):d.destroy()});for(let d=0;d<b.length;d++){let f=b[d],p=f[V];if(ve(f,Xe),p&&p.hasAnimation)continue;let S=[];if(a.size){let $=a.get(f);$&&$.length&&S.push(...$);let I=this.driver.query(f,Ze,!0);for(let H=0;H<I.length;H++){let w=a.get(I[H]);w&&w.length&&S.push(...w)}}let P=S.filter($=>!$.destroyed);P.length?Bn(this,f,P):this.processLeaveNode(f)}return b.length=0,vt.forEach(d=>{this.players.push(d),d.onDone(()=>{d.destroy();let f=this.players.indexOf(d);this.players.splice(f,1)}),d.play()}),vt}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,i,r,s){let o=[];if(t){let a=this.playersByQueriedElement.get(e);a&&(o=a)}else{let a=this.playersByElement.get(e);if(a){let l=!s||s==ke;a.forEach(c=>{c.queued||!l&&c.triggerName!=r||o.push(c)})}}return(i||r)&&(o=o.filter(a=>!(i&&i!=a.namespaceId||r&&r!=a.triggerName))),o}_beforeAnimationBuild(e,t,i){let r=t.triggerName,s=t.element,o=t.isRemovalTransition?void 0:e,a=t.isRemovalTransition?void 0:r;for(let l of t.timelines){let c=l.element,u=c!==s,h=O(i,c,[]);this._getPreviousPlayers(c,u,o,a,t.toState).forEach(y=>{let m=y.getRealPlayer();m.beforeDestroy&&m.beforeDestroy(),y.destroy(),h.push(y)})}J(s,t.fromStyles)}_buildAnimation(e,t,i,r,s,o){let a=t.triggerName,l=t.element,c=[],u=new Set,h=new Set,v=t.timelines.map(m=>{let g=m.element;u.add(g);let b=g[V];if(b&&b.removedBeforeQueried)return new oe(m.duration,m.delay);let T=g!==l,A=zn((i.get(g)||On).map(k=>k.getRealPlayer())).filter(k=>{let D=k;return D.element?D.element===g:!1}),N=s.get(g),M=o.get(g),he=Pt(this._normalizer,m.keyframes,N,M),Q=this._buildPlayer(m,he,A);if(m.subTimeline&&r&&h.add(g),T){let k=new Re(e,a,g);k.setRealPlayer(Q),c.push(k)}return Q});c.forEach(m=>{O(this.playersByQueriedElement,m.element,[]).push(m),m.onDone(()=>In(this.playersByQueriedElement,m.element,m))}),u.forEach(m=>K(m,Ot));let y=x(v);return y.onDestroy(()=>{u.forEach(m=>ve(m,Ot)),j(l,t.toStyles)}),h.forEach(m=>{O(r,m,[]).push(y)}),y}_buildPlayer(e,t,i){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,i):new oe(e.duration,e.delay)}},Re=class{namespaceId;triggerName;element;_player=new oe;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,i){this.namespaceId=e,this.triggerName=t,this.element=i}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,i)=>{t.forEach(r=>xe(e,i,void 0,r))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){O(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function In(n,e,t){let i=n.get(e);if(i){if(i.length){let r=i.indexOf(t);i.splice(r,1)}i.length==0&&n.delete(e)}return i}function Fn(n){return n??null}function rt(n){return n&&n.nodeType===1}function Ln(n){return n=="start"||n=="done"}function kr(n,e){let t=n.style.display;return n.style.display=e??"none",t}function Dr(n,e,t,i,r){let s=[];t.forEach(l=>s.push(kr(l)));let o=[];i.forEach((l,c)=>{let u=new Map;l.forEach(h=>{let v=e.computeStyle(c,h,r);u.set(h,v),(!v||v.length==0)&&(c[V]=Rn,o.push(c))}),n.set(c,u)});let a=0;return t.forEach(l=>kr(l,s[a++])),o}function Or(n,e){let t=new Map;if(n.forEach(a=>t.set(a,[])),e.length==0)return t;let i=1,r=new Set(e),s=new Map;function o(a){if(!a)return i;let l=s.get(a);if(l)return l;let c=a.parentNode;return t.has(c)?l=c:r.has(c)?l=i:l=o(c),s.set(a,l),l}return e.forEach(a=>{let l=o(a);l!==i&&t.get(l).push(a)}),t}function K(n,e){n.classList?.add(e)}function ve(n,e){n.classList?.remove(e)}function Bn(n,e,t){x(t).onDone(()=>n.processLeaveNode(e))}function zn(n){let e=[];return Kr(n,e),e}function Kr(n,e){for(let t=0;t<n.length;t++){let i=n[t];i instanceof Qe?Kr(i.players,e):e.push(i)}}function Kn(n,e){let t=Object.keys(n),i=Object.keys(e);if(t.length!=i.length)return!1;for(let r=0;r<t.length;r++){let s=t[r];if(!e.hasOwnProperty(s)||n[s]!==e[s])return!1}return!0}function Rr(n,e,t){let i=t.get(n);if(!i)return!1;let r=e.get(n);return r?i.forEach(s=>r.add(s)):e.set(n,i),t.delete(n),!0}var _e=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,i){this._driver=t,this._normalizer=i,this._transitionEngine=new xt(e.body,t,i),this._timelineEngine=new Wt(e.body,t,i),this._transitionEngine.onRemovalComplete=(r,s)=>this.onRemovalComplete(r,s)}registerTrigger(e,t,i,r,s){let o=e+"-"+r,a=this._triggerCache[o];if(!a){let l=[],c=[],u=Fr(this._driver,s,l,c);if(l.length)throw lr(r,l);a=An(r,u,this._normalizer),this._triggerCache[o]=a}this._transitionEngine.registerTrigger(t,r,a)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,i,r){this._transitionEngine.insertNode(e,t,i,r)}onRemove(e,t,i){this._transitionEngine.removeNode(e,t,i)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,i,r){if(i.charAt(0)=="@"){let[s,o]=Mt(i),a=r;this._timelineEngine.command(s,t,o,a)}else this._transitionEngine.trigger(e,t,i,r)}listen(e,t,i,r,s){if(i.charAt(0)=="@"){let[o,a]=Mt(i);return this._timelineEngine.listen(o,t,a,s)}return this._transitionEngine.listen(e,t,i,r,s)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function Un(n,e){let t=null,i=null;return Array.isArray(e)&&e.length?(t=zt(e[0]),e.length>1&&(i=zt(e[e.length-1]))):e instanceof Map&&(t=zt(e)),t||i?new qn(n,t,i):null}var qn=(()=>{class n{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,i,r){this._element=t,this._startStyles=i,this._endStyles=r;let s=n.initialStylesByElement.get(t);s||n.initialStylesByElement.set(t,s=new Map),this._initialStyles=s}start(){this._state<1&&(this._startStyles&&j(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(j(this._element,this._initialStyles),this._endStyles&&(j(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(n.initialStylesByElement.delete(this._element),this._startStyles&&(J(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(J(this._element,this._endStyles),this._endStyles=null),j(this._element,this._initialStyles),this._state=3)}}return n})();function zt(n){let e=null;return n.forEach((t,i)=>{jn(i)&&(e=e||new Map,e.set(i,t))}),e}function jn(n){return n==="display"||n==="position"}var ut=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,i,r){this.element=e,this.keyframes=t,this.options=i,this._specialStyles=r,this._duration=i.duration,this._delay=i.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(i=>{t.push(Object.fromEntries(i))}),t}_triggerWebAnimation(e,t,i){return e.animate(this._convertKeyframesToObject(t),i)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((i,r)=>{r!=="offset"&&e.set(r,this._finished?i:et(this.element,r))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(i=>i()),t.length=0}},ht=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return Ct(e,t)}getParentElement(e){return Ye(e)}query(e,t,i){return Nt(e,t,i)}computeStyle(e,t,i){return et(e,t)}animate(e,t,i,r,s,o=[]){let a=r==0?"both":"forwards",l={duration:i,delay:r,fill:a};s&&(l.easing=s);let c=new Map,u=o.filter(y=>y instanceof ut);br(i,r)&&u.forEach(y=>{y.currentSnapshot.forEach((m,g)=>c.set(g,m))});let h=Sr(t).map(y=>new Map(y));h=wr(e,h,c);let v=Un(e,h);return new ut(e,h,l,v)}};var nt="@",Ur="@.disabled",dt=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,i,r){this.namespaceId=e,this.delegate=t,this.engine=i,this._onDestroy=r}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,i,r=!0){this.delegate.insertBefore(e,t,i),this.engine.onInsert(this.namespaceId,t,e,r)}removeChild(e,t,i){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,i,r){this.delegate.setAttribute(e,t,i,r)}removeAttribute(e,t,i){this.delegate.removeAttribute(e,t,i)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,i,r){this.delegate.setStyle(e,t,i,r)}removeStyle(e,t,i){this.delegate.removeStyle(e,t,i)}setProperty(e,t,i){t.charAt(0)==nt&&t==Ur?this.disableAnimations(e,!!i):this.delegate.setProperty(e,t,i)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,i,r){return this.delegate.listen(e,t,i,r)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},Ht=class extends dt{factory;constructor(e,t,i,r,s){super(t,i,r,s),this.factory=e,this.namespaceId=t}setProperty(e,t,i){t.charAt(0)==nt?t.charAt(1)=="."&&t==Ur?(i=i===void 0?!0:!!i,this.disableAnimations(e,i)):this.engine.process(this.namespaceId,e,t.slice(1),i):this.delegate.setProperty(e,t,i)}listen(e,t,i,r){if(t.charAt(0)==nt){let s=Vn(e),o=t.slice(1),a="";return o.charAt(0)!=nt&&([o,a]=Qn(o)),this.engine.listen(this.namespaceId,s,o,a,l=>{let c=l._data||-1;this.factory.scheduleListenerCallback(c,i,l)})}return this.delegate.listen(e,t,i,r)}};function Vn(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}function Qn(n){let e=n.indexOf("."),t=n.substring(0,e),i=n.slice(e+1);return[t,i]}var mt=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,i){this.delegate=e,this.engine=t,this._zone=i,t.onRemovalComplete=(r,s)=>{s?.removeChild(null,r)}}createRenderer(e,t){let i="",r=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let c=this._rendererCache,u=c.get(r);if(!u){let h=()=>c.delete(r);u=new dt(i,r,this.engine,h),c.set(r,u)}return u}let s=t.id,o=t.id+"-"+this._currentId;this._currentId++,this.engine.register(o,e);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(s,o,e,c.name,c)};return t.data.animation.forEach(a),new Ht(this,o,r,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,i){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(i));return}let r=this._animationCallbacksBuffer;r.length==0&&queueMicrotask(()=>{this._zone.run(()=>{r.forEach(s=>{let[o,a]=s;o(a)}),this._animationCallbacksBuffer=[]})}),r.push([t,i])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};var xn=(()=>{class n extends _e{constructor(t,i,r){super(t,i,r)}ngOnDestroy(){this.flush()}static \u0275fac=function(i){return new(i||n)(B(Pi),B(ce),B(ue))};static \u0275prov=U({token:n,factory:n.\u0275fac})}return n})();function Hn(){return new st}function Yn(n,e,t){return new mt(n,e,t)}var qr=[{provide:ue,useFactory:Hn},{provide:_e,useClass:xn},{provide:vi,useFactory:Yn,deps:[Ni,_e,Ke]}],Ns=[{provide:ce,useClass:Yt},{provide:St,useValue:"NoopAnimations"},...qr],Xn=[{provide:ce,useFactory:()=>new ht},{provide:St,useFactory:()=>"BrowserAnimations"},...qr];function jr(){return gi("NgEagerAnimations"),[...Xn]}var Ie="Service workers are disabled or not supported by this browser",Ee=class{serviceWorker;worker;registration;events;constructor(e,t){if(this.serviceWorker=e,!e)this.worker=this.events=this.registration=new Be(i=>i.error(new Error(Ie)));else{let i=null,r=new ze;this.worker=new Be(c=>(i!==null&&c.next(i),r.subscribe(u=>c.next(u))));let s=()=>{let{controller:c}=e;c!==null&&(i=c,r.next(i))};e.addEventListener("controllerchange",s),s(),this.registration=this.worker.pipe(re(()=>e.getRegistration()));let o=new ze;this.events=o.asObservable();let a=c=>{let{data:u}=c;u?.type&&o.next(u)};e.addEventListener("message",a),t?.get(Et,null,{optional:!0})?.onDestroy(()=>{e.removeEventListener("controllerchange",s),e.removeEventListener("message",a)})}}postMessage(e,t){return new Promise(i=>{this.worker.pipe(Y(1)).subscribe(r=>{r.postMessage(me({action:e},t)),i()})})}postMessageWithOperation(e,t,i){let r=this.waitForOperationCompleted(i),s=this.postMessage(e,t);return Promise.all([s,r]).then(([,o])=>o)}generateNonce(){return Math.round(Math.random()*1e7)}eventsOfType(e){let t;return typeof e=="string"?t=i=>i.type===e:t=i=>e.includes(i.type),this.events.pipe(we(t))}nextEventOfType(e){return this.eventsOfType(e).pipe(Y(1))}waitForOperationCompleted(e){return new Promise((t,i)=>{this.eventsOfType("OPERATION_COMPLETED").pipe(we(r=>r.nonce===e),Y(1),be(r=>{if(r.result!==void 0)return r.result;throw new Error(r.error)})).subscribe({next:t,error:i})})}get isEnabled(){return!!this.serviceWorker}},Zt=(()=>{class n{sw;messages;notificationClicks;subscription;get isEnabled(){return this.sw.isEnabled}pushManager=null;subscriptionChanges=new ze;constructor(t){if(this.sw=t,!t.isEnabled){this.messages=fe,this.notificationClicks=fe,this.subscription=fe;return}this.messages=this.sw.eventsOfType("PUSH").pipe(be(r=>r.data)),this.notificationClicks=this.sw.eventsOfType("NOTIFICATION_CLICK").pipe(be(r=>r.data)),this.pushManager=this.sw.registration.pipe(be(r=>r.pushManager));let i=this.pushManager.pipe(re(r=>r.getSubscription()));this.subscription=new Be(r=>{let s=i.subscribe(r),o=this.subscriptionChanges.subscribe(r);return()=>{s.unsubscribe(),o.unsubscribe()}})}requestSubscription(t){if(!this.sw.isEnabled||this.pushManager===null)return Promise.reject(new Error(Ie));let i={userVisibleOnly:!0},r=this.decodeBase64(t.serverPublicKey.replace(/_/g,"/").replace(/-/g,"+")),s=new Uint8Array(new ArrayBuffer(r.length));for(let o=0;o<r.length;o++)s[o]=r.charCodeAt(o);return i.applicationServerKey=s,new Promise((o,a)=>{this.pushManager.pipe(re(l=>l.subscribe(i)),Y(1)).subscribe({next:l=>{this.subscriptionChanges.next(l),o(l)},error:a})})}unsubscribe(){if(!this.sw.isEnabled)return Promise.reject(new Error(Ie));let t=i=>{if(i===null)throw new Error("Not subscribed to push notifications.");return i.unsubscribe().then(r=>{if(!r)throw new Error("Unsubscribe failed!");this.subscriptionChanges.next(null)})};return new Promise((i,r)=>{this.subscription.pipe(Y(1),re(t)).subscribe({next:i,error:r})})}decodeBase64(t){return atob(t)}static \u0275fac=function(i){return new(i||n)(B(Ee))};static \u0275prov=U({token:n,factory:n.\u0275fac})}return n})(),Zn=(()=>{class n{sw;versionUpdates;unrecoverable;get isEnabled(){return this.sw.isEnabled}constructor(t){if(this.sw=t,!t.isEnabled){this.versionUpdates=fe,this.unrecoverable=fe;return}this.versionUpdates=this.sw.eventsOfType(["VERSION_DETECTED","VERSION_INSTALLATION_FAILED","VERSION_READY","NO_NEW_VERSION_DETECTED"]),this.unrecoverable=this.sw.eventsOfType("UNRECOVERABLE_STATE")}checkForUpdate(){if(!this.sw.isEnabled)return Promise.reject(new Error(Ie));let t=this.sw.generateNonce();return this.sw.postMessageWithOperation("CHECK_FOR_UPDATES",{nonce:t},t)}activateUpdate(){if(!this.sw.isEnabled)return Promise.reject(new Error(Ie));let t=this.sw.generateNonce();return this.sw.postMessageWithOperation("ACTIVATE_UPDATE",{nonce:t},t)}static \u0275fac=function(i){return new(i||n)(B(Ee))};static \u0275prov=U({token:n,factory:n.\u0275fac})}return n})();var Qr=new ui("");function Jn(){let n=z(Fe);if(!("serviceWorker"in navigator&&n.enabled!==!1))return;let e=z(Qr),t=z(Ke),i=z(Et);t.runOutsideAngular(()=>{let r=navigator.serviceWorker,s=()=>r.controller?.postMessage({action:"INITIALIZE"});r.addEventListener("controllerchange",s),i.onDestroy(()=>{r.removeEventListener("controllerchange",s)})}),t.runOutsideAngular(()=>{let r,{registrationStrategy:s}=n;if(typeof s=="function")r=new Promise(o=>s().subscribe(()=>o()));else{let[o,...a]=(s||"registerWhenStable:30000").split(":");switch(o){case"registerImmediately":r=Promise.resolve();break;case"registerWithDelay":r=Vr(+a[0]||0);break;case"registerWhenStable":r=Promise.race([i.whenStable(),Vr(+a[0])]);break;default:throw new Error(`Unknown ServiceWorker registration strategy: ${n.registrationStrategy}`)}}r.then(()=>navigator.serviceWorker.register(e,{scope:n.scope}).catch(o=>console.error("Service worker registration failed with:",o)))})}function Vr(n){return new Promise(e=>setTimeout(e,n))}function es(n,e){return new Ee(n.enabled!==!1?navigator.serviceWorker:void 0,e)}var Fe=class{enabled;scope;registrationStrategy};function $r(n,e={}){return hi([Zt,Zn,{provide:Qr,useValue:n},{provide:Fe,useValue:e},{provide:Ee,useFactory:es,deps:[Fe,pi]},Si(Jn)])}var Jt=(n,e)=>F(void 0,null,function*(){let t=z(X),i=z(Z),r=z(pe);try{let s=yield i.getCurrentUser();if(console.log("User from getCurrentUser:",s),s){if(!s.is_approved){let o=s.role==="admin"?"Your account has been deactivated. Please contact support.":"Your account is pending approval. Please wait for administrator approval.";return r.open(o,"Close",{duration:5e3}),yield i.logout(),t.navigate(["/auth/login"]),!1}return!0}try{let o=yield ai(i.user$.pipe(li(3e3),we(a=>a!==void 0),Y(1)));if(o){if(!o.is_approved){let a=o.role==="admin"?"Your account has been deactivated. Please contact support.":"Your account is pending approval. Please wait for administrator approval.";return r.open(a,"Close",{duration:5e3}),yield i.logout(),t.navigate(["/auth/login"]),!1}return!0}else return t.navigate(["/auth/login"],{queryParams:{returnUrl:e.url}}),!1}catch(o){return console.warn("Auth state initialization timed out:",o),t.navigate(["/auth/login"],{queryParams:{returnUrl:e.url}}),!1}}catch(s){return console.error("Auth guard error:",s),t.navigate(["/auth/login"]),!1}});var ei=(n,e)=>F(void 0,null,function*(){let t=z(X),i=z(Z);try{let r=e.url.includes("/auth/login")||e.url.includes("/auth/register"),s=e.url==="/"||e.url==="";console.log("PublicGuard - Current URL:",e.url),console.log("PublicGuard - Is root route:",s);let o=yield i.getCurrentUser();if(console.log("PublicGuard - Current user:",o?"Authenticated":"Not authenticated"),o){let a=yield i.getUserRole();if(console.log("PublicGuard - User role:",a),a&&(r||s)){let l=i.getDashboardRouteForRole(a);return console.log(`PublicGuard - Authenticated user at ${e.url}, redirecting to ${l}`),yield t.navigate([l]),!1}return!0}return s?(console.log("PublicGuard - Unauthenticated user at root, redirecting to login"),yield t.navigate(["/auth/login"]),!1):r?(console.log("PublicGuard - Allowing unauthenticated user to access auth route"),!0):(console.log("PublicGuard - Unauthenticated user trying to access protected route, redirecting to login"),yield t.navigate(["/auth/login"],{queryParams:{returnUrl:e.url}}),!1)}catch(r){return console.error("Public guard error:",r),!e.url.includes("/auth/login")&&!e.url.includes("/auth/register")&&(console.log("PublicGuard - Error occurred, redirecting to login"),yield t.navigate(["/auth/login"])),!0}});var Wr=[{path:"",pathMatch:"full",redirectTo:"/auth/login"},{path:"auth",children:[{path:"login",loadComponent:()=>import("./chunk-BBS65SMW.js").then(n=>n.LoginComponent),canActivate:[ei]},{path:"register",loadComponent:()=>import("./chunk-OOGNXGI3.js").then(n=>n.RegisterComponent),canActivate:[ei]},{path:"profile",loadComponent:()=>import("./chunk-DRW4KKII.js").then(n=>n.ProfileComponent),canActivate:[Jt]}]},{path:"dashboard",canActivate:[Jt],children:[{path:"rider",loadComponent:()=>import("./chunk-F2YUDJYY.js").then(n=>n.RiderComponent)},{path:"driver",loadComponent:()=>import("./chunk-KNF5J5ZB.js").then(n=>n.DriverComponent)},{path:"admin",loadComponent:()=>import("./chunk-G7BUU72O.js").then(n=>n.AdminComponent)},{path:":role/messages",loadComponent:()=>import("./chunk-2MF43XNI.js").then(n=>n.MessagesComponent)},{path:":role/messages/:threadId",loadComponent:()=>import("./chunk-2MF43XNI.js").then(n=>n.MessagesComponent)},{path:"map-test",loadComponent:()=>import("./chunk-S4H4KTXL.js").then(n=>n.MapTestComponent)}]},{path:"**",redirectTo:"/auth/login"}];var Gr={providers:[Ai({eventCoalescing:!0}),Ri(Wr),jr(),Di(),di(Bi,zi,qi),$r("ngsw-worker.js",{enabled:!0,registrationStrategy:"registerWhenStable:30000"})]};var ft=class n{constructor(e,t,i){this.messageService=e;this.authService=t;this.router=i}unreadCount=0;refreshSubscription=null;ngOnInit(){this.checkUnreadMessages(),this.refreshSubscription=ci(3e4).pipe(re(()=>new Promise(e=>F(this,null,function*(){yield this.checkUnreadMessages(),e()})))).subscribe()}ngOnDestroy(){this.refreshSubscription&&this.refreshSubscription.unsubscribe()}checkUnreadMessages(){return F(this,null,function*(){try{this.unreadCount=yield this.messageService.getUnreadMessageCount()}catch(e){console.error("Error checking unread messages:",e)}})}navigateToMessages(){return F(this,null,function*(){try{let e=yield this.authService.getUserRole();e&&this.router.navigate(["/dashboard",e,"messages"])}catch(e){console.error("Error navigating to messages:",e)}})}static \u0275fac=function(t){return new(t||n)(G(Vi),G(Z),G(X))};static \u0275cmp=Ue({type:n,selectors:[["app-message-notification"]],decls:3,vars:2,consts:[["mat-icon-button","","matBadgeColor","warn",3,"click","matBadge","matBadgeHidden"]],template:function(t,i){t&1&&(ne(0,"button",0),Te("click",function(){return i.navigateToMessages()}),ne(1,"mat-icon"),Ae(2,"chat"),se()()),t&2&&qe("matBadge",i.unreadCount)("matBadgeHidden",i.unreadCount===0)},dependencies:[Ve,$i,Qi,$e,Fi,Ge,We],styles:["[_nghost-%COMP%]{display:inline-block}"]})};var pt=class n{constructor(e){this.snackBar=e;this.init()}registration=null;init(){return F(this,null,function*(){if("serviceWorker"in navigator)try{let e=yield navigator.serviceWorker.getRegistration();this.registration=e||null,this.registration&&navigator.serviceWorker.addEventListener("controllerchange",()=>{this.promptUpdate()})}catch(e){console.error("Error getting service worker registration",e)}})}checkForUpdates(){this.registration?this.registration.update().then(()=>console.log("Service worker update check completed")).catch(e=>console.error("Service worker update check failed",e)):"serviceWorker"in navigator&&navigator.serviceWorker.getRegistration().then(e=>(this.registration=e||null,e?e.update():Promise.resolve())).then(()=>{this.registration&&console.log("Service worker update check completed after registration")}).catch(e=>console.error("Service worker registration or update failed",e))}promptUpdate(){this.snackBar.open("A new version of the app is available","Update now",{duration:0}).onAction().subscribe(()=>{document.location.reload()})}static \u0275fac=function(t){return new(t||n)(B(pe))};static \u0275prov=U({token:n,factory:n.\u0275fac,providedIn:"root"})};var gt=class n{constructor(e,t){this.swPush=e;this.snackBar=t;this.swPushAvailable=!!this.swPush,this.checkNotificationPermission(),this.swPushAvailable&&this.swPush.isEnabled&&this.swPush.messages.subscribe(i=>{this.showNotification(i)})}VAPID_PUBLIC_KEY="YOUR_VAPID_PUBLIC_KEY";notificationsEnabled$=new oi(!1);swPushAvailable=!1;checkNotificationPermission(){if("Notification"in window){let e=Notification.permission;this.notificationsEnabled$.next(e==="granted")}}requestNotificationPermission(){return this.swPushAvailable?this.swPush.isEnabled?this.swPush.requestSubscription({serverPublicKey:this.VAPID_PUBLIC_KEY}).then(e=>(this.notificationsEnabled$.next(!0),!0)).catch(e=>(console.error("Could not subscribe to notifications",e),this.snackBar.open("Could not subscribe to notifications","Dismiss",{duration:3e3}),!1)):(this.snackBar.open("Push notifications are not supported in this browser","Dismiss",{duration:3e3}),Promise.resolve(!1)):(this.snackBar.open("Push notification service is not available","Dismiss",{duration:3e3}),Promise.resolve(!1))}showNotification(e){this.snackBar.open(e.notification?.title||"New notification","View",{duration:5e3}).onAction().subscribe(()=>{console.log("Notification clicked",e)})}getNotificationStatus(){return this.notificationsEnabled$.asObservable()}static \u0275fac=function(t){return new(t||n)(B(Zt,8),B(pe))};static \u0275prov=U({token:n,factory:n.\u0275fac,providedIn:"root"})};function us(n,e){if(n&1){let t=Ei();ne(0,"button",5),Te("click",function(){mi(t);let r=bi();return fi(r.logout())}),ne(1,"mat-icon"),Ae(2,"logout"),se(),Ae(3," Logout "),se()}}var yt=class n{constructor(e,t,i,r){this.authService=e;this.router=t;this.updateService=i;this.notificationService=r;this.authSubscription=this.authService.user$.subscribe(s=>{if(console.log("Auth state changed:",s?"authenticated":"not authenticated"),s)try{console.log("User is logged in, checking notification permission"),this.checkNotificationPermission()}catch(o){console.error("Error checking notification permission:",o)}})}authSubscription;notificationSubscription=null;updateCheckInterval=null;showNotificationPrompt=!1;pwaComponentsReady=!1;title="holy-rides";ngOnInit(){console.log("App component initialized");try{setTimeout(()=>{this.pwaComponentsReady=!0,console.log("Offline indicator enabled")},2e3),setTimeout(()=>{try{this.updateService.checkForUpdates(),console.log("Update service initialized"),this.updateCheckInterval=setInterval(()=>{this.updateService.checkForUpdates()},30*60*1e3)}catch(e){console.error("Error setting up update checks:",e)}},5e3)}catch(e){console.error("Error in ngOnInit:",e)}}logout(){return F(this,null,function*(){try{yield this.authService.logout(),yield this.router.navigate(["/auth/login"])}catch(e){console.error("Logout error:",e)}})}navigateToDashboard(){return F(this,null,function*(){try{let e=yield this.authService.getUserRole();e?yield this.router.navigate([`/dashboard/${e}`]):yield this.router.navigate(["/auth/login"])}catch(e){console.error("Error navigating to dashboard:",e),yield this.router.navigate(["/auth/login"])}})}checkNotificationPermission(){try{this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.notificationSubscription=this.notificationService.getNotificationStatus().subscribe(e=>{this.showNotificationPrompt=!e})}catch(e){console.error("Error checking notification permission:",e)}}requestNotifications(){this.notificationService.requestNotificationPermission().then(e=>{this.showNotificationPrompt=!e})}ngOnDestroy(){this.authSubscription&&this.authSubscription.unsubscribe(),this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.updateCheckInterval&&clearInterval(this.updateCheckInterval)}static \u0275fac=function(t){return new(t||n)(G(Z),G(X),G(pt),G(gt))};static \u0275cmp=Ue({type:n,selectors:[["app-root"]],decls:8,vars:3,consts:[["color","primary"],[1,"logo-container",3,"click"],["src","assets/hr.png","alt","Holy Rides Logo",1,"logo"],[1,"spacer"],["mat-button","",3,"click",4,"ngIf"],["mat-button","",3,"click"]],template:function(t,i){t&1&&(ne(0,"mat-toolbar",0)(1,"a",1),Te("click",function(){return i.navigateToDashboard()}),je(2,"img",2),se(),je(3,"span",3)(4,"app-message-notification"),_i(5,us,4,0,"button",4),wi(6,"async"),se(),je(7,"router-outlet")),t&2&&(yi(5),qe("ngIf",Ti(6,1,i.authService.user$)))},dependencies:[Ve,Mi,Ci,Oi,$e,Li,Ui,Ki,Ge,We,ji,ft],styles:[".spacer[_ngcontent-%COMP%]{flex:1 1 auto}mat-toolbar[_ngcontent-%COMP%]{margin-bottom:20px}.logo-container[_ngcontent-%COMP%]{display:flex;align-items:center;text-decoration:none;color:#fff;cursor:pointer}.logo[_ngcontent-%COMP%]{height:40px;margin-right:10px;filter:brightness(0) invert(1)}.notification-prompt[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;z-index:999;padding:16px}.notification-prompt[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 -2px 10px #0000001a}.notification-prompt[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-size:18px}.notification-prompt[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 16px;color:#0009}.notification-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}"]})};ki(yt,Gr).catch(n=>console.error("Error bootstrapping app:",n));
