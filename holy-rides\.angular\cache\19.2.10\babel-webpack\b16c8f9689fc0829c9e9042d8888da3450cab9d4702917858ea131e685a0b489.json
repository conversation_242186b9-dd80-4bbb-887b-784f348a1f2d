{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => {};\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined';\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket.\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint, options) {\n    var _this = this;\n    var _a;\n    this.accessTokenValue = null;\n    this.apiKey = null;\n    this.channels = [];\n    this.endPoint = '';\n    this.httpEndpoint = '';\n    this.headers = DEFAULT_HEADERS;\n    this.params = {};\n    this.timeout = DEFAULT_TIMEOUT;\n    this.heartbeatIntervalMs = 30000;\n    this.heartbeatTimer = undefined;\n    this.pendingHeartbeatRef = null;\n    this.ref = 0;\n    this.logger = noop;\n    this.conn = null;\n    this.sendBuffer = [];\n    this.serializer = new Serializer();\n    this.stateChangeCallbacks = {\n      open: [],\n      close: [],\n      error: [],\n      message: []\n    };\n    this.accessToken = null;\n    /**\n     * Use either custom fetch, if provided, or default fetch to make HTTP requests\n     *\n     * @internal\n     */\n    this._resolveFetch = customFetch => {\n      let _fetch;\n      if (customFetch) {\n        _fetch = customFetch;\n      } else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({\n          default: fetch\n        }) => fetch(...args));\n      } else {\n        _fetch = fetch;\n      }\n      return (...args) => _fetch(...args);\n    };\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n    this.httpEndpoint = httpEndpointURL(endPoint);\n    if (options === null || options === void 0 ? void 0 : options.transport) {\n      this.transport = options.transport;\n    } else {\n      this.transport = null;\n    }\n    if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n    if (options === null || options === void 0 ? void 0 : options.headers) this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n    if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n    if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n    if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n    const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue;\n      this.apiKey = accessTokenValue;\n    }\n    this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : tries => {\n      return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n    };\n    this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback) => {\n      return callback(JSON.stringify(payload));\n    };\n    this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n    this.reconnectTimer = new Timer(/*#__PURE__*/_asyncToGenerator(function* () {\n      _this.disconnect();\n      _this.connect();\n    }), this.reconnectAfterMs);\n    this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    if (options === null || options === void 0 ? void 0 : options.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported');\n      }\n      this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n      this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n    }\n    this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n  }\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect() {\n    if (this.conn) {\n      return;\n    }\n    if (this.transport) {\n      this.conn = new this.transport(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      return;\n    }\n    if (NATIVE_WEBSOCKET_AVAILABLE) {\n      this.conn = new WebSocket(this.endpointURL());\n      this.setupConnection();\n      return;\n    }\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null;\n      }\n    });\n    import('ws').then(({\n      default: WS\n    }) => {\n      this.conn = new WS(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      this.setupConnection();\n    });\n  }\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL() {\n    return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n      vsn: VSN\n    }));\n  }\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code, reason) {\n    if (this.conn) {\n      this.conn.onclose = function () {}; // noop\n      if (code) {\n        this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n      } else {\n        this.conn.close();\n      }\n      this.conn = null;\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.reconnectTimer.reset();\n    }\n  }\n  /**\n   * Returns all created channels\n   */\n  getChannels() {\n    return this.channels;\n  }\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  removeChannel(channel) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const status = yield channel.unsubscribe();\n      if (_this2.channels.length === 0) {\n        _this2.disconnect();\n      }\n      return status;\n    })();\n  }\n  /**\n   * Unsubscribes and removes all channels\n   */\n  removeAllChannels() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const values_1 = yield Promise.all(_this3.channels.map(channel => channel.unsubscribe()));\n      _this3.disconnect();\n      return values_1;\n    })();\n  }\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind, msg, data) {\n    this.logger(kind, msg, data);\n  }\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState() {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting;\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open;\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing;\n      default:\n        return CONNECTION_STATE.Closed;\n    }\n  }\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected() {\n    return this.connectionState() === CONNECTION_STATE.Open;\n  }\n  channel(topic, params = {\n    config: {}\n  }) {\n    const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n    this.channels.push(chan);\n    return chan;\n  }\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data) {\n    const {\n      topic,\n      event,\n      payload,\n      ref\n    } = data;\n    const callback = () => {\n      this.encode(data, result => {\n        var _a;\n        (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n      });\n    };\n    this.log('push', `${topic} ${event} (${ref})`, payload);\n    if (this.isConnected()) {\n      callback();\n    } else {\n      this.sendBuffer.push(callback);\n    }\n  }\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  setAuth() {\n    var _this4 = this;\n    return _asyncToGenerator(function* (token = null) {\n      let tokenToSend = token || _this4.accessToken && (yield _this4.accessToken()) || _this4.accessTokenValue;\n      if (tokenToSend) {\n        let parsed = null;\n        try {\n          parsed = JSON.parse(atob(tokenToSend.split('.')[1]));\n        } catch (_error) {}\n        if (parsed && parsed.exp) {\n          let now = Math.floor(Date.now() / 1000);\n          let valid = now - parsed.exp < 0;\n          if (!valid) {\n            _this4.log('auth', `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n            return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n          }\n        }\n        _this4.accessTokenValue = tokenToSend;\n        _this4.channels.forEach(channel => {\n          tokenToSend && channel.updateJoinPayload({\n            access_token: tokenToSend\n          });\n          if (channel.joinedOnce && channel._isJoined()) {\n            channel._push(CHANNEL_EVENTS.access_token, {\n              access_token: tokenToSend\n            });\n          }\n        });\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  sendHeartbeat() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      if (!_this5.isConnected()) {\n        return;\n      }\n      if (_this5.pendingHeartbeatRef) {\n        _this5.pendingHeartbeatRef = null;\n        _this5.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n        (_a = _this5.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n        return;\n      }\n      _this5.pendingHeartbeatRef = _this5._makeRef();\n      _this5.push({\n        topic: 'phoenix',\n        event: 'heartbeat',\n        payload: {},\n        ref: _this5.pendingHeartbeatRef\n      });\n      _this5.setAuth();\n    })();\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach(callback => callback());\n      this.sendBuffer = [];\n    }\n  }\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef() {\n    let newRef = this.ref + 1;\n    if (newRef === this.ref) {\n      this.ref = 0;\n    } else {\n      this.ref = newRef;\n    }\n    return this.ref.toString();\n  }\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic) {\n    let dupChannel = this.channels.find(c => c.topic === topic && (c._isJoined() || c._isJoining()));\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`);\n      dupChannel.unsubscribe();\n    }\n  }\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel) {\n    this.channels = this.channels.filter(c => c._joinRef() !== channel._joinRef());\n  }\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  setupConnection() {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer';\n      this.conn.onopen = () => this._onConnOpen();\n      this.conn.onerror = error => this._onConnError(error);\n      this.conn.onmessage = event => this._onConnMessage(event);\n      this.conn.onclose = event => this._onConnClose(event);\n    }\n  }\n  /** @internal */\n  _onConnMessage(rawMessage) {\n    this.decode(rawMessage.data, msg => {\n      let {\n        topic,\n        event,\n        payload,\n        ref\n      } = msg;\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null;\n      }\n      this.log('receive', `${payload.status || ''} ${topic} ${event} ${ref && '(' + ref + ')' || ''}`, payload);\n      this.channels.filter(channel => channel._isMember(topic)).forEach(channel => channel._trigger(event, payload, ref));\n      this.stateChangeCallbacks.message.forEach(callback => callback(msg));\n    });\n  }\n  /** @internal */\n  _onConnOpen() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      _this6.log('transport', `connected to ${_this6.endpointURL()}`);\n      _this6.flushSendBuffer();\n      _this6.reconnectTimer.reset();\n      if (!_this6.worker) {\n        _this6.heartbeatTimer && clearInterval(_this6.heartbeatTimer);\n        _this6.heartbeatTimer = setInterval(() => _this6.sendHeartbeat(), _this6.heartbeatIntervalMs);\n      } else {\n        if (_this6.workerUrl) {\n          _this6.log('worker', `starting worker for from ${_this6.workerUrl}`);\n        } else {\n          _this6.log('worker', `starting default worker`);\n        }\n        const objectUrl = _this6._workerObjectUrl(_this6.workerUrl);\n        _this6.workerRef = new Worker(objectUrl);\n        _this6.workerRef.onerror = error => {\n          _this6.log('worker', 'worker error', error.message);\n          _this6.workerRef.terminate();\n        };\n        _this6.workerRef.onmessage = event => {\n          if (event.data.event === 'keepAlive') {\n            _this6.sendHeartbeat();\n          }\n        };\n        _this6.workerRef.postMessage({\n          event: 'start',\n          interval: _this6.heartbeatIntervalMs\n        });\n      }\n      _this6.stateChangeCallbacks.open.forEach(callback => callback());\n    })();\n  }\n  /** @internal */\n  _onConnClose(event) {\n    this.log('transport', 'close', event);\n    this._triggerChanError();\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.reconnectTimer.scheduleTimeout();\n    this.stateChangeCallbacks.close.forEach(callback => callback(event));\n  }\n  /** @internal */\n  _onConnError(error) {\n    this.log('transport', error.message);\n    this._triggerChanError();\n    this.stateChangeCallbacks.error.forEach(callback => callback(error));\n  }\n  /** @internal */\n  _triggerChanError() {\n    this.channels.forEach(channel => channel._trigger(CHANNEL_EVENTS.error));\n  }\n  /** @internal */\n  _appendParams(url, params) {\n    if (Object.keys(params).length === 0) {\n      return url;\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?';\n    const query = new URLSearchParams(params);\n    return `${url}${prefix}${query}`;\n  }\n  _workerObjectUrl(url) {\n    let result_url;\n    if (url) {\n      result_url = url;\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], {\n        type: 'application/javascript'\n      });\n      result_url = URL.createObjectURL(blob);\n    }\n    return result_url;\n  }\n}\nclass WSWebSocketDummy {\n  constructor(address, _protocols, options) {\n    this.binaryType = 'arraybuffer';\n    this.onclose = () => {};\n    this.onerror = () => {};\n    this.onmessage = () => {};\n    this.onopen = () => {};\n    this.readyState = SOCKET_STATES.connecting;\n    this.send = () => {};\n    this.url = null;\n    this.url = address;\n    this.close = options.close;\n  }\n}", "map": {"version": 3, "names": ["CHANNEL_EVENTS", "CONNECTION_STATE", "DEFAULT_HEADERS", "DEFAULT_TIMEOUT", "SOCKET_STATES", "TRANSPORTS", "VSN", "WS_CLOSE_NORMAL", "Serializer", "Timer", "httpEndpointURL", "RealtimeChannel", "noop", "NATIVE_WEBSOCKET_AVAILABLE", "WebSocket", "WORKER_SCRIPT", "RealtimeClient", "constructor", "endPoint", "options", "_this", "_a", "accessTokenValue", "<PERSON><PERSON><PERSON><PERSON>", "channels", "httpEndpoint", "headers", "params", "timeout", "heartbeatIntervalMs", "heartbeatTimer", "undefined", "pendingHeartbeatRef", "ref", "logger", "conn", "send<PERSON><PERSON><PERSON>", "serializer", "stateChangeCallbacks", "open", "close", "error", "message", "accessToken", "_resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "websocket", "transport", "Object", "assign", "apikey", "reconnectAfterMs", "tries", "encode", "payload", "callback", "JSON", "stringify", "decode", "bind", "reconnectTimer", "_asyncToGenerator", "disconnect", "connect", "worker", "window", "Worker", "Error", "workerUrl", "endpointURL", "setupConnection", "WSWebSocketDummy", "WS", "_appendParams", "vsn", "code", "reason", "onclose", "clearInterval", "reset", "getChannels", "removeChannel", "channel", "_this2", "status", "unsubscribe", "length", "removeAllChannels", "_this3", "values_1", "Promise", "all", "map", "log", "kind", "msg", "data", "connectionState", "readyState", "connecting", "Connecting", "Open", "closing", "Closing", "Closed", "isConnected", "topic", "config", "chan", "push", "event", "result", "send", "setAuth", "_this4", "token", "tokenToSend", "parsed", "parse", "atob", "split", "_error", "exp", "now", "Math", "floor", "Date", "valid", "reject", "for<PERSON>ach", "updateJoinPayload", "access_token", "joinedOnce", "_isJoined", "_push", "apply", "arguments", "sendHeartbeat", "_this5", "_makeRef", "flushSendBuffer", "newRef", "toString", "_leaveOpenTopic", "dup<PERSON><PERSON><PERSON>", "find", "c", "_isJoining", "_remove", "filter", "_joinRef", "binaryType", "onopen", "_onConnOpen", "onerror", "_onConnError", "onmessage", "_onConnMessage", "_onConnClose", "rawMessage", "_isMember", "_trigger", "_this6", "setInterval", "objectUrl", "_workerObjectUrl", "workerRef", "terminate", "postMessage", "interval", "_trigger<PERSON>hanError", "scheduleTimeout", "url", "keys", "prefix", "match", "query", "URLSearchParams", "result_url", "blob", "Blob", "type", "URL", "createObjectURL", "address", "_protocols"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js"], "sourcesContent": ["import { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL, } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => { };\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined';\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket.\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers The optional headers to pass when connecting.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = [];\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        this.headers = DEFAULT_HEADERS;\n        this.params = {};\n        this.timeout = DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 30000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new Serializer();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n        this.httpEndpoint = httpEndpointURL(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.headers)\n            this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new Timer(async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (this.transport) {\n            this.conn = new this.transport(this.endpointURL(), undefined, {\n                headers: this.headers,\n            });\n            return;\n        }\n        if (NATIVE_WEBSOCKET_AVAILABLE) {\n            this.conn = new WebSocket(this.endpointURL());\n            this.setupConnection();\n            return;\n        }\n        this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n            close: () => {\n                this.conn = null;\n            },\n        });\n        import('ws').then(({ default: WS }) => {\n            this.conn = new WS(this.endpointURL(), undefined, {\n                headers: this.headers,\n            });\n            this.setupConnection();\n        });\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case SOCKET_STATES.connecting:\n                return CONNECTION_STATE.Connecting;\n            case SOCKET_STATES.open:\n                return CONNECTION_STATE.Open;\n            case SOCKET_STATES.closing:\n                return CONNECTION_STATE.Closing;\n            default:\n                return CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n        this.channels.push(chan);\n        return chan;\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (tokenToSend) {\n            let parsed = null;\n            try {\n                parsed = JSON.parse(atob(tokenToSend.split('.')[1]));\n            }\n            catch (_error) { }\n            if (parsed && parsed.exp) {\n                let now = Math.floor(Date.now() / 1000);\n                let valid = now - parsed.exp < 0;\n                if (!valid) {\n                    this.log('auth', `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                    return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                }\n            }\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                tokenToSend && channel.updateJoinPayload({ access_token: tokenToSend });\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.setAuth();\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c._joinRef() !== channel._joinRef());\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            this.channels\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    async _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n        }\n        else {\n            if (this.workerUrl) {\n                this.log('worker', `starting worker for from ${this.workerUrl}`);\n            }\n            else {\n                this.log('worker', `starting default worker`);\n            }\n            const objectUrl = this._workerObjectUrl(this.workerUrl);\n            this.workerRef = new Worker(objectUrl);\n            this.workerRef.onerror = (error) => {\n                this.log('worker', 'worker error', error.message);\n                this.workerRef.terminate();\n            };\n            this.workerRef.onmessage = (event) => {\n                if (event.data.event === 'keepAlive') {\n                    this.sendHeartbeat();\n                }\n            };\n            this.workerRef.postMessage({\n                event: 'start',\n                interval: this.heartbeatIntervalMs,\n            });\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', error.message);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\nclass WSWebSocketDummy {\n    constructor(address, _protocols, options) {\n        this.binaryType = 'arraybuffer';\n        this.onclose = () => { };\n        this.onerror = () => { };\n        this.onmessage = () => { };\n        this.onopen = () => { };\n        this.readyState = SOCKET_STATES.connecting;\n        this.send = () => { };\n        this.url = null;\n        this.url = address;\n        this.close = options.close;\n    }\n}\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,aAAa,EAAEC,UAAU,EAAEC,GAAG,EAAEC,eAAe,QAAS,iBAAiB;AACtJ,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB,MAAMC,0BAA0B,GAAG,OAAOC,SAAS,KAAK,WAAW;AACnE,MAAMC,aAAa,GAAG;AACtB;AACA;AACA;AACA;AACA,MAAM;AACN,eAAe,MAAMC,cAAc,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAC3B,IAAIC,EAAE;IACN,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACN,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACO,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,OAAO,GAAGxB,eAAe;IAC9B,IAAI,CAACyB,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACC,OAAO,GAAGzB,eAAe;IAC9B,IAAI,CAAC0B,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,cAAc,GAAGC,SAAS;IAC/B,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,MAAM,GAAGtB,IAAI;IAClB,IAAI,CAACuB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI7B,UAAU,CAAC,CAAC;IAClC,IAAI,CAAC8B,oBAAoB,GAAG;MACxBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACb,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAIC,WAAW,IAAK;MAClC,IAAIC,MAAM;MACV,IAAID,WAAW,EAAE;QACbC,MAAM,GAAGD,WAAW;MACxB,CAAC,MACI,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;QACnCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KAAK,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAC,CAAC;UAAEC,OAAO,EAAEH;QAAM,CAAC,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;MACrG,CAAC,MACI;QACDF,MAAM,GAAGC,KAAK;MAClB;MACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;IACvC,CAAC;IACD,IAAI,CAAC9B,QAAQ,GAAG,GAAGA,QAAQ,IAAIb,UAAU,CAAC8C,SAAS,EAAE;IACrD,IAAI,CAAC1B,YAAY,GAAGf,eAAe,CAACQ,QAAQ,CAAC;IAC7C,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiC,SAAS,EAAE;MACrE,IAAI,CAACA,SAAS,GAAGjC,OAAO,CAACiC,SAAS;IACtC,CAAC,MACI;MACD,IAAI,CAACA,SAAS,GAAG,IAAI;IACzB;IACA,IAAIjC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,MAAM,EAChE,IAAI,CAACA,MAAM,GAAGR,OAAO,CAACQ,MAAM;IAChC,IAAIR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,OAAO,EACjE,IAAI,CAACA,OAAO,GAAG2B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC5B,OAAO,CAAC,EAAEP,OAAO,CAACO,OAAO,CAAC;IAClF,IAAIP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACS,OAAO,EACjE,IAAI,CAACA,OAAO,GAAGT,OAAO,CAACS,OAAO;IAClC,IAAIT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,MAAM,EAChE,IAAI,CAACA,MAAM,GAAGf,OAAO,CAACe,MAAM;IAChC,IAAIf,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACU,mBAAmB,EAC7E,IAAI,CAACA,mBAAmB,GAAGV,OAAO,CAACU,mBAAmB;IAC1D,MAAMP,gBAAgB,GAAG,CAACD,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkC,MAAM;IAC/I,IAAIjC,gBAAgB,EAAE;MAClB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,MAAM,GAAGD,gBAAgB;IAClC;IACA,IAAI,CAACkC,gBAAgB,GAAG,CAACrC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqC,gBAAgB,IAC7FrC,OAAO,CAACqC,gBAAgB,GACvBC,KAAK,IAAK;MACT,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAACA,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK;IACxD,CAAC;IACL,IAAI,CAACC,MAAM,GAAG,CAACvC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuC,MAAM,IACzEvC,OAAO,CAACuC,MAAM,GACd,CAACC,OAAO,EAAEC,QAAQ,KAAK;MACrB,OAAOA,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC,CAAC;IAC5C,CAAC;IACL,IAAI,CAACI,MAAM,GAAG,CAAC5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4C,MAAM,IACzE5C,OAAO,CAAC4C,MAAM,GACd,IAAI,CAAC1B,UAAU,CAAC0B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,UAAU,CAAC;IAClD,IAAI,CAAC4B,cAAc,GAAG,IAAIxD,KAAK,cAAAyD,iBAAA,CAAC,aAAY;MACxC9C,KAAI,CAAC+C,UAAU,CAAC,CAAC;MACjB/C,KAAI,CAACgD,OAAO,CAAC,CAAC;IAClB,CAAC,GAAE,IAAI,CAACZ,gBAAgB,CAAC;IACzB,IAAI,CAACT,KAAK,GAAG,IAAI,CAACH,aAAa,CAACzB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4B,KAAK,CAAC;IAChG,IAAI5B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkD,MAAM,EAAE;MAClE,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACjD,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAClD;MACA,IAAI,CAACH,MAAM,GAAG,CAAClD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACkD,MAAM,KAAK,KAAK;MACzF,IAAI,CAACI,SAAS,GAAGtD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsD,SAAS;IACxF;IACA,IAAI,CAAC9B,WAAW,GAAG,CAACxB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwB,WAAW,KAAK,IAAI;EACtG;EACA;AACJ;AACA;EACIyB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjC,IAAI,EAAE;MACX;IACJ;IACA,IAAI,IAAI,CAACiB,SAAS,EAAE;MAChB,IAAI,CAACjB,IAAI,GAAG,IAAI,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACsB,WAAW,CAAC,CAAC,EAAE3C,SAAS,EAAE;QAC1DL,OAAO,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC;MACF;IACJ;IACA,IAAIb,0BAA0B,EAAE;MAC5B,IAAI,CAACsB,IAAI,GAAG,IAAIrB,SAAS,CAAC,IAAI,CAAC4D,WAAW,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB;IACJ;IACA,IAAI,CAACxC,IAAI,GAAG,IAAIyC,gBAAgB,CAAC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE3C,SAAS,EAAE;MAC5DS,KAAK,EAAEA,CAAA,KAAM;QACT,IAAI,CAACL,IAAI,GAAG,IAAI;MACpB;IACJ,CAAC,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,CAACc,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAE2B;IAAG,CAAC,KAAK;MACnC,IAAI,CAAC1C,IAAI,GAAG,IAAI0C,EAAE,CAAC,IAAI,CAACH,WAAW,CAAC,CAAC,EAAE3C,SAAS,EAAE;QAC9CL,OAAO,EAAE,IAAI,CAACA;MAClB,CAAC,CAAC;MACF,IAAI,CAACiD,eAAe,CAAC,CAAC;IAC1B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACID,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACI,aAAa,CAAC,IAAI,CAAC5D,QAAQ,EAAEmC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC3B,MAAM,EAAE;MAAEoD,GAAG,EAAEzE;IAAI,CAAC,CAAC,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6D,UAAUA,CAACa,IAAI,EAAEC,MAAM,EAAE;IACrB,IAAI,IAAI,CAAC9C,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC+C,OAAO,GAAG,YAAY,CAAE,CAAC,CAAC,CAAC;MACrC,IAAIF,IAAI,EAAE;QACN,IAAI,CAAC7C,IAAI,CAACK,KAAK,CAACwC,IAAI,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,EAAE,CAAC;MAC7E,CAAC,MACI;QACD,IAAI,CAAC9C,IAAI,CAACK,KAAK,CAAC,CAAC;MACrB;MACA,IAAI,CAACL,IAAI,GAAG,IAAI;MAChB;MACA,IAAI,CAACL,cAAc,IAAIqD,aAAa,CAAC,IAAI,CAACrD,cAAc,CAAC;MACzD,IAAI,CAACmC,cAAc,CAACmB,KAAK,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7D,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACU8D,aAAaA,CAACC,OAAO,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACzB,MAAMuB,MAAM,SAASF,OAAO,CAACG,WAAW,CAAC,CAAC;MAC1C,IAAIF,MAAI,CAAChE,QAAQ,CAACmE,MAAM,KAAK,CAAC,EAAE;QAC5BH,MAAI,CAACrB,UAAU,CAAC,CAAC;MACrB;MACA,OAAOsB,MAAM;IAAC;EAClB;EACA;AACJ;AACA;EACUG,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MACtB,MAAM4B,QAAQ,SAASC,OAAO,CAACC,GAAG,CAACH,MAAI,CAACrE,QAAQ,CAACyE,GAAG,CAAEV,OAAO,IAAKA,OAAO,CAACG,WAAW,CAAC,CAAC,CAAC,CAAC;MACzFG,MAAI,CAAC1B,UAAU,CAAC,CAAC;MACjB,OAAO2B,QAAQ;IAAC;EACpB;EACA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACjB,IAAI,CAACnE,MAAM,CAACiE,IAAI,EAAEC,GAAG,EAAEC,IAAI,CAAC;EAChC;EACA;AACJ;AACA;EACIC,eAAeA,CAAA,EAAG;IACd,QAAQ,IAAI,CAACnE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACoE,UAAU;MACrC,KAAKnG,aAAa,CAACoG,UAAU;QACzB,OAAOvG,gBAAgB,CAACwG,UAAU;MACtC,KAAKrG,aAAa,CAACmC,IAAI;QACnB,OAAOtC,gBAAgB,CAACyG,IAAI;MAChC,KAAKtG,aAAa,CAACuG,OAAO;QACtB,OAAO1G,gBAAgB,CAAC2G,OAAO;MACnC;QACI,OAAO3G,gBAAgB,CAAC4G,MAAM;IACtC;EACJ;EACA;AACJ;AACA;EACIC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,eAAe,CAAC,CAAC,KAAKrG,gBAAgB,CAACyG,IAAI;EAC3D;EACAnB,OAAOA,CAACwB,KAAK,EAAEpF,MAAM,GAAG;IAAEqF,MAAM,EAAE,CAAC;EAAE,CAAC,EAAE;IACpC,MAAMC,IAAI,GAAG,IAAItG,eAAe,CAAC,YAAYoG,KAAK,EAAE,EAAEpF,MAAM,EAAE,IAAI,CAAC;IACnE,IAAI,CAACH,QAAQ,CAAC0F,IAAI,CAACD,IAAI,CAAC;IACxB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,IAAIA,CAACb,IAAI,EAAE;IACP,MAAM;MAAEU,KAAK;MAAEI,KAAK;MAAExD,OAAO;MAAE1B;IAAI,CAAC,GAAGoE,IAAI;IAC3C,MAAMzC,QAAQ,GAAGA,CAAA,KAAM;MACnB,IAAI,CAACF,MAAM,CAAC2C,IAAI,EAAGe,MAAM,IAAK;QAC1B,IAAI/F,EAAE;QACN,CAACA,EAAE,GAAG,IAAI,CAACc,IAAI,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgG,IAAI,CAACD,MAAM,CAAC;MACzE,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAClB,GAAG,CAAC,MAAM,EAAE,GAAGa,KAAK,IAAII,KAAK,KAAKlF,GAAG,GAAG,EAAE0B,OAAO,CAAC;IACvD,IAAI,IAAI,CAACmD,WAAW,CAAC,CAAC,EAAE;MACpBlD,QAAQ,CAAC,CAAC;IACd,CAAC,MACI;MACD,IAAI,CAACxB,UAAU,CAAC8E,IAAI,CAACtD,QAAQ,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU0D,OAAOA,CAAA,EAAe;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA,YAAdsD,KAAK,GAAG,IAAI;MACtB,IAAIC,WAAW,GAAGD,KAAK,IAClBD,MAAI,CAAC5E,WAAW,WAAW4E,MAAI,CAAC5E,WAAW,CAAC,CAAC,CAAE,IAChD4E,MAAI,CAACjG,gBAAgB;MACzB,IAAImG,WAAW,EAAE;QACb,IAAIC,MAAM,GAAG,IAAI;QACjB,IAAI;UACAA,MAAM,GAAG7D,IAAI,CAAC8D,KAAK,CAACC,IAAI,CAACH,WAAW,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CACD,OAAOC,MAAM,EAAE,CAAE;QACjB,IAAIJ,MAAM,IAAIA,MAAM,CAACK,GAAG,EAAE;UACtB,IAAIC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;UACvC,IAAII,KAAK,GAAGJ,GAAG,GAAGN,MAAM,CAACK,GAAG,GAAG,CAAC;UAChC,IAAI,CAACK,KAAK,EAAE;YACRb,MAAI,CAACrB,GAAG,CAAC,MAAM,EAAE,iEAAiEwB,MAAM,CAACK,GAAG,EAAE,CAAC;YAC/F,OAAOhC,OAAO,CAACsC,MAAM,CAAC,iEAAiEX,MAAM,CAACK,GAAG,EAAE,CAAC;UACxG;QACJ;QACAR,MAAI,CAACjG,gBAAgB,GAAGmG,WAAW;QACnCF,MAAI,CAAC/F,QAAQ,CAAC8G,OAAO,CAAE/C,OAAO,IAAK;UAC/BkC,WAAW,IAAIlC,OAAO,CAACgD,iBAAiB,CAAC;YAAEC,YAAY,EAAEf;UAAY,CAAC,CAAC;UACvE,IAAIlC,OAAO,CAACkD,UAAU,IAAIlD,OAAO,CAACmD,SAAS,CAAC,CAAC,EAAE;YAC3CnD,OAAO,CAACoD,KAAK,CAAC3I,cAAc,CAACwI,YAAY,EAAE;cACvCA,YAAY,EAAEf;YAClB,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;IAAC,GAAAmB,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;EACUC,aAAaA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA7E,iBAAA;MAClB,IAAI7C,EAAE;MACN,IAAI,CAAC0H,MAAI,CAACjC,WAAW,CAAC,CAAC,EAAE;QACrB;MACJ;MACA,IAAIiC,MAAI,CAAC/G,mBAAmB,EAAE;QAC1B+G,MAAI,CAAC/G,mBAAmB,GAAG,IAAI;QAC/B+G,MAAI,CAAC7C,GAAG,CAAC,WAAW,EAAE,0DAA0D,CAAC;QACjF,CAAC7E,EAAE,GAAG0H,MAAI,CAAC5G,IAAI,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,KAAK,CAACjC,eAAe,EAAE,kBAAkB,CAAC;QACnG;MACJ;MACAwI,MAAI,CAAC/G,mBAAmB,GAAG+G,MAAI,CAACC,QAAQ,CAAC,CAAC;MAC1CD,MAAI,CAAC7B,IAAI,CAAC;QACNH,KAAK,EAAE,SAAS;QAChBI,KAAK,EAAE,WAAW;QAClBxD,OAAO,EAAE,CAAC,CAAC;QACX1B,GAAG,EAAE8G,MAAI,CAAC/G;MACd,CAAC,CAAC;MACF+G,MAAI,CAACzB,OAAO,CAAC,CAAC;IAAC;EACnB;EACA;AACJ;AACA;EACI2B,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnC,WAAW,CAAC,CAAC,IAAI,IAAI,CAAC1E,UAAU,CAACuD,MAAM,GAAG,CAAC,EAAE;MAClD,IAAI,CAACvD,UAAU,CAACkG,OAAO,CAAE1E,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;MACjD,IAAI,CAACxB,UAAU,GAAG,EAAE;IACxB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4G,QAAQA,CAAA,EAAG;IACP,IAAIE,MAAM,GAAG,IAAI,CAACjH,GAAG,GAAG,CAAC;IACzB,IAAIiH,MAAM,KAAK,IAAI,CAACjH,GAAG,EAAE;MACrB,IAAI,CAACA,GAAG,GAAG,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACA,GAAG,GAAGiH,MAAM;IACrB;IACA,OAAO,IAAI,CAACjH,GAAG,CAACkH,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIC,eAAeA,CAACrC,KAAK,EAAE;IACnB,IAAIsC,UAAU,GAAG,IAAI,CAAC7H,QAAQ,CAAC8H,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACxC,KAAK,KAAKA,KAAK,KAAKwC,CAAC,CAACb,SAAS,CAAC,CAAC,IAAIa,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;IAClG,IAAIH,UAAU,EAAE;MACZ,IAAI,CAACnD,GAAG,CAAC,WAAW,EAAE,4BAA4Ba,KAAK,GAAG,CAAC;MAC3DsC,UAAU,CAAC3D,WAAW,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+D,OAAOA,CAAClE,OAAO,EAAE;IACb,IAAI,CAAC/D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACkI,MAAM,CAAEH,CAAC,IAAKA,CAAC,CAACI,QAAQ,CAAC,CAAC,KAAKpE,OAAO,CAACoE,QAAQ,CAAC,CAAC,CAAC;EACpF;EACA;AACJ;AACA;AACA;AACA;EACIhF,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACxC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACyH,UAAU,GAAG,aAAa;MACpC,IAAI,CAACzH,IAAI,CAAC0H,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC,CAAC;MAC3C,IAAI,CAAC3H,IAAI,CAAC4H,OAAO,GAAItH,KAAK,IAAK,IAAI,CAACuH,YAAY,CAACvH,KAAK,CAAC;MACvD,IAAI,CAACN,IAAI,CAAC8H,SAAS,GAAI9C,KAAK,IAAK,IAAI,CAAC+C,cAAc,CAAC/C,KAAK,CAAC;MAC3D,IAAI,CAAChF,IAAI,CAAC+C,OAAO,GAAIiC,KAAK,IAAK,IAAI,CAACgD,YAAY,CAAChD,KAAK,CAAC;IAC3D;EACJ;EACA;EACA+C,cAAcA,CAACE,UAAU,EAAE;IACvB,IAAI,CAACrG,MAAM,CAACqG,UAAU,CAAC/D,IAAI,EAAGD,GAAG,IAAK;MAClC,IAAI;QAAEW,KAAK;QAAEI,KAAK;QAAExD,OAAO;QAAE1B;MAAI,CAAC,GAAGmE,GAAG;MACxC,IAAInE,GAAG,IAAIA,GAAG,KAAK,IAAI,CAACD,mBAAmB,EAAE;QACzC,IAAI,CAACA,mBAAmB,GAAG,IAAI;MACnC;MACA,IAAI,CAACkE,GAAG,CAAC,SAAS,EAAE,GAAGvC,OAAO,CAAC8B,MAAM,IAAI,EAAE,IAAIsB,KAAK,IAAII,KAAK,IAAKlF,GAAG,IAAI,GAAG,GAAGA,GAAG,GAAG,GAAG,IAAK,EAAE,EAAE,EAAE0B,OAAO,CAAC;MAC3G,IAAI,CAACnC,QAAQ,CACRkI,MAAM,CAAEnE,OAAO,IAAKA,OAAO,CAAC8E,SAAS,CAACtD,KAAK,CAAC,CAAC,CAC7CuB,OAAO,CAAE/C,OAAO,IAAKA,OAAO,CAAC+E,QAAQ,CAACnD,KAAK,EAAExD,OAAO,EAAE1B,GAAG,CAAC,CAAC;MAChE,IAAI,CAACK,oBAAoB,CAACI,OAAO,CAAC4F,OAAO,CAAE1E,QAAQ,IAAKA,QAAQ,CAACwC,GAAG,CAAC,CAAC;IAC1E,CAAC,CAAC;EACN;EACA;EACM0D,WAAWA,CAAA,EAAG;IAAA,IAAAS,MAAA;IAAA,OAAArG,iBAAA;MAChBqG,MAAI,CAACrE,GAAG,CAAC,WAAW,EAAE,gBAAgBqE,MAAI,CAAC7F,WAAW,CAAC,CAAC,EAAE,CAAC;MAC3D6F,MAAI,CAACtB,eAAe,CAAC,CAAC;MACtBsB,MAAI,CAACtG,cAAc,CAACmB,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACmF,MAAI,CAAClG,MAAM,EAAE;QACdkG,MAAI,CAACzI,cAAc,IAAIqD,aAAa,CAACoF,MAAI,CAACzI,cAAc,CAAC;QACzDyI,MAAI,CAACzI,cAAc,GAAG0I,WAAW,CAAC,MAAMD,MAAI,CAACzB,aAAa,CAAC,CAAC,EAAEyB,MAAI,CAAC1I,mBAAmB,CAAC;MAC3F,CAAC,MACI;QACD,IAAI0I,MAAI,CAAC9F,SAAS,EAAE;UAChB8F,MAAI,CAACrE,GAAG,CAAC,QAAQ,EAAE,4BAA4BqE,MAAI,CAAC9F,SAAS,EAAE,CAAC;QACpE,CAAC,MACI;UACD8F,MAAI,CAACrE,GAAG,CAAC,QAAQ,EAAE,yBAAyB,CAAC;QACjD;QACA,MAAMuE,SAAS,GAAGF,MAAI,CAACG,gBAAgB,CAACH,MAAI,CAAC9F,SAAS,CAAC;QACvD8F,MAAI,CAACI,SAAS,GAAG,IAAIpG,MAAM,CAACkG,SAAS,CAAC;QACtCF,MAAI,CAACI,SAAS,CAACZ,OAAO,GAAItH,KAAK,IAAK;UAChC8H,MAAI,CAACrE,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAEzD,KAAK,CAACC,OAAO,CAAC;UACjD6H,MAAI,CAACI,SAAS,CAACC,SAAS,CAAC,CAAC;QAC9B,CAAC;QACDL,MAAI,CAACI,SAAS,CAACV,SAAS,GAAI9C,KAAK,IAAK;UAClC,IAAIA,KAAK,CAACd,IAAI,CAACc,KAAK,KAAK,WAAW,EAAE;YAClCoD,MAAI,CAACzB,aAAa,CAAC,CAAC;UACxB;QACJ,CAAC;QACDyB,MAAI,CAACI,SAAS,CAACE,WAAW,CAAC;UACvB1D,KAAK,EAAE,OAAO;UACd2D,QAAQ,EAAEP,MAAI,CAAC1I;QACnB,CAAC,CAAC;MACN;MACA0I,MAAI,CAACjI,oBAAoB,CAACC,IAAI,CAAC+F,OAAO,CAAE1E,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAAC;EACrE;EACA;EACAuG,YAAYA,CAAChD,KAAK,EAAE;IAChB,IAAI,CAACjB,GAAG,CAAC,WAAW,EAAE,OAAO,EAAEiB,KAAK,CAAC;IACrC,IAAI,CAAC4D,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACjJ,cAAc,IAAIqD,aAAa,CAAC,IAAI,CAACrD,cAAc,CAAC;IACzD,IAAI,CAACmC,cAAc,CAAC+G,eAAe,CAAC,CAAC;IACrC,IAAI,CAAC1I,oBAAoB,CAACE,KAAK,CAAC8F,OAAO,CAAE1E,QAAQ,IAAKA,QAAQ,CAACuD,KAAK,CAAC,CAAC;EAC1E;EACA;EACA6C,YAAYA,CAACvH,KAAK,EAAE;IAChB,IAAI,CAACyD,GAAG,CAAC,WAAW,EAAEzD,KAAK,CAACC,OAAO,CAAC;IACpC,IAAI,CAACqI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACzI,oBAAoB,CAACG,KAAK,CAAC6F,OAAO,CAAE1E,QAAQ,IAAKA,QAAQ,CAACnB,KAAK,CAAC,CAAC;EAC1E;EACA;EACAsI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvJ,QAAQ,CAAC8G,OAAO,CAAE/C,OAAO,IAAKA,OAAO,CAAC+E,QAAQ,CAACtK,cAAc,CAACyC,KAAK,CAAC,CAAC;EAC9E;EACA;EACAqC,aAAaA,CAACmG,GAAG,EAAEtJ,MAAM,EAAE;IACvB,IAAI0B,MAAM,CAAC6H,IAAI,CAACvJ,MAAM,CAAC,CAACgE,MAAM,KAAK,CAAC,EAAE;MAClC,OAAOsF,GAAG;IACd;IACA,MAAME,MAAM,GAAGF,GAAG,CAACG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IAC1C,MAAMC,KAAK,GAAG,IAAIC,eAAe,CAAC3J,MAAM,CAAC;IACzC,OAAO,GAAGsJ,GAAG,GAAGE,MAAM,GAAGE,KAAK,EAAE;EACpC;EACAX,gBAAgBA,CAACO,GAAG,EAAE;IAClB,IAAIM,UAAU;IACd,IAAIN,GAAG,EAAE;MACLM,UAAU,GAAGN,GAAG;IACpB,CAAC,MACI;MACD,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC1K,aAAa,CAAC,EAAE;QAAE2K,IAAI,EAAE;MAAyB,CAAC,CAAC;MAC1EH,UAAU,GAAGI,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IAC1C;IACA,OAAOD,UAAU;EACrB;AACJ;AACA,MAAM3G,gBAAgB,CAAC;EACnB3D,WAAWA,CAAC4K,OAAO,EAAEC,UAAU,EAAE3K,OAAO,EAAE;IACtC,IAAI,CAACyI,UAAU,GAAG,aAAa;IAC/B,IAAI,CAAC1E,OAAO,GAAG,MAAM,CAAE,CAAC;IACxB,IAAI,CAAC6E,OAAO,GAAG,MAAM,CAAE,CAAC;IACxB,IAAI,CAACE,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B,IAAI,CAACJ,MAAM,GAAG,MAAM,CAAE,CAAC;IACvB,IAAI,CAACtD,UAAU,GAAGnG,aAAa,CAACoG,UAAU;IAC1C,IAAI,CAACa,IAAI,GAAG,MAAM,CAAE,CAAC;IACrB,IAAI,CAAC4D,GAAG,GAAG,IAAI;IACf,IAAI,CAACA,GAAG,GAAGY,OAAO;IAClB,IAAI,CAACrJ,KAAK,GAAGrB,OAAO,CAACqB,KAAK;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}