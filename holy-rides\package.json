{"name": "holy-rides", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.9", "@angular/cdk": "^19.2.14", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/forms": "^19.2.9", "@angular/google-maps": "^19.2.14", "@angular/material": "^19.2.14", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/pwa": "^19.2.10", "@angular/router": "^19.2.9", "@angular/service-worker": "^19.2.9", "@rollup/rollup-win32-x64-msvc": "^4.40.1", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "chart.js": "^4.4.9", "ng2-charts": "^8.0.0", "ngx-mat-timepicker": "^19.0.0", "rxjs": "~7.8.0", "square": "^42.1.0", "tslib": "^2.3.0", "twilio": "^5.6.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.10", "@angular/cli": "^19.2.10", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.7.2"}, "overrides": {"@supabase/supabase-js": {"@supabase/gotrue-js": "2.61.0", "@supabase/auth-js": "2.61.0"}}}