# Driver Payment Feature

This component allows administrators to process test payments to drivers for completed rides and manage driver payouts.

## Features

- View completed rides that need payment processing
- Process payments for completed rides
- View driver payout history
- Update payout status (mark as paid)

## How It Works

1. **Ride Completion**: When a ride is completed, it becomes eligible for payment processing.

2. **Payment Processing**: An admin can select a completed ride and process the payment, which:
   - Ensures the ride has a fare (estimates one if not set)
   - Creates a payment intent
   - Processes the payment
   - Updates the ride's payment status to "paid"
   - Creates a driver payout record with "pending" status

3. **Payout Management**: Admins can view all driver payouts and mark them as "paid" when funds have been sent to the driver.

## Usage

1. Log in as an admin user
2. Navigate to the Admin Dashboard
3. Click on the "Driver Payment" tab
4. Select a completed ride from the dropdown
5. Review the ride details
6. Click "Process Payment" to process the payment
7. View the payout in the "Recent Driver Payouts" section
8. Click the payment icon to mark a payout as "paid"

## Implementation Details

The driver payment feature uses the following components and services:

- **DriverPaymentComponent**: UI for processing payments and managing payouts
- **PaymentService**: Service for creating payment intents, processing payments, and managing payouts
- **RideService**: Service for retrieving and updating rides
- **UserService**: Service for retrieving user information

When a payment is processed, the following happens:

1. A payment intent is created with a unique ID
2. The ride's payment status is updated to "paid"
3. A driver payout record is created with "pending" status
4. The driver can see the pending payout in their earnings dashboard

When an admin marks a payout as "paid", the payout status is updated in the database, and the driver can see the updated status in their earnings dashboard.

## Database Schema

The feature relies on the following database tables:

- **rides**: Stores ride information, including payment status
- **driver_payouts**: Stores payout information for drivers

## Future Enhancements

- Integration with real payment processors (e.g., Square, Stripe)
- Automated payout processing
- Batch payment processing for multiple rides
- Detailed payment reports and analytics
