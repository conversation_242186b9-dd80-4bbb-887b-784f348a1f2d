{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nexport default class StorageBucketApi {\n  constructor(url, headers = {}, fetch) {\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  listBuckets() {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  getBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket/${id}`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   */\n  createBucket(id, options = {\n    public: false\n  }) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  updateBucket(id, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  emptyBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  deleteBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "DEFAULT_HEADERS", "isStorageError", "get", "post", "put", "remove", "resolveFetch", "StorageBucketApi", "constructor", "url", "headers", "fetch", "Object", "assign", "listBuckets", "data", "error", "getBucket", "id", "createBucket", "options", "public", "name", "file_size_limit", "fileSizeLimit", "allowed_mime_types", "allowedMimeTypes", "updateBucket", "emptyBucket", "deleteBucket"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nexport default class StorageBucketApi {\n    constructor(url, headers = {}, fetch) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n        this.fetch = resolveFetch(fetch);\n    }\n    /**\n     * Retrieves the details of all Storage buckets within an existing project.\n     */\n    listBuckets() {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield get(this.fetch, `${this.url}/bucket`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing Storage bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to retrieve.\n     */\n    getBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield get(this.fetch, `${this.url}/bucket/${id}`, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a new Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are creating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     * @returns newly created bucket id\n     */\n    createBucket(id, options = {\n        public: false,\n    }) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/bucket`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Updates a Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are updating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     */\n    updateBucket(id, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Removes all objects inside a single bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to empty.\n     */\n    emptyBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n     * You must first `empty()` the bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to delete.\n     */\n    deleteBucket(id) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,eAAe,QAAQ,kBAAkB;AAClD,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AACrD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,eAAe,MAAMC,gBAAgB,CAAC;EAClCC,WAAWA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,KAAK,EAAE;IAClC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,eAAe,CAAC,EAAEU,OAAO,CAAC;IACzE,IAAI,CAACC,KAAK,GAAGL,YAAY,CAACK,KAAK,CAAC;EACpC;EACA;AACJ;AACA;EACIG,WAAWA,CAAA,EAAG;IACV,OAAOjC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMb,GAAG,CAAC,IAAI,CAACS,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,SAAS,EAAE;UAAEC,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QACnF,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACC,EAAE,EAAE;IACV,OAAOrC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMb,GAAG,CAAC,IAAI,CAACS,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,WAAWS,EAAE,EAAE,EAAE;UAAER,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QACzF,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,YAAYA,CAACD,EAAE,EAAEE,OAAO,GAAG;IACvBC,MAAM,EAAE;EACZ,CAAC,EAAE;IACC,OAAOxC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMZ,IAAI,CAAC,IAAI,CAACQ,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,SAAS,EAAE;UACtDS,EAAE;UACFI,IAAI,EAAEJ,EAAE;UACRG,MAAM,EAAED,OAAO,CAACC,MAAM;UACtBE,eAAe,EAAEH,OAAO,CAACI,aAAa;UACtCC,kBAAkB,EAAEL,OAAO,CAACM;QAChC,CAAC,EAAE;UAAEhB,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC7B,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIW,YAAYA,CAACT,EAAE,EAAEE,OAAO,EAAE;IACtB,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMX,GAAG,CAAC,IAAI,CAACO,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,WAAWS,EAAE,EAAE,EAAE;UAC3DA,EAAE;UACFI,IAAI,EAAEJ,EAAE;UACRG,MAAM,EAAED,OAAO,CAACC,MAAM;UACtBE,eAAe,EAAEH,OAAO,CAACI,aAAa;UACtCC,kBAAkB,EAAEL,OAAO,CAACM;QAChC,CAAC,EAAE;UAAEhB,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC7B,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIY,WAAWA,CAACV,EAAE,EAAE;IACZ,OAAOrC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMZ,IAAI,CAAC,IAAI,CAACQ,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,WAAWS,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;UAAER,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QACpG,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,YAAYA,CAACX,EAAE,EAAE;IACb,OAAOrC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkC,IAAI,GAAG,MAAMV,MAAM,CAAC,IAAI,CAACM,KAAK,EAAE,GAAG,IAAI,CAACF,GAAG,WAAWS,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE;UAAER,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAChG,OAAO;UAAEK,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIf,cAAc,CAACe,KAAK,CAAC,EAAE;UACvB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}