import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule
  ],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  profileForm: FormGroup;
  loading = false;
  userId: string | null = null;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.profileForm = this.formBuilder.group({
      full_name: ['', Validators.required],
      phone: ['', Validators.required],
      email: [{ value: '', disabled: true }]
    });
  }

  async ngOnInit() {
    const user = await this.authService.getCurrentUser();
    if (user) {
      this.userId = user.id;
      this.profileForm.patchValue({
        email: user.email,
        full_name: user.full_name || '',
        phone: user.phone || ''
      });
    }
  }

  async onSubmit() {
    if (this.profileForm.invalid || !this.userId) {
      return;
    }

    this.loading = true;

    try {
      const success = await this.authService.updateProfile(this.profileForm.getRawValue());

      if (!success) {
        throw new Error('Failed to update profile');
      }

      this.snackBar.open('Profile updated successfully', 'Close', {
        duration: 3000
      });

      // Get user role and navigate to appropriate dashboard
      const role = await this.authService.getUserRole();
      if (role) {
        await this.router.navigate([this.authService.getDashboardRouteForRole(role)]);
      } else {
        throw new Error('User role not found');
      }

    } catch (error: any) {
      this.snackBar.open(error.message || 'An error occurred', 'Close', {
        duration: 3000
      });
    } finally {
      this.loading = false;
    }
  }
}
