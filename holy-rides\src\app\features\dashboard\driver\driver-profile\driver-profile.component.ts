import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { AuthService } from '../../../../core/services/auth.service';
import { VehicleService } from '../../../../core/services/vehicle.service';
import { Vehicle } from '../../../../core/models/vehicle.model';
import { User } from '../../../../core/models/user.model';

@Component({
  selector: 'app-driver-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSnackBarModule,
    MatDividerModule,
    MatIconModule
  ],
  template: `
    <div class="profile-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Driver Profile</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Full Name</mat-label>
              <input matInput formControlName="full_name" placeholder="Enter your full name">
              <mat-error *ngIf="profileForm.get('full_name')?.hasError('required')">
                Full name is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Phone Number</mat-label>
              <input matInput formControlName="phone" placeholder="Enter your phone number">
              <mat-error *ngIf="profileForm.get('phone')?.hasError('required')">
                Phone number is required
              </mat-error>
            </mat-form-field>

            <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || profileForm.pristine">
              Update Profile
            </button>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- <mat-card class="vehicle-card">
        <mat-card-header>
          <mat-card-title>Vehicle Information</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div *ngIf="vehicles.length === 0" class="no-vehicles">
            <p>No vehicles added yet.</p>
          </div>

          <div *ngFor="let vehicle of vehicles" class="vehicle-item">
            <div class="vehicle-details">
              <h3>{{ vehicle.year }} {{ vehicle.make }} {{ vehicle.model }}</h3>
              <p>Color: {{ vehicle.color }}</p>
              <p>License Plate: {{ vehicle.license_plate }}</p>
              <p>Passenger Capacity: {{ vehicle.capacity }}</p>
            </div>
            <div class="vehicle-actions">
              <button mat-icon-button color="primary" (click)="editVehicle(vehicle)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteVehicle(vehicle.id)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>

          <mat-divider *ngIf="vehicles.length > 0" class="divider"></mat-divider>

          <form [formGroup]="vehicleForm" (ngSubmit)="saveVehicle()" class="vehicle-form">
            <h3>{{ editingVehicle ? 'Edit Vehicle' : 'Add New Vehicle' }}</h3>
            
            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Make</mat-label>
                <input matInput formControlName="make" placeholder="e.g. Toyota">
                <mat-error *ngIf="vehicleForm.get('make')?.hasError('required')">
                  Make is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Model</mat-label>
                <input matInput formControlName="model" placeholder="e.g. Camry">
                <mat-error *ngIf="vehicleForm.get('model')?.hasError('required')">
                  Model is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Year</mat-label>
                <input matInput formControlName="year" type="number" placeholder="e.g. 2020">
                <mat-error *ngIf="vehicleForm.get('year')?.hasError('required')">
                  Year is required
                </mat-error>
                <mat-error *ngIf="vehicleForm.get('year')?.hasError('min') || vehicleForm.get('year')?.hasError('max')">
                  Year must be between 1990 and {{ currentYear }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Color</mat-label>
                <input matInput formControlName="color" placeholder="e.g. Silver">
                <mat-error *ngIf="vehicleForm.get('color')?.hasError('required')">
                  Color is required
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>License Plate</mat-label>
                <input matInput formControlName="license_plate" placeholder="e.g. ABC123">
                <mat-error *ngIf="vehicleForm.get('license_plate')?.hasError('required')">
                  License plate is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Passenger Capacity</mat-label>
                <input matInput formControlName="capacity" type="number" placeholder="e.g. 4">
                <mat-error *ngIf="vehicleForm.get('capacity')?.hasError('required')">
                  Capacity is required
                </mat-error>
                <mat-error *ngIf="vehicleForm.get('capacity')?.hasError('min') || vehicleForm.get('capacity')?.hasError('max')">
                  Capacity must be between 1 and 10
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="vehicleForm.invalid">
                {{ editingVehicle ? 'Update Vehicle' : 'Add Vehicle' }}
              </button>
              <button mat-button type="button" *ngIf="editingVehicle" (click)="cancelEdit()">
                Cancel
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card> -->
    </div>
  `,
  styles: [`
    .profile-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .vehicle-card {
      margin-top: 20px;
    }

    .vehicle-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #eee;
    }

    .vehicle-item:last-child {
      border-bottom: none;
    }

    .vehicle-details h3 {
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .vehicle-details p {
      margin: 4px 0;
      color: #666;
    }

    .vehicle-actions {
      display: flex;
      gap: 8px;
    }

    .divider {
      margin: 16px 0;
    }

    .vehicle-form {
      margin-top: 16px;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .form-actions {
      display: flex;
      gap: 8px;
      margin-top: 16px;
    }

    .no-vehicles {
      padding: 16px 0;
      color: #666;
      font-style: italic;
    }
  `]
})
export class DriverProfileComponent implements OnInit {
  profileForm: FormGroup;
  vehicleForm: FormGroup;
  vehicles: Vehicle[] = [];
  currentUser: User | null = null;
  editingVehicle: Vehicle | null = null;
  currentYear = new Date().getFullYear();

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private vehicleService: VehicleService,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.fb.group({
      full_name: ['', Validators.required],
      phone: ['', Validators.required]
    });

    this.vehicleForm = this.fb.group({
      make: ['', Validators.required],
      model: ['', Validators.required],
      year: ['', [Validators.required, Validators.min(1990), Validators.max(this.currentYear)]],
      color: ['', Validators.required],
      license_plate: ['', Validators.required],
      capacity: [4, [Validators.required, Validators.min(1), Validators.max(10)]]
    });
  }

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadVehicles();
  }

  async loadUserProfile(): Promise<void> {
    try {
      this.currentUser = await this.authService.getCurrentUser();
      if (this.currentUser) {
        this.profileForm.patchValue({
          full_name: this.currentUser.full_name || '',
          phone: this.currentUser.phone || ''
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      this.snackBar.open('Failed to load profile', 'Close', { duration: 3000 });
    }
  }

  async loadVehicles(): Promise<void> {
    try {
      if (this.currentUser) {
        this.vehicles = await this.vehicleService.getDriverVehicles(this.currentUser.id);
      }
    } catch (error) {
      console.error('Error loading vehicles:', error);
      this.snackBar.open('Failed to load vehicles', 'Close', { duration: 3000 });
    }
  }

  async updateProfile(): Promise<void> {
    if (this.profileForm.invalid) return;

    try {
      const success = await this.authService.updateProfile(this.profileForm.value);
      if (success) {
        this.snackBar.open('Profile updated successfully', 'Close', { duration: 3000 });
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      this.snackBar.open('Failed to update profile', 'Close', { duration: 3000 });
    }
  }

  editVehicle(vehicle: Vehicle): void {
    this.editingVehicle = vehicle;
    this.vehicleForm.patchValue({
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      color: vehicle.color,
      license_plate: vehicle.license_plate,
      capacity: vehicle.capacity
    });
  }

  cancelEdit(): void {
    this.editingVehicle = null;
    this.vehicleForm.reset({
      capacity: 4
    });
  }

  async saveVehicle(): Promise<void> {
    if (this.vehicleForm.invalid || !this.currentUser) return;

    try {
      if (this.editingVehicle) {
        // Update existing vehicle
        const success = await this.vehicleService.updateVehicle(
          this.editingVehicle.id,
          this.vehicleForm.value
        );
        
        if (success) {
          this.snackBar.open('Vehicle updated successfully', 'Close', { duration: 3000 });
          this.cancelEdit();
          this.loadVehicles();
        } else {
          throw new Error('Failed to update vehicle');
        }
      } else {
        // Add new vehicle
        const newVehicle = {
          ...this.vehicleForm.value,
          driver_id: this.currentUser.id
        };
        
        const result = await this.vehicleService.addVehicle(newVehicle);
        if (result) {
          this.snackBar.open('Vehicle added successfully', 'Close', { duration: 3000 });
          this.vehicleForm.reset({
            capacity: 4
          });
          this.loadVehicles();
        } else {
          throw new Error('Failed to add vehicle');
        }
      }
    } catch (error) {
      console.error('Error saving vehicle:', error);
      this.snackBar.open('Failed to save vehicle', 'Close', { duration: 3000 });
    }
  }

  async deleteVehicle(vehicleId: string): Promise<void> {
    try {
      const success = await this.vehicleService.deleteVehicle(vehicleId);
      if (success) {
        this.snackBar.open('Vehicle deleted successfully', 'Close', { duration: 3000 });
        this.loadVehicles();
      } else {
        throw new Error('Failed to delete vehicle');
      }
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      this.snackBar.open('Failed to delete vehicle', 'Close', { duration: 3000 });
    }
  }
}
