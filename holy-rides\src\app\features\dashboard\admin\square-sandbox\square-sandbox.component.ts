import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { environment } from '../../../../../environments/environment';
import { PaymentService } from '../../../../core/services/payment.service';

declare global {
  interface Window {
    Square: any;
  }
}

@Component({
  selector: 'app-square-sandbox',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  template: `
    <div class="container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Square Sandbox Testing</mat-card-title>
          <mat-card-subtitle>Test Square payment processing in sandbox mode</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div *ngIf="!sdkLoaded" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading Square SDK...</p>
          </div>

          <div *ngIf="sdkLoaded">
            <form [formGroup]="paymentForm" (ngSubmit)="processPayment()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Amount (USD)</mat-label>
                <input matInput type="number" formControlName="amount" min="1" step="0.01">
                <mat-error *ngIf="paymentForm.get('amount')?.hasError('required')">
                  Amount is required
                </mat-error>
                <mat-error *ngIf="paymentForm.get('amount')?.hasError('min')">
                  Amount must be at least $1.00
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Test Scenario</mat-label>
                <mat-select formControlName="testScenario">
                  <mat-option value="APPROVED">Approved Payment</mat-option>
                  <mat-option value="DECLINED">Declined Payment</mat-option>
                  <mat-option value="CARD_DECLINED">Card Declined</mat-option>
                  <mat-option value="VERIFICATION_REQUIRED">Verification Required</mat-option>
                </mat-select>
              </mat-form-field>

              <div class="card-container">
                <div id="card-container"></div>
                <div id="payment-status-container"></div>
              </div>

              <div class="button-container">
                <button mat-raised-button color="primary" type="submit" [disabled]="paymentForm.invalid || processing">
                  <span *ngIf="!processing">Process Payment</span>
                  <mat-spinner *ngIf="processing" diameter="24"></mat-spinner>
                </button>
              </div>
            </form>

            <mat-divider class="divider"></mat-divider>

            <div class="payment-results" *ngIf="paymentResult">
              <h3>Payment Result</h3>
              <pre>{{ paymentResult | json }}</pre>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .container {
      max-width: 800px;
      margin: 20px auto;
    }
    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }
    .card-container {
      margin: 20px 0;
      min-height: 140px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 12px;
    }
    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
    }
    .divider {
      margin: 30px 0;
    }
    .payment-results {
      margin-top: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow: auto;
    }
  `]
})
export class SquareSandboxComponent implements OnInit, AfterViewInit {
  paymentForm: FormGroup;
  sdkLoaded = false;
  processing = false;
  paymentResult: any = null;
  card: any = null;
  payments: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private paymentService: PaymentService,
    private snackBar: MatSnackBar
  ) {
    this.paymentForm = this.formBuilder.group({
      amount: [10.00, [Validators.required, Validators.min(1)]],
      testScenario: ['APPROVED', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadSquareSDK();
  }

  ngAfterViewInit(): void {
    // The card element will be initialized after the SDK is loaded
  }

  loadSquareSDK(): void {
    // Check if Square SDK is already loaded
    if (window.Square) {
      this.initializeSquare();
      return;
    }

    // Create script element to load Square SDK
    const script = document.createElement('script');
    script.src = 'https://connect.squareupsandbox.com';
    script.onload = () => {
      this.initializeSquare();
    };
    script.onerror = () => {
      this.sdkLoaded = false;
      this.snackBar.open('Failed to load Square SDK. Please try again later.', 'Close', { duration: 5000 });
    };
    document.body.appendChild(script);
  }

  initializeSquare(): void {
    if (!window.Square) {
      this.snackBar.open('Square SDK not available', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Initialize Square payments
      this.payments = window.Square.payments(environment.squareApplicationId, environment.squareLocationId);
      
      // Initialize card
      this.initializeCard();
    } catch (error) {
      console.error('Error initializing Square payments:', error);
      this.snackBar.open('Error initializing Square payments. Check your credentials.', 'Close', { duration: 5000 });
    }
  }

  async initializeCard(): Promise<void> {
    try {
      this.card = await this.payments.card();
      await this.card.attach('#card-container');
      this.sdkLoaded = true;
    } catch (error) {
      console.error('Error initializing Square card:', error);
      this.snackBar.open('Error initializing Square card form', 'Close', { duration: 5000 });
    }
  }

  async processPayment(): Promise<void> {
    if (this.paymentForm.invalid || !this.card) {
      return;
    }

    this.processing = true;
    this.paymentResult = null;
    const amount = this.paymentForm.get('amount')?.value;
    const testScenario = this.paymentForm.get('testScenario')?.value;

    try {
      // Get a payment token from Square
      const result = await this.card.tokenize();
      
      if (result.status === 'OK') {
        // For sandbox testing, we'll use our mock implementation
        // In a real app, you would send this token to your server
        const paymentId = `sandbox_${Math.random().toString(36).substring(2, 15)}`;
        
        // Create a mock payment result
        this.paymentResult = {
          status: testScenario,
          token: result.token,
          paymentId: paymentId,
          amount: amount,
          currency: 'USD',
          timestamp: new Date().toISOString()
        };

        if (testScenario === 'APPROVED') {
          this.snackBar.open('Payment approved successfully!', 'Close', { duration: 3000 });
        } else {
          this.snackBar.open(`Payment ${testScenario.toLowerCase().replace('_', ' ')}`, 'Close', { duration: 3000 });
        }
      } else {
        this.paymentResult = {
          status: 'ERROR',
          errors: result.errors
        };
        this.snackBar.open('Error processing payment', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      this.paymentResult = {
        status: 'ERROR',
        error: error
      };
      this.snackBar.open('Error processing payment', 'Close', { duration: 3000 });
    } finally {
      this.processing = false;
    }
  }
}
