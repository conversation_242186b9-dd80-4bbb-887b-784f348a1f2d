import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject, Observable } from 'rxjs';
import { Vehicle } from '../models/vehicle.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class VehicleService {
  private supabase: SupabaseClient;
  private vehiclesSubject = new BehaviorSubject<Vehicle[]>([]);
  vehicles$ = this.vehiclesSubject.asObservable();

  constructor(private authService: AuthService) {
    this.supabase = authService.supabase;

    // For demo purposes, we'll create some mock vehicles
    // In a real app, you would fetch these from the database
    this.initializeMockVehicles();
  }

  private initializeMockVehicles() {
    const mockVehicles: Vehicle[] = [
      {
        id: '1',
        driver_id: 'driver1',
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        color: 'Silver',
        license_plate: 'ABC123',
        capacity: 4,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '2',
        driver_id: 'driver2',
        make: 'Honda',
        model: 'Accord',
        year: 2019,
        color: 'Black',
        license_plate: 'XYZ789',
        capacity: 5,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    this.vehiclesSubject.next(mockVehicles);
  }

  async getDriverVehicles(driverId: string): Promise<Vehicle[]> {
    try {
      // In a real app, you would fetch from Supabase
      // For now, we'll filter the mock vehicles
      return this.vehiclesSubject.value.filter(vehicle => vehicle.driver_id === driverId);
    } catch (error) {
      console.error('Error fetching driver vehicles:', error);
      return [];
    }
  }

  async addVehicle(vehicle: Omit<Vehicle, 'id' | 'created_at' | 'updated_at'>): Promise<Vehicle | null> {
    try {
      // In a real app, you would insert into Supabase
      // For now, we'll just add to the mock vehicles
      const newVehicle: Vehicle = {
        ...vehicle,
        id: Math.random().toString(36).substring(2, 9),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const currentVehicles = this.vehiclesSubject.value;
      this.vehiclesSubject.next([...currentVehicles, newVehicle]);

      return newVehicle;
    } catch (error) {
      console.error('Error adding vehicle:', error);
      return null;
    }
  }

  async updateVehicle(id: string, updates: Partial<Vehicle>): Promise<boolean> {
    try {
      // In a real app, you would update in Supabase
      // For now, we'll update the mock vehicles
      const currentVehicles = this.vehiclesSubject.value;
      const updatedVehicles = currentVehicles.map(vehicle =>
        vehicle.id === id ? {
          ...vehicle,
          ...updates,
          updated_at: new Date().toISOString()
        } : vehicle
      );

      this.vehiclesSubject.next(updatedVehicles);
      return true;
    } catch (error) {
      console.error('Error updating vehicle:', error);
      return false;
    }
  }

  async deleteVehicle(id: string): Promise<boolean> {
    try {
      // In a real app, you would delete from Supabase
      // For now, we'll remove from the mock vehicles
      const currentVehicles = this.vehiclesSubject.value;
      const updatedVehicles = currentVehicles.filter(vehicle => vehicle.id !== id);

      this.vehiclesSubject.next(updatedVehicles);
      return true;
    } catch (error) {
      console.error('Error deleting vehicle:', error);
      return false;
    }
  }
}
