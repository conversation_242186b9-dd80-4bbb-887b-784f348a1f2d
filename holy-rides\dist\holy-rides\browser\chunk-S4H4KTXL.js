import{b as u}from"./chunk-OFTCLERB.js";import"./chunk-ORDMVBIZ.js";import"./chunk-WSXVBUWR.js";import{Fc as s,Jd as d,Kb as r,La as n,Ld as c,Md as l,Nd as M,Qd as f,Rd as C,Wa as i,hb as m,qb as e,rb as a,sb as p}from"./chunk-ST4QC4E3.js";import"./chunk-ODN5LVDJ.js";var g=class o{static \u0275fac=function(t){return new(t||o)};static \u0275cmp=i({type:o,selectors:[["app-map-test"]],decls:6,vars:2,consts:[[3,"origin","destination"]],template:function(t,T){t&1&&(e(0,"mat-card")(1,"mat-card-header")(2,"mat-card-title"),r(3,"Google Maps Test"),a()(),e(4,"mat-card-content"),p(5,"app-map-display",0),a()()),t&2&&(n(5),m("origin","New York, NY")("destination","Boston, MA"))},dependencies:[s,C,c,M,f,l,d,u],styles:["mat-card[_ngcontent-%COMP%]{margin:16px}"]})};export{g as MapTestComponent};
