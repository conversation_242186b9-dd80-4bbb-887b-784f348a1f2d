import { Component, Input, OnInit, OnChanges, SimpleChanges, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { environment } from '../../../../environments/environment';
import { Coordinates, LocationService } from '../../../core/services/location.service';
import { GoogleMapsModule, MapDirectionsService } from '@angular/google-maps';
import { map, Observable, of } from 'rxjs';
import { GoogleMapsLoaderService } from '../../../core/services/google-maps-loader.service';

@Component({
  selector: 'app-map-display',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    GoogleMapsModule
  ],
  template: `
    <mat-card class="map-card">
      <mat-card-content>
        <div *ngIf="!apiLoaded" class="map-placeholder">
          <p>Loading map...</p>
        </div>
        <div class="map-container" [style.display]="apiLoaded ? 'block' : 'none'">
          <google-map
            *ngIf="apiLoaded"
            height="300px"
            width="100%"
            [center]="center"
            [zoom]="zoom"
            [options]="options">
            <map-marker
              *ngIf="originMarker"
              [position]="originMarker"
              [title]="'Origin'"
              [options]="markerOptions">
            </map-marker>
            <map-marker
              *ngIf="destinationMarker"
              [position]="destinationMarker"
              [title]="'Destination'"
              [options]="markerOptions">
            </map-marker>
            <map-directions-renderer
              *ngIf="directionsResults$ | async as directionsResults"
              [directions]="directionsResults">
            </map-directions-renderer>
          </google-map>
        </div>

        <div class="map-actions" *ngIf="showDirectionsLink && origin && destination">
          <a [href]="directionsUrl" target="_blank">
            <button mat-raised-button color="primary">
              <mat-icon>directions</mat-icon>
              Get Directions
            </button>
          </a>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .map-card {
      margin-bottom: 16px;
    }

    .map-container {
      height: 300px;
      width: 100%;
    }

    .map-placeholder {
      height: 300px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f5f5f5;
      color: #666;
    }

    .map-actions {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  `]
})
export class MapDisplayComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() origin?: string | Coordinates;
  @Input() destination?: string | Coordinates;
  @Input() showDirectionsLink: boolean = true;

  // Google Maps properties
  apiLoaded: boolean = false;
  directionsUrl: string = '';
  center: google.maps.LatLngLiteral = { lat: 40.7128, lng: -74.0060 }; // Default to NYC
  zoom = 12;
  options: google.maps.MapOptions = {
    mapTypeId: 'roadmap',
    zoomControl: true,
    scrollwheel: true,
    disableDoubleClickZoom: false,
    maxZoom: 20,
    minZoom: 4,
  };
  originMarker?: google.maps.LatLngLiteral;
  destinationMarker?: google.maps.LatLngLiteral;
  markerAnimation: typeof google.maps.Animation | null = null;
  markerOptions: google.maps.MarkerOptions = {};
  directionsResults$: Observable<google.maps.DirectionsResult | undefined> | undefined;

  constructor(
    private locationService: LocationService,
    private mapDirectionsService: MapDirectionsService,
    private googleMapsLoader: GoogleMapsLoaderService
  ) {}

  ngOnInit(): void {
    this.loadGoogleMapsApi();
    this.updateDirectionsUrl();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['origin'] || changes['destination']) && this.apiLoaded) {
      this.updateMap();
      this.updateDirectionsUrl();
    }
  }

  ngAfterViewInit(): void {
    if (this.apiLoaded) {
      this.initMap();
    }
  }

  private async loadGoogleMapsApi(): Promise<void> {
    try {
      await this.googleMapsLoader.loadGoogleMapsApi();
      this.apiLoaded = true;
      
      if (typeof google !== 'undefined' && google.maps) {
        this.markerAnimation = google.maps.Animation;
        this.markerOptions = {
          animation: google.maps.Animation.DROP
        };
      }
      
      this.initMap();
    } catch (error) {
      console.error('Failed to load Google Maps API:', error);
      this.apiLoaded = false;
    }
  }

  private initMap(): void {
    if (this.origin && this.destination) {
      this.updateMap();
    }
  }

  private async updateMap(): Promise<void> {
    if (!this.origin || !this.destination) return;

    try {
      // Convert origin and destination to coordinates if they are strings
      const originCoords = typeof this.origin === 'string'
        ? await this.locationService.geocodeAddress(this.origin)
        : this.origin;

      const destCoords = typeof this.destination === 'string'
        ? await this.locationService.geocodeAddress(this.destination)
        : this.destination;

      // Set markers
      this.originMarker = {
        lat: originCoords.latitude,
        lng: originCoords.longitude
      };

      this.destinationMarker = {
        lat: destCoords.latitude,
        lng: destCoords.longitude
      };

      // Center the map between the two points
      this.center = {
        lat: (originCoords.latitude + destCoords.latitude) / 2,
        lng: (originCoords.longitude + destCoords.longitude) / 2
      };

      // Calculate zoom level based on distance
      const distance = this.calculateDistance(
        originCoords.latitude, originCoords.longitude,
        destCoords.latitude, destCoords.longitude
      );

      this.zoom = this.calculateZoomLevel(distance);

      // Get directions
      this.getDirections(this.originMarker, this.destinationMarker);

    } catch (error) {
      console.error('Error updating map:', error);
    }
  }

  private getDirections(origin: google.maps.LatLngLiteral, destination: google.maps.LatLngLiteral): void {
    const request: google.maps.DirectionsRequest = {
      origin: origin,
      destination: destination,
      travelMode: google.maps.TravelMode.DRIVING
    };

    this.directionsResults$ = this.mapDirectionsService.route(request).pipe(
      map(response => {
        return response.result;
      })
    );
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    // Haversine formula to calculate distance between two points
    const R = 6371; // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c; // Distance in km
    return distance;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  private calculateZoomLevel(distance: number): number {
    // Adjust zoom level based on distance
    if (distance > 1000) return 4;
    if (distance > 500) return 5;
    if (distance > 200) return 6;
    if (distance > 100) return 7;
    if (distance > 50) return 8;
    if (distance > 20) return 9;
    if (distance > 10) return 10;
    if (distance > 5) return 11;
    if (distance > 2) return 12;
    if (distance > 1) return 13;
    if (distance > 0.5) return 14;
    return 15;
  }

  private updateDirectionsUrl(): void {
    if (!this.origin || !this.destination) return;

    let originStr = typeof this.origin === 'string' ? this.origin : `${this.origin.latitude},${this.origin.longitude}`;
    let destinationStr = typeof this.destination === 'string' ? this.destination : `${this.destination.latitude},${this.destination.longitude}`;

    this.directionsUrl = this.locationService.getGoogleMapsDirectionsUrl(originStr, destinationStr);
  }
}
