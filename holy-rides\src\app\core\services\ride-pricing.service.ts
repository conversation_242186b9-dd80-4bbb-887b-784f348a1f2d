import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from './auth.service';
import { RidePricing } from '../models/ride-pricing.model';

@Injectable({
  providedIn: 'root'
})
export class RidePricingService {
  private supabase: SupabaseClient;
  private pricingSubject = new BehaviorSubject<RidePricing | null>(null);
  pricing$ = this.pricingSubject.asObservable();

  constructor(private authService: AuthService) {
    this.supabase = authService.supabase;
    this.loadActivePricing();
  }

  /**
   * Load the active pricing configuration
   */
  async loadActivePricing(): Promise<RidePricing | null> {
    try {
      const { data, error } = await this.supabase
        .from('ride_pricing')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) throw error;

      this.pricingSubject.next(data);
      return data;
    } catch (error) {
      console.error('Error loading active pricing:', error);
      return null;
    }
  }

  /**
   * Get all pricing configurations
   */
  async getAllPricing(): Promise<RidePricing[]> {
    try {
      const { data, error } = await this.supabase
        .from('ride_pricing')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error('Error loading pricing configurations:', error);
      return [];
    }
  }

  /**
   * Create a new pricing configuration
   */
  async createPricing(pricing: Omit<RidePricing, 'id' | 'created_at' | 'updated_at'>): Promise<RidePricing | null> {
    try {
      // If setting to active, first deactivate all other pricing configurations
      if (pricing.is_active) {
        const { error: deactivateError } = await this.supabase
          .from('ride_pricing')
          .update({ is_active: false })
          .eq('is_active', true);

        if (deactivateError) throw deactivateError;
      }

      const { data, error } = await this.supabase
        .from('ride_pricing')
        .insert([pricing])
        .select()
        .single();

      if (error) throw error;

      // If this is the active pricing, update the subject
      if (pricing.is_active) {
        this.pricingSubject.next(data);
      }

      return data;
    } catch (error) {
      console.error('Error creating pricing configuration:', error);
      return null;
    }
  }

  /**
   * Update a pricing configuration
   */
  async updatePricing(id: string, updates: Partial<RidePricing>): Promise<RidePricing | null> {
    try {
      // If setting to active, first deactivate all other pricing configurations
      if (updates.is_active) {
        const { error: deactivateError } = await this.supabase
          .from('ride_pricing')
          .update({ is_active: false })
          .neq('id', id)
          .eq('is_active', true);

        if (deactivateError) throw deactivateError;
      }

      const { data, error } = await this.supabase
        .from('ride_pricing')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // If this is the active pricing, update the subject
      if (data.is_active) {
        this.pricingSubject.next(data);
      }

      return data;
    } catch (error) {
      console.error('Error updating pricing configuration:', error);
      return null;
    }
  }

  /**
   * Set a pricing configuration as active
   */
  async setActiveStatus(id: string, isActive: boolean): Promise<boolean> {
    try {
      // If setting to active, first deactivate all other pricing configurations
      if (isActive) {
        const { error: deactivateError } = await this.supabase
          .from('ride_pricing')
          .update({ is_active: false })
          .neq('id', id);

        if (deactivateError) throw deactivateError;
      }

      // Update the specified pricing configuration
      const { error } = await this.supabase
        .from('ride_pricing')
        .update({ is_active: isActive })
        .eq('id', id);

      if (error) throw error;

      // Reload the active pricing
      await this.loadActivePricing();
      return true;
    } catch (error) {
      console.error('Error setting active status:', error);
      return false;
    }
  }

  /**
   * Delete a pricing configuration
   */
  async deletePricing(id: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('ride_pricing')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // Reload the active pricing in case we deleted the active one
      await this.loadActivePricing();
      return true;
    } catch (error) {
      console.error('Error deleting pricing configuration:', error);
      return false;
    }
  }
}
