{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, inject, NgZone, InjectionToken, ɵPendingTasksInternal as _PendingTasksInternal, PLATFORM_ID, ɵConsole as _Console, ɵformatRuntimeError as _formatRuntimeError, runInInjectionContext, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { isPlatformServer, XhrFactory, parseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n  /**\n   * Internal map of lowercase header names to values.\n   */\n  headers;\n  /**\n   * Internal map of lowercased header names to the normalized\n   * form of the name (the form seen first).\n   */\n  normalizedNames = new Map();\n  /**\n   * Complete the lazy initialization of this object (needed before reading).\n   */\n  lazyInit;\n  /**\n   * Queued updates to be materialized the next initialization.\n   */\n  lazyUpdate = null;\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const value = line.slice(index + 1).trim();\n            this.addHeaderEntry(name, value);\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map();\n      headers.forEach((value, name) => {\n        this.addHeaderEntry(name, value);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n  addHeaderEntry(name, value) {\n    const key = name.toLowerCase();\n    this.maybeSetNormalizedName(name, key);\n    if (this.headers.has(key)) {\n      this.headers.get(key).push(value);\n    } else {\n      this.headers.set(key, [value]);\n    }\n  }\n  setHeaderEntries(name, values) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map(value => value.toString());\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n  /**\n   * @internal\n   */\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` + `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n    }\n  }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n}\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n  map;\n  encoder;\n  updates = null;\n  cloneFrom = null;\n  constructor(options = {}) {\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (options.fromString) {\n      if (options.fromObject) {\n        throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key);\n      // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    })\n    // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n  defaultValue;\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n  map = new Map();\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token) {\n    return this.map.has(token);\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys() {\n    return this.map.keys();\n  }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n  url;\n  /**\n   * The request body, or `null` if one isn't set.\n   *\n   * Bodies are not enforced to be immutable, as they can include a reference to any\n   * user-defined data type. However, interceptors should take care to preserve\n   * idempotence by treating them as such.\n   */\n  body = null;\n  /**\n   * Outgoing headers for this request.\n   */\n  headers;\n  /**\n   * Shared and mutable context that can be used by interceptors\n   */\n  context;\n  /**\n   * Whether this request should be made in a way that exposes progress events.\n   *\n   * Progress events are expensive (change detection runs on each event) and so\n   * they should only be requested if the consumer intends to monitor them.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  reportProgress = false;\n  /**\n   * Whether this request should be sent with outgoing credentials (cookies).\n   */\n  withCredentials = false;\n  /**\n   * The expected response type of the server.\n   *\n   * This is used to parse the response appropriately before returning it to\n   * the requestee.\n   */\n  responseType = 'json';\n  /**\n   * The outgoing HTTP request method.\n   */\n  method;\n  /**\n   * Outgoing URL parameters.\n   *\n   * To pass a string representation of HTTP parameters in the URL-query-string format,\n   * the `HttpParamsOptions`' `fromString` may be used. For example:\n   *\n   * ```ts\n   * new HttpParams({fromString: 'angular=awesome'})\n   * ```\n   */\n  params;\n  /**\n   * The outgoing URL with all URL parameters set.\n   */\n  urlWithParams;\n  /**\n   * The HttpTransferCache option for the request\n   */\n  transferCache;\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options;\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    }\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n      if (!!options.context) {\n        this.context = options.context;\n      }\n      if (!!options.params) {\n        this.params = options.params;\n      }\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    this.headers ??= new HttpHeaders();\n    // If no context have been passed in, construct a new HttpContext instance.\n    this.context ??= new HttpContext();\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (typeof this.body === 'string' || isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body)) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return TEXT_CONTENT_TYPE;\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return JSON_CONTENT_TYPE;\n    }\n    // No type could be inferred.\n    return null;\n  }\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = update.transferCache ?? this.transferCache;\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials ?? this.withCredentials;\n    const reportProgress = update.reportProgress ?? this.reportProgress;\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    }\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    }\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache\n    });\n  }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n  /**\n   * All response headers.\n   */\n  headers;\n  /**\n   * Response status code.\n   */\n  status;\n  /**\n   * Textual description of response status code, defaults to OK.\n   *\n   * Do not depend on this.\n   */\n  statusText;\n  /**\n   * URL of the resource retrieved, or null if not available.\n   */\n  url;\n  /**\n   * Whether the status code falls in the 2xx range.\n   */\n  ok;\n  /**\n   * Type of the response, narrowed to either the full response or the header.\n   */\n  type;\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n  }\n  type = HttpEventType.ResponseHeader;\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * The response body, or `null` if one was not returned.\n   */\n  body;\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.body = init.body !== undefined ? init.body : null;\n  }\n  type = HttpEventType.Response;\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n  name = 'HttpErrorResponse';\n  message;\n  error;\n  /**\n   * Errors are never okay, even when the status code is in the 2xx success range.\n   */\n  ok = false;\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n    this.error = init.error || null;\n  }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n  HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n  HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n  HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n  HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n  HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n  HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n  HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n  HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n  HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n  HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n  HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n  HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n  HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n  HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n  HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n  HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n  HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n  HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n  HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n  HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n  HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n  HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n  HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n  HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n  HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n  HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n  HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n  HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n  HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n  HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n  HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n  HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n  HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n  HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n  HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n  HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n  HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n  HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n  HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n  HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n  HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n  HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n  HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n  HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n  HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n  HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n  HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n  HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n  HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n  HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n  HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n  HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n  HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n  HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n  HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n  HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n  HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n  HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n  HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n  HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n  HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n  handler;\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(first, url, options = {}) {\n    let req;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n      // Sort out parameters.\n      let params = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      }\n      // Construct the request.\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache\n      });\n    }\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req)));\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse));\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n              }\n              return res.body;\n            }));\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n              }\n              return res.body;\n            }));\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n              }\n              return res.body;\n            }));\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n  static ɵfac = function HttpClient_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClient)(i0.ɵɵinject(HttpHandler));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpClient,\n    factory: HttpClient.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], () => [{\n    type: HttpHandler\n  }], null);\n})();\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n  // We use an arrow function to always reference the current global implementation of `fetch`.\n  // This is helpful for cases when the global `fetch` implementation is modified by external code,\n  // see https://github.com/angular/angular/issues/57527.\n  fetchImpl = inject(FetchFactory, {\n    optional: true\n  })?.fetch ?? ((...args) => globalThis.fetch(...args));\n  ngZone = inject(NgZone);\n  handle(request) {\n    return new Observable(observer => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, error => observer.error(new HttpErrorResponse({\n        error\n      })));\n      return () => aborter.abort();\n    });\n  }\n  doRequest(request, signal, observer) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const init = _this.createRequestInit(request);\n      let response;\n      try {\n        // Run fetch outside of Angular zone.\n        // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n        // the response should eventually timeout which causes extra CD cycles every 500ms\n        const fetchPromise = _this.ngZone.runOutsideAngular(() => _this.fetchImpl(request.urlWithParams, {\n          signal,\n          ...init\n        }));\n        // Make sure Zone.js doesn't trigger false-positive unhandled promise\n        // error in case the Promise is rejected synchronously. See function\n        // description for additional information.\n        silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n        // Send the `Sent` event before awaiting the response.\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        response = yield fetchPromise;\n      } catch (error) {\n        observer.error(new HttpErrorResponse({\n          error,\n          status: error.status ?? 0,\n          statusText: error.statusText,\n          url: request.urlWithParams,\n          headers: error.headers\n        }));\n        return;\n      }\n      const headers = new HttpHeaders(response.headers);\n      const statusText = response.statusText;\n      const url = getResponseUrl$1(response) ?? request.urlWithParams;\n      let status = response.status;\n      let body = null;\n      if (request.reportProgress) {\n        observer.next(new HttpHeaderResponse({\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n      if (response.body) {\n        // Read Progress\n        const contentLength = response.headers.get('content-length');\n        const chunks = [];\n        const reader = response.body.getReader();\n        let receivedLength = 0;\n        let decoder;\n        let partialText;\n        // We have to check whether the Zone is defined in the global scope because this may be called\n        // when the zone is nooped.\n        const reqZone = typeof Zone !== 'undefined' && Zone.current;\n        // Perform response processing outside of Angular zone to\n        // ensure no excessive change detection runs are executed\n        // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n        yield _this.ngZone.runOutsideAngular(/*#__PURE__*/_asyncToGenerator(function* () {\n          while (true) {\n            const {\n              done,\n              value\n            } = yield reader.read();\n            if (done) {\n              break;\n            }\n            chunks.push(value);\n            receivedLength += value.length;\n            if (request.reportProgress) {\n              partialText = request.responseType === 'text' ? (partialText ?? '') + (decoder ??= new TextDecoder()).decode(value, {\n                stream: true\n              }) : undefined;\n              const reportProgress = () => observer.next({\n                type: HttpEventType.DownloadProgress,\n                total: contentLength ? +contentLength : undefined,\n                loaded: receivedLength,\n                partialText\n              });\n              reqZone ? reqZone.run(reportProgress) : reportProgress();\n            }\n          }\n        }));\n        // Combine all chunks.\n        const chunksAll = _this.concatChunks(chunks, receivedLength);\n        try {\n          const contentType = response.headers.get(CONTENT_TYPE_HEADER) ?? '';\n          body = _this.parseBody(request, chunksAll, contentType);\n        } catch (error) {\n          // Body loading or parsing failed\n          observer.error(new HttpErrorResponse({\n            error,\n            headers: new HttpHeaders(response.headers),\n            status: response.status,\n            statusText: response.statusText,\n            url: getResponseUrl$1(response) ?? request.urlWithParams\n          }));\n          return;\n        }\n      }\n      // Same behavior as the XhrBackend\n      if (status === 0) {\n        status = body ? HTTP_STATUS_CODE_OK : 0;\n      }\n      // ok determines whether the response will be transmitted on the event or\n      // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n      // but a successful status code can still result in an error if the user\n      // asked for JSON data and the body cannot be parsed as such.\n      const ok = status >= 200 && status < 300;\n      if (ok) {\n        observer.next(new HttpResponse({\n          body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n        // The full body has been received and delivered, no further events\n        // are possible. This request is complete.\n        observer.complete();\n      } else {\n        observer.error(new HttpErrorResponse({\n          error: body,\n          headers,\n          status,\n          statusText,\n          url\n        }));\n      }\n    })();\n  }\n  parseBody(request, binContent, contentType) {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n        return text === '' ? null : JSON.parse(text);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {\n          type: contentType\n        });\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n  createRequestInit(req) {\n    // We could share some of this logic with the XhrBackend\n    const headers = {};\n    const credentials = req.withCredentials ? 'include' : undefined;\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => headers[name] = values.join(','));\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has(ACCEPT_HEADER)) {\n      headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n    }\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers[CONTENT_TYPE_HEADER] = detectedType;\n      }\n    }\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials\n    };\n  }\n  concatChunks(chunks, totalLength) {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n    return chunksAll;\n  }\n  static ɵfac = function FetchBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FetchBackend)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FetchBackend,\n    factory: FetchBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FetchBackend, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {}\nfunction noop() {}\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n  promise.then(noop, noop);\n}\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n  return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n  return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n    handle: downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)\n  });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n  return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, downstreamRequest => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n  let chain = null;\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {\n        optional: true\n      }) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n    }\n    const pendingTasks = inject(_PendingTasksInternal);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const taskId = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n  backend;\n  injector;\n  chain = null;\n  pendingTasks = inject(_PendingTasksInternal);\n  contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n  constructor(backend, injector) {\n    super();\n    this.backend = backend;\n    this.injector = injector;\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n      // This flag is necessary because provideHttpClientTesting() overrides the backend\n      // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n      // no HTTP calls are actually performed during the test, so producing a warning would be\n      // misleading.\n      const isTestingBackend = this.backend.isTestingBackend;\n      if (isServer && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n        fetchBackendWarningDisplayed = true;\n        injector.get(_Console).warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' + \"to use `fetch` APIs. It's strongly recommended to \" + 'enable `fetch` for applications that use Server-Side Rendering ' + 'for better performance and compatibility. ' + 'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' + 'call at the root of the application.'));\n      }\n    }\n  }\n  handle(initialRequest) {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(new Set([...this.injector.get(HTTP_INTERCEPTOR_FNS), ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, [])]));\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n    }\n    if (this.contributeToStability) {\n      const taskId = this.pendingTasks.add();\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n    } else {\n      return this.chain(initialRequest, downstreamRequest => this.backend.handle(downstreamRequest));\n    }\n  }\n  static ɵfac = function HttpInterceptorHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpInterceptorHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpInterceptorHandler,\n    factory: HttpInterceptorHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: HttpBackend\n  }, {\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n  callbackMap;\n  document;\n  /**\n   * A resolved promise that can be used to schedule microtasks in the event handlers.\n   */\n  resolvedPromise = Promise.resolve();\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n    // Everything else happens inside the Observable boundary.\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n      let body = null;\n      // Whether the response callback has been called.\n      let finished = false;\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = event => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          }\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(new HttpResponse({\n            body,\n            status: HTTP_STATUS_CODE_OK,\n            statusText: 'OK',\n            url\n          }));\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError = error => {\n        cleanup();\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      };\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n      // The request has now been successfully sent.\n      observer.next({\n        type: HttpEventType.Sent\n      });\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n  removeListeners(script) {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument ??= this.document.implementation.createHTMLDocument();\n    foreignDocument.adoptNode(script);\n  }\n  static ɵfac = function JsonpClientBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpClientBackend,\n    factory: JsonpClientBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], () => [{\n    type: JsonpCallbackContext\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req);\n  }\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function JsonpInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || JsonpInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: JsonpInterceptor,\n    factory: JsonpInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n  }\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n  xhrFactory;\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n    }\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory = this.xhrFactory;\n    const source = xhrFactory.ɵloadImpl ? from(xhrFactory.ɵloadImpl()) : of(null);\n    return source.pipe(switchMap(() => {\n      // Everything happens on Observable subscription.\n      return new Observable(observer => {\n        // Start by setting up the XHR object with request method, URL, and withCredentials\n        // flag.\n        const xhr = xhrFactory.build();\n        xhr.open(req.method, req.urlWithParams);\n        if (req.withCredentials) {\n          xhr.withCredentials = true;\n        }\n        // Add all the requested headers.\n        req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n          xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n          const detectedType = req.detectContentTypeHeader();\n          // Sometimes Content-Type detection fails.\n          if (detectedType !== null) {\n            xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n          }\n        }\n        // Set the responseType if one was requested.\n        if (req.responseType) {\n          const responseType = req.responseType.toLowerCase();\n          // JSON responses need to be processed as text. This is because if the server\n          // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n          // xhr.response will be null, and xhr.responseText cannot be accessed to\n          // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n          // is parsed by first requesting text and then applying JSON.parse.\n          xhr.responseType = responseType !== 'json' ? responseType : 'text';\n        }\n        // Serialize the request body if one is present. If not, this will be set to null.\n        const reqBody = req.serializeBody();\n        // If progress events are enabled, response headers will be delivered\n        // in two events - the HttpHeaderResponse event and the full HttpResponse\n        // event. However, since response headers don't change in between these\n        // two events, it doesn't make sense to parse them twice. So headerResponse\n        // caches the data extracted from the response whenever it's first parsed,\n        // to ensure parsing isn't duplicated.\n        let headerResponse = null;\n        // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n        // state, and memoizes it into headerResponse.\n        const partialFromXhr = () => {\n          if (headerResponse !== null) {\n            return headerResponse;\n          }\n          const statusText = xhr.statusText || 'OK';\n          // Parse headers from XMLHttpRequest - this step is lazy.\n          const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n          // Read the response URL from the XMLHttpResponse instance and fall back on the\n          // request URL.\n          const url = getResponseUrl(xhr) || req.url;\n          // Construct the HttpHeaderResponse and memoize it.\n          headerResponse = new HttpHeaderResponse({\n            headers,\n            status: xhr.status,\n            statusText,\n            url\n          });\n          return headerResponse;\n        };\n        // Next, a few closures are defined for the various events which XMLHttpRequest can\n        // emit. This allows them to be unregistered as event listeners later.\n        // First up is the load event, which represents a response being fully available.\n        const onLoad = () => {\n          // Read response state from the memoized partial data.\n          let {\n            headers,\n            status,\n            statusText,\n            url\n          } = partialFromXhr();\n          // The body will be read out if present.\n          let body = null;\n          if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n            // Use XMLHttpRequest.response if set, responseText otherwise.\n            body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n          }\n          // Normalize another potential bug (this one comes from CORS).\n          if (status === 0) {\n            status = !!body ? HTTP_STATUS_CODE_OK : 0;\n          }\n          // ok determines whether the response will be transmitted on the event or\n          // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n          // but a successful status code can still result in an error if the user\n          // asked for JSON data and the body cannot be parsed as such.\n          let ok = status >= 200 && status < 300;\n          // Check whether the body needs to be parsed as JSON (in many cases the browser\n          // will have done that already).\n          if (req.responseType === 'json' && typeof body === 'string') {\n            // Save the original body, before attempting XSSI prefix stripping.\n            const originalBody = body;\n            body = body.replace(XSSI_PREFIX, '');\n            try {\n              // Attempt the parse. If it fails, a parse error should be delivered to the\n              // user.\n              body = body !== '' ? JSON.parse(body) : null;\n            } catch (error) {\n              // Since the JSON.parse failed, it's reasonable to assume this might not have\n              // been a JSON response. Restore the original body (including any XSSI prefix)\n              // to deliver a better error response.\n              body = originalBody;\n              // If this was an error request to begin with, leave it as a string, it\n              // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n              if (ok) {\n                // Even though the response status was 2xx, this is still an error.\n                ok = false;\n                // The parse error contains the text of the body that failed to parse.\n                body = {\n                  error,\n                  text: body\n                };\n              }\n            }\n          }\n          if (ok) {\n            // A successful response is delivered on the event stream.\n            observer.next(new HttpResponse({\n              body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n          } else {\n            // An unsuccessful request is delivered on the error channel.\n            observer.error(new HttpErrorResponse({\n              // The error in this case is the response body (error from the server).\n              error: body,\n              headers,\n              status,\n              statusText,\n              url: url || undefined\n            }));\n          }\n        };\n        // The onError callback is called when something goes wrong at the network level.\n        // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n        // transmitted on the error channel.\n        const onError = error => {\n          const {\n            url\n          } = partialFromXhr();\n          const res = new HttpErrorResponse({\n            error,\n            status: xhr.status || 0,\n            statusText: xhr.statusText || 'Unknown Error',\n            url: url || undefined\n          });\n          observer.error(res);\n        };\n        // The sentHeaders flag tracks whether the HttpResponseHeaders event\n        // has been sent on the stream. This is necessary to track if progress\n        // is enabled since the event will be sent on only the first download\n        // progress event.\n        let sentHeaders = false;\n        // The download progress event handler, which is only registered if\n        // progress events are enabled.\n        const onDownProgress = event => {\n          // Send the HttpResponseHeaders event if it hasn't been sent already.\n          if (!sentHeaders) {\n            observer.next(partialFromXhr());\n            sentHeaders = true;\n          }\n          // Start building the download progress event to deliver on the response\n          // event stream.\n          let progressEvent = {\n            type: HttpEventType.DownloadProgress,\n            loaded: event.loaded\n          };\n          // Set the total number of bytes in the event if it's available.\n          if (event.lengthComputable) {\n            progressEvent.total = event.total;\n          }\n          // If the request was for text content and a partial response is\n          // available on XMLHttpRequest, include it in the progress event\n          // to allow for streaming reads.\n          if (req.responseType === 'text' && !!xhr.responseText) {\n            progressEvent.partialText = xhr.responseText;\n          }\n          // Finally, fire the event.\n          observer.next(progressEvent);\n        };\n        // The upload progress event handler, which is only registered if\n        // progress events are enabled.\n        const onUpProgress = event => {\n          // Upload progress events are simpler. Begin building the progress\n          // event.\n          let progress = {\n            type: HttpEventType.UploadProgress,\n            loaded: event.loaded\n          };\n          // If the total number of bytes being uploaded is available, include\n          // it.\n          if (event.lengthComputable) {\n            progress.total = event.total;\n          }\n          // Send the event.\n          observer.next(progress);\n        };\n        // By default, register for load and error events.\n        xhr.addEventListener('load', onLoad);\n        xhr.addEventListener('error', onError);\n        xhr.addEventListener('timeout', onError);\n        xhr.addEventListener('abort', onError);\n        // Progress events are only enabled if requested.\n        if (req.reportProgress) {\n          // Download progress is always enabled if requested.\n          xhr.addEventListener('progress', onDownProgress);\n          // Upload progress depends on whether there is a body to upload.\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.addEventListener('progress', onUpProgress);\n          }\n        }\n        // Fire the request, and notify the event stream that it was fired.\n        xhr.send(reqBody);\n        observer.next({\n          type: HttpEventType.Sent\n        });\n        // This is the return from the Observable function, which is the\n        // request cancellation handler.\n        return () => {\n          // On a cancellation, remove all registered event listeners.\n          xhr.removeEventListener('error', onError);\n          xhr.removeEventListener('abort', onError);\n          xhr.removeEventListener('load', onLoad);\n          xhr.removeEventListener('timeout', onError);\n          if (req.reportProgress) {\n            xhr.removeEventListener('progress', onDownProgress);\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.removeEventListener('progress', onUpProgress);\n            }\n          }\n          // Finally, abort the in-flight request.\n          if (xhr.readyState !== xhr.DONE) {\n            xhr.abort();\n          }\n        };\n      });\n    }));\n  }\n  static ɵfac = function HttpXhrBackend_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXhrBackend)(i0.ɵɵinject(XhrFactory));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXhrBackend,\n    factory: HttpXhrBackend.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], () => [{\n    type: XhrFactory\n  }], null);\n})();\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n  doc;\n  platform;\n  cookieName;\n  lastCookieString = '';\n  lastToken = null;\n  /**\n   * @internal for testing\n   */\n  parseCount = 0;\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n  }\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = parseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n  static ɵfac = function HttpXsrfCookieExtractor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfCookieExtractor,\n    factory: HttpXsrfCookieExtractor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [XSRF_COOKIE_NAME]\n    }]\n  }], null);\n})();\nfunction xsrfInterceptorFn(req, next) {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (!inject(XSRF_ENABLED) || req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n    return next(req);\n  }\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({\n      headers: req.headers.set(headerName, token)\n    });\n  }\n  return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n  injector;\n  constructor(injector) {\n    this.injector = injector;\n  }\n  intercept(initialRequest, next) {\n    return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, downstreamRequest => next.handle(downstreamRequest)));\n  }\n  static ɵfac = function HttpXsrfInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpXsrfInterceptor)(i0.ɵɵinject(i0.EnvironmentInjector));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HttpXsrfInterceptor,\n    factory: HttpXsrfInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], () => [{\n    type: i0.EnvironmentInjector\n  }], null);\n})();\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n  HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n  HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n  HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n  HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n  HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n  HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map(f => f.ɵkind));\n    if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) && featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n      throw new Error(ngDevMode ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.` : '');\n    }\n  }\n  const providers = [HttpClient, HttpXhrBackend, HttpInterceptorHandler, {\n    provide: HttpHandler,\n    useExisting: HttpInterceptorHandler\n  }, {\n    provide: HttpBackend,\n    useFactory: () => {\n      return inject(FETCH_BACKEND, {\n        optional: true\n      }) ?? inject(HttpXhrBackend);\n    }\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: xsrfInterceptorFn,\n    multi: true\n  }, {\n    provide: XSRF_ENABLED,\n    useValue: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }];\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n  return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map(interceptorFn => {\n    return {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: interceptorFn,\n      multi: true\n    };\n  }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [{\n    provide: LEGACY_INTERCEPTOR_FN,\n    useFactory: legacyInterceptorFnFactory\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useExisting: LEGACY_INTERCEPTOR_FN,\n    multi: true\n  }]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({\n  cookieName,\n  headerName\n}) {\n  const providers = [];\n  if (cookieName !== undefined) {\n    providers.push({\n      provide: XSRF_COOKIE_NAME,\n      useValue: cookieName\n    });\n  }\n  if (headerName !== undefined) {\n    providers.push({\n      provide: XSRF_HEADER_NAME,\n      useValue: headerName\n    });\n  }\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [{\n    provide: XSRF_ENABLED,\n    useValue: false\n  }]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTOR_FNS,\n    useValue: jsonpInterceptorFn,\n    multi: true\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withRequestsMadeViaParent() {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [{\n    provide: HttpBackend,\n    useFactory: () => {\n      const handlerFromParent = inject(HttpHandler, {\n        skipSelf: true,\n        optional: true\n      });\n      if (ngDevMode && handlerFromParent === null) {\n        throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n      }\n      return handlerFromParent;\n    }\n  }]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [FetchBackend, {\n    provide: FETCH_BACKEND,\n    useExisting: FetchBackend\n  }, {\n    provide: HttpBackend,\n    useExisting: FetchBackend\n  }]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders\n    };\n  }\n  static ɵfac = function HttpClientXsrfModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientXsrfModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientXsrfModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [HttpXsrfInterceptor, {\n      provide: HTTP_INTERCEPTORS,\n      useExisting: HttpXsrfInterceptor,\n      multi: true\n    }, {\n      provide: HttpXsrfTokenExtractor,\n      useClass: HttpXsrfCookieExtractor\n    }, withXsrfConfiguration({\n      cookieName: XSRF_DEFAULT_COOKIE_NAME,\n      headerName: XSRF_DEFAULT_HEADER_NAME\n    }).ɵproviders, {\n      provide: XSRF_ENABLED,\n      useValue: true\n    }]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, withXsrfConfiguration({\n        cookieName: XSRF_DEFAULT_COOKIE_NAME,\n        headerName: XSRF_DEFAULT_HEADER_NAME\n      }).ɵproviders, {\n        provide: XSRF_ENABLED,\n        useValue: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n  static ɵfac = function HttpClientModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [provideHttpClient(withInterceptorsFromDi())]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Configures the dependency injector where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [provideHttpClient(withInterceptorsFromDi())]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n  static ɵfac = function HttpClientJsonpModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HttpClientJsonpModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: HttpClientJsonpModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [withJsonpSupport().ɵproviders]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [withJsonpSupport().ɵproviders]\n    }]\n  }], null, null);\n})();\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };", "map": {"version": 3, "names": ["i0", "ɵRuntimeError", "_RuntimeError", "Injectable", "inject", "NgZone", "InjectionToken", "ɵPendingTasksInternal", "_PendingTasksInternal", "PLATFORM_ID", "ɵConsole", "_Console", "ɵformatRuntimeError", "_formatRuntimeError", "runInInjectionContext", "Inject", "makeEnvironmentProviders", "NgModule", "concatMap", "filter", "map", "finalize", "switchMap", "of", "Observable", "from", "isPlatformServer", "XhrFactory", "parseCookieValue", "DOCUMENT", "HttpHandler", "HttpBackend", "HttpHeaders", "headers", "normalizedNames", "Map", "lazyInit", "lazyUpdate", "constructor", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "value", "trim", "addHeaderEntry", "Headers", "ngDevMode", "assertValidHeaders", "Object", "entries", "values", "setHeaderEntries", "has", "init", "toLowerCase", "get", "length", "keys", "Array", "getAll", "append", "clone", "op", "set", "delete", "maybeSetNormalizedName", "lcName", "copyFrom", "update", "applyUpdate", "other", "key", "concat", "base", "undefined", "push", "toDelete", "existing", "headerValues", "isArray", "toString", "fn", "Error", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "valueToString", "HttpParams", "encoder", "updates", "cloneFrom", "options", "fromString", "fromObject", "res", "appendAll", "_value", "<PERSON><PERSON><PERSON>", "join", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "CONTENT_TYPE_HEADER", "ACCEPT_HEADER", "X_REQUEST_URL_HEADER", "TEXT_CONTENT_TYPE", "JSON_CONTENT_TYPE", "ACCEPT_HEADER_VALUE", "HttpRequest", "url", "body", "context", "reportProgress", "withCredentials", "responseType", "urlWithParams", "transferCache", "third", "fourth", "toUpperCase", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "status", "statusText", "ok", "defaultStatus", "defaultStatusText", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "HTTP_STATUS_CODE_OK", "HTTP_STATUS_CODE_NO_CONTENT", "HttpStatusCode", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "ɵfac", "HttpClient_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "factory", "ɵsetClassMetadata", "XSSI_PREFIX$1", "getResponseUrl$1", "response", "xRequestUrl", "toLocaleLowerCase", "FETCH_BACKEND", "FetchBackend", "fetchImpl", "FetchFactory", "optional", "fetch", "args", "globalThis", "ngZone", "observer", "aborter", "AbortController", "doRequest", "signal", "then", "noop", "abort", "_this", "_asyncToGenerator", "createRequestInit", "fetchPromise", "runOutsideAngular", "silenceSuperfluousUnhandledPromiseRejection", "next", "<PERSON><PERSON>", "contentLength", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "partialText", "reqZone", "Zone", "current", "done", "read", "TextDecoder", "decode", "stream", "DownloadProgress", "total", "loaded", "run", "chunksAll", "concatChunks", "contentType", "parseBody", "complete", "binContent", "text", "parse", "buffer", "credentials", "detectedType", "totalLength", "Uint8Array", "position", "chunk", "FetchBackend_Factory", "promise", "interceptorChainEndFn", "finalHandlerFn", "adaptLegacyInterceptorToChain", "chainTailFn", "interceptor", "initialRequest", "intercept", "downstreamRequest", "chainedInterceptorFn", "interceptorFn", "injector", "HTTP_INTERCEPTORS", "HTTP_INTERCEPTOR_FNS", "HTTP_ROOT_INTERCEPTOR_FNS", "REQUESTS_CONTRIBUTE_TO_STABILITY", "providedIn", "legacyInterceptorFnFactory", "chain", "interceptors", "reduceRight", "pendingTasks", "contributeToStability", "taskId", "add", "remove", "fetchBackendWarningDisplayed", "HttpInterceptorHandler", "backend", "isServer", "isTestingBackend", "warn", "dedupedInterceptorFns", "Set", "nextSequencedFn", "HttpInterceptorHandler_Factory", "EnvironmentInjector", "nextRequestId", "foreignDocument", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JSONP_ERR_HEADERS_NOT_SUPPORTED", "JsonpCallbackContext", "jsonpCallbackContext", "window", "JsonpClientBackend", "callbackMap", "document", "resolvedPromise", "Promise", "resolve", "nextCallback", "callback", "node", "createElement", "src", "finished", "data", "cleanup", "removeEventListener", "onLoad", "onError", "addEventListener", "append<PERSON><PERSON><PERSON>", "removeListeners", "script", "implementation", "createHTMLDocument", "adoptNode", "JsonpClientBackend_Factory", "decorators", "jsonpInterceptorFn", "JsonpInterceptor", "JsonpInterceptor_Factory", "XSSI_PREFIX", "X_REQUEST_URL_REGEXP", "RegExp", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "source", "ɵloadImpl", "build", "open", "setRequestHeader", "reqBody", "headerResponse", "partialFromXhr", "responseText", "originalBody", "sentHeaders", "onDownProgress", "progressEvent", "lengthComputable", "onUpProgress", "progress", "UploadProgress", "upload", "send", "readyState", "DONE", "HttpXhrBackend_Factory", "XSRF_ENABLED", "XSRF_DEFAULT_COOKIE_NAME", "XSRF_COOKIE_NAME", "XSRF_DEFAULT_HEADER_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "platform", "cookieName", "lastCookieString", "lastToken", "parseCount", "getToken", "cookieString", "cookie", "HttpXsrfCookieExtractor_Factory", "xsrfInterceptorFn", "lcUrl", "startsWith", "headerName", "HttpXsrfInterceptor", "HttpXsrfInterceptor_Factory", "HttpFeatureKind", "makeHttpFeature", "kind", "providers", "ɵkind", "ɵproviders", "provideHttpClient", "features", "featureKinds", "f", "NoXsrfProtection", "CustomXsrfConfiguration", "provide", "useExisting", "useFactory", "useValue", "multi", "useClass", "feature", "withInterceptors", "interceptorFns", "Interceptors", "LEGACY_INTERCEPTOR_FN", "withInterceptorsFromDi", "LegacyInterceptors", "withXsrfConfiguration", "withNoXsrfProtection", "withJsonpSupport", "JsonpSupport", "withRequestsMadeViaParent", "RequestsMadeViaParent", "handlerFromParent", "skipSelf", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>tch", "HttpClientXsrfModule", "disable", "ngModule", "withOptions", "HttpClientXsrfModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "HttpClientModule", "HttpClientModule_Factory", "HttpClientJsonpModule", "HttpClientJsonpModule_Factory"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/common/fesm2022/module-BHk9jdTn.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ɵRuntimeError as _RuntimeError, Injectable, inject, NgZone, InjectionToken, ɵPendingTasksInternal as _PendingTasksInternal, PLATFORM_ID, ɵConsole as _Console, ɵformatRuntimeError as _formatRuntimeError, runInInjectionContext, Inject, makeEnvironmentProviders, NgModule } from '@angular/core';\nimport { concatMap, filter, map, finalize, switchMap } from 'rxjs/operators';\nimport { of, Observable, from } from 'rxjs';\nimport { isPlatformServer, XhrFactory, parseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**\n     * Internal map of lowercase header names to values.\n     */\n    headers;\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    normalizedNames = new Map();\n    /**\n     * Complete the lazy initialization of this object (needed before reading).\n     */\n    lazyInit;\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n    lazyUpdate = null;\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach((line) => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const value = line.slice(index + 1).trim();\n                        this.addHeaderEntry(name, value);\n                    }\n                });\n            };\n        }\n        else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n            this.headers = new Map();\n            headers.forEach((value, name) => {\n                this.addHeaderEntry(name, value);\n            });\n        }\n        else {\n            this.lazyInit = () => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    assertValidHeaders(headers);\n                }\n                this.headers = new Map();\n                Object.entries(headers).forEach(([name, values]) => {\n                    this.setHeaderEntries(name, values);\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or override for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach((update) => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach((key) => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter((value) => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    addHeaderEntry(name, value) {\n        const key = name.toLowerCase();\n        this.maybeSetNormalizedName(name, key);\n        if (this.headers.has(key)) {\n            this.headers.get(key).push(value);\n        }\n        else {\n            this.headers.set(key, [value]);\n        }\n    }\n    setHeaderEntries(name, values) {\n        const headerValues = (Array.isArray(values) ? values : [values]).map((value) => value.toString());\n        const key = name.toLowerCase();\n        this.headers.set(key, headerValues);\n        this.maybeSetNormalizedName(name, key);\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys()).forEach((key) => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(headers) {\n    for (const [key, value] of Object.entries(headers)) {\n        if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n            throw new Error(`Unexpected value of the \\`${key}\\` header provided. ` +\n                `Expecting either a string, a number or an array, but got: \\`${value}\\`.`);\n        }\n    }\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1\n                ? [codec.decodeKey(param), '']\n                : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    map;\n    encoder;\n    updates = null;\n    cloneFrom = null;\n    constructor(options = {}) {\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (options.fromString) {\n            if (options.fromObject) {\n                throw new _RuntimeError(2805 /* RuntimeErrorCode.CANNOT_SPECIFY_BOTH_FROM_STRING_AND_FROM_OBJECT */, ngDevMode && 'Cannot specify both fromString and fromObject.');\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach((key) => {\n                const value = options.fromObject[key];\n                // convert the values to strings\n                const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n                this.map.set(key, values);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach((param) => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach((_value) => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return (this.keys()\n            .map((key) => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key)\n                .map((value) => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter((param) => param !== '')\n            .join('&'));\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach((key) => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach((update) => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    defaultValue;\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```ts\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    map = new Map();\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * Checks for existence of a given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns True if the token exists, false otherwise.\n     */\n    has(token) {\n        return this.map.has(token);\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * `Content-Type` is an HTTP header used to indicate the media type\n * (also known as MIME type) of the resource being sent to the client\n * or received from the server.\n */\nconst CONTENT_TYPE_HEADER = 'Content-Type';\n/**\n * The `Accept` header is an HTTP request header that indicates the media types\n * (or content types) the client is willing to receive from the server.\n */\nconst ACCEPT_HEADER = 'Accept';\n/**\n * `X-Request-URL` is a custom HTTP header used in older browser versions,\n * including Firefox (< 32), Chrome (< 37), Safari (< 8), and Internet Explorer,\n * to include the full URL of the request in cross-origin requests.\n */\nconst X_REQUEST_URL_HEADER = 'X-Request-URL';\n/**\n * `text/plain` is a content type used to indicate that the content being\n * sent is plain text with no special formatting or structured data\n * like HTML, XML, or JSON.\n */\nconst TEXT_CONTENT_TYPE = 'text/plain';\n/**\n * `application/json` is a content type used to indicate that the content\n * being sent is in the JSON format.\n */\nconst JSON_CONTENT_TYPE = 'application/json';\n/**\n * `application/json, text/plain, *\\/*` is a content negotiation string often seen in the\n * Accept header of HTTP requests. It indicates the types of content the client is willing\n * to accept from the server, with a preference for `application/json` and `text/plain`,\n * but also accepting any other type (*\\/*).\n */\nconst ACCEPT_HEADER_VALUE = `${JSON_CONTENT_TYPE}, ${TEXT_CONTENT_TYPE}, */*`;\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n    body = null;\n    /**\n     * Outgoing headers for this request.\n     */\n    headers;\n    /**\n     * Shared and mutable context that can be used by interceptors\n     */\n    context;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n    withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n    responseType = 'json';\n    /**\n     * The outgoing HTTP request method.\n     */\n    method;\n    /**\n     * Outgoing URL parameters.\n     *\n     * To pass a string representation of HTTP parameters in the URL-query-string format,\n     * the `HttpParamsOptions`' `fromString` may be used. For example:\n     *\n     * ```ts\n     * new HttpParams({fromString: 'angular=awesome'})\n     * ```\n     */\n    params;\n    /**\n     * The outgoing URL with all URL parameters set.\n     */\n    urlWithParams;\n    /**\n     * The HttpTransferCache option for the request\n     */\n    transferCache;\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = third !== undefined ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n            // We do want to assign transferCache even if it's falsy (false is valid value)\n            this.transferCache = options.transferCache;\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        this.headers ??= new HttpHeaders();\n        // If no context have been passed in, construct a new HttpContext instance.\n        this.context ??= new HttpContext();\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (typeof this.body === 'string' ||\n            isArrayBuffer(this.body) ||\n            isBlob(this.body) ||\n            isFormData(this.body) ||\n            isUrlSearchParams(this.body)) {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return TEXT_CONTENT_TYPE;\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' ||\n            typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return JSON_CONTENT_TYPE;\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // Carefully handle the transferCache to differentiate between\n        // `false` and `undefined` in the update args.\n        const transferCache = update.transferCache ?? this.transferCache;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = update.body !== undefined ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = update.withCredentials ?? this.withCredentials;\n        const reportProgress = update.reportProgress ?? this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n            transferCache,\n        });\n    }\n}\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     *\n     * Note: The `FetchBackend` doesn't support progress report on uploads.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * All response headers.\n     */\n    headers;\n    /**\n     * Response status code.\n     */\n    status;\n    /**\n     * Textual description of response status code, defaults to OK.\n     *\n     * Do not depend on this.\n     */\n    statusText;\n    /**\n     * URL of the resource retrieved, or null if not available.\n     */\n    url;\n    /**\n     * Whether the status code falls in the 2xx range.\n     */\n    ok;\n    /**\n     * Type of the response, narrowed to either the full response or the header.\n     */\n    type;\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n    }\n    type = HttpEventType.ResponseHeader;\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * The response body, or `null` if one was not returned.\n     */\n    body;\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    type = HttpEventType.Response;\n    clone(update = {}) {\n        return new HttpResponse({\n            body: update.body !== undefined ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    name = 'HttpErrorResponse';\n    message;\n    error;\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n    ok = false;\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nconst HTTP_STATUS_CODE_OK = 200;\nconst HTTP_STATUS_CODE_NO_CONTENT = 204;\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nvar HttpStatusCode;\n(function (HttpStatusCode) {\n    HttpStatusCode[HttpStatusCode[\"Continue\"] = 100] = \"Continue\";\n    HttpStatusCode[HttpStatusCode[\"SwitchingProtocols\"] = 101] = \"SwitchingProtocols\";\n    HttpStatusCode[HttpStatusCode[\"Processing\"] = 102] = \"Processing\";\n    HttpStatusCode[HttpStatusCode[\"EarlyHints\"] = 103] = \"EarlyHints\";\n    HttpStatusCode[HttpStatusCode[\"Ok\"] = 200] = \"Ok\";\n    HttpStatusCode[HttpStatusCode[\"Created\"] = 201] = \"Created\";\n    HttpStatusCode[HttpStatusCode[\"Accepted\"] = 202] = \"Accepted\";\n    HttpStatusCode[HttpStatusCode[\"NonAuthoritativeInformation\"] = 203] = \"NonAuthoritativeInformation\";\n    HttpStatusCode[HttpStatusCode[\"NoContent\"] = 204] = \"NoContent\";\n    HttpStatusCode[HttpStatusCode[\"ResetContent\"] = 205] = \"ResetContent\";\n    HttpStatusCode[HttpStatusCode[\"PartialContent\"] = 206] = \"PartialContent\";\n    HttpStatusCode[HttpStatusCode[\"MultiStatus\"] = 207] = \"MultiStatus\";\n    HttpStatusCode[HttpStatusCode[\"AlreadyReported\"] = 208] = \"AlreadyReported\";\n    HttpStatusCode[HttpStatusCode[\"ImUsed\"] = 226] = \"ImUsed\";\n    HttpStatusCode[HttpStatusCode[\"MultipleChoices\"] = 300] = \"MultipleChoices\";\n    HttpStatusCode[HttpStatusCode[\"MovedPermanently\"] = 301] = \"MovedPermanently\";\n    HttpStatusCode[HttpStatusCode[\"Found\"] = 302] = \"Found\";\n    HttpStatusCode[HttpStatusCode[\"SeeOther\"] = 303] = \"SeeOther\";\n    HttpStatusCode[HttpStatusCode[\"NotModified\"] = 304] = \"NotModified\";\n    HttpStatusCode[HttpStatusCode[\"UseProxy\"] = 305] = \"UseProxy\";\n    HttpStatusCode[HttpStatusCode[\"Unused\"] = 306] = \"Unused\";\n    HttpStatusCode[HttpStatusCode[\"TemporaryRedirect\"] = 307] = \"TemporaryRedirect\";\n    HttpStatusCode[HttpStatusCode[\"PermanentRedirect\"] = 308] = \"PermanentRedirect\";\n    HttpStatusCode[HttpStatusCode[\"BadRequest\"] = 400] = \"BadRequest\";\n    HttpStatusCode[HttpStatusCode[\"Unauthorized\"] = 401] = \"Unauthorized\";\n    HttpStatusCode[HttpStatusCode[\"PaymentRequired\"] = 402] = \"PaymentRequired\";\n    HttpStatusCode[HttpStatusCode[\"Forbidden\"] = 403] = \"Forbidden\";\n    HttpStatusCode[HttpStatusCode[\"NotFound\"] = 404] = \"NotFound\";\n    HttpStatusCode[HttpStatusCode[\"MethodNotAllowed\"] = 405] = \"MethodNotAllowed\";\n    HttpStatusCode[HttpStatusCode[\"NotAcceptable\"] = 406] = \"NotAcceptable\";\n    HttpStatusCode[HttpStatusCode[\"ProxyAuthenticationRequired\"] = 407] = \"ProxyAuthenticationRequired\";\n    HttpStatusCode[HttpStatusCode[\"RequestTimeout\"] = 408] = \"RequestTimeout\";\n    HttpStatusCode[HttpStatusCode[\"Conflict\"] = 409] = \"Conflict\";\n    HttpStatusCode[HttpStatusCode[\"Gone\"] = 410] = \"Gone\";\n    HttpStatusCode[HttpStatusCode[\"LengthRequired\"] = 411] = \"LengthRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionFailed\"] = 412] = \"PreconditionFailed\";\n    HttpStatusCode[HttpStatusCode[\"PayloadTooLarge\"] = 413] = \"PayloadTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UriTooLong\"] = 414] = \"UriTooLong\";\n    HttpStatusCode[HttpStatusCode[\"UnsupportedMediaType\"] = 415] = \"UnsupportedMediaType\";\n    HttpStatusCode[HttpStatusCode[\"RangeNotSatisfiable\"] = 416] = \"RangeNotSatisfiable\";\n    HttpStatusCode[HttpStatusCode[\"ExpectationFailed\"] = 417] = \"ExpectationFailed\";\n    HttpStatusCode[HttpStatusCode[\"ImATeapot\"] = 418] = \"ImATeapot\";\n    HttpStatusCode[HttpStatusCode[\"MisdirectedRequest\"] = 421] = \"MisdirectedRequest\";\n    HttpStatusCode[HttpStatusCode[\"UnprocessableEntity\"] = 422] = \"UnprocessableEntity\";\n    HttpStatusCode[HttpStatusCode[\"Locked\"] = 423] = \"Locked\";\n    HttpStatusCode[HttpStatusCode[\"FailedDependency\"] = 424] = \"FailedDependency\";\n    HttpStatusCode[HttpStatusCode[\"TooEarly\"] = 425] = \"TooEarly\";\n    HttpStatusCode[HttpStatusCode[\"UpgradeRequired\"] = 426] = \"UpgradeRequired\";\n    HttpStatusCode[HttpStatusCode[\"PreconditionRequired\"] = 428] = \"PreconditionRequired\";\n    HttpStatusCode[HttpStatusCode[\"TooManyRequests\"] = 429] = \"TooManyRequests\";\n    HttpStatusCode[HttpStatusCode[\"RequestHeaderFieldsTooLarge\"] = 431] = \"RequestHeaderFieldsTooLarge\";\n    HttpStatusCode[HttpStatusCode[\"UnavailableForLegalReasons\"] = 451] = \"UnavailableForLegalReasons\";\n    HttpStatusCode[HttpStatusCode[\"InternalServerError\"] = 500] = \"InternalServerError\";\n    HttpStatusCode[HttpStatusCode[\"NotImplemented\"] = 501] = \"NotImplemented\";\n    HttpStatusCode[HttpStatusCode[\"BadGateway\"] = 502] = \"BadGateway\";\n    HttpStatusCode[HttpStatusCode[\"ServiceUnavailable\"] = 503] = \"ServiceUnavailable\";\n    HttpStatusCode[HttpStatusCode[\"GatewayTimeout\"] = 504] = \"GatewayTimeout\";\n    HttpStatusCode[HttpStatusCode[\"HttpVersionNotSupported\"] = 505] = \"HttpVersionNotSupported\";\n    HttpStatusCode[HttpStatusCode[\"VariantAlsoNegotiates\"] = 506] = \"VariantAlsoNegotiates\";\n    HttpStatusCode[HttpStatusCode[\"InsufficientStorage\"] = 507] = \"InsufficientStorage\";\n    HttpStatusCode[HttpStatusCode[\"LoopDetected\"] = 508] = \"LoopDetected\";\n    HttpStatusCode[HttpStatusCode[\"NotExtended\"] = 510] = \"NotExtended\";\n    HttpStatusCode[HttpStatusCode[\"NetworkAuthenticationRequired\"] = 511] = \"NetworkAuthenticationRequired\";\n})(HttpStatusCode || (HttpStatusCode = {}));\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```ts\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```ts\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```ts\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```ts\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    handler;\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n                transferCache: options.transferCache,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = (events$.pipe(filter((event) => event instanceof HttpResponse)));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new _RuntimeError(2806 /* RuntimeErrorCode.RESPONSE_IS_NOT_AN_ARRAY_BUFFER */, ngDevMode && 'Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new _RuntimeError(2807 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_BLOB */, ngDevMode && 'Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new _RuntimeError(2808 /* RuntimeErrorCode.RESPONSE_IS_NOT_A_STRING */, ngDevMode && 'Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new _RuntimeError(2809 /* RuntimeErrorCode.UNHANDLED_OBSERVE_TYPE */, ngDevMode && `Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClient });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpHandler }] });\n\nconst XSSI_PREFIX$1 = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl$1(response) {\n    if (response.url) {\n        return response.url;\n    }\n    // stored as lowercase in the map\n    const xRequestUrl = X_REQUEST_URL_HEADER.toLocaleLowerCase();\n    return response.headers.get(xRequestUrl);\n}\n/**\n * An internal injection token to reference `FetchBackend` implementation\n * in a tree-shakable way.\n */\nconst FETCH_BACKEND = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'FETCH_BACKEND' : '');\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\nclass FetchBackend {\n    // We use an arrow function to always reference the current global implementation of `fetch`.\n    // This is helpful for cases when the global `fetch` implementation is modified by external code,\n    // see https://github.com/angular/angular/issues/57527.\n    fetchImpl = inject(FetchFactory, { optional: true })?.fetch ?? ((...args) => globalThis.fetch(...args));\n    ngZone = inject(NgZone);\n    handle(request) {\n        return new Observable((observer) => {\n            const aborter = new AbortController();\n            this.doRequest(request, aborter.signal, observer).then(noop, (error) => observer.error(new HttpErrorResponse({ error })));\n            return () => aborter.abort();\n        });\n    }\n    async doRequest(request, signal, observer) {\n        const init = this.createRequestInit(request);\n        let response;\n        try {\n            // Run fetch outside of Angular zone.\n            // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n            // the response should eventually timeout which causes extra CD cycles every 500ms\n            const fetchPromise = this.ngZone.runOutsideAngular(() => this.fetchImpl(request.urlWithParams, { signal, ...init }));\n            // Make sure Zone.js doesn't trigger false-positive unhandled promise\n            // error in case the Promise is rejected synchronously. See function\n            // description for additional information.\n            silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n            // Send the `Sent` event before awaiting the response.\n            observer.next({ type: HttpEventType.Sent });\n            response = await fetchPromise;\n        }\n        catch (error) {\n            observer.error(new HttpErrorResponse({\n                error,\n                status: error.status ?? 0,\n                statusText: error.statusText,\n                url: request.urlWithParams,\n                headers: error.headers,\n            }));\n            return;\n        }\n        const headers = new HttpHeaders(response.headers);\n        const statusText = response.statusText;\n        const url = getResponseUrl$1(response) ?? request.urlWithParams;\n        let status = response.status;\n        let body = null;\n        if (request.reportProgress) {\n            observer.next(new HttpHeaderResponse({ headers, status, statusText, url }));\n        }\n        if (response.body) {\n            // Read Progress\n            const contentLength = response.headers.get('content-length');\n            const chunks = [];\n            const reader = response.body.getReader();\n            let receivedLength = 0;\n            let decoder;\n            let partialText;\n            // We have to check whether the Zone is defined in the global scope because this may be called\n            // when the zone is nooped.\n            const reqZone = typeof Zone !== 'undefined' && Zone.current;\n            // Perform response processing outside of Angular zone to\n            // ensure no excessive change detection runs are executed\n            // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n            await this.ngZone.runOutsideAngular(async () => {\n                while (true) {\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    chunks.push(value);\n                    receivedLength += value.length;\n                    if (request.reportProgress) {\n                        partialText =\n                            request.responseType === 'text'\n                                ? (partialText ?? '') +\n                                    (decoder ??= new TextDecoder()).decode(value, { stream: true })\n                                : undefined;\n                        const reportProgress = () => observer.next({\n                            type: HttpEventType.DownloadProgress,\n                            total: contentLength ? +contentLength : undefined,\n                            loaded: receivedLength,\n                            partialText,\n                        });\n                        reqZone ? reqZone.run(reportProgress) : reportProgress();\n                    }\n                }\n            });\n            // Combine all chunks.\n            const chunksAll = this.concatChunks(chunks, receivedLength);\n            try {\n                const contentType = response.headers.get(CONTENT_TYPE_HEADER) ?? '';\n                body = this.parseBody(request, chunksAll, contentType);\n            }\n            catch (error) {\n                // Body loading or parsing failed\n                observer.error(new HttpErrorResponse({\n                    error,\n                    headers: new HttpHeaders(response.headers),\n                    status: response.status,\n                    statusText: response.statusText,\n                    url: getResponseUrl$1(response) ?? request.urlWithParams,\n                }));\n                return;\n            }\n        }\n        // Same behavior as the XhrBackend\n        if (status === 0) {\n            status = body ? HTTP_STATUS_CODE_OK : 0;\n        }\n        // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n        const ok = status >= 200 && status < 300;\n        if (ok) {\n            observer.next(new HttpResponse({\n                body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n            // The full body has been received and delivered, no further events\n            // are possible. This request is complete.\n            observer.complete();\n        }\n        else {\n            observer.error(new HttpErrorResponse({\n                error: body,\n                headers,\n                status,\n                statusText,\n                url,\n            }));\n        }\n    }\n    parseBody(request, binContent, contentType) {\n        switch (request.responseType) {\n            case 'json':\n                // stripping the XSSI when present\n                const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX$1, '');\n                return text === '' ? null : JSON.parse(text);\n            case 'text':\n                return new TextDecoder().decode(binContent);\n            case 'blob':\n                return new Blob([binContent], { type: contentType });\n            case 'arraybuffer':\n                return binContent.buffer;\n        }\n    }\n    createRequestInit(req) {\n        // We could share some of this logic with the XhrBackend\n        const headers = {};\n        const credentials = req.withCredentials ? 'include' : undefined;\n        // Setting all the requested headers.\n        req.headers.forEach((name, values) => (headers[name] = values.join(',')));\n        // Add an Accept header if one isn't present already.\n        if (!req.headers.has(ACCEPT_HEADER)) {\n            headers[ACCEPT_HEADER] = ACCEPT_HEADER_VALUE;\n        }\n        // Auto-detect the Content-Type header if one isn't present already.\n        if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n            const detectedType = req.detectContentTypeHeader();\n            // Sometimes Content-Type detection fails.\n            if (detectedType !== null) {\n                headers[CONTENT_TYPE_HEADER] = detectedType;\n            }\n        }\n        return {\n            body: req.serializeBody(),\n            method: req.method,\n            headers,\n            credentials,\n        };\n    }\n    concatChunks(chunks, totalLength) {\n        const chunksAll = new Uint8Array(totalLength);\n        let position = 0;\n        for (const chunk of chunks) {\n            chunksAll.set(chunk, position);\n            position += chunk.length;\n        }\n        return chunksAll;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FetchBackend, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FetchBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FetchBackend, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nclass FetchFactory {\n}\nfunction noop() { }\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise) {\n    promise.then(noop, noop);\n}\n\nfunction interceptorChainEndFn(req, finalHandlerFn) {\n    return finalHandlerFn(req);\n}\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(chainTailFn, interceptor) {\n    return (initialRequest, finalHandlerFn) => interceptor.intercept(initialRequest, {\n        handle: (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn),\n    });\n}\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(chainTailFn, interceptorFn, injector) {\n    return (initialRequest, finalHandlerFn) => runInInjectionContext(injector, () => interceptorFn(initialRequest, (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn)));\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTORS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nconst HTTP_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '');\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nconst HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken(ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '');\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nconst REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken(ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '', { providedIn: 'root', factory: () => true });\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nfunction legacyInterceptorFnFactory() {\n    let chain = null;\n    return (req, handler) => {\n        if (chain === null) {\n            const interceptors = inject(HTTP_INTERCEPTORS, { optional: true }) ?? [];\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            chain = interceptors.reduceRight(adaptLegacyInterceptorToChain, interceptorChainEndFn);\n        }\n        const pendingTasks = inject(_PendingTasksInternal);\n        const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n        if (contributeToStability) {\n            const taskId = pendingTasks.add();\n            return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n        }\n        else {\n            return chain(req, handler);\n        }\n    };\n}\nlet fetchBackendWarningDisplayed = false;\nclass HttpInterceptorHandler extends HttpHandler {\n    backend;\n    injector;\n    chain = null;\n    pendingTasks = inject(_PendingTasksInternal);\n    contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    constructor(backend, injector) {\n        super();\n        this.backend = backend;\n        this.injector = injector;\n        // We strongly recommend using fetch backend for HTTP calls when SSR is used\n        // for an application. The logic below checks if that's the case and produces\n        // a warning otherwise.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n            const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n            // This flag is necessary because provideHttpClientTesting() overrides the backend\n            // even if `withFetch()` is used within the test. When the testing HTTP backend is provided,\n            // no HTTP calls are actually performed during the test, so producing a warning would be\n            // misleading.\n            const isTestingBackend = this.backend.isTestingBackend;\n            if (isServer && !(this.backend instanceof FetchBackend) && !isTestingBackend) {\n                fetchBackendWarningDisplayed = true;\n                injector\n                    .get(_Console)\n                    .warn(_formatRuntimeError(2801 /* RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR */, 'Angular detected that `HttpClient` is not configured ' +\n                    \"to use `fetch` APIs. It's strongly recommended to \" +\n                    'enable `fetch` for applications that use Server-Side Rendering ' +\n                    'for better performance and compatibility. ' +\n                    'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' +\n                    'call at the root of the application.'));\n            }\n        }\n    }\n    handle(initialRequest) {\n        if (this.chain === null) {\n            const dedupedInterceptorFns = Array.from(new Set([\n                ...this.injector.get(HTTP_INTERCEPTOR_FNS),\n                ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, []),\n            ]));\n            // Note: interceptors are wrapped right-to-left so that final execution order is\n            // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n            // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n            // out.\n            this.chain = dedupedInterceptorFns.reduceRight((nextSequencedFn, interceptorFn) => chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector), interceptorChainEndFn);\n        }\n        if (this.contributeToStability) {\n            const taskId = this.pendingTasks.add();\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest)).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n        }\n        else {\n            return this.chain(initialRequest, (downstreamRequest) => this.backend.handle(downstreamRequest));\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpInterceptorHandler, deps: [{ token: HttpBackend }, { token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpInterceptorHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpInterceptorHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: HttpBackend }, { type: i0.EnvironmentInjector }] });\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nconst JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    callbackMap;\n    document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n    resolvedPromise = Promise.resolve();\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Check the request headers. JSONP doesn't support headers and\n        // cannot set any that were supplied.\n        if (req.headers.keys().length > 0) {\n            throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                node.removeEventListener('load', onLoad);\n                node.removeEventListener('error', onError);\n                // Remove the <script> tag if it's still on the page.\n                node.remove();\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: HTTP_STATUS_CODE_OK,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                if (!finished) {\n                    this.removeListeners(node);\n                }\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n    removeListeners(script) {\n        // Issue #34818\n        // Changing <script>'s ownerDocument will prevent it from execution.\n        // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n        foreignDocument ??= this.document.implementation.createHTMLDocument();\n        foreignDocument.adoptNode(script);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpClientBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nfunction jsonpInterceptorFn(req, next) {\n    if (req.method === 'JSONP') {\n        return inject(JsonpClientBackend).handle(req);\n    }\n    // Fall through for normal HTTP requests.\n    return next(req);\n}\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param initialRequest The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => jsonpInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\nconst X_REQUEST_URL_REGEXP = RegExp(`^${X_REQUEST_URL_HEADER}:`, 'm');\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (X_REQUEST_URL_REGEXP.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader(X_REQUEST_URL_HEADER);\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    xhrFactory;\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new _RuntimeError(-2800 /* RuntimeErrorCode.MISSING_JSONP_MODULE */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`);\n        }\n        // Check whether this factory has a special function to load an XHR implementation\n        // for various non-browser environments. We currently limit it to only `ServerXhr`\n        // class, which needs to load an XHR implementation.\n        const xhrFactory = this.xhrFactory;\n        const source = xhrFactory.ɵloadImpl\n            ? from(xhrFactory.ɵloadImpl())\n            : of(null);\n        return source.pipe(switchMap(() => {\n            // Everything happens on Observable subscription.\n            return new Observable((observer) => {\n                // Start by setting up the XHR object with request method, URL, and withCredentials\n                // flag.\n                const xhr = xhrFactory.build();\n                xhr.open(req.method, req.urlWithParams);\n                if (req.withCredentials) {\n                    xhr.withCredentials = true;\n                }\n                // Add all the requested headers.\n                req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n                // Add an Accept header if one isn't present already.\n                if (!req.headers.has(ACCEPT_HEADER)) {\n                    xhr.setRequestHeader(ACCEPT_HEADER, ACCEPT_HEADER_VALUE);\n                }\n                // Auto-detect the Content-Type header if one isn't present already.\n                if (!req.headers.has(CONTENT_TYPE_HEADER)) {\n                    const detectedType = req.detectContentTypeHeader();\n                    // Sometimes Content-Type detection fails.\n                    if (detectedType !== null) {\n                        xhr.setRequestHeader(CONTENT_TYPE_HEADER, detectedType);\n                    }\n                }\n                // Set the responseType if one was requested.\n                if (req.responseType) {\n                    const responseType = req.responseType.toLowerCase();\n                    // JSON responses need to be processed as text. This is because if the server\n                    // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                    // xhr.response will be null, and xhr.responseText cannot be accessed to\n                    // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                    // is parsed by first requesting text and then applying JSON.parse.\n                    xhr.responseType = (responseType !== 'json' ? responseType : 'text');\n                }\n                // Serialize the request body if one is present. If not, this will be set to null.\n                const reqBody = req.serializeBody();\n                // If progress events are enabled, response headers will be delivered\n                // in two events - the HttpHeaderResponse event and the full HttpResponse\n                // event. However, since response headers don't change in between these\n                // two events, it doesn't make sense to parse them twice. So headerResponse\n                // caches the data extracted from the response whenever it's first parsed,\n                // to ensure parsing isn't duplicated.\n                let headerResponse = null;\n                // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n                // state, and memoizes it into headerResponse.\n                const partialFromXhr = () => {\n                    if (headerResponse !== null) {\n                        return headerResponse;\n                    }\n                    const statusText = xhr.statusText || 'OK';\n                    // Parse headers from XMLHttpRequest - this step is lazy.\n                    const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                    // Read the response URL from the XMLHttpResponse instance and fall back on the\n                    // request URL.\n                    const url = getResponseUrl(xhr) || req.url;\n                    // Construct the HttpHeaderResponse and memoize it.\n                    headerResponse = new HttpHeaderResponse({ headers, status: xhr.status, statusText, url });\n                    return headerResponse;\n                };\n                // Next, a few closures are defined for the various events which XMLHttpRequest can\n                // emit. This allows them to be unregistered as event listeners later.\n                // First up is the load event, which represents a response being fully available.\n                const onLoad = () => {\n                    // Read response state from the memoized partial data.\n                    let { headers, status, statusText, url } = partialFromXhr();\n                    // The body will be read out if present.\n                    let body = null;\n                    if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n                        // Use XMLHttpRequest.response if set, responseText otherwise.\n                        body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n                    }\n                    // Normalize another potential bug (this one comes from CORS).\n                    if (status === 0) {\n                        status = !!body ? HTTP_STATUS_CODE_OK : 0;\n                    }\n                    // ok determines whether the response will be transmitted on the event or\n                    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                    // but a successful status code can still result in an error if the user\n                    // asked for JSON data and the body cannot be parsed as such.\n                    let ok = status >= 200 && status < 300;\n                    // Check whether the body needs to be parsed as JSON (in many cases the browser\n                    // will have done that already).\n                    if (req.responseType === 'json' && typeof body === 'string') {\n                        // Save the original body, before attempting XSSI prefix stripping.\n                        const originalBody = body;\n                        body = body.replace(XSSI_PREFIX, '');\n                        try {\n                            // Attempt the parse. If it fails, a parse error should be delivered to the\n                            // user.\n                            body = body !== '' ? JSON.parse(body) : null;\n                        }\n                        catch (error) {\n                            // Since the JSON.parse failed, it's reasonable to assume this might not have\n                            // been a JSON response. Restore the original body (including any XSSI prefix)\n                            // to deliver a better error response.\n                            body = originalBody;\n                            // If this was an error request to begin with, leave it as a string, it\n                            // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n                            if (ok) {\n                                // Even though the response status was 2xx, this is still an error.\n                                ok = false;\n                                // The parse error contains the text of the body that failed to parse.\n                                body = { error, text: body };\n                            }\n                        }\n                    }\n                    if (ok) {\n                        // A successful response is delivered on the event stream.\n                        observer.next(new HttpResponse({\n                            body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                        // The full body has been received and delivered, no further events\n                        // are possible. This request is complete.\n                        observer.complete();\n                    }\n                    else {\n                        // An unsuccessful request is delivered on the error channel.\n                        observer.error(new HttpErrorResponse({\n                            // The error in this case is the response body (error from the server).\n                            error: body,\n                            headers,\n                            status,\n                            statusText,\n                            url: url || undefined,\n                        }));\n                    }\n                };\n                // The onError callback is called when something goes wrong at the network level.\n                // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n                // transmitted on the error channel.\n                const onError = (error) => {\n                    const { url } = partialFromXhr();\n                    const res = new HttpErrorResponse({\n                        error,\n                        status: xhr.status || 0,\n                        statusText: xhr.statusText || 'Unknown Error',\n                        url: url || undefined,\n                    });\n                    observer.error(res);\n                };\n                // The sentHeaders flag tracks whether the HttpResponseHeaders event\n                // has been sent on the stream. This is necessary to track if progress\n                // is enabled since the event will be sent on only the first download\n                // progress event.\n                let sentHeaders = false;\n                // The download progress event handler, which is only registered if\n                // progress events are enabled.\n                const onDownProgress = (event) => {\n                    // Send the HttpResponseHeaders event if it hasn't been sent already.\n                    if (!sentHeaders) {\n                        observer.next(partialFromXhr());\n                        sentHeaders = true;\n                    }\n                    // Start building the download progress event to deliver on the response\n                    // event stream.\n                    let progressEvent = {\n                        type: HttpEventType.DownloadProgress,\n                        loaded: event.loaded,\n                    };\n                    // Set the total number of bytes in the event if it's available.\n                    if (event.lengthComputable) {\n                        progressEvent.total = event.total;\n                    }\n                    // If the request was for text content and a partial response is\n                    // available on XMLHttpRequest, include it in the progress event\n                    // to allow for streaming reads.\n                    if (req.responseType === 'text' && !!xhr.responseText) {\n                        progressEvent.partialText = xhr.responseText;\n                    }\n                    // Finally, fire the event.\n                    observer.next(progressEvent);\n                };\n                // The upload progress event handler, which is only registered if\n                // progress events are enabled.\n                const onUpProgress = (event) => {\n                    // Upload progress events are simpler. Begin building the progress\n                    // event.\n                    let progress = {\n                        type: HttpEventType.UploadProgress,\n                        loaded: event.loaded,\n                    };\n                    // If the total number of bytes being uploaded is available, include\n                    // it.\n                    if (event.lengthComputable) {\n                        progress.total = event.total;\n                    }\n                    // Send the event.\n                    observer.next(progress);\n                };\n                // By default, register for load and error events.\n                xhr.addEventListener('load', onLoad);\n                xhr.addEventListener('error', onError);\n                xhr.addEventListener('timeout', onError);\n                xhr.addEventListener('abort', onError);\n                // Progress events are only enabled if requested.\n                if (req.reportProgress) {\n                    // Download progress is always enabled if requested.\n                    xhr.addEventListener('progress', onDownProgress);\n                    // Upload progress depends on whether there is a body to upload.\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.addEventListener('progress', onUpProgress);\n                    }\n                }\n                // Fire the request, and notify the event stream that it was fired.\n                xhr.send(reqBody);\n                observer.next({ type: HttpEventType.Sent });\n                // This is the return from the Observable function, which is the\n                // request cancellation handler.\n                return () => {\n                    // On a cancellation, remove all registered event listeners.\n                    xhr.removeEventListener('error', onError);\n                    xhr.removeEventListener('abort', onError);\n                    xhr.removeEventListener('load', onLoad);\n                    xhr.removeEventListener('timeout', onError);\n                    if (req.reportProgress) {\n                        xhr.removeEventListener('progress', onDownProgress);\n                        if (reqBody !== null && xhr.upload) {\n                            xhr.upload.removeEventListener('progress', onUpProgress);\n                        }\n                    }\n                    // Finally, abort the in-flight request.\n                    if (xhr.readyState !== xhr.DONE) {\n                        xhr.abort();\n                    }\n                };\n            });\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXhrBackend });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: XhrFactory }] });\n\nconst XSRF_ENABLED = new InjectionToken(ngDevMode ? 'XSRF_ENABLED' : '');\nconst XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nconst XSRF_COOKIE_NAME = new InjectionToken(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_COOKIE_NAME,\n});\nconst XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nconst XSRF_HEADER_NAME = new InjectionToken(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n    providedIn: 'root',\n    factory: () => XSRF_DEFAULT_HEADER_NAME,\n});\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    doc;\n    platform;\n    cookieName;\n    lastCookieString = '';\n    lastToken = null;\n    /**\n     * @internal for testing\n     */\n    parseCount = 0;\n    constructor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n    }\n    getToken() {\n        if (this.platform === 'server') {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = parseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfCookieExtractor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }] });\nfunction xsrfInterceptorFn(req, next) {\n    const lcUrl = req.url.toLowerCase();\n    // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n    if (!inject(XSRF_ENABLED) ||\n        req.method === 'GET' ||\n        req.method === 'HEAD' ||\n        lcUrl.startsWith('http://') ||\n        lcUrl.startsWith('https://')) {\n        return next(req);\n    }\n    const token = inject(HttpXsrfTokenExtractor).getToken();\n    const headerName = inject(XSRF_HEADER_NAME);\n    // Be careful not to overwrite an existing header of the same name.\n    if (token != null && !req.headers.has(headerName)) {\n        req = req.clone({ headers: req.headers.set(headerName, token) });\n    }\n    return next(req);\n}\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    injector;\n    constructor(injector) {\n        this.injector = injector;\n    }\n    intercept(initialRequest, next) {\n        return runInInjectionContext(this.injector, () => xsrfInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: i0.EnvironmentInjector }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.EnvironmentInjector }] });\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nvar HttpFeatureKind;\n(function (HttpFeatureKind) {\n    HttpFeatureKind[HttpFeatureKind[\"Interceptors\"] = 0] = \"Interceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"LegacyInterceptors\"] = 1] = \"LegacyInterceptors\";\n    HttpFeatureKind[HttpFeatureKind[\"CustomXsrfConfiguration\"] = 2] = \"CustomXsrfConfiguration\";\n    HttpFeatureKind[HttpFeatureKind[\"NoXsrfProtection\"] = 3] = \"NoXsrfProtection\";\n    HttpFeatureKind[HttpFeatureKind[\"JsonpSupport\"] = 4] = \"JsonpSupport\";\n    HttpFeatureKind[HttpFeatureKind[\"RequestsMadeViaParent\"] = 5] = \"RequestsMadeViaParent\";\n    HttpFeatureKind[HttpFeatureKind[\"Fetch\"] = 6] = \"Fetch\";\n})(HttpFeatureKind || (HttpFeatureKind = {}));\nfunction makeHttpFeature(kind, providers) {\n    return {\n        ɵkind: kind,\n        ɵproviders: providers,\n    };\n}\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```ts\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nfunction provideHttpClient(...features) {\n    if (ngDevMode) {\n        const featureKinds = new Set(features.map((f) => f.ɵkind));\n        if (featureKinds.has(HttpFeatureKind.NoXsrfProtection) &&\n            featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)) {\n            throw new Error(ngDevMode\n                ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.`\n                : '');\n        }\n    }\n    const providers = [\n        HttpClient,\n        HttpXhrBackend,\n        HttpInterceptorHandler,\n        { provide: HttpHandler, useExisting: HttpInterceptorHandler },\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                return inject(FETCH_BACKEND, { optional: true }) ?? inject(HttpXhrBackend);\n            },\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: xsrfInterceptorFn,\n            multi: true,\n        },\n        { provide: XSRF_ENABLED, useValue: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n    ];\n    for (const feature of features) {\n        providers.push(...feature.ɵproviders);\n    }\n    return makeEnvironmentProviders(providers);\n}\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withInterceptors(interceptorFns) {\n    return makeHttpFeature(HttpFeatureKind.Interceptors, interceptorFns.map((interceptorFn) => {\n        return {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useValue: interceptorFn,\n            multi: true,\n        };\n    }));\n}\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken(ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '');\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nfunction withInterceptorsFromDi() {\n    // Note: the legacy interceptor function is provided here via an intermediate token\n    // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n    // included multiple times, all of the multi-provider entries will have the same instance of the\n    // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n    // interceptors will not run multiple times.\n    return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [\n        {\n            provide: LEGACY_INTERCEPTOR_FN,\n            useFactory: legacyInterceptorFnFactory,\n        },\n        {\n            provide: HTTP_INTERCEPTOR_FNS,\n            useExisting: LEGACY_INTERCEPTOR_FN,\n            multi: true,\n        },\n    ]);\n}\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withXsrfConfiguration({ cookieName, headerName, }) {\n    const providers = [];\n    if (cookieName !== undefined) {\n        providers.push({ provide: XSRF_COOKIE_NAME, useValue: cookieName });\n    }\n    if (headerName !== undefined) {\n        providers.push({ provide: XSRF_HEADER_NAME, useValue: headerName });\n    }\n    return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nfunction withNoXsrfProtection() {\n    return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [\n        {\n            provide: XSRF_ENABLED,\n            useValue: false,\n        },\n    ]);\n}\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nfunction withJsonpSupport() {\n    return makeHttpFeature(HttpFeatureKind.JsonpSupport, [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTOR_FNS, useValue: jsonpInterceptorFn, multi: true },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @publicApi\n */\nfunction withRequestsMadeViaParent() {\n    return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [\n        {\n            provide: HttpBackend,\n            useFactory: () => {\n                const handlerFromParent = inject(HttpHandler, { skipSelf: true, optional: true });\n                if (ngDevMode && handlerFromParent === null) {\n                    throw new Error('withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient');\n                }\n                return handlerFromParent;\n            },\n        },\n    ]);\n}\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nfunction withFetch() {\n    return makeHttpFeature(HttpFeatureKind.Fetch, [\n        FetchBackend,\n        { provide: FETCH_BACKEND, useExisting: FetchBackend },\n        { provide: HttpBackend, useExisting: FetchBackend },\n    ]);\n}\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [withNoXsrfProtection().ɵproviders],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: withXsrfConfiguration(options).ɵproviders,\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientXsrfModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n            HttpXsrfInterceptor,\n            { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n            { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n            withXsrfConfiguration({\n                cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                headerName: XSRF_DEFAULT_HEADER_NAME,\n            }).ɵproviders,\n            { provide: XSRF_ENABLED, useValue: true },\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        withXsrfConfiguration({\n                            cookieName: XSRF_DEFAULT_COOKIE_NAME,\n                            headerName: XSRF_DEFAULT_HEADER_NAME,\n                        }).ɵproviders,\n                        { provide: XSRF_ENABLED, useValue: true },\n                    ],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\nclass HttpClientModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientModule, providers: [provideHttpClient(withInterceptorsFromDi())] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Configures the dependency injector where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [provideHttpClient(withInterceptorsFromDi())],\n                }]\n        }] });\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\nclass HttpClientJsonpModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientJsonpModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientJsonpModule, providers: [withJsonpSupport().ɵproviders] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [withJsonpSupport().ɵproviders],\n                }]\n        }] });\n\nexport { FetchBackend, HTTP_INTERCEPTORS, HTTP_ROOT_INTERCEPTOR_FNS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpFeatureKind, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpInterceptorHandler, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpStatusCode, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, REQUESTS_CONTRIBUTE_TO_STABILITY, provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,aAAa,IAAIC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAEC,qBAAqB,IAAIC,qBAAqB,EAAEC,WAAW,EAAEC,QAAQ,IAAIC,QAAQ,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,qBAAqB,EAAEC,MAAM,EAAEC,wBAAwB,EAAEC,QAAQ,QAAQ,eAAe;AAC5S,SAASC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAC5E,SAASC,EAAE,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AAC3C,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,oBAAoB;AACnF,SAASC,QAAQ,QAAQ,2BAA2B;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;AACJ;AACA;EACIC,QAAQ;EACR;AACJ;AACA;EACIC,UAAU,GAAG,IAAI;EACjB;EACAC,WAAWA,CAACL,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,EAAE;MACV,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;IAC5B,CAAC,MACI,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACG,QAAQ,GAAG,MAAM;QAClB,IAAI,CAACH,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBF,OAAO,CAACM,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,IAAI,IAAK;UAClC,MAAMC,KAAK,GAAGD,IAAI,CAACE,OAAO,CAAC,GAAG,CAAC;UAC/B,IAAID,KAAK,GAAG,CAAC,EAAE;YACX,MAAME,IAAI,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;YACjC,MAAMI,KAAK,GAAGL,IAAI,CAACI,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;YAC1C,IAAI,CAACC,cAAc,CAACJ,IAAI,EAAEE,KAAK,CAAC;UACpC;QACJ,CAAC,CAAC;MACN,CAAC;IACL,CAAC,MACI,IAAI,OAAOG,OAAO,KAAK,WAAW,IAAIhB,OAAO,YAAYgB,OAAO,EAAE;MACnE,IAAI,CAAChB,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;MACxBF,OAAO,CAACO,OAAO,CAAC,CAACM,KAAK,EAAEF,IAAI,KAAK;QAC7B,IAAI,CAACI,cAAc,CAACJ,IAAI,EAAEE,KAAK,CAAC;MACpC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACV,QAAQ,GAAG,MAAM;QAClB,IAAI,OAAOc,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/CC,kBAAkB,CAAClB,OAAO,CAAC;QAC/B;QACA,IAAI,CAACA,OAAO,GAAG,IAAIE,GAAG,CAAC,CAAC;QACxBiB,MAAM,CAACC,OAAO,CAACpB,OAAO,CAAC,CAACO,OAAO,CAAC,CAAC,CAACI,IAAI,EAAEU,MAAM,CAAC,KAAK;UAChD,IAAI,CAACC,gBAAgB,CAACX,IAAI,EAAEU,MAAM,CAAC;QACvC,CAAC,CAAC;MACN,CAAC;IACL;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,GAAGA,CAACZ,IAAI,EAAE;IACN,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACxB,OAAO,CAACuB,GAAG,CAACZ,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACf,IAAI,EAAE;IACN,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,MAAMH,MAAM,GAAG,IAAI,CAACrB,OAAO,CAAC0B,GAAG,CAACf,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC;IACnD,OAAOJ,MAAM,IAAIA,MAAM,CAACM,MAAM,GAAG,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;EACzD;EACA;AACJ;AACA;AACA;AACA;EACIO,IAAIA,CAAA,EAAG;IACH,IAAI,CAACJ,IAAI,CAAC,CAAC;IACX,OAAOK,KAAK,CAACrC,IAAI,CAAC,IAAI,CAACS,eAAe,CAACoB,MAAM,CAAC,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIS,MAAMA,CAACnB,IAAI,EAAE;IACT,IAAI,CAACa,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACxB,OAAO,CAAC0B,GAAG,CAACf,IAAI,CAACc,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,MAAMA,CAACpB,IAAI,EAAEE,KAAK,EAAE;IAChB,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAErB,IAAI;MAAEE,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACvB,IAAI,EAAEE,KAAK,EAAE;IACb,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAErB,IAAI;MAAEE,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACxB,IAAI,EAAEE,KAAK,EAAE;IAChB,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAErB,IAAI;MAAEE,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAC/C;EACAG,sBAAsBA,CAACzB,IAAI,EAAE0B,MAAM,EAAE;IACjC,IAAI,CAAC,IAAI,CAACpC,eAAe,CAACsB,GAAG,CAACc,MAAM,CAAC,EAAE;MACnC,IAAI,CAACpC,eAAe,CAACiC,GAAG,CAACG,MAAM,EAAE1B,IAAI,CAAC;IAC1C;EACJ;EACAa,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,CAAC,IAAI,CAACrB,QAAQ,EAAE;MACjB,IAAI,IAAI,CAACA,QAAQ,YAAYJ,WAAW,EAAE;QACtC,IAAI,CAACuC,QAAQ,CAAC,IAAI,CAACnC,QAAQ,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACA,QAAQ,CAAC,CAAC;MACnB;MACA,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC,CAAC,IAAI,CAACC,UAAU,EAAE;QACnB,IAAI,CAACA,UAAU,CAACG,OAAO,CAAEgC,MAAM,IAAK,IAAI,CAACC,WAAW,CAACD,MAAM,CAAC,CAAC;QAC7D,IAAI,CAACnC,UAAU,GAAG,IAAI;MAC1B;IACJ;EACJ;EACAkC,QAAQA,CAACG,KAAK,EAAE;IACZA,KAAK,CAACjB,IAAI,CAAC,CAAC;IACZK,KAAK,CAACrC,IAAI,CAACiD,KAAK,CAACzC,OAAO,CAAC4B,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAEmC,GAAG,IAAK;MAC9C,IAAI,CAAC1C,OAAO,CAACkC,GAAG,CAACQ,GAAG,EAAED,KAAK,CAACzC,OAAO,CAAC0B,GAAG,CAACgB,GAAG,CAAC,CAAC;MAC7C,IAAI,CAACzC,eAAe,CAACiC,GAAG,CAACQ,GAAG,EAAED,KAAK,CAACxC,eAAe,CAACyB,GAAG,CAACgB,GAAG,CAAC,CAAC;IACjE,CAAC,CAAC;EACN;EACAV,KAAKA,CAACO,MAAM,EAAE;IACV,MAAMP,KAAK,GAAG,IAAIjC,WAAW,CAAC,CAAC;IAC/BiC,KAAK,CAAC7B,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACA,QAAQ,IAAI,IAAI,CAACA,QAAQ,YAAYJ,WAAW,GAAG,IAAI,CAACI,QAAQ,GAAG,IAAI;IAC/F6B,KAAK,CAAC5B,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,IAAI,EAAE,EAAEuC,MAAM,CAAC,CAACJ,MAAM,CAAC,CAAC;IAC3D,OAAOP,KAAK;EAChB;EACAQ,WAAWA,CAACD,MAAM,EAAE;IAChB,MAAMG,GAAG,GAAGH,MAAM,CAAC5B,IAAI,CAACc,WAAW,CAAC,CAAC;IACrC,QAAQc,MAAM,CAACN,EAAE;MACb,KAAK,GAAG;MACR,KAAK,GAAG;QACJ,IAAIpB,KAAK,GAAG0B,MAAM,CAAC1B,KAAK;QACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3BA,KAAK,GAAG,CAACA,KAAK,CAAC;QACnB;QACA,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;UACpB;QACJ;QACA,IAAI,CAACS,sBAAsB,CAACG,MAAM,CAAC5B,IAAI,EAAE+B,GAAG,CAAC;QAC7C,MAAME,IAAI,GAAG,CAACL,MAAM,CAACN,EAAE,KAAK,GAAG,GAAG,IAAI,CAACjC,OAAO,CAAC0B,GAAG,CAACgB,GAAG,CAAC,GAAGG,SAAS,KAAK,EAAE;QAC1ED,IAAI,CAACE,IAAI,CAAC,GAAGjC,KAAK,CAAC;QACnB,IAAI,CAACb,OAAO,CAACkC,GAAG,CAACQ,GAAG,EAAEE,IAAI,CAAC;QAC3B;MACJ,KAAK,GAAG;QACJ,MAAMG,QAAQ,GAAGR,MAAM,CAAC1B,KAAK;QAC7B,IAAI,CAACkC,QAAQ,EAAE;UACX,IAAI,CAAC/C,OAAO,CAACmC,MAAM,CAACO,GAAG,CAAC;UACxB,IAAI,CAACzC,eAAe,CAACkC,MAAM,CAACO,GAAG,CAAC;QACpC,CAAC,MACI;UACD,IAAIM,QAAQ,GAAG,IAAI,CAAChD,OAAO,CAAC0B,GAAG,CAACgB,GAAG,CAAC;UACpC,IAAI,CAACM,QAAQ,EAAE;YACX;UACJ;UACAA,QAAQ,GAAGA,QAAQ,CAAC9D,MAAM,CAAE2B,KAAK,IAAKkC,QAAQ,CAACrC,OAAO,CAACG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;UACrE,IAAImC,QAAQ,CAACrB,MAAM,KAAK,CAAC,EAAE;YACvB,IAAI,CAAC3B,OAAO,CAACmC,MAAM,CAACO,GAAG,CAAC;YACxB,IAAI,CAACzC,eAAe,CAACkC,MAAM,CAACO,GAAG,CAAC;UACpC,CAAC,MACI;YACD,IAAI,CAAC1C,OAAO,CAACkC,GAAG,CAACQ,GAAG,EAAEM,QAAQ,CAAC;UACnC;QACJ;QACA;IACR;EACJ;EACAjC,cAAcA,CAACJ,IAAI,EAAEE,KAAK,EAAE;IACxB,MAAM6B,GAAG,GAAG/B,IAAI,CAACc,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACW,sBAAsB,CAACzB,IAAI,EAAE+B,GAAG,CAAC;IACtC,IAAI,IAAI,CAAC1C,OAAO,CAACuB,GAAG,CAACmB,GAAG,CAAC,EAAE;MACvB,IAAI,CAAC1C,OAAO,CAAC0B,GAAG,CAACgB,GAAG,CAAC,CAACI,IAAI,CAACjC,KAAK,CAAC;IACrC,CAAC,MACI;MACD,IAAI,CAACb,OAAO,CAACkC,GAAG,CAACQ,GAAG,EAAE,CAAC7B,KAAK,CAAC,CAAC;IAClC;EACJ;EACAS,gBAAgBA,CAACX,IAAI,EAAEU,MAAM,EAAE;IAC3B,MAAM4B,YAAY,GAAG,CAACpB,KAAK,CAACqB,OAAO,CAAC7B,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC,EAAElC,GAAG,CAAE0B,KAAK,IAAKA,KAAK,CAACsC,QAAQ,CAAC,CAAC,CAAC;IACjG,MAAMT,GAAG,GAAG/B,IAAI,CAACc,WAAW,CAAC,CAAC;IAC9B,IAAI,CAACzB,OAAO,CAACkC,GAAG,CAACQ,GAAG,EAAEO,YAAY,CAAC;IACnC,IAAI,CAACb,sBAAsB,CAACzB,IAAI,EAAE+B,GAAG,CAAC;EAC1C;EACA;AACJ;AACA;EACInC,OAAOA,CAAC6C,EAAE,EAAE;IACR,IAAI,CAAC5B,IAAI,CAAC,CAAC;IACXK,KAAK,CAACrC,IAAI,CAAC,IAAI,CAACS,eAAe,CAAC2B,IAAI,CAAC,CAAC,CAAC,CAACrB,OAAO,CAAEmC,GAAG,IAAKU,EAAE,CAAC,IAAI,CAACnD,eAAe,CAACyB,GAAG,CAACgB,GAAG,CAAC,EAAE,IAAI,CAAC1C,OAAO,CAAC0B,GAAG,CAACgB,GAAG,CAAC,CAAC,CAAC;EACtH;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,kBAAkBA,CAAClB,OAAO,EAAE;EACjC,KAAK,MAAM,CAAC0C,GAAG,EAAE7B,KAAK,CAAC,IAAIM,MAAM,CAACC,OAAO,CAACpB,OAAO,CAAC,EAAE;IAChD,IAAI,EAAE,OAAOa,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC,IAAI,CAACgB,KAAK,CAACqB,OAAO,CAACrC,KAAK,CAAC,EAAE;MACpF,MAAM,IAAIwC,KAAK,CAAC,6BAA6BX,GAAG,sBAAsB,GAClE,+DAA+D7B,KAAK,KAAK,CAAC;IAClF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyC,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACb,GAAG,EAAE;IACX,OAAOc,gBAAgB,CAACd,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIe,WAAWA,CAAC5C,KAAK,EAAE;IACf,OAAO2C,gBAAgB,CAAC3C,KAAK,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACI6C,SAASA,CAAChB,GAAG,EAAE;IACX,OAAOiB,kBAAkB,CAACjB,GAAG,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIkB,WAAWA,CAAC/C,KAAK,EAAE;IACf,OAAO8C,kBAAkB,CAAC9C,KAAK,CAAC;EACpC;AACJ;AACA,SAASgD,WAAWA,CAACC,SAAS,EAAEC,KAAK,EAAE;EACnC,MAAM5E,GAAG,GAAG,IAAIe,GAAG,CAAC,CAAC;EACrB,IAAI4D,SAAS,CAACnC,MAAM,GAAG,CAAC,EAAE;IACtB;IACA;IACA;IACA,MAAMqC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC3D,KAAK,CAAC,GAAG,CAAC;IACtD0D,MAAM,CAACzD,OAAO,CAAE2D,KAAK,IAAK;MACtB,MAAMC,KAAK,GAAGD,KAAK,CAACxD,OAAO,CAAC,GAAG,CAAC;MAChC,MAAM,CAACgC,GAAG,EAAE0B,GAAG,CAAC,GAAGD,KAAK,IAAI,CAAC,CAAC,GACxB,CAACJ,KAAK,CAACL,SAAS,CAACQ,KAAK,CAAC,EAAE,EAAE,CAAC,GAC5B,CAACH,KAAK,CAACL,SAAS,CAACQ,KAAK,CAACtD,KAAK,CAAC,CAAC,EAAEuD,KAAK,CAAC,CAAC,EAAEJ,KAAK,CAACH,WAAW,CAACM,KAAK,CAACtD,KAAK,CAACuD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;MACzF,MAAME,IAAI,GAAGlF,GAAG,CAACuC,GAAG,CAACgB,GAAG,CAAC,IAAI,EAAE;MAC/B2B,IAAI,CAACvB,IAAI,CAACsB,GAAG,CAAC;MACdjF,GAAG,CAAC+C,GAAG,CAACQ,GAAG,EAAE2B,IAAI,CAAC;IACtB,CAAC,CAAC;EACN;EACA,OAAOlF,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMmF,uBAAuB,GAAG,iBAAiB;AACjD,MAAMC,8BAA8B,GAAG;EACnC,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE,GAAG;EACT,IAAI,EAAE;AACV,CAAC;AACD,SAASf,gBAAgBA,CAACgB,CAAC,EAAE;EACzB,OAAOC,kBAAkB,CAACD,CAAC,CAAC,CAACP,OAAO,CAACK,uBAAuB,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKJ,8BAA8B,CAACI,CAAC,CAAC,IAAID,CAAC,CAAC;AACnH;AACA,SAASE,aAAaA,CAAC/D,KAAK,EAAE;EAC1B,OAAO,GAAGA,KAAK,EAAE;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgE,UAAU,CAAC;EACb1F,GAAG;EACH2F,OAAO;EACPC,OAAO,GAAG,IAAI;EACdC,SAAS,GAAG,IAAI;EAChB3E,WAAWA,CAAC4E,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAACH,OAAO,GAAGG,OAAO,CAACH,OAAO,IAAI,IAAIxB,oBAAoB,CAAC,CAAC;IAC5D,IAAI2B,OAAO,CAACC,UAAU,EAAE;MACpB,IAAID,OAAO,CAACE,UAAU,EAAE;QACpB,MAAM,IAAIlH,aAAa,CAAC,IAAI,CAAC,wEAAwEgD,SAAS,IAAI,gDAAgD,CAAC;MACvK;MACA,IAAI,CAAC9B,GAAG,GAAG0E,WAAW,CAACoB,OAAO,CAACC,UAAU,EAAE,IAAI,CAACJ,OAAO,CAAC;IAC5D,CAAC,MACI,IAAI,CAAC,CAACG,OAAO,CAACE,UAAU,EAAE;MAC3B,IAAI,CAAChG,GAAG,GAAG,IAAIe,GAAG,CAAC,CAAC;MACpBiB,MAAM,CAACS,IAAI,CAACqD,OAAO,CAACE,UAAU,CAAC,CAAC5E,OAAO,CAAEmC,GAAG,IAAK;QAC7C,MAAM7B,KAAK,GAAGoE,OAAO,CAACE,UAAU,CAACzC,GAAG,CAAC;QACrC;QACA,MAAMrB,MAAM,GAAGQ,KAAK,CAACqB,OAAO,CAACrC,KAAK,CAAC,GAAGA,KAAK,CAAC1B,GAAG,CAACyF,aAAa,CAAC,GAAG,CAACA,aAAa,CAAC/D,KAAK,CAAC,CAAC;QACvF,IAAI,CAAC1B,GAAG,CAAC+C,GAAG,CAACQ,GAAG,EAAErB,MAAM,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAClC,GAAG,GAAG,IAAI;IACnB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoC,GAAGA,CAAC2C,KAAK,EAAE;IACP,IAAI,CAAC1C,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACrC,GAAG,CAACoC,GAAG,CAAC2C,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIxC,GAAGA,CAACwC,KAAK,EAAE;IACP,IAAI,CAAC1C,IAAI,CAAC,CAAC;IACX,MAAM4D,GAAG,GAAG,IAAI,CAACjG,GAAG,CAACuC,GAAG,CAACwC,KAAK,CAAC;IAC/B,OAAO,CAAC,CAACkB,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;EACItD,MAAMA,CAACoC,KAAK,EAAE;IACV,IAAI,CAAC1C,IAAI,CAAC,CAAC;IACX,OAAO,IAAI,CAACrC,GAAG,CAACuC,GAAG,CAACwC,KAAK,CAAC,IAAI,IAAI;EACtC;EACA;AACJ;AACA;AACA;EACItC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACJ,IAAI,CAAC,CAAC;IACX,OAAOK,KAAK,CAACrC,IAAI,CAAC,IAAI,CAACL,GAAG,CAACyC,IAAI,CAAC,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAACmC,KAAK,EAAErD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAEkC,KAAK;MAAErD,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIoD,SAASA,CAACrB,MAAM,EAAE;IACd,MAAMe,OAAO,GAAG,EAAE;IAClB5D,MAAM,CAACS,IAAI,CAACoC,MAAM,CAAC,CAACzD,OAAO,CAAE2D,KAAK,IAAK;MACnC,MAAMrD,KAAK,GAAGmD,MAAM,CAACE,KAAK,CAAC;MAC3B,IAAIrC,KAAK,CAACqB,OAAO,CAACrC,KAAK,CAAC,EAAE;QACtBA,KAAK,CAACN,OAAO,CAAE+E,MAAM,IAAK;UACtBP,OAAO,CAACjC,IAAI,CAAC;YAAEoB,KAAK;YAAErD,KAAK,EAAEyE,MAAM;YAAErD,EAAE,EAAE;UAAI,CAAC,CAAC;QACnD,CAAC,CAAC;MACN,CAAC,MACI;QACD8C,OAAO,CAACjC,IAAI,CAAC;UAAEoB,KAAK;UAAErD,KAAK,EAAEA,KAAK;UAAEoB,EAAE,EAAE;QAAI,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACD,KAAK,CAAC+C,OAAO,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI7C,GAAGA,CAACgC,KAAK,EAAErD,KAAK,EAAE;IACd,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAEkC,KAAK;MAAErD,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAAC+B,KAAK,EAAErD,KAAK,EAAE;IACjB,OAAO,IAAI,CAACmB,KAAK,CAAC;MAAEkC,KAAK;MAAErD,KAAK;MAAEoB,EAAE,EAAE;IAAI,CAAC,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIkB,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3B,IAAI,CAAC,CAAC;IACX,OAAQ,IAAI,CAACI,IAAI,CAAC,CAAC,CACdzC,GAAG,CAAEuD,GAAG,IAAK;MACd,MAAM6C,IAAI,GAAG,IAAI,CAACT,OAAO,CAACvB,SAAS,CAACb,GAAG,CAAC;MACxC;MACA;MACA;MACA,OAAO,IAAI,CAACvD,GAAG,CAACuC,GAAG,CAACgB,GAAG,CAAC,CACnBvD,GAAG,CAAE0B,KAAK,IAAK0E,IAAI,GAAG,GAAG,GAAG,IAAI,CAACT,OAAO,CAACrB,WAAW,CAAC5C,KAAK,CAAC,CAAC,CAC5D2E,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IACG;IACA;IAAA,CACCtG,MAAM,CAAEgF,KAAK,IAAKA,KAAK,KAAK,EAAE,CAAC,CAC/BsB,IAAI,CAAC,GAAG,CAAC;EAClB;EACAxD,KAAKA,CAACO,MAAM,EAAE;IACV,MAAMP,KAAK,GAAG,IAAI6C,UAAU,CAAC;MAAEC,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,CAAC;IACvD9C,KAAK,CAACgD,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI;IACxChD,KAAK,CAAC+C,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,IAAI,EAAE,EAAEpC,MAAM,CAACJ,MAAM,CAAC;IACnD,OAAOP,KAAK;EAChB;EACAR,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACrC,GAAG,KAAK,IAAI,EAAE;MACnB,IAAI,CAACA,GAAG,GAAG,IAAIe,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,IAAI,CAAC8E,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACA,SAAS,CAACxD,IAAI,CAAC,CAAC;MACrB,IAAI,CAACwD,SAAS,CAACpD,IAAI,CAAC,CAAC,CAACrB,OAAO,CAAEmC,GAAG,IAAK,IAAI,CAACvD,GAAG,CAAC+C,GAAG,CAACQ,GAAG,EAAE,IAAI,CAACsC,SAAS,CAAC7F,GAAG,CAACuC,GAAG,CAACgB,GAAG,CAAC,CAAC,CAAC;MACtF,IAAI,CAACqC,OAAO,CAACxE,OAAO,CAAEgC,MAAM,IAAK;QAC7B,QAAQA,MAAM,CAACN,EAAE;UACb,KAAK,GAAG;UACR,KAAK,GAAG;YACJ,MAAMW,IAAI,GAAG,CAACL,MAAM,CAACN,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC9C,GAAG,CAACuC,GAAG,CAACa,MAAM,CAAC2B,KAAK,CAAC,GAAGrB,SAAS,KAAK,EAAE;YAC/ED,IAAI,CAACE,IAAI,CAAC8B,aAAa,CAACrC,MAAM,CAAC1B,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC1B,GAAG,CAAC+C,GAAG,CAACK,MAAM,CAAC2B,KAAK,EAAEtB,IAAI,CAAC;YAChC;UACJ,KAAK,GAAG;YACJ,IAAIL,MAAM,CAAC1B,KAAK,KAAKgC,SAAS,EAAE;cAC5B,IAAID,IAAI,GAAG,IAAI,CAACzD,GAAG,CAACuC,GAAG,CAACa,MAAM,CAAC2B,KAAK,CAAC,IAAI,EAAE;cAC3C,MAAMuB,GAAG,GAAG7C,IAAI,CAAClC,OAAO,CAACkE,aAAa,CAACrC,MAAM,CAAC1B,KAAK,CAAC,CAAC;cACrD,IAAI4E,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ7C,IAAI,CAAC8C,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;cACvB;cACA,IAAI7C,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,CAACxC,GAAG,CAAC+C,GAAG,CAACK,MAAM,CAAC2B,KAAK,EAAEtB,IAAI,CAAC;cACpC,CAAC,MACI;gBACD,IAAI,CAACzD,GAAG,CAACgD,MAAM,CAACI,MAAM,CAAC2B,KAAK,CAAC;cACjC;YACJ,CAAC,MACI;cACD,IAAI,CAAC/E,GAAG,CAACgD,MAAM,CAACI,MAAM,CAAC2B,KAAK,CAAC;cAC7B;YACJ;QACR;MACJ,CAAC,CAAC;MACF,IAAI,CAACc,SAAS,GAAG,IAAI,CAACD,OAAO,GAAG,IAAI;IACxC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMY,gBAAgB,CAAC;EACnBC,YAAY;EACZvF,WAAWA,CAACuF,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACd1G,GAAG,GAAG,IAAIe,GAAG,CAAC,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIgC,GAAGA,CAAC4D,KAAK,EAAEjF,KAAK,EAAE;IACd,IAAI,CAAC1B,GAAG,CAAC+C,GAAG,CAAC4D,KAAK,EAAEjF,KAAK,CAAC;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIa,GAAGA,CAACoE,KAAK,EAAE;IACP,IAAI,CAAC,IAAI,CAAC3G,GAAG,CAACoC,GAAG,CAACuE,KAAK,CAAC,EAAE;MACtB,IAAI,CAAC3G,GAAG,CAAC+C,GAAG,CAAC4D,KAAK,EAAEA,KAAK,CAACF,YAAY,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAI,CAACzG,GAAG,CAACuC,GAAG,CAACoE,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3D,MAAMA,CAAC2D,KAAK,EAAE;IACV,IAAI,CAAC3G,GAAG,CAACgD,MAAM,CAAC2D,KAAK,CAAC;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvE,GAAGA,CAACuE,KAAK,EAAE;IACP,OAAO,IAAI,CAAC3G,GAAG,CAACoC,GAAG,CAACuE,KAAK,CAAC;EAC9B;EACA;AACJ;AACA;EACIlE,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACzC,GAAG,CAACyC,IAAI,CAAC,CAAC;EAC1B;AACJ;;AAEA;AACA;AACA;AACA,SAASmE,aAAaA,CAACC,MAAM,EAAE;EAC3B,QAAQA,MAAM;IACV,KAAK,QAAQ;IACb,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,SAAS;IACd,KAAK,OAAO;MACR,OAAO,KAAK;IAChB;MACI,OAAO,IAAI;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACpF,KAAK,EAAE;EAC1B,OAAO,OAAOqF,WAAW,KAAK,WAAW,IAAIrF,KAAK,YAAYqF,WAAW;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACtF,KAAK,EAAE;EACnB,OAAO,OAAOuF,IAAI,KAAK,WAAW,IAAIvF,KAAK,YAAYuF,IAAI;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACxF,KAAK,EAAE;EACvB,OAAO,OAAOyF,QAAQ,KAAK,WAAW,IAAIzF,KAAK,YAAYyF,QAAQ;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAC1F,KAAK,EAAE;EAC9B,OAAO,OAAO2F,eAAe,KAAK,WAAW,IAAI3F,KAAK,YAAY2F,eAAe;AACrF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,cAAc;AAC1C;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,QAAQ;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,eAAe;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,YAAY;AACtC;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAGD,iBAAiB,KAAKD,iBAAiB,OAAO;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,CAAC;EACdC,GAAG;EACH;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,IAAI,GAAG,IAAI;EACX;AACJ;AACA;EACIjH,OAAO;EACP;AACJ;AACA;EACIkH,OAAO;EACP;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAc,GAAG,KAAK;EACtB;AACJ;AACA;EACIC,eAAe,GAAG,KAAK;EACvB;AACJ;AACA;AACA;AACA;AACA;EACIC,YAAY,GAAG,MAAM;EACrB;AACJ;AACA;EACIrB,MAAM;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhC,MAAM;EACN;AACJ;AACA;EACIsD,aAAa;EACb;AACJ;AACA;EACIC,aAAa;EACblH,WAAWA,CAAC2F,MAAM,EAAEgB,GAAG,EAAEQ,KAAK,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACT,GAAG,GAAGA,GAAG;IACd,IAAI,CAAChB,MAAM,GAAGA,MAAM,CAAC0B,WAAW,CAAC,CAAC;IAClC;IACA;IACA,IAAIzC,OAAO;IACX;IACA;IACA,IAAIc,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAAC,CAACyB,MAAM,EAAE;MACxC;MACA,IAAI,CAACR,IAAI,GAAGO,KAAK,KAAK3E,SAAS,GAAG2E,KAAK,GAAG,IAAI;MAC9CvC,OAAO,GAAGwC,MAAM;IACpB,CAAC,MACI;MACD;MACAxC,OAAO,GAAGuC,KAAK;IACnB;IACA;IACA,IAAIvC,OAAO,EAAE;MACT;MACA,IAAI,CAACkC,cAAc,GAAG,CAAC,CAAClC,OAAO,CAACkC,cAAc;MAC9C,IAAI,CAACC,eAAe,GAAG,CAAC,CAACnC,OAAO,CAACmC,eAAe;MAChD;MACA,IAAI,CAAC,CAACnC,OAAO,CAACoC,YAAY,EAAE;QACxB,IAAI,CAACA,YAAY,GAAGpC,OAAO,CAACoC,YAAY;MAC5C;MACA;MACA,IAAI,CAAC,CAACpC,OAAO,CAACjF,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGiF,OAAO,CAACjF,OAAO;MAClC;MACA,IAAI,CAAC,CAACiF,OAAO,CAACiC,OAAO,EAAE;QACnB,IAAI,CAACA,OAAO,GAAGjC,OAAO,CAACiC,OAAO;MAClC;MACA,IAAI,CAAC,CAACjC,OAAO,CAACjB,MAAM,EAAE;QAClB,IAAI,CAACA,MAAM,GAAGiB,OAAO,CAACjB,MAAM;MAChC;MACA;MACA,IAAI,CAACuD,aAAa,GAAGtC,OAAO,CAACsC,aAAa;IAC9C;IACA;IACA,IAAI,CAACvH,OAAO,KAAK,IAAID,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,CAACmH,OAAO,KAAK,IAAIrB,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,CAAC,IAAI,CAAC7B,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAIa,UAAU,CAAC,CAAC;MAC9B,IAAI,CAACyC,aAAa,GAAGN,GAAG;IAC5B,CAAC,MACI;MACD;MACA,MAAMhD,MAAM,GAAG,IAAI,CAACA,MAAM,CAACb,QAAQ,CAAC,CAAC;MACrC,IAAIa,MAAM,CAACrC,MAAM,KAAK,CAAC,EAAE;QACrB;QACA,IAAI,CAAC2F,aAAa,GAAGN,GAAG;MAC5B,CAAC,MACI;QACD;QACA,MAAMW,IAAI,GAAGX,GAAG,CAACtG,OAAO,CAAC,GAAG,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMkH,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,GAAGX,GAAG,CAACrF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;QAChE,IAAI,CAAC2F,aAAa,GAAGN,GAAG,GAAGY,GAAG,GAAG5D,MAAM;MAC3C;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI6D,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACZ,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7BhB,aAAa,CAAC,IAAI,CAACgB,IAAI,CAAC,IACxBd,MAAM,CAAC,IAAI,CAACc,IAAI,CAAC,IACjBZ,UAAU,CAAC,IAAI,CAACY,IAAI,CAAC,IACrBV,iBAAiB,CAAC,IAAI,CAACU,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACA,IAAI;IACpB;IACA;IACA,IAAI,IAAI,CAACA,IAAI,YAAYpC,UAAU,EAAE;MACjC,OAAO,IAAI,CAACoC,IAAI,CAAC9D,QAAQ,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,OAAO,IAAI,CAAC8D,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,IAC9BpF,KAAK,CAACqB,OAAO,CAAC,IAAI,CAAC+D,IAAI,CAAC,EAAE;MAC1B,OAAOa,IAAI,CAACC,SAAS,CAAC,IAAI,CAACd,IAAI,CAAC;IACpC;IACA;IACA,OAAO,IAAI,CAACA,IAAI,CAAC9D,QAAQ,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6E,uBAAuBA,CAAA,EAAG;IACtB;IACA,IAAI,IAAI,CAACf,IAAI,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA;IACA,IAAIZ,UAAU,CAAC,IAAI,CAACY,IAAI,CAAC,EAAE;MACvB,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAId,MAAM,CAAC,IAAI,CAACc,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI,CAACA,IAAI,CAACgB,IAAI,IAAI,IAAI;IACjC;IACA;IACA,IAAIhC,aAAa,CAAC,IAAI,CAACgB,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACf;IACA;IACA;IACA,IAAI,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;MAC/B,OAAOL,iBAAiB;IAC5B;IACA;IACA,IAAI,IAAI,CAACK,IAAI,YAAYpC,UAAU,EAAE;MACjC,OAAO,iDAAiD;IAC5D;IACA;IACA,IAAI,OAAO,IAAI,CAACoC,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,QAAQ,IAC7B,OAAO,IAAI,CAACA,IAAI,KAAK,SAAS,EAAE;MAChC,OAAOJ,iBAAiB;IAC5B;IACA;IACA,OAAO,IAAI;EACf;EACA7E,KAAKA,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,MAAMyD,MAAM,GAAGzD,MAAM,CAACyD,MAAM,IAAI,IAAI,CAACA,MAAM;IAC3C,MAAMgB,GAAG,GAAGzE,MAAM,CAACyE,GAAG,IAAI,IAAI,CAACA,GAAG;IAClC,MAAMK,YAAY,GAAG9E,MAAM,CAAC8E,YAAY,IAAI,IAAI,CAACA,YAAY;IAC7D;IACA;IACA,MAAME,aAAa,GAAGhF,MAAM,CAACgF,aAAa,IAAI,IAAI,CAACA,aAAa;IAChE;IACA;IACA;IACA;IACA,MAAMN,IAAI,GAAG1E,MAAM,CAAC0E,IAAI,KAAKpE,SAAS,GAAGN,MAAM,CAAC0E,IAAI,GAAG,IAAI,CAACA,IAAI;IAChE;IACA;IACA,MAAMG,eAAe,GAAG7E,MAAM,CAAC6E,eAAe,IAAI,IAAI,CAACA,eAAe;IACtE,MAAMD,cAAc,GAAG5E,MAAM,CAAC4E,cAAc,IAAI,IAAI,CAACA,cAAc;IACnE;IACA;IACA,IAAInH,OAAO,GAAGuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;IAC5C,IAAIgE,MAAM,GAAGzB,MAAM,CAACyB,MAAM,IAAI,IAAI,CAACA,MAAM;IACzC;IACA,MAAMkD,OAAO,GAAG3E,MAAM,CAAC2E,OAAO,IAAI,IAAI,CAACA,OAAO;IAC9C;IACA,IAAI3E,MAAM,CAAC2F,UAAU,KAAKrF,SAAS,EAAE;MACjC;MACA7C,OAAO,GAAGmB,MAAM,CAACS,IAAI,CAACW,MAAM,CAAC2F,UAAU,CAAC,CAACC,MAAM,CAAC,CAACnI,OAAO,EAAEW,IAAI,KAAKX,OAAO,CAACkC,GAAG,CAACvB,IAAI,EAAE4B,MAAM,CAAC2F,UAAU,CAACvH,IAAI,CAAC,CAAC,EAAEX,OAAO,CAAC;IAC3H;IACA;IACA,IAAIuC,MAAM,CAAC6F,SAAS,EAAE;MAClB;MACApE,MAAM,GAAG7C,MAAM,CAACS,IAAI,CAACW,MAAM,CAAC6F,SAAS,CAAC,CAACD,MAAM,CAAC,CAACnE,MAAM,EAAEE,KAAK,KAAKF,MAAM,CAAC9B,GAAG,CAACgC,KAAK,EAAE3B,MAAM,CAAC6F,SAAS,CAAClE,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;IACxH;IACA;IACA,OAAO,IAAI+C,WAAW,CAACf,MAAM,EAAEgB,GAAG,EAAEC,IAAI,EAAE;MACtCjD,MAAM;MACNhE,OAAO;MACPkH,OAAO;MACPC,cAAc;MACdE,YAAY;MACZD,eAAe;MACfG;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIc,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjD;AACJ;AACA;AACA;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACrE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACzE;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;AACJ;AACA;EACIA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACrD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;AACJ;AACA;EACItI,OAAO;EACP;AACJ;AACA;EACIuI,MAAM;EACN;AACJ;AACA;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;EACIxB,GAAG;EACH;AACJ;AACA;EACIyB,EAAE;EACF;AACJ;AACA;EACIR,IAAI;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI5H,WAAWA,CAACmB,IAAI,EAAEkH,aAAa,GAAG,GAAG,EAAEC,iBAAiB,GAAG,IAAI,EAAE;IAC7D;IACA;IACA,IAAI,CAAC3I,OAAO,GAAGwB,IAAI,CAACxB,OAAO,IAAI,IAAID,WAAW,CAAC,CAAC;IAChD,IAAI,CAACwI,MAAM,GAAG/G,IAAI,CAAC+G,MAAM,KAAK1F,SAAS,GAAGrB,IAAI,CAAC+G,MAAM,GAAGG,aAAa;IACrE,IAAI,CAACF,UAAU,GAAGhH,IAAI,CAACgH,UAAU,IAAIG,iBAAiB;IACtD,IAAI,CAAC3B,GAAG,GAAGxF,IAAI,CAACwF,GAAG,IAAI,IAAI;IAC3B;IACA,IAAI,CAACyB,EAAE,GAAG,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;EACrD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,kBAAkB,SAASN,gBAAgB,CAAC;EAC9C;AACJ;AACA;EACIjI,WAAWA,CAACmB,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;EACf;EACAyG,IAAI,GAAGI,aAAa,CAACQ,cAAc;EACnC;AACJ;AACA;AACA;EACI7G,KAAKA,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;IACf;IACA;IACA,OAAO,IAAIqG,kBAAkB,CAAC;MAC1B5I,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCuI,MAAM,EAAEhG,MAAM,CAACgG,MAAM,KAAK1F,SAAS,GAAGN,MAAM,CAACgG,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAEjG,MAAM,CAACiG,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDxB,GAAG,EAAEzE,MAAM,CAACyE,GAAG,IAAI,IAAI,CAACA,GAAG,IAAInE;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiG,YAAY,SAASR,gBAAgB,CAAC;EACxC;AACJ;AACA;EACIrB,IAAI;EACJ;AACJ;AACA;EACI5G,WAAWA,CAACmB,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,KAAK,CAACA,IAAI,CAAC;IACX,IAAI,CAACyF,IAAI,GAAGzF,IAAI,CAACyF,IAAI,KAAKpE,SAAS,GAAGrB,IAAI,CAACyF,IAAI,GAAG,IAAI;EAC1D;EACAgB,IAAI,GAAGI,aAAa,CAACU,QAAQ;EAC7B/G,KAAKA,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;IACf,OAAO,IAAIuG,YAAY,CAAC;MACpB7B,IAAI,EAAE1E,MAAM,CAAC0E,IAAI,KAAKpE,SAAS,GAAGN,MAAM,CAAC0E,IAAI,GAAG,IAAI,CAACA,IAAI;MACzDjH,OAAO,EAAEuC,MAAM,CAACvC,OAAO,IAAI,IAAI,CAACA,OAAO;MACvCuI,MAAM,EAAEhG,MAAM,CAACgG,MAAM,KAAK1F,SAAS,GAAGN,MAAM,CAACgG,MAAM,GAAG,IAAI,CAACA,MAAM;MACjEC,UAAU,EAAEjG,MAAM,CAACiG,UAAU,IAAI,IAAI,CAACA,UAAU;MAChDxB,GAAG,EAAEzE,MAAM,CAACyE,GAAG,IAAI,IAAI,CAACA,GAAG,IAAInE;IACnC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmG,iBAAiB,SAASV,gBAAgB,CAAC;EAC7C3H,IAAI,GAAG,mBAAmB;EAC1BsI,OAAO;EACPC,KAAK;EACL;AACJ;AACA;EACIT,EAAE,GAAG,KAAK;EACVpI,WAAWA,CAACmB,IAAI,EAAE;IACd;IACA,KAAK,CAACA,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC;IAC/B;IACA;IACA;IACA,IAAI,IAAI,CAAC+G,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,EAAE;MACzC,IAAI,CAACU,OAAO,GAAG,mCAAmCzH,IAAI,CAACwF,GAAG,IAAI,eAAe,EAAE;IACnF,CAAC,MACI;MACD,IAAI,CAACiC,OAAO,GAAG,6BAA6BzH,IAAI,CAACwF,GAAG,IAAI,eAAe,KAAKxF,IAAI,CAAC+G,MAAM,IAAI/G,IAAI,CAACgH,UAAU,EAAE;IAChH;IACA,IAAI,CAACU,KAAK,GAAG1H,IAAI,CAAC0H,KAAK,IAAI,IAAI;EACnC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,GAAG;AAC/B,MAAMC,2BAA2B,GAAG,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;EACjDA,cAAc,CAACA,cAAc,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,SAAS;EAC3DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,GAAG,OAAO;EACvDA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,GAAG,eAAe;EACvEA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,MAAM;EACrDA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,mBAAmB;EAC/EA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,GAAG,WAAW;EAC/DA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;EACzDA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,kBAAkB;EAC7EA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU;EAC7DA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,GAAG,sBAAsB;EACrFA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,GAAG,iBAAiB;EAC3EA,cAAc,CAACA,cAAc,CAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,GAAG,6BAA6B;EACnGA,cAAc,CAACA,cAAc,CAAC,4BAA4B,CAAC,GAAG,GAAG,CAAC,GAAG,4BAA4B;EACjGA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY;EACjEA,cAAc,CAACA,cAAc,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,oBAAoB;EACjFA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,GAAG,CAAC,GAAG,gBAAgB;EACzEA,cAAc,CAACA,cAAc,CAAC,yBAAyB,CAAC,GAAG,GAAG,CAAC,GAAG,yBAAyB;EAC3FA,cAAc,CAACA,cAAc,CAAC,uBAAuB,CAAC,GAAG,GAAG,CAAC,GAAG,uBAAuB;EACvFA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,qBAAqB;EACnFA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,GAAG,cAAc;EACrEA,cAAc,CAACA,cAAc,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,GAAG,aAAa;EACnEA,cAAc,CAACA,cAAc,CAAC,+BAA+B,CAAC,GAAG,GAAG,CAAC,GAAG,+BAA+B;AAC3G,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACrE,OAAO,EAAEgC,IAAI,EAAE;EAC5B,OAAO;IACHA,IAAI;IACJjH,OAAO,EAAEiF,OAAO,CAACjF,OAAO;IACxBkH,OAAO,EAAEjC,OAAO,CAACiC,OAAO;IACxBqC,OAAO,EAAEtE,OAAO,CAACsE,OAAO;IACxBvF,MAAM,EAAEiB,OAAO,CAACjB,MAAM;IACtBmD,cAAc,EAAElC,OAAO,CAACkC,cAAc;IACtCE,YAAY,EAAEpC,OAAO,CAACoC,YAAY;IAClCD,eAAe,EAAEnC,OAAO,CAACmC,eAAe;IACxCG,aAAa,EAAEtC,OAAO,CAACsC;EAC3B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiC,UAAU,CAAC;EACbC,OAAO;EACPpJ,WAAWA,CAACoJ,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,KAAK,EAAE3C,GAAG,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI2E,GAAG;IACP;IACA,IAAID,KAAK,YAAY5C,WAAW,EAAE;MAC9B;MACA;MACA6C,GAAG,GAAGD,KAAK;IACf,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,IAAI3J,OAAO,GAAG6C,SAAS;MACvB,IAAIoC,OAAO,CAACjF,OAAO,YAAYD,WAAW,EAAE;QACxCC,OAAO,GAAGiF,OAAO,CAACjF,OAAO;MAC7B,CAAC,MACI;QACDA,OAAO,GAAG,IAAID,WAAW,CAACkF,OAAO,CAACjF,OAAO,CAAC;MAC9C;MACA;MACA,IAAIgE,MAAM,GAAGnB,SAAS;MACtB,IAAI,CAAC,CAACoC,OAAO,CAACjB,MAAM,EAAE;QAClB,IAAIiB,OAAO,CAACjB,MAAM,YAAYa,UAAU,EAAE;UACtCb,MAAM,GAAGiB,OAAO,CAACjB,MAAM;QAC3B,CAAC,MACI;UACDA,MAAM,GAAG,IAAIa,UAAU,CAAC;YAAEM,UAAU,EAAEF,OAAO,CAACjB;UAAO,CAAC,CAAC;QAC3D;MACJ;MACA;MACA4F,GAAG,GAAG,IAAI7C,WAAW,CAAC4C,KAAK,EAAE3C,GAAG,EAAE/B,OAAO,CAACgC,IAAI,KAAKpE,SAAS,GAAGoC,OAAO,CAACgC,IAAI,GAAG,IAAI,EAAE;QAChFjH,OAAO;QACPkH,OAAO,EAAEjC,OAAO,CAACiC,OAAO;QACxBlD,MAAM;QACNmD,cAAc,EAAElC,OAAO,CAACkC,cAAc;QACtC;QACAE,YAAY,EAAEpC,OAAO,CAACoC,YAAY,IAAI,MAAM;QAC5CD,eAAe,EAAEnC,OAAO,CAACmC,eAAe;QACxCG,aAAa,EAAEtC,OAAO,CAACsC;MAC3B,CAAC,CAAC;IACN;IACA;IACA;IACA;IACA;IACA,MAAMsC,OAAO,GAAGvK,EAAE,CAACsK,GAAG,CAAC,CAACE,IAAI,CAAC7K,SAAS,CAAE2K,GAAG,IAAK,IAAI,CAACH,OAAO,CAACM,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA;IACA;IACA,IAAID,KAAK,YAAY5C,WAAW,IAAI9B,OAAO,CAACsE,OAAO,KAAK,QAAQ,EAAE;MAC9D,OAAOM,OAAO;IAClB;IACA;IACA;IACA;IACA,MAAMG,IAAI,GAAIH,OAAO,CAACC,IAAI,CAAC5K,MAAM,CAAE+K,KAAK,IAAKA,KAAK,YAAYnB,YAAY,CAAC,CAAE;IAC7E;IACA,QAAQ7D,OAAO,CAACsE,OAAO,IAAI,MAAM;MAC7B,KAAK,MAAM;QACP;QACA;QACA;QACA;QACA;QACA,QAAQK,GAAG,CAACvC,YAAY;UACpB,KAAK,aAAa;YACd,OAAO2C,IAAI,CAACF,IAAI,CAAC3K,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAAC6B,IAAI,KAAK,IAAI,IAAI,EAAE7B,GAAG,CAAC6B,IAAI,YAAYf,WAAW,CAAC,EAAE;gBACzD,MAAM,IAAIjI,aAAa,CAAC,IAAI,CAAC,wDAAwDgD,SAAS,IAAI,iCAAiC,CAAC;cACxI;cACA,OAAOmE,GAAG,CAAC6B,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAO+C,IAAI,CAACF,IAAI,CAAC3K,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAAC6B,IAAI,KAAK,IAAI,IAAI,EAAE7B,GAAG,CAAC6B,IAAI,YAAYb,IAAI,CAAC,EAAE;gBAClD,MAAM,IAAInI,aAAa,CAAC,IAAI,CAAC,+CAA+CgD,SAAS,IAAI,yBAAyB,CAAC;cACvH;cACA,OAAOmE,GAAG,CAAC6B,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;YACP,OAAO+C,IAAI,CAACF,IAAI,CAAC3K,GAAG,CAAEiG,GAAG,IAAK;cAC1B;cACA,IAAIA,GAAG,CAAC6B,IAAI,KAAK,IAAI,IAAI,OAAO7B,GAAG,CAAC6B,IAAI,KAAK,QAAQ,EAAE;gBACnD,MAAM,IAAIhJ,aAAa,CAAC,IAAI,CAAC,iDAAiDgD,SAAS,IAAI,2BAA2B,CAAC;cAC3H;cACA,OAAOmE,GAAG,CAAC6B,IAAI;YACnB,CAAC,CAAC,CAAC;UACP,KAAK,MAAM;UACX;YACI;YACA,OAAO+C,IAAI,CAACF,IAAI,CAAC3K,GAAG,CAAEiG,GAAG,IAAKA,GAAG,CAAC6B,IAAI,CAAC,CAAC;QAChD;MACJ,KAAK,UAAU;QACX;QACA,OAAO+C,IAAI;MACf;QACI;QACA,MAAM,IAAI/L,aAAa,CAAC,IAAI,CAAC,+CAA+CgD,SAAS,IAAI,uCAAuCgE,OAAO,CAACsE,OAAO,GAAG,CAAC;IAC3J;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpH,MAAMA,CAAC6E,GAAG,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACtB,OAAO,IAAI,CAACyE,OAAO,CAAC,QAAQ,EAAE1C,GAAG,EAAE/B,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;EACIvD,GAAGA,CAACsF,GAAG,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACyE,OAAO,CAAC,KAAK,EAAE1C,GAAG,EAAE/B,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiF,IAAIA,CAAClD,GAAG,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACpB,OAAO,IAAI,CAACyE,OAAO,CAAC,MAAM,EAAE1C,GAAG,EAAE/B,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkF,KAAKA,CAACnD,GAAG,EAAEoD,aAAa,EAAE;IACtB,OAAO,IAAI,CAACV,OAAO,CAAC,OAAO,EAAE1C,GAAG,EAAE;MAC9BhD,MAAM,EAAE,IAAIa,UAAU,CAAC,CAAC,CAAC9C,MAAM,CAACqI,aAAa,EAAE,gBAAgB,CAAC;MAChEb,OAAO,EAAE,MAAM;MACflC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpC,OAAOA,CAAC+B,GAAG,EAAE/B,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACyE,OAAO,CAAC,SAAS,EAAE1C,GAAG,EAAE/B,OAAO,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIoF,KAAKA,CAACrD,GAAG,EAAEC,IAAI,EAAEhC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACyE,OAAO,CAAC,OAAO,EAAE1C,GAAG,EAAEsC,OAAO,CAACrE,OAAO,EAAEgC,IAAI,CAAC,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqD,IAAIA,CAACtD,GAAG,EAAEC,IAAI,EAAEhC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1B,OAAO,IAAI,CAACyE,OAAO,CAAC,MAAM,EAAE1C,GAAG,EAAEsC,OAAO,CAACrE,OAAO,EAAEgC,IAAI,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsD,GAAGA,CAACvD,GAAG,EAAEC,IAAI,EAAEhC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACyE,OAAO,CAAC,KAAK,EAAE1C,GAAG,EAAEsC,OAAO,CAACrE,OAAO,EAAEgC,IAAI,CAAC,CAAC;EAC3D;EACA,OAAOuD,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFlB,UAAU,EAApBzL,EAAE,CAAA4M,QAAA,CAAoC9K,WAAW;EAAA;EAC1I,OAAO+K,KAAK,kBAD6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EACY0D,UAAU;IAAAsB,OAAA,EAAVtB,UAAU,CAAAgB;EAAA;AACrH;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAH6FlD,EAAE,CAAAgN,iBAAA,CAGJvB,UAAU,EAAc,CAAC;IACxGvB,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAEpI;EAAY,CAAC,CAAC;AAAA;AAEzD,MAAMmL,aAAa,GAAG,cAAc;AACpC;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAChC,IAAIA,QAAQ,CAAClE,GAAG,EAAE;IACd,OAAOkE,QAAQ,CAAClE,GAAG;EACvB;EACA;EACA,MAAMmE,WAAW,GAAGxE,oBAAoB,CAACyE,iBAAiB,CAAC,CAAC;EAC5D,OAAOF,QAAQ,CAAClL,OAAO,CAAC0B,GAAG,CAACyJ,WAAW,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,MAAME,aAAa,GAAG,IAAIhN,cAAc,CAAC,OAAO4C,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,eAAe,GAAG,EAAE,CAAC;AAC9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqK,YAAY,CAAC;EACf;EACA;EACA;EACAC,SAAS,GAAGpN,MAAM,CAACqN,YAAY,EAAE;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC,EAAEC,KAAK,KAAK,CAAC,GAAGC,IAAI,KAAKC,UAAU,CAACF,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;EACvGE,MAAM,GAAG1N,MAAM,CAACC,MAAM,CAAC;EACvB2L,MAAMA,CAACL,OAAO,EAAE;IACZ,OAAO,IAAInK,UAAU,CAAEuM,QAAQ,IAAK;MAChC,MAAMC,OAAO,GAAG,IAAIC,eAAe,CAAC,CAAC;MACrC,IAAI,CAACC,SAAS,CAACvC,OAAO,EAAEqC,OAAO,CAACG,MAAM,EAAEJ,QAAQ,CAAC,CAACK,IAAI,CAACC,IAAI,EAAGlD,KAAK,IAAK4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;QAAEE;MAAM,CAAC,CAAC,CAAC,CAAC;MACzH,OAAO,MAAM6C,OAAO,CAACM,KAAK,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACMJ,SAASA,CAACvC,OAAO,EAAEwC,MAAM,EAAEJ,QAAQ,EAAE;IAAA,IAAAQ,KAAA;IAAA,OAAAC,iBAAA;MACvC,MAAM/K,IAAI,GAAG8K,KAAI,CAACE,iBAAiB,CAAC9C,OAAO,CAAC;MAC5C,IAAIwB,QAAQ;MACZ,IAAI;QACA;QACA;QACA;QACA,MAAMuB,YAAY,GAAGH,KAAI,CAACT,MAAM,CAACa,iBAAiB,CAAC,MAAMJ,KAAI,CAACf,SAAS,CAAC7B,OAAO,CAACpC,aAAa,EAAE;UAAE4E,MAAM;UAAE,GAAG1K;QAAK,CAAC,CAAC,CAAC;QACpH;QACA;QACA;QACAmL,2CAA2C,CAACF,YAAY,CAAC;QACzD;QACAX,QAAQ,CAACc,IAAI,CAAC;UAAE3E,IAAI,EAAEI,aAAa,CAACwE;QAAK,CAAC,CAAC;QAC3C3B,QAAQ,SAASuB,YAAY;MACjC,CAAC,CACD,OAAOvD,KAAK,EAAE;QACV4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLX,MAAM,EAAEW,KAAK,CAACX,MAAM,IAAI,CAAC;UACzBC,UAAU,EAAEU,KAAK,CAACV,UAAU;UAC5BxB,GAAG,EAAE0C,OAAO,CAACpC,aAAa;UAC1BtH,OAAO,EAAEkJ,KAAK,CAAClJ;QACnB,CAAC,CAAC,CAAC;QACH;MACJ;MACA,MAAMA,OAAO,GAAG,IAAID,WAAW,CAACmL,QAAQ,CAAClL,OAAO,CAAC;MACjD,MAAMwI,UAAU,GAAG0C,QAAQ,CAAC1C,UAAU;MACtC,MAAMxB,GAAG,GAAGiE,gBAAgB,CAACC,QAAQ,CAAC,IAAIxB,OAAO,CAACpC,aAAa;MAC/D,IAAIiB,MAAM,GAAG2C,QAAQ,CAAC3C,MAAM;MAC5B,IAAItB,IAAI,GAAG,IAAI;MACf,IAAIyC,OAAO,CAACvC,cAAc,EAAE;QACxB2E,QAAQ,CAACc,IAAI,CAAC,IAAIhE,kBAAkB,CAAC;UAAE5I,OAAO;UAAEuI,MAAM;UAAEC,UAAU;UAAExB;QAAI,CAAC,CAAC,CAAC;MAC/E;MACA,IAAIkE,QAAQ,CAACjE,IAAI,EAAE;QACf;QACA,MAAM6F,aAAa,GAAG5B,QAAQ,CAAClL,OAAO,CAAC0B,GAAG,CAAC,gBAAgB,CAAC;QAC5D,MAAMqL,MAAM,GAAG,EAAE;QACjB,MAAMC,MAAM,GAAG9B,QAAQ,CAACjE,IAAI,CAACgG,SAAS,CAAC,CAAC;QACxC,IAAIC,cAAc,GAAG,CAAC;QACtB,IAAIC,OAAO;QACX,IAAIC,WAAW;QACf;QACA;QACA,MAAMC,OAAO,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,OAAO;QAC3D;QACA;QACA;QACA,MAAMjB,KAAI,CAACT,MAAM,CAACa,iBAAiB,cAAAH,iBAAA,CAAC,aAAY;UAC5C,OAAO,IAAI,EAAE;YACT,MAAM;cAAEiB,IAAI;cAAE3M;YAAM,CAAC,SAASmM,MAAM,CAACS,IAAI,CAAC,CAAC;YAC3C,IAAID,IAAI,EAAE;cACN;YACJ;YACAT,MAAM,CAACjK,IAAI,CAACjC,KAAK,CAAC;YAClBqM,cAAc,IAAIrM,KAAK,CAACc,MAAM;YAC9B,IAAI+H,OAAO,CAACvC,cAAc,EAAE;cACxBiG,WAAW,GACP1D,OAAO,CAACrC,YAAY,KAAK,MAAM,GACzB,CAAC+F,WAAW,IAAI,EAAE,IAChB,CAACD,OAAO,KAAK,IAAIO,WAAW,CAAC,CAAC,EAAEC,MAAM,CAAC9M,KAAK,EAAE;gBAAE+M,MAAM,EAAE;cAAK,CAAC,CAAC,GACjE/K,SAAS;cACnB,MAAMsE,cAAc,GAAGA,CAAA,KAAM2E,QAAQ,CAACc,IAAI,CAAC;gBACvC3E,IAAI,EAAEI,aAAa,CAACwF,gBAAgB;gBACpCC,KAAK,EAAEhB,aAAa,GAAG,CAACA,aAAa,GAAGjK,SAAS;gBACjDkL,MAAM,EAAEb,cAAc;gBACtBE;cACJ,CAAC,CAAC;cACFC,OAAO,GAAGA,OAAO,CAACW,GAAG,CAAC7G,cAAc,CAAC,GAAGA,cAAc,CAAC,CAAC;YAC5D;UACJ;QACJ,CAAC,EAAC;QACF;QACA,MAAM8G,SAAS,GAAG3B,KAAI,CAAC4B,YAAY,CAACnB,MAAM,EAAEG,cAAc,CAAC;QAC3D,IAAI;UACA,MAAMiB,WAAW,GAAGjD,QAAQ,CAAClL,OAAO,CAAC0B,GAAG,CAAC+E,mBAAmB,CAAC,IAAI,EAAE;UACnEQ,IAAI,GAAGqF,KAAI,CAAC8B,SAAS,CAAC1E,OAAO,EAAEuE,SAAS,EAAEE,WAAW,CAAC;QAC1D,CAAC,CACD,OAAOjF,KAAK,EAAE;UACV;UACA4C,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;YACjCE,KAAK;YACLlJ,OAAO,EAAE,IAAID,WAAW,CAACmL,QAAQ,CAAClL,OAAO,CAAC;YAC1CuI,MAAM,EAAE2C,QAAQ,CAAC3C,MAAM;YACvBC,UAAU,EAAE0C,QAAQ,CAAC1C,UAAU;YAC/BxB,GAAG,EAAEiE,gBAAgB,CAACC,QAAQ,CAAC,IAAIxB,OAAO,CAACpC;UAC/C,CAAC,CAAC,CAAC;UACH;QACJ;MACJ;MACA;MACA,IAAIiB,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGtB,IAAI,GAAGkC,mBAAmB,GAAG,CAAC;MAC3C;MACA;MACA;MACA;MACA;MACA,MAAMV,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;MACxC,IAAIE,EAAE,EAAE;QACJqD,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;UAC3B7B,IAAI;UACJjH,OAAO;UACPuI,MAAM;UACNC,UAAU;UACVxB;QACJ,CAAC,CAAC,CAAC;QACH;QACA;QACA8E,QAAQ,CAACuC,QAAQ,CAAC,CAAC;MACvB,CAAC,MACI;QACDvC,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK,EAAEjC,IAAI;UACXjH,OAAO;UACPuI,MAAM;UACNC,UAAU;UACVxB;QACJ,CAAC,CAAC,CAAC;MACP;IAAC;EACL;EACAoH,SAASA,CAAC1E,OAAO,EAAE4E,UAAU,EAAEH,WAAW,EAAE;IACxC,QAAQzE,OAAO,CAACrC,YAAY;MACxB,KAAK,MAAM;QACP;QACA,MAAMkH,IAAI,GAAG,IAAIb,WAAW,CAAC,CAAC,CAACC,MAAM,CAACW,UAAU,CAAC,CAACrK,OAAO,CAAC+G,aAAa,EAAE,EAAE,CAAC;QAC5E,OAAOuD,IAAI,KAAK,EAAE,GAAG,IAAI,GAAGzG,IAAI,CAAC0G,KAAK,CAACD,IAAI,CAAC;MAChD,KAAK,MAAM;QACP,OAAO,IAAIb,WAAW,CAAC,CAAC,CAACC,MAAM,CAACW,UAAU,CAAC;MAC/C,KAAK,MAAM;QACP,OAAO,IAAIlI,IAAI,CAAC,CAACkI,UAAU,CAAC,EAAE;UAAErG,IAAI,EAAEkG;QAAY,CAAC,CAAC;MACxD,KAAK,aAAa;QACd,OAAOG,UAAU,CAACG,MAAM;IAChC;EACJ;EACAjC,iBAAiBA,CAAC5C,GAAG,EAAE;IACnB;IACA,MAAM5J,OAAO,GAAG,CAAC,CAAC;IAClB,MAAM0O,WAAW,GAAG9E,GAAG,CAACxC,eAAe,GAAG,SAAS,GAAGvE,SAAS;IAC/D;IACA+G,GAAG,CAAC5J,OAAO,CAACO,OAAO,CAAC,CAACI,IAAI,EAAEU,MAAM,KAAMrB,OAAO,CAACW,IAAI,CAAC,GAAGU,MAAM,CAACmE,IAAI,CAAC,GAAG,CAAE,CAAC;IACzE;IACA,IAAI,CAACoE,GAAG,CAAC5J,OAAO,CAACuB,GAAG,CAACmF,aAAa,CAAC,EAAE;MACjC1G,OAAO,CAAC0G,aAAa,CAAC,GAAGI,mBAAmB;IAChD;IACA;IACA,IAAI,CAAC8C,GAAG,CAAC5J,OAAO,CAACuB,GAAG,CAACkF,mBAAmB,CAAC,EAAE;MACvC,MAAMkI,YAAY,GAAG/E,GAAG,CAAC5B,uBAAuB,CAAC,CAAC;MAClD;MACA,IAAI2G,YAAY,KAAK,IAAI,EAAE;QACvB3O,OAAO,CAACyG,mBAAmB,CAAC,GAAGkI,YAAY;MAC/C;IACJ;IACA,OAAO;MACH1H,IAAI,EAAE2C,GAAG,CAAC/B,aAAa,CAAC,CAAC;MACzB7B,MAAM,EAAE4D,GAAG,CAAC5D,MAAM;MAClBhG,OAAO;MACP0O;IACJ,CAAC;EACL;EACAR,YAAYA,CAACnB,MAAM,EAAE6B,WAAW,EAAE;IAC9B,MAAMX,SAAS,GAAG,IAAIY,UAAU,CAACD,WAAW,CAAC;IAC7C,IAAIE,QAAQ,GAAG,CAAC;IAChB,KAAK,MAAMC,KAAK,IAAIhC,MAAM,EAAE;MACxBkB,SAAS,CAAC/L,GAAG,CAAC6M,KAAK,EAAED,QAAQ,CAAC;MAC9BA,QAAQ,IAAIC,KAAK,CAACpN,MAAM;IAC5B;IACA,OAAOsM,SAAS;EACpB;EACA,OAAOzD,IAAI,YAAAwE,qBAAAtE,iBAAA;IAAA,YAAAA,iBAAA,IAAwFY,YAAY;EAAA;EAC/G,OAAOV,KAAK,kBA3N6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EA2NYwF,YAAY;IAAAR,OAAA,EAAZQ,YAAY,CAAAd;EAAA;AACvH;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KA7N6FlD,EAAE,CAAAgN,iBAAA,CA6NJO,YAAY,EAAc,CAAC;IAC1GrD,IAAI,EAAE/J;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMsN,YAAY,CAAC;AAEnB,SAASY,IAAIA,CAAA,EAAG,CAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,2CAA2CA,CAACsC,OAAO,EAAE;EAC1DA,OAAO,CAAC9C,IAAI,CAACC,IAAI,EAAEA,IAAI,CAAC;AAC5B;AAEA,SAAS8C,qBAAqBA,CAACtF,GAAG,EAAEuF,cAAc,EAAE;EAChD,OAAOA,cAAc,CAACvF,GAAG,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASwF,6BAA6BA,CAACC,WAAW,EAAEC,WAAW,EAAE;EAC7D,OAAO,CAACC,cAAc,EAAEJ,cAAc,KAAKG,WAAW,CAACE,SAAS,CAACD,cAAc,EAAE;IAC7ExF,MAAM,EAAG0F,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc;EAChF,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA,SAASO,oBAAoBA,CAACL,WAAW,EAAEM,aAAa,EAAEC,QAAQ,EAAE;EAChE,OAAO,CAACL,cAAc,EAAEJ,cAAc,KAAKtQ,qBAAqB,CAAC+Q,QAAQ,EAAE,MAAMD,aAAa,CAACJ,cAAc,EAAGE,iBAAiB,IAAKJ,WAAW,CAACI,iBAAiB,EAAEN,cAAc,CAAC,CAAC,CAAC;AAC1L;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,iBAAiB,GAAG,IAAIxR,cAAc,CAAC4C,SAAS,GAAG,mBAAmB,GAAG,EAAE,CAAC;AAClF;AACA;AACA;AACA,MAAM6O,oBAAoB,GAAG,IAAIzR,cAAc,CAAC4C,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA,MAAM8O,yBAAyB,GAAG,IAAI1R,cAAc,CAAC4C,SAAS,GAAG,2BAA2B,GAAG,EAAE,CAAC;AAClG;AACA;AACA;AACA,MAAM+O,gCAAgC,GAAG,IAAI3R,cAAc,CAAC4C,SAAS,GAAG,kCAAkC,GAAG,EAAE,EAAE;EAAEgP,UAAU,EAAE,MAAM;EAAEnF,OAAO,EAAEA,CAAA,KAAM;AAAK,CAAC,CAAC;AAC7J;AACA;AACA;AACA;AACA,SAASoF,0BAA0BA,CAAA,EAAG;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,CAACvG,GAAG,EAAEH,OAAO,KAAK;IACrB,IAAI0G,KAAK,KAAK,IAAI,EAAE;MAChB,MAAMC,YAAY,GAAGjS,MAAM,CAAC0R,iBAAiB,EAAE;QAAEpE,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAI,EAAE;MACxE;MACA;MACA;MACA;MACA0E,KAAK,GAAGC,YAAY,CAACC,WAAW,CAACjB,6BAA6B,EAAEF,qBAAqB,CAAC;IAC1F;IACA,MAAMoB,YAAY,GAAGnS,MAAM,CAACI,qBAAqB,CAAC;IAClD,MAAMgS,qBAAqB,GAAGpS,MAAM,CAAC6R,gCAAgC,CAAC;IACtE,IAAIO,qBAAqB,EAAE;MACvB,MAAMC,MAAM,GAAGF,YAAY,CAACG,GAAG,CAAC,CAAC;MACjC,OAAON,KAAK,CAACvG,GAAG,EAAEH,OAAO,CAAC,CAACK,IAAI,CAAC1K,QAAQ,CAAC,MAAMkR,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAChF,CAAC,MACI;MACD,OAAOL,KAAK,CAACvG,GAAG,EAAEH,OAAO,CAAC;IAC9B;EACJ,CAAC;AACL;AACA,IAAIkH,4BAA4B,GAAG,KAAK;AACxC,MAAMC,sBAAsB,SAAS/Q,WAAW,CAAC;EAC7CgR,OAAO;EACPjB,QAAQ;EACRO,KAAK,GAAG,IAAI;EACZG,YAAY,GAAGnS,MAAM,CAACI,qBAAqB,CAAC;EAC5CgS,qBAAqB,GAAGpS,MAAM,CAAC6R,gCAAgC,CAAC;EAChE3P,WAAWA,CAACwQ,OAAO,EAAEjB,QAAQ,EAAE;IAC3B,KAAK,CAAC,CAAC;IACP,IAAI,CAACiB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjB,QAAQ,GAAGA,QAAQ;IACxB;IACA;IACA;IACA,IAAI,CAAC,OAAO3O,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK,CAAC0P,4BAA4B,EAAE;MAClF,MAAMG,QAAQ,GAAGrR,gBAAgB,CAACmQ,QAAQ,CAAClO,GAAG,CAAClD,WAAW,CAAC,CAAC;MAC5D;MACA;MACA;MACA;MACA,MAAMuS,gBAAgB,GAAG,IAAI,CAACF,OAAO,CAACE,gBAAgB;MACtD,IAAID,QAAQ,IAAI,EAAE,IAAI,CAACD,OAAO,YAAYvF,YAAY,CAAC,IAAI,CAACyF,gBAAgB,EAAE;QAC1EJ,4BAA4B,GAAG,IAAI;QACnCf,QAAQ,CACHlO,GAAG,CAAChD,QAAQ,CAAC,CACbsS,IAAI,CAACpS,mBAAmB,CAAC,IAAI,CAAC,uDAAuD,uDAAuD,GAC7I,oDAAoD,GACpD,iEAAiE,GACjE,4CAA4C,GAC5C,wEAAwE,GACxE,sCAAsC,CAAC,CAAC;MAChD;IACJ;EACJ;EACAmL,MAAMA,CAACwF,cAAc,EAAE;IACnB,IAAI,IAAI,CAACY,KAAK,KAAK,IAAI,EAAE;MACrB,MAAMc,qBAAqB,GAAGpP,KAAK,CAACrC,IAAI,CAAC,IAAI0R,GAAG,CAAC,CAC7C,GAAG,IAAI,CAACtB,QAAQ,CAAClO,GAAG,CAACoO,oBAAoB,CAAC,EAC1C,GAAG,IAAI,CAACF,QAAQ,CAAClO,GAAG,CAACqO,yBAAyB,EAAE,EAAE,CAAC,CACtD,CAAC,CAAC;MACH;MACA;MACA;MACA;MACA,IAAI,CAACI,KAAK,GAAGc,qBAAqB,CAACZ,WAAW,CAAC,CAACc,eAAe,EAAExB,aAAa,KAAKD,oBAAoB,CAACyB,eAAe,EAAExB,aAAa,EAAE,IAAI,CAACC,QAAQ,CAAC,EAAEV,qBAAqB,CAAC;IAClL;IACA,IAAI,IAAI,CAACqB,qBAAqB,EAAE;MAC5B,MAAMC,MAAM,GAAG,IAAI,CAACF,YAAY,CAACG,GAAG,CAAC,CAAC;MACtC,OAAO,IAAI,CAACN,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACoB,OAAO,CAAC9G,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC3F,IAAI,CAAC1K,QAAQ,CAAC,MAAM,IAAI,CAACkR,YAAY,CAACI,MAAM,CAACF,MAAM,CAAC,CAAC,CAAC;IAC3J,CAAC,MACI;MACD,OAAO,IAAI,CAACL,KAAK,CAACZ,cAAc,EAAGE,iBAAiB,IAAK,IAAI,CAACoB,OAAO,CAAC9G,MAAM,CAAC0F,iBAAiB,CAAC,CAAC;IACpG;EACJ;EACA,OAAOjF,IAAI,YAAA4G,+BAAA1G,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkG,sBAAsB,EAtWhC7S,EAAE,CAAA4M,QAAA,CAsWgD7K,WAAW,GAtW7D/B,EAAE,CAAA4M,QAAA,CAsWwE5M,EAAE,CAACsT,mBAAmB;EAAA;EACzL,OAAOzG,KAAK,kBAvW6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EAuWY8K,sBAAsB;IAAA9F,OAAA,EAAtB8F,sBAAsB,CAAApG;EAAA;AACjI;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAzW6FlD,EAAE,CAAAgN,iBAAA,CAyWJ6F,sBAAsB,EAAc,CAAC;IACpH3I,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAEnI;EAAY,CAAC,EAAE;IAAEmI,IAAI,EAAElK,EAAE,CAACsT;EAAoB,CAAC,CAAC;AAAA;;AAE3F;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,CAAC;AACrB;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB;AACA;AACA,MAAMC,qBAAqB,GAAG,gDAAgD;AAC9E;AACA;AACA,MAAMC,sBAAsB,GAAG,+CAA+C;AAC9E,MAAMC,6BAA6B,GAAG,6CAA6C;AACnF;AACA;AACA,MAAMC,+BAA+B,GAAG,wCAAwC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOA,MAAM;EACjB;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBC,WAAW;EACXC,QAAQ;EACR;AACJ;AACA;EACIC,eAAe,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;EACnC/R,WAAWA,CAAC2R,WAAW,EAAEC,QAAQ,EAAE;IAC/B,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;EACII,YAAYA,CAAA,EAAG;IACX,OAAO,qBAAqBf,aAAa,EAAE,EAAE;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvH,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAC5D,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI3C,KAAK,CAACoO,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAI7H,GAAG,CAACvC,YAAY,KAAK,MAAM,EAAE;MAClC,MAAM,IAAIhE,KAAK,CAACqO,6BAA6B,CAAC;IAClD;IACA;IACA;IACA,IAAI9H,GAAG,CAAC5J,OAAO,CAAC4B,IAAI,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAI0B,KAAK,CAACsO,+BAA+B,CAAC;IACpD;IACA;IACA,OAAO,IAAIpS,UAAU,CAAEuM,QAAQ,IAAK;MAChC;MACA;MACA;MACA,MAAMwG,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;MACpC,MAAMrL,GAAG,GAAG4C,GAAG,CAACtC,aAAa,CAACrD,OAAO,CAAC,sBAAsB,EAAE,IAAIqO,QAAQ,IAAI,CAAC;MAC/E;MACA,MAAMC,IAAI,GAAG,IAAI,CAACN,QAAQ,CAACO,aAAa,CAAC,QAAQ,CAAC;MAClDD,IAAI,CAACE,GAAG,GAAGzL,GAAG;MACd;MACA;MACA;MACA,IAAIC,IAAI,GAAG,IAAI;MACf;MACA,IAAIyL,QAAQ,GAAG,KAAK;MACpB;MACA;MACA;MACA,IAAI,CAACV,WAAW,CAACM,QAAQ,CAAC,GAAIK,IAAI,IAAK;QACnC;QACA,OAAO,IAAI,CAACX,WAAW,CAACM,QAAQ,CAAC;QACjC;QACArL,IAAI,GAAG0L,IAAI;QACXD,QAAQ,GAAG,IAAI;MACnB,CAAC;MACD;MACA;MACA;MACA,MAAME,OAAO,GAAGA,CAAA,KAAM;QAClBL,IAAI,CAACM,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;QACxCP,IAAI,CAACM,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;QAC1C;QACAR,IAAI,CAAC7B,MAAM,CAAC,CAAC;QACb;QACA;QACA,OAAO,IAAI,CAACsB,WAAW,CAACM,QAAQ,CAAC;MACrC,CAAC;MACD;MACA;MACA;MACA;MACA,MAAMQ,MAAM,GAAI7I,KAAK,IAAK;QACtB;QACA;QACA;QACA,IAAI,CAACiI,eAAe,CAAC/F,IAAI,CAAC,MAAM;UAC5B;UACAyG,OAAO,CAAC,CAAC;UACT;UACA,IAAI,CAACF,QAAQ,EAAE;YACX;YACA;YACA5G,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjChC,GAAG;cACHuB,MAAM,EAAE,CAAC;cACTC,UAAU,EAAE,aAAa;cACzBU,KAAK,EAAE,IAAI7F,KAAK,CAACmO,qBAAqB;YAC1C,CAAC,CAAC,CAAC;YACH;UACJ;UACA;UACA;UACA1F,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;YAC3B7B,IAAI;YACJsB,MAAM,EAAEY,mBAAmB;YAC3BX,UAAU,EAAE,IAAI;YAChBxB;UACJ,CAAC,CAAC,CAAC;UACH;UACA8E,QAAQ,CAACuC,QAAQ,CAAC,CAAC;QACvB,CAAC,CAAC;MACN,CAAC;MACD;MACA;MACA;MACA,MAAM0E,OAAO,GAAI7J,KAAK,IAAK;QACvB0J,OAAO,CAAC,CAAC;QACT;QACA9G,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;UACjCE,KAAK;UACLX,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,aAAa;UACzBxB;QACJ,CAAC,CAAC,CAAC;MACP,CAAC;MACD;MACA;MACAuL,IAAI,CAACS,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;MACrCP,IAAI,CAACS,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;MACvC,IAAI,CAACd,QAAQ,CAAChL,IAAI,CAACgM,WAAW,CAACV,IAAI,CAAC;MACpC;MACAzG,QAAQ,CAACc,IAAI,CAAC;QAAE3E,IAAI,EAAEI,aAAa,CAACwE;MAAK,CAAC,CAAC;MAC3C;MACA,OAAO,MAAM;QACT,IAAI,CAAC6F,QAAQ,EAAE;UACX,IAAI,CAACQ,eAAe,CAACX,IAAI,CAAC;QAC9B;QACA;QACAK,OAAO,CAAC,CAAC;MACb,CAAC;IACL,CAAC,CAAC;EACN;EACAM,eAAeA,CAACC,MAAM,EAAE;IACpB;IACA;IACA;IACA5B,eAAe,KAAK,IAAI,CAACU,QAAQ,CAACmB,cAAc,CAACC,kBAAkB,CAAC,CAAC;IACrE9B,eAAe,CAAC+B,SAAS,CAACH,MAAM,CAAC;EACrC;EACA,OAAO3I,IAAI,YAAA+I,2BAAA7I,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqH,kBAAkB,EAnjB5BhU,EAAE,CAAA4M,QAAA,CAmjB4CiH,oBAAoB,GAnjBlE7T,EAAE,CAAA4M,QAAA,CAmjB6E/K,QAAQ;EAAA;EAChL,OAAOgL,KAAK,kBApjB6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EAojBYiM,kBAAkB;IAAAjH,OAAA,EAAlBiH,kBAAkB,CAAAvH;EAAA;AAC7H;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAtjB6FlD,EAAE,CAAAgN,iBAAA,CAsjBJgH,kBAAkB,EAAc,CAAC;IAChH9J,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAE2J;EAAqB,CAAC,EAAE;IAAE3J,IAAI,EAAEpF,SAAS;IAAE2Q,UAAU,EAAE,CAAC;MAC/EvL,IAAI,EAAEnJ,MAAM;MACZ6M,IAAI,EAAE,CAAC/L,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,SAAS6T,kBAAkBA,CAAC7J,GAAG,EAAEgD,IAAI,EAAE;EACnC,IAAIhD,GAAG,CAAC5D,MAAM,KAAK,OAAO,EAAE;IACxB,OAAO7H,MAAM,CAAC4T,kBAAkB,CAAC,CAAChI,MAAM,CAACH,GAAG,CAAC;EACjD;EACA;EACA,OAAOgD,IAAI,CAAChD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8J,gBAAgB,CAAC;EACnB9D,QAAQ;EACRvP,WAAWA,CAACuP,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,SAASA,CAACD,cAAc,EAAE3C,IAAI,EAAE;IAC5B,OAAO/N,qBAAqB,CAAC,IAAI,CAAC+Q,QAAQ,EAAE,MAAM6D,kBAAkB,CAAClE,cAAc,EAAGE,iBAAiB,IAAK7C,IAAI,CAAC7C,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC;EAChJ;EACA,OAAOjF,IAAI,YAAAmJ,yBAAAjJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFgJ,gBAAgB,EA7lB1B3V,EAAE,CAAA4M,QAAA,CA6lB0C5M,EAAE,CAACsT,mBAAmB;EAAA;EAC3J,OAAOzG,KAAK,kBA9lB6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EA8lBY4N,gBAAgB;IAAA5I,OAAA,EAAhB4I,gBAAgB,CAAAlJ;EAAA;AAC3H;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAhmB6FlD,EAAE,CAAAgN,iBAAA,CAgmBJ2I,gBAAgB,EAAc,CAAC;IAC9GzL,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAElK,EAAE,CAACsT;EAAoB,CAAC,CAAC;AAAA;AAEpE,MAAMuC,WAAW,GAAG,cAAc;AAClC,MAAMC,oBAAoB,GAAGC,MAAM,CAAC,IAAInN,oBAAoB,GAAG,EAAE,GAAG,CAAC;AACrE;AACA;AACA;AACA;AACA,SAASoN,cAAcA,CAACC,GAAG,EAAE;EACzB,IAAI,aAAa,IAAIA,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE;IACzC,OAAOD,GAAG,CAACC,WAAW;EAC1B;EACA,IAAIJ,oBAAoB,CAACK,IAAI,CAACF,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACxD,OAAOH,GAAG,CAACI,iBAAiB,CAACzN,oBAAoB,CAAC;EACtD;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0N,cAAc,CAAC;EACjBC,UAAU;EACVjU,WAAWA,CAACiU,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;AACJ;AACA;AACA;AACA;EACIvK,MAAMA,CAACH,GAAG,EAAE;IACR;IACA;IACA,IAAIA,GAAG,CAAC5D,MAAM,KAAK,OAAO,EAAE;MACxB,MAAM,IAAI/H,aAAa,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,OAAOgD,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,sNAAsN,CAAC;IAC/N;IACA;IACA;IACA;IACA,MAAMqT,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMC,MAAM,GAAGD,UAAU,CAACE,SAAS,GAC7BhV,IAAI,CAAC8U,UAAU,CAACE,SAAS,CAAC,CAAC,CAAC,GAC5BlV,EAAE,CAAC,IAAI,CAAC;IACd,OAAOiV,MAAM,CAACzK,IAAI,CAACzK,SAAS,CAAC,MAAM;MAC/B;MACA,OAAO,IAAIE,UAAU,CAAEuM,QAAQ,IAAK;QAChC;QACA;QACA,MAAMkI,GAAG,GAAGM,UAAU,CAACG,KAAK,CAAC,CAAC;QAC9BT,GAAG,CAACU,IAAI,CAAC9K,GAAG,CAAC5D,MAAM,EAAE4D,GAAG,CAACtC,aAAa,CAAC;QACvC,IAAIsC,GAAG,CAACxC,eAAe,EAAE;UACrB4M,GAAG,CAAC5M,eAAe,GAAG,IAAI;QAC9B;QACA;QACAwC,GAAG,CAAC5J,OAAO,CAACO,OAAO,CAAC,CAACI,IAAI,EAAEU,MAAM,KAAK2S,GAAG,CAACW,gBAAgB,CAAChU,IAAI,EAAEU,MAAM,CAACmE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACnF;QACA,IAAI,CAACoE,GAAG,CAAC5J,OAAO,CAACuB,GAAG,CAACmF,aAAa,CAAC,EAAE;UACjCsN,GAAG,CAACW,gBAAgB,CAACjO,aAAa,EAAEI,mBAAmB,CAAC;QAC5D;QACA;QACA,IAAI,CAAC8C,GAAG,CAAC5J,OAAO,CAACuB,GAAG,CAACkF,mBAAmB,CAAC,EAAE;UACvC,MAAMkI,YAAY,GAAG/E,GAAG,CAAC5B,uBAAuB,CAAC,CAAC;UAClD;UACA,IAAI2G,YAAY,KAAK,IAAI,EAAE;YACvBqF,GAAG,CAACW,gBAAgB,CAAClO,mBAAmB,EAAEkI,YAAY,CAAC;UAC3D;QACJ;QACA;QACA,IAAI/E,GAAG,CAACvC,YAAY,EAAE;UAClB,MAAMA,YAAY,GAAGuC,GAAG,CAACvC,YAAY,CAAC5F,WAAW,CAAC,CAAC;UACnD;UACA;UACA;UACA;UACA;UACAuS,GAAG,CAAC3M,YAAY,GAAIA,YAAY,KAAK,MAAM,GAAGA,YAAY,GAAG,MAAO;QACxE;QACA;QACA,MAAMuN,OAAO,GAAGhL,GAAG,CAAC/B,aAAa,CAAC,CAAC;QACnC;QACA;QACA;QACA;QACA;QACA;QACA,IAAIgN,cAAc,GAAG,IAAI;QACzB;QACA;QACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;UACzB,IAAID,cAAc,KAAK,IAAI,EAAE;YACzB,OAAOA,cAAc;UACzB;UACA,MAAMrM,UAAU,GAAGwL,GAAG,CAACxL,UAAU,IAAI,IAAI;UACzC;UACA,MAAMxI,OAAO,GAAG,IAAID,WAAW,CAACiU,GAAG,CAACG,qBAAqB,CAAC,CAAC,CAAC;UAC5D;UACA;UACA,MAAMnN,GAAG,GAAG+M,cAAc,CAACC,GAAG,CAAC,IAAIpK,GAAG,CAAC5C,GAAG;UAC1C;UACA6N,cAAc,GAAG,IAAIjM,kBAAkB,CAAC;YAAE5I,OAAO;YAAEuI,MAAM,EAAEyL,GAAG,CAACzL,MAAM;YAAEC,UAAU;YAAExB;UAAI,CAAC,CAAC;UACzF,OAAO6N,cAAc;QACzB,CAAC;QACD;QACA;QACA;QACA,MAAM/B,MAAM,GAAGA,CAAA,KAAM;UACjB;UACA,IAAI;YAAE9S,OAAO;YAAEuI,MAAM;YAAEC,UAAU;YAAExB;UAAI,CAAC,GAAG8N,cAAc,CAAC,CAAC;UAC3D;UACA,IAAI7N,IAAI,GAAG,IAAI;UACf,IAAIsB,MAAM,KAAKa,2BAA2B,EAAE;YACxC;YACAnC,IAAI,GAAG,OAAO+M,GAAG,CAAC9I,QAAQ,KAAK,WAAW,GAAG8I,GAAG,CAACe,YAAY,GAAGf,GAAG,CAAC9I,QAAQ;UAChF;UACA;UACA,IAAI3C,MAAM,KAAK,CAAC,EAAE;YACdA,MAAM,GAAG,CAAC,CAACtB,IAAI,GAAGkC,mBAAmB,GAAG,CAAC;UAC7C;UACA;UACA;UACA;UACA;UACA,IAAIV,EAAE,GAAGF,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG;UACtC;UACA;UACA,IAAIqB,GAAG,CAACvC,YAAY,KAAK,MAAM,IAAI,OAAOJ,IAAI,KAAK,QAAQ,EAAE;YACzD;YACA,MAAM+N,YAAY,GAAG/N,IAAI;YACzBA,IAAI,GAAGA,IAAI,CAAChD,OAAO,CAAC2P,WAAW,EAAE,EAAE,CAAC;YACpC,IAAI;cACA;cACA;cACA3M,IAAI,GAAGA,IAAI,KAAK,EAAE,GAAGa,IAAI,CAAC0G,KAAK,CAACvH,IAAI,CAAC,GAAG,IAAI;YAChD,CAAC,CACD,OAAOiC,KAAK,EAAE;cACV;cACA;cACA;cACAjC,IAAI,GAAG+N,YAAY;cACnB;cACA;cACA,IAAIvM,EAAE,EAAE;gBACJ;gBACAA,EAAE,GAAG,KAAK;gBACV;gBACAxB,IAAI,GAAG;kBAAEiC,KAAK;kBAAEqF,IAAI,EAAEtH;gBAAK,CAAC;cAChC;YACJ;UACJ;UACA,IAAIwB,EAAE,EAAE;YACJ;YACAqD,QAAQ,CAACc,IAAI,CAAC,IAAI9D,YAAY,CAAC;cAC3B7B,IAAI;cACJjH,OAAO;cACPuI,MAAM;cACNC,UAAU;cACVxB,GAAG,EAAEA,GAAG,IAAInE;YAChB,CAAC,CAAC,CAAC;YACH;YACA;YACAiJ,QAAQ,CAACuC,QAAQ,CAAC,CAAC;UACvB,CAAC,MACI;YACD;YACAvC,QAAQ,CAAC5C,KAAK,CAAC,IAAIF,iBAAiB,CAAC;cACjC;cACAE,KAAK,EAAEjC,IAAI;cACXjH,OAAO;cACPuI,MAAM;cACNC,UAAU;cACVxB,GAAG,EAAEA,GAAG,IAAInE;YAChB,CAAC,CAAC,CAAC;UACP;QACJ,CAAC;QACD;QACA;QACA;QACA,MAAMkQ,OAAO,GAAI7J,KAAK,IAAK;UACvB,MAAM;YAAElC;UAAI,CAAC,GAAG8N,cAAc,CAAC,CAAC;UAChC,MAAM1P,GAAG,GAAG,IAAI4D,iBAAiB,CAAC;YAC9BE,KAAK;YACLX,MAAM,EAAEyL,GAAG,CAACzL,MAAM,IAAI,CAAC;YACvBC,UAAU,EAAEwL,GAAG,CAACxL,UAAU,IAAI,eAAe;YAC7CxB,GAAG,EAAEA,GAAG,IAAInE;UAChB,CAAC,CAAC;UACFiJ,QAAQ,CAAC5C,KAAK,CAAC9D,GAAG,CAAC;QACvB,CAAC;QACD;QACA;QACA;QACA;QACA,IAAI6P,WAAW,GAAG,KAAK;QACvB;QACA;QACA,MAAMC,cAAc,GAAIjL,KAAK,IAAK;UAC9B;UACA,IAAI,CAACgL,WAAW,EAAE;YACdnJ,QAAQ,CAACc,IAAI,CAACkI,cAAc,CAAC,CAAC,CAAC;YAC/BG,WAAW,GAAG,IAAI;UACtB;UACA;UACA;UACA,IAAIE,aAAa,GAAG;YAChBlN,IAAI,EAAEI,aAAa,CAACwF,gBAAgB;YACpCE,MAAM,EAAE9D,KAAK,CAAC8D;UAClB,CAAC;UACD;UACA,IAAI9D,KAAK,CAACmL,gBAAgB,EAAE;YACxBD,aAAa,CAACrH,KAAK,GAAG7D,KAAK,CAAC6D,KAAK;UACrC;UACA;UACA;UACA;UACA,IAAIlE,GAAG,CAACvC,YAAY,KAAK,MAAM,IAAI,CAAC,CAAC2M,GAAG,CAACe,YAAY,EAAE;YACnDI,aAAa,CAAC/H,WAAW,GAAG4G,GAAG,CAACe,YAAY;UAChD;UACA;UACAjJ,QAAQ,CAACc,IAAI,CAACuI,aAAa,CAAC;QAChC,CAAC;QACD;QACA;QACA,MAAME,YAAY,GAAIpL,KAAK,IAAK;UAC5B;UACA;UACA,IAAIqL,QAAQ,GAAG;YACXrN,IAAI,EAAEI,aAAa,CAACkN,cAAc;YAClCxH,MAAM,EAAE9D,KAAK,CAAC8D;UAClB,CAAC;UACD;UACA;UACA,IAAI9D,KAAK,CAACmL,gBAAgB,EAAE;YACxBE,QAAQ,CAACxH,KAAK,GAAG7D,KAAK,CAAC6D,KAAK;UAChC;UACA;UACAhC,QAAQ,CAACc,IAAI,CAAC0I,QAAQ,CAAC;QAC3B,CAAC;QACD;QACAtB,GAAG,CAAChB,gBAAgB,CAAC,MAAM,EAAEF,MAAM,CAAC;QACpCkB,GAAG,CAAChB,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;QACtCiB,GAAG,CAAChB,gBAAgB,CAAC,SAAS,EAAED,OAAO,CAAC;QACxCiB,GAAG,CAAChB,gBAAgB,CAAC,OAAO,EAAED,OAAO,CAAC;QACtC;QACA,IAAInJ,GAAG,CAACzC,cAAc,EAAE;UACpB;UACA6M,GAAG,CAAChB,gBAAgB,CAAC,UAAU,EAAEkC,cAAc,CAAC;UAChD;UACA,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;YAChCxB,GAAG,CAACwB,MAAM,CAACxC,gBAAgB,CAAC,UAAU,EAAEqC,YAAY,CAAC;UACzD;QACJ;QACA;QACArB,GAAG,CAACyB,IAAI,CAACb,OAAO,CAAC;QACjB9I,QAAQ,CAACc,IAAI,CAAC;UAAE3E,IAAI,EAAEI,aAAa,CAACwE;QAAK,CAAC,CAAC;QAC3C;QACA;QACA,OAAO,MAAM;UACT;UACAmH,GAAG,CAACnB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCiB,GAAG,CAACnB,mBAAmB,CAAC,OAAO,EAAEE,OAAO,CAAC;UACzCiB,GAAG,CAACnB,mBAAmB,CAAC,MAAM,EAAEC,MAAM,CAAC;UACvCkB,GAAG,CAACnB,mBAAmB,CAAC,SAAS,EAAEE,OAAO,CAAC;UAC3C,IAAInJ,GAAG,CAACzC,cAAc,EAAE;YACpB6M,GAAG,CAACnB,mBAAmB,CAAC,UAAU,EAAEqC,cAAc,CAAC;YACnD,IAAIN,OAAO,KAAK,IAAI,IAAIZ,GAAG,CAACwB,MAAM,EAAE;cAChCxB,GAAG,CAACwB,MAAM,CAAC3C,mBAAmB,CAAC,UAAU,EAAEwC,YAAY,CAAC;YAC5D;UACJ;UACA;UACA,IAAIrB,GAAG,CAAC0B,UAAU,KAAK1B,GAAG,CAAC2B,IAAI,EAAE;YAC7B3B,GAAG,CAAC3H,KAAK,CAAC,CAAC;UACf;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;EACP;EACA,OAAO7B,IAAI,YAAAoL,uBAAAlL,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2J,cAAc,EA13BxBtW,EAAE,CAAA4M,QAAA,CA03BwCjL,UAAU;EAAA;EAC7I,OAAOkL,KAAK,kBA33B6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EA23BYuO,cAAc;IAAAvJ,OAAA,EAAduJ,cAAc,CAAA7J;EAAA;AACzH;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KA73B6FlD,EAAE,CAAAgN,iBAAA,CA63BJsJ,cAAc,EAAc,CAAC;IAC5GpM,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAEvI;EAAW,CAAC,CAAC;AAAA;AAExD,MAAMmW,YAAY,GAAG,IAAIxX,cAAc,CAAC4C,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC;AACxE,MAAM6U,wBAAwB,GAAG,YAAY;AAC7C,MAAMC,gBAAgB,GAAG,IAAI1X,cAAc,CAAC4C,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EgP,UAAU,EAAE,MAAM;EAClBnF,OAAO,EAAEA,CAAA,KAAMgL;AACnB,CAAC,CAAC;AACF,MAAME,wBAAwB,GAAG,cAAc;AAC/C,MAAMC,gBAAgB,GAAG,IAAI5X,cAAc,CAAC4C,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EgP,UAAU,EAAE,MAAM;EAClBnF,OAAO,EAAEA,CAAA,KAAMkL;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAME,sBAAsB,CAAC;AAE7B;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BC,GAAG;EACHC,QAAQ;EACRC,UAAU;EACVC,gBAAgB,GAAG,EAAE;EACrBC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;EACIC,UAAU,GAAG,CAAC;EACdpW,WAAWA,CAAC+V,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAE;IACnC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;EAChC;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,QAAQ,KAAK,QAAQ,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,MAAMM,YAAY,GAAG,IAAI,CAACP,GAAG,CAACQ,MAAM,IAAI,EAAE;IAC1C,IAAID,YAAY,KAAK,IAAI,CAACJ,gBAAgB,EAAE;MACxC,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACD,SAAS,GAAG7W,gBAAgB,CAACgX,YAAY,EAAE,IAAI,CAACL,UAAU,CAAC;MAChE,IAAI,CAACC,gBAAgB,GAAGI,YAAY;IACxC;IACA,OAAO,IAAI,CAACH,SAAS;EACzB;EACA,OAAOhM,IAAI,YAAAqM,gCAAAnM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyL,uBAAuB,EAj7BjCpY,EAAE,CAAA4M,QAAA,CAi7BiD/K,QAAQ,GAj7B3D7B,EAAE,CAAA4M,QAAA,CAi7BsEnM,WAAW,GAj7BnFT,EAAE,CAAA4M,QAAA,CAi7B8FoL,gBAAgB;EAAA;EACzM,OAAOnL,KAAK,kBAl7B6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EAk7BYqQ,uBAAuB;IAAArL,OAAA,EAAvBqL,uBAAuB,CAAA3L;EAAA;AAClI;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAp7B6FlD,EAAE,CAAAgN,iBAAA,CAo7BJoL,uBAAuB,EAAc,CAAC;IACrHlO,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAEpF,SAAS;IAAE2Q,UAAU,EAAE,CAAC;MAC/CvL,IAAI,EAAEnJ,MAAM;MACZ6M,IAAI,EAAE,CAAC/L,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEqI,IAAI,EAAEpF,SAAS;IAAE2Q,UAAU,EAAE,CAAC;MAClCvL,IAAI,EAAEnJ,MAAM;MACZ6M,IAAI,EAAE,CAACnN,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEyJ,IAAI,EAAEpF,SAAS;IAAE2Q,UAAU,EAAE,CAAC;MAClCvL,IAAI,EAAEnJ,MAAM;MACZ6M,IAAI,EAAE,CAACoK,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAASe,iBAAiBA,CAAClN,GAAG,EAAEgD,IAAI,EAAE;EAClC,MAAMmK,KAAK,GAAGnN,GAAG,CAAC5C,GAAG,CAACvF,WAAW,CAAC,CAAC;EACnC;EACA;EACA;EACA;EACA,IAAI,CAACtD,MAAM,CAAC0X,YAAY,CAAC,IACrBjM,GAAG,CAAC5D,MAAM,KAAK,KAAK,IACpB4D,GAAG,CAAC5D,MAAM,KAAK,MAAM,IACrB+Q,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAC3BD,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;IAC9B,OAAOpK,IAAI,CAAChD,GAAG,CAAC;EACpB;EACA,MAAM9D,KAAK,GAAG3H,MAAM,CAAC+X,sBAAsB,CAAC,CAACQ,QAAQ,CAAC,CAAC;EACvD,MAAMO,UAAU,GAAG9Y,MAAM,CAAC8X,gBAAgB,CAAC;EAC3C;EACA,IAAInQ,KAAK,IAAI,IAAI,IAAI,CAAC8D,GAAG,CAAC5J,OAAO,CAACuB,GAAG,CAAC0V,UAAU,CAAC,EAAE;IAC/CrN,GAAG,GAAGA,GAAG,CAAC5H,KAAK,CAAC;MAAEhC,OAAO,EAAE4J,GAAG,CAAC5J,OAAO,CAACkC,GAAG,CAAC+U,UAAU,EAAEnR,KAAK;IAAE,CAAC,CAAC;EACpE;EACA,OAAO8G,IAAI,CAAChD,GAAG,CAAC;AACpB;AACA;AACA;AACA;AACA,MAAMsN,mBAAmB,CAAC;EACtBtH,QAAQ;EACRvP,WAAWA,CAACuP,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAJ,SAASA,CAACD,cAAc,EAAE3C,IAAI,EAAE;IAC5B,OAAO/N,qBAAqB,CAAC,IAAI,CAAC+Q,QAAQ,EAAE,MAAMkH,iBAAiB,CAACvH,cAAc,EAAGE,iBAAiB,IAAK7C,IAAI,CAAC7C,MAAM,CAAC0F,iBAAiB,CAAC,CAAC,CAAC;EAC/I;EACA,OAAOjF,IAAI,YAAA2M,4BAAAzM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwM,mBAAmB,EAh+B7BnZ,EAAE,CAAA4M,QAAA,CAg+B6C5M,EAAE,CAACsT,mBAAmB;EAAA;EAC9J,OAAOzG,KAAK,kBAj+B6E7M,EAAE,CAAA8M,kBAAA;IAAA/E,KAAA,EAi+BYoR,mBAAmB;IAAApM,OAAA,EAAnBoM,mBAAmB,CAAA1M;EAAA;AAC9H;AACA;EAAA,QAAAvJ,SAAA,oBAAAA,SAAA,KAn+B6FlD,EAAE,CAAAgN,iBAAA,CAm+BJmM,mBAAmB,EAAc,CAAC;IACjHjP,IAAI,EAAE/J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE+J,IAAI,EAAElK,EAAE,CAACsT;EAAoB,CAAC,CAAC;AAAA;;AAEpE;AACA;AACA;AACA;AACA;AACA,IAAI+F,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB;EACjFA,eAAe,CAACA,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,GAAG,yBAAyB;EAC3FA,eAAe,CAACA,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EAC7EA,eAAe,CAACA,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EACrEA,eAAe,CAACA,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB;EACvFA,eAAe,CAACA,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AAC3D,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACtC,OAAO;IACHC,KAAK,EAAEF,IAAI;IACXG,UAAU,EAAEF;EAChB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAC,GAAGC,QAAQ,EAAE;EACpC,IAAI1W,SAAS,EAAE;IACX,MAAM2W,YAAY,GAAG,IAAI1G,GAAG,CAACyG,QAAQ,CAACxY,GAAG,CAAE0Y,CAAC,IAAKA,CAAC,CAACL,KAAK,CAAC,CAAC;IAC1D,IAAII,YAAY,CAACrW,GAAG,CAAC6V,eAAe,CAACU,gBAAgB,CAAC,IAClDF,YAAY,CAACrW,GAAG,CAAC6V,eAAe,CAACW,uBAAuB,CAAC,EAAE;MAC3D,MAAM,IAAI1U,KAAK,CAACpC,SAAS,GACnB,uJAAuJ,GACvJ,EAAE,CAAC;IACb;EACJ;EACA,MAAMsW,SAAS,GAAG,CACd/N,UAAU,EACV6K,cAAc,EACdzD,sBAAsB,EACtB;IAAEoH,OAAO,EAAEnY,WAAW;IAAEoY,WAAW,EAAErH;EAAuB,CAAC,EAC7D;IACIoH,OAAO,EAAElY,WAAW;IACpBoY,UAAU,EAAEA,CAAA,KAAM;MACd,OAAO/Z,MAAM,CAACkN,aAAa,EAAE;QAAEI,QAAQ,EAAE;MAAK,CAAC,CAAC,IAAItN,MAAM,CAACkW,cAAc,CAAC;IAC9E;EACJ,CAAC,EACD;IACI2D,OAAO,EAAElI,oBAAoB;IAC7BqI,QAAQ,EAAErB,iBAAiB;IAC3BsB,KAAK,EAAE;EACX,CAAC,EACD;IAAEJ,OAAO,EAAEnC,YAAY;IAAEsC,QAAQ,EAAE;EAAK,CAAC,EACzC;IAAEH,OAAO,EAAE9B,sBAAsB;IAAEmC,QAAQ,EAAElC;EAAwB,CAAC,CACzE;EACD,KAAK,MAAMmC,OAAO,IAAIX,QAAQ,EAAE;IAC5BJ,SAAS,CAACzU,IAAI,CAAC,GAAGwV,OAAO,CAACb,UAAU,CAAC;EACzC;EACA,OAAO1Y,wBAAwB,CAACwY,SAAS,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOnB,eAAe,CAACD,eAAe,CAACqB,YAAY,EAAED,cAAc,CAACrZ,GAAG,CAAEwQ,aAAa,IAAK;IACvF,OAAO;MACHqI,OAAO,EAAElI,oBAAoB;MAC7BqI,QAAQ,EAAExI,aAAa;MACvByI,KAAK,EAAE;IACX,CAAC;EACL,CAAC,CAAC,CAAC;AACP;AACA,MAAMM,qBAAqB,GAAG,IAAIra,cAAc,CAAC4C,SAAS,GAAG,uBAAuB,GAAG,EAAE,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0X,sBAAsBA,CAAA,EAAG;EAC9B;EACA;EACA;EACA;EACA;EACA,OAAOtB,eAAe,CAACD,eAAe,CAACwB,kBAAkB,EAAE,CACvD;IACIZ,OAAO,EAAEU,qBAAqB;IAC9BR,UAAU,EAAEhI;EAChB,CAAC,EACD;IACI8H,OAAO,EAAElI,oBAAoB;IAC7BmI,WAAW,EAAES,qBAAqB;IAClCN,KAAK,EAAE;EACX,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqBA,CAAC;EAAEvC,UAAU;EAAEW;AAAY,CAAC,EAAE;EACxD,MAAMM,SAAS,GAAG,EAAE;EACpB,IAAIjB,UAAU,KAAKzT,SAAS,EAAE;IAC1B0U,SAAS,CAACzU,IAAI,CAAC;MAAEkV,OAAO,EAAEjC,gBAAgB;MAAEoC,QAAQ,EAAE7B;IAAW,CAAC,CAAC;EACvE;EACA,IAAIW,UAAU,KAAKpU,SAAS,EAAE;IAC1B0U,SAAS,CAACzU,IAAI,CAAC;MAAEkV,OAAO,EAAE/B,gBAAgB;MAAEkC,QAAQ,EAAElB;IAAW,CAAC,CAAC;EACvE;EACA,OAAOI,eAAe,CAACD,eAAe,CAACW,uBAAuB,EAAER,SAAS,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,oBAAoBA,CAAA,EAAG;EAC5B,OAAOzB,eAAe,CAACD,eAAe,CAACU,gBAAgB,EAAE,CACrD;IACIE,OAAO,EAAEnC,YAAY;IACrBsC,QAAQ,EAAE;EACd,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,gBAAgBA,CAAA,EAAG;EACxB,OAAO1B,eAAe,CAACD,eAAe,CAAC4B,YAAY,EAAE,CACjDjH,kBAAkB,EAClB;IAAEiG,OAAO,EAAEpG,oBAAoB;IAAEsG,UAAU,EAAErG;EAAqB,CAAC,EACnE;IAAEmG,OAAO,EAAElI,oBAAoB;IAAEqI,QAAQ,EAAE1E,kBAAkB;IAAE2E,KAAK,EAAE;EAAK,CAAC,CAC/E,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,yBAAyBA,CAAA,EAAG;EACjC,OAAO5B,eAAe,CAACD,eAAe,CAAC8B,qBAAqB,EAAE,CAC1D;IACIlB,OAAO,EAAElY,WAAW;IACpBoY,UAAU,EAAEA,CAAA,KAAM;MACd,MAAMiB,iBAAiB,GAAGhb,MAAM,CAAC0B,WAAW,EAAE;QAAEuZ,QAAQ,EAAE,IAAI;QAAE3N,QAAQ,EAAE;MAAK,CAAC,CAAC;MACjF,IAAIxK,SAAS,IAAIkY,iBAAiB,KAAK,IAAI,EAAE;QACzC,MAAM,IAAI9V,KAAK,CAAC,kGAAkG,CAAC;MACvH;MACA,OAAO8V,iBAAiB;IAC5B;EACJ,CAAC,CACJ,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAAA,EAAG;EACjB,OAAOhC,eAAe,CAACD,eAAe,CAACkC,KAAK,EAAE,CAC1ChO,YAAY,EACZ;IAAE0M,OAAO,EAAE3M,aAAa;IAAE4M,WAAW,EAAE3M;EAAa,CAAC,EACrD;IAAE0M,OAAO,EAAElY,WAAW;IAAEmY,WAAW,EAAE3M;EAAa,CAAC,CACtD,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiO,oBAAoB,CAAC;EACvB;AACJ;AACA;EACI,OAAOC,OAAOA,CAAA,EAAG;IACb,OAAO;MACHC,QAAQ,EAAEF,oBAAoB;MAC9BhC,SAAS,EAAE,CAACuB,oBAAoB,CAAC,CAAC,CAACrB,UAAU;IACjD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOiC,WAAWA,CAACzU,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO;MACHwU,QAAQ,EAAEF,oBAAoB;MAC9BhC,SAAS,EAAEsB,qBAAqB,CAAC5T,OAAO,CAAC,CAACwS;IAC9C,CAAC;EACL;EACA,OAAOjN,IAAI,YAAAmP,6BAAAjP,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6O,oBAAoB;EAAA;EACvH,OAAOK,IAAI,kBA9uC8E7b,EAAE,CAAA8b,gBAAA;IAAA5R,IAAA,EA8uCSsR;EAAoB;EACxH,OAAOO,IAAI,kBA/uC8E/b,EAAE,CAAAgc,gBAAA;IAAAxC,SAAA,EA+uC0C,CAC7HL,mBAAmB,EACnB;MAAEc,OAAO,EAAEnI,iBAAiB;MAAEoI,WAAW,EAAEf,mBAAmB;MAAEkB,KAAK,EAAE;IAAK,CAAC,EAC7E;MAAEJ,OAAO,EAAE9B,sBAAsB;MAAEmC,QAAQ,EAAElC;IAAwB,CAAC,EACtE0C,qBAAqB,CAAC;MAClBvC,UAAU,EAAER,wBAAwB;MACpCmB,UAAU,EAAEjB;IAChB,CAAC,CAAC,CAACyB,UAAU,EACb;MAAEO,OAAO,EAAEnC,YAAY;MAAEsC,QAAQ,EAAE;IAAK,CAAC;EAC5C;AACT;AACA;EAAA,QAAAlX,SAAA,oBAAAA,SAAA,KA1vC6FlD,EAAE,CAAAgN,iBAAA,CA0vCJwO,oBAAoB,EAAc,CAAC;IAClHtR,IAAI,EAAEjJ,QAAQ;IACd2M,IAAI,EAAE,CAAC;MACC4L,SAAS,EAAE,CACPL,mBAAmB,EACnB;QAAEc,OAAO,EAAEnI,iBAAiB;QAAEoI,WAAW,EAAEf,mBAAmB;QAAEkB,KAAK,EAAE;MAAK,CAAC,EAC7E;QAAEJ,OAAO,EAAE9B,sBAAsB;QAAEmC,QAAQ,EAAElC;MAAwB,CAAC,EACtE0C,qBAAqB,CAAC;QAClBvC,UAAU,EAAER,wBAAwB;QACpCmB,UAAU,EAAEjB;MAChB,CAAC,CAAC,CAACyB,UAAU,EACb;QAAEO,OAAO,EAAEnC,YAAY;QAAEsC,QAAQ,EAAE;MAAK,CAAC;IAEjD,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,gBAAgB,CAAC;EACnB,OAAOxP,IAAI,YAAAyP,yBAAAvP,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsP,gBAAgB;EAAA;EACnH,OAAOJ,IAAI,kBArxC8E7b,EAAE,CAAA8b,gBAAA;IAAA5R,IAAA,EAqxCS+R;EAAgB;EACpH,OAAOF,IAAI,kBAtxC8E/b,EAAE,CAAAgc,gBAAA;IAAAxC,SAAA,EAsxCsC,CAACG,iBAAiB,CAACiB,sBAAsB,CAAC,CAAC,CAAC;EAAC;AAClL;AACA;EAAA,QAAA1X,SAAA,oBAAAA,SAAA,KAxxC6FlD,EAAE,CAAAgN,iBAAA,CAwxCJiP,gBAAgB,EAAc,CAAC;IAC9G/R,IAAI,EAAEjJ,QAAQ;IACd2M,IAAI,EAAE,CAAC;MACC;AACpB;AACA;AACA;MACoB4L,SAAS,EAAE,CAACG,iBAAiB,CAACiB,sBAAsB,CAAC,CAAC,CAAC;IAC3D,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,qBAAqB,CAAC;EACxB,OAAO1P,IAAI,YAAA2P,8BAAAzP,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwP,qBAAqB;EAAA;EACxH,OAAON,IAAI,kBA7yC8E7b,EAAE,CAAA8b,gBAAA;IAAA5R,IAAA,EA6yCSiS;EAAqB;EACzH,OAAOJ,IAAI,kBA9yC8E/b,EAAE,CAAAgc,gBAAA;IAAAxC,SAAA,EA8yC2C,CAACwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;EAAC;AACzK;AACA;EAAA,QAAAxW,SAAA,oBAAAA,SAAA,KAhzC6FlD,EAAE,CAAAgN,iBAAA,CAgzCJmP,qBAAqB,EAAc,CAAC;IACnHjS,IAAI,EAAEjJ,QAAQ;IACd2M,IAAI,EAAE,CAAC;MACC4L,SAAS,EAAE,CAACwB,gBAAgB,CAAC,CAAC,CAACtB,UAAU;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASnM,YAAY,EAAEuE,iBAAiB,EAAEE,yBAAyB,EAAEjQ,WAAW,EAAE0J,UAAU,EAAE0Q,qBAAqB,EAAEF,gBAAgB,EAAET,oBAAoB,EAAE1T,WAAW,EAAEF,gBAAgB,EAAEqD,iBAAiB,EAAEX,aAAa,EAAE+O,eAAe,EAAEvX,WAAW,EAAE+I,kBAAkB,EAAE7I,WAAW,EAAE6Q,sBAAsB,EAAE/L,UAAU,EAAEkC,WAAW,EAAE+B,YAAY,EAAER,gBAAgB,EAAEe,cAAc,EAAE/F,oBAAoB,EAAE+Q,cAAc,EAAE6B,sBAAsB,EAAEnE,kBAAkB,EAAE2B,gBAAgB,EAAE1D,gCAAgC,EAAE0H,iBAAiB,EAAE2B,SAAS,EAAEd,gBAAgB,EAAEI,sBAAsB,EAAEI,gBAAgB,EAAED,oBAAoB,EAAEG,yBAAyB,EAAEJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}