.reports-container {
  padding: 20px;
}

.controls-card {
  margin-bottom: 20px;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  align-items: start;
}

.button-container {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  align-items: center;
  grid-column: 1 / -1;
  margin-top: 10px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.report-content {
  margin-top: 20px;
}

.date-range {
  color: #666;
  margin-bottom: 20px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  margin: 10px 0;
}

.summary-details {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #666;
}

.table-card {
  margin-bottom: 20px;
}

table {
  width: 100%;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.no-data mat-icon {
  font-size: 48px;
  height: 48px;
  width: 48px;
  margin-bottom: 16px;
}

/* Print styles */
@media print {
  .controls-card,
  button {
    display: none !important;
  }

  .report-content {
    page-break-inside: avoid;
  }
}