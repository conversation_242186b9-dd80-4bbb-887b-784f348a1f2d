{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc'\n  }\n};\nconst DEFAULT_FILE_OPTIONS = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false\n};\nexport default class StorageFileApi {\n  constructor(url, headers = {}, bucketId, fetch) {\n    this.url = url;\n    this.headers = headers;\n    this.bucketId = bucketId;\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadOrUpdate(method, path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let body;\n        const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n        let headers = Object.assign(Object.assign({}, this.headers), method === 'POST' && {\n          'x-upsert': String(options.upsert)\n        });\n        const metadata = options.metadata;\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n          if (metadata) {\n            headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n          }\n        }\n        if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n          headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n        }\n        const cleanPath = this._removeEmptyFolders(path);\n        const _path = this._getFinalPath(cleanPath);\n        const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({\n          method,\n          body: body,\n          headers\n        }, (options === null || options === void 0 ? void 0 : options.duplex) ? {\n          duplex: options.duplex\n        } : {}));\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              id: data.Id,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  upload(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadToSignedUrl(path, token, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const cleanPath = this._removeEmptyFolders(path);\n      const _path = this._getFinalPath(cleanPath);\n      const url = new URL(this.url + `/object/upload/sign/${_path}`);\n      url.searchParams.set('token', token);\n      try {\n        let body;\n        const options = Object.assign({\n          upsert: DEFAULT_FILE_OPTIONS.upsert\n        }, fileOptions);\n        const headers = Object.assign(Object.assign({}, this.headers), {\n          'x-upsert': String(options.upsert)\n        });\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n        }\n        const res = yield this.fetch(url.toString(), {\n          method: 'PUT',\n          body: body,\n          headers\n        });\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  createSignedUploadUrl(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        const headers = Object.assign({}, this.headers);\n        if (options === null || options === void 0 ? void 0 : options.upsert) {\n          headers['x-upsert'] = 'true';\n        }\n        const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, {\n          headers\n        });\n        const url = new URL(this.url + data.url);\n        const token = url.searchParams.get('token');\n        if (!token) {\n          throw new StorageError('No token returned by API');\n        }\n        return {\n          data: {\n            signedUrl: url.toString(),\n            path,\n            token\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  update(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  move(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/move`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  copy(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/copy`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data: {\n            path: data.Key\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  createSignedUrl(path, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({\n          expiresIn\n        }, (options === null || options === void 0 ? void 0 : options.transform) ? {\n          transform: options.transform\n        } : {}), {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n        data = {\n          signedUrl\n        };\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  createSignedUrls(paths, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, {\n          expiresIn,\n          paths\n        }, {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        return {\n          data: data.map(datum => Object.assign(Object.assign({}, datum), {\n            signedUrl: datum.signedURL ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`) : null\n          })),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  download(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n      const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n      const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n      const queryString = transformationQuery ? `?${transformationQuery}` : '';\n      try {\n        const _path = this._getFinalPath(path);\n        const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n          headers: this.headers,\n          noResolveJson: true\n        });\n        const data = yield res.blob();\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  info(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: recursiveToCamel(data),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  exists(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        yield head(this.fetch, `${this.url}/object/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: true,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error) && error instanceof StorageUnknownError) {\n          const originalError = error.originalError;\n          if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n            return {\n              data: false,\n              error\n            };\n          }\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(path, options) {\n    const _path = this._getFinalPath(path);\n    const _queryString = [];\n    const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `download=${options.download === true ? '' : options.download}` : '';\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam);\n    }\n    const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n    const renderPath = wantsTransformation ? 'render/image' : 'object';\n    const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery);\n    }\n    let queryString = _queryString.join('&');\n    if (queryString !== '') {\n      queryString = `?${queryString}`;\n    }\n    return {\n      data: {\n        publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`)\n      }\n    };\n  }\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  remove(paths) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, {\n          prefixes: paths\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  list(path, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), {\n          prefix: path || ''\n        });\n        const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, {\n          headers: this.headers\n        }, parameters);\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  encodeMetadata(metadata) {\n    return JSON.stringify(metadata);\n  }\n  toBase64(data) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64');\n    }\n    return btoa(data);\n  }\n  _getFinalPath(path) {\n    return `${this.bucketId}/${path}`;\n  }\n  _removeEmptyFolders(path) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n  }\n  transformOptsToQueryString(transform) {\n    const params = [];\n    if (transform.width) {\n      params.push(`width=${transform.width}`);\n    }\n    if (transform.height) {\n      params.push(`height=${transform.height}`);\n    }\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`);\n    }\n    if (transform.format) {\n      params.push(`format=${transform.format}`);\n    }\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`);\n    }\n    return params.join('&');\n  }\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "isStorageError", "StorageError", "StorageUnknownError", "get", "head", "post", "remove", "recursiveToCamel", "resolveFetch", "DEFAULT_SEARCH_OPTIONS", "limit", "offset", "sortBy", "column", "order", "DEFAULT_FILE_OPTIONS", "cacheControl", "contentType", "upsert", "StorageFileApi", "constructor", "url", "headers", "bucketId", "fetch", "uploadOrUpdate", "method", "path", "fileBody", "fileOptions", "body", "options", "Object", "assign", "String", "metadata", "Blob", "FormData", "append", "encodeMetadata", "toBase64", "cleanPath", "_removeEmptyFolders", "_path", "_get<PERSON><PERSON><PERSON><PERSON>", "res", "duplex", "data", "json", "ok", "id", "Id", "fullPath", "Key", "error", "upload", "uploadToSignedUrl", "token", "URL", "searchParams", "set", "toString", "createSignedUploadUrl", "signedUrl", "update", "move", "fromPath", "to<PERSON><PERSON>", "sourceKey", "destinationKey", "destinationBucket", "copy", "createSignedUrl", "expiresIn", "transform", "downloadQueryParam", "download", "encodeURI", "signedURL", "createSignedUrls", "paths", "map", "datum", "wantsTransformation", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>y", "transformOptsToQueryString", "queryString", "noResolveJson", "blob", "info", "exists", "originalError", "includes", "status", "getPublicUrl", "_queryString", "push", "join", "publicUrl", "prefixes", "list", "parameters", "prefix", "JSON", "stringify", "<PERSON><PERSON><PERSON>", "from", "btoa", "replace", "params", "width", "height", "resize", "format", "quality"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n    limit: 100,\n    offset: 0,\n    sortBy: {\n        column: 'name',\n        order: 'asc',\n    },\n};\nconst DEFAULT_FILE_OPTIONS = {\n    cacheControl: '3600',\n    contentType: 'text/plain;charset=UTF-8',\n    upsert: false,\n};\nexport default class StorageFileApi {\n    constructor(url, headers = {}, bucketId, fetch) {\n        this.url = url;\n        this.headers = headers;\n        this.bucketId = bucketId;\n        this.fetch = resolveFetch(fetch);\n    }\n    /**\n     * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n     *\n     * @param method HTTP method.\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadOrUpdate(method, path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let body;\n                const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n                let headers = Object.assign(Object.assign({}, this.headers), (method === 'POST' && { 'x-upsert': String(options.upsert) }));\n                const metadata = options.metadata;\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                    if (metadata) {\n                        body.append('metadata', this.encodeMetadata(metadata));\n                    }\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                    if (metadata) {\n                        headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n                    }\n                }\n                if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n                    headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n                }\n                const cleanPath = this._removeEmptyFolders(path);\n                const _path = this._getFinalPath(cleanPath);\n                const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({ method, body: body, headers }, ((options === null || options === void 0 ? void 0 : options.duplex) ? { duplex: options.duplex } : {})));\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, id: data.Id, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Uploads a file to an existing bucket.\n     *\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    upload(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Upload a file with a token generated from `createSignedUploadUrl`.\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param token The token generated from `createSignedUploadUrl`\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    uploadToSignedUrl(path, token, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const cleanPath = this._removeEmptyFolders(path);\n            const _path = this._getFinalPath(cleanPath);\n            const url = new URL(this.url + `/object/upload/sign/${_path}`);\n            url.searchParams.set('token', token);\n            try {\n                let body;\n                const options = Object.assign({ upsert: DEFAULT_FILE_OPTIONS.upsert }, fileOptions);\n                const headers = Object.assign(Object.assign({}, this.headers), { 'x-upsert': String(options.upsert) });\n                if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append('cacheControl', options.cacheControl);\n                    body.append('', fileBody);\n                }\n                else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append('cacheControl', options.cacheControl);\n                }\n                else {\n                    body = fileBody;\n                    headers['cache-control'] = `max-age=${options.cacheControl}`;\n                    headers['content-type'] = options.contentType;\n                }\n                const res = yield this.fetch(url.toString(), {\n                    method: 'PUT',\n                    body: body,\n                    headers,\n                });\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: { path: cleanPath, fullPath: data.Key },\n                        error: null,\n                    };\n                }\n                else {\n                    const error = data;\n                    return { data: null, error };\n                }\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed upload URL.\n     * Signed upload URLs can be used to upload files to the bucket without further authentication.\n     * They are valid for 2 hours.\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n     */\n    createSignedUploadUrl(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                const headers = Object.assign({}, this.headers);\n                if (options === null || options === void 0 ? void 0 : options.upsert) {\n                    headers['x-upsert'] = 'true';\n                }\n                const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, { headers });\n                const url = new URL(this.url + data.url);\n                const token = url.searchParams.get('token');\n                if (!token) {\n                    throw new StorageError('No token returned by API');\n                }\n                return { data: { signedUrl: url.toString(), path, token }, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Replaces an existing file at the specified path with a new one.\n     *\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */\n    update(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function* () {\n            return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Moves an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n     * @param options The destination options.\n     */\n    move(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/move`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Copies an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n     * @param options The destination options.\n     */\n    copy(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/copy`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket,\n                }, { headers: this.headers });\n                return { data: { path: data.Key }, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    createSignedUrl(path, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                let _path = this._getFinalPath(path);\n                let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({ expiresIn }, ((options === null || options === void 0 ? void 0 : options.transform) ? { transform: options.transform } : {})), { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n                data = { signedUrl };\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n     * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     */\n    createSignedUrls(paths, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, { expiresIn, paths }, { headers: this.headers });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n                    ? `&download=${options.download === true ? '' : options.download}`\n                    : '';\n                return {\n                    data: data.map((datum) => (Object.assign(Object.assign({}, datum), { signedUrl: datum.signedURL\n                            ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`)\n                            : null }))),\n                    error: null,\n                };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n     *\n     * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    download(path, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n            const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n            const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n            const queryString = transformationQuery ? `?${transformationQuery}` : '';\n            try {\n                const _path = this._getFinalPath(path);\n                const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n                    headers: this.headers,\n                    noResolveJson: true,\n                });\n                const data = yield res.blob();\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing file.\n     * @param path\n     */\n    info(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: recursiveToCamel(data), error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Checks the existence of a file.\n     * @param path\n     */\n    exists(path) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const _path = this._getFinalPath(path);\n            try {\n                yield head(this.fetch, `${this.url}/object/${_path}`, {\n                    headers: this.headers,\n                });\n                return { data: true, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error) && error instanceof StorageUnknownError) {\n                    const originalError = error.originalError;\n                    if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n                        return { data: false, error };\n                    }\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n     * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n     *\n     * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n     * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */\n    getPublicUrl(path, options) {\n        const _path = this._getFinalPath(path);\n        const _queryString = [];\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download)\n            ? `download=${options.download === true ? '' : options.download}`\n            : '';\n        if (downloadQueryParam !== '') {\n            _queryString.push(downloadQueryParam);\n        }\n        const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n        const renderPath = wantsTransformation ? 'render/image' : 'object';\n        const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n        if (transformationQuery !== '') {\n            _queryString.push(transformationQuery);\n        }\n        let queryString = _queryString.join('&');\n        if (queryString !== '') {\n            queryString = `?${queryString}`;\n        }\n        return {\n            data: { publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`) },\n        };\n    }\n    /**\n     * Deletes files within the same bucket\n     *\n     * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n     */\n    remove(paths) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, { prefixes: paths }, { headers: this.headers });\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Get file metadata\n     * @param id the file id to retrieve metadata\n     */\n    // async getMetadata(\n    //   id: string\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Update file metadata\n     * @param id the file id to update metadata\n     * @param meta the new file metadata\n     */\n    // async updateMetadata(\n    //   id: string,\n    //   meta: Metadata\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await post(\n    //       this.fetch,\n    //       `${this.url}/metadata/${id}`,\n    //       { ...meta },\n    //       { headers: this.headers }\n    //     )\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Lists all the files within a bucket.\n     * @param path The folder path.\n     */\n    list(path, options, parameters) {\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), { prefix: path || '' });\n                const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, { headers: this.headers }, parameters);\n                return { data, error: null };\n            }\n            catch (error) {\n                if (isStorageError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    encodeMetadata(metadata) {\n        return JSON.stringify(metadata);\n    }\n    toBase64(data) {\n        if (typeof Buffer !== 'undefined') {\n            return Buffer.from(data).toString('base64');\n        }\n        return btoa(data);\n    }\n    _getFinalPath(path) {\n        return `${this.bucketId}/${path}`;\n    }\n    _removeEmptyFolders(path) {\n        return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n    }\n    transformOptsToQueryString(transform) {\n        const params = [];\n        if (transform.width) {\n            params.push(`width=${transform.width}`);\n        }\n        if (transform.height) {\n            params.push(`height=${transform.height}`);\n        }\n        if (transform.resize) {\n            params.push(`resize=${transform.resize}`);\n        }\n        if (transform.format) {\n            params.push(`format=${transform.format}`);\n        }\n        if (transform.quality) {\n            params.push(`quality=${transform.quality}`);\n        }\n        return params.join('&');\n    }\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,cAAc,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,eAAe;AACjF,SAASC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAQ,cAAc;AACtD,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,gBAAgB;AAC/D,MAAMC,sBAAsB,GAAG;EAC3BC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;IACJC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE;EACX;AACJ,CAAC;AACD,MAAMC,oBAAoB,GAAG;EACzBC,YAAY,EAAE,MAAM;EACpBC,WAAW,EAAE,0BAA0B;EACvCC,MAAM,EAAE;AACZ,CAAC;AACD,eAAe,MAAMC,cAAc,CAAC;EAChCC,WAAWA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IAC5C,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGhB,YAAY,CAACgB,KAAK,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IAChD,OAAOhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,IAAIiD,IAAI;QACR,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElB,oBAAoB,CAAC,EAAEc,WAAW,CAAC;QACnF,IAAIP,OAAO,GAAGU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,OAAO,CAAC,EAAGI,MAAM,KAAK,MAAM,IAAI;UAAE,UAAU,EAAEQ,MAAM,CAACH,OAAO,CAACb,MAAM;QAAE,CAAE,CAAC;QAC3H,MAAMiB,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;QACjC,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAIR,QAAQ,YAAYQ,IAAI,EAAE;UACzDN,IAAI,GAAG,IAAIO,QAAQ,CAAC,CAAC;UACrBP,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAY,CAAC;UACjD,IAAImB,QAAQ,EAAE;YACVL,IAAI,CAACQ,MAAM,CAAC,UAAU,EAAE,IAAI,CAACC,cAAc,CAACJ,QAAQ,CAAC,CAAC;UAC1D;UACAL,IAAI,CAACQ,MAAM,CAAC,EAAE,EAAEV,QAAQ,CAAC;QAC7B,CAAC,MACI,IAAI,OAAOS,QAAQ,KAAK,WAAW,IAAIT,QAAQ,YAAYS,QAAQ,EAAE;UACtEP,IAAI,GAAGF,QAAQ;UACfE,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAY,CAAC;UACjD,IAAImB,QAAQ,EAAE;YACVL,IAAI,CAACQ,MAAM,CAAC,UAAU,EAAE,IAAI,CAACC,cAAc,CAACJ,QAAQ,CAAC,CAAC;UAC1D;QACJ,CAAC,MACI;UACDL,IAAI,GAAGF,QAAQ;UACfN,OAAO,CAAC,eAAe,CAAC,GAAG,WAAWS,OAAO,CAACf,YAAY,EAAE;UAC5DM,OAAO,CAAC,cAAc,CAAC,GAAGS,OAAO,CAACd,WAAW;UAC7C,IAAIkB,QAAQ,EAAE;YACVb,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAACkB,QAAQ,CAAC,IAAI,CAACD,cAAc,CAACJ,QAAQ,CAAC,CAAC;UACxE;QACJ;QACA,IAAIN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACP,OAAO,EAAE;UAC/EA,OAAO,GAAGU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,OAAO,CAAC,EAAEO,WAAW,CAACP,OAAO,CAAC;QAC5E;QACA,MAAMmB,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACf,IAAI,CAAC;QAChD,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;QAC3C,MAAMI,GAAG,GAAG,MAAM,IAAI,CAACrB,KAAK,CAAC,GAAG,IAAI,CAACH,GAAG,WAAWsB,KAAK,EAAE,EAAEX,MAAM,CAACC,MAAM,CAAC;UAAEP,MAAM;UAAEI,IAAI,EAAEA,IAAI;UAAER;QAAQ,CAAC,EAAG,CAACS,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,MAAM,IAAI;UAAEA,MAAM,EAAEf,OAAO,CAACe;QAAO,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;QACnN,MAAMC,IAAI,GAAG,MAAMF,GAAG,CAACG,IAAI,CAAC,CAAC;QAC7B,IAAIH,GAAG,CAACI,EAAE,EAAE;UACR,OAAO;YACHF,IAAI,EAAE;cAAEpB,IAAI,EAAEc,SAAS;cAAES,EAAE,EAAEH,IAAI,CAACI,EAAE;cAAEC,QAAQ,EAAEL,IAAI,CAACM;YAAI,CAAC;YAC1DC,KAAK,EAAE;UACX,CAAC;QACL,CAAC,MACI;UACD,MAAMA,KAAK,GAAGP,IAAI;UAClB,OAAO;YAAEA,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;MACJ,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAAC5B,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IAChC,OAAOhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,OAAO,IAAI,CAAC4C,cAAc,CAAC,MAAM,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,WAAW,CAAC;IACnE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2B,iBAAiBA,CAAC7B,IAAI,EAAE8B,KAAK,EAAE7B,QAAQ,EAAEC,WAAW,EAAE;IAClD,OAAOhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,MAAM4D,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAACf,IAAI,CAAC;MAChD,MAAMgB,KAAK,GAAG,IAAI,CAACC,aAAa,CAACH,SAAS,CAAC;MAC3C,MAAMpB,GAAG,GAAG,IAAIqC,GAAG,CAAC,IAAI,CAACrC,GAAG,GAAG,uBAAuBsB,KAAK,EAAE,CAAC;MAC9DtB,GAAG,CAACsC,YAAY,CAACC,GAAG,CAAC,OAAO,EAAEH,KAAK,CAAC;MACpC,IAAI;QACA,IAAI3B,IAAI;QACR,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;UAAEf,MAAM,EAAEH,oBAAoB,CAACG;QAAO,CAAC,EAAEW,WAAW,CAAC;QACnF,MAAMP,OAAO,GAAGU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,OAAO,CAAC,EAAE;UAAE,UAAU,EAAEY,MAAM,CAACH,OAAO,CAACb,MAAM;QAAE,CAAC,CAAC;QACtG,IAAI,OAAOkB,IAAI,KAAK,WAAW,IAAIR,QAAQ,YAAYQ,IAAI,EAAE;UACzDN,IAAI,GAAG,IAAIO,QAAQ,CAAC,CAAC;UACrBP,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAY,CAAC;UACjDc,IAAI,CAACQ,MAAM,CAAC,EAAE,EAAEV,QAAQ,CAAC;QAC7B,CAAC,MACI,IAAI,OAAOS,QAAQ,KAAK,WAAW,IAAIT,QAAQ,YAAYS,QAAQ,EAAE;UACtEP,IAAI,GAAGF,QAAQ;UACfE,IAAI,CAACQ,MAAM,CAAC,cAAc,EAAEP,OAAO,CAACf,YAAY,CAAC;QACrD,CAAC,MACI;UACDc,IAAI,GAAGF,QAAQ;UACfN,OAAO,CAAC,eAAe,CAAC,GAAG,WAAWS,OAAO,CAACf,YAAY,EAAE;UAC5DM,OAAO,CAAC,cAAc,CAAC,GAAGS,OAAO,CAACd,WAAW;QACjD;QACA,MAAM4B,GAAG,GAAG,MAAM,IAAI,CAACrB,KAAK,CAACH,GAAG,CAACwC,QAAQ,CAAC,CAAC,EAAE;UACzCnC,MAAM,EAAE,KAAK;UACbI,IAAI,EAAEA,IAAI;UACVR;QACJ,CAAC,CAAC;QACF,MAAMyB,IAAI,GAAG,MAAMF,GAAG,CAACG,IAAI,CAAC,CAAC;QAC7B,IAAIH,GAAG,CAACI,EAAE,EAAE;UACR,OAAO;YACHF,IAAI,EAAE;cAAEpB,IAAI,EAAEc,SAAS;cAAEW,QAAQ,EAAEL,IAAI,CAACM;YAAI,CAAC;YAC7CC,KAAK,EAAE;UACX,CAAC;QACL,CAAC,MACI;UACD,MAAMA,KAAK,GAAGP,IAAI;UAClB,OAAO;YAAEA,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;MACJ,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,qBAAqBA,CAACnC,IAAI,EAAEI,OAAO,EAAE;IACjC,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,IAAI8D,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QACpC,MAAML,OAAO,GAAGU,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACX,OAAO,CAAC;QAC/C,IAAIS,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACb,MAAM,EAAE;UAClEI,OAAO,CAAC,UAAU,CAAC,GAAG,MAAM;QAChC;QACA,MAAMyB,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,uBAAuBsB,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;UAAErB;QAAQ,CAAC,CAAC;QAC/F,MAAMD,GAAG,GAAG,IAAIqC,GAAG,CAAC,IAAI,CAACrC,GAAG,GAAG0B,IAAI,CAAC1B,GAAG,CAAC;QACxC,MAAMoC,KAAK,GAAGpC,GAAG,CAACsC,YAAY,CAACxD,GAAG,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACsD,KAAK,EAAE;UACR,MAAM,IAAIxD,YAAY,CAAC,0BAA0B,CAAC;QACtD;QACA,OAAO;UAAE8C,IAAI,EAAE;YAAEgB,SAAS,EAAE1C,GAAG,CAACwC,QAAQ,CAAC,CAAC;YAAElC,IAAI;YAAE8B;UAAM,CAAC;UAAEH,KAAK,EAAE;QAAK,CAAC;MAC5E,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,MAAMA,CAACrC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IAChC,OAAOhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,OAAO,IAAI,CAAC4C,cAAc,CAAC,KAAK,EAAEE,IAAI,EAAEC,QAAQ,EAAEC,WAAW,CAAC;IAClE,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIoC,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAEpC,OAAO,EAAE;IAC5B,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkE,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,cAAc,EAAE;UAC3DE,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB6C,SAAS,EAAEF,QAAQ;UACnBG,cAAc,EAAEF,MAAM;UACtBG,iBAAiB,EAAEvC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuC;QACjF,CAAC,EAAE;UAAEhD,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC7B,OAAO;UAAEyB,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIiB,IAAIA,CAACL,QAAQ,EAAEC,MAAM,EAAEpC,OAAO,EAAE;IAC5B,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkE,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,cAAc,EAAE;UAC3DE,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvB6C,SAAS,EAAEF,QAAQ;UACnBG,cAAc,EAAEF,MAAM;UACtBG,iBAAiB,EAAEvC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuC;QACjF,CAAC,EAAE;UAAEhD,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC7B,OAAO;UAAEyB,IAAI,EAAE;YAAEpB,IAAI,EAAEoB,IAAI,CAACM;UAAI,CAAC;UAAEC,KAAK,EAAE;QAAK,CAAC;MACpD,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,eAAeA,CAAC7C,IAAI,EAAE8C,SAAS,EAAE1C,OAAO,EAAE;IACtC,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,IAAI8D,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QACpC,IAAIoB,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,gBAAgBsB,KAAK,EAAE,EAAEX,MAAM,CAACC,MAAM,CAAC;UAAEwC;QAAU,CAAC,EAAG,CAAC1C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,SAAS,IAAI;UAAEA,SAAS,EAAE3C,OAAO,CAAC2C;QAAU,CAAC,GAAG,CAAC,CAAE,CAAC,EAAE;UAAEpD,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC/O,MAAMqD,kBAAkB,GAAG,CAAC5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6C,QAAQ,IACxF,aAAa7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAChE,EAAE;QACR,MAAMb,SAAS,GAAGc,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,GAAG0B,IAAI,CAAC+B,SAAS,GAAGH,kBAAkB,EAAE,CAAC;QAChF5B,IAAI,GAAG;UAAEgB;QAAU,CAAC;QACpB,OAAO;UAAEhB,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyB,gBAAgBA,CAACC,KAAK,EAAEP,SAAS,EAAE1C,OAAO,EAAE;IACxC,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkE,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,gBAAgB,IAAI,CAACE,QAAQ,EAAE,EAAE;UAAEkD,SAAS;UAAEO;QAAM,CAAC,EAAE;UAAE1D,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAChI,MAAMqD,kBAAkB,GAAG,CAAC5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6C,QAAQ,IACxF,aAAa7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAChE,EAAE;QACR,OAAO;UACH7B,IAAI,EAAEA,IAAI,CAACkC,GAAG,CAAEC,KAAK,IAAMlD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiD,KAAK,CAAC,EAAE;YAAEnB,SAAS,EAAEmB,KAAK,CAACJ,SAAS,GACrFD,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,GAAG6D,KAAK,CAACJ,SAAS,GAAGH,kBAAkB,EAAE,CAAC,GAC/D;UAAK,CAAC,CAAE,CAAC;UACnBrB,KAAK,EAAE;QACX,CAAC;MACL,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsB,QAAQA,CAACjD,IAAI,EAAEI,OAAO,EAAE;IACpB,OAAOlD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,MAAMsG,mBAAmB,GAAG,QAAQpD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,SAAS,CAAC,KAAK,WAAW;MACxH,MAAMU,UAAU,GAAGD,mBAAmB,GAAG,4BAA4B,GAAG,QAAQ;MAChF,MAAME,mBAAmB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAACvD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,SAAS,KAAK,CAAC,CAAC,CAAC;MACxI,MAAMa,WAAW,GAAGF,mBAAmB,GAAG,IAAIA,mBAAmB,EAAE,GAAG,EAAE;MACxE,IAAI;QACA,MAAM1C,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;QACtC,MAAMkB,GAAG,GAAG,MAAM1C,GAAG,CAAC,IAAI,CAACqB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,IAAI+D,UAAU,IAAIzC,KAAK,GAAG4C,WAAW,EAAE,EAAE;UAClFjE,OAAO,EAAE,IAAI,CAACA,OAAO;UACrBkE,aAAa,EAAE;QACnB,CAAC,CAAC;QACF,MAAMzC,IAAI,GAAG,MAAMF,GAAG,CAAC4C,IAAI,CAAC,CAAC;QAC7B,OAAO;UAAE1C,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIoC,IAAIA,CAAC/D,IAAI,EAAE;IACP,OAAO9C,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,MAAM8D,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;MACtC,IAAI;QACA,MAAMoB,IAAI,GAAG,MAAM5C,GAAG,CAAC,IAAI,CAACqB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,gBAAgBsB,KAAK,EAAE,EAAE;UACnErB,OAAO,EAAE,IAAI,CAACA;QAClB,CAAC,CAAC;QACF,OAAO;UAAEyB,IAAI,EAAExC,gBAAgB,CAACwC,IAAI,CAAC;UAAEO,KAAK,EAAE;QAAK,CAAC;MACxD,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIqC,MAAMA,CAAChE,IAAI,EAAE;IACT,OAAO9C,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,MAAM8D,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;MACtC,IAAI;QACA,MAAMvB,IAAI,CAAC,IAAI,CAACoB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,WAAWsB,KAAK,EAAE,EAAE;UAClDrB,OAAO,EAAE,IAAI,CAACA;QAClB,CAAC,CAAC;QACF,OAAO;UAAEyB,IAAI,EAAE,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MACtC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,IAAIA,KAAK,YAAYpD,mBAAmB,EAAE;UAC/D,MAAM0F,aAAa,GAAGtC,KAAK,CAACsC,aAAa;UACzC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,QAAQ,CAACD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACE,MAAM,CAAC,EAAE;YACzG,OAAO;cAAE/C,IAAI,EAAE,KAAK;cAAEO;YAAM,CAAC;UACjC;QACJ;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIyC,YAAYA,CAACpE,IAAI,EAAEI,OAAO,EAAE;IACxB,MAAMY,KAAK,GAAG,IAAI,CAACC,aAAa,CAACjB,IAAI,CAAC;IACtC,MAAMqE,YAAY,GAAG,EAAE;IACvB,MAAMrB,kBAAkB,GAAG,CAAC5C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC6C,QAAQ,IACxF,YAAY7C,OAAO,CAAC6C,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG7C,OAAO,CAAC6C,QAAQ,EAAE,GAC/D,EAAE;IACR,IAAID,kBAAkB,KAAK,EAAE,EAAE;MAC3BqB,YAAY,CAACC,IAAI,CAACtB,kBAAkB,CAAC;IACzC;IACA,MAAMQ,mBAAmB,GAAG,QAAQpD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,SAAS,CAAC,KAAK,WAAW;IACxH,MAAMU,UAAU,GAAGD,mBAAmB,GAAG,cAAc,GAAG,QAAQ;IAClE,MAAME,mBAAmB,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAACvD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2C,SAAS,KAAK,CAAC,CAAC,CAAC;IACxI,IAAIW,mBAAmB,KAAK,EAAE,EAAE;MAC5BW,YAAY,CAACC,IAAI,CAACZ,mBAAmB,CAAC;IAC1C;IACA,IAAIE,WAAW,GAAGS,YAAY,CAACE,IAAI,CAAC,GAAG,CAAC;IACxC,IAAIX,WAAW,KAAK,EAAE,EAAE;MACpBA,WAAW,GAAG,IAAIA,WAAW,EAAE;IACnC;IACA,OAAO;MACHxC,IAAI,EAAE;QAAEoD,SAAS,EAAEtB,SAAS,CAAC,GAAG,IAAI,CAACxD,GAAG,IAAI+D,UAAU,WAAWzC,KAAK,GAAG4C,WAAW,EAAE;MAAE;IAC5F,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIjF,MAAMA,CAAC0E,KAAK,EAAE;IACV,OAAOnG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMkE,IAAI,GAAG,MAAMzC,MAAM,CAAC,IAAI,CAACkB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,WAAW,IAAI,CAACE,QAAQ,EAAE,EAAE;UAAE6E,QAAQ,EAAEpB;QAAM,CAAC,EAAE;UAAE1D,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,CAAC;QAC5H,OAAO;UAAEyB,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACI+C,IAAIA,CAAC1E,IAAI,EAAEI,OAAO,EAAEuE,UAAU,EAAE;IAC5B,OAAOzH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAMiD,IAAI,GAAGE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExB,sBAAsB,CAAC,EAAEsB,OAAO,CAAC,EAAE;UAAEwE,MAAM,EAAE5E,IAAI,IAAI;QAAG,CAAC,CAAC;QACrH,MAAMoB,IAAI,GAAG,MAAM1C,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,GAAG,IAAI,CAACH,GAAG,gBAAgB,IAAI,CAACE,QAAQ,EAAE,EAAEO,IAAI,EAAE;UAAER,OAAO,EAAE,IAAI,CAACA;QAAQ,CAAC,EAAEgF,UAAU,CAAC;QAC5H,OAAO;UAAEvD,IAAI;UAAEO,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAItD,cAAc,CAACsD,KAAK,CAAC,EAAE;UACvB,OAAO;YAAEP,IAAI,EAAE,IAAI;YAAEO;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IACJ,CAAC,CAAC;EACN;EACAf,cAAcA,CAACJ,QAAQ,EAAE;IACrB,OAAOqE,IAAI,CAACC,SAAS,CAACtE,QAAQ,CAAC;EACnC;EACAK,QAAQA,CAACO,IAAI,EAAE;IACX,IAAI,OAAO2D,MAAM,KAAK,WAAW,EAAE;MAC/B,OAAOA,MAAM,CAACC,IAAI,CAAC5D,IAAI,CAAC,CAACc,QAAQ,CAAC,QAAQ,CAAC;IAC/C;IACA,OAAO+C,IAAI,CAAC7D,IAAI,CAAC;EACrB;EACAH,aAAaA,CAACjB,IAAI,EAAE;IAChB,OAAO,GAAG,IAAI,CAACJ,QAAQ,IAAII,IAAI,EAAE;EACrC;EACAe,mBAAmBA,CAACf,IAAI,EAAE;IACtB,OAAOA,IAAI,CAACkF,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAC5D;EACAvB,0BAA0BA,CAACZ,SAAS,EAAE;IAClC,MAAMoC,MAAM,GAAG,EAAE;IACjB,IAAIpC,SAAS,CAACqC,KAAK,EAAE;MACjBD,MAAM,CAACb,IAAI,CAAC,SAASvB,SAAS,CAACqC,KAAK,EAAE,CAAC;IAC3C;IACA,IAAIrC,SAAS,CAACsC,MAAM,EAAE;MAClBF,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACsC,MAAM,EAAE,CAAC;IAC7C;IACA,IAAItC,SAAS,CAACuC,MAAM,EAAE;MAClBH,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACuC,MAAM,EAAE,CAAC;IAC7C;IACA,IAAIvC,SAAS,CAACwC,MAAM,EAAE;MAClBJ,MAAM,CAACb,IAAI,CAAC,UAAUvB,SAAS,CAACwC,MAAM,EAAE,CAAC;IAC7C;IACA,IAAIxC,SAAS,CAACyC,OAAO,EAAE;MACnBL,MAAM,CAACb,IAAI,CAAC,WAAWvB,SAAS,CAACyC,OAAO,EAAE,CAAC;IAC/C;IACA,OAAOL,MAAM,CAACZ,IAAI,CAAC,GAAG,CAAC;EAC3B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}