import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { Rating, RatingSummary } from '../models/rating.model';
import { Ride } from '../models/ride.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class RatingService {
  private supabase: SupabaseClient;
  private ratingsSubject = new BehaviorSubject<Rating[]>([]);
  ratings$ = this.ratingsSubject.asObservable();

  constructor(private authService: AuthService) {
    this.supabase = authService.supabase;
  }

  /**
   * Submit a rating for a ride
   * @param rideId The ID of the ride
   * @param raterId The ID of the user giving the rating
   * @param ratedId The ID of the user being rated
   * @param rating The rating value (1-5)
   * @param feedback Optional feedback text
   */
  async submitRating(rideId: string, raterId: string, ratedId: string, rating: number, feedback?: string): Promise<Rating> {
    try {
      const { data, error } = await this.supabase
        .from('ratings')
        .insert([
          {
            ride_id: rideId,
            rater_id: raterId,
            rated_id: ratedId,
            rating,
            feedback
          }
        ])
        .select()
        .single();

      if (error) throw error;

      // Update local ratings state
      const currentRatings = this.ratingsSubject.value;
      this.ratingsSubject.next([...currentRatings, data]);

      return data;
    } catch (error) {
      console.error('Error submitting rating:', error);
      throw error;
    }
  }

  /**
   * Check if a user has already rated another user for a specific ride
   */
  async hasUserRated(rideId: string, raterId: string, ratedId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('ratings')
        .select('id')
        .eq('ride_id', rideId)
        .eq('rater_id', raterId)
        .eq('rated_id', ratedId);

      if (error) throw error;

      return data.length > 0;
    } catch (error) {
      console.error('Error checking if user has rated:', error);
      return false;
    }
  }

  /**
   * Get all ratings given by a user
   */
  async getRatingsByUser(userId: string): Promise<Rating[]> {
    try {
      const { data, error } = await this.supabase
        .from('ratings')
        .select('*')
        .eq('rater_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching ratings by user:', error);
      return [];
    }
  }

  /**
   * Get all ratings received by a user
   */
  async getRatingsForUser(userId: string): Promise<Rating[]> {
    try {
      const { data, error } = await this.supabase
        .from('ratings')
        .select('*')
        .eq('rater_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching ratings for user:', error);
      return [];
    }
  }

  /**
   * Get a rating summary for a user
   */
  async getUserRatingSummary(userId: string): Promise<RatingSummary> {
    try {
      // Get all ratings for the user
      const ratings = await this.getRatingsForUser(userId);

      // Calculate average rating
      const totalRatings = ratings.length;
      const averageRating = totalRatings > 0
        ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / totalRatings
        : 0;

      // Get recent ratings (last 5)
      const recentRatings = ratings.slice(0, 5);

      return {
        averageRating,
        totalRatings,
        recentRatings
      };
    } catch (error) {
      console.error('Error getting user rating summary:', error);
      return {
        averageRating: 0,
        totalRatings: 0,
        recentRatings: []
      };
    }
  }

  /**
   * Get ratings for a specific ride
   */
  async getRatingsForRide(rideId: string): Promise<Rating[]> {
    try {
      const { data, error } = await this.supabase
        .from('ratings')
        .select('*')
        .eq('ride_id', rideId);

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('Error fetching ratings for ride:', error);
      return [];
    }
  }
}
