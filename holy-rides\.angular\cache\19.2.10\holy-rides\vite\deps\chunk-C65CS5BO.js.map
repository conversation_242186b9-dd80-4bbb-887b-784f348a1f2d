{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/form-field.mjs"], "sourcesContent": ["export { a as MAT_ERROR, h as MAT_FORM_FIELD, i as MAT_FORM_FIELD_DEFAULT_OPTIONS, d as MAT_PREFIX, f as MAT_SUFFIX, b as <PERSON><PERSON><PERSON><PERSON>, j as MatFormField, k as MatFormFieldControl, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON>fix, g as MatSuffix, m as getMatFormFieldDuplicatedHintError, n as getMatFormFieldMissingControlError, l as getMatFormFieldPlaceholderConflictError } from './form-field-B4o2BB25.mjs';\nexport { M as MatFormFieldModule } from './module-DAp_YJSv.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/platform';\nimport '@angular/common';\nimport '@angular/core';\nimport '@angular/cdk/a11y';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/observers';\nimport './common-module-WayjW0Pb.mjs';\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matFormFieldAnimations = {\n  // Represents:\n  // trigger('transitionMessages', [\n  //   // TODO(mmalerba): Use angular animations for label animation as well.\n  //   state('enter', style({opacity: 1, transform: 'translateY(0%)'})),\n  //   transition('void => enter', [\n  //     style({opacity: 0, transform: 'translateY(-5px)'}),\n  //     animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n  //   ]),\n  // ])\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: {\n    type: 7,\n    name: 'transitionMessages',\n    definitions: [{\n      type: 0,\n      name: 'enter',\n      styles: {\n        type: 6,\n        styles: {\n          opacity: 1,\n          transform: 'translateY(0%)'\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: 'void => enter',\n      animation: [{\n        type: 6,\n        styles: {\n          opacity: 0,\n          transform: 'translateY(-5px)'\n        },\n        offset: null\n      }, {\n        type: 4,\n        styles: null,\n        timings: '300ms cubic-bezier(0.55, 0, 0.55, 0.2)'\n      }],\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { matFormFieldAnimations };\n"], "mappings": ";AAoBA,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW7B,oBAAoB;AAAA,IAClB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW,CAAC;AAAA,QACV,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,CAAC;AAAA,MACD,SAAS;AAAA,IACX,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,EACZ;AACF;", "names": []}