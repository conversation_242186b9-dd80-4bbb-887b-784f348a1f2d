{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = require(\"./version\");\nexports.DEFAULT_HEADERS = {\n  'X-Client-Info': `postgrest-js/${version_1.version}`\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "DEFAULT_HEADERS", "version_1", "require", "version"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/postgrest-js/dist/cjs/constants.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = require(\"./version\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version_1.version}` };\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAG,KAAK,CAAC;AAChC,MAAMC,SAAS,GAAGC,OAAO,CAAC,WAAW,CAAC;AACtCJ,OAAO,CAACE,eAAe,GAAG;EAAE,eAAe,EAAE,gBAAgBC,SAAS,CAACE,OAAO;AAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}