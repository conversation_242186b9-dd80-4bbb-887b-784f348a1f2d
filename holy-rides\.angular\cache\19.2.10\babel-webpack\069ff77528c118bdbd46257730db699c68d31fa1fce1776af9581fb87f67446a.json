{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants';\nimport Push from './lib/push';\nimport Timer from './lib/timer';\nimport RealtimePresence from './RealtimePresence';\nimport * as Transformers from './lib/transformers';\nimport { httpEndpointURL } from './lib/transformers';\nexport var REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nexport var REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n  REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n  REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n  REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n  REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nexport var REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n  REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n  REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n  REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n  REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  constructor(/** Topic name can be any string. */\n  topic, params = {\n    config: {}\n  }, socket) {\n    this.topic = topic;\n    this.params = params;\n    this.socket = socket;\n    this.bindings = {};\n    this.state = CHANNEL_STATES.closed;\n    this.joinedOnce = false;\n    this.pushBuffer = [];\n    this.subTopic = topic.replace(/^realtime:/i, '');\n    this.params.config = Object.assign({\n      broadcast: {\n        ack: false,\n        self: false\n      },\n      presence: {\n        key: ''\n      },\n      private: false\n    }, params.config);\n    this.timeout = this.socket.timeout;\n    this.joinPush = new Push(this, CHANNEL_EVENTS.join, this.params, this.timeout);\n    this.rejoinTimer = new Timer(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined;\n      this.rejoinTimer.reset();\n      this.pushBuffer.forEach(pushEvent => pushEvent.send());\n      this.pushBuffer = [];\n    });\n    this._onClose(() => {\n      this.rejoinTimer.reset();\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n      this.state = CHANNEL_STATES.closed;\n      this.socket._remove(this);\n    });\n    this._onError(reason => {\n      if (this._isLeaving() || this._isClosed()) {\n        return;\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return;\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this._on(CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n      this._trigger(this._replyEventName(ref), payload);\n    });\n    this.presence = new RealtimePresence(this);\n    this.broadcastEndpointURL = httpEndpointURL(this.socket.endPoint) + '/api/broadcast';\n    this.private = this.params.config.private || false;\n  }\n  /** Subscribe registers your client with the server */\n  subscribe(callback, timeout = this.timeout) {\n    var _this = this;\n    var _a, _b;\n    if (!this.socket.isConnected()) {\n      this.socket.connect();\n    }\n    if (this.joinedOnce) {\n      throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n    } else {\n      const {\n        config: {\n          broadcast,\n          presence,\n          private: isPrivate\n        }\n      } = this.params;\n      this._onError(e => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n      this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n      const accessTokenPayload = {};\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map(r => r.filter)) !== null && _b !== void 0 ? _b : [],\n        private: isPrivate\n      };\n      if (this.socket.accessTokenValue) {\n        accessTokenPayload.access_token = this.socket.accessTokenValue;\n      }\n      this.updateJoinPayload(Object.assign({\n        config\n      }, accessTokenPayload));\n      this.joinedOnce = true;\n      this._rejoin(timeout);\n      this.joinPush.receive('ok', /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* ({\n          postgres_changes\n        }) {\n          var _a;\n          _this.socket.setAuth();\n          if (postgres_changes === undefined) {\n            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n            return;\n          } else {\n            const clientPostgresBindings = _this.bindings.postgres_changes;\n            const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n            const newPostgresBindings = [];\n            for (let i = 0; i < bindingsLen; i++) {\n              const clientPostgresBinding = clientPostgresBindings[i];\n              const {\n                filter: {\n                  event,\n                  schema,\n                  table,\n                  filter\n                }\n              } = clientPostgresBinding;\n              const serverPostgresFilter = postgres_changes && postgres_changes[i];\n              if (serverPostgresFilter && serverPostgresFilter.event === event && serverPostgresFilter.schema === schema && serverPostgresFilter.table === table && serverPostgresFilter.filter === filter) {\n                newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), {\n                  id: serverPostgresFilter.id\n                }));\n              } else {\n                _this.unsubscribe();\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                return;\n              }\n            }\n            _this.bindings.postgres_changes = newPostgresBindings;\n            callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n            return;\n          }\n        });\n        return function (_x) {\n          return _ref2.apply(this, arguments);\n        };\n      }()).receive('error', error => {\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n        return;\n      }).receive('timeout', () => {\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n        return;\n      });\n    }\n    return this;\n  }\n  presenceState() {\n    return this.presence.state;\n  }\n  track(_x2) {\n    var _this2 = this;\n    return _asyncToGenerator(function* (payload, opts = {}) {\n      return yield _this2.send({\n        type: 'presence',\n        event: 'track',\n        payload\n      }, opts.timeout || _this2.timeout);\n    }).apply(this, arguments);\n  }\n  untrack() {\n    var _this3 = this;\n    return _asyncToGenerator(function* (opts = {}) {\n      return yield _this3.send({\n        type: 'presence',\n        event: 'untrack'\n      }, opts);\n    }).apply(this, arguments);\n  }\n  on(type, filter, callback) {\n    return this._on(type, filter, callback);\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  send(_x3) {\n    var _this4 = this;\n    return _asyncToGenerator(function* (args, opts = {}) {\n      var _a, _b;\n      if (!_this4._canPush() && args.type === 'broadcast') {\n        const {\n          event,\n          payload: endpoint_payload\n        } = args;\n        const authorization = _this4.socket.accessTokenValue ? `Bearer ${_this4.socket.accessTokenValue}` : '';\n        const options = {\n          method: 'POST',\n          headers: {\n            Authorization: authorization,\n            apikey: _this4.socket.apiKey ? _this4.socket.apiKey : '',\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            messages: [{\n              topic: _this4.subTopic,\n              event,\n              payload: endpoint_payload,\n              private: _this4.private\n            }]\n          })\n        };\n        try {\n          const response = yield _this4._fetchWithTimeout(_this4.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : _this4.timeout);\n          yield (_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel();\n          return response.ok ? 'ok' : 'error';\n        } catch (error) {\n          if (error.name === 'AbortError') {\n            return 'timed out';\n          } else {\n            return 'error';\n          }\n        }\n      } else {\n        return new Promise(resolve => {\n          var _a, _b, _c;\n          const push = _this4._push(args.type, args, opts.timeout || _this4.timeout);\n          if (args.type === 'broadcast' && !((_c = (_b = (_a = _this4.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n            resolve('ok');\n          }\n          push.receive('ok', () => resolve('ok'));\n          push.receive('error', () => resolve('error'));\n          push.receive('timeout', () => resolve('timed out'));\n        });\n      }\n    }).apply(this, arguments);\n  }\n  updateJoinPayload(payload) {\n    this.joinPush.updatePayload(payload);\n  }\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout) {\n    this.state = CHANNEL_STATES.leaving;\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`);\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef());\n    };\n    this.rejoinTimer.reset();\n    // Destroy joinPush to avoid connection timeouts during unscription phase\n    this.joinPush.destroy();\n    return new Promise(resolve => {\n      const leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout);\n      leavePush.receive('ok', () => {\n        onClose();\n        resolve('ok');\n      }).receive('timeout', () => {\n        onClose();\n        resolve('timed out');\n      }).receive('error', () => {\n        resolve('error');\n      });\n      leavePush.send();\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {});\n      }\n    });\n  }\n  /** @internal */\n  _fetchWithTimeout(url, options, timeout) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const controller = new AbortController();\n      const id = setTimeout(() => controller.abort(), timeout);\n      const response = yield _this5.socket.fetch(url, Object.assign(Object.assign({}, options), {\n        signal: controller.signal\n      }));\n      clearTimeout(id);\n      return response;\n    })();\n  }\n  /** @internal */\n  _push(event, payload, timeout = this.timeout) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n    }\n    let pushEvent = new Push(this, event, payload, timeout);\n    if (this._canPush()) {\n      pushEvent.send();\n    } else {\n      pushEvent.startTimeout();\n      this.pushBuffer.push(pushEvent);\n    }\n    return pushEvent;\n  }\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event, payload, _ref) {\n    return payload;\n  }\n  /** @internal */\n  _isMember(topic) {\n    return this.topic === topic;\n  }\n  /** @internal */\n  _joinRef() {\n    return this.joinPush.ref;\n  }\n  /** @internal */\n  _trigger(type, payload, ref) {\n    var _a, _b;\n    const typeLower = type.toLocaleLowerCase();\n    const {\n      close,\n      error,\n      leave,\n      join\n    } = CHANNEL_EVENTS;\n    const events = [close, error, leave, join];\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return;\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref);\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n    }\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter(bind => {\n        var _a, _b, _c;\n        return ((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' || ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower;\n      }).map(bind => bind.callback(handledPayload, ref));\n    } else {\n      (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter(bind => {\n        var _a, _b, _c, _d, _e, _f;\n        if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n          if ('id' in bind) {\n            const bindId = bind.id;\n            const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n            return bindId && ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) && (bindEvent === '*' || (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) === ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase()));\n          } else {\n            const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n            return bindEvent === '*' || bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase());\n          }\n        } else {\n          return bind.type.toLocaleLowerCase() === typeLower;\n        }\n      }).map(bind => {\n        if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n          const postgresChanges = handledPayload.data;\n          const {\n            schema,\n            table,\n            commit_timestamp,\n            type,\n            errors\n          } = postgresChanges;\n          const enrichedPayload = {\n            schema: schema,\n            table: table,\n            commit_timestamp: commit_timestamp,\n            eventType: type,\n            new: {},\n            old: {},\n            errors: errors\n          };\n          handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n        }\n        bind.callback(handledPayload, ref);\n      });\n    }\n  }\n  /** @internal */\n  _isClosed() {\n    return this.state === CHANNEL_STATES.closed;\n  }\n  /** @internal */\n  _isJoined() {\n    return this.state === CHANNEL_STATES.joined;\n  }\n  /** @internal */\n  _isJoining() {\n    return this.state === CHANNEL_STATES.joining;\n  }\n  /** @internal */\n  _isLeaving() {\n    return this.state === CHANNEL_STATES.leaving;\n  }\n  /** @internal */\n  _replyEventName(ref) {\n    return `chan_reply_${ref}`;\n  }\n  /** @internal */\n  _on(type, filter, callback) {\n    const typeLower = type.toLocaleLowerCase();\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback\n    };\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding);\n    } else {\n      this.bindings[typeLower] = [binding];\n    }\n    return this;\n  }\n  /** @internal */\n  _off(type, filter) {\n    const typeLower = type.toLocaleLowerCase();\n    this.bindings[typeLower] = this.bindings[typeLower].filter(bind => {\n      var _a;\n      return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower && RealtimeChannel.isEqual(bind.filter, filter));\n    });\n    return this;\n  }\n  /** @internal */\n  static isEqual(obj1, obj2) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false;\n    }\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /** @internal */\n  _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout();\n    if (this.socket.isConnected()) {\n      this._rejoin();\n    }\n  }\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  _onClose(callback) {\n    this._on(CHANNEL_EVENTS.close, {}, callback);\n  }\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  _onError(callback) {\n    this._on(CHANNEL_EVENTS.error, {}, reason => callback(reason));\n  }\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  _canPush() {\n    return this.socket.isConnected() && this._isJoined();\n  }\n  /** @internal */\n  _rejoin(timeout = this.timeout) {\n    if (this._isLeaving()) {\n      return;\n    }\n    this.socket._leaveOpenTopic(this.topic);\n    this.state = CHANNEL_STATES.joining;\n    this.joinPush.resend(timeout);\n  }\n  /** @internal */\n  _getPayloadRecords(payload) {\n    const records = {\n      new: {},\n      old: {}\n    };\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(payload.columns, payload.record);\n    }\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n    }\n    return records;\n  }\n}", "map": {"version": 3, "names": ["CHANNEL_EVENTS", "CHANNEL_STATES", "<PERSON><PERSON>", "Timer", "RealtimePresence", "Transformers", "httpEndpointURL", "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "REALTIME_LISTEN_TYPES", "REALTIME_SUBSCRIBE_STATES", "REALTIME_CHANNEL_STATES", "RealtimeChannel", "constructor", "topic", "params", "config", "socket", "bindings", "state", "closed", "joinedOnce", "pushBuffer", "subTopic", "replace", "Object", "assign", "broadcast", "ack", "self", "presence", "key", "private", "timeout", "joinPush", "join", "rejoinTimer", "_rejoinUntilConnected", "reconnectAfterMs", "receive", "joined", "reset", "for<PERSON>ach", "pushEvent", "send", "_onClose", "log", "_joinRef", "_remove", "_onError", "reason", "_isLeaving", "_isClosed", "errored", "scheduleTimeout", "_isJoining", "_on", "reply", "payload", "ref", "_trigger", "_replyEventName", "broadcastEndpointURL", "endPoint", "subscribe", "callback", "_this", "_a", "_b", "isConnected", "connect", "isPrivate", "e", "CHANNEL_ERROR", "CLOSED", "accessTokenPayload", "postgres_changes", "map", "r", "filter", "accessTokenValue", "access_token", "updateJoinPayload", "_rejoin", "_ref2", "_asyncToGenerator", "setAuth", "undefined", "SUBSCRIBED", "clientPostgresBindings", "bindingsLen", "length", "newPostgresBindings", "i", "clientPostgresBinding", "event", "schema", "table", "serverPostgresFilter", "push", "id", "unsubscribe", "Error", "_x", "apply", "arguments", "error", "JSON", "stringify", "values", "TIMED_OUT", "presenceState", "track", "_x2", "_this2", "opts", "type", "untrack", "_this3", "on", "_x3", "_this4", "args", "_canPush", "endpoint_payload", "authorization", "options", "method", "headers", "Authorization", "apikey", "<PERSON><PERSON><PERSON><PERSON>", "body", "messages", "response", "_fetchWithTimeout", "cancel", "ok", "name", "Promise", "resolve", "_c", "_push", "updatePayload", "leaving", "onClose", "close", "destroy", "leavePush", "leave", "trigger", "url", "_this5", "controller", "AbortController", "setTimeout", "abort", "fetch", "signal", "clearTimeout", "startTimeout", "_onMessage", "_event", "_ref", "_isMember", "typeLower", "toLocaleLowerCase", "events", "indexOf", "handledPayload", "includes", "bind", "_d", "_e", "_f", "bindId", "bindEvent", "ids", "data", "postgresChanges", "commit_timestamp", "errors", "enrichedPayload", "eventType", "new", "old", "_getPayloadRecords", "_isJoined", "joining", "binding", "_off", "isEqual", "obj1", "obj2", "keys", "k", "_leaveOpenTopic", "resend", "records", "convertChangeData", "columns", "record", "old_record"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js"], "sourcesContent": ["import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants';\nimport Push from './lib/push';\nimport Timer from './lib/timer';\nimport RealtimePresence from './RealtimePresence';\nimport * as Transformers from './lib/transformers';\nimport { httpEndpointURL } from './lib/transformers';\nexport var REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nexport var REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nexport var REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new Push(this, CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new Timer(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new RealtimePresence(this);\n        this.broadcastEndpointURL =\n            httpEndpointURL(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.joinedOnce) {\n            throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n        }\n        else {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.rejoinTimer.reset();\n        // Destroy joinPush to avoid connection timeouts during unscription phase\n        this.joinPush.destroy();\n        return new Promise((resolve) => {\n            const leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        });\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new Push(this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = Transformers.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAO,IAAIC,sCAAsC;AACjD,CAAC,UAAUA,sCAAsC,EAAE;EAC/CA,sCAAsC,CAAC,KAAK,CAAC,GAAG,GAAG;EACnDA,sCAAsC,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC3DA,sCAAsC,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC3DA,sCAAsC,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC/D,CAAC,EAAEA,sCAAsC,KAAKA,sCAAsC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3F,OAAO,IAAIC,qBAAqB;AAChC,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,WAAW,CAAC,GAAG,WAAW;EAChDA,qBAAqB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC9CA,qBAAqB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC9DA,qBAAqB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC9C,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO,IAAIC,yBAAyB;AACpC,CAAC,UAAUA,yBAAyB,EAAE;EAClCA,yBAAyB,CAAC,YAAY,CAAC,GAAG,YAAY;EACtDA,yBAAyB,CAAC,WAAW,CAAC,GAAG,WAAW;EACpDA,yBAAyB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC9CA,yBAAyB,CAAC,eAAe,CAAC,GAAG,eAAe;AAChE,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE,OAAO,MAAMC,uBAAuB,GAAGT,cAAc;AACrD;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMU,eAAe,CAAC;EACjCC,WAAWA,CACX;EACAC,KAAK,EAAEC,MAAM,GAAG;IAAEC,MAAM,EAAE,CAAC;EAAE,CAAC,EAAEC,MAAM,EAAE;IACpC,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,KAAK,GAAGjB,cAAc,CAACkB,MAAM;IAClC,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAGT,KAAK,CAACU,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;IAChD,IAAI,CAACT,MAAM,CAACC,MAAM,GAAGS,MAAM,CAACC,MAAM,CAAC;MAC/BC,SAAS,EAAE;QAAEC,GAAG,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAM,CAAC;MACtCC,QAAQ,EAAE;QAAEC,GAAG,EAAE;MAAG,CAAC;MACrBC,OAAO,EAAE;IACb,CAAC,EAAEjB,MAAM,CAACC,MAAM,CAAC;IACjB,IAAI,CAACiB,OAAO,GAAG,IAAI,CAAChB,MAAM,CAACgB,OAAO;IAClC,IAAI,CAACC,QAAQ,GAAG,IAAI/B,IAAI,CAAC,IAAI,EAAEF,cAAc,CAACkC,IAAI,EAAE,IAAI,CAACpB,MAAM,EAAE,IAAI,CAACkB,OAAO,CAAC;IAC9E,IAAI,CAACG,WAAW,GAAG,IAAIhC,KAAK,CAAC,MAAM,IAAI,CAACiC,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACpB,MAAM,CAACqB,gBAAgB,CAAC;IAC9F,IAAI,CAACJ,QAAQ,CAACK,OAAO,CAAC,IAAI,EAAE,MAAM;MAC9B,IAAI,CAACpB,KAAK,GAAGjB,cAAc,CAACsC,MAAM;MAClC,IAAI,CAACJ,WAAW,CAACK,KAAK,CAAC,CAAC;MACxB,IAAI,CAACnB,UAAU,CAACoB,OAAO,CAAEC,SAAS,IAAKA,SAAS,CAACC,IAAI,CAAC,CAAC,CAAC;MACxD,IAAI,CAACtB,UAAU,GAAG,EAAE;IACxB,CAAC,CAAC;IACF,IAAI,CAACuB,QAAQ,CAAC,MAAM;MAChB,IAAI,CAACT,WAAW,CAACK,KAAK,CAAC,CAAC;MACxB,IAAI,CAACxB,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,IAAI,IAAI,CAACiC,QAAQ,CAAC,CAAC,EAAE,CAAC;MACpE,IAAI,CAAC5B,KAAK,GAAGjB,cAAc,CAACkB,MAAM;MAClC,IAAI,CAACH,MAAM,CAAC+B,OAAO,CAAC,IAAI,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,CAAEC,MAAM,IAAK;MACtB,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;QACvC;MACJ;MACA,IAAI,CAACnC,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,EAAE,EAAEoC,MAAM,CAAC;MACzD,IAAI,CAAC/B,KAAK,GAAGjB,cAAc,CAACmD,OAAO;MACnC,IAAI,CAACjB,WAAW,CAACkB,eAAe,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACpB,QAAQ,CAACK,OAAO,CAAC,SAAS,EAAE,MAAM;MACnC,IAAI,CAAC,IAAI,CAACgB,UAAU,CAAC,CAAC,EAAE;QACpB;MACJ;MACA,IAAI,CAACtC,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,WAAW,IAAI,CAAChC,KAAK,EAAE,EAAE,IAAI,CAACoB,QAAQ,CAACD,OAAO,CAAC;MAC1E,IAAI,CAACd,KAAK,GAAGjB,cAAc,CAACmD,OAAO;MACnC,IAAI,CAACjB,WAAW,CAACkB,eAAe,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACE,GAAG,CAACvD,cAAc,CAACwD,KAAK,EAAE,CAAC,CAAC,EAAE,CAACC,OAAO,EAAEC,GAAG,KAAK;MACjD,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACC,eAAe,CAACF,GAAG,CAAC,EAAED,OAAO,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAAC5B,QAAQ,GAAG,IAAIzB,gBAAgB,CAAC,IAAI,CAAC;IAC1C,IAAI,CAACyD,oBAAoB,GACrBvD,eAAe,CAAC,IAAI,CAACU,MAAM,CAAC8C,QAAQ,CAAC,GAAG,gBAAgB;IAC5D,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAACjB,MAAM,CAACC,MAAM,CAACgB,OAAO,IAAI,KAAK;EACtD;EACA;EACAgC,SAASA,CAACC,QAAQ,EAAEhC,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IAAA,IAAAiC,KAAA;IACxC,IAAIC,EAAE,EAAEC,EAAE;IACV,IAAI,CAAC,IAAI,CAACnD,MAAM,CAACoD,WAAW,CAAC,CAAC,EAAE;MAC5B,IAAI,CAACpD,MAAM,CAACqD,OAAO,CAAC,CAAC;IACzB;IACA,IAAI,IAAI,CAACjD,UAAU,EAAE;MACjB,MAAM,sGAAsG;IAChH,CAAC,MACI;MACD,MAAM;QAAEL,MAAM,EAAE;UAAEW,SAAS;UAAEG,QAAQ;UAAEE,OAAO,EAAEuC;QAAU;MAAG,CAAC,GAAG,IAAI,CAACxD,MAAM;MAC5E,IAAI,CAACkC,QAAQ,CAAEuB,CAAC,IAAKP,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAAC+D,aAAa,EAAED,CAAC,CAAC,CAAC;MAC9H,IAAI,CAAC3B,QAAQ,CAAC,MAAMoB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAACgE,MAAM,CAAC,CAAC;MACnH,MAAMC,kBAAkB,GAAG,CAAC,CAAC;MAC7B,MAAM3D,MAAM,GAAG;QACXW,SAAS;QACTG,QAAQ;QACR8C,gBAAgB,EAAE,CAACR,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACjD,QAAQ,CAAC0D,gBAAgB,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QAC/JpC,OAAO,EAAEuC;MACb,CAAC;MACD,IAAI,IAAI,CAACtD,MAAM,CAAC+D,gBAAgB,EAAE;QAC9BL,kBAAkB,CAACM,YAAY,GAAG,IAAI,CAAChE,MAAM,CAAC+D,gBAAgB;MAClE;MACA,IAAI,CAACE,iBAAiB,CAACzD,MAAM,CAACC,MAAM,CAAC;QAAEV;MAAO,CAAC,EAAE2D,kBAAkB,CAAC,CAAC;MACrE,IAAI,CAACtD,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC8D,OAAO,CAAClD,OAAO,CAAC;MACrB,IAAI,CAACC,QAAQ,CACRK,OAAO,CAAC,IAAI;QAAA,IAAA6C,KAAA,GAAAC,iBAAA,CAAE,WAAO;UAAET;QAAiB,CAAC,EAAK;UAC/C,IAAIT,EAAE;UACND,KAAI,CAACjD,MAAM,CAACqE,OAAO,CAAC,CAAC;UACrB,IAAIV,gBAAgB,KAAKW,SAAS,EAAE;YAChCtB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAAC8E,UAAU,CAAC;YAClG;UACJ,CAAC,MACI;YACD,MAAMC,sBAAsB,GAAGvB,KAAI,CAAChD,QAAQ,CAAC0D,gBAAgB;YAC7D,MAAMc,WAAW,GAAG,CAACvB,EAAE,GAAGsB,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACE,MAAM,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;YAC3K,MAAMyB,mBAAmB,GAAG,EAAE;YAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;cAClC,MAAMC,qBAAqB,GAAGL,sBAAsB,CAACI,CAAC,CAAC;cACvD,MAAM;gBAAEd,MAAM,EAAE;kBAAEgB,KAAK;kBAAEC,MAAM;kBAAEC,KAAK;kBAAElB;gBAAO;cAAG,CAAC,GAAGe,qBAAqB;cAC3E,MAAMI,oBAAoB,GAAGtB,gBAAgB,IAAIA,gBAAgB,CAACiB,CAAC,CAAC;cACpE,IAAIK,oBAAoB,IACpBA,oBAAoB,CAACH,KAAK,KAAKA,KAAK,IACpCG,oBAAoB,CAACF,MAAM,KAAKA,MAAM,IACtCE,oBAAoB,CAACD,KAAK,KAAKA,KAAK,IACpCC,oBAAoB,CAACnB,MAAM,KAAKA,MAAM,EAAE;gBACxCa,mBAAmB,CAACO,IAAI,CAAC1E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoE,qBAAqB,CAAC,EAAE;kBAAEM,EAAE,EAAEF,oBAAoB,CAACE;gBAAG,CAAC,CAAC,CAAC;cACtH,CAAC,MACI;gBACDlC,KAAI,CAACmC,WAAW,CAAC,CAAC;gBAClBpC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAAC+D,aAAa,EAAE,IAAI6B,KAAK,CAAC,kEAAkE,CAAC,CAAC;gBACpL;cACJ;YACJ;YACApC,KAAI,CAAChD,QAAQ,CAAC0D,gBAAgB,GAAGgB,mBAAmB;YACpD3B,QAAQ,IAAIA,QAAQ,CAACvD,yBAAyB,CAAC8E,UAAU,CAAC;YAC1D;UACJ;QACJ,CAAC;QAAA,iBAAAe,EAAA;UAAA,OAAAnB,KAAA,CAAAoB,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC,CACGlE,OAAO,CAAC,OAAO,EAAGmE,KAAK,IAAK;QAC7BzC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAAC+D,aAAa,EAAE,IAAI6B,KAAK,CAACK,IAAI,CAACC,SAAS,CAACnF,MAAM,CAACoF,MAAM,CAACH,KAAK,CAAC,CAACvE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;QAC5K;MACJ,CAAC,CAAC,CACGI,OAAO,CAAC,SAAS,EAAE,MAAM;QAC1B0B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvD,yBAAyB,CAACoG,SAAS,CAAC;QACjG;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACf;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjF,QAAQ,CAACX,KAAK;EAC9B;EACM6F,KAAKA,CAAAC,GAAA,EAAqB;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA,YAApB3B,OAAO,EAAEyD,IAAI,GAAG,CAAC,CAAC;MAC1B,aAAaD,MAAI,CAACtE,IAAI,CAAC;QACnBwE,IAAI,EAAE,UAAU;QAChBrB,KAAK,EAAE,OAAO;QACdrC;MACJ,CAAC,EAAEyD,IAAI,CAAClF,OAAO,IAAIiF,MAAI,CAACjF,OAAO,CAAC;IAAC,GAAAuE,KAAA,OAAAC,SAAA;EACrC;EACMY,OAAOA,CAAA,EAAY;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA,YAAX8B,IAAI,GAAG,CAAC,CAAC;MACnB,aAAaG,MAAI,CAAC1E,IAAI,CAAC;QACnBwE,IAAI,EAAE,UAAU;QAChBrB,KAAK,EAAE;MACX,CAAC,EAAEoB,IAAI,CAAC;IAAC,GAAAX,KAAA,OAAAC,SAAA;EACb;EACAc,EAAEA,CAACH,IAAI,EAAErC,MAAM,EAAEd,QAAQ,EAAE;IACvB,OAAO,IAAI,CAACT,GAAG,CAAC4D,IAAI,EAAErC,MAAM,EAAEd,QAAQ,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUrB,IAAIA,CAAA4E,GAAA,EAAkB;IAAA,IAAAC,MAAA;IAAA,OAAApC,iBAAA,YAAjBqC,IAAI,EAAEP,IAAI,GAAG,CAAC,CAAC;MACtB,IAAIhD,EAAE,EAAEC,EAAE;MACV,IAAI,CAACqD,MAAI,CAACE,QAAQ,CAAC,CAAC,IAAID,IAAI,CAACN,IAAI,KAAK,WAAW,EAAE;QAC/C,MAAM;UAAErB,KAAK;UAAErC,OAAO,EAAEkE;QAAiB,CAAC,GAAGF,IAAI;QACjD,MAAMG,aAAa,GAAGJ,MAAI,CAACxG,MAAM,CAAC+D,gBAAgB,GAC5C,UAAUyC,MAAI,CAACxG,MAAM,CAAC+D,gBAAgB,EAAE,GACxC,EAAE;QACR,MAAM8C,OAAO,GAAG;UACZC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACLC,aAAa,EAAEJ,aAAa;YAC5BK,MAAM,EAAET,MAAI,CAACxG,MAAM,CAACkH,MAAM,GAAGV,MAAI,CAACxG,MAAM,CAACkH,MAAM,GAAG,EAAE;YACpD,cAAc,EAAE;UACpB,CAAC;UACDC,IAAI,EAAEzB,IAAI,CAACC,SAAS,CAAC;YACjByB,QAAQ,EAAE,CACN;cACIvH,KAAK,EAAE2G,MAAI,CAAClG,QAAQ;cACpBwE,KAAK;cACLrC,OAAO,EAAEkE,gBAAgB;cACzB5F,OAAO,EAAEyF,MAAI,CAACzF;YAClB,CAAC;UAET,CAAC;QACL,CAAC;QACD,IAAI;UACA,MAAMsG,QAAQ,SAASb,MAAI,CAACc,iBAAiB,CAACd,MAAI,CAAC3D,oBAAoB,EAAEgE,OAAO,EAAE,CAAC3D,EAAE,GAAGgD,IAAI,CAAClF,OAAO,MAAM,IAAI,IAAIkC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGsD,MAAI,CAACxF,OAAO,CAAC;UACpJ,MAAO,CAACmC,EAAE,GAAGkE,QAAQ,CAACF,IAAI,MAAM,IAAI,IAAIhE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoE,MAAM,CAAC,CAAC;UAC5E,OAAOF,QAAQ,CAACG,EAAE,GAAG,IAAI,GAAG,OAAO;QACvC,CAAC,CACD,OAAO/B,KAAK,EAAE;UACV,IAAIA,KAAK,CAACgC,IAAI,KAAK,YAAY,EAAE;YAC7B,OAAO,WAAW;UACtB,CAAC,MACI;YACD,OAAO,OAAO;UAClB;QACJ;MACJ,CAAC,MACI;QACD,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;UAC5B,IAAIzE,EAAE,EAAEC,EAAE,EAAEyE,EAAE;UACd,MAAM1C,IAAI,GAAGsB,MAAI,CAACqB,KAAK,CAACpB,IAAI,CAACN,IAAI,EAAEM,IAAI,EAAEP,IAAI,CAAClF,OAAO,IAAIwF,MAAI,CAACxF,OAAO,CAAC;UACtE,IAAIyF,IAAI,CAACN,IAAI,KAAK,WAAW,IAAI,EAAE,CAACyB,EAAE,GAAG,CAACzE,EAAE,GAAG,CAACD,EAAE,GAAGsD,MAAI,CAAC1G,MAAM,MAAM,IAAI,IAAIoD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnD,MAAM,MAAM,IAAI,IAAIoD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzC,SAAS,MAAM,IAAI,IAAIkH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjH,GAAG,CAAC,EAAE;YAC/MgH,OAAO,CAAC,IAAI,CAAC;UACjB;UACAzC,IAAI,CAAC5D,OAAO,CAAC,IAAI,EAAE,MAAMqG,OAAO,CAAC,IAAI,CAAC,CAAC;UACvCzC,IAAI,CAAC5D,OAAO,CAAC,OAAO,EAAE,MAAMqG,OAAO,CAAC,OAAO,CAAC,CAAC;UAC7CzC,IAAI,CAAC5D,OAAO,CAAC,SAAS,EAAE,MAAMqG,OAAO,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC,CAAC;MACN;IAAC,GAAApC,KAAA,OAAAC,SAAA;EACL;EACAvB,iBAAiBA,CAACxB,OAAO,EAAE;IACvB,IAAI,CAACxB,QAAQ,CAAC6G,aAAa,CAACrF,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2C,WAAWA,CAACpE,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IAChC,IAAI,CAACd,KAAK,GAAGjB,cAAc,CAAC8I,OAAO;IACnC,MAAMC,OAAO,GAAGA,CAAA,KAAM;MAClB,IAAI,CAAChI,MAAM,CAAC6B,GAAG,CAAC,SAAS,EAAE,SAAS,IAAI,CAAChC,KAAK,EAAE,CAAC;MACjD,IAAI,CAAC8C,QAAQ,CAAC3D,cAAc,CAACiJ,KAAK,EAAE,OAAO,EAAE,IAAI,CAACnG,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,CAACX,WAAW,CAACK,KAAK,CAAC,CAAC;IACxB;IACA,IAAI,CAACP,QAAQ,CAACiH,OAAO,CAAC,CAAC;IACvB,OAAO,IAAIR,OAAO,CAAEC,OAAO,IAAK;MAC5B,MAAMQ,SAAS,GAAG,IAAIjJ,IAAI,CAAC,IAAI,EAAEF,cAAc,CAACoJ,KAAK,EAAE,CAAC,CAAC,EAAEpH,OAAO,CAAC;MACnEmH,SAAS,CACJ7G,OAAO,CAAC,IAAI,EAAE,MAAM;QACrB0G,OAAO,CAAC,CAAC;QACTL,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC,CACGrG,OAAO,CAAC,SAAS,EAAE,MAAM;QAC1B0G,OAAO,CAAC,CAAC;QACTL,OAAO,CAAC,WAAW,CAAC;MACxB,CAAC,CAAC,CACGrG,OAAO,CAAC,OAAO,EAAE,MAAM;QACxBqG,OAAO,CAAC,OAAO,CAAC;MACpB,CAAC,CAAC;MACFQ,SAAS,CAACxG,IAAI,CAAC,CAAC;MAChB,IAAI,CAAC,IAAI,CAAC+E,QAAQ,CAAC,CAAC,EAAE;QAClByB,SAAS,CAACE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACA;EACMf,iBAAiBA,CAACgB,GAAG,EAAEzB,OAAO,EAAE7F,OAAO,EAAE;IAAA,IAAAuH,MAAA;IAAA,OAAAnE,iBAAA;MAC3C,MAAMoE,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMtD,EAAE,GAAGuD,UAAU,CAAC,MAAMF,UAAU,CAACG,KAAK,CAAC,CAAC,EAAE3H,OAAO,CAAC;MACxD,MAAMqG,QAAQ,SAASkB,MAAI,CAACvI,MAAM,CAAC4I,KAAK,CAACN,GAAG,EAAE9H,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoG,OAAO,CAAC,EAAE;QAAEgC,MAAM,EAAEL,UAAU,CAACK;MAAO,CAAC,CAAC,CAAC;MACvHC,YAAY,CAAC3D,EAAE,CAAC;MAChB,OAAOkC,QAAQ;IAAC;EACpB;EACA;EACAQ,KAAKA,CAAC/C,KAAK,EAAErC,OAAO,EAAEzB,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IAC1C,IAAI,CAAC,IAAI,CAACZ,UAAU,EAAE;MAClB,MAAM,kBAAkB0E,KAAK,SAAS,IAAI,CAACjF,KAAK,iEAAiE;IACrH;IACA,IAAI6B,SAAS,GAAG,IAAIxC,IAAI,CAAC,IAAI,EAAE4F,KAAK,EAAErC,OAAO,EAAEzB,OAAO,CAAC;IACvD,IAAI,IAAI,CAAC0F,QAAQ,CAAC,CAAC,EAAE;MACjBhF,SAAS,CAACC,IAAI,CAAC,CAAC;IACpB,CAAC,MACI;MACDD,SAAS,CAACqH,YAAY,CAAC,CAAC;MACxB,IAAI,CAAC1I,UAAU,CAAC6E,IAAI,CAACxD,SAAS,CAAC;IACnC;IACA,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIsH,UAAUA,CAACC,MAAM,EAAExG,OAAO,EAAEyG,IAAI,EAAE;IAC9B,OAAOzG,OAAO;EAClB;EACA;EACA0G,SAASA,CAACtJ,KAAK,EAAE;IACb,OAAO,IAAI,CAACA,KAAK,KAAKA,KAAK;EAC/B;EACA;EACAiC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,QAAQ,CAACyB,GAAG;EAC5B;EACA;EACAC,QAAQA,CAACwD,IAAI,EAAE1D,OAAO,EAAEC,GAAG,EAAE;IACzB,IAAIQ,EAAE,EAAEC,EAAE;IACV,MAAMiG,SAAS,GAAGjD,IAAI,CAACkD,iBAAiB,CAAC,CAAC;IAC1C,MAAM;MAAEpB,KAAK;MAAExC,KAAK;MAAE2C,KAAK;MAAElH;IAAK,CAAC,GAAGlC,cAAc;IACpD,MAAMsK,MAAM,GAAG,CAACrB,KAAK,EAAExC,KAAK,EAAE2C,KAAK,EAAElH,IAAI,CAAC;IAC1C,IAAIwB,GAAG,IAAI4G,MAAM,CAACC,OAAO,CAACH,SAAS,CAAC,IAAI,CAAC,IAAI1G,GAAG,KAAK,IAAI,CAACZ,QAAQ,CAAC,CAAC,EAAE;MAClE;IACJ;IACA,IAAI0H,cAAc,GAAG,IAAI,CAACR,UAAU,CAACI,SAAS,EAAE3G,OAAO,EAAEC,GAAG,CAAC;IAC7D,IAAID,OAAO,IAAI,CAAC+G,cAAc,EAAE;MAC5B,MAAM,6EAA6E;IACvF;IACA,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACL,SAAS,CAAC,EAAE;MACpD,CAAClG,EAAE,GAAG,IAAI,CAACjD,QAAQ,CAAC0D,gBAAgB,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,MAAM,CAAE4F,IAAI,IAAK;QAC3F,IAAIxG,EAAE,EAAEC,EAAE,EAAEyE,EAAE;QACd,OAAQ,CAAC,CAAC1E,EAAE,GAAGwG,IAAI,CAAC5F,MAAM,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,KAAK,MAAM,GAAG,IAC9E,CAAC,CAAC8C,EAAE,GAAG,CAACzE,EAAE,GAAGuG,IAAI,CAAC5F,MAAM,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,KAAK,MAAM,IAAI,IAAI8C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyB,iBAAiB,CAAC,CAAC,MAAMD,SAAS;MAC3J,CAAC,CAAC,CAACxF,GAAG,CAAE8F,IAAI,IAAKA,IAAI,CAAC1G,QAAQ,CAACwG,cAAc,EAAE9G,GAAG,CAAC,CAAC;IACxD,CAAC,MACI;MACD,CAACS,EAAE,GAAG,IAAI,CAAClD,QAAQ,CAACmJ,SAAS,CAAC,MAAM,IAAI,IAAIjG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,MAAM,CAAE4F,IAAI,IAAK;QACrF,IAAIxG,EAAE,EAAEC,EAAE,EAAEyE,EAAE,EAAE+B,EAAE,EAAEC,EAAE,EAAEC,EAAE;QAC1B,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAACJ,QAAQ,CAACL,SAAS,CAAC,EAAE;UACnE,IAAI,IAAI,IAAIM,IAAI,EAAE;YACd,MAAMI,MAAM,GAAGJ,IAAI,CAACvE,EAAE;YACtB,MAAM4E,SAAS,GAAG,CAAC7G,EAAE,GAAGwG,IAAI,CAAC5F,MAAM,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,KAAK;YAClF,OAAQgF,MAAM,KACT,CAAC3G,EAAE,GAAGV,OAAO,CAACuH,GAAG,MAAM,IAAI,IAAI7G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsG,QAAQ,CAACK,MAAM,CAAC,CAAC,KAC5EC,SAAS,KAAK,GAAG,IACd,CAACA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACV,iBAAiB,CAAC,CAAC,OAC/E,CAACzB,EAAE,GAAGnF,OAAO,CAACwH,IAAI,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzB,IAAI,CAACkD,iBAAiB,CAAC,CAAC,CAAC,CAAC;UACvG,CAAC,MACI;YACD,MAAMU,SAAS,GAAG,CAACH,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC5F,MAAM,MAAM,IAAI,IAAI6F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7E,KAAK,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACP,iBAAiB,CAAC,CAAC;YACjM,OAAQU,SAAS,KAAK,GAAG,IACrBA,SAAS,MAAM,CAACF,EAAE,GAAGpH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqC,KAAK,MAAM,IAAI,IAAI+E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACR,iBAAiB,CAAC,CAAC,CAAC;UAC1J;QACJ,CAAC,MACI;UACD,OAAOK,IAAI,CAACvD,IAAI,CAACkD,iBAAiB,CAAC,CAAC,KAAKD,SAAS;QACtD;MACJ,CAAC,CAAC,CAACxF,GAAG,CAAE8F,IAAI,IAAK;QACb,IAAI,OAAOF,cAAc,KAAK,QAAQ,IAAI,KAAK,IAAIA,cAAc,EAAE;UAC/D,MAAMU,eAAe,GAAGV,cAAc,CAACS,IAAI;UAC3C,MAAM;YAAElF,MAAM;YAAEC,KAAK;YAAEmF,gBAAgB;YAAEhE,IAAI;YAAEiE;UAAO,CAAC,GAAGF,eAAe;UACzE,MAAMG,eAAe,GAAG;YACpBtF,MAAM,EAAEA,MAAM;YACdC,KAAK,EAAEA,KAAK;YACZmF,gBAAgB,EAAEA,gBAAgB;YAClCG,SAAS,EAAEnE,IAAI;YACfoE,GAAG,EAAE,CAAC,CAAC;YACPC,GAAG,EAAE,CAAC,CAAC;YACPJ,MAAM,EAAEA;UACZ,CAAC;UACDZ,cAAc,GAAGhJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4J,eAAe,CAAC,EAAE,IAAI,CAACI,kBAAkB,CAACP,eAAe,CAAC,CAAC;QAChH;QACAR,IAAI,CAAC1G,QAAQ,CAACwG,cAAc,EAAE9G,GAAG,CAAC;MACtC,CAAC,CAAC;IACN;EACJ;EACA;EACAP,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACjC,KAAK,KAAKjB,cAAc,CAACkB,MAAM;EAC/C;EACA;EACAuK,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxK,KAAK,KAAKjB,cAAc,CAACsC,MAAM;EAC/C;EACA;EACAe,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACpC,KAAK,KAAKjB,cAAc,CAAC0L,OAAO;EAChD;EACA;EACAzI,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAChC,KAAK,KAAKjB,cAAc,CAAC8I,OAAO;EAChD;EACA;EACAnF,eAAeA,CAACF,GAAG,EAAE;IACjB,OAAO,cAAcA,GAAG,EAAE;EAC9B;EACA;EACAH,GAAGA,CAAC4D,IAAI,EAAErC,MAAM,EAAEd,QAAQ,EAAE;IACxB,MAAMoG,SAAS,GAAGjD,IAAI,CAACkD,iBAAiB,CAAC,CAAC;IAC1C,MAAMuB,OAAO,GAAG;MACZzE,IAAI,EAAEiD,SAAS;MACftF,MAAM,EAAEA,MAAM;MACdd,QAAQ,EAAEA;IACd,CAAC;IACD,IAAI,IAAI,CAAC/C,QAAQ,CAACmJ,SAAS,CAAC,EAAE;MAC1B,IAAI,CAACnJ,QAAQ,CAACmJ,SAAS,CAAC,CAAClE,IAAI,CAAC0F,OAAO,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAAC3K,QAAQ,CAACmJ,SAAS,CAAC,GAAG,CAACwB,OAAO,CAAC;IACxC;IACA,OAAO,IAAI;EACf;EACA;EACAC,IAAIA,CAAC1E,IAAI,EAAErC,MAAM,EAAE;IACf,MAAMsF,SAAS,GAAGjD,IAAI,CAACkD,iBAAiB,CAAC,CAAC;IAC1C,IAAI,CAACpJ,QAAQ,CAACmJ,SAAS,CAAC,GAAG,IAAI,CAACnJ,QAAQ,CAACmJ,SAAS,CAAC,CAACtF,MAAM,CAAE4F,IAAI,IAAK;MACjE,IAAIxG,EAAE;MACN,OAAO,EAAE,CAAC,CAACA,EAAE,GAAGwG,IAAI,CAACvD,IAAI,MAAM,IAAI,IAAIjD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmG,iBAAiB,CAAC,CAAC,MAAMD,SAAS,IACjGzJ,eAAe,CAACmL,OAAO,CAACpB,IAAI,CAAC5F,MAAM,EAAEA,MAAM,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;EACA,OAAOgH,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACvB,IAAIxK,MAAM,CAACyK,IAAI,CAACF,IAAI,CAAC,CAACrG,MAAM,KAAKlE,MAAM,CAACyK,IAAI,CAACD,IAAI,CAAC,CAACtG,MAAM,EAAE;MACvD,OAAO,KAAK;IAChB;IACA,KAAK,MAAMwG,CAAC,IAAIH,IAAI,EAAE;MAClB,IAAIA,IAAI,CAACG,CAAC,CAAC,KAAKF,IAAI,CAACE,CAAC,CAAC,EAAE;QACrB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA9J,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,WAAW,CAACkB,eAAe,CAAC,CAAC;IAClC,IAAI,IAAI,CAACrC,MAAM,CAACoD,WAAW,CAAC,CAAC,EAAE;MAC3B,IAAI,CAACc,OAAO,CAAC,CAAC;IAClB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACItC,QAAQA,CAACoB,QAAQ,EAAE;IACf,IAAI,CAACT,GAAG,CAACvD,cAAc,CAACiJ,KAAK,EAAE,CAAC,CAAC,EAAEjF,QAAQ,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;EACIhB,QAAQA,CAACgB,QAAQ,EAAE;IACf,IAAI,CAACT,GAAG,CAACvD,cAAc,CAACyG,KAAK,EAAE,CAAC,CAAC,EAAGxD,MAAM,IAAKe,QAAQ,CAACf,MAAM,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;EACIyE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1G,MAAM,CAACoD,WAAW,CAAC,CAAC,IAAI,IAAI,CAACsH,SAAS,CAAC,CAAC;EACxD;EACA;EACAxG,OAAOA,CAAClD,OAAO,GAAG,IAAI,CAACA,OAAO,EAAE;IAC5B,IAAI,IAAI,CAACkB,UAAU,CAAC,CAAC,EAAE;MACnB;IACJ;IACA,IAAI,CAAClC,MAAM,CAACmL,eAAe,CAAC,IAAI,CAACtL,KAAK,CAAC;IACvC,IAAI,CAACK,KAAK,GAAGjB,cAAc,CAAC0L,OAAO;IACnC,IAAI,CAAC1J,QAAQ,CAACmK,MAAM,CAACpK,OAAO,CAAC;EACjC;EACA;EACAyJ,kBAAkBA,CAAChI,OAAO,EAAE;IACxB,MAAM4I,OAAO,GAAG;MACZd,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC;IACV,CAAC;IACD,IAAI/H,OAAO,CAAC0D,IAAI,KAAK,QAAQ,IAAI1D,OAAO,CAAC0D,IAAI,KAAK,QAAQ,EAAE;MACxDkF,OAAO,CAACd,GAAG,GAAGlL,YAAY,CAACiM,iBAAiB,CAAC7I,OAAO,CAAC8I,OAAO,EAAE9I,OAAO,CAAC+I,MAAM,CAAC;IACjF;IACA,IAAI/I,OAAO,CAAC0D,IAAI,KAAK,QAAQ,IAAI1D,OAAO,CAAC0D,IAAI,KAAK,QAAQ,EAAE;MACxDkF,OAAO,CAACb,GAAG,GAAGnL,YAAY,CAACiM,iBAAiB,CAAC7I,OAAO,CAAC8I,OAAO,EAAE9I,OAAO,CAACgJ,UAAU,CAAC;IACrF;IACA,OAAOJ,OAAO;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}