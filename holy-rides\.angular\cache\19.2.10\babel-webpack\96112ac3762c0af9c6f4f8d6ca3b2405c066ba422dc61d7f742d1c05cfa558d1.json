{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n  _platformId = inject(PLATFORM_ID);\n  // We want to use the Angular platform check because if the Document is shimmed\n  // without the navigator, the following checks will fail. This is preferred because\n  // sometimes the Document may be shimmed without the user's knowledge or intention\n  /** Whether the Angular application is being rendered in the browser. */\n  isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n  /** Whether the current browser is Microsoft Edge. */\n  EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n  /** Whether the current rendering engine is Microsoft Trident. */\n  TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n  // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n  /** Whether the current rendering engine is Blink. */\n  BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT;\n  // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n  // ensure that Webkit runs standalone and is not used as another engine's base.\n  /** Whether the current rendering engine is WebKit. */\n  WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n  /** Whether the current platform is Apple iOS. */\n  IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n  // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n  // them self as Gecko-like browsers and modify the userAgent's according to that.\n  // Since we only cover one explicit Firefox case, we can simply check for Firefox\n  // instead of having an unstable check for Gecko.\n  /** Whether the current browser is Firefox. */\n  FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n  /** Whether the current platform is Android. */\n  // Trident on mobile adds the android platform to the userAgent to trick detections.\n  ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n  // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n  // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n  // Safari browser should also use Webkit as its layout engine.\n  /** Whether the current browser is Safari. */\n  SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  constructor() {}\n  static ɵfac = function Platform_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Platform)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Platform,\n    factory: Platform.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Platform as P };", "map": {"version": 3, "names": ["i0", "inject", "PLATFORM_ID", "Injectable", "isPlatformBrowser", "hasV8BreakIterator", "Intl", "v8BreakIterator", "Platform", "_platformId", "<PERSON><PERSON><PERSON><PERSON>", "document", "EDGE", "test", "navigator", "userAgent", "TRIDENT", "BLINK", "window", "chrome", "CSS", "WEBKIT", "IOS", "FIREFOX", "ANDROID", "SAFARI", "constructor", "ɵfac", "Platform_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "P"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/platform-DmdVEw_C.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, Injectable } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n}\ncatch {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    _platformId = inject(PLATFORM_ID);\n    // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n    /** Whether the Angular application is being rendered in the browser. */\n    isBrowser = this._platformId\n        ? isPlatformBrowser(this._platformId)\n        : typeof document === 'object' && !!document;\n    /** Whether the current browser is Microsoft Edge. */\n    EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n    /** Whether the current rendering engine is Microsoft Trident. */\n    TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n    // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n    /** Whether the current rendering engine is Blink. */\n    BLINK = this.isBrowser &&\n        !!(window.chrome || hasV8BreakIterator) &&\n        typeof CSS !== 'undefined' &&\n        !this.EDGE &&\n        !this.TRIDENT;\n    // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n    /** Whether the current rendering engine is WebKit. */\n    WEBKIT = this.isBrowser &&\n        /AppleWebKit/i.test(navigator.userAgent) &&\n        !this.BLINK &&\n        !this.EDGE &&\n        !this.TRIDENT;\n    /** Whether the current platform is Apple iOS. */\n    IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n    // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n    /** Whether the current browser is Firefox. */\n    FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n    ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n    // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n    /** Whether the current browser is Safari. */\n    SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nexport { Platform as P };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,QAAQ,eAAe;AAC/D,SAASC,iBAAiB,QAAQ,iBAAiB;;AAEnD;AACA;AACA,IAAIC,kBAAkB;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI;EACAA,kBAAkB,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACC,eAAe;AAC5E,CAAC,CACD,MAAM;EACFF,kBAAkB,GAAG,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA,MAAMG,QAAQ,CAAC;EACXC,WAAW,GAAGR,MAAM,CAACC,WAAW,CAAC;EACjC;EACA;EACA;EACA;EACAQ,SAAS,GAAG,IAAI,CAACD,WAAW,GACtBL,iBAAiB,CAAC,IAAI,CAACK,WAAW,CAAC,GACnC,OAAOE,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAACA,QAAQ;EAChD;EACAC,IAAI,GAAG,IAAI,CAACF,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EAC5D;EACAC,OAAO,GAAG,IAAI,CAACN,SAAS,IAAI,iBAAiB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EACvE;EACA;EACAE,KAAK,GAAG,IAAI,CAACP,SAAS,IAClB,CAAC,EAAEQ,MAAM,CAACC,MAAM,IAAId,kBAAkB,CAAC,IACvC,OAAOe,GAAG,KAAK,WAAW,IAC1B,CAAC,IAAI,CAACR,IAAI,IACV,CAAC,IAAI,CAACI,OAAO;EACjB;EACA;EACA;EACAK,MAAM,GAAG,IAAI,CAACX,SAAS,IACnB,cAAc,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IACxC,CAAC,IAAI,CAACE,KAAK,IACX,CAAC,IAAI,CAACL,IAAI,IACV,CAAC,IAAI,CAACI,OAAO;EACjB;EACAM,GAAG,GAAG,IAAI,CAACZ,SAAS,IAAI,kBAAkB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,EAAE,UAAU,IAAIG,MAAM,CAAC;EAC/F;EACA;EACA;EACA;EACA;EACAK,OAAO,GAAG,IAAI,CAACb,SAAS,IAAI,sBAAsB,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;EAC5E;EACA;EACAS,OAAO,GAAG,IAAI,CAACd,SAAS,IAAI,UAAU,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAACC,OAAO;EACjF;EACA;EACA;EACA;EACAS,MAAM,GAAG,IAAI,CAACf,SAAS,IAAI,SAAS,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,IAAI,CAACM,MAAM;EAC7EK,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAOC,IAAI,YAAAC,iBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFrB,QAAQ;EAAA;EAC3G,OAAOsB,KAAK,kBAD6E9B,EAAE,CAAA+B,kBAAA;IAAAC,KAAA,EACYxB,QAAQ;IAAAyB,OAAA,EAARzB,QAAQ,CAAAmB,IAAA;IAAAO,UAAA,EAAc;EAAM;AACvI;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FnC,EAAE,CAAAoC,iBAAA,CAGJ5B,QAAQ,EAAc,CAAC;IACtG6B,IAAI,EAAElC,UAAU;IAChBmC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAAS1B,QAAQ,IAAI+B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}