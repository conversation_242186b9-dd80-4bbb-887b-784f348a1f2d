{"version": 3, "sources": ["../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/version.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/cjs/index.js", "../../../../../../node_modules/@supabase/functions-js/dist/module/helper.js", "../../../../../../node_modules/@supabase/functions-js/dist/module/types.js", "../../../../../../node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "../../../../../../node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/version.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/lib/push.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "../../../../../../node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/lib/errors.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/lib/version.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/lib/constants.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "../../../../../../node_modules/@supabase/storage-js/dist/module/StorageClient.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/lib/version.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/errors.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/version.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/constants.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/locks.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "../../../../../../node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/AuthClient.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "../../../../../../node_modules/@supabase/supabase-js/dist/module/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n  constructor(context) {\n    super(context.message);\n    this.name = 'PostgrestError';\n    this.details = context.details;\n    this.hint = context.hint;\n    this.code = context.code;\n  }\n}\nexports.default = PostgrestError;\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n// @ts-ignore\nconst node_fetch_1 = __importDefault(require(\"@supabase/node-fetch\"));\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nclass PostgrestBuilder {\n  constructor(builder) {\n    this.shouldThrowOnError = false;\n    this.method = builder.method;\n    this.url = builder.url;\n    this.headers = builder.headers;\n    this.schema = builder.schema;\n    this.body = builder.body;\n    this.shouldThrowOnError = builder.shouldThrowOnError;\n    this.signal = builder.signal;\n    this.isMaybeSingle = builder.isMaybeSingle;\n    if (builder.fetch) {\n      this.fetch = builder.fetch;\n    } else if (typeof fetch === 'undefined') {\n      this.fetch = node_fetch_1.default;\n    } else {\n      this.fetch = fetch;\n    }\n  }\n  /**\n   * If there's an error with the query, throwOnError will reject the promise by\n   * throwing the error instead of returning it as part of a successful response.\n   *\n   * {@link https://github.com/supabase/supabase-js/issues/92}\n   */\n  throwOnError() {\n    this.shouldThrowOnError = true;\n    return this;\n  }\n  /**\n   * Set an HTTP header for the request.\n   */\n  setHeader(name, value) {\n    this.headers = Object.assign({}, this.headers);\n    this.headers[name] = value;\n    return this;\n  }\n  then(onfulfilled, onrejected) {\n    // https://postgrest.org/en/stable/api.html#switching-schemas\n    if (this.schema === undefined) {\n      // skip\n    } else if (['GET', 'HEAD'].includes(this.method)) {\n      this.headers['Accept-Profile'] = this.schema;\n    } else {\n      this.headers['Content-Profile'] = this.schema;\n    }\n    if (this.method !== 'GET' && this.method !== 'HEAD') {\n      this.headers['Content-Type'] = 'application/json';\n    }\n    // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n    // https://github.com/supabase/postgrest-js/pull/247\n    const _fetch = this.fetch;\n    let res = _fetch(this.url.toString(), {\n      method: this.method,\n      headers: this.headers,\n      body: JSON.stringify(this.body),\n      signal: this.signal\n    }).then(async res => {\n      var _a, _b, _c;\n      let error = null;\n      let data = null;\n      let count = null;\n      let status = res.status;\n      let statusText = res.statusText;\n      if (res.ok) {\n        if (this.method !== 'HEAD') {\n          const body = await res.text();\n          if (body === '') {\n            // Prefer: return=minimal\n          } else if (this.headers['Accept'] === 'text/csv') {\n            data = body;\n          } else if (this.headers['Accept'] && this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n            data = body;\n          } else {\n            data = JSON.parse(body);\n          }\n        }\n        const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n        const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n        if (countHeader && contentRange && contentRange.length > 1) {\n          count = parseInt(contentRange[1]);\n        }\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n          if (data.length > 1) {\n            error = {\n              // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n              code: 'PGRST116',\n              details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n              hint: null,\n              message: 'JSON object requested, multiple (or no) rows returned'\n            };\n            data = null;\n            count = null;\n            status = 406;\n            statusText = 'Not Acceptable';\n          } else if (data.length === 1) {\n            data = data[0];\n          } else {\n            data = null;\n          }\n        }\n      } else {\n        const body = await res.text();\n        try {\n          error = JSON.parse(body);\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (Array.isArray(error) && res.status === 404) {\n            data = [];\n            error = null;\n            status = 200;\n            statusText = 'OK';\n          }\n        } catch (_d) {\n          // Workaround for https://github.com/supabase/postgrest-js/issues/295\n          if (res.status === 404 && body === '') {\n            status = 204;\n            statusText = 'No Content';\n          } else {\n            error = {\n              message: body\n            };\n          }\n        }\n        if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n          error = null;\n          status = 200;\n          statusText = 'OK';\n        }\n        if (error && this.shouldThrowOnError) {\n          throw new PostgrestError_1.default(error);\n        }\n      }\n      const postgrestResponse = {\n        error,\n        data,\n        count,\n        status,\n        statusText\n      };\n      return postgrestResponse;\n    });\n    if (!this.shouldThrowOnError) {\n      res = res.catch(fetchError => {\n        var _a, _b, _c;\n        return {\n          error: {\n            message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n            details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n            hint: '',\n            code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`\n          },\n          data: null,\n          count: null,\n          status: 0,\n          statusText: ''\n        };\n      });\n    }\n    return res.then(onfulfilled, onrejected);\n  }\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns() {\n    /* istanbul ignore next */\n    return this;\n  }\n  /**\n   * Override the type of the returned `data` field in the response.\n   *\n   * @typeParam NewResult - The new type to cast the response data to\n   * @typeParam Options - Optional type configuration (defaults to { merge: true })\n   * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n   * @example\n   * ```typescript\n   * // Merge with existing types (default behavior)\n   * const query = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ custom_field: string }>()\n   *\n   * // Replace existing types completely\n   * const replaceQuery = supabase\n   *   .from('users')\n   *   .select()\n   *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n   * ```\n   * @returns A PostgrestBuilder instance with the new type\n   */\n  overrideTypes() {\n    return this;\n  }\n}\nexports.default = PostgrestBuilder;\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n  /**\n   * Perform a SELECT on the query result.\n   *\n   * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n   * return modified rows. By calling this method, modified rows are returned in\n   * `data`.\n   *\n   * @param columns - The columns to retrieve, separated by commas\n   */\n  select(columns) {\n    // Remove whitespaces except when quoted\n    let quoted = false;\n    const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*').split('').map(c => {\n      if (/\\s/.test(c) && !quoted) {\n        return '';\n      }\n      if (c === '\"') {\n        quoted = !quoted;\n      }\n      return c;\n    }).join('');\n    this.url.searchParams.set('select', cleanedColumns);\n    if (this.headers['Prefer']) {\n      this.headers['Prefer'] += ',';\n    }\n    this.headers['Prefer'] += 'return=representation';\n    return this;\n  }\n  /**\n   * Order the query result by `column`.\n   *\n   * You can call this method multiple times to order by multiple columns.\n   *\n   * You can order referenced tables, but it only affects the ordering of the\n   * parent table if you use `!inner` in the query.\n   *\n   * @param column - The column to order by\n   * @param options - Named parameters\n   * @param options.ascending - If `true`, the result will be in ascending order\n   * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n   * `null`s appear last.\n   * @param options.referencedTable - Set this to order a referenced table by\n   * its columns\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  order(column, {\n    ascending = true,\n    nullsFirst,\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = referencedTable ? `${referencedTable}.order` : 'order';\n    const existingOrder = this.url.searchParams.get(key);\n    this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n    return this;\n  }\n  /**\n   * Limit the query result by `count`.\n   *\n   * @param count - The maximum number of rows to return\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  limit(count, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n    this.url.searchParams.set(key, `${count}`);\n    return this;\n  }\n  /**\n   * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n   * Only records within this range are returned.\n   * This respects the query order and if there is no order clause the range could behave unexpectedly.\n   * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n   * and fourth rows of the query.\n   *\n   * @param from - The starting index from which to limit the result\n   * @param to - The last index to which to limit the result\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to limit rows of referenced\n   * tables instead of the parent table\n   * @param options.foreignTable - Deprecated, use `options.referencedTable`\n   * instead\n   */\n  range(from, to, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n    const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n    this.url.searchParams.set(keyOffset, `${from}`);\n    // Range is inclusive, so add 1\n    this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n    return this;\n  }\n  /**\n   * Set the AbortSignal for the fetch request.\n   *\n   * @param signal - The AbortSignal to use for the fetch request\n   */\n  abortSignal(signal) {\n    this.signal = signal;\n    return this;\n  }\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n   * returns an error.\n   */\n  single() {\n    this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n    return this;\n  }\n  /**\n   * Return `data` as a single object instead of an array of objects.\n   *\n   * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n   * this returns an error.\n   */\n  maybeSingle() {\n    // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n    // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n    if (this.method === 'GET') {\n      this.headers['Accept'] = 'application/json';\n    } else {\n      this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n    }\n    this.isMaybeSingle = true;\n    return this;\n  }\n  /**\n   * Return `data` as a string in CSV format.\n   */\n  csv() {\n    this.headers['Accept'] = 'text/csv';\n    return this;\n  }\n  /**\n   * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n   */\n  geojson() {\n    this.headers['Accept'] = 'application/geo+json';\n    return this;\n  }\n  /**\n   * Return `data` as the EXPLAIN plan for the query.\n   *\n   * You need to enable the\n   * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n   * setting before using this method.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.analyze - If `true`, the query will be executed and the\n   * actual run time will be returned\n   *\n   * @param options.verbose - If `true`, the query identifier will be returned\n   * and `data` will include the output columns of the query\n   *\n   * @param options.settings - If `true`, include information on configuration\n   * parameters that affect query planning\n   *\n   * @param options.buffers - If `true`, include information on buffer usage\n   *\n   * @param options.wal - If `true`, include information on WAL record generation\n   *\n   * @param options.format - The format of the output, can be `\"text\"` (default)\n   * or `\"json\"`\n   */\n  explain({\n    analyze = false,\n    verbose = false,\n    settings = false,\n    buffers = false,\n    wal = false,\n    format = 'text'\n  } = {}) {\n    var _a;\n    const options = [analyze ? 'analyze' : null, verbose ? 'verbose' : null, settings ? 'settings' : null, buffers ? 'buffers' : null, wal ? 'wal' : null].filter(Boolean).join('|');\n    // An Accept header can carry multiple media types but postgrest-js always sends one\n    const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n    this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n    if (format === 'json') return this;else return this;\n  }\n  /**\n   * Rollback the query.\n   *\n   * `data` will still be returned, but the query is not committed.\n   */\n  rollback() {\n    var _a;\n    if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n      this.headers['Prefer'] += ',tx=rollback';\n    } else {\n      this.headers['Prefer'] = 'tx=rollback';\n    }\n    return this;\n  }\n  /**\n   * Override the type of the returned `data`.\n   *\n   * @typeParam NewResult - The new result type to override with\n   * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n   */\n  returns() {\n    return this;\n  }\n}\nexports.default = PostgrestTransformBuilder;\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq(column, value) {\n    this.url.searchParams.append(column, `eq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq(column, value) {\n    this.url.searchParams.append(column, `neq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column, value) {\n    this.url.searchParams.append(column, `gt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column, value) {\n    this.url.searchParams.append(column, `gte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column, value) {\n    this.url.searchParams.append(column, `lt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column, value) {\n    this.url.searchParams.append(column, `lte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column, pattern) {\n    this.url.searchParams.append(column, `like.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column, pattern) {\n    this.url.searchParams.append(column, `ilike.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column, value) {\n    this.url.searchParams.append(column, `is.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in(column, values) {\n    const cleanedValues = Array.from(new Set(values)).map(s => {\n      // handle postgrest reserved characters\n      // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n      if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`;else return `${s}`;\n    }).join(',');\n    this.url.searchParams.append(column, `in.(${cleanedValues})`);\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column, value) {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column, range) {\n    this.url.searchParams.append(column, `sr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column, range) {\n    this.url.searchParams.append(column, `nxl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column, range) {\n    this.url.searchParams.append(column, `sl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column, range) {\n    this.url.searchParams.append(column, `nxr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column, range) {\n    this.url.searchParams.append(column, `adj.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`);\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(column, query, {\n    config,\n    type\n  } = {}) {\n    let typePart = '';\n    if (type === 'plain') {\n      typePart = 'pl';\n    } else if (type === 'phrase') {\n      typePart = 'ph';\n    } else if (type === 'websearch') {\n      typePart = 'w';\n    }\n    const configPart = config === undefined ? '' : `(${config})`;\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n    return this;\n  }\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query) {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`);\n    });\n    return this;\n  }\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column, operator, value) {\n    this.url.searchParams.append(column, `not.${operator}.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(filters, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = referencedTable ? `${referencedTable}.or` : 'or';\n    this.url.searchParams.append(key, `(${filters})`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column, operator, value) {\n    this.url.searchParams.append(column, `${operator}.${value}`);\n    return this;\n  }\n}\nexports.default = PostgrestFilterBuilder;\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nclass PostgrestQueryBuilder {\n  constructor(url, {\n    headers = {},\n    schema,\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.schema = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a SELECT query on the table or view.\n   *\n   * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n   *\n   * @param options - Named parameters\n   *\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   *\n   * @param options.count - Count algorithm to use to count rows in the table or view.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  select(columns, {\n    head = false,\n    count\n  } = {}) {\n    const method = head ? 'HEAD' : 'GET';\n    // Remove whitespaces except when quoted\n    let quoted = false;\n    const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*').split('').map(c => {\n      if (/\\s/.test(c) && !quoted) {\n        return '';\n      }\n      if (c === '\"') {\n        quoted = !quoted;\n      }\n      return c;\n    }).join('');\n    this.url.searchParams.set('select', cleanedColumns);\n    if (count) {\n      this.headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an INSERT into the table or view.\n   *\n   * By default, inserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to insert. Pass an object to insert a single row\n   * or an array to insert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count inserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. Only applies for bulk\n   * inserts.\n   */\n  insert(values, {\n    count,\n    defaultToNull = true\n  } = {}) {\n    const method = 'POST';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPSERT on the table or view. Depending on the column(s) passed\n   * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n   * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n   * exist, or if it does exist, perform an alternative action depending on\n   * `ignoreDuplicates`.\n   *\n   * By default, upserted rows are not returned. To return it, chain the call\n   * with `.select()`.\n   *\n   * @param values - The values to upsert with. Pass an object to upsert a\n   * single row or an array to upsert multiple rows.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n   * duplicate rows are determined. Two rows are duplicates if all the\n   * `onConflict` columns are equal.\n   *\n   * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n   * `false`, duplicate rows are merged with existing rows.\n   *\n   * @param options.count - Count algorithm to use to count upserted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   *\n   * @param options.defaultToNull - Make missing fields default to `null`.\n   * Otherwise, use the default value for the column. This only applies when\n   * inserting new rows, not when merging with existing rows under\n   * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n   */\n  upsert(values, {\n    onConflict,\n    ignoreDuplicates = false,\n    count,\n    defaultToNull = true\n  } = {}) {\n    const method = 'POST';\n    const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n    if (onConflict !== undefined) this.url.searchParams.set('on_conflict', onConflict);\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (!defaultToNull) {\n      prefersHeaders.push('missing=default');\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    if (Array.isArray(values)) {\n      const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n      if (columns.length > 0) {\n        const uniqueColumns = [...new Set(columns)].map(column => `\"${column}\"`);\n        this.url.searchParams.set('columns', uniqueColumns.join(','));\n      }\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform an UPDATE on the table or view.\n   *\n   * By default, updated rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param values - The values to update with\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count updated rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  update(values, {\n    count\n  } = {}) {\n    const method = 'PATCH';\n    const prefersHeaders = [];\n    if (this.headers['Prefer']) {\n      prefersHeaders.push(this.headers['Prefer']);\n    }\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      body: values,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n  /**\n   * Perform a DELETE on the table or view.\n   *\n   * By default, deleted rows are not returned. To return it, chain the call\n   * with `.select()` after filters.\n   *\n   * @param options - Named parameters\n   *\n   * @param options.count - Count algorithm to use to count deleted rows.\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  delete({\n    count\n  } = {}) {\n    const method = 'DELETE';\n    const prefersHeaders = [];\n    if (count) {\n      prefersHeaders.push(`count=${count}`);\n    }\n    if (this.headers['Prefer']) {\n      prefersHeaders.unshift(this.headers['Prefer']);\n    }\n    this.headers['Prefer'] = prefersHeaders.join(',');\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url: this.url,\n      headers: this.headers,\n      schema: this.schema,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestQueryBuilder;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = require(\"./version\");\nexports.DEFAULT_HEADERS = {\n  'X-Client-Info': `postgrest-js/${version_1.version}`\n};\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(url, {\n    headers = {},\n    schema,\n    fetch\n  } = {}) {\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n    this.schemaName = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    const url = new URL(`${this.url}/${relation}`);\n    return new PostgrestQueryBuilder_1.default(url, {\n      headers: Object.assign({}, this.headers),\n      schema: this.schemaName,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, {\n    head = false,\n    get = false,\n    count\n  } = {}) {\n    let method;\n    const url = new URL(`${this.url}/rpc/${fn}`);\n    let body;\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET';\n      Object.entries(args)\n      // params with undefined value needs to be filtered out, otherwise it'll\n      // show up as `?param=undefined`\n      .filter(([_, value]) => value !== undefined)\n      // array values need special syntax\n      .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`]).forEach(([name, value]) => {\n        url.searchParams.append(name, value);\n      });\n    } else {\n      method = 'POST';\n      body = args;\n    }\n    const headers = Object.assign({}, this.headers);\n    if (count) {\n      headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestClient;\n", "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(require(\"./PostgrestClient\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(require(\"./PostgrestBuilder\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(require(\"./PostgrestError\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports.default = {\n  PostgrestClient: PostgrestClient_1.default,\n  PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n  PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n  PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n  PostgrestBuilder: PostgrestBuilder_1.default,\n  PostgrestError: PostgrestError_1.default\n};\n", "export const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\n", "export class FunctionsError extends Error {\n  constructor(message, name = 'FunctionsError', context) {\n    super(message);\n    this.name = name;\n    this.context = context;\n  }\n}\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n  }\n}\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n  }\n}\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n  }\n}\n// Define the enum for the 'region' property\nexport var FunctionRegion;\n(function (FunctionRegion) {\n  FunctionRegion[\"Any\"] = \"any\";\n  FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n  FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n  FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n  FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n  FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n  FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n  FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n  FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n  FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n  FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n  FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n  FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n  FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n  FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';\nexport class FunctionsClient {\n  constructor(url, {\n    headers = {},\n    customFetch,\n    region = FunctionRegion.Any\n  } = {}) {\n    this.url = url;\n    this.headers = headers;\n    this.region = region;\n    this.fetch = resolveFetch(customFetch);\n  }\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token) {\n    this.headers.Authorization = `Bearer ${token}`;\n  }\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  invoke(functionName, options = {}) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const {\n          headers,\n          method,\n          body: functionArgs\n        } = options;\n        let _headers = {};\n        let {\n          region\n        } = options;\n        if (!region) {\n          region = this.region;\n        }\n        if (region && region !== 'any') {\n          _headers['x-region'] = region;\n        }\n        let body;\n        if (functionArgs && (headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type') || !headers)) {\n          if (typeof Blob !== 'undefined' && functionArgs instanceof Blob || functionArgs instanceof ArrayBuffer) {\n            // will work for File as File inherits Blob\n            // also works for ArrayBuffer as it is the same underlying structure as a Blob\n            _headers['Content-Type'] = 'application/octet-stream';\n            body = functionArgs;\n          } else if (typeof functionArgs === 'string') {\n            // plain string\n            _headers['Content-Type'] = 'text/plain';\n            body = functionArgs;\n          } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n            // don't set content-type headers\n            // Request will automatically add the right boundary value\n            body = functionArgs;\n          } else {\n            // default, assume this is JSON\n            _headers['Content-Type'] = 'application/json';\n            body = JSON.stringify(functionArgs);\n          }\n        }\n        const response = yield this.fetch(`${this.url}/${functionName}`, {\n          method: method || 'POST',\n          // headers priority is (high to low):\n          // 1. invoke-level headers\n          // 2. client-level headers\n          // 3. default Content-Type header\n          headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n          body\n        }).catch(fetchError => {\n          throw new FunctionsFetchError(fetchError);\n        });\n        const isRelayError = response.headers.get('x-relay-error');\n        if (isRelayError && isRelayError === 'true') {\n          throw new FunctionsRelayError(response);\n        }\n        if (!response.ok) {\n          throw new FunctionsHttpError(response);\n        }\n        let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n        let data;\n        if (responseType === 'application/json') {\n          data = yield response.json();\n        } else if (responseType === 'application/octet-stream') {\n          data = yield response.blob();\n        } else if (responseType === 'text/event-stream') {\n          data = response;\n        } else if (responseType === 'multipart/form-data') {\n          data = yield response.formData();\n        } else {\n          // default to text\n          data = yield response.text();\n        }\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        return {\n          data: null,\n          error\n        };\n      }\n    });\n  }\n}\n", "import index from '../cjs/index.js';\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError\n} = index;\nexport { PostgrestBuilder, PostgrestClient, PostgrestFilterBuilder, PostgrestQueryBuilder, PostgrestTransformBuilder, PostgrestError };\n\n// compatibility with CJS output\nexport default {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError\n};", "export const version = '2.11.2';\n", "import { version } from './version';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `realtime-js/${version}`\n};\nexport const VSN = '1.0.0';\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES;\n(function (SOCKET_STATES) {\n  SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n  SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n  SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n  SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nexport var CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n  CHANNEL_STATES[\"closed\"] = \"closed\";\n  CHANNEL_STATES[\"errored\"] = \"errored\";\n  CHANNEL_STATES[\"joined\"] = \"joined\";\n  CHANNEL_STATES[\"joining\"] = \"joining\";\n  CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nexport var CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n  CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n  CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n  CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n  CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n  CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n  CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nexport var TRANSPORTS;\n(function (TRANSPORTS) {\n  TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nexport var CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n  CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n  CONNECTION_STATE[\"Open\"] = \"open\";\n  CONNECTION_STATE[\"Closing\"] = \"closing\";\n  CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n", "// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nexport default class Serializer {\n  constructor() {\n    this.HEADER_LENGTH = 1;\n  }\n  decode(rawPayload, callback) {\n    if (rawPayload.constructor === ArrayBuffer) {\n      return callback(this._binaryDecode(rawPayload));\n    }\n    if (typeof rawPayload === 'string') {\n      return callback(JSON.parse(rawPayload));\n    }\n    return callback({});\n  }\n  _binaryDecode(buffer) {\n    const view = new DataView(buffer);\n    const decoder = new TextDecoder();\n    return this._decodeBroadcast(buffer, view, decoder);\n  }\n  _decodeBroadcast(buffer, view, decoder) {\n    const topicSize = view.getUint8(1);\n    const eventSize = view.getUint8(2);\n    let offset = this.HEADER_LENGTH + 2;\n    const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n    offset = offset + topicSize;\n    const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n    offset = offset + eventSize;\n    const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n    return {\n      ref: null,\n      topic: topic,\n      event: event,\n      payload: data\n    };\n  }\n}\n", "/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nexport default class Timer {\n  constructor(callback, timerCalc) {\n    this.callback = callback;\n    this.timerCalc = timerCalc;\n    this.timer = undefined;\n    this.tries = 0;\n    this.callback = callback;\n    this.timerCalc = timerCalc;\n  }\n  reset() {\n    this.tries = 0;\n    clearTimeout(this.timer);\n  }\n  // Cancels any previous scheduleTimeout and schedules callback\n  scheduleTimeout() {\n    clearTimeout(this.timer);\n    this.timer = setTimeout(() => {\n      this.tries = this.tries + 1;\n      this.callback();\n    }, this.timerCalc(this.tries + 1));\n  }\n}\n", "/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nexport var PostgresTypes;\n(function (PostgresTypes) {\n  PostgresTypes[\"abstime\"] = \"abstime\";\n  PostgresTypes[\"bool\"] = \"bool\";\n  PostgresTypes[\"date\"] = \"date\";\n  PostgresTypes[\"daterange\"] = \"daterange\";\n  PostgresTypes[\"float4\"] = \"float4\";\n  PostgresTypes[\"float8\"] = \"float8\";\n  PostgresTypes[\"int2\"] = \"int2\";\n  PostgresTypes[\"int4\"] = \"int4\";\n  PostgresTypes[\"int4range\"] = \"int4range\";\n  PostgresTypes[\"int8\"] = \"int8\";\n  PostgresTypes[\"int8range\"] = \"int8range\";\n  PostgresTypes[\"json\"] = \"json\";\n  PostgresTypes[\"jsonb\"] = \"jsonb\";\n  PostgresTypes[\"money\"] = \"money\";\n  PostgresTypes[\"numeric\"] = \"numeric\";\n  PostgresTypes[\"oid\"] = \"oid\";\n  PostgresTypes[\"reltime\"] = \"reltime\";\n  PostgresTypes[\"text\"] = \"text\";\n  PostgresTypes[\"time\"] = \"time\";\n  PostgresTypes[\"timestamp\"] = \"timestamp\";\n  PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n  PostgresTypes[\"timetz\"] = \"timetz\";\n  PostgresTypes[\"tsrange\"] = \"tsrange\";\n  PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nexport const convertChangeData = (columns, record, options = {}) => {\n  var _a;\n  const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n  return Object.keys(record).reduce((acc, rec_key) => {\n    acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n    return acc;\n  }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nexport const convertColumn = (columnName, columns, record, skipTypes) => {\n  const column = columns.find(x => x.name === columnName);\n  const colType = column === null || column === void 0 ? void 0 : column.type;\n  const value = record[columnName];\n  if (colType && !skipTypes.includes(colType)) {\n    return convertCell(colType, value);\n  }\n  return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nexport const convertCell = (type, value) => {\n  // if data type is an array\n  if (type.charAt(0) === '_') {\n    const dataType = type.slice(1, type.length);\n    return toArray(value, dataType);\n  }\n  // If not null, convert to correct type.\n  switch (type) {\n    case PostgresTypes.bool:\n      return toBoolean(value);\n    case PostgresTypes.float4:\n    case PostgresTypes.float8:\n    case PostgresTypes.int2:\n    case PostgresTypes.int4:\n    case PostgresTypes.int8:\n    case PostgresTypes.numeric:\n    case PostgresTypes.oid:\n      return toNumber(value);\n    case PostgresTypes.json:\n    case PostgresTypes.jsonb:\n      return toJson(value);\n    case PostgresTypes.timestamp:\n      return toTimestampString(value);\n    // Format to be consistent with PostgREST\n    case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n    case PostgresTypes.date: // To allow users to cast it based on Timezone\n    case PostgresTypes.daterange:\n    case PostgresTypes.int4range:\n    case PostgresTypes.int8range:\n    case PostgresTypes.money:\n    case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n    case PostgresTypes.text:\n    case PostgresTypes.time: // To allow users to cast it based on Timezone\n    case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n    case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n    case PostgresTypes.tsrange:\n    case PostgresTypes.tstzrange:\n      return noop(value);\n    default:\n      // Return the value for remaining types\n      return noop(value);\n  }\n};\nconst noop = value => {\n  return value;\n};\nexport const toBoolean = value => {\n  switch (value) {\n    case 't':\n      return true;\n    case 'f':\n      return false;\n    default:\n      return value;\n  }\n};\nexport const toNumber = value => {\n  if (typeof value === 'string') {\n    const parsedValue = parseFloat(value);\n    if (!Number.isNaN(parsedValue)) {\n      return parsedValue;\n    }\n  }\n  return value;\n};\nexport const toJson = value => {\n  if (typeof value === 'string') {\n    try {\n      return JSON.parse(value);\n    } catch (error) {\n      console.log(`JSON parse error: ${error}`);\n      return value;\n    }\n  }\n  return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nexport const toArray = (value, type) => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  const lastIdx = value.length - 1;\n  const closeBrace = value[lastIdx];\n  const openBrace = value[0];\n  // Confirm value is a Postgres array by checking curly brackets\n  if (openBrace === '{' && closeBrace === '}') {\n    let arr;\n    const valTrim = value.slice(1, lastIdx);\n    // TODO: find a better solution to separate Postgres array data\n    try {\n      arr = JSON.parse('[' + valTrim + ']');\n    } catch (_) {\n      // WARNING: splitting on comma does not cover all edge cases\n      arr = valTrim ? valTrim.split(',') : [];\n    }\n    return arr.map(val => convertCell(type, val));\n  }\n  return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nexport const toTimestampString = value => {\n  if (typeof value === 'string') {\n    return value.replace(' ', 'T');\n  }\n  return value;\n};\nexport const httpEndpointURL = socketUrl => {\n  let url = socketUrl;\n  url = url.replace(/^ws/i, 'http');\n  url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n  return url.replace(/\\/+$/, '');\n};\n", "import { DEFAULT_TIMEOUT } from '../lib/constants';\nexport default class Push {\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(channel, event, payload = {}, timeout = DEFAULT_TIMEOUT) {\n    this.channel = channel;\n    this.event = event;\n    this.payload = payload;\n    this.timeout = timeout;\n    this.sent = false;\n    this.timeoutTimer = undefined;\n    this.ref = '';\n    this.receivedResp = null;\n    this.recHooks = [];\n    this.refEvent = null;\n  }\n  resend(timeout) {\n    this.timeout = timeout;\n    this._cancelRefEvent();\n    this.ref = '';\n    this.refEvent = null;\n    this.receivedResp = null;\n    this.sent = false;\n    this.send();\n  }\n  send() {\n    if (this._hasReceived('timeout')) {\n      return;\n    }\n    this.startTimeout();\n    this.sent = true;\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef()\n    });\n  }\n  updatePayload(payload) {\n    this.payload = Object.assign(Object.assign({}, this.payload), payload);\n  }\n  receive(status, callback) {\n    var _a;\n    if (this._hasReceived(status)) {\n      callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n    }\n    this.recHooks.push({\n      status,\n      callback\n    });\n    return this;\n  }\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return;\n    }\n    this.ref = this.channel.socket._makeRef();\n    this.refEvent = this.channel._replyEventName(this.ref);\n    const callback = payload => {\n      this._cancelRefEvent();\n      this._cancelTimeout();\n      this.receivedResp = payload;\n      this._matchReceive(payload);\n    };\n    this.channel._on(this.refEvent, {}, callback);\n    this.timeoutTimer = setTimeout(() => {\n      this.trigger('timeout', {});\n    }, this.timeout);\n  }\n  trigger(status, response) {\n    if (this.refEvent) this.channel._trigger(this.refEvent, {\n      status,\n      response\n    });\n  }\n  destroy() {\n    this._cancelRefEvent();\n    this._cancelTimeout();\n  }\n  _cancelRefEvent() {\n    if (!this.refEvent) {\n      return;\n    }\n    this.channel._off(this.refEvent, {});\n  }\n  _cancelTimeout() {\n    clearTimeout(this.timeoutTimer);\n    this.timeoutTimer = undefined;\n  }\n  _matchReceive({\n    status,\n    response\n  }) {\n    this.recHooks.filter(h => h.status === status).forEach(h => h.callback(response));\n  }\n  _hasReceived(status) {\n    return this.receivedResp && this.receivedResp.status === status;\n  }\n}\n", "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nexport var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nexport default class RealtimePresence {\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(channel, opts) {\n    this.channel = channel;\n    this.state = {};\n    this.pendingDiffs = [];\n    this.joinRef = null;\n    this.caller = {\n      onJoin: () => {},\n      onLeave: () => {},\n      onSync: () => {}\n    };\n    const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n      state: 'presence_state',\n      diff: 'presence_diff'\n    };\n    this.channel._on(events.state, {}, newState => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      this.joinRef = this.channel._joinRef();\n      this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n      this.pendingDiffs.forEach(diff => {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n      });\n      this.pendingDiffs = [];\n      onSync();\n    });\n    this.channel._on(events.diff, {}, diff => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff);\n      } else {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n        onSync();\n      }\n    });\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences\n      });\n    });\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences\n      });\n    });\n    this.onSync(() => {\n      this.channel._trigger('presence', {\n        event: 'sync'\n      });\n    });\n  }\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  static syncState(currentState, newState, onJoin, onLeave) {\n    const state = this.cloneDeep(currentState);\n    const transformedState = this.transformState(newState);\n    const joins = {};\n    const leaves = {};\n    this.map(state, (key, presences) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences;\n      }\n    });\n    this.map(transformedState, (key, newPresences) => {\n      const currentPresences = state[key];\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(m => m.presence_ref);\n        const curPresenceRefs = currentPresences.map(m => m.presence_ref);\n        const joinedPresences = newPresences.filter(m => curPresenceRefs.indexOf(m.presence_ref) < 0);\n        const leftPresences = currentPresences.filter(m => newPresenceRefs.indexOf(m.presence_ref) < 0);\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences;\n        }\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences;\n        }\n      } else {\n        joins[key] = newPresences;\n      }\n    });\n    return this.syncDiff(state, {\n      joins,\n      leaves\n    }, onJoin, onLeave);\n  }\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  static syncDiff(state, diff, onJoin, onLeave) {\n    const {\n      joins,\n      leaves\n    } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves)\n    };\n    if (!onJoin) {\n      onJoin = () => {};\n    }\n    if (!onLeave) {\n      onLeave = () => {};\n    }\n    this.map(joins, (key, newPresences) => {\n      var _a;\n      const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n      state[key] = this.cloneDeep(newPresences);\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(m => m.presence_ref);\n        const curPresences = currentPresences.filter(m => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n        state[key].unshift(...curPresences);\n      }\n      onJoin(key, currentPresences, newPresences);\n    });\n    this.map(leaves, (key, leftPresences) => {\n      let currentPresences = state[key];\n      if (!currentPresences) return;\n      const presenceRefsToRemove = leftPresences.map(m => m.presence_ref);\n      currentPresences = currentPresences.filter(m => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n      state[key] = currentPresences;\n      onLeave(key, currentPresences, leftPresences);\n      if (currentPresences.length === 0) delete state[key];\n    });\n    return state;\n  }\n  /** @internal */\n  static map(obj, func) {\n    return Object.getOwnPropertyNames(obj).map(key => func(key, obj[key]));\n  }\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  static transformState(state) {\n    state = this.cloneDeep(state);\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key];\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map(presence => {\n          presence['presence_ref'] = presence['phx_ref'];\n          delete presence['phx_ref'];\n          delete presence['phx_ref_prev'];\n          return presence;\n        });\n      } else {\n        newState[key] = presences;\n      }\n      return newState;\n    }, {});\n  }\n  /** @internal */\n  static cloneDeep(obj) {\n    return JSON.parse(JSON.stringify(obj));\n  }\n  /** @internal */\n  onJoin(callback) {\n    this.caller.onJoin = callback;\n  }\n  /** @internal */\n  onLeave(callback) {\n    this.caller.onLeave = callback;\n  }\n  /** @internal */\n  onSync(callback) {\n    this.caller.onSync = callback;\n  }\n  /** @internal */\n  inPendingSyncState() {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef();\n  }\n}\n", "import { CHANNEL_EVENTS, CHANNEL_STATES } from './lib/constants';\nimport Push from './lib/push';\nimport Timer from './lib/timer';\nimport RealtimePresence from './RealtimePresence';\nimport * as Transformers from './lib/transformers';\nimport { httpEndpointURL } from './lib/transformers';\nexport var REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n  REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nexport var REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n  REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n  REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n  REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n  REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nexport var REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n  REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n  REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n  REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n  REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nexport const REALTIME_CHANNEL_STATES = CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nexport default class RealtimeChannel {\n  constructor(/** Topic name can be any string. */\n  topic, params = {\n    config: {}\n  }, socket) {\n    this.topic = topic;\n    this.params = params;\n    this.socket = socket;\n    this.bindings = {};\n    this.state = CHANNEL_STATES.closed;\n    this.joinedOnce = false;\n    this.pushBuffer = [];\n    this.subTopic = topic.replace(/^realtime:/i, '');\n    this.params.config = Object.assign({\n      broadcast: {\n        ack: false,\n        self: false\n      },\n      presence: {\n        key: ''\n      },\n      private: false\n    }, params.config);\n    this.timeout = this.socket.timeout;\n    this.joinPush = new Push(this, CHANNEL_EVENTS.join, this.params, this.timeout);\n    this.rejoinTimer = new Timer(() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n    this.joinPush.receive('ok', () => {\n      this.state = CHANNEL_STATES.joined;\n      this.rejoinTimer.reset();\n      this.pushBuffer.forEach(pushEvent => pushEvent.send());\n      this.pushBuffer = [];\n    });\n    this._onClose(() => {\n      this.rejoinTimer.reset();\n      this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n      this.state = CHANNEL_STATES.closed;\n      this.socket._remove(this);\n    });\n    this._onError(reason => {\n      if (this._isLeaving() || this._isClosed()) {\n        return;\n      }\n      this.socket.log('channel', `error ${this.topic}`, reason);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this.joinPush.receive('timeout', () => {\n      if (!this._isJoining()) {\n        return;\n      }\n      this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n      this.state = CHANNEL_STATES.errored;\n      this.rejoinTimer.scheduleTimeout();\n    });\n    this._on(CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n      this._trigger(this._replyEventName(ref), payload);\n    });\n    this.presence = new RealtimePresence(this);\n    this.broadcastEndpointURL = httpEndpointURL(this.socket.endPoint) + '/api/broadcast';\n    this.private = this.params.config.private || false;\n  }\n  /** Subscribe registers your client with the server */\n  subscribe(callback, timeout = this.timeout) {\n    var _a, _b;\n    if (!this.socket.isConnected()) {\n      this.socket.connect();\n    }\n    if (this.joinedOnce) {\n      throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n    } else {\n      const {\n        config: {\n          broadcast,\n          presence,\n          private: isPrivate\n        }\n      } = this.params;\n      this._onError(e => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n      this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n      const accessTokenPayload = {};\n      const config = {\n        broadcast,\n        presence,\n        postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map(r => r.filter)) !== null && _b !== void 0 ? _b : [],\n        private: isPrivate\n      };\n      if (this.socket.accessTokenValue) {\n        accessTokenPayload.access_token = this.socket.accessTokenValue;\n      }\n      this.updateJoinPayload(Object.assign({\n        config\n      }, accessTokenPayload));\n      this.joinedOnce = true;\n      this._rejoin(timeout);\n      this.joinPush.receive('ok', async ({\n        postgres_changes\n      }) => {\n        var _a;\n        this.socket.setAuth();\n        if (postgres_changes === undefined) {\n          callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n          return;\n        } else {\n          const clientPostgresBindings = this.bindings.postgres_changes;\n          const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n          const newPostgresBindings = [];\n          for (let i = 0; i < bindingsLen; i++) {\n            const clientPostgresBinding = clientPostgresBindings[i];\n            const {\n              filter: {\n                event,\n                schema,\n                table,\n                filter\n              }\n            } = clientPostgresBinding;\n            const serverPostgresFilter = postgres_changes && postgres_changes[i];\n            if (serverPostgresFilter && serverPostgresFilter.event === event && serverPostgresFilter.schema === schema && serverPostgresFilter.table === table && serverPostgresFilter.filter === filter) {\n              newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), {\n                id: serverPostgresFilter.id\n              }));\n            } else {\n              this.unsubscribe();\n              callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n              return;\n            }\n          }\n          this.bindings.postgres_changes = newPostgresBindings;\n          callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n          return;\n        }\n      }).receive('error', error => {\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n        return;\n      }).receive('timeout', () => {\n        callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n        return;\n      });\n    }\n    return this;\n  }\n  presenceState() {\n    return this.presence.state;\n  }\n  async track(payload, opts = {}) {\n    return await this.send({\n      type: 'presence',\n      event: 'track',\n      payload\n    }, opts.timeout || this.timeout);\n  }\n  async untrack(opts = {}) {\n    return await this.send({\n      type: 'presence',\n      event: 'untrack'\n    }, opts);\n  }\n  on(type, filter, callback) {\n    return this._on(type, filter, callback);\n  }\n  /**\n   * Sends a message into the channel.\n   *\n   * @param args Arguments to send to channel\n   * @param args.type The type of event to send\n   * @param args.event The name of the event being sent\n   * @param args.payload Payload to be sent\n   * @param opts Options to be used during the send process\n   */\n  async send(args, opts = {}) {\n    var _a, _b;\n    if (!this._canPush() && args.type === 'broadcast') {\n      const {\n        event,\n        payload: endpoint_payload\n      } = args;\n      const authorization = this.socket.accessTokenValue ? `Bearer ${this.socket.accessTokenValue}` : '';\n      const options = {\n        method: 'POST',\n        headers: {\n          Authorization: authorization,\n          apikey: this.socket.apiKey ? this.socket.apiKey : '',\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages: [{\n            topic: this.subTopic,\n            event,\n            payload: endpoint_payload,\n            private: this.private\n          }]\n        })\n      };\n      try {\n        const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n        await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n        return response.ok ? 'ok' : 'error';\n      } catch (error) {\n        if (error.name === 'AbortError') {\n          return 'timed out';\n        } else {\n          return 'error';\n        }\n      }\n    } else {\n      return new Promise(resolve => {\n        var _a, _b, _c;\n        const push = this._push(args.type, args, opts.timeout || this.timeout);\n        if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n          resolve('ok');\n        }\n        push.receive('ok', () => resolve('ok'));\n        push.receive('error', () => resolve('error'));\n        push.receive('timeout', () => resolve('timed out'));\n      });\n    }\n  }\n  updateJoinPayload(payload) {\n    this.joinPush.updatePayload(payload);\n  }\n  /**\n   * Leaves the channel.\n   *\n   * Unsubscribes from server events, and instructs channel to terminate on server.\n   * Triggers onClose() hooks.\n   *\n   * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n   * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n   */\n  unsubscribe(timeout = this.timeout) {\n    this.state = CHANNEL_STATES.leaving;\n    const onClose = () => {\n      this.socket.log('channel', `leave ${this.topic}`);\n      this._trigger(CHANNEL_EVENTS.close, 'leave', this._joinRef());\n    };\n    this.rejoinTimer.reset();\n    // Destroy joinPush to avoid connection timeouts during unscription phase\n    this.joinPush.destroy();\n    return new Promise(resolve => {\n      const leavePush = new Push(this, CHANNEL_EVENTS.leave, {}, timeout);\n      leavePush.receive('ok', () => {\n        onClose();\n        resolve('ok');\n      }).receive('timeout', () => {\n        onClose();\n        resolve('timed out');\n      }).receive('error', () => {\n        resolve('error');\n      });\n      leavePush.send();\n      if (!this._canPush()) {\n        leavePush.trigger('ok', {});\n      }\n    });\n  }\n  /** @internal */\n  async _fetchWithTimeout(url, options, timeout) {\n    const controller = new AbortController();\n    const id = setTimeout(() => controller.abort(), timeout);\n    const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), {\n      signal: controller.signal\n    }));\n    clearTimeout(id);\n    return response;\n  }\n  /** @internal */\n  _push(event, payload, timeout = this.timeout) {\n    if (!this.joinedOnce) {\n      throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n    }\n    let pushEvent = new Push(this, event, payload, timeout);\n    if (this._canPush()) {\n      pushEvent.send();\n    } else {\n      pushEvent.startTimeout();\n      this.pushBuffer.push(pushEvent);\n    }\n    return pushEvent;\n  }\n  /**\n   * Overridable message hook\n   *\n   * Receives all events for specialized message handling before dispatching to the channel callbacks.\n   * Must return the payload, modified or unmodified.\n   *\n   * @internal\n   */\n  _onMessage(_event, payload, _ref) {\n    return payload;\n  }\n  /** @internal */\n  _isMember(topic) {\n    return this.topic === topic;\n  }\n  /** @internal */\n  _joinRef() {\n    return this.joinPush.ref;\n  }\n  /** @internal */\n  _trigger(type, payload, ref) {\n    var _a, _b;\n    const typeLower = type.toLocaleLowerCase();\n    const {\n      close,\n      error,\n      leave,\n      join\n    } = CHANNEL_EVENTS;\n    const events = [close, error, leave, join];\n    if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n      return;\n    }\n    let handledPayload = this._onMessage(typeLower, payload, ref);\n    if (payload && !handledPayload) {\n      throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n    }\n    if (['insert', 'update', 'delete'].includes(typeLower)) {\n      (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter(bind => {\n        var _a, _b, _c;\n        return ((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' || ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower;\n      }).map(bind => bind.callback(handledPayload, ref));\n    } else {\n      (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter(bind => {\n        var _a, _b, _c, _d, _e, _f;\n        if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n          if ('id' in bind) {\n            const bindId = bind.id;\n            const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n            return bindId && ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) && (bindEvent === '*' || (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) === ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase()));\n          } else {\n            const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n            return bindEvent === '*' || bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase());\n          }\n        } else {\n          return bind.type.toLocaleLowerCase() === typeLower;\n        }\n      }).map(bind => {\n        if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n          const postgresChanges = handledPayload.data;\n          const {\n            schema,\n            table,\n            commit_timestamp,\n            type,\n            errors\n          } = postgresChanges;\n          const enrichedPayload = {\n            schema: schema,\n            table: table,\n            commit_timestamp: commit_timestamp,\n            eventType: type,\n            new: {},\n            old: {},\n            errors: errors\n          };\n          handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n        }\n        bind.callback(handledPayload, ref);\n      });\n    }\n  }\n  /** @internal */\n  _isClosed() {\n    return this.state === CHANNEL_STATES.closed;\n  }\n  /** @internal */\n  _isJoined() {\n    return this.state === CHANNEL_STATES.joined;\n  }\n  /** @internal */\n  _isJoining() {\n    return this.state === CHANNEL_STATES.joining;\n  }\n  /** @internal */\n  _isLeaving() {\n    return this.state === CHANNEL_STATES.leaving;\n  }\n  /** @internal */\n  _replyEventName(ref) {\n    return `chan_reply_${ref}`;\n  }\n  /** @internal */\n  _on(type, filter, callback) {\n    const typeLower = type.toLocaleLowerCase();\n    const binding = {\n      type: typeLower,\n      filter: filter,\n      callback: callback\n    };\n    if (this.bindings[typeLower]) {\n      this.bindings[typeLower].push(binding);\n    } else {\n      this.bindings[typeLower] = [binding];\n    }\n    return this;\n  }\n  /** @internal */\n  _off(type, filter) {\n    const typeLower = type.toLocaleLowerCase();\n    this.bindings[typeLower] = this.bindings[typeLower].filter(bind => {\n      var _a;\n      return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower && RealtimeChannel.isEqual(bind.filter, filter));\n    });\n    return this;\n  }\n  /** @internal */\n  static isEqual(obj1, obj2) {\n    if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n      return false;\n    }\n    for (const k in obj1) {\n      if (obj1[k] !== obj2[k]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  /** @internal */\n  _rejoinUntilConnected() {\n    this.rejoinTimer.scheduleTimeout();\n    if (this.socket.isConnected()) {\n      this._rejoin();\n    }\n  }\n  /**\n   * Registers a callback that will be executed when the channel closes.\n   *\n   * @internal\n   */\n  _onClose(callback) {\n    this._on(CHANNEL_EVENTS.close, {}, callback);\n  }\n  /**\n   * Registers a callback that will be executed when the channel encounteres an error.\n   *\n   * @internal\n   */\n  _onError(callback) {\n    this._on(CHANNEL_EVENTS.error, {}, reason => callback(reason));\n  }\n  /**\n   * Returns `true` if the socket is connected and the channel has been joined.\n   *\n   * @internal\n   */\n  _canPush() {\n    return this.socket.isConnected() && this._isJoined();\n  }\n  /** @internal */\n  _rejoin(timeout = this.timeout) {\n    if (this._isLeaving()) {\n      return;\n    }\n    this.socket._leaveOpenTopic(this.topic);\n    this.state = CHANNEL_STATES.joining;\n    this.joinPush.resend(timeout);\n  }\n  /** @internal */\n  _getPayloadRecords(payload) {\n    const records = {\n      new: {},\n      old: {}\n    };\n    if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n      records.new = Transformers.convertChangeData(payload.columns, payload.record);\n    }\n    if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n      records.old = Transformers.convertChangeData(payload.columns, payload.old_record);\n    }\n    return records;\n  }\n}\n", "import { CHANNEL_EVENTS, CONNECTION_STATE, DEFAULT_HEADERS, DEFAULT_TIMEOUT, SOCKET_STATES, TRANSPORTS, VSN, WS_CLOSE_NORMAL } from './lib/constants';\nimport Serializer from './lib/serializer';\nimport Timer from './lib/timer';\nimport { httpEndpointURL } from './lib/transformers';\nimport RealtimeChannel from './RealtimeChannel';\nconst noop = () => {};\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== 'undefined';\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nexport default class RealtimeClient {\n  /**\n   * Initializes the Socket.\n   *\n   * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n   * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n   * @param options.transport The Websocket Transport, for example WebSocket.\n   * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n   * @param options.params The optional params to pass when connecting.\n   * @param options.headers The optional headers to pass when connecting.\n   * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n   * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n   * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n   * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n   * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n   * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n   * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n   */\n  constructor(endPoint, options) {\n    var _a;\n    this.accessTokenValue = null;\n    this.apiKey = null;\n    this.channels = [];\n    this.endPoint = '';\n    this.httpEndpoint = '';\n    this.headers = DEFAULT_HEADERS;\n    this.params = {};\n    this.timeout = DEFAULT_TIMEOUT;\n    this.heartbeatIntervalMs = 30000;\n    this.heartbeatTimer = undefined;\n    this.pendingHeartbeatRef = null;\n    this.ref = 0;\n    this.logger = noop;\n    this.conn = null;\n    this.sendBuffer = [];\n    this.serializer = new Serializer();\n    this.stateChangeCallbacks = {\n      open: [],\n      close: [],\n      error: [],\n      message: []\n    };\n    this.accessToken = null;\n    /**\n     * Use either custom fetch, if provided, or default fetch to make HTTP requests\n     *\n     * @internal\n     */\n    this._resolveFetch = customFetch => {\n      let _fetch;\n      if (customFetch) {\n        _fetch = customFetch;\n      } else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({\n          default: fetch\n        }) => fetch(...args));\n      } else {\n        _fetch = fetch;\n      }\n      return (...args) => _fetch(...args);\n    };\n    this.endPoint = `${endPoint}/${TRANSPORTS.websocket}`;\n    this.httpEndpoint = httpEndpointURL(endPoint);\n    if (options === null || options === void 0 ? void 0 : options.transport) {\n      this.transport = options.transport;\n    } else {\n      this.transport = null;\n    }\n    if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n    if (options === null || options === void 0 ? void 0 : options.headers) this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n    if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n    if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n    if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n    const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n    if (accessTokenValue) {\n      this.accessTokenValue = accessTokenValue;\n      this.apiKey = accessTokenValue;\n    }\n    this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : tries => {\n      return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n    };\n    this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback) => {\n      return callback(JSON.stringify(payload));\n    };\n    this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n    this.reconnectTimer = new Timer(async () => {\n      this.disconnect();\n      this.connect();\n    }, this.reconnectAfterMs);\n    this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n    if (options === null || options === void 0 ? void 0 : options.worker) {\n      if (typeof window !== 'undefined' && !window.Worker) {\n        throw new Error('Web Worker is not supported');\n      }\n      this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n      this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n    }\n    this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n  }\n  /**\n   * Connects the socket, unless already connected.\n   */\n  connect() {\n    if (this.conn) {\n      return;\n    }\n    if (this.transport) {\n      this.conn = new this.transport(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      return;\n    }\n    if (NATIVE_WEBSOCKET_AVAILABLE) {\n      this.conn = new WebSocket(this.endpointURL());\n      this.setupConnection();\n      return;\n    }\n    this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n      close: () => {\n        this.conn = null;\n      }\n    });\n    import('ws').then(({\n      default: WS\n    }) => {\n      this.conn = new WS(this.endpointURL(), undefined, {\n        headers: this.headers\n      });\n      this.setupConnection();\n    });\n  }\n  /**\n   * Returns the URL of the websocket.\n   * @returns string The URL of the websocket.\n   */\n  endpointURL() {\n    return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n      vsn: VSN\n    }));\n  }\n  /**\n   * Disconnects the socket.\n   *\n   * @param code A numeric status code to send on disconnect.\n   * @param reason A custom reason for the disconnect.\n   */\n  disconnect(code, reason) {\n    if (this.conn) {\n      this.conn.onclose = function () {}; // noop\n      if (code) {\n        this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n      } else {\n        this.conn.close();\n      }\n      this.conn = null;\n      // remove open handles\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.reconnectTimer.reset();\n    }\n  }\n  /**\n   * Returns all created channels\n   */\n  getChannels() {\n    return this.channels;\n  }\n  /**\n   * Unsubscribes and removes a single channel\n   * @param channel A RealtimeChannel instance\n   */\n  async removeChannel(channel) {\n    const status = await channel.unsubscribe();\n    if (this.channels.length === 0) {\n      this.disconnect();\n    }\n    return status;\n  }\n  /**\n   * Unsubscribes and removes all channels\n   */\n  async removeAllChannels() {\n    const values_1 = await Promise.all(this.channels.map(channel => channel.unsubscribe()));\n    this.disconnect();\n    return values_1;\n  }\n  /**\n   * Logs the message.\n   *\n   * For customized logging, `this.logger` can be overridden.\n   */\n  log(kind, msg, data) {\n    this.logger(kind, msg, data);\n  }\n  /**\n   * Returns the current state of the socket.\n   */\n  connectionState() {\n    switch (this.conn && this.conn.readyState) {\n      case SOCKET_STATES.connecting:\n        return CONNECTION_STATE.Connecting;\n      case SOCKET_STATES.open:\n        return CONNECTION_STATE.Open;\n      case SOCKET_STATES.closing:\n        return CONNECTION_STATE.Closing;\n      default:\n        return CONNECTION_STATE.Closed;\n    }\n  }\n  /**\n   * Returns `true` is the connection is open.\n   */\n  isConnected() {\n    return this.connectionState() === CONNECTION_STATE.Open;\n  }\n  channel(topic, params = {\n    config: {}\n  }) {\n    const chan = new RealtimeChannel(`realtime:${topic}`, params, this);\n    this.channels.push(chan);\n    return chan;\n  }\n  /**\n   * Push out a message if the socket is connected.\n   *\n   * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n   */\n  push(data) {\n    const {\n      topic,\n      event,\n      payload,\n      ref\n    } = data;\n    const callback = () => {\n      this.encode(data, result => {\n        var _a;\n        (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n      });\n    };\n    this.log('push', `${topic} ${event} (${ref})`, payload);\n    if (this.isConnected()) {\n      callback();\n    } else {\n      this.sendBuffer.push(callback);\n    }\n  }\n  /**\n   * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n   *\n   * If param is null it will use the `accessToken` callback function or the token set on the client.\n   *\n   * On callback used, it will set the value of the token internal to the client.\n   *\n   * @param token A JWT string to override the token set on the client.\n   */\n  async setAuth(token = null) {\n    let tokenToSend = token || this.accessToken && (await this.accessToken()) || this.accessTokenValue;\n    if (tokenToSend) {\n      let parsed = null;\n      try {\n        parsed = JSON.parse(atob(tokenToSend.split('.')[1]));\n      } catch (_error) {}\n      if (parsed && parsed.exp) {\n        let now = Math.floor(Date.now() / 1000);\n        let valid = now - parsed.exp < 0;\n        if (!valid) {\n          this.log('auth', `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n          return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n        }\n      }\n      this.accessTokenValue = tokenToSend;\n      this.channels.forEach(channel => {\n        tokenToSend && channel.updateJoinPayload({\n          access_token: tokenToSend\n        });\n        if (channel.joinedOnce && channel._isJoined()) {\n          channel._push(CHANNEL_EVENTS.access_token, {\n            access_token: tokenToSend\n          });\n        }\n      });\n    }\n  }\n  /**\n   * Sends a heartbeat message if the socket is connected.\n   */\n  async sendHeartbeat() {\n    var _a;\n    if (!this.isConnected()) {\n      return;\n    }\n    if (this.pendingHeartbeatRef) {\n      this.pendingHeartbeatRef = null;\n      this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n      (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(WS_CLOSE_NORMAL, 'hearbeat timeout');\n      return;\n    }\n    this.pendingHeartbeatRef = this._makeRef();\n    this.push({\n      topic: 'phoenix',\n      event: 'heartbeat',\n      payload: {},\n      ref: this.pendingHeartbeatRef\n    });\n    this.setAuth();\n  }\n  /**\n   * Flushes send buffer\n   */\n  flushSendBuffer() {\n    if (this.isConnected() && this.sendBuffer.length > 0) {\n      this.sendBuffer.forEach(callback => callback());\n      this.sendBuffer = [];\n    }\n  }\n  /**\n   * Return the next message ref, accounting for overflows\n   *\n   * @internal\n   */\n  _makeRef() {\n    let newRef = this.ref + 1;\n    if (newRef === this.ref) {\n      this.ref = 0;\n    } else {\n      this.ref = newRef;\n    }\n    return this.ref.toString();\n  }\n  /**\n   * Unsubscribe from channels with the specified topic.\n   *\n   * @internal\n   */\n  _leaveOpenTopic(topic) {\n    let dupChannel = this.channels.find(c => c.topic === topic && (c._isJoined() || c._isJoining()));\n    if (dupChannel) {\n      this.log('transport', `leaving duplicate topic \"${topic}\"`);\n      dupChannel.unsubscribe();\n    }\n  }\n  /**\n   * Removes a subscription from the socket.\n   *\n   * @param channel An open subscription.\n   *\n   * @internal\n   */\n  _remove(channel) {\n    this.channels = this.channels.filter(c => c._joinRef() !== channel._joinRef());\n  }\n  /**\n   * Sets up connection handlers.\n   *\n   * @internal\n   */\n  setupConnection() {\n    if (this.conn) {\n      this.conn.binaryType = 'arraybuffer';\n      this.conn.onopen = () => this._onConnOpen();\n      this.conn.onerror = error => this._onConnError(error);\n      this.conn.onmessage = event => this._onConnMessage(event);\n      this.conn.onclose = event => this._onConnClose(event);\n    }\n  }\n  /** @internal */\n  _onConnMessage(rawMessage) {\n    this.decode(rawMessage.data, msg => {\n      let {\n        topic,\n        event,\n        payload,\n        ref\n      } = msg;\n      if (ref && ref === this.pendingHeartbeatRef) {\n        this.pendingHeartbeatRef = null;\n      }\n      this.log('receive', `${payload.status || ''} ${topic} ${event} ${ref && '(' + ref + ')' || ''}`, payload);\n      this.channels.filter(channel => channel._isMember(topic)).forEach(channel => channel._trigger(event, payload, ref));\n      this.stateChangeCallbacks.message.forEach(callback => callback(msg));\n    });\n  }\n  /** @internal */\n  async _onConnOpen() {\n    this.log('transport', `connected to ${this.endpointURL()}`);\n    this.flushSendBuffer();\n    this.reconnectTimer.reset();\n    if (!this.worker) {\n      this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    } else {\n      if (this.workerUrl) {\n        this.log('worker', `starting worker for from ${this.workerUrl}`);\n      } else {\n        this.log('worker', `starting default worker`);\n      }\n      const objectUrl = this._workerObjectUrl(this.workerUrl);\n      this.workerRef = new Worker(objectUrl);\n      this.workerRef.onerror = error => {\n        this.log('worker', 'worker error', error.message);\n        this.workerRef.terminate();\n      };\n      this.workerRef.onmessage = event => {\n        if (event.data.event === 'keepAlive') {\n          this.sendHeartbeat();\n        }\n      };\n      this.workerRef.postMessage({\n        event: 'start',\n        interval: this.heartbeatIntervalMs\n      });\n    }\n    this.stateChangeCallbacks.open.forEach(callback => callback());\n  }\n  /** @internal */\n  _onConnClose(event) {\n    this.log('transport', 'close', event);\n    this._triggerChanError();\n    this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n    this.reconnectTimer.scheduleTimeout();\n    this.stateChangeCallbacks.close.forEach(callback => callback(event));\n  }\n  /** @internal */\n  _onConnError(error) {\n    this.log('transport', error.message);\n    this._triggerChanError();\n    this.stateChangeCallbacks.error.forEach(callback => callback(error));\n  }\n  /** @internal */\n  _triggerChanError() {\n    this.channels.forEach(channel => channel._trigger(CHANNEL_EVENTS.error));\n  }\n  /** @internal */\n  _appendParams(url, params) {\n    if (Object.keys(params).length === 0) {\n      return url;\n    }\n    const prefix = url.match(/\\?/) ? '&' : '?';\n    const query = new URLSearchParams(params);\n    return `${url}${prefix}${query}`;\n  }\n  _workerObjectUrl(url) {\n    let result_url;\n    if (url) {\n      result_url = url;\n    } else {\n      const blob = new Blob([WORKER_SCRIPT], {\n        type: 'application/javascript'\n      });\n      result_url = URL.createObjectURL(blob);\n    }\n    return result_url;\n  }\n}\nclass WSWebSocketDummy {\n  constructor(address, _protocols, options) {\n    this.binaryType = 'arraybuffer';\n    this.onclose = () => {};\n    this.onerror = () => {};\n    this.onmessage = () => {};\n    this.onopen = () => {};\n    this.readyState = SOCKET_STATES.connecting;\n    this.send = () => {};\n    this.url = null;\n    this.url = address;\n    this.close = options.close;\n  }\n}\n", "export class StorageError extends Error {\n  constructor(message) {\n    super(message);\n    this.__isStorageError = true;\n    this.name = 'StorageError';\n  }\n}\nexport function isStorageError(error) {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n  constructor(message, status) {\n    super(message);\n    this.name = 'StorageApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class StorageUnknownError extends StorageError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'StorageUnknownError';\n    this.originalError = originalError;\n  }\n}\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveResponse = () => __awaiter(void 0, void 0, void 0, function* () {\n  if (typeof Response === 'undefined') {\n    // @ts-ignore\n    return (yield import('@supabase/node-fetch')).Response;\n  }\n  return Response;\n});\nexport const recursiveToCamel = item => {\n  if (Array.isArray(item)) {\n    return item.map(el => recursiveToCamel(el));\n  } else if (typeof item === 'function' || item !== Object(item)) {\n    return item;\n  }\n  const result = {};\n  Object.entries(item).forEach(([key, value]) => {\n    const newKey = key.replace(/([-_][a-z])/gi, c => c.toUpperCase().replace(/[-_]/g, ''));\n    result[newKey] = recursiveToCamel(value);\n  });\n  return result;\n};\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { StorageApiError, StorageUnknownError } from './errors';\nimport { resolveResponse } from './helpers';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n  const Res = yield resolveResponse();\n  if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n    error.json().then(err => {\n      reject(new StorageApiError(_getErrorMessage(err), error.status || 500));\n    }).catch(err => {\n      reject(new StorageUnknownError(_getErrorMessage(err), err));\n    });\n  } else {\n    reject(new StorageUnknownError(_getErrorMessage(error), error));\n  }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  if (body) {\n    params.body = JSON.stringify(body);\n  }\n  return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n      fetcher(url, _getRequestParams(method, options, parameters, body)).then(result => {\n        if (!result.ok) throw result;\n        if (options === null || options === void 0 ? void 0 : options.noResolveJson) return result;\n        return result.json();\n      }).then(data => resolve(data)).catch(error => handleError(error, reject, options));\n    });\n  });\n}\nexport function get(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'GET', url, options, parameters);\n  });\n}\nexport function post(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n  });\n}\nexport function put(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n  });\n}\nexport function head(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), {\n      noResolveJson: true\n    }), parameters);\n  });\n}\nexport function remove(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n  });\n}\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { isStorageError, StorageError, StorageUnknownError } from '../lib/errors';\nimport { get, head, post, remove } from '../lib/fetch';\nimport { recursiveToCamel, resolveFetch } from '../lib/helpers';\nconst DEFAULT_SEARCH_OPTIONS = {\n  limit: 100,\n  offset: 0,\n  sortBy: {\n    column: 'name',\n    order: 'asc'\n  }\n};\nconst DEFAULT_FILE_OPTIONS = {\n  cacheControl: '3600',\n  contentType: 'text/plain;charset=UTF-8',\n  upsert: false\n};\nexport default class StorageFileApi {\n  constructor(url, headers = {}, bucketId, fetch) {\n    this.url = url;\n    this.headers = headers;\n    this.bucketId = bucketId;\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n   *\n   * @param method HTTP method.\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadOrUpdate(method, path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let body;\n        const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n        let headers = Object.assign(Object.assign({}, this.headers), method === 'POST' && {\n          'x-upsert': String(options.upsert)\n        });\n        const metadata = options.metadata;\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n          if (metadata) {\n            body.append('metadata', this.encodeMetadata(metadata));\n          }\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n          if (metadata) {\n            headers['x-metadata'] = this.toBase64(this.encodeMetadata(metadata));\n          }\n        }\n        if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n          headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n        }\n        const cleanPath = this._removeEmptyFolders(path);\n        const _path = this._getFinalPath(cleanPath);\n        const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({\n          method,\n          body: body,\n          headers\n        }, (options === null || options === void 0 ? void 0 : options.duplex) ? {\n          duplex: options.duplex\n        } : {}));\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              id: data.Id,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Uploads a file to an existing bucket.\n   *\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  upload(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('POST', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Upload a file with a token generated from `createSignedUploadUrl`.\n   * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n   * @param token The token generated from `createSignedUploadUrl`\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  uploadToSignedUrl(path, token, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const cleanPath = this._removeEmptyFolders(path);\n      const _path = this._getFinalPath(cleanPath);\n      const url = new URL(this.url + `/object/upload/sign/${_path}`);\n      url.searchParams.set('token', token);\n      try {\n        let body;\n        const options = Object.assign({\n          upsert: DEFAULT_FILE_OPTIONS.upsert\n        }, fileOptions);\n        const headers = Object.assign(Object.assign({}, this.headers), {\n          'x-upsert': String(options.upsert)\n        });\n        if (typeof Blob !== 'undefined' && fileBody instanceof Blob) {\n          body = new FormData();\n          body.append('cacheControl', options.cacheControl);\n          body.append('', fileBody);\n        } else if (typeof FormData !== 'undefined' && fileBody instanceof FormData) {\n          body = fileBody;\n          body.append('cacheControl', options.cacheControl);\n        } else {\n          body = fileBody;\n          headers['cache-control'] = `max-age=${options.cacheControl}`;\n          headers['content-type'] = options.contentType;\n        }\n        const res = yield this.fetch(url.toString(), {\n          method: 'PUT',\n          body: body,\n          headers\n        });\n        const data = yield res.json();\n        if (res.ok) {\n          return {\n            data: {\n              path: cleanPath,\n              fullPath: data.Key\n            },\n            error: null\n          };\n        } else {\n          const error = data;\n          return {\n            data: null,\n            error\n          };\n        }\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed upload URL.\n   * Signed upload URLs can be used to upload files to the bucket without further authentication.\n   * They are valid for 2 hours.\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n   */\n  createSignedUploadUrl(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        const headers = Object.assign({}, this.headers);\n        if (options === null || options === void 0 ? void 0 : options.upsert) {\n          headers['x-upsert'] = 'true';\n        }\n        const data = yield post(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, {\n          headers\n        });\n        const url = new URL(this.url + data.url);\n        const token = url.searchParams.get('token');\n        if (!token) {\n          throw new StorageError('No token returned by API');\n        }\n        return {\n          data: {\n            signedUrl: url.toString(),\n            path,\n            token\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Replaces an existing file at the specified path with a new one.\n   *\n   * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n   * @param fileBody The body of the file to be stored in the bucket.\n   */\n  update(path, fileBody, fileOptions) {\n    return __awaiter(this, void 0, void 0, function* () {\n      return this.uploadOrUpdate('PUT', path, fileBody, fileOptions);\n    });\n  }\n  /**\n   * Moves an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n   * @param options The destination options.\n   */\n  move(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/move`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Copies an existing file to a new path in the same bucket.\n   *\n   * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n   * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n   * @param options The destination options.\n   */\n  copy(fromPath, toPath, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/copy`, {\n          bucketId: this.bucketId,\n          sourceKey: fromPath,\n          destinationKey: toPath,\n          destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n        }, {\n          headers: this.headers\n        });\n        return {\n          data: {\n            path: data.Key\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param path The file path, including the current file name. For example `folder/image.png`.\n   * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  createSignedUrl(path, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        let _path = this._getFinalPath(path);\n        let data = yield post(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({\n          expiresIn\n        }, (options === null || options === void 0 ? void 0 : options.transform) ? {\n          transform: options.transform\n        } : {}), {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n        data = {\n          signedUrl\n        };\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n   *\n   * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n   * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n   * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   */\n  createSignedUrls(paths, expiresIn, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/object/sign/${this.bucketId}`, {\n          expiresIn,\n          paths\n        }, {\n          headers: this.headers\n        });\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? '' : options.download}` : '';\n        return {\n          data: data.map(datum => Object.assign(Object.assign({}, datum), {\n            signedUrl: datum.signedURL ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`) : null\n          })),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n   *\n   * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  download(path, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n      const renderPath = wantsTransformation ? 'render/image/authenticated' : 'object';\n      const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n      const queryString = transformationQuery ? `?${transformationQuery}` : '';\n      try {\n        const _path = this._getFinalPath(path);\n        const res = yield get(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n          headers: this.headers,\n          noResolveJson: true\n        });\n        const data = yield res.blob();\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing file.\n   * @param path\n   */\n  info(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        const data = yield get(this.fetch, `${this.url}/object/info/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: recursiveToCamel(data),\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Checks the existence of a file.\n   * @param path\n   */\n  exists(path) {\n    return __awaiter(this, void 0, void 0, function* () {\n      const _path = this._getFinalPath(path);\n      try {\n        yield head(this.fetch, `${this.url}/object/${_path}`, {\n          headers: this.headers\n        });\n        return {\n          data: true,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error) && error instanceof StorageUnknownError) {\n          const originalError = error.originalError;\n          if ([400, 404].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n            return {\n              data: false,\n              error\n            };\n          }\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n   * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n   *\n   * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n   * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n   * @param options.transform Transform the asset before serving it to the client.\n   */\n  getPublicUrl(path, options) {\n    const _path = this._getFinalPath(path);\n    const _queryString = [];\n    const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `download=${options.download === true ? '' : options.download}` : '';\n    if (downloadQueryParam !== '') {\n      _queryString.push(downloadQueryParam);\n    }\n    const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== 'undefined';\n    const renderPath = wantsTransformation ? 'render/image' : 'object';\n    const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n    if (transformationQuery !== '') {\n      _queryString.push(transformationQuery);\n    }\n    let queryString = _queryString.join('&');\n    if (queryString !== '') {\n      queryString = `?${queryString}`;\n    }\n    return {\n      data: {\n        publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`)\n      }\n    };\n  }\n  /**\n   * Deletes files within the same bucket\n   *\n   * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n   */\n  remove(paths) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/object/${this.bucketId}`, {\n          prefixes: paths\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Get file metadata\n   * @param id the file id to retrieve metadata\n   */\n  // async getMetadata(\n  //   id: string\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Update file metadata\n   * @param id the file id to update metadata\n   * @param meta the new file metadata\n   */\n  // async updateMetadata(\n  //   id: string,\n  //   meta: Metadata\n  // ): Promise<\n  //   | {\n  //       data: Metadata\n  //       error: null\n  //     }\n  //   | {\n  //       data: null\n  //       error: StorageError\n  //     }\n  // > {\n  //   try {\n  //     const data = await post(\n  //       this.fetch,\n  //       `${this.url}/metadata/${id}`,\n  //       { ...meta },\n  //       { headers: this.headers }\n  //     )\n  //     return { data, error: null }\n  //   } catch (error) {\n  //     if (isStorageError(error)) {\n  //       return { data: null, error }\n  //     }\n  //     throw error\n  //   }\n  // }\n  /**\n   * Lists all the files within a bucket.\n   * @param path The folder path.\n   */\n  list(path, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), {\n          prefix: path || ''\n        });\n        const data = yield post(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, {\n          headers: this.headers\n        }, parameters);\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  encodeMetadata(metadata) {\n    return JSON.stringify(metadata);\n  }\n  toBase64(data) {\n    if (typeof Buffer !== 'undefined') {\n      return Buffer.from(data).toString('base64');\n    }\n    return btoa(data);\n  }\n  _getFinalPath(path) {\n    return `${this.bucketId}/${path}`;\n  }\n  _removeEmptyFolders(path) {\n    return path.replace(/^\\/|\\/$/g, '').replace(/\\/+/g, '/');\n  }\n  transformOptsToQueryString(transform) {\n    const params = [];\n    if (transform.width) {\n      params.push(`width=${transform.width}`);\n    }\n    if (transform.height) {\n      params.push(`height=${transform.height}`);\n    }\n    if (transform.resize) {\n      params.push(`resize=${transform.resize}`);\n    }\n    if (transform.format) {\n      params.push(`format=${transform.format}`);\n    }\n    if (transform.quality) {\n      params.push(`quality=${transform.quality}`);\n    }\n    return params.join('&');\n  }\n}\n", "// generated by genversion\nexport const version = '2.7.1';\n", "import { version } from './version';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `storage-js/${version}`\n};\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { DEFAULT_HEADERS } from '../lib/constants';\nimport { isStorageError } from '../lib/errors';\nimport { get, post, put, remove } from '../lib/fetch';\nimport { resolveFetch } from '../lib/helpers';\nexport default class StorageBucketApi {\n  constructor(url, headers = {}, fetch) {\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, DEFAULT_HEADERS), headers);\n    this.fetch = resolveFetch(fetch);\n  }\n  /**\n   * Retrieves the details of all Storage buckets within an existing project.\n   */\n  listBuckets() {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Retrieves the details of an existing Storage bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to retrieve.\n   */\n  getBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield get(this.fetch, `${this.url}/bucket/${id}`, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Creates a new Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are creating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   * @returns newly created bucket id\n   */\n  createBucket(id, options = {\n    public: false\n  }) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Updates a Storage bucket\n   *\n   * @param id A unique identifier for the bucket you are updating.\n   * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n   * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n   * The global file size limit takes precedence over this value.\n   * The default value is null, which doesn't set a per bucket file size limit.\n   * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n   * The default value is null, which allows files with all mime types to be uploaded.\n   * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n   */\n  updateBucket(id, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield put(this.fetch, `${this.url}/bucket/${id}`, {\n          id,\n          name: id,\n          public: options.public,\n          file_size_limit: options.fileSizeLimit,\n          allowed_mime_types: options.allowedMimeTypes\n        }, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Removes all objects inside a single bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to empty.\n   */\n  emptyBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield post(this.fetch, `${this.url}/bucket/${id}/empty`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n   * You must first `empty()` the bucket.\n   *\n   * @param id The unique identifier of the bucket you would like to delete.\n   */\n  deleteBucket(id) {\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const data = yield remove(this.fetch, `${this.url}/bucket/${id}`, {}, {\n          headers: this.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isStorageError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n}\n", "import StorageFileApi from './packages/StorageFileApi';\nimport StorageBucketApi from './packages/StorageBucketApi';\nexport class StorageClient extends StorageBucketApi {\n  constructor(url, headers = {}, fetch) {\n    super(url, headers, fetch);\n  }\n  /**\n   * Perform file operation in a bucket.\n   *\n   * @param id The bucket id to operate on.\n   */\n  from(id) {\n    return new StorageFileApi(this.url, this.headers, id, this.fetch);\n  }\n}\n", "export const version = '2.49.4';\n", "import { version } from './version';\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno';\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web';\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native';\n} else {\n  JS_ENV = 'node';\n}\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `supabase-js-${JS_ENV}/${version}`\n};\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS\n};\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public'\n};\nexport const DEFAULT_AUTH_OPTIONS = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit'\n};\nexport const DEFAULT_REALTIME_OPTIONS = {};\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch';\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = nodeFetch;\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveHeadersConstructor = () => {\n  if (typeof Headers === 'undefined') {\n    return NodeFetchHeaders;\n  }\n  return Headers;\n};\nexport const fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n  const fetch = resolveFetch(customFetch);\n  const HeadersConstructor = resolveHeadersConstructor();\n  return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n    let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n    if (!headers.has('apikey')) {\n      headers.set('apikey', supabaseKey);\n    }\n    if (!headers.has('Authorization')) {\n      headers.set('Authorization', `Bearer ${accessToken}`);\n    }\n    return fetch(input, Object.assign(Object.assign({}, init), {\n      headers\n    }));\n  });\n};\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport function stripTrailingSlash(url) {\n  return url.replace(/\\/$/, '');\n}\nexport const isBrowser = () => typeof window !== 'undefined';\nexport function applySettingDefaults(options, defaults) {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions\n  } = options;\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS\n  } = defaults;\n  const result = {\n    db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n    auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n    realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n    global: Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions),\n    accessToken: () => __awaiter(this, void 0, void 0, function* () {\n      return '';\n    })\n  };\n  if (options.accessToken) {\n    result.accessToken = options.accessToken;\n  } else {\n    // hack around Required<>\n    delete result.accessToken;\n  }\n  return result;\n}\n", "export function expiresAt(expiresIn) {\n  const timeNow = Math.round(Date.now() / 1000);\n  return timeNow + expiresIn;\n}\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport const isBrowser = () => typeof document !== 'undefined';\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false;\n  }\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false;\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false;\n  }\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable;\n  }\n  const randomKey = `lswt-${Math.random()}${Math.random()}`;\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey);\n    globalThis.localStorage.removeItem(randomKey);\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = true;\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = false;\n  }\n  return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n  const result = {};\n  const url = new URL(href);\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value;\n      });\n    } catch (e) {\n      // hash is not a query string\n    }\n  }\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value;\n  });\n  return result;\n}\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = maybeResponse => {\n  return typeof maybeResponse === 'object' && maybeResponse !== null && 'status' in maybeResponse && 'ok' in maybeResponse && 'json' in maybeResponse && typeof maybeResponse.json === 'function';\n};\n// Storage helpers\nexport const setItemAsync = async (storage, key, data) => {\n  await storage.setItem(key, JSON.stringify(data));\n};\nexport const getItemAsync = async (storage, key) => {\n  const value = await storage.getItem(key);\n  if (!value) {\n    return null;\n  }\n  try {\n    return JSON.parse(value);\n  } catch (_a) {\n    return value;\n  }\n};\nexport const removeItemAsync = async (storage, key) => {\n  await storage.removeItem(key);\n};\nexport function decodeBase64URL(value) {\n  const key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n  let base64 = '';\n  let chr1, chr2, chr3;\n  let enc1, enc2, enc3, enc4;\n  let i = 0;\n  value = value.replace('-', '+').replace('_', '/');\n  while (i < value.length) {\n    enc1 = key.indexOf(value.charAt(i++));\n    enc2 = key.indexOf(value.charAt(i++));\n    enc3 = key.indexOf(value.charAt(i++));\n    enc4 = key.indexOf(value.charAt(i++));\n    chr1 = enc1 << 2 | enc2 >> 4;\n    chr2 = (enc2 & 15) << 4 | enc3 >> 2;\n    chr3 = (enc3 & 3) << 6 | enc4;\n    base64 = base64 + String.fromCharCode(chr1);\n    if (enc3 != 64 && chr2 != 0) {\n      base64 = base64 + String.fromCharCode(chr2);\n    }\n    if (enc4 != 64 && chr3 != 0) {\n      base64 = base64 + String.fromCharCode(chr3);\n    }\n  }\n  return base64;\n}\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n  constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    this.promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;\n      this.resolve = res;\n      this.reject = rej;\n    });\n  }\n}\nDeferred.promiseConstructor = Promise;\n// Taken from: https://stackoverflow.com/questions/38552003/how-to-decode-jwt-token-in-javascript-without-using-a-library\nexport function decodeJWTPayload(token) {\n  // Regex checks for base64url format\n  const base64UrlRegex = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i;\n  const parts = token.split('.');\n  if (parts.length !== 3) {\n    throw new Error('JWT is not valid: not a JWT structure');\n  }\n  if (!base64UrlRegex.test(parts[1])) {\n    throw new Error('JWT is not valid: payload is not in base64url format');\n  }\n  const base64Url = parts[1];\n  return JSON.parse(decodeBase64URL(base64Url));\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time) {\n  return await new Promise(accept => {\n    setTimeout(() => accept(null), time);\n  });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable(fn, isRetryable) {\n  const promise = new Promise((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    (async () => {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = await fn(attempt);\n          if (!isRetryable(attempt, null, result)) {\n            accept(result);\n            return;\n          }\n        } catch (e) {\n          if (!isRetryable(attempt, e)) {\n            reject(e);\n            return;\n          }\n        }\n      }\n    })();\n  });\n  return promise;\n}\nfunction dec2hex(dec) {\n  return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56;\n  const array = new Uint32Array(verifierLength);\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n    const charSetLen = charSet.length;\n    let verifier = '';\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n    }\n    return verifier;\n  }\n  crypto.getRandomValues(array);\n  return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n  const encoder = new TextEncoder();\n  const encodedData = encoder.encode(randomString);\n  const hash = await crypto.subtle.digest('SHA-256', encodedData);\n  const bytes = new Uint8Array(hash);\n  return Array.from(bytes).map(c => String.fromCharCode(c)).join('');\n}\nfunction base64urlencode(str) {\n  return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nexport async function generatePKCEChallenge(verifier) {\n  const hasCryptoSupport = typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined' && typeof TextEncoder !== 'undefined';\n  if (!hasCryptoSupport) {\n    console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n    return verifier;\n  }\n  const hashed = await sha256(verifier);\n  return base64urlencode(hashed);\n}\n", "export class AuthError extends Error {\n  constructor(message, status) {\n    super(message);\n    this.__isAuthError = true;\n    this.name = 'AuthError';\n    this.status = status;\n  }\n}\nexport function isAuthError(error) {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n  constructor(message, status) {\n    super(message, status);\n    this.name = 'AuthApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport function isAuthApiError(error) {\n  return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'AuthUnknownError';\n    this.originalError = originalError;\n  }\n}\nexport class CustomAuthError extends AuthError {\n  constructor(message, name, status) {\n    super(message);\n    this.name = name;\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400);\n  }\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500);\n  }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidCredentialsError', 400);\n  }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message, status) {\n    super(message, 'AuthRetryableFetchError', status);\n  }\n}\nexport function isAuthRetryableFetchError(error) {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  constructor(message, status, reasons) {\n    super(message, 'AuthWeakPasswordError', status);\n    this.reasons = reasons;\n  }\n}\nexport function isAuthWeakPasswordError(error) {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\n", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { expiresAt, looksLikeFetchResponse } from './helpers';\nimport { AuthApiError, AuthRetryableFetchError, AuthWeakPasswordError, AuthUnknownError } from './errors';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n  if (!looksLikeFetchResponse(error)) {\n    throw new AuthRetryableFetchError(_getErrorMessage(error), 0);\n  }\n  if (NETWORK_ERROR_CODES.includes(error.status)) {\n    // status in 500...599 range - server had an error, request might be retryed.\n    throw new AuthRetryableFetchError(_getErrorMessage(error), error.status);\n  }\n  let data;\n  try {\n    data = await error.json();\n  } catch (e) {\n    throw new AuthUnknownError(_getErrorMessage(e), e);\n  }\n  if (typeof data === 'object' && data && typeof data.weak_password === 'object' && data.weak_password && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n    throw new AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n  }\n  throw new AuthApiError(_getErrorMessage(data), error.status || 500);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json;charset=UTF-8'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  params.body = JSON.stringify(body);\n  return Object.assign(Object.assign({}, params), parameters);\n};\nexport async function _request(fetcher, method, url, options) {\n  var _a;\n  const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n  if (options === null || options === void 0 ? void 0 : options.jwt) {\n    headers['Authorization'] = `Bearer ${options.jwt}`;\n  }\n  const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n  if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n    qs['redirect_to'] = options.redirectTo;\n  }\n  const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n  const data = await _handleRequest(fetcher, method, url + queryString, {\n    headers,\n    noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson\n  }, {}, options === null || options === void 0 ? void 0 : options.body);\n  return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : {\n    data: Object.assign({}, data),\n    error: null\n  };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n  const requestParams = _getRequestParams(method, options, parameters, body);\n  let result;\n  try {\n    result = await fetcher(url, requestParams);\n  } catch (e) {\n    console.error(e);\n    // fetch failed, likely due to a network or CORS error\n    throw new AuthRetryableFetchError(_getErrorMessage(e), 0);\n  }\n  if (!result.ok) {\n    await handleError(result);\n  }\n  if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n    return result;\n  }\n  try {\n    return await result.json();\n  } catch (e) {\n    await handleError(e);\n  }\n}\nexport function _sessionResponse(data) {\n  var _a;\n  let session = null;\n  if (hasSession(data)) {\n    session = Object.assign({}, data);\n    if (!data.expires_at) {\n      session.expires_at = expiresAt(data.expires_in);\n    }\n  }\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      session,\n      user\n    },\n    error: null\n  };\n}\nexport function _sessionResponsePassword(data) {\n  const response = _sessionResponse(data);\n  if (!response.error && data.weak_password && typeof data.weak_password === 'object' && Array.isArray(data.weak_password.reasons) && data.weak_password.reasons.length && data.weak_password.message && typeof data.weak_password.message === 'string' && data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n    response.data.weak_password = data.weak_password;\n  }\n  return response;\n}\nexport function _userResponse(data) {\n  var _a;\n  const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n  return {\n    data: {\n      user\n    },\n    error: null\n  };\n}\nexport function _ssoResponse(data) {\n  return {\n    data,\n    error: null\n  };\n}\nexport function _generateLinkResponse(data) {\n  const {\n      action_link,\n      email_otp,\n      hashed_token,\n      redirect_to,\n      verification_type\n    } = data,\n    rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n  const properties = {\n    action_link,\n    email_otp,\n    hashed_token,\n    redirect_to,\n    verification_type\n  };\n  const user = Object.assign({}, rest);\n  return {\n    data: {\n      properties,\n      user\n    },\n    error: null\n  };\n}\nexport function _noResolveJsonResponse(data) {\n  return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n  return data.access_token && data.refresh_token && data.expires_in;\n}\n", "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse } from './lib/fetch';\nimport { resolveFetch } from './lib/helpers';\nimport { isAuthError } from './lib/errors';\nexport default class GoTrueAdminApi {\n  constructor({\n    url = '',\n    headers = {},\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.fetch = resolveFetch(fetch);\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this)\n    };\n  }\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  async signOut(jwt, scope = 'global') {\n    try {\n      await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n        headers: this.headers,\n        jwt,\n        noResolveJson: true\n      });\n      return {\n        data: null,\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  async inviteUserByEmail(email, options = {}) {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n        body: {\n          email,\n          data: options.data\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  async generateLink(params) {\n    try {\n      const {\n          options\n        } = params,\n        rest = __rest(params, [\"options\"]);\n      const body = Object.assign(Object.assign({}, rest), options);\n      if ('newEmail' in rest) {\n        // replace newEmail with new_email in request body\n        body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n        delete body['newEmail'];\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n        body: body,\n        headers: this.headers,\n        xform: _generateLinkResponse,\n        redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            properties: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async createUser(attributes) {\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  async listUsers(params) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    try {\n      const pagination = {\n        nextPage: null,\n        lastPage: 0,\n        total: 0\n      };\n      const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n        headers: this.headers,\n        noResolveJson: true,\n        query: {\n          page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n          per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : ''\n        },\n        xform: _noResolveJsonResponse\n      });\n      if (response.error) throw response.error;\n      const users = await response.json();\n      const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n      const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n      if (links.length > 0) {\n        links.forEach(link => {\n          const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n          const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n          pagination[`${rel}Page`] = page;\n        });\n        pagination.total = parseInt(total);\n      }\n      return {\n        data: Object.assign(Object.assign({}, users), pagination),\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            users: []\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async getUserById(uid) {\n    try {\n      return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async updateUserById(uid, attributes) {\n    try {\n      return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n        body: attributes,\n        headers: this.headers,\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  async deleteUser(id, shouldSoftDelete = false) {\n    try {\n      return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n        headers: this.headers,\n        body: {\n          should_soft_delete: shouldSoftDelete\n        },\n        xform: _userResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _listFactors(params) {\n    try {\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n        headers: this.headers,\n        xform: factors => {\n          return {\n            data: {\n              factors\n            },\n            error: null\n          };\n        }\n      });\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  async _deleteFactor(params) {\n    try {\n      const data = await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n        headers: this.headers\n      });\n      return {\n        data,\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n}\n", "// Generated by genversion.\nexport const version = '2.61.0';\n", "import { version } from './version';\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `gotrue-js/${version}`\n};\nexport const EXPIRY_MARGIN = 10; // in seconds\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2 // in deciseconds\n};\n", "import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n  getItem: key => {\n    if (!supportsLocalStorage()) {\n      return null;\n    }\n    return globalThis.localStorage.getItem(key);\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.setItem(key, value);\n  },\n  removeItem: key => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.removeItem(key);\n  }\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}\n", "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return;\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this;\n      },\n      configurable: true\n    });\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__;\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__;\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self;\n    }\n  }\n}\n", "import { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(globalThis && supportsLocalStorage() && globalThis.localStorage && globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true')\n};\nexport class LockAcquireTimeoutError extends Error {\n  constructor(message) {\n    super(message);\n    this.isAcquireTimeout = true;\n  }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @experimental\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock(name, acquireTimeout, fn) {\n  if (internals.debug) {\n    console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n  }\n  const abortController = new globalThis.AbortController();\n  if (acquireTimeout > 0) {\n    setTimeout(() => {\n      abortController.abort();\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n      }\n    }, acquireTimeout);\n  }\n  // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n  return await globalThis.navigator.locks.request(name, acquireTimeout === 0 ? {\n    mode: 'exclusive',\n    ifAvailable: true\n  } : {\n    mode: 'exclusive',\n    signal: abortController.signal\n  }, async lock => {\n    if (lock) {\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n      }\n      try {\n        return await fn();\n      } finally {\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n        }\n      }\n    } else {\n      if (acquireTimeout === 0) {\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n        }\n        throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n      } else {\n        if (internals.debug) {\n          try {\n            const result = await globalThis.navigator.locks.query();\n            console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n          } catch (e) {\n            console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n          }\n        }\n        // Browser is not following the Navigator LockManager spec, it\n        // returned a null lock when we didn't use ifAvailable. So we can\n        // pretend the lock is acquired in the name of backward compatibility\n        // and user experience and just run the function.\n        console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n        return await fn();\n      }\n    }\n  });\n}\n", "import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN, GOTRUE_URL, STORAGE_KEY } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse } from './lib/fetch';\nimport { decodeJWTPayload, Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, generatePKCEVerifier, generatePKCEChallenge, supportsLocalStorage, parseParametersFromURL } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError } from './lib/locks';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false\n};\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\nasync function lockNoOp(name, acquireTimeout, fn) {\n  return await fn();\n}\nexport default class GoTrueClient {\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options) {\n    var _a;\n    this.memoryStorage = null;\n    this.stateChangeEmitters = new Map();\n    this.autoRefreshTicker = null;\n    this.visibilityChangedCallback = null;\n    this.refreshingDeferred = null;\n    /**\n     * Keeps track of the async client initialization.\n     * When null or not yet resolved the auth state is `unknown`\n     * Once resolved the the auth state is known and it's save to call any further client methods.\n     * Keep extra care to never reject or throw uncaught errors\n     */\n    this.initializePromise = null;\n    this.detectSessionInUrl = true;\n    this.lockAcquired = false;\n    this.pendingInLock = [];\n    /**\n     * Used to broadcast state change events to other tabs listening.\n     */\n    this.broadcastChannel = null;\n    this.logger = console.log;\n    this.instanceID = GoTrueClient.nextInstanceID;\n    GoTrueClient.nextInstanceID += 1;\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n    }\n    const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n    this.logDebugMessages = !!settings.debug;\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug;\n    }\n    this.persistSession = settings.persistSession;\n    this.storageKey = settings.storageKey;\n    this.autoRefreshToken = settings.autoRefreshToken;\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch\n    });\n    this.url = settings.url;\n    this.headers = settings.headers;\n    this.fetch = resolveFetch(settings.fetch);\n    this.lock = settings.lock || lockNoOp;\n    this.detectSessionInUrl = settings.detectSessionInUrl;\n    this.flowType = settings.flowType;\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this)\n    };\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage;\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = localStorageAdapter;\n        } else {\n          this.memoryStorage = {};\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n      }\n    } else {\n      this.memoryStorage = {};\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n    }\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n      } catch (e) {\n        console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n      }\n      (_a = this.broadcastChannel) === null || _a === void 0 ? void 0 : _a.addEventListener('message', async event => {\n        this._debug('received broadcast notification from other tab or client', event);\n        await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n      });\n    }\n    this.initialize();\n  }\n  _debug(...args) {\n    if (this.logDebugMessages) {\n      this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n    }\n    return this;\n  }\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  async initialize() {\n    if (this.initializePromise) {\n      return await this.initializePromise;\n    }\n    this.initializePromise = (async () => {\n      return await this._acquireLock(-1, async () => {\n        return await this._initialize();\n      });\n    })();\n    return await this.initializePromise;\n  }\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  async _initialize() {\n    try {\n      const isPKCEFlow = isBrowser() ? await this._isPKCEFlow() : false;\n      this._debug('#_initialize()', 'begin', 'is PKCE flow', isPKCEFlow);\n      if (isPKCEFlow || this.detectSessionInUrl && this._isImplicitGrantFlow()) {\n        const {\n          data,\n          error\n        } = await this._getSessionFromURL(isPKCEFlow);\n        if (error) {\n          this._debug('#_initialize()', 'error detecting session from URL', error);\n          // hacky workaround to keep the existing session if there's an error returned from identity linking\n          // TODO: once error codes are ready, we should match against it instead of the message\n          if ((error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked' || (error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked to another user') {\n            return {\n              error\n            };\n          }\n          // failed login attempt via url,\n          // remove old session as in verifyOtp, signUp and signInWith*\n          await this._removeSession();\n          return {\n            error\n          };\n        }\n        const {\n          session,\n          redirectType\n        } = data;\n        this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n        await this._saveSession(session);\n        setTimeout(async () => {\n          if (redirectType === 'recovery') {\n            await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n          } else {\n            await this._notifyAllSubscribers('SIGNED_IN', session);\n          }\n        }, 0);\n        return {\n          error: null\n        };\n      }\n      // no login attempt via callback url try to recover session from storage\n      await this._recoverAndRefresh();\n      return {\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          error\n        };\n      }\n      return {\n        error: new AuthUnknownError('Unexpected error during initialization', error)\n      };\n    } finally {\n      await this._handleVisibilityChange();\n      this._debug('#_initialize()', 'end');\n    }\n  }\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  async signUp(credentials) {\n    var _a, _b, _c;\n    try {\n      await this._removeSession();\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          const codeVerifier = generatePKCEVerifier();\n          await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n          codeChallenge = await generatePKCEChallenge(codeVerifier);\n          codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        }\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: {\n            email,\n            password,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          xform: _sessionResponse\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n            channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponse\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error || !data) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: error\n        };\n      }\n      const session = data.session;\n      const user = data.user;\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  async signInWithPassword(credentials) {\n    try {\n      await this._removeSession();\n      let res;\n      if ('email' in credentials) {\n        const {\n          email,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            email,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          password,\n          options\n        } = credentials;\n        res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n          headers: this.headers,\n          body: {\n            phone,\n            password,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponsePassword\n        });\n      } else {\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n      }\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign({\n          user: data.user,\n          session: data.session\n        }, data.weak_password ? {\n          weakPassword: data.weak_password\n        } : null),\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  async signInWithOAuth(credentials) {\n    var _a, _b, _c, _d;\n    await this._removeSession();\n    return await this._handleProviderSignIn(credentials.provider, {\n      redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n      scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n      queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n      skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect\n    });\n  }\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  async exchangeCodeForSession(authCode) {\n    await this.initializePromise;\n    return this._acquireLock(-1, async () => {\n      return this._exchangeCodeForSession(authCode);\n    });\n  }\n  async _exchangeCodeForSession(authCode) {\n    const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n    const {\n      data,\n      error\n    } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n      headers: this.headers,\n      body: {\n        auth_code: authCode,\n        code_verifier: codeVerifier\n      },\n      xform: _sessionResponse\n    });\n    await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    if (error) {\n      return {\n        data: {\n          user: null,\n          session: null,\n          redirectType: null\n        },\n        error\n      };\n    } else if (!data || !data.session || !data.user) {\n      return {\n        data: {\n          user: null,\n          session: null,\n          redirectType: null\n        },\n        error: new AuthInvalidTokenResponseError()\n      };\n    }\n    if (data.session) {\n      await this._saveSession(data.session);\n      await this._notifyAllSubscribers('SIGNED_IN', data.session);\n    }\n    return {\n      data: Object.assign(Object.assign({}, data), {\n        redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null\n      }),\n      error\n    };\n  }\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  async signInWithIdToken(credentials) {\n    await this._removeSession();\n    try {\n      const {\n        options,\n        provider,\n        token,\n        access_token,\n        nonce\n      } = credentials;\n      const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n        headers: this.headers,\n        body: {\n          provider,\n          id_token: token,\n          access_token,\n          nonce,\n          gotrue_meta_security: {\n            captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n          }\n        },\n        xform: _sessionResponse\n      });\n      const {\n        data,\n        error\n      } = res;\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        await this._saveSession(data.session);\n        await this._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data,\n        error\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  async signInWithOtp(credentials) {\n    var _a, _b, _c, _d, _e;\n    try {\n      await this._removeSession();\n      if ('email' in credentials) {\n        const {\n          email,\n          options\n        } = credentials;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n          const codeVerifier = generatePKCEVerifier();\n          await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n          codeChallenge = await generatePKCEChallenge(codeVerifier);\n          codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        }\n        const {\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            email,\n            data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n            create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      if ('phone' in credentials) {\n        const {\n          phone,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n          headers: this.headers,\n          body: {\n            phone,\n            data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n            create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            },\n            channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms'\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  async verifyOtp(params) {\n    var _a, _b;\n    try {\n      if (params.type !== 'email_change' && params.type !== 'phone_change') {\n        // we don't want to remove the authenticated session if the user is performing an email_change or phone_change verification\n        await this._removeSession();\n      }\n      let redirectTo = undefined;\n      let captchaToken = undefined;\n      if ('options' in params) {\n        redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n        captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n      }\n      const {\n        data,\n        error\n      } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n        headers: this.headers,\n        body: Object.assign(Object.assign({}, params), {\n          gotrue_meta_security: {\n            captcha_token: captchaToken\n          }\n        }),\n        redirectTo,\n        xform: _sessionResponse\n      });\n      if (error) {\n        throw error;\n      }\n      if (!data) {\n        throw new Error('An error occurred on token verification.');\n      }\n      const session = data.session;\n      const user = data.user;\n      if (session === null || session === void 0 ? void 0 : session.access_token) {\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  async signInWithSSO(params) {\n    var _a, _b, _c;\n    try {\n      await this._removeSession();\n      let codeChallenge = null;\n      let codeChallengeMethod = null;\n      if (this.flowType === 'pkce') {\n        const codeVerifier = generatePKCEVerifier();\n        await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n        codeChallenge = await generatePKCEChallenge(codeVerifier);\n        codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n      }\n      return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n        body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, 'providerId' in params ? {\n          provider_id: params.providerId\n        } : null), 'domain' in params ? {\n          domain: params.domain\n        } : null), {\n          redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined\n        }), ((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken) ? {\n          gotrue_meta_security: {\n            captcha_token: params.options.captchaToken\n          }\n        } : null), {\n          skip_http_redirect: true,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod\n        }),\n        headers: this.headers,\n        xform: _ssoResponse\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  async reauthenticate() {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._reauthenticate();\n    });\n  }\n  async _reauthenticate() {\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) throw sessionError;\n        if (!session) throw new AuthSessionMissingError();\n        const {\n          error\n        } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n          headers: this.headers,\n          jwt: session.access_token\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  async resend(credentials) {\n    try {\n      if (credentials.type != 'email_change' && credentials.type != 'phone_change') {\n        await this._removeSession();\n      }\n      const endpoint = `${this.url}/resend`;\n      if ('email' in credentials) {\n        const {\n          email,\n          type,\n          options\n        } = credentials;\n        const {\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            email,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n        });\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      } else if ('phone' in credentials) {\n        const {\n          phone,\n          type,\n          options\n        } = credentials;\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', endpoint, {\n          headers: this.headers,\n          body: {\n            phone,\n            type,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          }\n        });\n        return {\n          data: {\n            user: null,\n            session: null,\n            messageId: data === null || data === void 0 ? void 0 : data.message_id\n          },\n          error\n        };\n      }\n      throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns the session, refreshing it if necessary.\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   */\n  async getSession() {\n    await this.initializePromise;\n    return this._acquireLock(-1, async () => {\n      return this._useSession(async result => {\n        return result;\n      });\n    });\n  }\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  async _acquireLock(acquireTimeout, fn) {\n    this._debug('#_acquireLock', 'begin', acquireTimeout);\n    try {\n      if (this.lockAcquired) {\n        const last = this.pendingInLock.length ? this.pendingInLock[this.pendingInLock.length - 1] : Promise.resolve();\n        const result = (async () => {\n          await last;\n          return await fn();\n        })();\n        this.pendingInLock.push((async () => {\n          try {\n            await result;\n          } catch (e) {\n            // we just care if it finished\n          }\n        })());\n        return result;\n      }\n      return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n        this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n        try {\n          this.lockAcquired = true;\n          const result = fn();\n          this.pendingInLock.push((async () => {\n            try {\n              await result;\n            } catch (e) {\n              // we just care if it finished\n            }\n          })());\n          await result;\n          // keep draining the queue until there's nothing to wait on\n          while (this.pendingInLock.length) {\n            const waitOn = [...this.pendingInLock];\n            await Promise.all(waitOn);\n            this.pendingInLock.splice(0, waitOn.length);\n          }\n          return await result;\n        } finally {\n          this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n          this.lockAcquired = false;\n        }\n      });\n    } finally {\n      this._debug('#_acquireLock', 'end');\n    }\n  }\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  async _useSession(fn) {\n    this._debug('#_useSession', 'begin');\n    try {\n      // the use of __loadSession here is the only correct use of the function!\n      const result = await this.__loadSession();\n      return await fn(result);\n    } finally {\n      this._debug('#_useSession', 'end');\n    }\n  }\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  async __loadSession() {\n    this._debug('#__loadSession()', 'begin');\n    if (!this.lockAcquired) {\n      this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n    }\n    try {\n      let currentSession = null;\n      const maybeSession = await getItemAsync(this.storage, this.storageKey);\n      this._debug('#getSession()', 'session from storage', maybeSession);\n      if (maybeSession !== null) {\n        if (this._isValidSession(maybeSession)) {\n          currentSession = maybeSession;\n        } else {\n          this._debug('#getSession()', 'session from storage is not valid');\n          await this._removeSession();\n        }\n      }\n      if (!currentSession) {\n        return {\n          data: {\n            session: null\n          },\n          error: null\n        };\n      }\n      const hasExpired = currentSession.expires_at ? currentSession.expires_at <= Date.now() / 1000 : false;\n      this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n      if (!hasExpired) {\n        return {\n          data: {\n            session: currentSession\n          },\n          error: null\n        };\n      }\n      const {\n        session,\n        error\n      } = await this._callRefreshToken(currentSession.refresh_token);\n      if (error) {\n        return {\n          data: {\n            session: null\n          },\n          error\n        };\n      }\n      return {\n        data: {\n          session\n        },\n        error: null\n      };\n    } finally {\n      this._debug('#__loadSession()', 'end');\n    }\n  }\n  /**\n   * Gets the current user details if there is an existing session.\n   * @param jwt Takes in an optional access token jwt. If no jwt is provided, getUser() will attempt to get the jwt from the current session.\n   */\n  async getUser(jwt) {\n    if (jwt) {\n      return await this._getUser(jwt);\n    }\n    await this.initializePromise;\n    return this._acquireLock(-1, async () => {\n      return await this._getUser();\n    });\n  }\n  async _getUser(jwt) {\n    try {\n      if (jwt) {\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: jwt,\n          xform: _userResponse\n        });\n      }\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        return await _request(this.fetch, 'GET', `${this.url}/user`, {\n          headers: this.headers,\n          jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n          xform: _userResponse\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Updates user data for a logged in user.\n   */\n  async updateUser(attributes, options = {}) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._updateUser(attributes, options);\n    });\n  }\n  async _updateUser(attributes, options = {}) {\n    try {\n      return await this._useSession(async result => {\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          throw sessionError;\n        }\n        if (!sessionData.session) {\n          throw new AuthSessionMissingError();\n        }\n        const session = sessionData.session;\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce' && attributes.email != null) {\n          const codeVerifier = generatePKCEVerifier();\n          await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n          codeChallenge = await generatePKCEChallenge(codeVerifier);\n          codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        }\n        const {\n          data,\n          error: userError\n        } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n          headers: this.headers,\n          redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n          body: Object.assign(Object.assign({}, attributes), {\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          }),\n          jwt: session.access_token,\n          xform: _userResponse\n        });\n        if (userError) throw userError;\n        session.user = data.user;\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('USER_UPDATED', session);\n        return {\n          data: {\n            user: session.user\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Decodes a JWT (without performing any validation).\n   */\n  _decodeJWT(jwt) {\n    return decodeJWTPayload(jwt);\n  }\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  async setSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._setSession(currentSession);\n    });\n  }\n  async _setSession(currentSession) {\n    try {\n      if (!currentSession.access_token || !currentSession.refresh_token) {\n        throw new AuthSessionMissingError();\n      }\n      const timeNow = Date.now() / 1000;\n      let expiresAt = timeNow;\n      let hasExpired = true;\n      let session = null;\n      const payload = decodeJWTPayload(currentSession.access_token);\n      if (payload.exp) {\n        expiresAt = payload.exp;\n        hasExpired = expiresAt <= timeNow;\n      }\n      if (hasExpired) {\n        const {\n          session: refreshedSession,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!refreshedSession) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        session = refreshedSession;\n      } else {\n        const {\n          data,\n          error\n        } = await this._getUser(currentSession.access_token);\n        if (error) {\n          throw error;\n        }\n        session = {\n          access_token: currentSession.access_token,\n          refresh_token: currentSession.refresh_token,\n          user: data.user,\n          token_type: 'bearer',\n          expires_in: expiresAt - timeNow,\n          expires_at: expiresAt\n        };\n        await this._saveSession(session);\n        await this._notifyAllSubscribers('SIGNED_IN', session);\n      }\n      return {\n        data: {\n          user: session.user,\n          session\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  async refreshSession(currentSession) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._refreshSession(currentSession);\n    });\n  }\n  async _refreshSession(currentSession) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        if (!currentSession) {\n          const {\n            data,\n            error\n          } = result;\n          if (error) {\n            throw error;\n          }\n          currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n        }\n        if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n          throw new AuthSessionMissingError();\n        }\n        const {\n          session,\n          error\n        } = await this._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: null\n          };\n        }\n        return {\n          data: {\n            user: session.user,\n            session\n          },\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            user: null,\n            session: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets the session data from a URL string\n   */\n  async _getSessionFromURL(isPKCEFlow) {\n    try {\n      if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.');\n      if (this.flowType === 'implicit' && !this._isImplicitGrantFlow()) {\n        throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n      } else if (this.flowType == 'pkce' && !isPKCEFlow) {\n        throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n      }\n      const params = parseParametersFromURL(window.location.href);\n      if (isPKCEFlow) {\n        if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n        const {\n          data,\n          error\n        } = await this._exchangeCodeForSession(params.code);\n        if (error) throw error;\n        const url = new URL(window.location.href);\n        url.searchParams.delete('code');\n        window.history.replaceState(window.history.state, '', url.toString());\n        return {\n          data: {\n            session: data.session,\n            redirectType: null\n          },\n          error: null\n        };\n      }\n      if (params.error || params.error_description || params.error_code) {\n        throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n          error: params.error || 'unspecified_error',\n          code: params.error_code || 'unspecified_code'\n        });\n      }\n      const {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        refresh_token,\n        expires_in,\n        expires_at,\n        token_type\n      } = params;\n      if (!access_token || !expires_in || !refresh_token || !token_type) {\n        throw new AuthImplicitGrantRedirectError('No session defined in URL');\n      }\n      const timeNow = Math.round(Date.now() / 1000);\n      const expiresIn = parseInt(expires_in);\n      let expiresAt = timeNow + expiresIn;\n      if (expires_at) {\n        expiresAt = parseInt(expires_at);\n      }\n      const actuallyExpiresIn = expiresAt - timeNow;\n      if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION) {\n        console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n      }\n      const issuedAt = expiresAt - expiresIn;\n      if (timeNow - issuedAt >= 120) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n      } else if (timeNow - issuedAt < 0) {\n        console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clok for skew', issuedAt, expiresAt, timeNow);\n      }\n      const {\n        data,\n        error\n      } = await this._getUser(access_token);\n      if (error) throw error;\n      const session = {\n        provider_token,\n        provider_refresh_token,\n        access_token,\n        expires_in: expiresIn,\n        expires_at: expiresAt,\n        refresh_token,\n        token_type,\n        user: data.user\n      };\n      // Remove tokens from URL\n      window.location.hash = '';\n      this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n      return {\n        data: {\n          session,\n          redirectType: params.type\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  _isImplicitGrantFlow() {\n    const params = parseParametersFromURL(window.location.href);\n    return !!(isBrowser() && (params.access_token || params.error_description));\n  }\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  async _isPKCEFlow() {\n    const params = parseParametersFromURL(window.location.href);\n    const currentStorageContent = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n    return !!(params.code && currentStorageContent);\n  }\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  async signOut(options = {\n    scope: 'global'\n  }) {\n    await this.initializePromise;\n    return await this._acquireLock(-1, async () => {\n      return await this._signOut(options);\n    });\n  }\n  async _signOut({\n    scope\n  } = {\n    scope: 'global'\n  }) {\n    return await this._useSession(async result => {\n      var _a;\n      const {\n        data,\n        error: sessionError\n      } = result;\n      if (sessionError) {\n        return {\n          error: sessionError\n        };\n      }\n      const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n      if (accessToken) {\n        const {\n          error\n        } = await this.admin.signOut(accessToken, scope);\n        if (error) {\n          // ignore 404s since user might not exist anymore\n          // ignore 401s since an invalid or expired JWT should sign out the current session\n          if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401))) {\n            return {\n              error\n            };\n          }\n        }\n      }\n      if (scope !== 'others') {\n        await this._removeSession();\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n      }\n      return {\n        error: null\n      };\n    });\n  }\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(callback) {\n    const id = uuid();\n    const subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id);\n        this.stateChangeEmitters.delete(id);\n      }\n    };\n    this._debug('#onAuthStateChange()', 'registered callback with id', id);\n    this.stateChangeEmitters.set(id, subscription);\n    (async () => {\n      await this.initializePromise;\n      await this._acquireLock(-1, async () => {\n        this._emitInitialSession(id);\n      });\n    })();\n    return {\n      data: {\n        subscription\n      }\n    };\n  }\n  async _emitInitialSession(id) {\n    return await this._useSession(async result => {\n      var _a, _b;\n      try {\n        const {\n          data: {\n            session\n          },\n          error\n        } = result;\n        if (error) throw error;\n        await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n      } catch (err) {\n        await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n        this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n        console.error(err);\n      }\n    });\n  }\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  async resetPasswordForEmail(email, options = {}) {\n    let codeChallenge = null;\n    let codeChallengeMethod = null;\n    if (this.flowType === 'pkce') {\n      const codeVerifier = generatePKCEVerifier();\n      await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, `${codeVerifier}/PASSWORD_RECOVERY`);\n      codeChallenge = await generatePKCEChallenge(codeVerifier);\n      codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    }\n    try {\n      return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n        body: {\n          email,\n          code_challenge: codeChallenge,\n          code_challenge_method: codeChallengeMethod,\n          gotrue_meta_security: {\n            captcha_token: options.captchaToken\n          }\n        },\n        headers: this.headers,\n        redirectTo: options.redirectTo\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Gets all the identities linked to a user.\n   */\n  async getUserIdentities() {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this.getUser();\n      if (error) throw error;\n      return {\n        data: {\n          identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : []\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  async linkIdentity(credentials) {\n    var _a;\n    try {\n      const {\n        data,\n        error\n      } = await this._useSession(async result => {\n        var _a, _b, _c, _d, _e;\n        const {\n          data,\n          error\n        } = result;\n        if (error) throw error;\n        const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n          redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n          scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n          queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n          skipBrowserRedirect: true\n        });\n        return await _request(this.fetch, 'GET', url, {\n          headers: this.headers,\n          jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined\n        });\n      });\n      if (error) throw error;\n      if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n        window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n      }\n      return {\n        data: {\n          provider: credentials.provider,\n          url: data === null || data === void 0 ? void 0 : data.url\n        },\n        error: null\n      };\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: {\n            provider: credentials.provider,\n            url: null\n          },\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  async unlinkIdentity(identity) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data,\n          error\n        } = result;\n        if (error) {\n          throw error;\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n          headers: this.headers,\n          jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  async _refreshAccessToken(refreshToken) {\n    const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      const startedAt = Date.now();\n      // will attempt to refresh the token with exponential backoff\n      return await retryable(async attempt => {\n        await sleep(attempt * 200); // 0, 200, 400, 800, ...\n        this._debug(debugName, 'refreshing attempt', attempt);\n        return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n          body: {\n            refresh_token: refreshToken\n          },\n          headers: this.headers,\n          xform: _sessionResponse\n        });\n      }, (attempt, _, result) => result && result.error && isAuthRetryableFetchError(result.error) &&\n      // retryable only if the request can be sent before the backoff overflows the tick duration\n      Date.now() + (attempt + 1) * 200 - startedAt < AUTO_REFRESH_TICK_DURATION);\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        return {\n          data: {\n            session: null,\n            user: null\n          },\n          error\n        };\n      }\n      throw error;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  _isValidSession(maybeSession) {\n    const isValidSession = typeof maybeSession === 'object' && maybeSession !== null && 'access_token' in maybeSession && 'refresh_token' in maybeSession && 'expires_at' in maybeSession;\n    return isValidSession;\n  }\n  async _handleProviderSignIn(provider, options) {\n    const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n      redirectTo: options.redirectTo,\n      scopes: options.scopes,\n      queryParams: options.queryParams\n    });\n    this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n    // try to open on the browser\n    if (isBrowser() && !options.skipBrowserRedirect) {\n      window.location.assign(url);\n    }\n    return {\n      data: {\n        provider,\n        url\n      },\n      error: null\n    };\n  }\n  /**\n   * Recovers the session from LocalStorage and refreshes\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  async _recoverAndRefresh() {\n    var _a;\n    const debugName = '#_recoverAndRefresh()';\n    this._debug(debugName, 'begin');\n    try {\n      const currentSession = await getItemAsync(this.storage, this.storageKey);\n      this._debug(debugName, 'session from storage', currentSession);\n      if (!this._isValidSession(currentSession)) {\n        this._debug(debugName, 'session is not valid');\n        if (currentSession !== null) {\n          await this._removeSession();\n        }\n        return;\n      }\n      const timeNow = Math.round(Date.now() / 1000);\n      const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) < timeNow + EXPIRY_MARGIN;\n      this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN}s`);\n      if (expiresWithMargin) {\n        if (this.autoRefreshToken && currentSession.refresh_token) {\n          const {\n            error\n          } = await this._callRefreshToken(currentSession.refresh_token);\n          if (error) {\n            console.error(error);\n            if (!isAuthRetryableFetchError(error)) {\n              this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n              await this._removeSession();\n            }\n          }\n        }\n      } else {\n        // no need to persist currentSession again, as we just loaded it from\n        // local storage; persisting it again may overwrite a value saved by\n        // another client with access to the same local storage\n        await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n      }\n    } catch (err) {\n      this._debug(debugName, 'error', err);\n      console.error(err);\n      return;\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  async _callRefreshToken(refreshToken) {\n    var _a, _b;\n    if (!refreshToken) {\n      throw new AuthSessionMissingError();\n    }\n    // refreshing is already in progress\n    if (this.refreshingDeferred) {\n      return this.refreshingDeferred.promise;\n    }\n    const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n    this._debug(debugName, 'begin');\n    try {\n      this.refreshingDeferred = new Deferred();\n      const {\n        data,\n        error\n      } = await this._refreshAccessToken(refreshToken);\n      if (error) throw error;\n      if (!data.session) throw new AuthSessionMissingError();\n      await this._saveSession(data.session);\n      await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n      const result = {\n        session: data.session,\n        error: null\n      };\n      this.refreshingDeferred.resolve(result);\n      return result;\n    } catch (error) {\n      this._debug(debugName, 'error', error);\n      if (isAuthError(error)) {\n        const result = {\n          session: null,\n          error\n        };\n        if (!isAuthRetryableFetchError(error)) {\n          await this._removeSession();\n          await this._notifyAllSubscribers('SIGNED_OUT', null);\n        }\n        (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n        return result;\n      }\n      (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n      throw error;\n    } finally {\n      this.refreshingDeferred = null;\n      this._debug(debugName, 'end');\n    }\n  }\n  async _notifyAllSubscribers(event, session, broadcast = true) {\n    const debugName = `#_notifyAllSubscribers(${event})`;\n    this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n    try {\n      if (this.broadcastChannel && broadcast) {\n        this.broadcastChannel.postMessage({\n          event,\n          session\n        });\n      }\n      const errors = [];\n      const promises = Array.from(this.stateChangeEmitters.values()).map(async x => {\n        try {\n          await x.callback(event, session);\n        } catch (e) {\n          errors.push(e);\n        }\n      });\n      await Promise.all(promises);\n      if (errors.length > 0) {\n        for (let i = 0; i < errors.length; i += 1) {\n          console.error(errors[i]);\n        }\n        throw errors[0];\n      }\n    } finally {\n      this._debug(debugName, 'end');\n    }\n  }\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  async _saveSession(session) {\n    this._debug('#_saveSession()', session);\n    await setItemAsync(this.storage, this.storageKey, session);\n  }\n  async _removeSession() {\n    this._debug('#_removeSession()');\n    await removeItemAsync(this.storage, this.storageKey);\n  }\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()');\n    const callback = this.visibilityChangedCallback;\n    this.visibilityChangedCallback = null;\n    try {\n      if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n        window.removeEventListener('visibilitychange', callback);\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e);\n    }\n  }\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  async _startAutoRefresh() {\n    await this._stopAutoRefresh();\n    this._debug('#_startAutoRefresh()');\n    const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION);\n    this.autoRefreshTicker = ticker;\n    if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n      // ticker is a NodeJS Timeout object that has an `unref` method\n      // https://nodejs.org/api/timers.html#timeoutunref\n      // When auto refresh is used in NodeJS (like for testing) the\n      // `setInterval` is preventing the process from being marked as\n      // finished and tests run endlessly. This can be prevented by calling\n      // `unref()` on the returned object.\n      ticker.unref();\n      // @ts-ignore\n    } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n      // similar like for NodeJS, but with the Deno API\n      // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n      // @ts-ignore\n      Deno.unrefTimer(ticker);\n    }\n    // run the tick immediately, but in the next pass of the event loop so that\n    // #_initialize can be allowed to complete without recursively waiting on\n    // itself\n    setTimeout(async () => {\n      await this.initializePromise;\n      await this._autoRefreshTokenTick();\n    }, 0);\n  }\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  async _stopAutoRefresh() {\n    this._debug('#_stopAutoRefresh()');\n    const ticker = this.autoRefreshTicker;\n    this.autoRefreshTicker = null;\n    if (ticker) {\n      clearInterval(ticker);\n    }\n  }\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  async startAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._startAutoRefresh();\n  }\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  async stopAutoRefresh() {\n    this._removeVisibilityChangedCallback();\n    await this._stopAutoRefresh();\n  }\n  /**\n   * Runs the auto refresh token tick.\n   */\n  async _autoRefreshTokenTick() {\n    this._debug('#_autoRefreshTokenTick()', 'begin');\n    try {\n      await this._acquireLock(0, async () => {\n        try {\n          const now = Date.now();\n          try {\n            return await this._useSession(async result => {\n              const {\n                data: {\n                  session\n                }\n              } = result;\n              if (!session || !session.refresh_token || !session.expires_at) {\n                this._debug('#_autoRefreshTokenTick()', 'no session');\n                return;\n              }\n              // session will expire in this many ticks (or has already expired if <= 0)\n              const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION);\n              this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n              if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                await this._callRefreshToken(session.refresh_token);\n              }\n            });\n          } catch (e) {\n            console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n          }\n        } finally {\n          this._debug('#_autoRefreshTokenTick()', 'end');\n        }\n      });\n    } catch (e) {\n      if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n        this._debug('auto refresh token tick lock not available');\n      } else {\n        throw e;\n      }\n    }\n  }\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  async _handleVisibilityChange() {\n    this._debug('#_handleVisibilityChange()');\n    if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n      if (this.autoRefreshToken) {\n        // in non-browser environments the refresh token ticker runs always\n        this.startAutoRefresh();\n      }\n      return false;\n    }\n    try {\n      this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n      window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n      // now immediately call the visbility changed callback to setup with the\n      // current visbility state\n      await this._onVisibilityChanged(true); // initial call\n    } catch (error) {\n      console.error('_handleVisibilityChange', error);\n    }\n  }\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  async _onVisibilityChanged(calledFromInitialize) {\n    const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n    this._debug(methodName, 'visibilityState', document.visibilityState);\n    if (document.visibilityState === 'visible') {\n      if (this.autoRefreshToken) {\n        // in browser environments the refresh token ticker runs only on focused tabs\n        // which prevents race conditions\n        this._startAutoRefresh();\n      }\n      if (!calledFromInitialize) {\n        // called when the visibility has changed, i.e. the browser\n        // transitioned from hidden -> visible so we need to see if the session\n        // should be recovered immediately... but to do that we need to acquire\n        // the lock first asynchronously\n        await this.initializePromise;\n        await this._acquireLock(-1, async () => {\n          if (document.visibilityState !== 'visible') {\n            this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n            // visibility has changed while waiting for the lock, abort\n            return;\n          }\n          // recover the session\n          await this._recoverAndRefresh();\n        });\n      }\n    } else if (document.visibilityState === 'hidden') {\n      if (this.autoRefreshToken) {\n        this._stopAutoRefresh();\n      }\n    }\n  }\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  async _getUrlForProvider(url, provider, options) {\n    const urlParams = [`provider=${encodeURIComponent(provider)}`];\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n      urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n    }\n    if (options === null || options === void 0 ? void 0 : options.scopes) {\n      urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n    }\n    if (this.flowType === 'pkce') {\n      const codeVerifier = generatePKCEVerifier();\n      await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n      const codeChallenge = await generatePKCEChallenge(codeVerifier);\n      const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n      this._debug('PKCE', 'code verifier', `${codeVerifier.substring(0, 5)}...`, 'code challenge', codeChallenge, 'method', codeChallengeMethod);\n      const flowParams = new URLSearchParams({\n        code_challenge: `${encodeURIComponent(codeChallenge)}`,\n        code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`\n      });\n      urlParams.push(flowParams.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.queryParams) {\n      const query = new URLSearchParams(options.queryParams);\n      urlParams.push(query.toString());\n    }\n    if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n      urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n    }\n    return `${url}?${urlParams.join('&')}`;\n  }\n  async _unenroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  async _enroll(params) {\n    try {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: sessionData,\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        const {\n          data,\n          error\n        } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n          body: {\n            friendly_name: params.friendlyName,\n            factor_type: params.factorType,\n            issuer: params.issuer\n          },\n          headers: this.headers,\n          jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n        });\n        if (error) {\n          return {\n            data: null,\n            error\n          };\n        }\n        if ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code) {\n          data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n        }\n        return {\n          data,\n          error: null\n        };\n      });\n    } catch (error) {\n      if (isAuthError(error)) {\n        return {\n          data: null,\n          error\n        };\n      }\n      throw error;\n    }\n  }\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  async _verify(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          const {\n            data,\n            error\n          } = await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n            body: {\n              code: params.code,\n              challenge_id: params.challengeId\n            },\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n          if (error) {\n            return {\n              data: null,\n              error\n            };\n          }\n          await this._saveSession(Object.assign({\n            expires_at: Math.round(Date.now() / 1000) + data.expires_in\n          }, data));\n          await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n          return {\n            data,\n            error\n          };\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  async _challenge(params) {\n    return this._acquireLock(-1, async () => {\n      try {\n        return await this._useSession(async result => {\n          var _a;\n          const {\n            data: sessionData,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              data: null,\n              error: sessionError\n            };\n          }\n          return await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n            headers: this.headers,\n            jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n          });\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  async _challengeAndVerify(params) {\n    // both _challenge and _verify independently acquire the lock, so no need\n    // to acquire it here\n    const {\n      data: challengeData,\n      error: challengeError\n    } = await this._challenge({\n      factorId: params.factorId\n    });\n    if (challengeError) {\n      return {\n        data: null,\n        error: challengeError\n      };\n    }\n    return await this._verify({\n      factorId: params.factorId,\n      challengeId: challengeData.id,\n      code: params.code\n    });\n  }\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  async _listFactors() {\n    // use #getUser instead of #_getUser as the former acquires a lock\n    const {\n      data: {\n        user\n      },\n      error: userError\n    } = await this.getUser();\n    if (userError) {\n      return {\n        data: null,\n        error: userError\n      };\n    }\n    const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n    const totp = factors.filter(factor => factor.factor_type === 'totp' && factor.status === 'verified');\n    return {\n      data: {\n        all: factors,\n        totp\n      },\n      error: null\n    };\n  }\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  async _getAuthenticatorAssuranceLevel() {\n    return this._acquireLock(-1, async () => {\n      return await this._useSession(async result => {\n        var _a, _b;\n        const {\n          data: {\n            session\n          },\n          error: sessionError\n        } = result;\n        if (sessionError) {\n          return {\n            data: null,\n            error: sessionError\n          };\n        }\n        if (!session) {\n          return {\n            data: {\n              currentLevel: null,\n              nextLevel: null,\n              currentAuthenticationMethods: []\n            },\n            error: null\n          };\n        }\n        const payload = this._decodeJWT(session.access_token);\n        let currentLevel = null;\n        if (payload.aal) {\n          currentLevel = payload.aal;\n        }\n        let nextLevel = currentLevel;\n        const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter(factor => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n        if (verifiedFactors.length > 0) {\n          nextLevel = 'aal2';\n        }\n        const currentAuthenticationMethods = payload.amr || [];\n        return {\n          data: {\n            currentLevel,\n            nextLevel,\n            currentAuthenticationMethods\n          },\n          error: null\n        };\n      });\n    });\n  }\n}\nGoTrueClient.nextInstanceID = 0;\n", "import GoTrueAdminApi from './GoTrueAdminApi';\nconst AuthAdminApi = GoTrueAdminApi;\nexport default AuthAdminApi;\n", "import GoTrueClient from './GoTrueClient';\nconst AuthClient = GoTrueClient;\nexport default AuthClient;\n", "import { AuthClient } from '@supabase/auth-js';\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options) {\n    super(options);\n  }\n}\n", "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { FunctionsClient } from '@supabase/functions-js';\nimport { PostgrestClient } from '@supabase/postgrest-js';\nimport { RealtimeClient } from '@supabase/realtime-js';\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js';\nimport { DEFAULT_GLOBAL_OPTIONS, DEFAULT_DB_OPTIONS, DEFAULT_AUTH_OPTIONS, DEFAULT_REALTIME_OPTIONS } from './lib/constants';\nimport { fetchWithAuth } from './lib/fetch';\nimport { stripTrailingSlash, applySettingDefaults } from './lib/helpers';\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient';\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient {\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(supabaseUrl, supabaseKey, options) {\n    var _a, _b, _c;\n    this.supabaseUrl = supabaseUrl;\n    this.supabaseKey = supabaseKey;\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n    if (!supabaseKey) throw new Error('supabaseKey is required.');\n    const _supabaseUrl = stripTrailingSlash(supabaseUrl);\n    this.realtimeUrl = `${_supabaseUrl}/realtime/v1`.replace(/^http/i, 'ws');\n    this.authUrl = `${_supabaseUrl}/auth/v1`;\n    this.storageUrl = `${_supabaseUrl}/storage/v1`;\n    this.functionsUrl = `${_supabaseUrl}/functions/v1`;\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${new URL(this.authUrl).hostname.split('.')[0]}-auth-token`;\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), {\n        storageKey: defaultStorageKey\n      }),\n      global: DEFAULT_GLOBAL_OPTIONS\n    };\n    const settings = applySettingDefaults(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n    this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n    this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n    } else {\n      this.accessToken = settings.accessToken;\n      this.auth = new Proxy({}, {\n        get: (_, prop) => {\n          throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n        }\n      });\n    }\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n    this.realtime = this._initRealtimeClient(Object.assign({\n      headers: this.headers,\n      accessToken: this._getAccessToken.bind(this)\n    }, settings.realtime));\n    this.rest = new PostgrestClient(`${_supabaseUrl}/rest/v1`, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch\n    });\n    if (!settings.accessToken) {\n      this._listenForAuthEvents();\n    }\n  }\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions() {\n    return new FunctionsClient(this.functionsUrl, {\n      headers: this.headers,\n      customFetch: this.fetch\n    });\n  }\n  /**\n   * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n   */\n  get storage() {\n    return new SupabaseStorageClient(this.storageUrl, this.headers, this.fetch);\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    return this.rest.from(relation);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return this.rest.schema(schema);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, options = {}) {\n    return this.rest.rpc(fn, args, options);\n  }\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name, opts = {\n    config: {}\n  }) {\n    return this.realtime.channel(name, opts);\n  }\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels() {\n    return this.realtime.getChannels();\n  }\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel) {\n    return this.realtime.removeChannel(channel);\n  }\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels() {\n    return this.realtime.removeAllChannels();\n  }\n  _getAccessToken() {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.accessToken) {\n        return yield this.accessToken();\n      }\n      const {\n        data\n      } = yield this.auth.getSession();\n      return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n    });\n  }\n  _initSupabaseAuthClient({\n    autoRefreshToken,\n    persistSession,\n    detectSessionInUrl,\n    storage,\n    storageKey,\n    flowType,\n    lock,\n    debug\n  }, headers, fetch) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`\n    };\n    return new SupabaseAuthClient({\n      url: this.authUrl,\n      headers: Object.assign(Object.assign({}, authHeaders), headers),\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers\n    });\n  }\n  _initRealtimeClient(options) {\n    return new RealtimeClient(this.realtimeUrl, Object.assign(Object.assign({}, options), {\n      params: Object.assign({\n        apikey: this.supabaseKey\n      }, options === null || options === void 0 ? void 0 : options.params)\n    }));\n  }\n  _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n    });\n    return data;\n  }\n  _handleTokenChanged(event, source, token) {\n    if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n      this.changedAccessToken = token;\n    } else if (event === 'SIGNED_OUT') {\n      this.realtime.setAuth();\n      if (source == 'STORAGE') this.auth.signOut();\n      this.changedAccessToken = undefined;\n    }\n  }\n}\n", "import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n  return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAMD,QAAMA,kBAAN,cAA6B,MAAM;AAAA,MACjC,YAAY,SAAS;AACnB,cAAM,QAAQ,OAAO;AACrB,aAAK,OAAO;AACZ,aAAK,UAAU,QAAQ;AACvB,aAAK,OAAO,QAAQ;AACpB,aAAK,OAAO,QAAQ;AAAA,MACtB;AAAA,IACF;AACA,YAAQ,UAAUA;AAAA;AAAA;;;ACnBlB;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAM,eAAe,gBAAgB,+CAA+B;AACpE,QAAM,mBAAmB,gBAAgB,wBAA2B;AACpE,QAAMC,oBAAN,MAAuB;AAAA,MACrB,YAAY,SAAS;AACnB,aAAK,qBAAqB;AAC1B,aAAK,SAAS,QAAQ;AACtB,aAAK,MAAM,QAAQ;AACnB,aAAK,UAAU,QAAQ;AACvB,aAAK,SAAS,QAAQ;AACtB,aAAK,OAAO,QAAQ;AACpB,aAAK,qBAAqB,QAAQ;AAClC,aAAK,SAAS,QAAQ;AACtB,aAAK,gBAAgB,QAAQ;AAC7B,YAAI,QAAQ,OAAO;AACjB,eAAK,QAAQ,QAAQ;AAAA,QACvB,WAAW,OAAO,UAAU,aAAa;AACvC,eAAK,QAAQ,aAAa;AAAA,QAC5B,OAAO;AACL,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,eAAe;AACb,aAAK,qBAAqB;AAC1B,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIA,UAAU,MAAM,OAAO;AACrB,aAAK,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC7C,aAAK,QAAQ,IAAI,IAAI;AACrB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,aAAa,YAAY;AAE5B,YAAI,KAAK,WAAW,QAAW;AAAA,QAE/B,WAAW,CAAC,OAAO,MAAM,EAAE,SAAS,KAAK,MAAM,GAAG;AAChD,eAAK,QAAQ,gBAAgB,IAAI,KAAK;AAAA,QACxC,OAAO;AACL,eAAK,QAAQ,iBAAiB,IAAI,KAAK;AAAA,QACzC;AACA,YAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,eAAK,QAAQ,cAAc,IAAI;AAAA,QACjC;AAGA,cAAM,SAAS,KAAK;AACpB,YAAI,MAAM,OAAO,KAAK,IAAI,SAAS,GAAG;AAAA,UACpC,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,MAAM,KAAK,UAAU,KAAK,IAAI;AAAA,UAC9B,QAAQ,KAAK;AAAA,QACf,CAAC,EAAE,KAAK,CAAMC,SAAO;AACnB,cAAI,IAAI,IAAI;AACZ,cAAI,QAAQ;AACZ,cAAI,OAAO;AACX,cAAI,QAAQ;AACZ,cAAI,SAASA,KAAI;AACjB,cAAI,aAAaA,KAAI;AACrB,cAAIA,KAAI,IAAI;AACV,gBAAI,KAAK,WAAW,QAAQ;AAC1B,oBAAM,OAAO,MAAMA,KAAI,KAAK;AAC5B,kBAAI,SAAS,IAAI;AAAA,cAEjB,WAAW,KAAK,QAAQ,QAAQ,MAAM,YAAY;AAChD,uBAAO;AAAA,cACT,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK,QAAQ,QAAQ,EAAE,SAAS,iCAAiC,GAAG;AACvG,uBAAO;AAAA,cACT,OAAO;AACL,uBAAO,KAAK,MAAM,IAAI;AAAA,cACxB;AAAA,YACF;AACA,kBAAM,eAAe,KAAK,KAAK,QAAQ,QAAQ,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,iCAAiC;AACjI,kBAAM,gBAAgB,KAAKA,KAAI,QAAQ,IAAI,eAAe,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,GAAG;AAC9G,gBAAI,eAAe,gBAAgB,aAAa,SAAS,GAAG;AAC1D,sBAAQ,SAAS,aAAa,CAAC,CAAC;AAAA,YAClC;AAGA,gBAAI,KAAK,iBAAiB,KAAK,WAAW,SAAS,MAAM,QAAQ,IAAI,GAAG;AACtE,kBAAI,KAAK,SAAS,GAAG;AACnB,wBAAQ;AAAA;AAAA,kBAEN,MAAM;AAAA,kBACN,SAAS,mBAAmB,KAAK,MAAM;AAAA,kBACvC,MAAM;AAAA,kBACN,SAAS;AAAA,gBACX;AACA,uBAAO;AACP,wBAAQ;AACR,yBAAS;AACT,6BAAa;AAAA,cACf,WAAW,KAAK,WAAW,GAAG;AAC5B,uBAAO,KAAK,CAAC;AAAA,cACf,OAAO;AACL,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF,OAAO;AACL,kBAAM,OAAO,MAAMA,KAAI,KAAK;AAC5B,gBAAI;AACF,sBAAQ,KAAK,MAAM,IAAI;AAEvB,kBAAI,MAAM,QAAQ,KAAK,KAAKA,KAAI,WAAW,KAAK;AAC9C,uBAAO,CAAC;AACR,wBAAQ;AACR,yBAAS;AACT,6BAAa;AAAA,cACf;AAAA,YACF,SAAS,IAAI;AAEX,kBAAIA,KAAI,WAAW,OAAO,SAAS,IAAI;AACrC,yBAAS;AACT,6BAAa;AAAA,cACf,OAAO;AACL,wBAAQ;AAAA,kBACN,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AACA,gBAAI,SAAS,KAAK,mBAAmB,KAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,QAAQ,IAAI;AAClK,sBAAQ;AACR,uBAAS;AACT,2BAAa;AAAA,YACf;AACA,gBAAI,SAAS,KAAK,oBAAoB;AACpC,oBAAM,IAAI,iBAAiB,QAAQ,KAAK;AAAA,YAC1C;AAAA,UACF;AACA,gBAAM,oBAAoB;AAAA,YACxB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,iBAAO;AAAA,QACT,EAAC;AACD,YAAI,CAAC,KAAK,oBAAoB;AAC5B,gBAAM,IAAI,MAAM,gBAAc;AAC5B,gBAAI,IAAI,IAAI;AACZ,mBAAO;AAAA,cACL,OAAO;AAAA,gBACL,SAAS,IAAI,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,UAAU,QAAQ,OAAO,SAAS,KAAK,YAAY,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,OAAO;AAAA,gBACvN,SAAS,IAAI,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,WAAW,QAAQ,OAAO,SAAS,KAAK,EAAE;AAAA,gBAC/H,MAAM;AAAA,gBACN,MAAM,IAAI,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,UAAU,QAAQ,OAAO,SAAS,KAAK,EAAE;AAAA,cAC7H;AAAA,cACA,MAAM;AAAA,cACN,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,YAAY;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH;AACA,eAAO,IAAI,KAAK,aAAa,UAAU;AAAA,MACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU;AAER,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBA,gBAAgB;AACd,eAAO;AAAA,MACT;AAAA,IACF;AACA,YAAQ,UAAUD;AAAA;AAAA;;;ACnNlB;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,QAAM,qBAAqB,gBAAgB,0BAA6B;AACxE,QAAME,6BAAN,cAAwC,mBAAmB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUjE,OAAO,SAAS;AAEd,YAAI,SAAS;AACb,cAAM,kBAAkB,YAAY,QAAQ,YAAY,SAAS,UAAU,KAAK,MAAM,EAAE,EAAE,IAAI,OAAK;AACjG,cAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,mBAAO;AAAA,UACT;AACA,cAAI,MAAM,KAAK;AACb,qBAAS,CAAC;AAAA,UACZ;AACA,iBAAO;AAAA,QACT,CAAC,EAAE,KAAK,EAAE;AACV,aAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,eAAK,QAAQ,QAAQ,KAAK;AAAA,QAC5B;AACA,aAAK,QAAQ,QAAQ,KAAK;AAC1B,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAmBA,MAAM,QAAQ;AAAA,QACZ,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,MACpB,IAAI,CAAC,GAAG;AACN,cAAM,MAAM,kBAAkB,GAAG,eAAe,WAAW;AAC3D,cAAM,gBAAgB,KAAK,IAAI,aAAa,IAAI,GAAG;AACnD,aAAK,IAAI,aAAa,IAAI,KAAK,GAAG,gBAAgB,GAAG,aAAa,MAAM,EAAE,GAAG,MAAM,IAAI,YAAY,QAAQ,MAAM,GAAG,eAAe,SAAY,KAAK,aAAa,gBAAgB,YAAY,EAAE;AAC/L,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,MAAM,OAAO;AAAA,QACX;AAAA,QACA,kBAAkB;AAAA,MACpB,IAAI,CAAC,GAAG;AACN,cAAM,MAAM,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACjF,aAAK,IAAI,aAAa,IAAI,KAAK,GAAG,KAAK,EAAE;AACzC,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBA,MAAM,MAAM,IAAI;AAAA,QACd;AAAA,QACA,kBAAkB;AAAA,MACpB,IAAI,CAAC,GAAG;AACN,cAAM,YAAY,OAAO,oBAAoB,cAAc,WAAW,GAAG,eAAe;AACxF,cAAM,WAAW,OAAO,oBAAoB,cAAc,UAAU,GAAG,eAAe;AACtF,aAAK,IAAI,aAAa,IAAI,WAAW,GAAG,IAAI,EAAE;AAE9C,aAAK,IAAI,aAAa,IAAI,UAAU,GAAG,KAAK,OAAO,CAAC,EAAE;AACtD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY,QAAQ;AAClB,aAAK,SAAS;AACd,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS;AACP,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,cAAc;AAGZ,YAAI,KAAK,WAAW,OAAO;AACzB,eAAK,QAAQ,QAAQ,IAAI;AAAA,QAC3B,OAAO;AACL,eAAK,QAAQ,QAAQ,IAAI;AAAA,QAC3B;AACA,aAAK,gBAAgB;AACrB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM;AACJ,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAIA,UAAU;AACR,aAAK,QAAQ,QAAQ,IAAI;AACzB,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA0BA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,MACX,IAAI,CAAC,GAAG;AACN,YAAI;AACJ,cAAM,UAAU,CAAC,UAAU,YAAY,MAAM,UAAU,YAAY,MAAM,WAAW,aAAa,MAAM,UAAU,YAAY,MAAM,MAAM,QAAQ,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG;AAE/K,cAAM,gBAAgB,KAAK,KAAK,QAAQ,QAAQ,OAAO,QAAQ,OAAO,SAAS,KAAK;AACpF,aAAK,QAAQ,QAAQ,IAAI,8BAA8B,MAAM,UAAU,YAAY,cAAc,OAAO;AACxG,YAAI,WAAW,OAAQ,QAAO;AAAA,YAAU,QAAO;AAAA,MACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,WAAW;AACT,YAAI;AACJ,cAAM,KAAK,KAAK,QAAQ,QAAQ,OAAO,QAAQ,OAAO,SAAS,KAAK,IAAI,KAAK,EAAE,SAAS,GAAG;AACzF,eAAK,QAAQ,QAAQ,KAAK;AAAA,QAC5B,OAAO;AACL,eAAK,QAAQ,QAAQ,IAAI;AAAA,QAC3B;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU;AACR,eAAO;AAAA,MACT;AAAA,IACF;AACA,YAAQ,UAAUA;AAAA;AAAA;;;ACnOlB;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,QAAM,8BAA8B,gBAAgB,mCAAsC;AAC1F,QAAMC,0BAAN,cAAqC,4BAA4B,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASvE,GAAG,QAAQ,OAAO;AAChB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,QAAQ,OAAO;AACjB,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,GAAG,QAAQ,OAAO;AAChB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,QAAQ,OAAO;AACjB,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,GAAG,QAAQ,OAAO;AAChB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,IAAI,QAAQ,OAAO;AACjB,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,KAAK,QAAQ,SAAS;AACpB,aAAK,IAAI,aAAa,OAAO,QAAQ,QAAQ,OAAO,EAAE;AACtD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,QAAQ,UAAU;AAC1B,aAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,QAAQ,UAAU;AAC1B,aAAK,IAAI,aAAa,OAAO,QAAQ,cAAc,SAAS,KAAK,GAAG,CAAC,GAAG;AACxE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,MAAM,QAAQ,SAAS;AACrB,aAAK,IAAI,aAAa,OAAO,QAAQ,SAAS,OAAO,EAAE;AACvD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,WAAW,QAAQ,UAAU;AAC3B,aAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,WAAW,QAAQ,UAAU;AAC3B,aAAK,IAAI,aAAa,OAAO,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC,GAAG;AACzE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA,GAAG,QAAQ,OAAO;AAChB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,GAAG,QAAQ,QAAQ;AACjB,cAAM,gBAAgB,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,IAAI,OAAK;AAGzD,cAAI,OAAO,MAAM,YAAY,IAAI,OAAO,OAAO,EAAE,KAAK,CAAC,EAAG,QAAO,IAAI,CAAC;AAAA,cAAS,QAAO,GAAG,CAAC;AAAA,QAC5F,CAAC,EAAE,KAAK,GAAG;AACX,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,aAAa,GAAG;AAC5D,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,QAAQ,OAAO;AACtB,YAAI,OAAO,UAAU,UAAU;AAG7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,QACpD,WAAW,MAAM,QAAQ,KAAK,GAAG;AAE/B,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;AAAA,QAChE,OAAO;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,QACpE;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,YAAY,QAAQ,OAAO;AACzB,YAAI,OAAO,UAAU,UAAU;AAE7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,QACpD,WAAW,MAAM,QAAQ,KAAK,GAAG;AAE/B,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;AAAA,QAChE,OAAO;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,QACpE;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,QAAQ,OAAO;AACrB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,QAAQ,OAAO;AACtB,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,QAAQ,OAAO;AACrB,aAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAClD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,QAAQ,OAAO;AACtB,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,cAAc,QAAQ,OAAO;AAC3B,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,KAAK,EAAE;AACnD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,QAAQ,OAAO;AACtB,YAAI,OAAO,UAAU,UAAU;AAE7B,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,QACpD,OAAO;AAEL,eAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG;AAAA,QAChE;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,WAAW,QAAQ,OAAO;AAAA,QACxB;AAAA,QACA;AAAA,MACF,IAAI,CAAC,GAAG;AACN,YAAI,WAAW;AACf,YAAI,SAAS,SAAS;AACpB,qBAAW;AAAA,QACb,WAAW,SAAS,UAAU;AAC5B,qBAAW;AAAA,QACb,WAAW,SAAS,aAAa;AAC/B,qBAAW;AAAA,QACb;AACA,cAAM,aAAa,WAAW,SAAY,KAAK,IAAI,MAAM;AACzD,aAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,MAAM,UAAU,IAAI,KAAK,EAAE;AAC3E,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,MAAM,OAAO;AACX,eAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,KAAK,MAAM;AACjD,eAAK,IAAI,aAAa,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,QACpD,CAAC;AACD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,IAAI,QAAQ,UAAU,OAAO;AAC3B,aAAK,IAAI,aAAa,OAAO,QAAQ,OAAO,QAAQ,IAAI,KAAK,EAAE;AAC/D,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgBA,GAAG,SAAS;AAAA,QACV;AAAA,QACA,kBAAkB;AAAA,MACpB,IAAI,CAAC,GAAG;AACN,cAAM,MAAM,kBAAkB,GAAG,eAAe,QAAQ;AACxD,aAAK,IAAI,aAAa,OAAO,KAAK,IAAI,OAAO,GAAG;AAChD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,OAAO,QAAQ,UAAU,OAAO;AAC9B,aAAK,IAAI,aAAa,OAAO,QAAQ,GAAG,QAAQ,IAAI,KAAK,EAAE;AAC3D,eAAO;AAAA,MACT;AAAA,IACF;AACA,YAAQ,UAAUA;AAAA;AAAA;;;AC1XlB;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,QAAM,2BAA2B,gBAAgB,gCAAmC;AACpF,QAAMC,yBAAN,MAA4B;AAAA,MAC1B,YAAY,KAAK;AAAA,QACf,UAAU,CAAC;AAAA,QACX;AAAA,QACA,OAAAC;AAAA,MACF,GAAG;AACD,aAAK,MAAM;AACX,aAAK,UAAU;AACf,aAAK,SAAS;AACd,aAAK,QAAQA;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBA,OAAO,SAAS;AAAA,QACd,MAAAC,QAAO;AAAA,QACP;AAAA,MACF,IAAI,CAAC,GAAG;AACN,cAAM,SAASA,QAAO,SAAS;AAE/B,YAAI,SAAS;AACb,cAAM,kBAAkB,YAAY,QAAQ,YAAY,SAAS,UAAU,KAAK,MAAM,EAAE,EAAE,IAAI,OAAK;AACjG,cAAI,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC3B,mBAAO;AAAA,UACT;AACA,cAAI,MAAM,KAAK;AACb,qBAAS,CAAC;AAAA,UACZ;AACA,iBAAO;AAAA,QACT,CAAC,EAAE,KAAK,EAAE;AACV,aAAK,IAAI,aAAa,IAAI,UAAU,cAAc;AAClD,YAAI,OAAO;AACT,eAAK,QAAQ,QAAQ,IAAI,SAAS,KAAK;AAAA,QACzC;AACA,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA,KAAK,KAAK;AAAA,UACV,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2BA,OAAO,QAAQ;AAAA,QACb;AAAA,QACA,gBAAgB;AAAA,MAClB,IAAI,CAAC,GAAG;AACN,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAC;AACxB,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;AAAA,QAC5C;AACA,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;AAAA,QACtC;AACA,YAAI,CAAC,eAAe;AAClB,yBAAe,KAAK,iBAAiB;AAAA,QACvC;AACA,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAChD,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACxE,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,YAAU,IAAI,MAAM,GAAG;AACvE,iBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;AAAA,UAC9D;AAAA,QACF;AACA,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA,KAAK,KAAK;AAAA,UACV,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuCA,OAAO,QAAQ;AAAA,QACb;AAAA,QACA,mBAAmB;AAAA,QACnB;AAAA,QACA,gBAAgB;AAAA,MAClB,IAAI,CAAC,GAAG;AACN,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAC,cAAc,mBAAmB,WAAW,OAAO,aAAa;AACxF,YAAI,eAAe,OAAW,MAAK,IAAI,aAAa,IAAI,eAAe,UAAU;AACjF,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;AAAA,QAC5C;AACA,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;AAAA,QACtC;AACA,YAAI,CAAC,eAAe;AAClB,yBAAe,KAAK,iBAAiB;AAAA,QACvC;AACA,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAChD,YAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,gBAAM,UAAU,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACxE,cAAI,QAAQ,SAAS,GAAG;AACtB,kBAAM,gBAAgB,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,EAAE,IAAI,YAAU,IAAI,MAAM,GAAG;AACvE,iBAAK,IAAI,aAAa,IAAI,WAAW,cAAc,KAAK,GAAG,CAAC;AAAA,UAC9D;AAAA,QACF;AACA,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA,KAAK,KAAK;AAAA,UACV,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBA,OAAO,QAAQ;AAAA,QACb;AAAA,MACF,IAAI,CAAC,GAAG;AACN,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAC;AACxB,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,KAAK,KAAK,QAAQ,QAAQ,CAAC;AAAA,QAC5C;AACA,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;AAAA,QACtC;AACA,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAChD,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA,KAAK,KAAK;AAAA,UACV,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBA,OAAO;AAAA,QACL;AAAA,MACF,IAAI,CAAC,GAAG;AACN,cAAM,SAAS;AACf,cAAM,iBAAiB,CAAC;AACxB,YAAI,OAAO;AACT,yBAAe,KAAK,SAAS,KAAK,EAAE;AAAA,QACtC;AACA,YAAI,KAAK,QAAQ,QAAQ,GAAG;AAC1B,yBAAe,QAAQ,KAAK,QAAQ,QAAQ,CAAC;AAAA,QAC/C;AACA,aAAK,QAAQ,QAAQ,IAAI,eAAe,KAAK,GAAG;AAChD,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA,KAAK,KAAK;AAAA,UACV,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAUF;AAAA;AAAA;;;ACjSlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,YAAQ,UAAU;AAAA;AAAA;;;ACNlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,kBAAkB;AAC1B,QAAM,YAAY;AAClB,YAAQ,kBAAkB;AAAA,MACxB,iBAAiB,gBAAgB,UAAU,OAAO;AAAA,IACpD;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,QAAM,0BAA0B,gBAAgB,+BAAkC;AAClF,QAAM,2BAA2B,gBAAgB,gCAAmC;AACpF,QAAM,cAAc;AAWpB,QAAMG,mBAAN,MAAM,iBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWpB,YAAY,KAAK;AAAA,QACf,UAAU,CAAC;AAAA,QACX;AAAA,QACA,OAAAC;AAAA,MACF,IAAI,CAAC,GAAG;AACN,aAAK,MAAM;AACX,aAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,eAAe,GAAG,OAAO;AACpF,aAAK,aAAa;AAClB,aAAK,QAAQA;AAAA,MACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,UAAU;AACb,cAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,QAAQ,EAAE;AAC7C,eAAO,IAAI,wBAAwB,QAAQ,KAAK;AAAA,UAC9C,SAAS,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAAA,UACvC,QAAQ,KAAK;AAAA,UACb,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,OAAO,QAAQ;AACb,eAAO,IAAI,iBAAgB,KAAK,KAAK;AAAA,UACnC,SAAS,KAAK;AAAA,UACd;AAAA,UACA,OAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBA,IAAI,IAAI,OAAO,CAAC,GAAG;AAAA,QACjB,MAAAC,QAAO;AAAA,QACP,KAAAC,OAAM;AAAA,QACN;AAAA,MACF,IAAI,CAAC,GAAG;AACN,YAAI;AACJ,cAAM,MAAM,IAAI,IAAI,GAAG,KAAK,GAAG,QAAQ,EAAE,EAAE;AAC3C,YAAI;AACJ,YAAID,SAAQC,MAAK;AACf,mBAASD,QAAO,SAAS;AACzB,iBAAO,QAAQ,IAAI,EAGlB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,UAAU,MAAS,EAE1C,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM,QAAQ,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,MAAM,KAAK,MAAM;AACrH,gBAAI,aAAa,OAAO,MAAM,KAAK;AAAA,UACrC,CAAC;AAAA,QACH,OAAO;AACL,mBAAS;AACT,iBAAO;AAAA,QACT;AACA,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC9C,YAAI,OAAO;AACT,kBAAQ,QAAQ,IAAI,SAAS,KAAK;AAAA,QACpC;AACA,eAAO,IAAI,yBAAyB,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,UACA;AAAA,UACA,QAAQ,KAAK;AAAA,UACb;AAAA,UACA,OAAO,KAAK;AAAA,UACZ,YAAY;AAAA,QACd,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAUF;AAAA;AAAA;;;ACnIlB;AAAA;AAAA;AAEA,QAAI,kBAAkB,WAAQ,QAAK,mBAAmB,SAAU,KAAK;AACnE,aAAO,OAAO,IAAI,aAAa,MAAM;AAAA,QACnC,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,iBAAiB,QAAQ,mBAAmB,QAAQ,4BAA4B,QAAQ,yBAAyB,QAAQ,wBAAwB,QAAQ,kBAAkB;AAEnL,QAAM,oBAAoB,gBAAgB,yBAA4B;AACtE,YAAQ,kBAAkB,kBAAkB;AAC5C,QAAM,0BAA0B,gBAAgB,+BAAkC;AAClF,YAAQ,wBAAwB,wBAAwB;AACxD,QAAM,2BAA2B,gBAAgB,gCAAmC;AACpF,YAAQ,yBAAyB,yBAAyB;AAC1D,QAAM,8BAA8B,gBAAgB,mCAAsC;AAC1F,YAAQ,4BAA4B,4BAA4B;AAChE,QAAM,qBAAqB,gBAAgB,0BAA6B;AACxE,YAAQ,mBAAmB,mBAAmB;AAC9C,QAAM,mBAAmB,gBAAgB,wBAA2B;AACpE,YAAQ,iBAAiB,iBAAiB;AAC1C,YAAQ,UAAU;AAAA,MAChB,iBAAiB,kBAAkB;AAAA,MACnC,uBAAuB,wBAAwB;AAAA,MAC/C,wBAAwB,yBAAyB;AAAA,MACjD,2BAA2B,4BAA4B;AAAA,MACvD,kBAAkB,mBAAmB;AAAA,MACrC,gBAAgB,iBAAiB;AAAA,IACnC;AAAA;AAAA;;;AC/BO,IAAM,eAAe,iBAAe;AACzC,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;AAAA,EACX,WAAW,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SAAS,OAAO,uBAAsB,EAAE,KAAK,CAAC;AAAA,MACzD,SAASI;AAAA,IACX,MAAMA,OAAM,GAAG,IAAI,CAAC;AAAA,EACtB,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;;;ACZO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACxC,YAAY,SAAS,OAAO,kBAAkB,SAAS;AACrD,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,UAAU;AAAA,EACjB;AACF;AACO,IAAM,sBAAN,cAAkC,eAAe;AAAA,EACtD,YAAY,SAAS;AACnB,UAAM,iDAAiD,uBAAuB,OAAO;AAAA,EACvF;AACF;AACO,IAAM,sBAAN,cAAkC,eAAe;AAAA,EACtD,YAAY,SAAS;AACnB,UAAM,0CAA0C,uBAAuB,OAAO;AAAA,EAChF;AACF;AACO,IAAM,qBAAN,cAAiC,eAAe;AAAA,EACrD,YAAY,SAAS;AACnB,UAAM,gDAAgD,sBAAsB,OAAO;AAAA,EACrF;AACF;AAEO,IAAI;AAAA,CACV,SAAUC,iBAAgB;AACzB,EAAAA,gBAAe,KAAK,IAAI;AACxB,EAAAA,gBAAe,cAAc,IAAI;AACjC,EAAAA,gBAAe,cAAc,IAAI;AACjC,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,cAAc,IAAI;AACjC,EAAAA,gBAAe,cAAc,IAAI;AACjC,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC9B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;;;ACxC1C,IAAI,YAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAGO,IAAM,kBAAN,MAAsB;AAAA,EAC3B,YAAY,KAAK;AAAA,IACf,UAAU,CAAC;AAAA,IACX;AAAA,IACA,SAAS,eAAe;AAAA,EAC1B,IAAI,CAAC,GAAG;AACN,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,QAAQ,aAAa,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,OAAO;AACb,SAAK,QAAQ,gBAAgB,UAAU,KAAK;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,cAAc,UAAU,CAAC,GAAG;AACjC,QAAI;AACJ,WAAO,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA,MAAM;AAAA,QACR,IAAI;AACJ,YAAI,WAAW,CAAC;AAChB,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AACJ,YAAI,CAAC,QAAQ;AACX,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,UAAU,WAAW,OAAO;AAC9B,mBAAS,UAAU,IAAI;AAAA,QACzB;AACA,YAAI;AACJ,YAAI,iBAAiB,WAAW,CAAC,OAAO,UAAU,eAAe,KAAK,SAAS,cAAc,KAAK,CAAC,UAAU;AAC3G,cAAI,OAAO,SAAS,eAAe,wBAAwB,QAAQ,wBAAwB,aAAa;AAGtG,qBAAS,cAAc,IAAI;AAC3B,mBAAO;AAAA,UACT,WAAW,OAAO,iBAAiB,UAAU;AAE3C,qBAAS,cAAc,IAAI;AAC3B,mBAAO;AAAA,UACT,WAAW,OAAO,aAAa,eAAe,wBAAwB,UAAU;AAG9E,mBAAO;AAAA,UACT,OAAO;AAEL,qBAAS,cAAc,IAAI;AAC3B,mBAAO,KAAK,UAAU,YAAY;AAAA,UACpC;AAAA,QACF;AACA,cAAM,WAAW,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,IAAI,YAAY,IAAI;AAAA,UAC/D,QAAQ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,UAKlB,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,QAAQ,GAAG,KAAK,OAAO,GAAG,OAAO;AAAA,UACxF;AAAA,QACF,CAAC,EAAE,MAAM,gBAAc;AACrB,gBAAM,IAAI,oBAAoB,UAAU;AAAA,QAC1C,CAAC;AACD,cAAM,eAAe,SAAS,QAAQ,IAAI,eAAe;AACzD,YAAI,gBAAgB,iBAAiB,QAAQ;AAC3C,gBAAM,IAAI,oBAAoB,QAAQ;AAAA,QACxC;AACA,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,mBAAmB,QAAQ;AAAA,QACvC;AACA,YAAI,iBAAiB,KAAK,SAAS,QAAQ,IAAI,cAAc,OAAO,QAAQ,OAAO,SAAS,KAAK,cAAc,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK;AAClI,YAAI;AACJ,YAAI,iBAAiB,oBAAoB;AACvC,iBAAO,MAAM,SAAS,KAAK;AAAA,QAC7B,WAAW,iBAAiB,4BAA4B;AACtD,iBAAO,MAAM,SAAS,KAAK;AAAA,QAC7B,WAAW,iBAAiB,qBAAqB;AAC/C,iBAAO;AAAA,QACT,WAAW,iBAAiB,uBAAuB;AACjD,iBAAO,MAAM,SAAS,SAAS;AAAA,QACjC,OAAO;AAEL,iBAAO,MAAM,SAAS,KAAK;AAAA,QAC7B;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,eAAO;AAAA,UACL,MAAM;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;ACxIA,iBAAkB;AAClB,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI,WAAAC;;;ACRG,IAAM,UAAU;;;ACChB,IAAM,kBAAkB;AAAA,EAC7B,iBAAiB,eAAe,OAAO;AACzC;AACO,IAAM,MAAM;AACZ,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAI;AAAA,CACV,SAAUC,gBAAe;AACxB,EAAAA,eAAcA,eAAc,YAAY,IAAI,CAAC,IAAI;AACjD,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3C,EAAAA,eAAcA,eAAc,SAAS,IAAI,CAAC,IAAI;AAC9C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC/C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACjC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AACzB,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,SAAS,IAAI;AAC9B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AACzB,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,MAAM,IAAI;AACzB,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,cAAc,IAAI;AACnC,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AACnC,IAAI;AAAA,CACV,SAAUC,aAAY;AACrB,EAAAA,YAAW,WAAW,IAAI;AAC5B,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,MAAM,IAAI;AAC3B,EAAAA,kBAAiB,SAAS,IAAI;AAC9B,EAAAA,kBAAiB,QAAQ,IAAI;AAC/B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;;;ACvC9C,IAAqB,aAArB,MAAgC;AAAA,EAC9B,cAAc;AACZ,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO,YAAY,UAAU;AAC3B,QAAI,WAAW,gBAAgB,aAAa;AAC1C,aAAO,SAAS,KAAK,cAAc,UAAU,CAAC;AAAA,IAChD;AACA,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,SAAS,KAAK,MAAM,UAAU,CAAC;AAAA,IACxC;AACA,WAAO,SAAS,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,OAAO,IAAI,SAAS,MAAM;AAChC,UAAM,UAAU,IAAI,YAAY;AAChC,WAAO,KAAK,iBAAiB,QAAQ,MAAM,OAAO;AAAA,EACpD;AAAA,EACA,iBAAiB,QAAQ,MAAM,SAAS;AACtC,UAAM,YAAY,KAAK,SAAS,CAAC;AACjC,UAAM,YAAY,KAAK,SAAS,CAAC;AACjC,QAAI,SAAS,KAAK,gBAAgB;AAClC,UAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,aAAS,SAAS;AAClB,UAAM,QAAQ,QAAQ,OAAO,OAAO,MAAM,QAAQ,SAAS,SAAS,CAAC;AACrE,aAAS,SAAS;AAClB,UAAM,OAAO,KAAK,MAAM,QAAQ,OAAO,OAAO,MAAM,QAAQ,OAAO,UAAU,CAAC,CAAC;AAC/E,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AACF;;;ACxBA,IAAqB,QAArB,MAA2B;AAAA,EACzB,YAAY,UAAU,WAAW;AAC/B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ;AACN,SAAK,QAAQ;AACb,iBAAa,KAAK,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,kBAAkB;AAChB,iBAAa,KAAK,KAAK;AACvB,SAAK,QAAQ,WAAW,MAAM;AAC5B,WAAK,QAAQ,KAAK,QAAQ;AAC1B,WAAK,SAAS;AAAA,IAChB,GAAG,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC;AAAA,EACnC;AACF;;;AC5BO,IAAI;AAAA,CACV,SAAUC,gBAAe;AACxB,EAAAA,eAAc,SAAS,IAAI;AAC3B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,WAAW,IAAI;AAC7B,EAAAA,eAAc,QAAQ,IAAI;AAC1B,EAAAA,eAAc,QAAQ,IAAI;AAC1B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,WAAW,IAAI;AAC7B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,WAAW,IAAI;AAC7B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,OAAO,IAAI;AACzB,EAAAA,eAAc,OAAO,IAAI;AACzB,EAAAA,eAAc,SAAS,IAAI;AAC3B,EAAAA,eAAc,KAAK,IAAI;AACvB,EAAAA,eAAc,SAAS,IAAI;AAC3B,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,MAAM,IAAI;AACxB,EAAAA,eAAc,WAAW,IAAI;AAC7B,EAAAA,eAAc,aAAa,IAAI;AAC/B,EAAAA,eAAc,QAAQ,IAAI;AAC1B,EAAAA,eAAc,SAAS,IAAI;AAC3B,EAAAA,eAAc,WAAW,IAAI;AAC/B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAajC,IAAM,oBAAoB,CAAC,SAAS,QAAQ,UAAU,CAAC,MAAM;AAClE,MAAI;AACJ,QAAM,aAAa,KAAK,QAAQ,eAAe,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC7E,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,YAAY;AAClD,QAAI,OAAO,IAAI,cAAc,SAAS,SAAS,QAAQ,SAAS;AAChE,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAeO,IAAM,gBAAgB,CAAC,YAAY,SAAS,QAAQ,cAAc;AACvE,QAAM,SAAS,QAAQ,KAAK,OAAK,EAAE,SAAS,UAAU;AACtD,QAAM,UAAU,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvE,QAAM,QAAQ,OAAO,UAAU;AAC/B,MAAI,WAAW,CAAC,UAAU,SAAS,OAAO,GAAG;AAC3C,WAAO,YAAY,SAAS,KAAK;AAAA,EACnC;AACA,SAAO,KAAK,KAAK;AACnB;AAcO,IAAM,cAAc,CAAC,MAAM,UAAU;AAE1C,MAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AAC1B,UAAM,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AAC1C,WAAO,QAAQ,OAAO,QAAQ;AAAA,EAChC;AAEA,UAAQ,MAAM;AAAA,IACZ,KAAK,cAAc;AACjB,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AACjB,aAAO,SAAS,KAAK;AAAA,IACvB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AACjB,aAAO,OAAO,KAAK;AAAA,IACrB,KAAK,cAAc;AACjB,aAAO,kBAAkB,KAAK;AAAA;AAAA,IAEhC,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA;AAAA,IACnB,KAAK,cAAc;AAAA,IACnB,KAAK,cAAc;AACjB,aAAO,KAAK,KAAK;AAAA,IACnB;AAEE,aAAO,KAAK,KAAK;AAAA,EACrB;AACF;AACA,IAAM,OAAO,WAAS;AACpB,SAAO;AACT;AACO,IAAM,YAAY,WAAS;AAChC,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACO,IAAM,WAAW,WAAS;AAC/B,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,cAAc,WAAW,KAAK;AACpC,QAAI,CAAC,OAAO,MAAM,WAAW,GAAG;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,SAAS,WAAS;AAC7B,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI;AACF,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB,SAAS,OAAO;AACd,cAAQ,IAAI,qBAAqB,KAAK,EAAE;AACxC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAWO,IAAM,UAAU,CAAC,OAAO,SAAS;AACtC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,UAAU,MAAM,SAAS;AAC/B,QAAM,aAAa,MAAM,OAAO;AAChC,QAAM,YAAY,MAAM,CAAC;AAEzB,MAAI,cAAc,OAAO,eAAe,KAAK;AAC3C,QAAI;AACJ,UAAM,UAAU,MAAM,MAAM,GAAG,OAAO;AAEtC,QAAI;AACF,YAAM,KAAK,MAAM,MAAM,UAAU,GAAG;AAAA,IACtC,SAAS,GAAG;AAEV,YAAM,UAAU,QAAQ,MAAM,GAAG,IAAI,CAAC;AAAA,IACxC;AACA,WAAO,IAAI,IAAI,SAAO,YAAY,MAAM,GAAG,CAAC;AAAA,EAC9C;AACA,SAAO;AACT;AAQO,IAAM,oBAAoB,WAAS;AACxC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,MAAM,QAAQ,KAAK,GAAG;AAAA,EAC/B;AACA,SAAO;AACT;AACO,IAAM,kBAAkB,eAAa;AAC1C,MAAI,MAAM;AACV,QAAM,IAAI,QAAQ,QAAQ,MAAM;AAChC,QAAM,IAAI,QAAQ,mDAAmD,EAAE;AACvE,SAAO,IAAI,QAAQ,QAAQ,EAAE;AAC/B;;;ACrNA,IAAqB,OAArB,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxB,YAAY,SAAS,OAAO,UAAU,CAAC,GAAG,UAAU,iBAAiB;AACnE,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,WAAW,CAAC;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO,SAAS;AACd,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,OAAO;AACL,QAAI,KAAK,aAAa,SAAS,GAAG;AAChC;AAAA,IACF;AACA,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,QAAQ,OAAO,KAAK;AAAA,MACvB,OAAO,KAAK,QAAQ;AAAA,MACpB,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,KAAK,KAAK;AAAA,MACV,UAAU,KAAK,QAAQ,SAAS;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,SAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,OAAO;AAAA,EACvE;AAAA,EACA,QAAQ,QAAQ,UAAU;AACxB,QAAI;AACJ,QAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,gBAAU,KAAK,KAAK,kBAAkB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAAA,IACpF;AACA,SAAK,SAAS,KAAK;AAAA,MACjB;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,QAAI,KAAK,cAAc;AACrB;AAAA,IACF;AACA,SAAK,MAAM,KAAK,QAAQ,OAAO,SAAS;AACxC,SAAK,WAAW,KAAK,QAAQ,gBAAgB,KAAK,GAAG;AACrD,UAAM,WAAW,aAAW;AAC1B,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,eAAe;AACpB,WAAK,cAAc,OAAO;AAAA,IAC5B;AACA,SAAK,QAAQ,IAAI,KAAK,UAAU,CAAC,GAAG,QAAQ;AAC5C,SAAK,eAAe,WAAW,MAAM;AACnC,WAAK,QAAQ,WAAW,CAAC,CAAC;AAAA,IAC5B,GAAG,KAAK,OAAO;AAAA,EACjB;AAAA,EACA,QAAQ,QAAQ,UAAU;AACxB,QAAI,KAAK,SAAU,MAAK,QAAQ,SAAS,KAAK,UAAU;AAAA,MACtD;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,kBAAkB;AAChB,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,QAAQ,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,iBAAiB;AACf,iBAAa,KAAK,YAAY;AAC9B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,SAAS,OAAO,OAAK,EAAE,WAAW,MAAM,EAAE,QAAQ,OAAK,EAAE,SAAS,QAAQ,CAAC;AAAA,EAClF;AAAA,EACA,aAAa,QAAQ;AACnB,WAAO,KAAK,gBAAgB,KAAK,aAAa,WAAW;AAAA,EAC3D;AACF;;;ACrGO,IAAI;AAAA,CACV,SAAUC,kCAAiC;AAC1C,EAAAA,iCAAgC,MAAM,IAAI;AAC1C,EAAAA,iCAAgC,MAAM,IAAI;AAC1C,EAAAA,iCAAgC,OAAO,IAAI;AAC7C,GAAG,oCAAoC,kCAAkC,CAAC,EAAE;AAC5E,IAAqB,mBAArB,MAAqB,kBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQpC,YAAY,SAAS,MAAM;AACzB,SAAK,UAAU;AACf,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe,CAAC;AACrB,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,MACZ,QAAQ,MAAM;AAAA,MAAC;AAAA,MACf,SAAS,MAAM;AAAA,MAAC;AAAA,MAChB,QAAQ,MAAM;AAAA,MAAC;AAAA,IACjB;AACA,UAAM,UAAU,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,WAAW;AAAA,MAC1E,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AACA,SAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,GAAG,cAAY;AAC7C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,WAAK,UAAU,KAAK,QAAQ,SAAS;AACrC,WAAK,QAAQ,kBAAiB,UAAU,KAAK,OAAO,UAAU,QAAQ,OAAO;AAC7E,WAAK,aAAa,QAAQ,UAAQ;AAChC,aAAK,QAAQ,kBAAiB,SAAS,KAAK,OAAO,MAAM,QAAQ,OAAO;AAAA,MAC1E,CAAC;AACD,WAAK,eAAe,CAAC;AACrB,aAAO;AAAA,IACT,CAAC;AACD,SAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,GAAG,UAAQ;AACxC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,KAAK,mBAAmB,GAAG;AAC7B,aAAK,aAAa,KAAK,IAAI;AAAA,MAC7B,OAAO;AACL,aAAK,QAAQ,kBAAiB,SAAS,KAAK,OAAO,MAAM,QAAQ,OAAO;AACxE,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,OAAO,CAAC,KAAK,kBAAkB,iBAAiB;AACnD,WAAK,QAAQ,SAAS,YAAY;AAAA,QAChC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,QAAQ,CAAC,KAAK,kBAAkB,kBAAkB;AACrD,WAAK,QAAQ,SAAS,YAAY;AAAA,QAChC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,OAAO,MAAM;AAChB,WAAK,QAAQ,SAAS,YAAY;AAAA,QAChC,OAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,UAAU,cAAc,UAAU,QAAQ,SAAS;AACxD,UAAM,QAAQ,KAAK,UAAU,YAAY;AACzC,UAAM,mBAAmB,KAAK,eAAe,QAAQ;AACrD,UAAM,QAAQ,CAAC;AACf,UAAM,SAAS,CAAC;AAChB,SAAK,IAAI,OAAO,CAAC,KAAK,cAAc;AAClC,UAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF,CAAC;AACD,SAAK,IAAI,kBAAkB,CAAC,KAAK,iBAAiB;AAChD,YAAM,mBAAmB,MAAM,GAAG;AAClC,UAAI,kBAAkB;AACpB,cAAM,kBAAkB,aAAa,IAAI,OAAK,EAAE,YAAY;AAC5D,cAAM,kBAAkB,iBAAiB,IAAI,OAAK,EAAE,YAAY;AAChE,cAAM,kBAAkB,aAAa,OAAO,OAAK,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAC5F,cAAM,gBAAgB,iBAAiB,OAAO,OAAK,gBAAgB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAC9F,YAAI,gBAAgB,SAAS,GAAG;AAC9B,gBAAM,GAAG,IAAI;AAAA,QACf;AACA,YAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,GAAG,IAAI;AAAA,QAChB;AAAA,MACF,OAAO;AACL,cAAM,GAAG,IAAI;AAAA,MACf;AAAA,IACF,CAAC;AACD,WAAO,KAAK,SAAS,OAAO;AAAA,MAC1B;AAAA,MACA;AAAA,IACF,GAAG,QAAQ,OAAO;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,SAAS,OAAO,MAAM,QAAQ,SAAS;AAC5C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAAA,MACF,OAAO,KAAK,eAAe,KAAK,KAAK;AAAA,MACrC,QAAQ,KAAK,eAAe,KAAK,MAAM;AAAA,IACzC;AACA,QAAI,CAAC,QAAQ;AACX,eAAS,MAAM;AAAA,MAAC;AAAA,IAClB;AACA,QAAI,CAAC,SAAS;AACZ,gBAAU,MAAM;AAAA,MAAC;AAAA,IACnB;AACA,SAAK,IAAI,OAAO,CAAC,KAAK,iBAAiB;AACrC,UAAI;AACJ,YAAM,oBAAoB,KAAK,MAAM,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC7E,YAAM,GAAG,IAAI,KAAK,UAAU,YAAY;AACxC,UAAI,iBAAiB,SAAS,GAAG;AAC/B,cAAM,qBAAqB,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,YAAY;AAC7D,cAAM,eAAe,iBAAiB,OAAO,OAAK,mBAAmB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAChG,cAAM,GAAG,EAAE,QAAQ,GAAG,YAAY;AAAA,MACpC;AACA,aAAO,KAAK,kBAAkB,YAAY;AAAA,IAC5C,CAAC;AACD,SAAK,IAAI,QAAQ,CAAC,KAAK,kBAAkB;AACvC,UAAI,mBAAmB,MAAM,GAAG;AAChC,UAAI,CAAC,iBAAkB;AACvB,YAAM,uBAAuB,cAAc,IAAI,OAAK,EAAE,YAAY;AAClE,yBAAmB,iBAAiB,OAAO,OAAK,qBAAqB,QAAQ,EAAE,YAAY,IAAI,CAAC;AAChG,YAAM,GAAG,IAAI;AACb,cAAQ,KAAK,kBAAkB,aAAa;AAC5C,UAAI,iBAAiB,WAAW,EAAG,QAAO,MAAM,GAAG;AAAA,IACrD,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,IAAI,KAAK,MAAM;AACpB,WAAO,OAAO,oBAAoB,GAAG,EAAE,IAAI,SAAO,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,OAAO,eAAe,OAAO;AAC3B,YAAQ,KAAK,UAAU,KAAK;AAC5B,WAAO,OAAO,oBAAoB,KAAK,EAAE,OAAO,CAAC,UAAU,QAAQ;AACjE,YAAM,YAAY,MAAM,GAAG;AAC3B,UAAI,WAAW,WAAW;AACxB,iBAAS,GAAG,IAAI,UAAU,MAAM,IAAI,cAAY;AAC9C,mBAAS,cAAc,IAAI,SAAS,SAAS;AAC7C,iBAAO,SAAS,SAAS;AACzB,iBAAO,SAAS,cAAc;AAC9B,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,GAAG,IAAI;AAAA,MAClB;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AAAA;AAAA,EAEA,OAAO,UAAU,KAAK;AACpB,WAAO,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC;AAAA,EACvC;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA,EAEA,QAAQ,UAAU;AAChB,SAAK,OAAO,UAAU;AAAA,EACxB;AAAA;AAAA,EAEA,OAAO,UAAU;AACf,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,CAAC,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ,SAAS;AAAA,EACjE;AACF;;;ACnOO,IAAI;AAAA,CACV,SAAUC,yCAAwC;AACjD,EAAAA,wCAAuC,KAAK,IAAI;AAChD,EAAAA,wCAAuC,QAAQ,IAAI;AACnD,EAAAA,wCAAuC,QAAQ,IAAI;AACnD,EAAAA,wCAAuC,QAAQ,IAAI;AACrD,GAAG,2CAA2C,yCAAyC,CAAC,EAAE;AACnF,IAAI;AAAA,CACV,SAAUC,wBAAuB;AAChC,EAAAA,uBAAsB,WAAW,IAAI;AACrC,EAAAA,uBAAsB,UAAU,IAAI;AACpC,EAAAA,uBAAsB,kBAAkB,IAAI;AAC5C,EAAAA,uBAAsB,QAAQ,IAAI;AACpC,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AACjD,IAAI;AAAA,CACV,SAAUC,4BAA2B;AACpC,EAAAA,2BAA0B,YAAY,IAAI;AAC1C,EAAAA,2BAA0B,WAAW,IAAI;AACzC,EAAAA,2BAA0B,QAAQ,IAAI;AACtC,EAAAA,2BAA0B,eAAe,IAAI;AAC/C,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AACzD,IAAM,0BAA0B;AAMvC,IAAqB,kBAArB,MAAqB,iBAAgB;AAAA,EACnC,YACA,OAAO,SAAS;AAAA,IACd,QAAQ,CAAC;AAAA,EACX,GAAG,QAAQ;AACT,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ,eAAe;AAC5B,SAAK,aAAa;AAClB,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,MAAM,QAAQ,eAAe,EAAE;AAC/C,SAAK,OAAO,SAAS,OAAO,OAAO;AAAA,MACjC,WAAW;AAAA,QACT,KAAK;AAAA,QACL,MAAM;AAAA,MACR;AAAA,MACA,UAAU;AAAA,QACR,KAAK;AAAA,MACP;AAAA,MACA,SAAS;AAAA,IACX,GAAG,OAAO,MAAM;AAChB,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,WAAW,IAAI,KAAK,MAAM,eAAe,MAAM,KAAK,QAAQ,KAAK,OAAO;AAC7E,SAAK,cAAc,IAAI,MAAM,MAAM,KAAK,sBAAsB,GAAG,KAAK,OAAO,gBAAgB;AAC7F,SAAK,SAAS,QAAQ,MAAM,MAAM;AAChC,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,MAAM;AACvB,WAAK,WAAW,QAAQ,eAAa,UAAU,KAAK,CAAC;AACrD,WAAK,aAAa,CAAC;AAAA,IACrB,CAAC;AACD,SAAK,SAAS,MAAM;AAClB,WAAK,YAAY,MAAM;AACvB,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,KAAK,SAAS,CAAC,EAAE;AACnE,WAAK,QAAQ,eAAe;AAC5B,WAAK,OAAO,QAAQ,IAAI;AAAA,IAC1B,CAAC;AACD,SAAK,SAAS,YAAU;AACtB,UAAI,KAAK,WAAW,KAAK,KAAK,UAAU,GAAG;AACzC;AAAA,MACF;AACA,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,IAAI,MAAM;AACxD,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,gBAAgB;AAAA,IACnC,CAAC;AACD,SAAK,SAAS,QAAQ,WAAW,MAAM;AACrC,UAAI,CAAC,KAAK,WAAW,GAAG;AACtB;AAAA,MACF;AACA,WAAK,OAAO,IAAI,WAAW,WAAW,KAAK,KAAK,IAAI,KAAK,SAAS,OAAO;AACzE,WAAK,QAAQ,eAAe;AAC5B,WAAK,YAAY,gBAAgB;AAAA,IACnC,CAAC;AACD,SAAK,IAAI,eAAe,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ;AACnD,WAAK,SAAS,KAAK,gBAAgB,GAAG,GAAG,OAAO;AAAA,IAClD,CAAC;AACD,SAAK,WAAW,IAAI,iBAAiB,IAAI;AACzC,SAAK,uBAAuB,gBAAgB,KAAK,OAAO,QAAQ,IAAI;AACpE,SAAK,UAAU,KAAK,OAAO,OAAO,WAAW;AAAA,EAC/C;AAAA;AAAA,EAEA,UAAU,UAAU,UAAU,KAAK,SAAS;AAC1C,QAAI,IAAI;AACR,QAAI,CAAC,KAAK,OAAO,YAAY,GAAG;AAC9B,WAAK,OAAO,QAAQ;AAAA,IACtB;AACA,QAAI,KAAK,YAAY;AACnB,YAAM;AAAA,IACR,OAAO;AACL,YAAM;AAAA,QACJ,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA,SAAS;AAAA,QACX;AAAA,MACF,IAAI,KAAK;AACT,WAAK,SAAS,OAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,eAAe,CAAC,CAAC;AAC3H,WAAK,SAAS,MAAM,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,MAAM,CAAC;AAClH,YAAM,qBAAqB,CAAC;AAC5B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA,mBAAmB,MAAM,KAAK,KAAK,SAAS,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,IAAI,OAAK,EAAE,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,QAC5J,SAAS;AAAA,MACX;AACA,UAAI,KAAK,OAAO,kBAAkB;AAChC,2BAAmB,eAAe,KAAK,OAAO;AAAA,MAChD;AACA,WAAK,kBAAkB,OAAO,OAAO;AAAA,QACnC;AAAA,MACF,GAAG,kBAAkB,CAAC;AACtB,WAAK,aAAa;AAClB,WAAK,QAAQ,OAAO;AACpB,WAAK,SAAS,QAAQ,MAAM,CAAO,OAE7B,eAF6B,KAE7B,WAF6B;AAAA,QACjC;AAAA,MACF,GAAM;AACJ,YAAIC;AACJ,aAAK,OAAO,QAAQ;AACpB,YAAI,qBAAqB,QAAW;AAClC,uBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,UAAU;AACjG;AAAA,QACF,OAAO;AACL,gBAAM,yBAAyB,KAAK,SAAS;AAC7C,gBAAM,eAAeA,MAAK,2BAA2B,QAAQ,2BAA2B,SAAS,SAAS,uBAAuB,YAAY,QAAQA,QAAO,SAASA,MAAK;AAC1K,gBAAM,sBAAsB,CAAC;AAC7B,mBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,kBAAM,wBAAwB,uBAAuB,CAAC;AACtD,kBAAM;AAAA,cACJ,QAAQ;AAAA,gBACN;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF,IAAI;AACJ,kBAAM,uBAAuB,oBAAoB,iBAAiB,CAAC;AACnE,gBAAI,wBAAwB,qBAAqB,UAAU,SAAS,qBAAqB,WAAW,UAAU,qBAAqB,UAAU,SAAS,qBAAqB,WAAW,QAAQ;AAC5L,kCAAoB,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,qBAAqB,GAAG;AAAA,gBAC/E,IAAI,qBAAqB;AAAA,cAC3B,CAAC,CAAC;AAAA,YACJ,OAAO;AACL,mBAAK,YAAY;AACjB,2BAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,eAAe,IAAI,MAAM,kEAAkE,CAAC;AACnL;AAAA,YACF;AAAA,UACF;AACA,eAAK,SAAS,mBAAmB;AACjC,sBAAY,SAAS,0BAA0B,UAAU;AACzD;AAAA,QACF;AAAA,MACF,EAAC,EAAE,QAAQ,SAAS,WAAS;AAC3B,qBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,eAAe,IAAI,MAAM,KAAK,UAAU,OAAO,OAAO,KAAK,EAAE,KAAK,IAAI,KAAK,OAAO,CAAC,CAAC;AAC3K;AAAA,MACF,CAAC,EAAE,QAAQ,WAAW,MAAM;AAC1B,qBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,SAAS;AAChG;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACM,MAAM,IAAoB;AAAA,+CAApB,SAAS,OAAO,CAAC,GAAG;AAC9B,aAAO,MAAM,KAAK,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,MACF,GAAG,KAAK,WAAW,KAAK,OAAO;AAAA,IACjC;AAAA;AAAA,EACM,UAAmB;AAAA,+CAAX,OAAO,CAAC,GAAG;AACvB,aAAO,MAAM,KAAK,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,OAAO;AAAA,MACT,GAAG,IAAI;AAAA,IACT;AAAA;AAAA,EACA,GAAG,MAAM,QAAQ,UAAU;AACzB,WAAO,KAAK,IAAI,MAAM,QAAQ,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,KAAK,IAAiB;AAAA,+CAAjB,MAAM,OAAO,CAAC,GAAG;AAC1B,UAAI,IAAI;AACR,UAAI,CAAC,KAAK,SAAS,KAAK,KAAK,SAAS,aAAa;AACjD,cAAM;AAAA,UACJ;AAAA,UACA,SAAS;AAAA,QACX,IAAI;AACJ,cAAM,gBAAgB,KAAK,OAAO,mBAAmB,UAAU,KAAK,OAAO,gBAAgB,KAAK;AAChG,cAAM,UAAU;AAAA,UACd,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,eAAe;AAAA,YACf,QAAQ,KAAK,OAAO,SAAS,KAAK,OAAO,SAAS;AAAA,YAClD,gBAAgB;AAAA,UAClB;AAAA,UACA,MAAM,KAAK,UAAU;AAAA,YACnB,UAAU,CAAC;AAAA,cACT,OAAO,KAAK;AAAA,cACZ;AAAA,cACA,SAAS;AAAA,cACT,SAAS,KAAK;AAAA,YAChB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,kBAAkB,KAAK,sBAAsB,UAAU,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,KAAK,KAAK,OAAO;AACnJ,iBAAQ,KAAK,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC3E,iBAAO,SAAS,KAAK,OAAO;AAAA,QAC9B,SAAS,OAAO;AACd,cAAI,MAAM,SAAS,cAAc;AAC/B,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,IAAI,QAAQ,aAAW;AAC5B,cAAIA,KAAIC,KAAI;AACZ,gBAAM,OAAO,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,WAAW,KAAK,OAAO;AACrE,cAAI,KAAK,SAAS,eAAe,GAAG,MAAMA,OAAMD,MAAK,KAAK,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,QAAQC,QAAO,SAAS,SAASA,IAAG,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AACjN,oBAAQ,IAAI;AAAA,UACd;AACA,eAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI,CAAC;AACtC,eAAK,QAAQ,SAAS,MAAM,QAAQ,OAAO,CAAC;AAC5C,eAAK,QAAQ,WAAW,MAAM,QAAQ,WAAW,CAAC;AAAA,QACpD,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,EACA,kBAAkB,SAAS;AACzB,SAAK,SAAS,cAAc,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,UAAU,KAAK,SAAS;AAClC,SAAK,QAAQ,eAAe;AAC5B,UAAM,UAAU,MAAM;AACpB,WAAK,OAAO,IAAI,WAAW,SAAS,KAAK,KAAK,EAAE;AAChD,WAAK,SAAS,eAAe,OAAO,SAAS,KAAK,SAAS,CAAC;AAAA,IAC9D;AACA,SAAK,YAAY,MAAM;AAEvB,SAAK,SAAS,QAAQ;AACtB,WAAO,IAAI,QAAQ,aAAW;AAC5B,YAAM,YAAY,IAAI,KAAK,MAAM,eAAe,OAAO,CAAC,GAAG,OAAO;AAClE,gBAAU,QAAQ,MAAM,MAAM;AAC5B,gBAAQ;AACR,gBAAQ,IAAI;AAAA,MACd,CAAC,EAAE,QAAQ,WAAW,MAAM;AAC1B,gBAAQ;AACR,gBAAQ,WAAW;AAAA,MACrB,CAAC,EAAE,QAAQ,SAAS,MAAM;AACxB,gBAAQ,OAAO;AAAA,MACjB,CAAC;AACD,gBAAU,KAAK;AACf,UAAI,CAAC,KAAK,SAAS,GAAG;AACpB,kBAAU,QAAQ,MAAM,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEM,kBAAkB,KAAK,SAAS,SAAS;AAAA;AAC7C,YAAM,aAAa,IAAI,gBAAgB;AACvC,YAAM,KAAK,WAAW,MAAM,WAAW,MAAM,GAAG,OAAO;AACvD,YAAM,WAAW,MAAM,KAAK,OAAO,MAAM,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,QACtF,QAAQ,WAAW;AAAA,MACrB,CAAC,CAAC;AACF,mBAAa,EAAE;AACf,aAAO;AAAA,IACT;AAAA;AAAA;AAAA,EAEA,MAAM,OAAO,SAAS,UAAU,KAAK,SAAS;AAC5C,QAAI,CAAC,KAAK,YAAY;AACpB,YAAM,kBAAkB,KAAK,SAAS,KAAK,KAAK;AAAA,IAClD;AACA,QAAI,YAAY,IAAI,KAAK,MAAM,OAAO,SAAS,OAAO;AACtD,QAAI,KAAK,SAAS,GAAG;AACnB,gBAAU,KAAK;AAAA,IACjB,OAAO;AACL,gBAAU,aAAa;AACvB,WAAK,WAAW,KAAK,SAAS;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,QAAQ,SAAS,MAAM;AAChC,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU,OAAO;AACf,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA,EAEA,SAAS,MAAM,SAAS,KAAK;AAC3B,QAAI,IAAI;AACR,UAAM,YAAY,KAAK,kBAAkB;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,CAAC,OAAO,OAAO,OAAO,IAAI;AACzC,QAAI,OAAO,OAAO,QAAQ,SAAS,KAAK,KAAK,QAAQ,KAAK,SAAS,GAAG;AACpE;AAAA,IACF;AACA,QAAI,iBAAiB,KAAK,WAAW,WAAW,SAAS,GAAG;AAC5D,QAAI,WAAW,CAAC,gBAAgB;AAC9B,YAAM;AAAA,IACR;AACA,QAAI,CAAC,UAAU,UAAU,QAAQ,EAAE,SAAS,SAAS,GAAG;AACtD,OAAC,KAAK,KAAK,SAAS,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,UAAQ;AAC3F,YAAID,KAAIC,KAAI;AACZ,iBAASD,MAAK,KAAK,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,SAAS,MAAMC,MAAK,KAAK,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,OAAO;AAAA,MACjO,CAAC,EAAE,IAAI,UAAQ,KAAK,SAAS,gBAAgB,GAAG,CAAC;AAAA,IACnD,OAAO;AACL,OAAC,KAAK,KAAK,SAAS,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,UAAQ;AACrF,YAAID,KAAIC,KAAI,IAAI,IAAI,IAAI;AACxB,YAAI,CAAC,aAAa,YAAY,kBAAkB,EAAE,SAAS,SAAS,GAAG;AACrE,cAAI,QAAQ,MAAM;AAChB,kBAAM,SAAS,KAAK;AACpB,kBAAM,aAAaD,MAAK,KAAK,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG;AAC7E,mBAAO,YAAYC,MAAK,QAAQ,SAAS,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,MAAM,OAAO,cAAc,QAAQ,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,SAAS,KAAK,QAAQ,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,kBAAkB;AAAA,UACxS,OAAO;AACL,kBAAM,aAAa,MAAM,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB;AAChM,mBAAO,cAAc,OAAO,gBAAgB,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB;AAAA,UAC9K;AAAA,QACF,OAAO;AACL,iBAAO,KAAK,KAAK,kBAAkB,MAAM;AAAA,QAC3C;AAAA,MACF,CAAC,EAAE,IAAI,UAAQ;AACb,YAAI,OAAO,mBAAmB,YAAY,SAAS,gBAAgB;AACjE,gBAAM,kBAAkB,eAAe;AACvC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA,MAAAC;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,kBAAkB;AAAA,YACtB;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAWA;AAAA,YACX,KAAK,CAAC;AAAA,YACN,KAAK,CAAC;AAAA,YACN;AAAA,UACF;AACA,2BAAiB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,eAAe,GAAG,KAAK,mBAAmB,eAAe,CAAC;AAAA,QAC7G;AACA,aAAK,SAAS,gBAAgB,GAAG;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,UAAU,eAAe;AAAA,EACvC;AAAA;AAAA,EAEA,gBAAgB,KAAK;AACnB,WAAO,cAAc,GAAG;AAAA,EAC1B;AAAA;AAAA,EAEA,IAAI,MAAM,QAAQ,UAAU;AAC1B,UAAM,YAAY,KAAK,kBAAkB;AACzC,UAAM,UAAU;AAAA,MACd,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AACA,QAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,WAAK,SAAS,SAAS,EAAE,KAAK,OAAO;AAAA,IACvC,OAAO;AACL,WAAK,SAAS,SAAS,IAAI,CAAC,OAAO;AAAA,IACrC;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,KAAK,MAAM,QAAQ;AACjB,UAAM,YAAY,KAAK,kBAAkB;AACzC,SAAK,SAAS,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,OAAO,UAAQ;AACjE,UAAI;AACJ,aAAO,IAAI,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,OAAO,aAAa,iBAAgB,QAAQ,KAAK,QAAQ,MAAM;AAAA,IACtJ,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,OAAO,QAAQ,MAAM,MAAM;AACzB,QAAI,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACzD,aAAO;AAAA,IACT;AACA,eAAW,KAAK,MAAM;AACpB,UAAI,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,YAAY,gBAAgB;AACjC,QAAI,KAAK,OAAO,YAAY,GAAG;AAC7B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,UAAU;AACjB,SAAK,IAAI,eAAe,OAAO,CAAC,GAAG,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,UAAU;AACjB,SAAK,IAAI,eAAe,OAAO,CAAC,GAAG,YAAU,SAAS,MAAM,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,OAAO,YAAY,KAAK,KAAK,UAAU;AAAA,EACrD;AAAA;AAAA,EAEA,QAAQ,UAAU,KAAK,SAAS;AAC9B,QAAI,KAAK,WAAW,GAAG;AACrB;AAAA,IACF;AACA,SAAK,OAAO,gBAAgB,KAAK,KAAK;AACtC,SAAK,QAAQ,eAAe;AAC5B,SAAK,SAAS,OAAO,OAAO;AAAA,EAC9B;AAAA;AAAA,EAEA,mBAAmB,SAAS;AAC1B,UAAM,UAAU;AAAA,MACd,KAAK,CAAC;AAAA,MACN,KAAK,CAAC;AAAA,IACR;AACA,QAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,cAAQ,MAAmB,kBAAkB,QAAQ,SAAS,QAAQ,MAAM;AAAA,IAC9E;AACA,QAAI,QAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAC1D,cAAQ,MAAmB,kBAAkB,QAAQ,SAAS,QAAQ,UAAU;AAAA,IAClF;AACA,WAAO;AAAA,EACT;AACF;;;ACnfA,IAAMC,QAAO,MAAM;AAAC;AACpB,IAAM,6BAA6B,OAAO,cAAc;AACxD,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAMtB,IAAqB,iBAArB,MAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBlC,YAAY,UAAU,SAAS;AAC7B,QAAI;AACJ,SAAK,mBAAmB;AACxB,SAAK,SAAS;AACd,SAAK,WAAW,CAAC;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,SAAS,CAAC;AACf,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,SAASA;AACd,SAAK,OAAO;AACZ,SAAK,aAAa,CAAC;AACnB,SAAK,aAAa,IAAI,WAAW;AACjC,SAAK,uBAAuB;AAAA,MAC1B,MAAM,CAAC;AAAA,MACP,OAAO,CAAC;AAAA,MACR,OAAO,CAAC;AAAA,MACR,SAAS,CAAC;AAAA,IACZ;AACA,SAAK,cAAc;AAMnB,SAAK,gBAAgB,iBAAe;AAClC,UAAI;AACJ,UAAI,aAAa;AACf,iBAAS;AAAA,MACX,WAAW,OAAO,UAAU,aAAa;AACvC,iBAAS,IAAI,SAAS,OAAO,uBAAsB,EAAE,KAAK,CAAC;AAAA,UACzD,SAASC;AAAA,QACX,MAAMA,OAAM,GAAG,IAAI,CAAC;AAAA,MACtB,OAAO;AACL,iBAAS;AAAA,MACX;AACA,aAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AAAA,IACpC;AACA,SAAK,WAAW,GAAG,QAAQ,IAAI,WAAW,SAAS;AACnD,SAAK,eAAe,gBAAgB,QAAQ;AAC5C,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW;AACvE,WAAK,YAAY,QAAQ;AAAA,IAC3B,OAAO;AACL,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAQ,MAAK,SAAS,QAAQ;AAC5F,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAS,MAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,QAAQ,OAAO;AACpJ,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAS,MAAK,UAAU,QAAQ;AAC9F,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAQ,MAAK,SAAS,QAAQ;AAC5F,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAqB,MAAK,sBAAsB,QAAQ;AACtH,UAAM,oBAAoB,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG;AACzI,QAAI,kBAAkB;AACpB,WAAK,mBAAmB;AACxB,WAAK,SAAS;AAAA,IAChB;AACA,SAAK,oBAAoB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAoB,QAAQ,mBAAmB,WAAS;AACzI,aAAO,CAAC,KAAM,KAAM,KAAM,GAAK,EAAE,QAAQ,CAAC,KAAK;AAAA,IACjD;AACA,SAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,SAAS,CAAC,SAAS,aAAa;AACzH,aAAO,SAAS,KAAK,UAAU,OAAO,CAAC;AAAA,IACzC;AACA,SAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,SAAS,KAAK,WAAW,OAAO,KAAK,KAAK,UAAU;AAC/I,SAAK,iBAAiB,IAAI,MAAM,MAAY;AAC1C,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACf,IAAG,KAAK,gBAAgB;AACxB,SAAK,QAAQ,KAAK,cAAc,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK;AAC/F,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AACpE,UAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACnD,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC/C;AACA,WAAK,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW;AACpF,WAAK,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,IAC7E;AACA,SAAK,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,QAAI,KAAK,MAAM;AACb;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,KAAK,UAAU,KAAK,YAAY,GAAG,QAAW;AAAA,QAC5D,SAAS,KAAK;AAAA,MAChB,CAAC;AACD;AAAA,IACF;AACA,QAAI,4BAA4B;AAC9B,WAAK,OAAO,IAAI,UAAU,KAAK,YAAY,CAAC;AAC5C,WAAK,gBAAgB;AACrB;AAAA,IACF;AACA,SAAK,OAAO,IAAI,iBAAiB,KAAK,YAAY,GAAG,QAAW;AAAA,MAC9D,OAAO,MAAM;AACX,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO,uBAAI,EAAE,KAAK,CAAC;AAAA,MACjB,SAAS;AAAA,IACX,MAAM;AACJ,WAAK,OAAO,IAAI,GAAG,KAAK,YAAY,GAAG,QAAW;AAAA,QAChD,SAAS,KAAK;AAAA,MAChB,CAAC;AACD,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK,cAAc,KAAK,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,QAAQ;AAAA,MACtE,KAAK;AAAA,IACP,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM,QAAQ;AACvB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,UAAU,WAAY;AAAA,MAAC;AACjC,UAAI,MAAM;AACR,aAAK,KAAK,MAAM,MAAM,WAAW,QAAQ,WAAW,SAAS,SAAS,EAAE;AAAA,MAC1E,OAAO;AACL,aAAK,KAAK,MAAM;AAAA,MAClB;AACA,WAAK,OAAO;AAEZ,WAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,WAAK,eAAe,MAAM;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,cAAc,SAAS;AAAA;AAC3B,YAAM,SAAS,MAAM,QAAQ,YAAY;AACzC,UAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,aAAK,WAAW;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,oBAAoB;AAAA;AACxB,YAAM,WAAW,MAAM,QAAQ,IAAI,KAAK,SAAS,IAAI,aAAW,QAAQ,YAAY,CAAC,CAAC;AACtF,WAAK,WAAW;AAChB,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,KAAK,MAAM;AACnB,SAAK,OAAO,MAAM,KAAK,IAAI;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,YAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY;AAAA,MACzC,KAAK,cAAc;AACjB,eAAO,iBAAiB;AAAA,MAC1B,KAAK,cAAc;AACjB,eAAO,iBAAiB;AAAA,MAC1B,KAAK,cAAc;AACjB,eAAO,iBAAiB;AAAA,MAC1B;AACE,eAAO,iBAAiB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK,gBAAgB,MAAM,iBAAiB;AAAA,EACrD;AAAA,EACA,QAAQ,OAAO,SAAS;AAAA,IACtB,QAAQ,CAAC;AAAA,EACX,GAAG;AACD,UAAM,OAAO,IAAI,gBAAgB,YAAY,KAAK,IAAI,QAAQ,IAAI;AAClE,SAAK,SAAS,KAAK,IAAI;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,MAAM;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,MAAM;AACrB,WAAK,OAAO,MAAM,YAAU;AAC1B,YAAI;AACJ,SAAC,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,MAAM;AAAA,MACtE,CAAC;AAAA,IACH;AACA,SAAK,IAAI,QAAQ,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO;AACtD,QAAI,KAAK,YAAY,GAAG;AACtB,eAAS;AAAA,IACX,OAAO;AACL,WAAK,WAAW,KAAK,QAAQ;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,QAAQ,QAAQ,MAAM;AAAA;AAC1B,UAAI,cAAc,SAAS,KAAK,gBAAgB,MAAM,KAAK,YAAY,MAAM,KAAK;AAClF,UAAI,aAAa;AACf,YAAI,SAAS;AACb,YAAI;AACF,mBAAS,KAAK,MAAM,KAAK,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,QACrD,SAAS,QAAQ;AAAA,QAAC;AAClB,YAAI,UAAU,OAAO,KAAK;AACxB,cAAI,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AACtC,cAAI,QAAQ,MAAM,OAAO,MAAM;AAC/B,cAAI,CAAC,OAAO;AACV,iBAAK,IAAI,QAAQ,iEAAiE,OAAO,GAAG,EAAE;AAC9F,mBAAO,QAAQ,OAAO,iEAAiE,OAAO,GAAG,EAAE;AAAA,UACrG;AAAA,QACF;AACA,aAAK,mBAAmB;AACxB,aAAK,SAAS,QAAQ,aAAW;AAC/B,yBAAe,QAAQ,kBAAkB;AAAA,YACvC,cAAc;AAAA,UAChB,CAAC;AACD,cAAI,QAAQ,cAAc,QAAQ,UAAU,GAAG;AAC7C,oBAAQ,MAAM,eAAe,cAAc;AAAA,cACzC,cAAc;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,gBAAgB;AAAA;AACpB,UAAI;AACJ,UAAI,CAAC,KAAK,YAAY,GAAG;AACvB;AAAA,MACF;AACA,UAAI,KAAK,qBAAqB;AAC5B,aAAK,sBAAsB;AAC3B,aAAK,IAAI,aAAa,0DAA0D;AAChF,SAAC,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,iBAAiB,kBAAkB;AAClG;AAAA,MACF;AACA,WAAK,sBAAsB,KAAK,SAAS;AACzC,WAAK,KAAK;AAAA,QACR,OAAO;AAAA,QACP,OAAO;AAAA,QACP,SAAS,CAAC;AAAA,QACV,KAAK,KAAK;AAAA,MACZ,CAAC;AACD,WAAK,QAAQ;AAAA,IACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,QAAI,KAAK,YAAY,KAAK,KAAK,WAAW,SAAS,GAAG;AACpD,WAAK,WAAW,QAAQ,cAAY,SAAS,CAAC;AAC9C,WAAK,aAAa,CAAC;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI,SAAS,KAAK,MAAM;AACxB,QAAI,WAAW,KAAK,KAAK;AACvB,WAAK,MAAM;AAAA,IACb,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AACA,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACrB,QAAI,aAAa,KAAK,SAAS,KAAK,OAAK,EAAE,UAAU,UAAU,EAAE,UAAU,KAAK,EAAE,WAAW,EAAE;AAC/F,QAAI,YAAY;AACd,WAAK,IAAI,aAAa,4BAA4B,KAAK,GAAG;AAC1D,iBAAW,YAAY;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,SAAS;AACf,SAAK,WAAW,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,MAAM,QAAQ,SAAS,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,aAAa;AACvB,WAAK,KAAK,SAAS,MAAM,KAAK,YAAY;AAC1C,WAAK,KAAK,UAAU,WAAS,KAAK,aAAa,KAAK;AACpD,WAAK,KAAK,YAAY,WAAS,KAAK,eAAe,KAAK;AACxD,WAAK,KAAK,UAAU,WAAS,KAAK,aAAa,KAAK;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,YAAY;AACzB,SAAK,OAAO,WAAW,MAAM,SAAO;AAClC,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAI,OAAO,QAAQ,KAAK,qBAAqB;AAC3C,aAAK,sBAAsB;AAAA,MAC7B;AACA,WAAK,IAAI,WAAW,GAAG,QAAQ,UAAU,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE,IAAI,OAAO;AACxG,WAAK,SAAS,OAAO,aAAW,QAAQ,UAAU,KAAK,CAAC,EAAE,QAAQ,aAAW,QAAQ,SAAS,OAAO,SAAS,GAAG,CAAC;AAClH,WAAK,qBAAqB,QAAQ,QAAQ,cAAY,SAAS,GAAG,CAAC;AAAA,IACrE,CAAC;AAAA,EACH;AAAA;AAAA,EAEM,cAAc;AAAA;AAClB,WAAK,IAAI,aAAa,gBAAgB,KAAK,YAAY,CAAC,EAAE;AAC1D,WAAK,gBAAgB;AACrB,WAAK,eAAe,MAAM;AAC1B,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,aAAK,iBAAiB,YAAY,MAAM,KAAK,cAAc,GAAG,KAAK,mBAAmB;AAAA,MACxF,OAAO;AACL,YAAI,KAAK,WAAW;AAClB,eAAK,IAAI,UAAU,4BAA4B,KAAK,SAAS,EAAE;AAAA,QACjE,OAAO;AACL,eAAK,IAAI,UAAU,yBAAyB;AAAA,QAC9C;AACA,cAAM,YAAY,KAAK,iBAAiB,KAAK,SAAS;AACtD,aAAK,YAAY,IAAI,OAAO,SAAS;AACrC,aAAK,UAAU,UAAU,WAAS;AAChC,eAAK,IAAI,UAAU,gBAAgB,MAAM,OAAO;AAChD,eAAK,UAAU,UAAU;AAAA,QAC3B;AACA,aAAK,UAAU,YAAY,WAAS;AAClC,cAAI,MAAM,KAAK,UAAU,aAAa;AACpC,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AACA,aAAK,UAAU,YAAY;AAAA,UACzB,OAAO;AAAA,UACP,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH;AACA,WAAK,qBAAqB,KAAK,QAAQ,cAAY,SAAS,CAAC;AAAA,IAC/D;AAAA;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,SAAK,IAAI,aAAa,SAAS,KAAK;AACpC,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,cAAc,KAAK,cAAc;AACxD,SAAK,eAAe,gBAAgB;AACpC,SAAK,qBAAqB,MAAM,QAAQ,cAAY,SAAS,KAAK,CAAC;AAAA,EACrE;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,SAAK,IAAI,aAAa,MAAM,OAAO;AACnC,SAAK,kBAAkB;AACvB,SAAK,qBAAqB,MAAM,QAAQ,cAAY,SAAS,KAAK,CAAC;AAAA,EACrE;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,SAAS,QAAQ,aAAW,QAAQ,SAAS,eAAe,KAAK,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,cAAc,KAAK,QAAQ;AACzB,QAAI,OAAO,KAAK,MAAM,EAAE,WAAW,GAAG;AACpC,aAAO;AAAA,IACT;AACA,UAAM,SAAS,IAAI,MAAM,IAAI,IAAI,MAAM;AACvC,UAAM,QAAQ,IAAI,gBAAgB,MAAM;AACxC,WAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK;AAAA,EAChC;AAAA,EACA,iBAAiB,KAAK;AACpB,QAAI;AACJ,QAAI,KAAK;AACP,mBAAa;AAAA,IACf,OAAO;AACL,YAAM,OAAO,IAAI,KAAK,CAAC,aAAa,GAAG;AAAA,QACrC,MAAM;AAAA,MACR,CAAC;AACD,mBAAa,IAAI,gBAAgB,IAAI;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,SAAS,YAAY,SAAS;AACxC,SAAK,aAAa;AAClB,SAAK,UAAU,MAAM;AAAA,IAAC;AACtB,SAAK,UAAU,MAAM;AAAA,IAAC;AACtB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,SAAS,MAAM;AAAA,IAAC;AACrB,SAAK,aAAa,cAAc;AAChC,SAAK,OAAO,MAAM;AAAA,IAAC;AACnB,SAAK,MAAM;AACX,SAAK,MAAM;AACX,SAAK,QAAQ,QAAQ;AAAA,EACvB;AACF;;;ACheO,IAAM,eAAN,cAA2B,MAAM;AAAA,EACtC,YAAY,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,mBAAmB;AACxB,SAAK,OAAO;AAAA,EACd;AACF;AACO,SAAS,eAAe,OAAO;AACpC,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,sBAAsB;AAC9E;AACO,IAAM,kBAAN,cAA8B,aAAa;AAAA,EAChD,YAAY,SAAS,QAAQ;AAC3B,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACO,IAAM,sBAAN,cAAkC,aAAa;AAAA,EACpD,YAAY,SAAS,eAAe;AAClC,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACvB;AACF;;;AC9BA,IAAIC,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AACO,IAAMC,gBAAe,iBAAe;AACzC,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;AAAA,EACX,WAAW,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SAAS,OAAO,uBAAsB,EAAE,KAAK,CAAC;AAAA,MACzD,SAASC;AAAA,IACX,MAAMA,OAAM,GAAG,IAAI,CAAC;AAAA,EACtB,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;AACO,IAAM,kBAAkB,MAAMF,WAAU,QAAQ,QAAQ,QAAQ,aAAa;AAClF,MAAI,OAAO,aAAa,aAAa;AAEnC,YAAQ,MAAM,OAAO,uBAAsB,GAAG;AAAA,EAChD;AACA,SAAO;AACT,CAAC;AACM,IAAM,mBAAmB,UAAQ;AACtC,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,QAAM,iBAAiB,EAAE,CAAC;AAAA,EAC5C,WAAW,OAAO,SAAS,cAAc,SAAS,OAAO,IAAI,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,QAAM,SAAS,CAAC;AAChB,SAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,UAAM,SAAS,IAAI,QAAQ,iBAAiB,OAAK,EAAE,YAAY,EAAE,QAAQ,SAAS,EAAE,CAAC;AACrF,WAAO,MAAM,IAAI,iBAAiB,KAAK;AAAA,EACzC,CAAC;AACD,SAAO;AACT;;;AC3DA,IAAIG,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAGA,IAAM,mBAAmB,SAAO,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG;AAClH,IAAM,cAAc,CAAC,OAAO,QAAQ,YAAYA,WAAU,QAAQ,QAAQ,QAAQ,aAAa;AAC7F,QAAM,MAAM,MAAM,gBAAgB;AAClC,MAAI,iBAAiB,OAAO,EAAE,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,gBAAgB;AACtG,UAAM,KAAK,EAAE,KAAK,SAAO;AACvB,aAAO,IAAI,gBAAgB,iBAAiB,GAAG,GAAG,MAAM,UAAU,GAAG,CAAC;AAAA,IACxE,CAAC,EAAE,MAAM,SAAO;AACd,aAAO,IAAI,oBAAoB,iBAAiB,GAAG,GAAG,GAAG,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH,OAAO;AACL,WAAO,IAAI,oBAAoB,iBAAiB,KAAK,GAAG,KAAK,CAAC;AAAA,EAChE;AACF,CAAC;AACD,IAAM,oBAAoB,CAAC,QAAQ,SAAS,YAAY,SAAS;AAC/D,QAAM,SAAS;AAAA,IACb;AAAA,IACA,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,CAAC;AAAA,EACnF;AACA,MAAI,WAAW,OAAO;AACpB,WAAO;AAAA,EACT;AACA,SAAO,UAAU,OAAO,OAAO;AAAA,IAC7B,gBAAgB;AAAA,EAClB,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACpE,MAAI,MAAM;AACR,WAAO,OAAO,KAAK,UAAU,IAAI;AAAA,EACnC;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,UAAU;AAC5D;AACA,SAAS,eAAe,SAAS,QAAQ,KAAK,SAAS,YAAY,MAAM;AACvE,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAQ,KAAK,kBAAkB,QAAQ,SAAS,YAAY,IAAI,CAAC,EAAE,KAAK,YAAU;AAChF,YAAI,CAAC,OAAO,GAAI,OAAM;AACtB,YAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAe,QAAO;AACpF,eAAO,OAAO,KAAK;AAAA,MACrB,CAAC,EAAE,KAAK,UAAQ,QAAQ,IAAI,CAAC,EAAE,MAAM,WAAS,YAAY,OAAO,QAAQ,OAAO,CAAC;AAAA,IACnF,CAAC;AAAA,EACH,CAAC;AACH;AACO,SAAS,IAAI,SAAS,KAAK,SAAS,YAAY;AACrD,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,eAAe,SAAS,OAAO,KAAK,SAAS,UAAU;AAAA,EAChE,CAAC;AACH;AACO,SAAS,KAAK,SAAS,KAAK,MAAM,SAAS,YAAY;AAC5D,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,eAAe,SAAS,QAAQ,KAAK,SAAS,YAAY,IAAI;AAAA,EACvE,CAAC;AACH;AACO,SAAS,IAAI,SAAS,KAAK,MAAM,SAAS,YAAY;AAC3D,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,eAAe,SAAS,OAAO,KAAK,SAAS,YAAY,IAAI;AAAA,EACtE,CAAC;AACH;AACO,SAAS,KAAK,SAAS,KAAK,SAAS,YAAY;AACtD,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,eAAe,SAAS,QAAQ,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,MACpF,eAAe;AAAA,IACjB,CAAC,GAAG,UAAU;AAAA,EAChB,CAAC;AACH;AACO,SAAS,OAAO,SAAS,KAAK,MAAM,SAAS,YAAY;AAC9D,SAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,WAAO,eAAe,SAAS,UAAU,KAAK,SAAS,YAAY,IAAI;AAAA,EACzE,CAAC;AACH;;;AC/FA,IAAIC,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAIA,IAAM,yBAAyB;AAAA,EAC7B,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,EACT;AACF;AACA,IAAM,uBAAuB;AAAA,EAC3B,cAAc;AAAA,EACd,aAAa;AAAA,EACb,QAAQ;AACV;AACA,IAAqB,iBAArB,MAAoC;AAAA,EAClC,YAAY,KAAK,UAAU,CAAC,GAAG,UAAUC,QAAO;AAC9C,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,QAAQC,cAAaD,MAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,QAAQ,MAAM,UAAU,aAAa;AAClD,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,YAAI;AACJ,cAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,oBAAoB,GAAG,WAAW;AAClF,YAAI,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,WAAW,UAAU;AAAA,UAChF,YAAY,OAAO,QAAQ,MAAM;AAAA,QACnC,CAAC;AACD,cAAM,WAAW,QAAQ;AACzB,YAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,iBAAO,IAAI,SAAS;AACpB,eAAK,OAAO,gBAAgB,QAAQ,YAAY;AAChD,cAAI,UAAU;AACZ,iBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;AAAA,UACvD;AACA,eAAK,OAAO,IAAI,QAAQ;AAAA,QAC1B,WAAW,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,iBAAO;AACP,eAAK,OAAO,gBAAgB,QAAQ,YAAY;AAChD,cAAI,UAAU;AACZ,iBAAK,OAAO,YAAY,KAAK,eAAe,QAAQ,CAAC;AAAA,UACvD;AAAA,QACF,OAAO;AACL,iBAAO;AACP,kBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,kBAAQ,cAAc,IAAI,QAAQ;AAClC,cAAI,UAAU;AACZ,oBAAQ,YAAY,IAAI,KAAK,SAAS,KAAK,eAAe,QAAQ,CAAC;AAAA,UACrE;AAAA,QACF;AACA,YAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS;AACjF,oBAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,YAAY,OAAO;AAAA,QACzE;AACA,cAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,cAAM,QAAQ,KAAK,cAAc,SAAS;AAC1C,cAAM,MAAM,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG,WAAW,KAAK,IAAI,OAAO,OAAO;AAAA,UACxE;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU;AAAA,UACtE,QAAQ,QAAQ;AAAA,QAClB,IAAI,CAAC,CAAC,CAAC;AACP,cAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAI,IAAI,IAAI;AACV,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,IAAI,KAAK;AAAA,cACT,UAAU,KAAK;AAAA,YACjB;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,gBAAM,QAAQ;AACd,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,UAAU,aAAa;AAClC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,aAAO,KAAK,eAAe,QAAQ,MAAM,UAAU,WAAW;AAAA,IAChE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,MAAM,OAAO,UAAU,aAAa;AACpD,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,YAAM,YAAY,KAAK,oBAAoB,IAAI;AAC/C,YAAM,QAAQ,KAAK,cAAc,SAAS;AAC1C,YAAM,MAAM,IAAI,IAAI,KAAK,MAAM,uBAAuB,KAAK,EAAE;AAC7D,UAAI,aAAa,IAAI,SAAS,KAAK;AACnC,UAAI;AACF,YAAI;AACJ,cAAM,UAAU,OAAO,OAAO;AAAA,UAC5B,QAAQ,qBAAqB;AAAA,QAC/B,GAAG,WAAW;AACd,cAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG;AAAA,UAC7D,YAAY,OAAO,QAAQ,MAAM;AAAA,QACnC,CAAC;AACD,YAAI,OAAO,SAAS,eAAe,oBAAoB,MAAM;AAC3D,iBAAO,IAAI,SAAS;AACpB,eAAK,OAAO,gBAAgB,QAAQ,YAAY;AAChD,eAAK,OAAO,IAAI,QAAQ;AAAA,QAC1B,WAAW,OAAO,aAAa,eAAe,oBAAoB,UAAU;AAC1E,iBAAO;AACP,eAAK,OAAO,gBAAgB,QAAQ,YAAY;AAAA,QAClD,OAAO;AACL,iBAAO;AACP,kBAAQ,eAAe,IAAI,WAAW,QAAQ,YAAY;AAC1D,kBAAQ,cAAc,IAAI,QAAQ;AAAA,QACpC;AACA,cAAM,MAAM,MAAM,KAAK,MAAM,IAAI,SAAS,GAAG;AAAA,UAC3C,QAAQ;AAAA,UACR;AAAA,UACA;AAAA,QACF,CAAC;AACD,cAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAI,IAAI,IAAI;AACV,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,UAAU,KAAK;AAAA,YACjB;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,gBAAM,QAAQ;AACd,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,sBAAsB,MAAM,SAAS;AACnC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,YAAI,QAAQ,KAAK,cAAc,IAAI;AACnC,cAAM,UAAU,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO;AAC9C,YAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AACpE,kBAAQ,UAAU,IAAI;AAAA,QACxB;AACA,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,uBAAuB,KAAK,IAAI,CAAC,GAAG;AAAA,UACjF;AAAA,QACF,CAAC;AACD,cAAM,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,GAAG;AACvC,cAAM,QAAQ,IAAI,aAAa,IAAI,OAAO;AAC1C,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,aAAa,0BAA0B;AAAA,QACnD;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,WAAW,IAAI,SAAS;AAAA,YACxB;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM,UAAU,aAAa;AAClC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,aAAO,KAAK,eAAe,OAAO,MAAM,UAAU,WAAW;AAAA,IAC/D,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,UAAU,QAAQ,SAAS;AAC9B,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB;AAAA,UAC7D,UAAU,KAAK;AAAA,UACf,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,mBAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,QAC/E,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,UAAU,QAAQ,SAAS;AAC9B,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB;AAAA,UAC7D,UAAU,KAAK;AAAA,UACf,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,mBAAmB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,QAC/E,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,MAAM,KAAK;AAAA,UACb;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,MAAM,WAAW,SAAS;AACxC,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,YAAI,QAAQ,KAAK,cAAc,IAAI;AACnC,YAAI,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAI,OAAO,OAAO;AAAA,UAClF;AAAA,QACF,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa;AAAA,UACzE,WAAW,QAAQ;AAAA,QACrB,IAAI,CAAC,CAAC,GAAG;AAAA,UACP,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,cAAM,sBAAsB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAAK;AACrK,cAAM,YAAY,UAAU,GAAG,KAAK,GAAG,GAAG,KAAK,SAAS,GAAG,kBAAkB,EAAE;AAC/E,eAAO;AAAA,UACL;AAAA,QACF;AACA,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,OAAO,WAAW,SAAS;AAC1C,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IAAI;AAAA,UAC9E;AAAA,UACA;AAAA,QACF,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,cAAM,sBAAsB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,aAAa,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAAK;AACrK,eAAO;AAAA,UACL,MAAM,KAAK,IAAI,WAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG;AAAA,YAC9D,WAAW,MAAM,YAAY,UAAU,GAAG,KAAK,GAAG,GAAG,MAAM,SAAS,GAAG,kBAAkB,EAAE,IAAI;AAAA,UACjG,CAAC,CAAC;AAAA,UACF,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,MAAM,SAAS;AACtB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,YAAM,sBAAsB,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe;AAC7G,YAAM,aAAa,sBAAsB,+BAA+B;AACxE,YAAM,sBAAsB,KAAK,4BAA4B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,CAAC,CAAC;AACvI,YAAM,cAAc,sBAAsB,IAAI,mBAAmB,KAAK;AACtE,UAAI;AACF,cAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,cAAM,MAAM,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,IAAI,UAAU,IAAI,KAAK,GAAG,WAAW,IAAI;AAAA,UACpF,SAAS,KAAK;AAAA,UACd,eAAe;AAAA,QACjB,CAAC;AACD,cAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACT,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,YAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,IAAI;AAAA,UACrE,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL,MAAM,iBAAiB,IAAI;AAAA,UAC3B,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM;AACX,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,YAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,UAAI;AACF,cAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,KAAK,IAAI;AAAA,UACpD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,KAAK,iBAAiB,qBAAqB;AACjE,gBAAM,gBAAgB,MAAM;AAC5B,cAAI,CAAC,KAAK,GAAG,EAAE,SAAS,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,MAAM,GAAG;AAC3G,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM,SAAS;AAC1B,UAAM,QAAQ,KAAK,cAAc,IAAI;AACrC,UAAM,eAAe,CAAC;AACtB,UAAM,sBAAsB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,YAAY,QAAQ,aAAa,OAAO,KAAK,QAAQ,QAAQ,KAAK;AACpK,QAAI,uBAAuB,IAAI;AAC7B,mBAAa,KAAK,kBAAkB;AAAA,IACtC;AACA,UAAM,sBAAsB,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe;AAC7G,UAAM,aAAa,sBAAsB,iBAAiB;AAC1D,UAAM,sBAAsB,KAAK,4BAA4B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc,CAAC,CAAC;AACvI,QAAI,wBAAwB,IAAI;AAC9B,mBAAa,KAAK,mBAAmB;AAAA,IACvC;AACA,QAAI,cAAc,aAAa,KAAK,GAAG;AACvC,QAAI,gBAAgB,IAAI;AACtB,oBAAc,IAAI,WAAW;AAAA,IAC/B;AACA,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,WAAW,UAAU,GAAG,KAAK,GAAG,IAAI,UAAU,WAAW,KAAK,GAAG,WAAW,EAAE;AAAA,MAChF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACZ,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,OAAO,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,KAAK,QAAQ,IAAI;AAAA,UAC3E,UAAU;AAAA,QACZ,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgEA,KAAK,MAAM,SAAS,YAAY;AAC9B,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,sBAAsB,GAAG,OAAO,GAAG;AAAA,UAC5F,QAAQ,QAAQ;AAAA,QAClB,CAAC;AACD,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,gBAAgB,KAAK,QAAQ,IAAI,MAAM;AAAA,UACpF,SAAS,KAAK;AAAA,QAChB,GAAG,UAAU;AACb,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,UAAU,QAAQ;AAAA,EAChC;AAAA,EACA,SAAS,MAAM;AACb,QAAI,OAAO,WAAW,aAAa;AACjC,aAAO,OAAO,KAAK,IAAI,EAAE,SAAS,QAAQ;AAAA,IAC5C;AACA,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,cAAc,MAAM;AAClB,WAAO,GAAG,KAAK,QAAQ,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,KAAK,QAAQ,YAAY,EAAE,EAAE,QAAQ,QAAQ,GAAG;AAAA,EACzD;AAAA,EACA,2BAA2B,WAAW;AACpC,UAAM,SAAS,CAAC;AAChB,QAAI,UAAU,OAAO;AACnB,aAAO,KAAK,SAAS,UAAU,KAAK,EAAE;AAAA,IACxC;AACA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;AAAA,IAC1C;AACA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;AAAA,IAC1C;AACA,QAAI,UAAU,QAAQ;AACpB,aAAO,KAAK,UAAU,UAAU,MAAM,EAAE;AAAA,IAC1C;AACA,QAAI,UAAU,SAAS;AACrB,aAAO,KAAK,WAAW,UAAU,OAAO,EAAE;AAAA,IAC5C;AACA,WAAO,OAAO,KAAK,GAAG;AAAA,EACxB;AACF;;;ACxpBO,IAAMG,WAAU;;;ACAhB,IAAMC,mBAAkB;AAAA,EAC7B,iBAAiB,cAAcC,QAAO;AACxC;;;ACHA,IAAIC,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAKA,IAAqB,mBAArB,MAAsC;AAAA,EACpC,YAAY,KAAK,UAAU,CAAC,GAAGC,QAAO;AACpC,SAAK,MAAM;AACX,SAAK,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGC,gBAAe,GAAG,OAAO;AACxE,SAAK,QAAQC,cAAaF,MAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAOD,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW;AAAA,UACvD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,IAAI;AACZ,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,IAAI;AAAA,UAC7D,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,aAAa,IAAI,UAAU;AAAA,IACzB,QAAQ;AAAA,EACV,GAAG;AACD,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW;AAAA,UACxD;AAAA,UACA,MAAM;AAAA,UACN,QAAQ,QAAQ;AAAA,UAChB,iBAAiB,QAAQ;AAAA,UACzB,oBAAoB,QAAQ;AAAA,QAC9B,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,IAAI,SAAS;AACxB,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,IAAI;AAAA,UAC7D;AAAA,UACA,MAAM;AAAA,UACN,QAAQ,QAAQ;AAAA,UAChB,iBAAiB,QAAQ;AAAA,UACzB,oBAAoB,QAAQ;AAAA,QAC9B,GAAG;AAAA,UACD,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,IAAI;AACd,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,UAAU,CAAC,GAAG;AAAA,UACxE,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,IAAI;AACf,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI;AACF,cAAM,OAAO,MAAM,OAAO,KAAK,OAAO,GAAG,KAAK,GAAG,WAAW,EAAE,IAAI,CAAC,GAAG;AAAA,UACpE,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,eAAe,KAAK,GAAG;AACzB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AACF;;;AC3NO,IAAM,gBAAN,cAA4B,iBAAiB;AAAA,EAClD,YAAY,KAAK,UAAU,CAAC,GAAGI,QAAO;AACpC,UAAM,KAAK,SAASA,MAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,IAAI;AACP,WAAO,IAAI,eAAe,KAAK,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,EAClE;AACF;;;ACdO,IAAMC,WAAU;;;ACCvB,IAAI,SAAS;AAEb,IAAI,OAAO,SAAS,aAAa;AAC/B,WAAS;AACX,WAAW,OAAO,aAAa,aAAa;AAC1C,WAAS;AACX,WAAW,OAAO,cAAc,eAAe,UAAU,YAAY,eAAe;AAClF,WAAS;AACX,OAAO;AACL,WAAS;AACX;AACO,IAAMC,mBAAkB;AAAA,EAC7B,iBAAiB,eAAe,MAAM,IAAIC,QAAO;AACnD;AACO,IAAM,yBAAyB;AAAA,EACpC,SAASD;AACX;AACO,IAAM,qBAAqB;AAAA,EAChC,QAAQ;AACV;AACO,IAAM,uBAAuB;AAAA,EAClC,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,UAAU;AACZ;AACO,IAAM,2BAA2B,CAAC;;;ACCzC;AA5BA,IAAIE,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAGO,IAAMC,gBAAe,iBAAe;AACzC,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;AAAA,EACX,WAAW,OAAO,UAAU,aAAa;AACvC,aAAS;AAAA,EACX,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;AACO,IAAM,4BAA4B,MAAM;AAC7C,MAAI,OAAO,YAAY,aAAa;AAClC,WAAOC;AAAA,EACT;AACA,SAAO;AACT;AACO,IAAM,gBAAgB,CAAC,aAAa,gBAAgB,gBAAgB;AACzE,QAAMC,SAAQF,cAAa,WAAW;AACtC,QAAM,qBAAqB,0BAA0B;AACrD,SAAO,CAAC,OAAO,SAASD,WAAU,QAAQ,QAAQ,QAAQ,aAAa;AACrE,QAAI;AACJ,UAAM,eAAe,KAAK,MAAM,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK;AACnF,QAAI,UAAU,IAAI,mBAAmB,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,OAAO;AAC7F,QAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC1B,cAAQ,IAAI,UAAU,WAAW;AAAA,IACnC;AACA,QAAI,CAAC,QAAQ,IAAI,eAAe,GAAG;AACjC,cAAQ,IAAI,iBAAiB,UAAU,WAAW,EAAE;AAAA,IACtD;AACA,WAAOG,OAAM,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,MACzD;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;;;AC/DA,IAAIC,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAQO,SAAS,mBAAmB,KAAK;AACtC,SAAO,IAAI,QAAQ,OAAO,EAAE;AAC9B;AAEO,SAAS,qBAAqB,SAAS,UAAU;AACtD,QAAM;AAAA,IACJ,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM;AAAA,IACJ,IAAIC;AAAA,IACJ,MAAMC;AAAA,IACN,UAAUC;AAAA,IACV,QAAQC;AAAA,EACV,IAAI;AACJ,QAAM,SAAS;AAAA,IACb,IAAI,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGH,mBAAkB,GAAG,SAAS;AAAA,IAClE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGC,qBAAoB,GAAG,WAAW;AAAA,IACxE,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGC,yBAAwB,GAAG,eAAe;AAAA,IACpF,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAGC,uBAAsB,GAAG,aAAa;AAAA,IAC9E,aAAa,MAAMC,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAC9D,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,QAAQ,aAAa;AACvB,WAAO,cAAc,QAAQ;AAAA,EAC/B,OAAO;AAEL,WAAO,OAAO;AAAA,EAChB;AACA,SAAO;AACT;;;ACnEO,SAAS,UAAU,WAAW;AACnC,QAAM,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAC5C,SAAO,UAAU;AACnB;AACO,SAAS,OAAO;AACrB,SAAO,uCAAuC,QAAQ,SAAS,SAAU,GAAG;AAC1E,UAAM,IAAI,KAAK,OAAO,IAAI,KAAK,GAC7B,IAAI,KAAK,MAAM,IAAI,IAAI,IAAM;AAC/B,WAAO,EAAE,SAAS,EAAE;AAAA,EACtB,CAAC;AACH;AACO,IAAM,YAAY,MAAM,OAAO,aAAa;AACnD,IAAM,yBAAyB;AAAA,EAC7B,QAAQ;AAAA,EACR,UAAU;AACZ;AAIO,IAAM,uBAAuB,MAAM;AACxC,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,OAAO,WAAW,iBAAiB,UAAU;AAC/C,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAEV,WAAO;AAAA,EACT;AACA,MAAI,uBAAuB,QAAQ;AACjC,WAAO,uBAAuB;AAAA,EAChC;AACA,QAAM,YAAY,QAAQ,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC;AACvD,MAAI;AACF,eAAW,aAAa,QAAQ,WAAW,SAAS;AACpD,eAAW,aAAa,WAAW,SAAS;AAC5C,2BAAuB,SAAS;AAChC,2BAAuB,WAAW;AAAA,EACpC,SAAS,GAAG;AAGV,2BAAuB,SAAS;AAChC,2BAAuB,WAAW;AAAA,EACpC;AACA,SAAO,uBAAuB;AAChC;AAIO,SAAS,uBAAuB,MAAM;AAC3C,QAAM,SAAS,CAAC;AAChB,QAAM,MAAM,IAAI,IAAI,IAAI;AACxB,MAAI,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK;AACnC,QAAI;AACF,YAAM,mBAAmB,IAAI,gBAAgB,IAAI,KAAK,UAAU,CAAC,CAAC;AAClE,uBAAiB,QAAQ,CAAC,OAAO,QAAQ;AACvC,eAAO,GAAG,IAAI;AAAA,MAChB,CAAC;AAAA,IACH,SAAS,GAAG;AAAA,IAEZ;AAAA,EACF;AAEA,MAAI,aAAa,QAAQ,CAAC,OAAO,QAAQ;AACvC,WAAO,GAAG,IAAI;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AACO,IAAMC,gBAAe,iBAAe;AACzC,MAAI;AACJ,MAAI,aAAa;AACf,aAAS;AAAA,EACX,WAAW,OAAO,UAAU,aAAa;AACvC,aAAS,IAAI,SAAS,OAAO,uBAAsB,EAAE,KAAK,CAAC;AAAA,MACzD,SAASC;AAAA,IACX,MAAMA,OAAM,GAAG,IAAI,CAAC;AAAA,EACtB,OAAO;AACL,aAAS;AAAA,EACX;AACA,SAAO,IAAI,SAAS,OAAO,GAAG,IAAI;AACpC;AACO,IAAM,yBAAyB,mBAAiB;AACrD,SAAO,OAAO,kBAAkB,YAAY,kBAAkB,QAAQ,YAAY,iBAAiB,QAAQ,iBAAiB,UAAU,iBAAiB,OAAO,cAAc,SAAS;AACvL;AAEO,IAAM,eAAe,CAAO,SAAS,KAAK,SAAS;AACxD,QAAM,QAAQ,QAAQ,KAAK,KAAK,UAAU,IAAI,CAAC;AACjD;AACO,IAAM,eAAe,CAAO,SAAS,QAAQ;AAClD,QAAM,QAAQ,MAAM,QAAQ,QAAQ,GAAG;AACvC,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI;AACF,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB,SAAS,IAAI;AACX,WAAO;AAAA,EACT;AACF;AACO,IAAM,kBAAkB,CAAO,SAAS,QAAQ;AACrD,QAAM,QAAQ,WAAW,GAAG;AAC9B;AACO,SAAS,gBAAgB,OAAO;AACrC,QAAM,MAAM;AACZ,MAAI,SAAS;AACb,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,MAAM,MAAM;AACtB,MAAI,IAAI;AACR,UAAQ,MAAM,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,GAAG;AAChD,SAAO,IAAI,MAAM,QAAQ;AACvB,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,IAAI,QAAQ,MAAM,OAAO,GAAG,CAAC;AACpC,WAAO,QAAQ,IAAI,QAAQ;AAC3B,YAAQ,OAAO,OAAO,IAAI,QAAQ;AAClC,YAAQ,OAAO,MAAM,IAAI;AACzB,aAAS,SAAS,OAAO,aAAa,IAAI;AAC1C,QAAI,QAAQ,MAAM,QAAQ,GAAG;AAC3B,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC5C;AACA,QAAI,QAAQ,MAAM,QAAQ,GAAG;AAC3B,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC5C;AAAA,EACF;AACA,SAAO;AACT;AAMO,IAAM,WAAN,MAAM,UAAS;AAAA,EACpB,cAAc;AAEZ;AACA,SAAK,UAAU,IAAI,UAAS,mBAAmB,CAAC,KAAK,QAAQ;AAE3D;AACA,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAChB,CAAC;AAAA,EACH;AACF;AACA,SAAS,qBAAqB;AAEvB,SAAS,iBAAiB,OAAO;AAEtC,QAAM,iBAAiB;AACvB,QAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,IAAI,MAAM,uCAAuC;AAAA,EACzD;AACA,MAAI,CAAC,eAAe,KAAK,MAAM,CAAC,CAAC,GAAG;AAClC,UAAM,IAAI,MAAM,sDAAsD;AAAA,EACxE;AACA,QAAM,YAAY,MAAM,CAAC;AACzB,SAAO,KAAK,MAAM,gBAAgB,SAAS,CAAC;AAC9C;AAIA,SAAsB,MAAM,MAAM;AAAA;AAChC,WAAO,MAAM,IAAI,QAAQ,YAAU;AACjC,iBAAW,MAAM,OAAO,IAAI,GAAG,IAAI;AAAA,IACrC,CAAC;AAAA,EACH;AAAA;AAMO,SAAS,UAAU,IAAI,aAAa;AACzC,QAAM,UAAU,IAAI,QAAQ,CAAC,QAAQ,WAAW;AAE9C;AACA,KAAC,MAAY;AACX,eAAS,UAAU,GAAG,UAAU,UAAU,WAAW;AACnD,YAAI;AACF,gBAAM,SAAS,MAAM,GAAG,OAAO;AAC/B,cAAI,CAAC,YAAY,SAAS,MAAM,MAAM,GAAG;AACvC,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,QACF,SAAS,GAAG;AACV,cAAI,CAAC,YAAY,SAAS,CAAC,GAAG;AAC5B,mBAAO,CAAC;AACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,IAAG;AAAA,EACL,CAAC;AACD,SAAO;AACT;AACA,SAAS,QAAQ,KAAK;AACpB,UAAQ,MAAM,IAAI,SAAS,EAAE,GAAG,OAAO,EAAE;AAC3C;AAEO,SAAS,uBAAuB;AACrC,QAAM,iBAAiB;AACvB,QAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,MAAI,OAAO,WAAW,aAAa;AACjC,UAAM,UAAU;AAChB,UAAM,aAAa,QAAQ;AAC3B,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAY,QAAQ,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,UAAU,CAAC;AAAA,IACnE;AACA,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,KAAK;AAC5B,SAAO,MAAM,KAAK,OAAO,OAAO,EAAE,KAAK,EAAE;AAC3C;AACA,SAAe,OAAO,cAAc;AAAA;AAClC,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,cAAc,QAAQ,OAAO,YAAY;AAC/C,UAAM,OAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW;AAC9D,UAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,WAAO,MAAM,KAAK,KAAK,EAAE,IAAI,OAAK,OAAO,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE;AAAA,EACnE;AAAA;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AAC5E;AACA,SAAsB,sBAAsB,UAAU;AAAA;AACpD,UAAM,mBAAmB,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,eAAe,OAAO,gBAAgB;AACzH,QAAI,CAAC,kBAAkB;AACrB,cAAQ,KAAK,oGAAoG;AACjH,aAAO;AAAA,IACT;AACA,UAAM,SAAS,MAAM,OAAO,QAAQ;AACpC,WAAO,gBAAgB,MAAM;AAAA,EAC/B;AAAA;;;AC1OO,IAAM,YAAN,cAAwB,MAAM;AAAA,EACnC,YAAY,SAAS,QAAQ;AAC3B,UAAM,OAAO;AACb,SAAK,gBAAgB;AACrB,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AACF;AACO,SAAS,YAAY,OAAO;AACjC,SAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,mBAAmB;AAC3E;AACO,IAAM,eAAN,cAA2B,UAAU;AAAA,EAC1C,YAAY,SAAS,QAAQ;AAC3B,UAAM,SAAS,MAAM;AACrB,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACO,SAAS,eAAe,OAAO;AACpC,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;AACO,IAAM,mBAAN,cAA+B,UAAU;AAAA,EAC9C,YAAY,SAAS,eAAe;AAClC,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,gBAAgB;AAAA,EACvB;AACF;AACO,IAAM,kBAAN,cAA8B,UAAU;AAAA,EAC7C,YAAY,SAAS,MAAM,QAAQ;AACjC,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACF;AACO,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EAC3D,cAAc;AACZ,UAAM,yBAAyB,2BAA2B,GAAG;AAAA,EAC/D;AACF;AACO,IAAM,gCAAN,cAA4C,gBAAgB;AAAA,EACjE,cAAc;AACZ,UAAM,gCAAgC,iCAAiC,GAAG;AAAA,EAC5E;AACF;AACO,IAAM,8BAAN,cAA0C,gBAAgB;AAAA,EAC/D,YAAY,SAAS;AACnB,UAAM,SAAS,+BAA+B,GAAG;AAAA,EACnD;AACF;AACO,IAAM,iCAAN,cAA6C,gBAAgB;AAAA,EAClE,YAAY,SAAS,UAAU,MAAM;AACnC,UAAM,SAAS,kCAAkC,GAAG;AACpD,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;AACO,IAAM,iCAAN,cAA6C,gBAAgB;AAAA,EAClE,YAAY,SAAS,UAAU,MAAM;AACnC,UAAM,SAAS,kCAAkC,GAAG;AACpD,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB;AAAA,EACF;AACF;AACO,IAAM,0BAAN,cAAsC,gBAAgB;AAAA,EAC3D,YAAY,SAAS,QAAQ;AAC3B,UAAM,SAAS,2BAA2B,MAAM;AAAA,EAClD;AACF;AACO,SAAS,0BAA0B,OAAO;AAC/C,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;AAMO,IAAM,wBAAN,cAAoC,gBAAgB;AAAA,EACzD,YAAY,SAAS,QAAQ,SAAS;AACpC,UAAM,SAAS,yBAAyB,MAAM;AAC9C,SAAK,UAAU;AAAA,EACjB;AACF;AACO,SAAS,wBAAwB,OAAO;AAC7C,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS;AAC9C;;;ACnHA,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAGA,IAAMC,oBAAmB,SAAO,IAAI,OAAO,IAAI,WAAW,IAAI,qBAAqB,IAAI,SAAS,KAAK,UAAU,GAAG;AAClH,IAAM,sBAAsB,CAAC,KAAK,KAAK,GAAG;AAC1C,SAAeC,aAAY,OAAO;AAAA;AAChC,QAAI,CAAC,uBAAuB,KAAK,GAAG;AAClC,YAAM,IAAI,wBAAwBD,kBAAiB,KAAK,GAAG,CAAC;AAAA,IAC9D;AACA,QAAI,oBAAoB,SAAS,MAAM,MAAM,GAAG;AAE9C,YAAM,IAAI,wBAAwBA,kBAAiB,KAAK,GAAG,MAAM,MAAM;AAAA,IACzE;AACA,QAAI;AACJ,QAAI;AACF,aAAO,MAAM,MAAM,KAAK;AAAA,IAC1B,SAAS,GAAG;AACV,YAAM,IAAI,iBAAiBA,kBAAiB,CAAC,GAAG,CAAC;AAAA,IACnD;AACA,QAAI,OAAO,SAAS,YAAY,QAAQ,OAAO,KAAK,kBAAkB,YAAY,KAAK,iBAAiB,MAAM,QAAQ,KAAK,cAAc,OAAO,KAAK,KAAK,cAAc,QAAQ,UAAU,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAG,MAAM,KAAK,OAAO,MAAM,UAAU,IAAI,GAAG;AACvQ,YAAM,IAAI,sBAAsBA,kBAAiB,IAAI,GAAG,MAAM,QAAQ,KAAK,cAAc,OAAO;AAAA,IAClG;AACA,UAAM,IAAI,aAAaA,kBAAiB,IAAI,GAAG,MAAM,UAAU,GAAG;AAAA,EACpE;AAAA;AACA,IAAME,qBAAoB,CAAC,QAAQ,SAAS,YAAY,SAAS;AAC/D,QAAM,SAAS;AAAA,IACb;AAAA,IACA,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,CAAC;AAAA,EACnF;AACA,MAAI,WAAW,OAAO;AACpB,WAAO;AAAA,EACT;AACA,SAAO,UAAU,OAAO,OAAO;AAAA,IAC7B,gBAAgB;AAAA,EAClB,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACpE,SAAO,OAAO,KAAK,UAAU,IAAI;AACjC,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG,UAAU;AAC5D;AACA,SAAsB,SAAS,SAAS,QAAQ,KAAK,SAAS;AAAA;AAC5D,QAAI;AACJ,UAAM,UAAU,OAAO,OAAO,CAAC,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,OAAO;AACnG,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK;AACjE,cAAQ,eAAe,IAAI,UAAU,QAAQ,GAAG;AAAA,IAClD;AACA,UAAM,MAAM,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK,CAAC;AACpH,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY;AACxE,SAAG,aAAa,IAAI,QAAQ;AAAA,IAC9B;AACA,UAAM,cAAc,OAAO,KAAK,EAAE,EAAE,SAAS,MAAM,IAAI,gBAAgB,EAAE,EAAE,SAAS,IAAI;AACxF,UAAM,OAAO,MAAMC,gBAAe,SAAS,QAAQ,MAAM,aAAa;AAAA,MACpE;AAAA,MACA,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,IAC3E,GAAG,CAAC,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,IAAI;AACrE,YAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,SAAS,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,IAAI,IAAI;AAAA,MACjJ,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI;AAAA,MAC5B,OAAO;AAAA,IACT;AAAA,EACF;AAAA;AACA,SAAeA,gBAAe,SAAS,QAAQ,KAAK,SAAS,YAAY,MAAM;AAAA;AAC7E,UAAM,gBAAgBD,mBAAkB,QAAQ,SAAS,YAAY,IAAI;AACzE,QAAI;AACJ,QAAI;AACF,eAAS,MAAM,QAAQ,KAAK,aAAa;AAAA,IAC3C,SAAS,GAAG;AACV,cAAQ,MAAM,CAAC;AAEf,YAAM,IAAI,wBAAwBF,kBAAiB,CAAC,GAAG,CAAC;AAAA,IAC1D;AACA,QAAI,CAAC,OAAO,IAAI;AACd,YAAMC,aAAY,MAAM;AAAA,IAC1B;AACA,QAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,eAAe;AAC3E,aAAO;AAAA,IACT;AACA,QAAI;AACF,aAAO,MAAM,OAAO,KAAK;AAAA,IAC3B,SAAS,GAAG;AACV,YAAMA,aAAY,CAAC;AAAA,IACrB;AAAA,EACF;AAAA;AACO,SAAS,iBAAiB,MAAM;AACrC,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,WAAW,IAAI,GAAG;AACpB,cAAU,OAAO,OAAO,CAAC,GAAG,IAAI;AAChC,QAAI,CAAC,KAAK,YAAY;AACpB,cAAQ,aAAa,UAAU,KAAK,UAAU;AAAA,IAChD;AAAA,EACF;AACA,QAAM,QAAQ,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,KAAK;AAC/D,SAAO;AAAA,IACL,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT;AACF;AACO,SAAS,yBAAyB,MAAM;AAC7C,QAAM,WAAW,iBAAiB,IAAI;AACtC,MAAI,CAAC,SAAS,SAAS,KAAK,iBAAiB,OAAO,KAAK,kBAAkB,YAAY,MAAM,QAAQ,KAAK,cAAc,OAAO,KAAK,KAAK,cAAc,QAAQ,UAAU,KAAK,cAAc,WAAW,OAAO,KAAK,cAAc,YAAY,YAAY,KAAK,cAAc,QAAQ,OAAO,CAAC,GAAG,MAAM,KAAK,OAAO,MAAM,UAAU,IAAI,GAAG;AACtU,aAAS,KAAK,gBAAgB,KAAK;AAAA,EACrC;AACA,SAAO;AACT;AACO,SAAS,cAAc,MAAM;AAClC,MAAI;AACJ,QAAM,QAAQ,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,KAAK;AAC/D,SAAO;AAAA,IACL,MAAM;AAAA,MACJ;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT;AACF;AACO,SAAS,aAAa,MAAM;AACjC,SAAO;AAAA,IACL;AAAA,IACA,OAAO;AAAA,EACT;AACF;AACO,SAAS,sBAAsB,MAAM;AAC1C,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MACJ,OAAO,OAAO,MAAM,CAAC,eAAe,aAAa,gBAAgB,eAAe,mBAAmB,CAAC;AACtG,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI;AACnC,SAAO;AAAA,IACL,MAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF;AAAA,IACA,OAAO;AAAA,EACT;AACF;AACO,SAAS,uBAAuB,MAAM;AAC3C,SAAO;AACT;AAMA,SAAS,WAAW,MAAM;AACxB,SAAO,KAAK,gBAAgB,KAAK,iBAAiB,KAAK;AACzD;;;ACnKA,IAAIG,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAIA,IAAqB,iBAArB,MAAoC;AAAA,EAClC,YAAY;AAAA,IACV,MAAM;AAAA,IACN,UAAU,CAAC;AAAA,IACX,OAAAC;AAAA,EACF,GAAG;AACD,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,QAAQC,cAAaD,MAAK;AAC/B,SAAK,MAAM;AAAA,MACT,aAAa,KAAK,aAAa,KAAK,IAAI;AAAA,MACxC,cAAc,KAAK,cAAc,KAAK,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,QAAQ,KAAK,QAAQ,UAAU;AAAA;AACnC,UAAI;AACF,cAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,iBAAiB,KAAK,IAAI;AAAA,UACtE,SAAS,KAAK;AAAA,UACd;AAAA,UACA,eAAe;AAAA,QACjB,CAAC;AACD,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,kBAAkB,IAAqB;AAAA,+CAArB,OAAO,UAAU,CAAC,GAAG;AAC3C,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;AAAA,UAC9D,MAAM;AAAA,YACJ;AAAA,YACA,MAAM,QAAQ;AAAA,UAChB;AAAA,UACA,SAAS,KAAK;AAAA,UACd,YAAY,QAAQ;AAAA,UACpB,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,aAAa,QAAQ;AAAA;AACzB,UAAI;AACF,cAAM;AAAA,UACF;AAAA,QACF,IAAI,QACJ,OAAOD,QAAO,QAAQ,CAAC,SAAS,CAAC;AACnC,cAAM,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,OAAO;AAC3D,YAAI,cAAc,MAAM;AAEtB,eAAK,YAAY,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAClE,iBAAO,KAAK,UAAU;AAAA,QACxB;AACA,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,wBAAwB;AAAA,UAC3E;AAAA,UACA,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,UACP,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,QACxE,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,YAAY;AAAA,cACZ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,YAAY;AAAA;AAC3B,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,gBAAgB;AAAA,UACnE,MAAM;AAAA,UACN,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,UAAU,QAAQ;AAAA;AACtB,UAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC5B,UAAI;AACF,cAAM,aAAa;AAAA,UACjB,UAAU;AAAA,UACV,UAAU;AAAA,UACV,OAAO;AAAA,QACT;AACA,cAAM,WAAW,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB;AAAA,UAC5E,SAAS,KAAK;AAAA,UACd,eAAe;AAAA,UACf,OAAO;AAAA,YACL,OAAO,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,YACpK,WAAW,MAAM,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,UAC7K;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AACD,YAAI,SAAS,MAAO,OAAM,SAAS;AACnC,cAAM,QAAQ,MAAM,SAAS,KAAK;AAClC,cAAM,SAAS,KAAK,SAAS,QAAQ,IAAI,eAAe,OAAO,QAAQ,OAAO,SAAS,KAAK;AAC5F,cAAM,SAAS,MAAM,KAAK,SAAS,QAAQ,IAAI,MAAM,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,GAAG,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC9I,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,QAAQ,UAAQ;AACpB,kBAAM,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC;AACtE,kBAAM,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC;AACvD,uBAAW,GAAG,GAAG,MAAM,IAAI;AAAA,UAC7B,CAAC;AACD,qBAAW,QAAQ,SAAS,KAAK;AAAA,QACnC;AACA,eAAO;AAAA,UACL,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,UAAU;AAAA,UACxD,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,OAAO,CAAC;AAAA,YACV;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,YAAY,KAAK;AAAA;AACrB,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;AAAA,UACzE,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,eAAe,KAAK,YAAY;AAAA;AACpC,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,GAAG,IAAI;AAAA,UACzE,MAAM;AAAA,UACN,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUM,WAAW,IAAI,mBAAmB,OAAO;AAAA;AAC7C,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,gBAAgB,EAAE,IAAI;AAAA,UAC3E,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,YACJ,oBAAoB;AAAA,UACtB;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA,EACM,aAAa,QAAQ;AAAA;AACzB,UAAI;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YAAY;AAAA,UACxF,SAAS,KAAK;AAAA,UACd,OAAO,aAAW;AAChB,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ;AAAA,cACF;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA,EACM,cAAc,QAAQ;AAAA;AAC1B,UAAI;AACF,cAAM,OAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,gBAAgB,OAAO,MAAM,YAAY,OAAO,EAAE,IAAI;AAAA,UACjH,SAAS,KAAK;AAAA,QAChB,CAAC;AACD,eAAO;AAAA,UACL;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AACF;;;ACjUO,IAAMG,WAAU;;;ACAhB,IAAM,aAAa;AACnB,IAAM,cAAc;AAEpB,IAAMC,mBAAkB;AAAA,EAC7B,iBAAiB,aAAaC,QAAO;AACvC;AACO,IAAM,gBAAgB;;;ACHtB,IAAM,sBAAsB;AAAA,EACjC,SAAS,SAAO;AACd,QAAI,CAAC,qBAAqB,GAAG;AAC3B,aAAO;AAAA,IACT;AACA,WAAO,WAAW,aAAa,QAAQ,GAAG;AAAA,EAC5C;AAAA,EACA,SAAS,CAAC,KAAK,UAAU;AACvB,QAAI,CAAC,qBAAqB,GAAG;AAC3B;AAAA,IACF;AACA,eAAW,aAAa,QAAQ,KAAK,KAAK;AAAA,EAC5C;AAAA,EACA,YAAY,SAAO;AACjB,QAAI,CAAC,qBAAqB,GAAG;AAC3B;AAAA,IACF;AACA,eAAW,aAAa,WAAW,GAAG;AAAA,EACxC;AACF;AAKO,SAAS,0BAA0B,QAAQ,CAAC,GAAG;AACpD,SAAO;AAAA,IACL,SAAS,SAAO;AACd,aAAO,MAAM,GAAG,KAAK;AAAA,IACvB;AAAA,IACA,SAAS,CAAC,KAAK,UAAU;AACvB,YAAM,GAAG,IAAI;AAAA,IACf;AAAA,IACA,YAAY,SAAO;AACjB,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,EACF;AACF;;;ACrCO,SAAS,qBAAqB;AACnC,MAAI,OAAO,eAAe,SAAU;AACpC,MAAI;AACF,WAAO,eAAe,OAAO,WAAW,aAAa;AAAA,MACnD,KAAK,WAAY;AACf,eAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,IAChB,CAAC;AAED,cAAU,aAAa;AAEvB,WAAO,OAAO,UAAU;AAAA,EAC1B,SAAS,GAAG;AACV,QAAI,OAAO,SAAS,aAAa;AAE/B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;;;AClBO,IAAM,YAAY;AAAA;AAAA;AAAA;AAAA,EAIvB,OAAO,CAAC,EAAE,cAAc,qBAAqB,KAAK,WAAW,gBAAgB,WAAW,aAAa,QAAQ,gCAAgC,MAAM;AACrJ;AACO,IAAM,0BAAN,cAAsC,MAAM;AAAA,EACjD,YAAY,SAAS;AACnB,UAAM,OAAO;AACb,SAAK,mBAAmB;AAAA,EAC1B;AACF;AACO,IAAM,mCAAN,cAA+C,wBAAwB;AAAC;AA4B/E,SAAsB,cAAc,MAAM,gBAAgB,IAAI;AAAA;AAC5D,QAAI,UAAU,OAAO;AACnB,cAAQ,IAAI,oDAAoD,MAAM,cAAc;AAAA,IACtF;AACA,UAAM,kBAAkB,IAAI,WAAW,gBAAgB;AACvD,QAAI,iBAAiB,GAAG;AACtB,iBAAW,MAAM;AACf,wBAAgB,MAAM;AACtB,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,wDAAwD,IAAI;AAAA,QAC1E;AAAA,MACF,GAAG,cAAc;AAAA,IACnB;AAEA,WAAO,MAAM,WAAW,UAAU,MAAM,QAAQ,MAAM,mBAAmB,IAAI;AAAA,MAC3E,MAAM;AAAA,MACN,aAAa;AAAA,IACf,IAAI;AAAA,MACF,MAAM;AAAA,MACN,QAAQ,gBAAgB;AAAA,IAC1B,GAAG,CAAM,SAAQ;AACf,UAAI,MAAM;AACR,YAAI,UAAU,OAAO;AACnB,kBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;AAAA,QAC7E;AACA,YAAI;AACF,iBAAO,MAAM,GAAG;AAAA,QAClB,UAAE;AACA,cAAI,UAAU,OAAO;AACnB,oBAAQ,IAAI,gDAAgD,MAAM,KAAK,IAAI;AAAA,UAC7E;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,mBAAmB,GAAG;AACxB,cAAI,UAAU,OAAO;AACnB,oBAAQ,IAAI,iEAAiE,IAAI;AAAA,UACnF;AACA,gBAAM,IAAI,iCAAiC,sDAAsD,IAAI,sBAAsB;AAAA,QAC7H,OAAO;AACL,cAAI,UAAU,OAAO;AACnB,gBAAI;AACF,oBAAM,SAAS,MAAM,WAAW,UAAU,MAAM,MAAM;AACtD,sBAAQ,IAAI,oDAAoD,KAAK,UAAU,QAAQ,MAAM,IAAI,CAAC;AAAA,YACpG,SAAS,GAAG;AACV,sBAAQ,KAAK,wEAAwE,CAAC;AAAA,YACxF;AAAA,UACF;AAKA,kBAAQ,KAAK,yPAAyP;AACtQ,iBAAO,MAAM,GAAG;AAAA,QAClB;AAAA,MACF;AAAA,IACF,EAAC;AAAA,EACH;AAAA;;;AC3FA,mBAAmB;AACnB,IAAM,kBAAkB;AAAA,EACtB,KAAK;AAAA,EACL,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,SAASC;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AACT;AAEA,IAAM,6BAA6B,KAAK;AAGxC,IAAM,8BAA8B;AACpC,SAAe,SAAS,MAAM,gBAAgB,IAAI;AAAA;AAChD,WAAO,MAAM,GAAG;AAAA,EAClB;AAAA;AACA,IAAqB,eAArB,MAAqB,cAAa;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,SAAS;AACnB,QAAI;AACJ,SAAK,gBAAgB;AACrB,SAAK,sBAAsB,oBAAI,IAAI;AACnC,SAAK,oBAAoB;AACzB,SAAK,4BAA4B;AACjC,SAAK,qBAAqB;AAO1B,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AACpB,SAAK,gBAAgB,CAAC;AAItB,SAAK,mBAAmB;AACxB,SAAK,SAAS,QAAQ;AACtB,SAAK,aAAa,cAAa;AAC/B,kBAAa,kBAAkB;AAC/B,QAAI,KAAK,aAAa,KAAK,UAAU,GAAG;AACtC,cAAQ,KAAK,8MAA8M;AAAA,IAC7N;AACA,UAAM,WAAW,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,eAAe,GAAG,OAAO;AAC1E,SAAK,mBAAmB,CAAC,CAAC,SAAS;AACnC,QAAI,OAAO,SAAS,UAAU,YAAY;AACxC,WAAK,SAAS,SAAS;AAAA,IACzB;AACA,SAAK,iBAAiB,SAAS;AAC/B,SAAK,aAAa,SAAS;AAC3B,SAAK,mBAAmB,SAAS;AACjC,SAAK,QAAQ,IAAI,eAAe;AAAA,MAC9B,KAAK,SAAS;AAAA,MACd,SAAS,SAAS;AAAA,MAClB,OAAO,SAAS;AAAA,IAClB,CAAC;AACD,SAAK,MAAM,SAAS;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,QAAQC,cAAa,SAAS,KAAK;AACxC,SAAK,OAAO,SAAS,QAAQ;AAC7B,SAAK,qBAAqB,SAAS;AACnC,SAAK,WAAW,SAAS;AACzB,SAAK,MAAM;AAAA,MACT,QAAQ,KAAK,QAAQ,KAAK,IAAI;AAAA,MAC9B,QAAQ,KAAK,QAAQ,KAAK,IAAI;AAAA,MAC9B,UAAU,KAAK,UAAU,KAAK,IAAI;AAAA,MAClC,WAAW,KAAK,WAAW,KAAK,IAAI;AAAA,MACpC,aAAa,KAAK,aAAa,KAAK,IAAI;AAAA,MACxC,oBAAoB,KAAK,oBAAoB,KAAK,IAAI;AAAA,MACtD,gCAAgC,KAAK,gCAAgC,KAAK,IAAI;AAAA,IAChF;AACA,QAAI,KAAK,gBAAgB;AACvB,UAAI,SAAS,SAAS;AACpB,aAAK,UAAU,SAAS;AAAA,MAC1B,OAAO;AACL,YAAI,qBAAqB,GAAG;AAC1B,eAAK,UAAU;AAAA,QACjB,OAAO;AACL,eAAK,gBAAgB,CAAC;AACtB,eAAK,UAAU,0BAA0B,KAAK,aAAa;AAAA,QAC7D;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,gBAAgB,CAAC;AACtB,WAAK,UAAU,0BAA0B,KAAK,aAAa;AAAA,IAC7D;AACA,QAAI,UAAU,KAAK,WAAW,oBAAoB,KAAK,kBAAkB,KAAK,YAAY;AACxF,UAAI;AACF,aAAK,mBAAmB,IAAI,WAAW,iBAAiB,KAAK,UAAU;AAAA,MACzE,SAAS,GAAG;AACV,gBAAQ,MAAM,0FAA0F,CAAC;AAAA,MAC3G;AACA,OAAC,KAAK,KAAK,sBAAsB,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,WAAW,CAAM,UAAS;AAC9G,aAAK,OAAO,4DAA4D,KAAK;AAC7E,cAAM,KAAK,sBAAsB,MAAM,KAAK,OAAO,MAAM,KAAK,SAAS,KAAK;AAAA,MAC9E,EAAC;AAAA,IACH;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,UAAU,MAAM;AACd,QAAI,KAAK,kBAAkB;AACzB,WAAK,OAAO,gBAAgB,KAAK,UAAU,KAAKC,QAAO,MAAK,oBAAI,KAAK,GAAE,YAAY,CAAC,IAAI,GAAG,IAAI;AAAA,IACjG;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,aAAa;AAAA;AACjB,UAAI,KAAK,mBAAmB;AAC1B,eAAO,MAAM,KAAK;AAAA,MACpB;AACA,WAAK,qBAAqB,MAAY;AACpC,eAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,iBAAO,MAAM,KAAK,YAAY;AAAA,QAChC,EAAC;AAAA,MACH,IAAG;AACH,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,cAAc;AAAA;AAClB,UAAI;AACF,cAAM,aAAa,UAAU,IAAI,MAAM,KAAK,YAAY,IAAI;AAC5D,aAAK,OAAO,kBAAkB,SAAS,gBAAgB,UAAU;AACjE,YAAI,cAAc,KAAK,sBAAsB,KAAK,qBAAqB,GAAG;AACxE,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,KAAK,mBAAmB,UAAU;AAC5C,cAAI,OAAO;AACT,iBAAK,OAAO,kBAAkB,oCAAoC,KAAK;AAGvE,iBAAK,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,iCAAiC,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,aAAa,8CAA8C;AACpN,qBAAO;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAGA,kBAAM,KAAK,eAAe;AAC1B,mBAAO;AAAA,cACL;AAAA,YACF;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,eAAK,OAAO,kBAAkB,2BAA2B,SAAS,iBAAiB,YAAY;AAC/F,gBAAM,KAAK,aAAa,OAAO;AAC/B,qBAAW,MAAY;AACrB,gBAAI,iBAAiB,YAAY;AAC/B,oBAAM,KAAK,sBAAsB,qBAAqB,OAAO;AAAA,YAC/D,OAAO;AACL,oBAAM,KAAK,sBAAsB,aAAa,OAAO;AAAA,YACvD;AAAA,UACF,IAAG,CAAC;AACJ,iBAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAEA,cAAM,KAAK,mBAAmB;AAC9B,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,OAAO,IAAI,iBAAiB,0CAA0C,KAAK;AAAA,QAC7E;AAAA,MACF,UAAE;AACA,cAAM,KAAK,wBAAwB;AACnC,aAAK,OAAO,kBAAkB,KAAK;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,OAAO,aAAa;AAAA;AACxB,UAAI,IAAI,IAAI;AACZ,UAAI;AACF,cAAM,KAAK,eAAe;AAC1B,YAAI;AACJ,YAAI,WAAW,aAAa;AAC1B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,gBAAgB;AACpB,cAAI,sBAAsB;AAC1B,cAAI,KAAK,aAAa,QAAQ;AAC5B,kBAAM,eAAe,qBAAqB;AAC1C,kBAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,YAAY;AACjF,4BAAgB,MAAM,sBAAsB,YAAY;AACxD,kCAAsB,iBAAiB,gBAAgB,UAAU;AAAA,UACnE;AACA,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;AAAA,YAC7D,SAAS,KAAK;AAAA,YACd,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,YACtE,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,OAAO,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,cAC9G,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,cACA,gBAAgB;AAAA,cAChB,uBAAuB;AAAA,YACzB;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH,WAAW,WAAW,aAAa;AACjC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;AAAA,YAC7D,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,OAAO,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,cAC9G,UAAU,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,cACnH,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,IAAI,4BAA4B,iEAAiE;AAAA,QACzG;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,SAAS,CAAC,MAAM;AAClB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM,UAAU,KAAK;AACrB,cAAM,OAAO,KAAK;AAClB,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,OAAO;AAAA,QACvD;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,mBAAmB,aAAa;AAAA;AACpC,UAAI;AACF,cAAM,KAAK,eAAe;AAC1B,YAAI;AACJ,YAAI,WAAW,aAAa;AAC1B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;AAAA,YAChF,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH,WAAW,WAAW,aAAa;AACjC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;AAAA,YAChF,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,IAAI,4BAA4B,iEAAiE;AAAA,QACzG;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,OAAO;AACT,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF,WAAW,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA,OAAO,IAAI,8BAA8B;AAAA,UAC3C;AAAA,QACF;AACA,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;AAAA,QAC5D;AACA,eAAO;AAAA,UACL,MAAM,OAAO,OAAO;AAAA,YAClB,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,UAChB,GAAG,KAAK,gBAAgB;AAAA,YACtB,cAAc,KAAK;AAAA,UACrB,IAAI,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,gBAAgB,aAAa;AAAA;AACjC,UAAI,IAAI,IAAI,IAAI;AAChB,YAAM,KAAK,eAAe;AAC1B,aAAO,MAAM,KAAK,sBAAsB,YAAY,UAAU;AAAA,QAC5D,aAAa,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,QAC/E,SAAS,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,QAC3E,cAAc,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,QAChF,sBAAsB,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,MAC1F,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,uBAAuB,UAAU;AAAA;AACrC,YAAM,KAAK;AACX,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,eAAO,KAAK,wBAAwB,QAAQ;AAAA,MAC9C,EAAC;AAAA,IACH;AAAA;AAAA,EACM,wBAAwB,UAAU;AAAA;AACtC,YAAM,cAAc,MAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACvF,YAAM,CAAC,cAAc,YAAY,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc,IAAI,MAAM,GAAG;AAClH,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,0BAA0B;AAAA,QAC1E,SAAS,KAAK;AAAA,QACd,MAAM;AAAA,UACJ,WAAW;AAAA,UACX,eAAe;AAAA,QACjB;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AACD,YAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,UAAI,OAAO;AACT,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,SAAS;AAAA,YACT,cAAc;AAAA,UAChB;AAAA,UACA;AAAA,QACF;AAAA,MACF,WAAW,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,MAAM;AAAA,YACN,SAAS;AAAA,YACT,cAAc;AAAA,UAChB;AAAA,UACA,OAAO,IAAI,8BAA8B;AAAA,QAC3C;AAAA,MACF;AACA,UAAI,KAAK,SAAS;AAChB,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;AAAA,MAC5D;AACA,aAAO;AAAA,QACL,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG;AAAA,UAC3C,cAAc,iBAAiB,QAAQ,iBAAiB,SAAS,eAAe;AAAA,QAClF,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,kBAAkB,aAAa;AAAA;AACnC,YAAM,KAAK,eAAe;AAC1B,UAAI;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,cAAM,MAAM,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,8BAA8B;AAAA,UACtF,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,YACJ;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA,sBAAsB;AAAA,cACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,YAC3E;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AACD,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,OAAO;AACT,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF,WAAW,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,MAAM;AAC/C,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA,OAAO,IAAI,8BAA8B;AAAA,UAC3C;AAAA,QACF;AACA,YAAI,KAAK,SAAS;AAChB,gBAAM,KAAK,aAAa,KAAK,OAAO;AACpC,gBAAM,KAAK,sBAAsB,aAAa,KAAK,OAAO;AAAA,QAC5D;AACA,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBM,cAAc,aAAa;AAAA;AAC/B,UAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAI;AACF,cAAM,KAAK,eAAe;AAC1B,YAAI,WAAW,aAAa;AAC1B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,gBAAgB;AACpB,cAAI,sBAAsB;AAC1B,cAAI,KAAK,aAAa,QAAQ;AAC5B,kBAAM,eAAe,qBAAqB;AAC1C,kBAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,YAAY;AACjF,4BAAgB,MAAM,sBAAsB,YAAY;AACxD,kCAAsB,iBAAiB,gBAAgB,UAAU;AAAA,UACnE;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;AAAA,YACxD,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA,OAAO,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,cAC9G,cAAc,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,sBAAsB,QAAQ,OAAO,SAAS,KAAK;AAAA,cAChI,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,cACA,gBAAgB;AAAA,cAChB,uBAAuB;AAAA,YACzB;AAAA,YACA,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,UACxE,CAAC;AACD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,YAAI,WAAW,aAAa;AAC1B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;AAAA,YACxD,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA,OAAO,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,cAC9G,cAAc,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,sBAAsB,QAAQ,OAAO,SAAS,KAAK;AAAA,cAChI,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,cACA,UAAU,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,YACrH;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,YAC9D;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM,IAAI,4BAA4B,mDAAmD;AAAA,MAC3F,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU,QAAQ;AAAA;AACtB,UAAI,IAAI;AACR,UAAI;AACF,YAAI,OAAO,SAAS,kBAAkB,OAAO,SAAS,gBAAgB;AAEpE,gBAAM,KAAK,eAAe;AAAA,QAC5B;AACA,YAAI,aAAa;AACjB,YAAI,eAAe;AACnB,YAAI,aAAa,QAAQ;AACvB,wBAAc,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC3E,0BAAgB,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,QAC/E;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,WAAW;AAAA,UAC3D,SAAS,KAAK;AAAA,UACd,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG;AAAA,YAC7C,sBAAsB;AAAA,cACpB,eAAe;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,UACD;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AACD,YAAI,OAAO;AACT,gBAAM;AAAA,QACR;AACA,YAAI,CAAC,MAAM;AACT,gBAAM,IAAI,MAAM,0CAA0C;AAAA,QAC5D;AACA,cAAM,UAAU,KAAK;AACrB,cAAM,OAAO,KAAK;AAClB,YAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,cAAc;AAC1E,gBAAM,KAAK,aAAa,OAAO;AAC/B,gBAAM,KAAK,sBAAsB,aAAa,OAAO;AAAA,QACvD;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeM,cAAc,QAAQ;AAAA;AAC1B,UAAI,IAAI,IAAI;AACZ,UAAI;AACF,cAAM,KAAK,eAAe;AAC1B,YAAI,gBAAgB;AACpB,YAAI,sBAAsB;AAC1B,YAAI,KAAK,aAAa,QAAQ;AAC5B,gBAAM,eAAe,qBAAqB;AAC1C,gBAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,YAAY;AACjF,0BAAgB,MAAM,sBAAsB,YAAY;AACxD,gCAAsB,iBAAiB,gBAAgB,UAAU;AAAA,QACnE;AACA,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,QAAQ;AAAA,UAC3D,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,SAAS;AAAA,YACvG,aAAa,OAAO;AAAA,UACtB,IAAI,IAAI,GAAG,YAAY,SAAS;AAAA,YAC9B,QAAQ,OAAO;AAAA,UACjB,IAAI,IAAI,GAAG;AAAA,YACT,cAAc,MAAM,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAAA,UAChI,CAAC,KAAK,KAAK,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,gBAAgB;AAAA,YACjI,sBAAsB;AAAA,cACpB,eAAe,OAAO,QAAQ;AAAA,YAChC;AAAA,UACF,IAAI,IAAI,GAAG;AAAA,YACT,oBAAoB;AAAA,YACpB,gBAAgB;AAAA,YAChB,uBAAuB;AAAA,UACzB,CAAC;AAAA,UACD,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,QACT,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,iBAAiB;AAAA;AACrB,YAAM,KAAK;AACX,aAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,eAAO,MAAM,KAAK,gBAAgB;AAAA,MACpC,EAAC;AAAA,IACH;AAAA;AAAA,EACM,kBAAkB;AAAA;AACtB,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,gBAAM;AAAA,YACJ,MAAM;AAAA,cACJ;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT,IAAI;AACJ,cAAI,aAAc,OAAM;AACxB,cAAI,CAAC,QAAS,OAAM,IAAI,wBAAwB;AAChD,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,mBAAmB;AAAA,YAClE,SAAS,KAAK;AAAA,YACd,KAAK,QAAQ;AAAA,UACf,CAAC;AACD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,OAAO,aAAa;AAAA;AACxB,UAAI;AACF,YAAI,YAAY,QAAQ,kBAAkB,YAAY,QAAQ,gBAAgB;AAC5E,gBAAM,KAAK,eAAe;AAAA,QAC5B;AACA,cAAM,WAAW,GAAG,KAAK,GAAG;AAC5B,YAAI,WAAW,aAAa;AAC1B,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;AAAA,YAC/C,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,YACF;AAAA,YACA,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,UACxE,CAAC;AACD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF,WAAW,WAAW,aAAa;AACjC,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,UAAU;AAAA,YAC/C,SAAS,KAAK;AAAA,YACd,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA,sBAAsB;AAAA,gBACpB,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,cAC3E;AAAA,YACF;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,cACT,WAAW,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,YAC9D;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM,IAAI,4BAA4B,6DAA6D;AAAA,MACrG,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,aAAa;AAAA;AACjB,YAAM,KAAK;AACX,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,eAAO,KAAK,YAAY,CAAM,WAAU;AACtC,iBAAO;AAAA,QACT,EAAC;AAAA,MACH,EAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,aAAa,gBAAgB,IAAI;AAAA;AACrC,WAAK,OAAO,iBAAiB,SAAS,cAAc;AACpD,UAAI;AACF,YAAI,KAAK,cAAc;AACrB,gBAAM,OAAO,KAAK,cAAc,SAAS,KAAK,cAAc,KAAK,cAAc,SAAS,CAAC,IAAI,QAAQ,QAAQ;AAC7G,gBAAM,UAAU,MAAY;AAC1B,kBAAM;AACN,mBAAO,MAAM,GAAG;AAAA,UAClB,IAAG;AACH,eAAK,cAAc,MAAM,MAAY;AACnC,gBAAI;AACF,oBAAM;AAAA,YACR,SAAS,GAAG;AAAA,YAEZ;AAAA,UACF,IAAG,CAAC;AACJ,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,KAAK,KAAK,QAAQ,KAAK,UAAU,IAAI,gBAAgB,MAAY;AAC5E,eAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAC7E,cAAI;AACF,iBAAK,eAAe;AACpB,kBAAM,SAAS,GAAG;AAClB,iBAAK,cAAc,MAAM,MAAY;AACnC,kBAAI;AACF,sBAAM;AAAA,cACR,SAAS,GAAG;AAAA,cAEZ;AAAA,YACF,IAAG,CAAC;AACJ,kBAAM;AAEN,mBAAO,KAAK,cAAc,QAAQ;AAChC,oBAAM,SAAS,CAAC,GAAG,KAAK,aAAa;AACrC,oBAAM,QAAQ,IAAI,MAAM;AACxB,mBAAK,cAAc,OAAO,GAAG,OAAO,MAAM;AAAA,YAC5C;AACA,mBAAO,MAAM;AAAA,UACf,UAAE;AACA,iBAAK,OAAO,iBAAiB,iCAAiC,KAAK,UAAU;AAC7E,iBAAK,eAAe;AAAA,UACtB;AAAA,QACF,EAAC;AAAA,MACH,UAAE;AACA,aAAK,OAAO,iBAAiB,KAAK;AAAA,MACpC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,YAAY,IAAI;AAAA;AACpB,WAAK,OAAO,gBAAgB,OAAO;AACnC,UAAI;AAEF,cAAM,SAAS,MAAM,KAAK,cAAc;AACxC,eAAO,MAAM,GAAG,MAAM;AAAA,MACxB,UAAE;AACA,aAAK,OAAO,gBAAgB,KAAK;AAAA,MACnC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,gBAAgB;AAAA;AACpB,WAAK,OAAO,oBAAoB,OAAO;AACvC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,OAAO,oBAAoB,qCAAqC,IAAI,MAAM,EAAE,KAAK;AAAA,MACxF;AACA,UAAI;AACF,YAAI,iBAAiB;AACrB,cAAM,eAAe,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AACrE,aAAK,OAAO,iBAAiB,wBAAwB,YAAY;AACjE,YAAI,iBAAiB,MAAM;AACzB,cAAI,KAAK,gBAAgB,YAAY,GAAG;AACtC,6BAAiB;AAAA,UACnB,OAAO;AACL,iBAAK,OAAO,iBAAiB,mCAAmC;AAChE,kBAAM,KAAK,eAAe;AAAA,UAC5B;AAAA,QACF;AACA,YAAI,CAAC,gBAAgB;AACnB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,YACX;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,aAAa,eAAe,aAAa,eAAe,cAAc,KAAK,IAAI,IAAI,MAAO;AAChG,aAAK,OAAO,oBAAoB,cAAc,aAAa,KAAK,MAAM,YAAY,cAAc,eAAe,UAAU;AACzH,YAAI,CAAC,YAAY;AACf,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,YACX;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAC7D,YAAI,OAAO;AACT,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,UAAE;AACA,aAAK,OAAO,oBAAoB,KAAK;AAAA,MACvC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,QAAQ,KAAK;AAAA;AACjB,UAAI,KAAK;AACP,eAAO,MAAM,KAAK,SAAS,GAAG;AAAA,MAChC;AACA,YAAM,KAAK;AACX,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,eAAO,MAAM,KAAK,SAAS;AAAA,MAC7B,EAAC;AAAA,IACH;AAAA;AAAA,EACM,SAAS,KAAK;AAAA;AAClB,UAAI;AACF,YAAI,KAAK;AACP,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;AAAA,YAC3D,SAAS,KAAK;AAAA,YACd;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI,IAAI;AACR,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,OAAO;AACT,kBAAM;AAAA,UACR;AACA,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;AAAA,YAC3D,SAAS,KAAK;AAAA,YACd,MAAM,MAAM,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,YACtH,OAAO;AAAA,UACT,CAAC;AAAA,QACH,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,WAAW,IAA0B;AAAA,+CAA1B,YAAY,UAAU,CAAC,GAAG;AACzC,YAAM,KAAK;AACX,aAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,eAAO,MAAM,KAAK,YAAY,YAAY,OAAO;AAAA,MACnD,EAAC;AAAA,IACH;AAAA;AAAA,EACM,YAAY,IAA0B;AAAA,+CAA1B,YAAY,UAAU,CAAC,GAAG;AAC1C,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,gBAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,UACT,IAAI;AACJ,cAAI,cAAc;AAChB,kBAAM;AAAA,UACR;AACA,cAAI,CAAC,YAAY,SAAS;AACxB,kBAAM,IAAI,wBAAwB;AAAA,UACpC;AACA,gBAAM,UAAU,YAAY;AAC5B,cAAI,gBAAgB;AACpB,cAAI,sBAAsB;AAC1B,cAAI,KAAK,aAAa,UAAU,WAAW,SAAS,MAAM;AACxD,kBAAM,eAAe,qBAAqB;AAC1C,kBAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,YAAY;AACjF,4BAAgB,MAAM,sBAAsB,YAAY;AACxD,kCAAsB,iBAAiB,gBAAgB,UAAU;AAAA,UACnE;AACA,gBAAM;AAAA,YACJ;AAAA,YACA,OAAO;AAAA,UACT,IAAI,MAAM,SAAS,KAAK,OAAO,OAAO,GAAG,KAAK,GAAG,SAAS;AAAA,YACxD,SAAS,KAAK;AAAA,YACd,YAAY,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAAA,YACtE,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG;AAAA,cACjD,gBAAgB;AAAA,cAChB,uBAAuB;AAAA,YACzB,CAAC;AAAA,YACD,KAAK,QAAQ;AAAA,YACb,OAAO;AAAA,UACT,CAAC;AACD,cAAI,UAAW,OAAM;AACrB,kBAAQ,OAAO,KAAK;AACpB,gBAAM,KAAK,aAAa,OAAO;AAC/B,gBAAM,KAAK,sBAAsB,gBAAgB,OAAO;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM,QAAQ;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK;AACd,WAAO,iBAAiB,GAAG;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,gBAAgB;AAAA;AAC/B,YAAM,KAAK;AACX,aAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,eAAO,MAAM,KAAK,YAAY,cAAc;AAAA,MAC9C,EAAC;AAAA,IACH;AAAA;AAAA,EACM,YAAY,gBAAgB;AAAA;AAChC,UAAI;AACF,YAAI,CAAC,eAAe,gBAAgB,CAAC,eAAe,eAAe;AACjE,gBAAM,IAAI,wBAAwB;AAAA,QACpC;AACA,cAAM,UAAU,KAAK,IAAI,IAAI;AAC7B,YAAIC,aAAY;AAChB,YAAI,aAAa;AACjB,YAAI,UAAU;AACd,cAAM,UAAU,iBAAiB,eAAe,YAAY;AAC5D,YAAI,QAAQ,KAAK;AACf,UAAAA,aAAY,QAAQ;AACpB,uBAAaA,cAAa;AAAA,QAC5B;AACA,YAAI,YAAY;AACd,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT;AAAA,UACF,IAAI,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAC7D,cAAI,OAAO;AACT,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,kBAAkB;AACrB,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AACA,oBAAU;AAAA,QACZ,OAAO;AACL,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,KAAK,SAAS,eAAe,YAAY;AACnD,cAAI,OAAO;AACT,kBAAM;AAAA,UACR;AACA,oBAAU;AAAA,YACR,cAAc,eAAe;AAAA,YAC7B,eAAe,eAAe;AAAA,YAC9B,MAAM,KAAK;AAAA,YACX,YAAY;AAAA,YACZ,YAAYA,aAAY;AAAA,YACxB,YAAYA;AAAA,UACd;AACA,gBAAM,KAAK,aAAa,OAAO;AAC/B,gBAAM,KAAK,sBAAsB,aAAa,OAAO;AAAA,QACvD;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,MAAM,QAAQ;AAAA,YACd;AAAA,UACF;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,eAAe,gBAAgB;AAAA;AACnC,YAAM,KAAK;AACX,aAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,eAAO,MAAM,KAAK,gBAAgB,cAAc;AAAA,MAClD,EAAC;AAAA,IACH;AAAA;AAAA,EACM,gBAAgB,gBAAgB;AAAA;AACpC,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI;AACJ,cAAI,CAAC,gBAAgB;AACnB,kBAAM;AAAA,cACJ;AAAA,cACA,OAAAC;AAAA,YACF,IAAI;AACJ,gBAAIA,QAAO;AACT,oBAAMA;AAAA,YACR;AACA,8BAAkB,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,UACxE;AACA,cAAI,EAAE,mBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,gBAAgB;AACnG,kBAAM,IAAI,wBAAwB;AAAA,UACpC;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAC7D,cAAI,OAAO;AACT,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,cACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,MAAM;AAAA,gBACN,SAAS;AAAA,cACX;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM,QAAQ;AAAA,cACd;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,mBAAmB,YAAY;AAAA;AACnC,UAAI;AACF,YAAI,CAAC,UAAU,EAAG,OAAM,IAAI,+BAA+B,sBAAsB;AACjF,YAAI,KAAK,aAAa,cAAc,CAAC,KAAK,qBAAqB,GAAG;AAChE,gBAAM,IAAI,+BAA+B,sCAAsC;AAAA,QACjF,WAAW,KAAK,YAAY,UAAU,CAAC,YAAY;AACjD,gBAAM,IAAI,+BAA+B,4BAA4B;AAAA,QACvE;AACA,cAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAC1D,YAAI,YAAY;AACd,cAAI,CAAC,OAAO,KAAM,OAAM,IAAI,+BAA+B,mBAAmB;AAC9E,gBAAM;AAAA,YACJ,MAAAC;AAAA,YACA,OAAAD;AAAA,UACF,IAAI,MAAM,KAAK,wBAAwB,OAAO,IAAI;AAClD,cAAIA,OAAO,OAAMA;AACjB,gBAAM,MAAM,IAAI,IAAI,OAAO,SAAS,IAAI;AACxC,cAAI,aAAa,OAAO,MAAM;AAC9B,iBAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,IAAI,IAAI,SAAS,CAAC;AACpE,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAASC,MAAK;AAAA,cACd,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,OAAO,SAAS,OAAO,qBAAqB,OAAO,YAAY;AACjE,gBAAM,IAAI,+BAA+B,OAAO,qBAAqB,mDAAmD;AAAA,YACtH,OAAO,OAAO,SAAS;AAAA,YACvB,MAAM,OAAO,cAAc;AAAA,UAC7B,CAAC;AAAA,QACH;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,iBAAiB,CAAC,YAAY;AACjE,gBAAM,IAAI,+BAA+B,2BAA2B;AAAA,QACtE;AACA,cAAM,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAC5C,cAAM,YAAY,SAAS,UAAU;AACrC,YAAIF,aAAY,UAAU;AAC1B,YAAI,YAAY;AACd,UAAAA,aAAY,SAAS,UAAU;AAAA,QACjC;AACA,cAAM,oBAAoBA,aAAY;AACtC,YAAI,oBAAoB,OAAQ,4BAA4B;AAC1D,kBAAQ,KAAK,iEAAiE,iBAAiB,iCAAiC,SAAS,GAAG;AAAA,QAC9I;AACA,cAAM,WAAWA,aAAY;AAC7B,YAAI,UAAU,YAAY,KAAK;AAC7B,kBAAQ,KAAK,mGAAmG,UAAUA,YAAW,OAAO;AAAA,QAC9I,WAAW,UAAU,WAAW,GAAG;AACjC,kBAAQ,KAAK,+GAA+G,UAAUA,YAAW,OAAO;AAAA,QAC1J;AACA,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,KAAK,SAAS,YAAY;AACpC,YAAI,MAAO,OAAM;AACjB,cAAM,UAAU;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AAAA,UACZ,YAAYA;AAAA,UACZ;AAAA,UACA;AAAA,UACA,MAAM,KAAK;AAAA,QACb;AAEA,eAAO,SAAS,OAAO;AACvB,aAAK,OAAO,yBAAyB,+BAA+B;AACpE,eAAO;AAAA,UACL,MAAM;AAAA,YACJ;AAAA,YACA,cAAc,OAAO;AAAA,UACvB;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,cAAc;AAAA,YAChB;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,UAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAC1D,WAAO,CAAC,EAAE,UAAU,MAAM,OAAO,gBAAgB,OAAO;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAIM,cAAc;AAAA;AAClB,YAAM,SAAS,uBAAuB,OAAO,SAAS,IAAI;AAC1D,YAAM,wBAAwB,MAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACjG,aAAO,CAAC,EAAE,OAAO,QAAQ;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,UAEH;AAAA,+CAFW,UAAU;AAAA,MACtB,OAAO;AAAA,IACT,GAAG;AACD,YAAM,KAAK;AACX,aAAO,MAAM,KAAK,aAAa,IAAI,MAAY;AAC7C,eAAO,MAAM,KAAK,SAAS,OAAO;AAAA,MACpC,EAAC;AAAA,IACH;AAAA;AAAA,EACM,WAIH;AAAA,+CAJY;AAAA,MACb;AAAA,IACF,IAAI;AAAA,MACF,OAAO;AAAA,IACT,GAAG;AACD,aAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,YAAI;AACJ,cAAM;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,QACT,IAAI;AACJ,YAAI,cAAc;AAChB,iBAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,eAAe,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAChF,YAAI,aAAa;AACf,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,MAAM,KAAK,MAAM,QAAQ,aAAa,KAAK;AAC/C,cAAI,OAAO;AAGT,gBAAI,EAAE,eAAe,KAAK,MAAM,MAAM,WAAW,OAAO,MAAM,WAAW,OAAO;AAC9E,qBAAO;AAAA,gBACL;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,UAAU,UAAU;AACtB,gBAAM,KAAK,eAAe;AAC1B,gBAAM,gBAAgB,KAAK,SAAS,GAAG,KAAK,UAAU,gBAAgB;AACtE,gBAAM,KAAK,sBAAsB,cAAc,IAAI;AAAA,QACrD;AACA,eAAO;AAAA,UACL,OAAO;AAAA,QACT;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,UAAU;AAC1B,UAAM,KAAK,KAAK;AAChB,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA,aAAa,MAAM;AACjB,aAAK,OAAO,kBAAkB,yCAAyC,EAAE;AACzE,aAAK,oBAAoB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AACA,SAAK,OAAO,wBAAwB,+BAA+B,EAAE;AACrE,SAAK,oBAAoB,IAAI,IAAI,YAAY;AAC7C,KAAC,MAAY;AACX,YAAM,KAAK;AACX,YAAM,KAAK,aAAa,IAAI,MAAY;AACtC,aAAK,oBAAoB,EAAE;AAAA,MAC7B,EAAC;AAAA,IACH,IAAG;AACH,WAAO;AAAA,MACL,MAAM;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACM,oBAAoB,IAAI;AAAA;AAC5B,aAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,YAAI,IAAI;AACR,YAAI;AACF,gBAAM;AAAA,YACJ,MAAM;AAAA,cACJ;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,MAAO,OAAM;AACjB,iBAAQ,KAAK,KAAK,oBAAoB,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,mBAAmB,OAAO;AAC1H,eAAK,OAAO,mBAAmB,eAAe,IAAI,WAAW,OAAO;AAAA,QACtE,SAAS,KAAK;AACZ,iBAAQ,KAAK,KAAK,oBAAoB,IAAI,EAAE,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,mBAAmB,IAAI;AACvH,eAAK,OAAO,mBAAmB,eAAe,IAAI,SAAS,GAAG;AAC9D,kBAAQ,MAAM,GAAG;AAAA,QACnB;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,sBAAsB,IAAqB;AAAA,+CAArB,OAAO,UAAU,CAAC,GAAG;AAC/C,UAAI,gBAAgB;AACpB,UAAI,sBAAsB;AAC1B,UAAI,KAAK,aAAa,QAAQ;AAC5B,cAAM,eAAe,qBAAqB;AAC1C,cAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,GAAG,YAAY,oBAAoB;AACxG,wBAAgB,MAAM,sBAAsB,YAAY;AACxD,8BAAsB,iBAAiB,gBAAgB,UAAU;AAAA,MACnE;AACA,UAAI;AACF,eAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;AAAA,UAC/D,MAAM;AAAA,YACJ;AAAA,YACA,gBAAgB;AAAA,YAChB,uBAAuB;AAAA,YACvB,sBAAsB;AAAA,cACpB,eAAe,QAAQ;AAAA,YACzB;AAAA,UACF;AAAA,UACA,SAAS,KAAK;AAAA,UACd,YAAY,QAAQ;AAAA,QACtB,CAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,oBAAoB;AAAA;AACxB,UAAI;AACJ,UAAI;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,KAAK,QAAQ;AACvB,YAAI,MAAO,OAAM;AACjB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,aAAa,KAAK,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,UAC5E;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,aAAa,aAAa;AAAA;AAC9B,UAAI;AACJ,UAAI;AACF,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,KAAK,YAAY,CAAM,WAAU;AACzC,cAAIG,KAAI,IAAI,IAAI,IAAI;AACpB,gBAAM;AAAA,YACJ,MAAAD;AAAA,YACA,OAAAD;AAAA,UACF,IAAI;AACJ,cAAIA,OAAO,OAAMA;AACjB,gBAAM,MAAM,MAAM,KAAK,mBAAmB,GAAG,KAAK,GAAG,8BAA8B,YAAY,UAAU;AAAA,YACvG,aAAaE,MAAK,YAAY,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG;AAAA,YAC/E,SAAS,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,YAC3E,cAAc,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,YAChF,qBAAqB;AAAA,UACvB,CAAC;AACD,iBAAO,MAAM,SAAS,KAAK,OAAO,OAAO,KAAK;AAAA,YAC5C,SAAS,KAAK;AAAA,YACd,MAAM,MAAM,KAAKD,MAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,UACxH,CAAC;AAAA,QACH,EAAC;AACD,YAAI,MAAO,OAAM;AACjB,YAAI,UAAU,KAAK,GAAG,KAAK,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB;AAC5G,iBAAO,SAAS,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,GAAG;AAAA,QAC7E;AACA,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,UAAU,YAAY;AAAA,YACtB,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,UACxD;AAAA,UACA,OAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,UAAU,YAAY;AAAA,cACtB,KAAK;AAAA,YACP;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,eAAe,UAAU;AAAA;AAC7B,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI,IAAI;AACR,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,OAAO;AACT,kBAAM;AAAA,UACR;AACA,iBAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,oBAAoB,SAAS,WAAW,IAAI;AAAA,YACjG,SAAS,KAAK;AAAA,YACd,MAAM,MAAM,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,UACxH,CAAC;AAAA,QACH,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,oBAAoB,cAAc;AAAA;AACtC,YAAM,YAAY,wBAAwB,aAAa,UAAU,GAAG,CAAC,CAAC;AACtE,WAAK,OAAO,WAAW,OAAO;AAC9B,UAAI;AACF,cAAM,YAAY,KAAK,IAAI;AAE3B,eAAO,MAAM,UAAU,CAAM,YAAW;AACtC,gBAAM,MAAM,UAAU,GAAG;AACzB,eAAK,OAAO,WAAW,sBAAsB,OAAO;AACpD,iBAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,mCAAmC;AAAA,YACtF,MAAM;AAAA,cACJ,eAAe;AAAA,YACjB;AAAA,YACA,SAAS,KAAK;AAAA,YACd,OAAO;AAAA,UACT,CAAC;AAAA,QACH,IAAG,CAAC,SAAS,GAAG,WAAW,UAAU,OAAO,SAAS,0BAA0B,OAAO,KAAK;AAAA,QAE3F,KAAK,IAAI,KAAK,UAAU,KAAK,MAAM,YAAY,0BAA0B;AAAA,MAC3E,SAAS,OAAO;AACd,aAAK,OAAO,WAAW,SAAS,KAAK;AACrC,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR,UAAE;AACA,aAAK,OAAO,WAAW,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,EACA,gBAAgB,cAAc;AAC5B,UAAM,iBAAiB,OAAO,iBAAiB,YAAY,iBAAiB,QAAQ,kBAAkB,gBAAgB,mBAAmB,gBAAgB,gBAAgB;AACzK,WAAO;AAAA,EACT;AAAA,EACM,sBAAsB,UAAU,SAAS;AAAA;AAC7C,YAAM,MAAM,MAAM,KAAK,mBAAmB,GAAG,KAAK,GAAG,cAAc,UAAU;AAAA,QAC3E,YAAY,QAAQ;AAAA,QACpB,QAAQ,QAAQ;AAAA,QAChB,aAAa,QAAQ;AAAA,MACvB,CAAC;AACD,WAAK,OAAO,4BAA4B,YAAY,UAAU,WAAW,SAAS,OAAO,GAAG;AAE5F,UAAI,UAAU,KAAK,CAAC,QAAQ,qBAAqB;AAC/C,eAAO,SAAS,OAAO,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,QACL,MAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,qBAAqB;AAAA;AACzB,UAAI;AACJ,YAAM,YAAY;AAClB,WAAK,OAAO,WAAW,OAAO;AAC9B,UAAI;AACF,cAAM,iBAAiB,MAAM,aAAa,KAAK,SAAS,KAAK,UAAU;AACvE,aAAK,OAAO,WAAW,wBAAwB,cAAc;AAC7D,YAAI,CAAC,KAAK,gBAAgB,cAAc,GAAG;AACzC,eAAK,OAAO,WAAW,sBAAsB;AAC7C,cAAI,mBAAmB,MAAM;AAC3B,kBAAM,KAAK,eAAe;AAAA,UAC5B;AACA;AAAA,QACF;AACA,cAAM,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI;AAC5C,cAAM,sBAAsB,KAAK,eAAe,gBAAgB,QAAQ,OAAO,SAAS,KAAK,YAAY,UAAU;AACnH,aAAK,OAAO,WAAW,cAAc,oBAAoB,KAAK,MAAM,2BAA2B,aAAa,GAAG;AAC/G,YAAI,mBAAmB;AACrB,cAAI,KAAK,oBAAoB,eAAe,eAAe;AACzD,kBAAM;AAAA,cACJ;AAAA,YACF,IAAI,MAAM,KAAK,kBAAkB,eAAe,aAAa;AAC7D,gBAAI,OAAO;AACT,sBAAQ,MAAM,KAAK;AACnB,kBAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,qBAAK,OAAO,WAAW,mEAAmE,KAAK;AAC/F,sBAAM,KAAK,eAAe;AAAA,cAC5B;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AAIL,gBAAM,KAAK,sBAAsB,aAAa,cAAc;AAAA,QAC9D;AAAA,MACF,SAAS,KAAK;AACZ,aAAK,OAAO,WAAW,SAAS,GAAG;AACnC,gBAAQ,MAAM,GAAG;AACjB;AAAA,MACF,UAAE;AACA,aAAK,OAAO,WAAW,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,EACM,kBAAkB,cAAc;AAAA;AACpC,UAAI,IAAI;AACR,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,wBAAwB;AAAA,MACpC;AAEA,UAAI,KAAK,oBAAoB;AAC3B,eAAO,KAAK,mBAAmB;AAAA,MACjC;AACA,YAAM,YAAY,sBAAsB,aAAa,UAAU,GAAG,CAAC,CAAC;AACpE,WAAK,OAAO,WAAW,OAAO;AAC9B,UAAI;AACF,aAAK,qBAAqB,IAAI,SAAS;AACvC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,MAAM,KAAK,oBAAoB,YAAY;AAC/C,YAAI,MAAO,OAAM;AACjB,YAAI,CAAC,KAAK,QAAS,OAAM,IAAI,wBAAwB;AACrD,cAAM,KAAK,aAAa,KAAK,OAAO;AACpC,cAAM,KAAK,sBAAsB,mBAAmB,KAAK,OAAO;AAChE,cAAM,SAAS;AAAA,UACb,SAAS,KAAK;AAAA,UACd,OAAO;AAAA,QACT;AACA,aAAK,mBAAmB,QAAQ,MAAM;AACtC,eAAO;AAAA,MACT,SAAS,OAAO;AACd,aAAK,OAAO,WAAW,SAAS,KAAK;AACrC,YAAI,YAAY,KAAK,GAAG;AACtB,gBAAM,SAAS;AAAA,YACb,SAAS;AAAA,YACT;AAAA,UACF;AACA,cAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,kBAAM,KAAK,eAAe;AAC1B,kBAAM,KAAK,sBAAsB,cAAc,IAAI;AAAA,UACrD;AACA,WAAC,KAAK,KAAK,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,MAAM;AACrF,iBAAO;AAAA,QACT;AACA,SAAC,KAAK,KAAK,wBAAwB,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,KAAK;AACnF,cAAM;AAAA,MACR,UAAE;AACA,aAAK,qBAAqB;AAC1B,aAAK,OAAO,WAAW,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA,EACM,sBAAsB,OAAO,SAAS,YAAY,MAAM;AAAA;AAC5D,YAAM,YAAY,0BAA0B,KAAK;AACjD,WAAK,OAAO,WAAW,SAAS,SAAS,eAAe,SAAS,EAAE;AACnE,UAAI;AACF,YAAI,KAAK,oBAAoB,WAAW;AACtC,eAAK,iBAAiB,YAAY;AAAA,YAChC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AACA,cAAM,SAAS,CAAC;AAChB,cAAM,WAAW,MAAM,KAAK,KAAK,oBAAoB,OAAO,CAAC,EAAE,IAAI,CAAM,MAAK;AAC5E,cAAI;AACF,kBAAM,EAAE,SAAS,OAAO,OAAO;AAAA,UACjC,SAAS,GAAG;AACV,mBAAO,KAAK,CAAC;AAAA,UACf;AAAA,QACF,EAAC;AACD,cAAM,QAAQ,IAAI,QAAQ;AAC1B,YAAI,OAAO,SAAS,GAAG;AACrB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,oBAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,UACzB;AACA,gBAAM,OAAO,CAAC;AAAA,QAChB;AAAA,MACF,UAAE;AACA,aAAK,OAAO,WAAW,KAAK;AAAA,MAC9B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,aAAa,SAAS;AAAA;AAC1B,WAAK,OAAO,mBAAmB,OAAO;AACtC,YAAM,aAAa,KAAK,SAAS,KAAK,YAAY,OAAO;AAAA,IAC3D;AAAA;AAAA,EACM,iBAAiB;AAAA;AACrB,WAAK,OAAO,mBAAmB;AAC/B,YAAM,gBAAgB,KAAK,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC;AACjC,SAAK,OAAO,qCAAqC;AACjD,UAAM,WAAW,KAAK;AACtB,SAAK,4BAA4B;AACjC,QAAI;AACF,UAAI,YAAY,UAAU,MAAM,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,sBAAsB;AAC3G,eAAO,oBAAoB,oBAAoB,QAAQ;AAAA,MACzD;AAAA,IACF,SAAS,GAAG;AACV,cAAQ,MAAM,6CAA6C,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,oBAAoB;AAAA;AACxB,YAAM,KAAK,iBAAiB;AAC5B,WAAK,OAAO,sBAAsB;AAClC,YAAM,SAAS,YAAY,MAAM,KAAK,sBAAsB,GAAG,0BAA0B;AACzF,WAAK,oBAAoB;AACzB,UAAI,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,UAAU,YAAY;AAO9E,eAAO,MAAM;AAAA,MAEf,WAAW,OAAO,SAAS,eAAe,OAAO,KAAK,eAAe,YAAY;AAI/E,aAAK,WAAW,MAAM;AAAA,MACxB;AAIA,iBAAW,MAAY;AACrB,cAAM,KAAK;AACX,cAAM,KAAK,sBAAsB;AAAA,MACnC,IAAG,CAAC;AAAA,IACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,mBAAmB;AAAA;AACvB,WAAK,OAAO,qBAAqB;AACjC,YAAM,SAAS,KAAK;AACpB,WAAK,oBAAoB;AACzB,UAAI,QAAQ;AACV,sBAAc,MAAM;AAAA,MACtB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBM,mBAAmB;AAAA;AACvB,WAAK,iCAAiC;AACtC,YAAM,KAAK,kBAAkB;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASM,kBAAkB;AAAA;AACtB,WAAK,iCAAiC;AACtC,YAAM,KAAK,iBAAiB;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,wBAAwB;AAAA;AAC5B,WAAK,OAAO,4BAA4B,OAAO;AAC/C,UAAI;AACF,cAAM,KAAK,aAAa,GAAG,MAAY;AACrC,cAAI;AACF,kBAAM,MAAM,KAAK,IAAI;AACrB,gBAAI;AACF,qBAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,sBAAM;AAAA,kBACJ,MAAM;AAAA,oBACJ;AAAA,kBACF;AAAA,gBACF,IAAI;AACJ,oBAAI,CAAC,WAAW,CAAC,QAAQ,iBAAiB,CAAC,QAAQ,YAAY;AAC7D,uBAAK,OAAO,4BAA4B,YAAY;AACpD;AAAA,gBACF;AAEA,sBAAM,iBAAiB,KAAK,OAAO,QAAQ,aAAa,MAAO,OAAO,0BAA0B;AAChG,qBAAK,OAAO,4BAA4B,2BAA2B,cAAc,wBAAwB,0BAA0B,4BAA4B,2BAA2B,QAAQ;AAClM,oBAAI,kBAAkB,6BAA6B;AACjD,wBAAM,KAAK,kBAAkB,QAAQ,aAAa;AAAA,gBACpD;AAAA,cACF,EAAC;AAAA,YACH,SAAS,GAAG;AACV,sBAAQ,MAAM,0EAA0E,CAAC;AAAA,YAC3F;AAAA,UACF,UAAE;AACA,iBAAK,OAAO,4BAA4B,KAAK;AAAA,UAC/C;AAAA,QACF,EAAC;AAAA,MACH,SAAS,GAAG;AACV,YAAI,EAAE,oBAAoB,aAAa,yBAAyB;AAC9D,eAAK,OAAO,4CAA4C;AAAA,QAC1D,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,0BAA0B;AAAA;AAC9B,WAAK,OAAO,4BAA4B;AACxC,UAAI,CAAC,UAAU,KAAK,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,mBAAmB;AAC9F,YAAI,KAAK,kBAAkB;AAEzB,eAAK,iBAAiB;AAAA,QACxB;AACA,eAAO;AAAA,MACT;AACA,UAAI;AACF,aAAK,4BAA4B,MAAS;AAAG,uBAAM,KAAK,qBAAqB,KAAK;AAAA;AAClF,mBAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,iBAAiB,oBAAoB,KAAK,yBAAyB;AAG1H,cAAM,KAAK,qBAAqB,IAAI;AAAA,MACtC,SAAS,OAAO;AACd,gBAAQ,MAAM,2BAA2B,KAAK;AAAA,MAChD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,qBAAqB,sBAAsB;AAAA;AAC/C,YAAM,aAAa,yBAAyB,oBAAoB;AAChE,WAAK,OAAO,YAAY,mBAAmB,SAAS,eAAe;AACnE,UAAI,SAAS,oBAAoB,WAAW;AAC1C,YAAI,KAAK,kBAAkB;AAGzB,eAAK,kBAAkB;AAAA,QACzB;AACA,YAAI,CAAC,sBAAsB;AAKzB,gBAAM,KAAK;AACX,gBAAM,KAAK,aAAa,IAAI,MAAY;AACtC,gBAAI,SAAS,oBAAoB,WAAW;AAC1C,mBAAK,OAAO,YAAY,0GAA0G;AAElI;AAAA,YACF;AAEA,kBAAM,KAAK,mBAAmB;AAAA,UAChC,EAAC;AAAA,QACH;AAAA,MACF,WAAW,SAAS,oBAAoB,UAAU;AAChD,YAAI,KAAK,kBAAkB;AACzB,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,mBAAmB,KAAK,UAAU,SAAS;AAAA;AAC/C,YAAM,YAAY,CAAC,YAAY,mBAAmB,QAAQ,CAAC,EAAE;AAC7D,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY;AACxE,kBAAU,KAAK,eAAe,mBAAmB,QAAQ,UAAU,CAAC,EAAE;AAAA,MACxE;AACA,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AACpE,kBAAU,KAAK,UAAU,mBAAmB,QAAQ,MAAM,CAAC,EAAE;AAAA,MAC/D;AACA,UAAI,KAAK,aAAa,QAAQ;AAC5B,cAAM,eAAe,qBAAqB;AAC1C,cAAM,aAAa,KAAK,SAAS,GAAG,KAAK,UAAU,kBAAkB,YAAY;AACjF,cAAM,gBAAgB,MAAM,sBAAsB,YAAY;AAC9D,cAAM,sBAAsB,iBAAiB,gBAAgB,UAAU;AACvE,aAAK,OAAO,QAAQ,iBAAiB,GAAG,aAAa,UAAU,GAAG,CAAC,CAAC,OAAO,kBAAkB,eAAe,UAAU,mBAAmB;AACzI,cAAM,aAAa,IAAI,gBAAgB;AAAA,UACrC,gBAAgB,GAAG,mBAAmB,aAAa,CAAC;AAAA,UACpD,uBAAuB,GAAG,mBAAmB,mBAAmB,CAAC;AAAA,QACnE,CAAC;AACD,kBAAU,KAAK,WAAW,SAAS,CAAC;AAAA,MACtC;AACA,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,aAAa;AACzE,cAAM,QAAQ,IAAI,gBAAgB,QAAQ,WAAW;AACrD,kBAAU,KAAK,MAAM,SAAS,CAAC;AAAA,MACjC;AACA,UAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,qBAAqB;AACjF,kBAAU,KAAK,sBAAsB,QAAQ,mBAAmB,EAAE;AAAA,MACpE;AACA,aAAO,GAAG,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;AAAA,IACtC;AAAA;AAAA,EACM,UAAU,QAAQ;AAAA;AACtB,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI;AACJ,gBAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,UACT,IAAI;AACJ,cAAI,cAAc;AAChB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO,MAAM,SAAS,KAAK,OAAO,UAAU,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,IAAI;AAAA,YACpF,SAAS,KAAK;AAAA,YACd,MAAM,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,UACpI,CAAC;AAAA,QACH,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,QAAQ,QAAQ;AAAA;AACpB,UAAI;AACF,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI,IAAI;AACR,gBAAM;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,UACT,IAAI;AACJ,cAAI,cAAc;AAChB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AACA,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY;AAAA,YAC5D,MAAM;AAAA,cACJ,eAAe,OAAO;AAAA,cACtB,aAAa,OAAO;AAAA,cACpB,QAAQ,OAAO;AAAA,YACjB;AAAA,YACA,SAAS,KAAK;AAAA,YACd,MAAM,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,UACpI,CAAC;AACD,cAAI,OAAO;AACT,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AACA,eAAK,KAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS;AAChH,iBAAK,KAAK,UAAU,4BAA4B,KAAK,KAAK,OAAO;AAAA,UACnE;AACA,iBAAO;AAAA,YACL;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,EAAC;AAAA,MACH,SAAS,OAAO;AACd,YAAI,YAAY,KAAK,GAAG;AACtB,iBAAO;AAAA,YACL,MAAM;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,QAAQ,QAAQ;AAAA;AACpB,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,YAAI;AACF,iBAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,gBAAI;AACJ,kBAAM;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,YACT,IAAI;AACJ,gBAAI,cAAc;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AACA,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,WAAW;AAAA,cACtF,MAAM;AAAA,gBACJ,MAAM,OAAO;AAAA,gBACb,cAAc,OAAO;AAAA,cACvB;AAAA,cACA,SAAS,KAAK;AAAA,cACd,MAAM,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,YACpI,CAAC;AACD,gBAAI,OAAO;AACT,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN;AAAA,cACF;AAAA,YACF;AACA,kBAAM,KAAK,aAAa,OAAO,OAAO;AAAA,cACpC,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI,GAAI,IAAI,KAAK;AAAA,YACnD,GAAG,IAAI,CAAC;AACR,kBAAM,KAAK,sBAAsB,0BAA0B,IAAI;AAC/D,mBAAO;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF,EAAC;AAAA,QACH,SAAS,OAAO;AACd,cAAI,YAAY,KAAK,GAAG;AACtB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AACA,gBAAM;AAAA,QACR;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,WAAW,QAAQ;AAAA;AACvB,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,YAAI;AACF,iBAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,gBAAI;AACJ,kBAAM;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,YACT,IAAI;AACJ,gBAAI,cAAc;AAChB,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,cACT;AAAA,YACF;AACA,mBAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,GAAG,KAAK,GAAG,YAAY,OAAO,QAAQ,cAAc;AAAA,cAC5F,SAAS,KAAK;AAAA,cACd,MAAM,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,YACpI,CAAC;AAAA,UACH,EAAC;AAAA,QACH,SAAS,OAAO;AACd,cAAI,YAAY,KAAK,GAAG;AACtB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF;AACA,gBAAM;AAAA,QACR;AAAA,MACF,EAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,oBAAoB,QAAQ;AAAA;AAGhC,YAAM;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,MACT,IAAI,MAAM,KAAK,WAAW;AAAA,QACxB,UAAU,OAAO;AAAA,MACnB,CAAC;AACD,UAAI,gBAAgB;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,MAAM,KAAK,QAAQ;AAAA,QACxB,UAAU,OAAO;AAAA,QACjB,aAAa,cAAc;AAAA,QAC3B,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,eAAe;AAAA;AAEnB,YAAM;AAAA,QACJ,MAAM;AAAA,UACJ;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT,IAAI,MAAM,KAAK,QAAQ;AACvB,UAAI,WAAW;AACb,eAAO;AAAA,UACL,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,WAAW,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAY,CAAC;AAC/E,YAAM,OAAO,QAAQ,OAAO,YAAU,OAAO,gBAAgB,UAAU,OAAO,WAAW,UAAU;AACnG,aAAO;AAAA,QACL,MAAM;AAAA,UACJ,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,OAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,kCAAkC;AAAA;AACtC,aAAO,KAAK,aAAa,IAAI,MAAY;AACvC,eAAO,MAAM,KAAK,YAAY,CAAM,WAAU;AAC5C,cAAI,IAAI;AACR,gBAAM;AAAA,YACJ,MAAM;AAAA,cACJ;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT,IAAI;AACJ,cAAI,cAAc;AAChB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,OAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,cAAc;AAAA,gBACd,WAAW;AAAA,gBACX,8BAA8B,CAAC;AAAA,cACjC;AAAA,cACA,OAAO;AAAA,YACT;AAAA,UACF;AACA,gBAAM,UAAU,KAAK,WAAW,QAAQ,YAAY;AACpD,cAAI,eAAe;AACnB,cAAI,QAAQ,KAAK;AACf,2BAAe,QAAQ;AAAA,UACzB;AACA,cAAI,YAAY;AAChB,gBAAM,mBAAmB,MAAM,KAAK,QAAQ,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,YAAU,OAAO,WAAW,UAAU,OAAO,QAAQ,OAAO,SAAS,KAAK,CAAC;AACpL,cAAI,gBAAgB,SAAS,GAAG;AAC9B,wBAAY;AAAA,UACd;AACA,gBAAM,+BAA+B,QAAQ,OAAO,CAAC;AACrD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,YACA,OAAO;AAAA,UACT;AAAA,QACF,EAAC;AAAA,MACH,EAAC;AAAA,IACH;AAAA;AACF;AACA,aAAa,iBAAiB;;;AC90E9B,IAAM,eAAe;AACrB,IAAO,uBAAQ;;;ACDf,IAAM,aAAa;AACnB,IAAO,qBAAQ;;;ACDR,IAAM,qBAAN,cAAiC,mBAAW;AAAA,EACjD,YAAY,SAAS;AACnB,UAAM,OAAO;AAAA,EACf;AACF;;;ACLA,IAAIE,aAAsC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAcA,IAAqB,iBAArB,MAAoC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAalC,YAAY,aAAa,aAAa,SAAS;AAC7C,QAAI,IAAI,IAAI;AACZ,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,QAAI,CAAC,YAAa,OAAM,IAAI,MAAM,0BAA0B;AAC5D,QAAI,CAAC,YAAa,OAAM,IAAI,MAAM,0BAA0B;AAC5D,UAAM,eAAe,mBAAmB,WAAW;AACnD,SAAK,cAAc,GAAG,YAAY,eAAe,QAAQ,UAAU,IAAI;AACvE,SAAK,UAAU,GAAG,YAAY;AAC9B,SAAK,aAAa,GAAG,YAAY;AACjC,SAAK,eAAe,GAAG,YAAY;AAEnC,UAAM,oBAAoB,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AAC5E,UAAM,WAAW;AAAA,MACf,IAAI;AAAA,MACJ,UAAU;AAAA,MACV,MAAM,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,oBAAoB,GAAG;AAAA,QAC3D,YAAY;AAAA,MACd,CAAC;AAAA,MACD,QAAQ;AAAA,IACV;AACA,UAAM,WAAW,qBAAqB,YAAY,QAAQ,YAAY,SAAS,UAAU,CAAC,GAAG,QAAQ;AACrG,SAAK,cAAc,KAAK,SAAS,KAAK,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AACnF,SAAK,WAAW,KAAK,SAAS,OAAO,aAAa,QAAQ,OAAO,SAAS,KAAK,CAAC;AAChF,QAAI,CAAC,SAAS,aAAa;AACzB,WAAK,OAAO,KAAK,yBAAyB,KAAK,SAAS,UAAU,QAAQ,OAAO,SAAS,KAAK,CAAC,GAAG,KAAK,SAAS,SAAS,OAAO,KAAK;AAAA,IACxI,OAAO;AACL,WAAK,cAAc,SAAS;AAC5B,WAAK,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,QACxB,KAAK,CAAC,GAAG,SAAS;AAChB,gBAAM,IAAI,MAAM,6GAA6G,OAAO,IAAI,CAAC,kBAAkB;AAAA,QAC7J;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,QAAQ,cAAc,aAAa,KAAK,gBAAgB,KAAK,IAAI,GAAG,SAAS,OAAO,KAAK;AAC9F,SAAK,WAAW,KAAK,oBAAoB,OAAO,OAAO;AAAA,MACrD,SAAS,KAAK;AAAA,MACd,aAAa,KAAK,gBAAgB,KAAK,IAAI;AAAA,IAC7C,GAAG,SAAS,QAAQ,CAAC;AACrB,SAAK,OAAO,IAAI,gBAAgB,GAAG,YAAY,YAAY;AAAA,MACzD,SAAS,KAAK;AAAA,MACd,QAAQ,SAAS,GAAG;AAAA,MACpB,OAAO,KAAK;AAAA,IACd,CAAC;AACD,QAAI,CAAC,SAAS,aAAa;AACzB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,YAAY;AACd,WAAO,IAAI,gBAAgB,KAAK,cAAc;AAAA,MAC5C,SAAS,KAAK;AAAA,MACd,aAAa,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,IAAI,cAAsB,KAAK,YAAY,KAAK,SAAS,KAAK,KAAK;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,UAAU;AACb,WAAO,KAAK,KAAK,KAAK,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBA,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;AAC/B,WAAO,KAAK,KAAK,IAAI,IAAI,MAAM,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM,OAAO;AAAA,IACnB,QAAQ,CAAC;AAAA,EACX,GAAG;AACD,WAAO,KAAK,SAAS,QAAQ,MAAM,IAAI;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK,SAAS,YAAY;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,SAAS;AACrB,WAAO,KAAK,SAAS,cAAc,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,oBAAoB;AAClB,WAAO,KAAK,SAAS,kBAAkB;AAAA,EACzC;AAAA,EACA,kBAAkB;AAChB,QAAI,IAAI;AACR,WAAOA,WAAU,MAAM,QAAQ,QAAQ,aAAa;AAClD,UAAI,KAAK,aAAa;AACpB,eAAO,MAAM,KAAK,YAAY;AAAA,MAChC;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,MAAM,KAAK,KAAK,WAAW;AAC/B,cAAQ,MAAM,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,kBAAkB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC1H,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,SAASC,QAAO;AACjB,UAAM,cAAc;AAAA,MAClB,eAAe,UAAU,KAAK,WAAW;AAAA,MACzC,QAAQ,GAAG,KAAK,WAAW;AAAA,IAC7B;AACA,WAAO,IAAI,mBAAmB;AAAA,MAC5B,KAAK,KAAK;AAAA,MACV,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,OAAO;AAAA,MAC9D;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAAA;AAAA;AAAA;AAAA,MAGA,8BAA8B,mBAAmB,KAAK;AAAA,IACxD,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,SAAS;AAC3B,WAAO,IAAI,eAAe,KAAK,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,MACpF,QAAQ,OAAO,OAAO;AAAA,QACpB,QAAQ,KAAK;AAAA,MACf,GAAG,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM;AAAA,IACrE,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,uBAAuB;AACrB,QAAI,OAAO,KAAK,KAAK,kBAAkB,CAAC,OAAO,YAAY;AACzD,WAAK,oBAAoB,OAAO,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY;AAAA,IAClH,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,OAAO,QAAQ,OAAO;AACxC,SAAK,UAAU,qBAAqB,UAAU,gBAAgB,KAAK,uBAAuB,OAAO;AAC/F,WAAK,qBAAqB;AAAA,IAC5B,WAAW,UAAU,cAAc;AACjC,WAAK,SAAS,QAAQ;AACtB,UAAI,UAAU,UAAW,MAAK,KAAK,QAAQ;AAC3C,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AACF;;;AC3PO,IAAM,eAAe,CAAC,aAAa,aAAa,YAAY;AACjE,SAAO,IAAI,eAAe,aAAa,aAAa,OAAO;AAC7D;", "names": ["PostgrestError", "PostgrestBuilder", "res", "PostgrestTransformBuilder", "PostgrestFilterBuilder", "PostgrestQueryBuilder", "fetch", "head", "PostgrestClient", "fetch", "head", "get", "fetch", "FunctionRegion", "index", "SOCKET_STATES", "CHANNEL_STATES", "CHANNEL_EVENTS", "TRANSPORTS", "CONNECTION_STATE", "PostgresTypes", "REALTIME_PRESENCE_LISTEN_EVENTS", "REALTIME_POSTGRES_CHANGES_LISTEN_EVENT", "REALTIME_LISTEN_TYPES", "REALTIME_SUBSCRIBE_STATES", "_a", "_b", "type", "noop", "fetch", "__awaiter", "resolveFetch", "fetch", "__awaiter", "__awaiter", "fetch", "resolveFetch", "version", "DEFAULT_HEADERS", "version", "__awaiter", "fetch", "DEFAULT_HEADERS", "resolveFetch", "fetch", "version", "DEFAULT_HEADERS", "version", "__awaiter", "resolveFetch", "Headers", "fetch", "__awaiter", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "__awaiter", "resolveFetch", "fetch", "_getErrorMessage", "handleError", "_getRequestParams", "_handleRequest", "__rest", "fetch", "resolveFetch", "version", "DEFAULT_HEADERS", "version", "DEFAULT_HEADERS", "resolveFetch", "version", "expiresAt", "error", "data", "_a", "__awaiter", "fetch"]}