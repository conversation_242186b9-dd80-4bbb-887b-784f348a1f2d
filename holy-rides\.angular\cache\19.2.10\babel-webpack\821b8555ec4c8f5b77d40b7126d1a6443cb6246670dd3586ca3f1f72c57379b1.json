{"ast": null, "code": "import { version } from './version';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `realtime-js/${version}`\n};\nexport const VSN = '1.0.0';\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES;\n(function (SOCKET_STATES) {\n  SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n  SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n  SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n  SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nexport var CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n  CHANNEL_STATES[\"closed\"] = \"closed\";\n  CHANNEL_STATES[\"errored\"] = \"errored\";\n  CHANNEL_STATES[\"joined\"] = \"joined\";\n  CHANNEL_STATES[\"joining\"] = \"joining\";\n  CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nexport var CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n  CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n  CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n  CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n  CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n  CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n  CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nexport var TRANSPORTS;\n(function (TRANSPORTS) {\n  TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nexport var CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n  CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n  CONNECTION_STATE[\"Open\"] = \"open\";\n  CONNECTION_STATE[\"Closing\"] = \"closing\";\n  CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));", "map": {"version": 3, "names": ["version", "DEFAULT_HEADERS", "VSN", "DEFAULT_TIMEOUT", "WS_CLOSE_NORMAL", "SOCKET_STATES", "CHANNEL_STATES", "CHANNEL_EVENTS", "TRANSPORTS", "CONNECTION_STATE"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/lib/constants.js"], "sourcesContent": ["import { version } from './version';\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `realtime-js/${version}` };\nexport const VSN = '1.0.0';\nexport const DEFAULT_TIMEOUT = 10000;\nexport const WS_CLOSE_NORMAL = 1000;\nexport var SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nexport var CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nexport var CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nexport var TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nexport var CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,eAAe,GAAG;EAAE,eAAe,EAAE,eAAeD,OAAO;AAAG,CAAC;AAC5E,OAAO,MAAME,GAAG,GAAG,OAAO;AAC1B,OAAO,MAAMC,eAAe,GAAG,KAAK;AACpC,OAAO,MAAMC,eAAe,GAAG,IAAI;AACnC,OAAO,IAAIC,aAAa;AACxB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAACA,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC7DA,aAAa,CAACA,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjDA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvDA,aAAa,CAACA,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACzD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrCA,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACnCA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrCA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;AACzC,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,OAAO,CAAC,GAAG,WAAW;EACrCA,cAAc,CAAC,OAAO,CAAC,GAAG,WAAW;EACrCA,cAAc,CAAC,MAAM,CAAC,GAAG,UAAU;EACnCA,cAAc,CAAC,OAAO,CAAC,GAAG,WAAW;EACrCA,cAAc,CAAC,OAAO,CAAC,GAAG,WAAW;EACrCA,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc;AACnD,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAAC,WAAW,CAAC,GAAG,WAAW;AACzC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO,IAAIC,gBAAgB;AAC3B,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,YAAY,CAAC,GAAG,YAAY;EAC7CA,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM;EACjCA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvCA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACzC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}