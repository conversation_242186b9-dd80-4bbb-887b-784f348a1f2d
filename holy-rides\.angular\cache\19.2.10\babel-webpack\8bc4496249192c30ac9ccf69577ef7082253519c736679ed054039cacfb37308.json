{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\n// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch';\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = nodeFetch;\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const resolveHeadersConstructor = () => {\n  if (typeof Headers === 'undefined') {\n    return NodeFetchHeaders;\n  }\n  return Headers;\n};\nexport const fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n  const fetch = resolveFetch(customFetch);\n  const HeadersConstructor = resolveHeadersConstructor();\n  return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n    var _a;\n    const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n    let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n    if (!headers.has('apikey')) {\n      headers.set('apikey', supabaseKey);\n    }\n    if (!headers.has('Authorization')) {\n      headers.set('Authorization', `Bearer ${accessToken}`);\n    }\n    return fetch(input, Object.assign(Object.assign({}, init), {\n      headers\n    }));\n  });\n};", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "nodeFetch", "Headers", "NodeFetchHeaders", "resolveFetch", "customFetch", "_fetch", "fetch", "args", "resolveHeadersConstructor", "fetchWithAuth", "supabase<PERSON>ey", "getAccessToken", "HeadersConstructor", "input", "init", "_a", "accessToken", "headers", "has", "set", "Object", "assign"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\nimport nodeFetch, { Headers as NodeFetchHeaders } from '@supabase/node-fetch';\nexport const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = nodeFetch;\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nexport const resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return NodeFetchHeaders;\n    }\n    return Headers;\n};\nexport const fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD;AACA,OAAOO,SAAS,IAAIC,OAAO,IAAIC,gBAAgB,QAAQ,sBAAsB;AAC7E,OAAO,MAAMC,YAAY,GAAIC,WAAW,IAAK;EACzC,IAAIC,MAAM;EACV,IAAID,WAAW,EAAE;IACbC,MAAM,GAAGD,WAAW;EACxB,CAAC,MACI,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACnCD,MAAM,GAAGL,SAAS;EACtB,CAAC,MACI;IACDK,MAAM,GAAGC,KAAK;EAClB;EACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACvC,CAAC;AACD,OAAO,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAC3C,IAAI,OAAOP,OAAO,KAAK,WAAW,EAAE;IAChC,OAAOC,gBAAgB;EAC3B;EACA,OAAOD,OAAO;AAClB,CAAC;AACD,OAAO,MAAMQ,aAAa,GAAGA,CAACC,WAAW,EAAEC,cAAc,EAAEP,WAAW,KAAK;EACvE,MAAME,KAAK,GAAGH,YAAY,CAACC,WAAW,CAAC;EACvC,MAAMQ,kBAAkB,GAAGJ,yBAAyB,CAAC,CAAC;EACtD,OAAO,CAACK,KAAK,EAAEC,IAAI,KAAKjC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IACnE,IAAIkC,EAAE;IACN,MAAMC,WAAW,GAAG,CAACD,EAAE,GAAI,MAAMJ,cAAc,CAAC,CAAE,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGL,WAAW;IAChG,IAAIO,OAAO,GAAG,IAAIL,kBAAkB,CAACE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,OAAO,CAAC;IAC9F,IAAI,CAACA,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAE;MACxBD,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAET,WAAW,CAAC;IACtC;IACA,IAAI,CAACO,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,EAAE;MAC/BD,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE,UAAUH,WAAW,EAAE,CAAC;IACzD;IACA,OAAOV,KAAK,CAACO,KAAK,EAAEO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,CAAC,EAAE;MAAEG;IAAQ,CAAC,CAAC,CAAC;EAC5E,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}