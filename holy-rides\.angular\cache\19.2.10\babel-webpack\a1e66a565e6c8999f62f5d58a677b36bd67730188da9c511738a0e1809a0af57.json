{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';\nexport class FunctionsClient {\n  constructor(url, {\n    headers = {},\n    customFetch,\n    region = FunctionRegion.Any\n  } = {}) {\n    this.url = url;\n    this.headers = headers;\n    this.region = region;\n    this.fetch = resolveFetch(customFetch);\n  }\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token) {\n    this.headers.Authorization = `Bearer ${token}`;\n  }\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  invoke(functionName, options = {}) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const {\n          headers,\n          method,\n          body: functionArgs\n        } = options;\n        let _headers = {};\n        let {\n          region\n        } = options;\n        if (!region) {\n          region = this.region;\n        }\n        if (region && region !== 'any') {\n          _headers['x-region'] = region;\n        }\n        let body;\n        if (functionArgs && (headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type') || !headers)) {\n          if (typeof Blob !== 'undefined' && functionArgs instanceof Blob || functionArgs instanceof ArrayBuffer) {\n            // will work for File as File inherits Blob\n            // also works for ArrayBuffer as it is the same underlying structure as a Blob\n            _headers['Content-Type'] = 'application/octet-stream';\n            body = functionArgs;\n          } else if (typeof functionArgs === 'string') {\n            // plain string\n            _headers['Content-Type'] = 'text/plain';\n            body = functionArgs;\n          } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n            // don't set content-type headers\n            // Request will automatically add the right boundary value\n            body = functionArgs;\n          } else {\n            // default, assume this is JSON\n            _headers['Content-Type'] = 'application/json';\n            body = JSON.stringify(functionArgs);\n          }\n        }\n        const response = yield this.fetch(`${this.url}/${functionName}`, {\n          method: method || 'POST',\n          // headers priority is (high to low):\n          // 1. invoke-level headers\n          // 2. client-level headers\n          // 3. default Content-Type header\n          headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n          body\n        }).catch(fetchError => {\n          throw new FunctionsFetchError(fetchError);\n        });\n        const isRelayError = response.headers.get('x-relay-error');\n        if (isRelayError && isRelayError === 'true') {\n          throw new FunctionsRelayError(response);\n        }\n        if (!response.ok) {\n          throw new FunctionsHttpError(response);\n        }\n        let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n        let data;\n        if (responseType === 'application/json') {\n          data = yield response.json();\n        } else if (responseType === 'application/octet-stream') {\n          data = yield response.blob();\n        } else if (responseType === 'text/event-stream') {\n          data = response;\n        } else if (responseType === 'multipart/form-data') {\n          data = yield response.formData();\n        } else {\n          // default to text\n          data = yield response.text();\n        }\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        return {\n          data: null,\n          error\n        };\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "resolveFetch", "FunctionsFetchError", "FunctionsHttpError", "FunctionsRelayError", "FunctionRegion", "FunctionsClient", "constructor", "url", "headers", "customFetch", "region", "Any", "fetch", "setAuth", "token", "Authorization", "invoke", "functionName", "options", "_a", "method", "body", "functionArgs", "_headers", "Object", "prototype", "hasOwnProperty", "call", "Blob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormData", "JSON", "stringify", "response", "assign", "catch", "fetchError", "isRelayError", "get", "ok", "responseType", "split", "trim", "data", "json", "blob", "formData", "text", "error"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion, } from './types';\nexport class FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = FunctionRegion.Any, } = {}) {\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = resolveFetch(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */\n    setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */\n    invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                if (region && region !== 'any') {\n                    _headers['x-region'] = region;\n                }\n                let body;\n                if (functionArgs &&\n                    ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)) {\n                    if ((typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n                        functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers['Content-Type'] = 'application/octet-stream';\n                        body = functionArgs;\n                    }\n                    else if (typeof functionArgs === 'string') {\n                        // plain string\n                        _headers['Content-Type'] = 'text/plain';\n                        body = functionArgs;\n                    }\n                    else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    }\n                    else {\n                        // default, assume this is JSON\n                        _headers['Content-Type'] = 'application/json';\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(`${this.url}/${functionName}`, {\n                    method: method || 'POST',\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body,\n                }).catch((fetchError) => {\n                    throw new FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get('x-relay-error');\n                if (isRelayError && isRelayError === 'true') {\n                    throw new FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n                let data;\n                if (responseType === 'application/json') {\n                    data = yield response.json();\n                }\n                else if (responseType === 'application/octet-stream') {\n                    data = yield response.blob();\n                }\n                else if (responseType === 'text/event-stream') {\n                    data = response;\n                }\n                else if (responseType === 'multipart/form-data') {\n                    data = yield response.formData();\n                }\n                else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return { data, error: null };\n            }\n            catch (error) {\n                return { data: null, error };\n            }\n        });\n    }\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,YAAY,QAAQ,UAAU;AACvC,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,cAAc,QAAS,SAAS;AACvG,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAACC,GAAG,EAAE;IAAEC,OAAO,GAAG,CAAC,CAAC;IAAEC,WAAW;IAAEC,MAAM,GAAGN,cAAc,CAACO;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/E,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,KAAK,GAAGZ,YAAY,CAACS,WAAW,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACII,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAACN,OAAO,CAACO,aAAa,GAAG,UAAUD,KAAK,EAAE;EAClD;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAACC,YAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAIC,EAAE;IACN,OAAOtC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI;QACA,MAAM;UAAE2B,OAAO;UAAEY,MAAM;UAAEC,IAAI,EAAEC;QAAa,CAAC,GAAGJ,OAAO;QACvD,IAAIK,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI;UAAEb;QAAO,CAAC,GAAGQ,OAAO;QACxB,IAAI,CAACR,MAAM,EAAE;UACTA,MAAM,GAAG,IAAI,CAACA,MAAM;QACxB;QACA,IAAIA,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;UAC5Ba,QAAQ,CAAC,UAAU,CAAC,GAAGb,MAAM;QACjC;QACA,IAAIW,IAAI;QACR,IAAIC,YAAY,KACVd,OAAO,IAAI,CAACgB,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACnB,OAAO,EAAE,cAAc,CAAC,IAAK,CAACA,OAAO,CAAC,EAAE;UAC3F,IAAK,OAAOoB,IAAI,KAAK,WAAW,IAAIN,YAAY,YAAYM,IAAI,IAC5DN,YAAY,YAAYO,WAAW,EAAE;YACrC;YACA;YACAN,QAAQ,CAAC,cAAc,CAAC,GAAG,0BAA0B;YACrDF,IAAI,GAAGC,YAAY;UACvB,CAAC,MACI,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;YACvC;YACAC,QAAQ,CAAC,cAAc,CAAC,GAAG,YAAY;YACvCF,IAAI,GAAGC,YAAY;UACvB,CAAC,MACI,IAAI,OAAOQ,QAAQ,KAAK,WAAW,IAAIR,YAAY,YAAYQ,QAAQ,EAAE;YAC1E;YACA;YACAT,IAAI,GAAGC,YAAY;UACvB,CAAC,MACI;YACD;YACAC,QAAQ,CAAC,cAAc,CAAC,GAAG,kBAAkB;YAC7CF,IAAI,GAAGU,IAAI,CAACC,SAAS,CAACV,YAAY,CAAC;UACvC;QACJ;QACA,MAAMW,QAAQ,GAAG,MAAM,IAAI,CAACrB,KAAK,CAAC,GAAG,IAAI,CAACL,GAAG,IAAIU,YAAY,EAAE,EAAE;UAC7DG,MAAM,EAAEA,MAAM,IAAI,MAAM;UACxB;UACA;UACA;UACA;UACAZ,OAAO,EAAEgB,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEX,QAAQ,CAAC,EAAE,IAAI,CAACf,OAAO,CAAC,EAAEA,OAAO,CAAC;UACzFa;QACJ,CAAC,CAAC,CAACc,KAAK,CAAEC,UAAU,IAAK;UACrB,MAAM,IAAInC,mBAAmB,CAACmC,UAAU,CAAC;QAC7C,CAAC,CAAC;QACF,MAAMC,YAAY,GAAGJ,QAAQ,CAACzB,OAAO,CAAC8B,GAAG,CAAC,eAAe,CAAC;QAC1D,IAAID,YAAY,IAAIA,YAAY,KAAK,MAAM,EAAE;UACzC,MAAM,IAAIlC,mBAAmB,CAAC8B,QAAQ,CAAC;QAC3C;QACA,IAAI,CAACA,QAAQ,CAACM,EAAE,EAAE;UACd,MAAM,IAAIrC,kBAAkB,CAAC+B,QAAQ,CAAC;QAC1C;QACA,IAAIO,YAAY,GAAG,CAAC,CAACrB,EAAE,GAAGc,QAAQ,CAACzB,OAAO,CAAC8B,GAAG,CAAC,cAAc,CAAC,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,YAAY,EAAEsB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QACnI,IAAIC,IAAI;QACR,IAAIH,YAAY,KAAK,kBAAkB,EAAE;UACrCG,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QAChC,CAAC,MACI,IAAIJ,YAAY,KAAK,0BAA0B,EAAE;UAClDG,IAAI,GAAG,MAAMV,QAAQ,CAACY,IAAI,CAAC,CAAC;QAChC,CAAC,MACI,IAAIL,YAAY,KAAK,mBAAmB,EAAE;UAC3CG,IAAI,GAAGV,QAAQ;QACnB,CAAC,MACI,IAAIO,YAAY,KAAK,qBAAqB,EAAE;UAC7CG,IAAI,GAAG,MAAMV,QAAQ,CAACa,QAAQ,CAAC,CAAC;QACpC,CAAC,MACI;UACD;UACAH,IAAI,GAAG,MAAMV,QAAQ,CAACc,IAAI,CAAC,CAAC;QAChC;QACA,OAAO;UAAEJ,IAAI;UAAEK,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,OAAO;UAAEL,IAAI,EAAE,IAAI;UAAEK;QAAM,CAAC;MAChC;IACJ,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}