{"ast": null, "code": "export const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};", "map": {"version": 3, "names": ["resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/functions-js/dist/module/helper.js"], "sourcesContent": ["export const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAIC,WAAW,IAAK;EACzC,IAAIC,MAAM;EACV,IAAID,WAAW,EAAE;IACbC,MAAM,GAAGD,WAAW;EACxB,CAAC,MACI,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACnCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KAAK,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAEH;IAAM,CAAC,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;EACrG,CAAC,MACI;IACDF,MAAM,GAAGC,KAAK;EAClB;EACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}