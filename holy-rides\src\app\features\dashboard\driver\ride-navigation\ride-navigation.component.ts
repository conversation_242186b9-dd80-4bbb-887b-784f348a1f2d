import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Ride } from '../../../../core/models/ride.model';

import { MapDisplayComponent } from '../../../../shared/components/map-display/map-display.component';
import { LocationService } from '../../../../core/services/location.service';

@Component({
  selector: 'app-ride-navigation',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MapDisplayComponent
  ],
  template: `
    <div class="navigation-overlay" *ngIf="ride">
      <mat-card class="navigation-card">
        <mat-card-header>
          <mat-card-title>Navigation</mat-card-title>
          <button mat-icon-button class="close-button" (click)="closeNavigation()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="navigation-details">
            <div class="location-info">
              <div class="location-item">
                <mat-icon class="location-icon pickup">location_on</mat-icon>
                <div class="location-text">
                  <span class="location-label">Pickup Location:</span>
                  <span class="location-value">{{ ride.pickup_location }}</span>
                </div>
              </div>
              <div class="location-item">
                <mat-icon class="location-icon dropoff">flag</mat-icon>
                <div class="location-text">
                  <span class="location-label">Dropoff Location:</span>
                  <span class="location-value">{{ ride.dropoff_location }}</span>
                </div>
              </div>
            </div>

            <app-map-display
              [origin]="ride.pickup_location"
              [destination]="ride.dropoff_location">
            </app-map-display>

            <div *ngIf="ride.distance_miles && ride.duration_minutes" class="route-info">
              <p><strong>Distance:</strong> {{ ride.distance_miles }} miles</p>
              <p><strong>Estimated Time:</strong> {{ ride.duration_minutes }} minutes</p>
            </div>

            <div class="navigation-links">
              <a [href]="googleMapsPickupUrl" target="_blank" class="nav-link">
                <button mat-raised-button color="primary">
                  <mat-icon>navigation</mat-icon>
                  Navigate to Pickup
                </button>
              </a>
              <a [href]="googleMapsDropoffUrl" target="_blank" class="nav-link">
                <button mat-raised-button color="accent">
                  <mat-icon>navigation</mat-icon>
                  Navigate to Dropoff
                </button>
              </a>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .navigation-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .navigation-card {
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
    }

    .close-button {
      position: absolute;
      right: 8px;
      top: 8px;
    }

    .navigation-details {
      padding: 16px 0;
    }

    .location-info {
      margin-bottom: 24px;
    }

    .location-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .location-icon {
      margin-right: 16px;
      color: #3f51b5;
    }

    .location-icon.pickup {
      color: #4caf50;
    }

    .location-icon.dropoff {
      color: #f44336;
    }

    .location-text {
      display: flex;
      flex-direction: column;
    }

    .location-label {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .location-value {
      color: #666;
    }

    .route-info {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 16px;
      margin-bottom: 24px;
    }

    .route-info p {
      margin: 8px 0;
    }

    .navigation-links {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      gap: 16px;
    }

    .nav-link {
      text-decoration: none;
    }
  `]
})
export class RideNavigationComponent implements OnInit {
  @Input() ride: Ride | null = null;
  @Output() close = new EventEmitter<void>();

  googleMapsPickupUrl: string = '';
  googleMapsDropoffUrl: string = '';

  constructor(private locationService: LocationService) {}

  ngOnInit(): void {
    this.generateNavigationLinks();
  }

  generateNavigationLinks(): void {
    if (!this.ride) return;

    // Create Google Maps navigation links using the LocationService
    this.googleMapsPickupUrl = this.locationService.getGoogleMapsUrl(this.ride.pickup_location);
    this.googleMapsDropoffUrl = this.locationService.getGoogleMapsUrl(this.ride.dropoff_location);
  }

  closeNavigation(): void {
    this.close.emit();
  }
}
