import { Component, <PERSON><PERSON>ni<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-install-prompt',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div *ngIf="showInstallPrompt" class="install-container">
      <mat-card class="install-card">
        <mat-card-content>
          <div class="install-content">
            <mat-icon>get_app</mat-icon>
            <div class="install-text">
              <h3>Install Holy Rides</h3>
              <p>Install this app on your device for a better experience</p>
            </div>
          </div>
          <div class="install-actions">
            <button mat-button color="primary" (click)="installApp()">
              Install
            </button>
            <button mat-button (click)="dismissPrompt()">
              Not Now
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .install-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 999;
      padding: 16px;
    }

    .install-card {
      background-color: white;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }

    .install-content {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .install-text {
      margin-left: 16px;
    }

    .install-text h3 {
      margin: 0;
      font-size: 18px;
    }

    .install-text p {
      margin: 4px 0 0;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }

    .install-actions {
      display: flex;
      justify-content: flex-end;
    }

    mat-icon {
      font-size: 36px;
      height: 36px;
      width: 36px;
      color: #3f51b5;
    }
  `]
})
export class InstallPromptComponent implements OnInit, OnDestroy {
  showInstallPrompt = false;
  deferredPrompt: any = null;

  constructor(private snackBar: MatSnackBar) {}

  private beforeInstallPromptHandler = (e: any) => {
    // Prevent the default browser install prompt
    e.preventDefault();
    // Save the event so it can be triggered later
    this.deferredPrompt = e;
    // Show our custom install prompt
    this.showInstallPrompt = true;
  };

  private appInstalledHandler = () => {
    this.showInstallPrompt = false;
    this.deferredPrompt = null;
    this.snackBar.open('Holy Rides has been installed successfully!', 'Dismiss', {
      duration: 3000
    });
  };

  ngOnInit(): void {
    // Listen for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', this.beforeInstallPromptHandler);

    // Hide the prompt if the app is installed
    window.addEventListener('appinstalled', this.appInstalledHandler);

    // Check if the prompt was recently dismissed
    const dismissedTime = localStorage.getItem('installPromptDismissed');
    if (dismissedTime) {
      const now = Date.now();
      const dismissed = parseInt(dismissedTime, 10);
      // Don't show the prompt if it was dismissed less than 24 hours ago
      if (now - dismissed < 24 * 60 * 60 * 1000) {
        this.showInstallPrompt = false;
      }
    }
  }

  ngOnDestroy(): void {
    // Clean up event listeners
    window.removeEventListener('beforeinstallprompt', this.beforeInstallPromptHandler);
    window.removeEventListener('appinstalled', this.appInstalledHandler);
  }

  installApp(): void {
    if (!this.deferredPrompt) {
      return;
    }

    // Show the browser install prompt
    this.deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    this.deferredPrompt.userChoice.then((choiceResult: { outcome: string }) => {
      if (choiceResult.outcome === 'accepted') {
        this.snackBar.open('Thank you for installing Holy Rides!', 'Dismiss', {
          duration: 3000
        });
      }
      // Reset the deferred prompt variable
      this.deferredPrompt = null;
      this.showInstallPrompt = false;
    });
  }

  dismissPrompt(): void {
    this.showInstallPrompt = false;
    // Store in localStorage that the user dismissed the prompt
    // so we don't show it again too soon
    localStorage.setItem('installPromptDismissed', Date.now().toString());
  }
}
