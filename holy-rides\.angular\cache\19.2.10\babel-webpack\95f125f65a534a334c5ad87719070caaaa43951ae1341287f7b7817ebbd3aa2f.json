{"ast": null, "code": "// generated by genversion\nexport const version = '2.7.1';", "map": {"version": 3, "names": ["version"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/lib/version.js"], "sourcesContent": ["// generated by genversion\nexport const version = '2.7.1';\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,OAAO,GAAG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}