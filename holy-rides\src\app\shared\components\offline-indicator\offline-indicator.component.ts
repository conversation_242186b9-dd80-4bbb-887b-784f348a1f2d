import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { OnlineStatusService } from '../../../core/services/online-status.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-offline-indicator',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule, MatButtonModule],
  template: `
    <div *ngIf="!isOnline" class="offline-container">
      <mat-card class="offline-card">
        <mat-card-content>
          <mat-icon>cloud_off</mat-icon>
          <p>You are currently offline</p>
          <p class="offline-subtitle">Some features may be limited</p>
          <button mat-button color="primary" (click)="checkConnection()">
            Try Again
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .offline-container {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      padding: 16px;
    }

    .offline-card {
      background-color: #f44336;
      color: white;
      text-align: center;
    }

    .offline-subtitle {
      font-size: 14px;
      opacity: 0.8;
    }

    mat-icon {
      font-size: 48px;
      height: 48px;
      width: 48px;
      margin-bottom: 8px;
    }
  `]
})
export class OfflineIndicatorComponent implements OnInit, OnDestroy {
  isOnline = true;
  private subscription: Subscription | null = null;

  constructor(private onlineStatusService: OnlineStatusService) {}

  ngOnInit(): void {
    // Initialize with current online status
    this.isOnline = navigator.onLine;

    // Subscribe to online status changes
    try {
      this.subscription = this.onlineStatusService.getOnlineStatus().subscribe(
        online => {
          this.isOnline = online;
        }
      );
    } catch (error) {
      console.error('Error subscribing to online status:', error);
    }
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  checkConnection(): void {
    this.isOnline = navigator.onLine;
  }
}
