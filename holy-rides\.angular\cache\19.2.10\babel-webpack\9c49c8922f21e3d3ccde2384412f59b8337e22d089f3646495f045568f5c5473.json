{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n  _appRef;\n  _injector = inject(Injector);\n  _environmentInjector = inject(EnvironmentInjector);\n  /**\n   * Loads a set of styles.\n   * @param loader Component which will be instantiated to load the styles.\n   */\n  load(loader) {\n    // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n    const appRef = this._appRef = this._appRef || this._injector.get(ApplicationRef);\n    let data = appsWithLoaders.get(appRef);\n    // If we haven't loaded for this app before, we have to initialize it.\n    if (!data) {\n      data = {\n        loaders: new Set(),\n        refs: []\n      };\n      appsWithLoaders.set(appRef, data);\n      // When the app is destroyed, we need to clean up all the related loaders.\n      appRef.onDestroy(() => {\n        appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n        appsWithLoaders.delete(appRef);\n      });\n    }\n    // If the loader hasn't been loaded before, we need to instatiate it.\n    if (!data.loaders.has(loader)) {\n      data.loaders.add(loader);\n      data.refs.push(createComponent(loader, {\n        environmentInjector: this._environmentInjector\n      }));\n    }\n  }\n  static ɵfac = function _CdkPrivateStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkPrivateStyleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _CdkPrivateStyleLoader,\n    factory: _CdkPrivateStyleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkPrivateStyleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _CdkPrivateStyleLoader as _ };", "map": {"version": 3, "names": ["i0", "inject", "Injector", "EnvironmentInjector", "ApplicationRef", "createComponent", "Injectable", "appsWithLoaders", "WeakMap", "_CdkPrivateStyleLoader", "_appRef", "_injector", "_environmentInjector", "load", "loader", "appRef", "get", "data", "loaders", "Set", "refs", "set", "onDestroy", "for<PERSON>ach", "ref", "destroy", "delete", "has", "add", "push", "environmentInjector", "ɵfac", "_CdkPrivateStyleLoader_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "_"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/style-loader-Cu9AvjH9.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injector, EnvironmentInjector, ApplicationRef, createComponent, Injectable } from '@angular/core';\n\n/** Apps in which we've loaded styles. */\nconst appsWithLoaders = new WeakMap();\n/**\n * Service that loads structural styles dynamically\n * and ensures that they're only loaded once per app.\n */\nclass _CdkPrivateStyleLoader {\n    _appRef;\n    _injector = inject(Injector);\n    _environmentInjector = inject(EnvironmentInjector);\n    /**\n     * Loads a set of styles.\n     * @param loader Component which will be instantiated to load the styles.\n     */\n    load(loader) {\n        // Resolve the app ref lazily to avoid circular dependency errors if this is called too early.\n        const appRef = (this._appRef = this._appRef || this._injector.get(ApplicationRef));\n        let data = appsWithLoaders.get(appRef);\n        // If we haven't loaded for this app before, we have to initialize it.\n        if (!data) {\n            data = { loaders: new Set(), refs: [] };\n            appsWithLoaders.set(appRef, data);\n            // When the app is destroyed, we need to clean up all the related loaders.\n            appRef.onDestroy(() => {\n                appsWithLoaders.get(appRef)?.refs.forEach(ref => ref.destroy());\n                appsWithLoaders.delete(appRef);\n            });\n        }\n        // If the loader hasn't been loaded before, we need to instatiate it.\n        if (!data.loaders.has(loader)) {\n            data.loaders.add(loader);\n            data.refs.push(createComponent(loader, { environmentInjector: this._environmentInjector }));\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkPrivateStyleLoader, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { _CdkPrivateStyleLoader as _ };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,UAAU,QAAQ,eAAe;;AAElH;AACA,MAAMC,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBC,OAAO;EACPC,SAAS,GAAGV,MAAM,CAACC,QAAQ,CAAC;EAC5BU,oBAAoB,GAAGX,MAAM,CAACE,mBAAmB,CAAC;EAClD;AACJ;AACA;AACA;EACIU,IAAIA,CAACC,MAAM,EAAE;IACT;IACA,MAAMC,MAAM,GAAI,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACC,SAAS,CAACK,GAAG,CAACZ,cAAc,CAAE;IAClF,IAAIa,IAAI,GAAGV,eAAe,CAACS,GAAG,CAACD,MAAM,CAAC;IACtC;IACA,IAAI,CAACE,IAAI,EAAE;MACPA,IAAI,GAAG;QAAEC,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAG,CAAC;MACvCb,eAAe,CAACc,GAAG,CAACN,MAAM,EAAEE,IAAI,CAAC;MACjC;MACAF,MAAM,CAACO,SAAS,CAAC,MAAM;QACnBf,eAAe,CAACS,GAAG,CAACD,MAAM,CAAC,EAAEK,IAAI,CAACG,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QAC/DlB,eAAe,CAACmB,MAAM,CAACX,MAAM,CAAC;MAClC,CAAC,CAAC;IACN;IACA;IACA,IAAI,CAACE,IAAI,CAACC,OAAO,CAACS,GAAG,CAACb,MAAM,CAAC,EAAE;MAC3BG,IAAI,CAACC,OAAO,CAACU,GAAG,CAACd,MAAM,CAAC;MACxBG,IAAI,CAACG,IAAI,CAACS,IAAI,CAACxB,eAAe,CAACS,MAAM,EAAE;QAAEgB,mBAAmB,EAAE,IAAI,CAAClB;MAAqB,CAAC,CAAC,CAAC;IAC/F;EACJ;EACA,OAAOmB,IAAI,YAAAC,+BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFxB,sBAAsB;EAAA;EACzH,OAAOyB,KAAK,kBAD6ElC,EAAE,CAAAmC,kBAAA;IAAAC,KAAA,EACY3B,sBAAsB;IAAA4B,OAAA,EAAtB5B,sBAAsB,CAAAsB,IAAA;IAAAO,UAAA,EAAc;EAAM;AACrJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FvC,EAAE,CAAAwC,iBAAA,CAGJ/B,sBAAsB,EAAc,CAAC;IACpHgC,IAAI,EAAEnC,UAAU;IAChBoC,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAAS7B,sBAAsB,IAAIkC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}