{"ast": null, "code": "/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵnormalizeQueryParams as _normalizeQueryParams, LocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Inject, Optional, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { PlatformNavigation } from './platform_navigation-B45Jeakb.mjs';\nimport { ɵFakeNavigation as _FakeNavigation } from '@angular/core/testing';\nexport { ɵFakeNavigation } from '@angular/core/testing';\nimport { PlatformLocation, Location, LocationStrategy as LocationStrategy$1 } from './location-DpBxd_aX.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase;\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl;\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8]\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: !serverBase && parsedUrl.hostname || '',\n    protocol: !serverBase && parsedUrl.protocol || '',\n    port: !serverBase && parsedUrl.port || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || ''\n  };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n  baseHref = '';\n  hashUpdate = new Subject();\n  popStateSubject = new Subject();\n  urlChangeIndex = 0;\n  urlChanges = [{\n    hostname: '',\n    protocol: '',\n    port: '',\n    pathname: '/',\n    search: '',\n    hash: '',\n    state: null\n  }];\n  constructor(config) {\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n      const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n      this.urlChanges[0] = {\n        ...parsedChanges\n      };\n    }\n  }\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n  getBaseHrefFromDOM() {\n    return this.baseHref;\n  }\n  onPopState(fn) {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  onHashChange(fn) {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  get href() {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n  get url() {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n  parseChanges(state, url, baseHref = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {\n      ...parseUrl(url, baseHref),\n      state\n    };\n  }\n  replaceState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    };\n  }\n  pushState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n  forward() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  back() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  historyGo(relativePosition = 0) {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  getState() {\n    return this.state;\n  }\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  emitEvents(oldHash, oldUrl) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url\n    });\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url\n      });\n    }\n  }\n  static ɵfac = function MockPlatformLocation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MockPlatformLocation)(i0.ɵɵinject(MOCK_PLATFORM_LOCATION_CONFIG, 8));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MockPlatformLocation,\n    factory: MockPlatformLocation.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockPlatformLocation, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MOCK_PLATFORM_LOCATION_CONFIG]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n/**\n * Mock implementation of URL state.\n */\nclass FakeNavigationPlatformLocation {\n  _platformNavigation;\n  constructor() {\n    const platformNavigation = inject(PlatformNavigation);\n    if (!(platformNavigation instanceof _FakeNavigation)) {\n      throw new Error('FakePlatformNavigation cannot be used without FakeNavigation. Use ' + '`provideFakeNavigation` to have all these services provided together.');\n    }\n    this._platformNavigation = platformNavigation;\n  }\n  config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {\n    optional: true\n  });\n  getBaseHrefFromDOM() {\n    return this.config?.appBaseHref ?? '';\n  }\n  onPopState(fn) {\n    this._platformNavigation.window.addEventListener('popstate', fn);\n    return () => this._platformNavigation.window.removeEventListener('popstate', fn);\n  }\n  onHashChange(fn) {\n    this._platformNavigation.window.addEventListener('hashchange', fn);\n    return () => this._platformNavigation.window.removeEventListener('hashchange', fn);\n  }\n  get href() {\n    return this._platformNavigation.currentEntry.url;\n  }\n  get protocol() {\n    return new URL(this._platformNavigation.currentEntry.url).protocol;\n  }\n  get hostname() {\n    return new URL(this._platformNavigation.currentEntry.url).hostname;\n  }\n  get port() {\n    return new URL(this._platformNavigation.currentEntry.url).port;\n  }\n  get pathname() {\n    return new URL(this._platformNavigation.currentEntry.url).pathname;\n  }\n  get search() {\n    return new URL(this._platformNavigation.currentEntry.url).search;\n  }\n  get hash() {\n    return new URL(this._platformNavigation.currentEntry.url).hash;\n  }\n  pushState(state, title, url) {\n    this._platformNavigation.pushState(state, title, url);\n  }\n  replaceState(state, title, url) {\n    this._platformNavigation.replaceState(state, title, url);\n  }\n  forward() {\n    this._platformNavigation.forward();\n  }\n  back() {\n    this._platformNavigation.back();\n  }\n  historyGo(relativePosition = 0) {\n    this._platformNavigation.go(relativePosition);\n  }\n  getState() {\n    return this._platformNavigation.currentEntry.getHistoryState();\n  }\n  static ɵfac = function FakeNavigationPlatformLocation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FakeNavigationPlatformLocation)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FakeNavigationPlatformLocation,\n    factory: FakeNavigationPlatformLocation.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FakeNavigationPlatformLocation, [{\n    type: Injectable\n  }], () => [], null);\n})();\nconst FAKE_NAVIGATION = new InjectionToken('fakeNavigation', {\n  providedIn: 'root',\n  factory: () => {\n    const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {\n      optional: true\n    });\n    const baseFallback = 'http://_empty_/';\n    const startUrl = new URL(config?.startUrl || baseFallback, baseFallback);\n    // TODO(atscott): If we want to replace MockPlatformLocation with FakeNavigationPlatformLocation\n    // as the default in TestBed, we will likely need to use setSynchronousTraversalsForTesting(true);\n    return new _FakeNavigation(inject(DOCUMENT), startUrl.href);\n  }\n});\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nfunction provideFakePlatformNavigation() {\n  return [{\n    provide: PlatformNavigation,\n    useFactory: () => inject(FAKE_NAVIGATION)\n  }, {\n    provide: PlatformLocation,\n    useClass: FakeNavigationPlatformLocation\n  }];\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n  urlChanges = [];\n  _history = [new LocationState('', '', null)];\n  _historyIndex = 0;\n  /** @internal */\n  _subject = new Subject();\n  /** @internal */\n  _basePath = '';\n  /** @internal */\n  _locationStrategy = null;\n  /** @internal */\n  _urlChangeListeners = [];\n  /** @internal */\n  _urlChangeSubscription = null;\n  /** @nodoc */\n  ngOnDestroy() {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  setInitialPath(url) {\n    this._history[this._historyIndex].path = url;\n  }\n  setBaseHref(url) {\n    this._basePath = url;\n  }\n  path() {\n    return this._history[this._historyIndex].path;\n  }\n  getState() {\n    return this._history[this._historyIndex].state;\n  }\n  isCurrentPathEqualTo(path, query = '') {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n  simulateUrlPop(pathname) {\n    this._subject.next({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n  }\n  simulateHashChange(pathname) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.next({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n    this._subject.next({\n      'url': pathname,\n      'pop': true,\n      'type': 'hashchange'\n    });\n  }\n  prepareExternalUrl(url) {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n  go(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    this.pushHistory(path, query, state);\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + _normalizeQueryParams(query), state);\n  }\n  replaceState(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    const history = this._history[this._historyIndex];\n    history.state = state;\n    if (history.path == path && history.query == query) {\n      return;\n    }\n    history.path = path;\n    history.query = query;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + _normalizeQueryParams(query), state);\n  }\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  historyGo(relativePosition = 0) {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.next({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  onUrlChange(fn) {\n    this._urlChangeListeners.push(fn);\n    this._urlChangeSubscription ??= this.subscribe(v => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow ?? undefined,\n      complete: onReturn ?? undefined\n    });\n  }\n  normalize(url) {\n    return null;\n  }\n  pushHistory(path, query, state) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n  static ɵfac = function SpyLocation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SpyLocation)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SpyLocation,\n    factory: SpyLocation.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SpyLocation, [{\n    type: Injectable\n  }], null, null);\n})();\nclass LocationState {\n  path;\n  query;\n  state;\n  constructor(path, query, state) {\n    this.path = path;\n    this.query = query;\n    this.state = state;\n  }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n  internalBaseHref = '/';\n  internalPath = '/';\n  internalTitle = '';\n  urlChanges = [];\n  /** @internal */\n  _subject = new Subject();\n  stateChanges = [];\n  constructor() {\n    super();\n  }\n  simulatePopState(url) {\n    this.internalPath = url;\n    this._subject.next(new _MockPopStateEvent(this.path()));\n  }\n  path(includeHash = false) {\n    return this.internalPath;\n  }\n  prepareExternalUrl(internal) {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n  pushState(ctx, title, path, query) {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n  replaceState(ctx, title, path, query) {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n  onPopState(fn) {\n    this._subject.subscribe({\n      next: fn\n    });\n  }\n  getBaseHref() {\n    return this.internalBaseHref;\n  }\n  back() {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n  forward() {\n    throw 'not implemented';\n  }\n  getState() {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n  static ɵfac = function MockLocationStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MockLocationStrategy)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MockLocationStrategy,\n    factory: MockLocationStrategy.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockLocationStrategy, [{\n    type: Injectable\n  }], () => [], null);\n})();\nclass _MockPopStateEvent {\n  newUrl;\n  pop = true;\n  type = 'popstate';\n  constructor(newUrl) {\n    this.newUrl = newUrl;\n  }\n}\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n  return [{\n    provide: Location,\n    useClass: SpyLocation\n  }, {\n    provide: LocationStrategy$1,\n    useClass: MockLocationStrategy\n  }];\n}\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks, provideFakePlatformNavigation as ɵprovideFakePlatformNavigation };", "map": {"version": 3, "names": ["ɵnormalizeQueryParams", "_normalizeQueryParams", "LocationStrategy", "i0", "InjectionToken", "inject", "Inject", "Optional", "Injectable", "Subject", "PlatformNavigation", "ɵFakeNavigation", "_FakeNavigation", "PlatformLocation", "Location", "LocationStrategy$1", "DOCUMENT", "urlParse", "parseUrl", "urlStr", "baseHref", "verifyProtocol", "serverBase", "test", "parsedUrl", "URL", "e", "result", "exec", "Error", "hostSplit", "split", "protocol", "hostname", "port", "pathname", "search", "hash", "indexOf", "substring", "length", "MOCK_PLATFORM_LOCATION_CONFIG", "MockPlatformLocation", "hashUpdate", "popStateSubject", "urlChangeIndex", "url<PERSON><PERSON><PERSON>", "state", "constructor", "config", "appBaseHref", "parsedChanges", "parseChanges", "startUrl", "getBaseHrefFromDOM", "onPopState", "fn", "subscription", "subscribe", "unsubscribe", "onHashChange", "href", "url", "JSON", "parse", "stringify", "replaceState", "title", "newUrl", "parsedState", "pushState", "splice", "push", "forward", "oldUrl", "oldHash", "emitEvents", "back", "historyGo", "relativePosition", "nextPageIndex", "getState", "next", "type", "ɵfac", "MockPlatformLocation_Factory", "__ngFactoryType__", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "undefined", "decorators", "args", "FakeNavigationPlatformLocation", "_platformNavigation", "platformNavigation", "optional", "window", "addEventListener", "removeEventListener", "currentEntry", "go", "getHistoryState", "FakeNavigationPlatformLocation_Factory", "FAKE_NAVIGATION", "providedIn", "<PERSON><PERSON><PERSON><PERSON>", "provideFakePlatformNavigation", "provide", "useFactory", "useClass", "SpyLocation", "_history", "LocationState", "_historyIndex", "_subject", "_basePath", "_locationStrategy", "_urlChangeListeners", "_urlChangeSubscription", "ngOnDestroy", "setInitialPath", "path", "setBaseHref", "isCurrentPathEqualTo", "query", "<PERSON><PERSON><PERSON>", "endsWith", "currPath", "simulateUrlPop", "simulateHashChange", "prepareExternalUrl", "pushHistory", "startsWith", "locationState", "_notifyUrlChangeListeners", "history", "onUrlChange", "v", "fnIndex", "for<PERSON>ach", "onNext", "onThrow", "onReturn", "error", "complete", "normalize", "SpyLocation_Factory", "MockLocationStrategy", "internalBaseHref", "internalPath", "internalTitle", "stateChanges", "simulatePopState", "_MockPopStateEvent", "includeHash", "internal", "ctx", "externalUrl", "getBaseHref", "pop", "nextUrl", "MockLocationStrategy_Factory", "provideLocationMocks", "ɵprovideFakePlatformNavigation"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/common/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵnormalizeQueryParams as _normalizeQueryParams, LocationStrategy } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Inject, Optional, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { PlatformNavigation } from './platform_navigation-B45Jeakb.mjs';\nimport { ɵFakeNavigation as _FakeNavigation } from '@angular/core/testing';\nexport { ɵFakeNavigation } from '@angular/core/testing';\nimport { PlatformLocation, Location, LocationStrategy as LocationStrategy$1 } from './location-DpBxd_aX.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n    const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n    let serverBase;\n    // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n    // an arbitrary base URL which can be removed afterward.\n    if (!verifyProtocol.test(urlStr)) {\n        serverBase = 'http://empty.com/';\n    }\n    let parsedUrl;\n    try {\n        parsedUrl = new URL(urlStr, serverBase);\n    }\n    catch (e) {\n        const result = urlParse.exec(serverBase || '' + urlStr);\n        if (!result) {\n            throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n        }\n        const hostSplit = result[4].split(':');\n        parsedUrl = {\n            protocol: result[1],\n            hostname: hostSplit[0],\n            port: hostSplit[1] || '',\n            pathname: result[5],\n            search: result[6],\n            hash: result[8],\n        };\n    }\n    if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n        parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n    }\n    return {\n        hostname: (!serverBase && parsedUrl.hostname) || '',\n        protocol: (!serverBase && parsedUrl.protocol) || '',\n        port: (!serverBase && parsedUrl.port) || '',\n        pathname: parsedUrl.pathname || '/',\n        search: parsedUrl.search || '',\n        hash: parsedUrl.hash || '',\n    };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n    baseHref = '';\n    hashUpdate = new Subject();\n    popStateSubject = new Subject();\n    urlChangeIndex = 0;\n    urlChanges = [{ hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null }];\n    constructor(config) {\n        if (config) {\n            this.baseHref = config.appBaseHref || '';\n            const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n            this.urlChanges[0] = { ...parsedChanges };\n        }\n    }\n    get hostname() {\n        return this.urlChanges[this.urlChangeIndex].hostname;\n    }\n    get protocol() {\n        return this.urlChanges[this.urlChangeIndex].protocol;\n    }\n    get port() {\n        return this.urlChanges[this.urlChangeIndex].port;\n    }\n    get pathname() {\n        return this.urlChanges[this.urlChangeIndex].pathname;\n    }\n    get search() {\n        return this.urlChanges[this.urlChangeIndex].search;\n    }\n    get hash() {\n        return this.urlChanges[this.urlChangeIndex].hash;\n    }\n    get state() {\n        return this.urlChanges[this.urlChangeIndex].state;\n    }\n    getBaseHrefFromDOM() {\n        return this.baseHref;\n    }\n    onPopState(fn) {\n        const subscription = this.popStateSubject.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    onHashChange(fn) {\n        const subscription = this.hashUpdate.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    get href() {\n        let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n        url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n        return url;\n    }\n    get url() {\n        return `${this.pathname}${this.search}${this.hash}`;\n    }\n    parseChanges(state, url, baseHref = '') {\n        // When the `history.state` value is stored, it is always copied.\n        state = JSON.parse(JSON.stringify(state));\n        return { ...parseUrl(url, baseHref), state };\n    }\n    replaceState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        this.urlChanges[this.urlChangeIndex] = {\n            ...this.urlChanges[this.urlChangeIndex],\n            pathname,\n            search,\n            hash,\n            state: parsedState,\n        };\n    }\n    pushState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        if (this.urlChangeIndex > 0) {\n            this.urlChanges.splice(this.urlChangeIndex + 1);\n        }\n        this.urlChanges.push({\n            ...this.urlChanges[this.urlChangeIndex],\n            pathname,\n            search,\n            hash,\n            state: parsedState,\n        });\n        this.urlChangeIndex = this.urlChanges.length - 1;\n    }\n    forward() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex < this.urlChanges.length) {\n            this.urlChangeIndex++;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    back() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex > 0) {\n            this.urlChangeIndex--;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    historyGo(relativePosition = 0) {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        const nextPageIndex = this.urlChangeIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n            this.urlChangeIndex = nextPageIndex;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    getState() {\n        return this.state;\n    }\n    /**\n     * Browsers are inconsistent in when they fire events and perform the state updates\n     * The most easiest thing to do in our mock is synchronous and that happens to match\n     * Firefox and Chrome, at least somewhat closely\n     *\n     * https://github.com/WICG/navigation-api#watching-for-navigations\n     * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n     * popstate is always sent before hashchange:\n     * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n     */\n    emitEvents(oldHash, oldUrl) {\n        this.popStateSubject.next({\n            type: 'popstate',\n            state: this.getState(),\n            oldUrl,\n            newUrl: this.url,\n        });\n        if (oldHash !== this.hash) {\n            this.hashUpdate.next({\n                type: 'hashchange',\n                state: null,\n                oldUrl,\n                newUrl: this.url,\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockPlatformLocation, deps: [{ token: MOCK_PLATFORM_LOCATION_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockPlatformLocation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockPlatformLocation, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MOCK_PLATFORM_LOCATION_CONFIG]\n                }, {\n                    type: Optional\n                }] }] });\n/**\n * Mock implementation of URL state.\n */\nclass FakeNavigationPlatformLocation {\n    _platformNavigation;\n    constructor() {\n        const platformNavigation = inject(PlatformNavigation);\n        if (!(platformNavigation instanceof _FakeNavigation)) {\n            throw new Error('FakePlatformNavigation cannot be used without FakeNavigation. Use ' +\n                '`provideFakeNavigation` to have all these services provided together.');\n        }\n        this._platformNavigation = platformNavigation;\n    }\n    config = inject(MOCK_PLATFORM_LOCATION_CONFIG, { optional: true });\n    getBaseHrefFromDOM() {\n        return this.config?.appBaseHref ?? '';\n    }\n    onPopState(fn) {\n        this._platformNavigation.window.addEventListener('popstate', fn);\n        return () => this._platformNavigation.window.removeEventListener('popstate', fn);\n    }\n    onHashChange(fn) {\n        this._platformNavigation.window.addEventListener('hashchange', fn);\n        return () => this._platformNavigation.window.removeEventListener('hashchange', fn);\n    }\n    get href() {\n        return this._platformNavigation.currentEntry.url;\n    }\n    get protocol() {\n        return new URL(this._platformNavigation.currentEntry.url).protocol;\n    }\n    get hostname() {\n        return new URL(this._platformNavigation.currentEntry.url).hostname;\n    }\n    get port() {\n        return new URL(this._platformNavigation.currentEntry.url).port;\n    }\n    get pathname() {\n        return new URL(this._platformNavigation.currentEntry.url).pathname;\n    }\n    get search() {\n        return new URL(this._platformNavigation.currentEntry.url).search;\n    }\n    get hash() {\n        return new URL(this._platformNavigation.currentEntry.url).hash;\n    }\n    pushState(state, title, url) {\n        this._platformNavigation.pushState(state, title, url);\n    }\n    replaceState(state, title, url) {\n        this._platformNavigation.replaceState(state, title, url);\n    }\n    forward() {\n        this._platformNavigation.forward();\n    }\n    back() {\n        this._platformNavigation.back();\n    }\n    historyGo(relativePosition = 0) {\n        this._platformNavigation.go(relativePosition);\n    }\n    getState() {\n        return this._platformNavigation.currentEntry.getHistoryState();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FakeNavigationPlatformLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FakeNavigationPlatformLocation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: FakeNavigationPlatformLocation, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\nconst FAKE_NAVIGATION = new InjectionToken('fakeNavigation', {\n    providedIn: 'root',\n    factory: () => {\n        const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, { optional: true });\n        const baseFallback = 'http://_empty_/';\n        const startUrl = new URL(config?.startUrl || baseFallback, baseFallback);\n        // TODO(atscott): If we want to replace MockPlatformLocation with FakeNavigationPlatformLocation\n        // as the default in TestBed, we will likely need to use setSynchronousTraversalsForTesting(true);\n        return new _FakeNavigation(inject(DOCUMENT), startUrl.href);\n    },\n});\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nfunction provideFakePlatformNavigation() {\n    return [\n        {\n            provide: PlatformNavigation,\n            useFactory: () => inject(FAKE_NAVIGATION),\n        },\n        { provide: PlatformLocation, useClass: FakeNavigationPlatformLocation },\n    ];\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n    urlChanges = [];\n    _history = [new LocationState('', '', null)];\n    _historyIndex = 0;\n    /** @internal */\n    _subject = new Subject();\n    /** @internal */\n    _basePath = '';\n    /** @internal */\n    _locationStrategy = null;\n    /** @internal */\n    _urlChangeListeners = [];\n    /** @internal */\n    _urlChangeSubscription = null;\n    /** @nodoc */\n    ngOnDestroy() {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeListeners = [];\n    }\n    setInitialPath(url) {\n        this._history[this._historyIndex].path = url;\n    }\n    setBaseHref(url) {\n        this._basePath = url;\n    }\n    path() {\n        return this._history[this._historyIndex].path;\n    }\n    getState() {\n        return this._history[this._historyIndex].state;\n    }\n    isCurrentPathEqualTo(path, query = '') {\n        const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n        const currPath = this.path().endsWith('/')\n            ? this.path().substring(0, this.path().length - 1)\n            : this.path();\n        return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n    }\n    simulateUrlPop(pathname) {\n        this._subject.next({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n    }\n    simulateHashChange(pathname) {\n        const path = this.prepareExternalUrl(pathname);\n        this.pushHistory(path, '', null);\n        this.urlChanges.push('hash: ' + pathname);\n        // the browser will automatically fire popstate event before each `hashchange` event, so we need\n        // to simulate it.\n        this._subject.next({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n        this._subject.next({ 'url': pathname, 'pop': true, 'type': 'hashchange' });\n    }\n    prepareExternalUrl(url) {\n        if (url.length > 0 && !url.startsWith('/')) {\n            url = '/' + url;\n        }\n        return this._basePath + url;\n    }\n    go(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        this.pushHistory(path, query, state);\n        const locationState = this._history[this._historyIndex - 1];\n        if (locationState.path == path && locationState.query == query) {\n            return;\n        }\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.urlChanges.push(url);\n        this._notifyUrlChangeListeners(path + _normalizeQueryParams(query), state);\n    }\n    replaceState(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        const history = this._history[this._historyIndex];\n        history.state = state;\n        if (history.path == path && history.query == query) {\n            return;\n        }\n        history.path = path;\n        history.query = query;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.urlChanges.push('replace: ' + url);\n        this._notifyUrlChangeListeners(path + _normalizeQueryParams(query), state);\n    }\n    forward() {\n        if (this._historyIndex < this._history.length - 1) {\n            this._historyIndex++;\n            this._subject.next({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    back() {\n        if (this._historyIndex > 0) {\n            this._historyIndex--;\n            this._subject.next({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    historyGo(relativePosition = 0) {\n        const nextPageIndex = this._historyIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n            this._historyIndex = nextPageIndex;\n            this._subject.next({\n                'url': this.path(),\n                'state': this.getState(),\n                'pop': true,\n                'type': 'popstate',\n            });\n        }\n    }\n    onUrlChange(fn) {\n        this._urlChangeListeners.push(fn);\n        this._urlChangeSubscription ??= this.subscribe((v) => {\n            this._notifyUrlChangeListeners(v.url, v.state);\n        });\n        return () => {\n            const fnIndex = this._urlChangeListeners.indexOf(fn);\n            this._urlChangeListeners.splice(fnIndex, 1);\n            if (this._urlChangeListeners.length === 0) {\n                this._urlChangeSubscription?.unsubscribe();\n                this._urlChangeSubscription = null;\n            }\n        };\n    }\n    /** @internal */\n    _notifyUrlChangeListeners(url = '', state) {\n        this._urlChangeListeners.forEach((fn) => fn(url, state));\n    }\n    subscribe(onNext, onThrow, onReturn) {\n        return this._subject.subscribe({\n            next: onNext,\n            error: onThrow ?? undefined,\n            complete: onReturn ?? undefined,\n        });\n    }\n    normalize(url) {\n        return null;\n    }\n    pushHistory(path, query, state) {\n        if (this._historyIndex > 0) {\n            this._history.splice(this._historyIndex + 1);\n        }\n        this._history.push(new LocationState(path, query, state));\n        this._historyIndex = this._history.length - 1;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: SpyLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: SpyLocation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: SpyLocation, decorators: [{\n            type: Injectable\n        }] });\nclass LocationState {\n    path;\n    query;\n    state;\n    constructor(path, query, state) {\n        this.path = path;\n        this.query = query;\n        this.state = state;\n    }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n    internalBaseHref = '/';\n    internalPath = '/';\n    internalTitle = '';\n    urlChanges = [];\n    /** @internal */\n    _subject = new Subject();\n    stateChanges = [];\n    constructor() {\n        super();\n    }\n    simulatePopState(url) {\n        this.internalPath = url;\n        this._subject.next(new _MockPopStateEvent(this.path()));\n    }\n    path(includeHash = false) {\n        return this.internalPath;\n    }\n    prepareExternalUrl(internal) {\n        if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n            return this.internalBaseHref + internal.substring(1);\n        }\n        return this.internalBaseHref + internal;\n    }\n    pushState(ctx, title, path, query) {\n        // Add state change to changes array\n        this.stateChanges.push(ctx);\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push(externalUrl);\n    }\n    replaceState(ctx, title, path, query) {\n        // Reset the last index of stateChanges to the ctx (state) object\n        this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? '?' + query : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push('replace: ' + externalUrl);\n    }\n    onPopState(fn) {\n        this._subject.subscribe({ next: fn });\n    }\n    getBaseHref() {\n        return this.internalBaseHref;\n    }\n    back() {\n        if (this.urlChanges.length > 0) {\n            this.urlChanges.pop();\n            this.stateChanges.pop();\n            const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n            this.simulatePopState(nextUrl);\n        }\n    }\n    forward() {\n        throw 'not implemented';\n    }\n    getState() {\n        return this.stateChanges[(this.stateChanges.length || 1) - 1];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockLocationStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockLocationStrategy });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.9\", ngImport: i0, type: MockLocationStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\nclass _MockPopStateEvent {\n    newUrl;\n    pop = true;\n    type = 'popstate';\n    constructor(newUrl) {\n        this.newUrl = newUrl;\n    }\n}\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n    return [\n        { provide: Location, useClass: SpyLocation },\n        { provide: LocationStrategy$1, useClass: MockLocationStrategy },\n    ];\n}\n\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks, provideFakePlatformNavigation as ɵprovideFakePlatformNavigation };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,qBAAqB,IAAIC,qBAAqB,EAAEC,gBAAgB,QAAQ,iBAAiB;AAClG,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACpF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,eAAe,IAAIC,eAAe,QAAQ,uBAAuB;AAC1E,SAASD,eAAe,QAAQ,uBAAuB;AACvD,SAASE,gBAAgB,EAAEC,QAAQ,EAAEZ,gBAAgB,IAAIa,kBAAkB,QAAQ,yBAAyB;AAC5G,SAASC,QAAQ,QAAQ,2BAA2B;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,+DAA+D;AAChF,SAASC,QAAQA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAChC,MAAMC,cAAc,GAAG,wBAAwB;EAC/C,IAAIC,UAAU;EACd;EACA;EACA,IAAI,CAACD,cAAc,CAACE,IAAI,CAACJ,MAAM,CAAC,EAAE;IAC9BG,UAAU,GAAG,mBAAmB;EACpC;EACA,IAAIE,SAAS;EACb,IAAI;IACAA,SAAS,GAAG,IAAIC,GAAG,CAACN,MAAM,EAAEG,UAAU,CAAC;EAC3C,CAAC,CACD,OAAOI,CAAC,EAAE;IACN,MAAMC,MAAM,GAAGV,QAAQ,CAACW,IAAI,CAACN,UAAU,IAAI,EAAE,GAAGH,MAAM,CAAC;IACvD,IAAI,CAACQ,MAAM,EAAE;MACT,MAAM,IAAIE,KAAK,CAAC,gBAAgBV,MAAM,eAAeC,QAAQ,EAAE,CAAC;IACpE;IACA,MAAMU,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC;IACtCP,SAAS,GAAG;MACRQ,QAAQ,EAAEL,MAAM,CAAC,CAAC,CAAC;MACnBM,QAAQ,EAAEH,SAAS,CAAC,CAAC,CAAC;MACtBI,IAAI,EAAEJ,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MACxBK,QAAQ,EAAER,MAAM,CAAC,CAAC,CAAC;MACnBS,MAAM,EAAET,MAAM,CAAC,CAAC,CAAC;MACjBU,IAAI,EAAEV,MAAM,CAAC,CAAC;IAClB,CAAC;EACL;EACA,IAAIH,SAAS,CAACW,QAAQ,IAAIX,SAAS,CAACW,QAAQ,CAACG,OAAO,CAAClB,QAAQ,CAAC,KAAK,CAAC,EAAE;IAClEI,SAAS,CAACW,QAAQ,GAAGX,SAAS,CAACW,QAAQ,CAACI,SAAS,CAACnB,QAAQ,CAACoB,MAAM,CAAC;EACtE;EACA,OAAO;IACHP,QAAQ,EAAG,CAACX,UAAU,IAAIE,SAAS,CAACS,QAAQ,IAAK,EAAE;IACnDD,QAAQ,EAAG,CAACV,UAAU,IAAIE,SAAS,CAACQ,QAAQ,IAAK,EAAE;IACnDE,IAAI,EAAG,CAACZ,UAAU,IAAIE,SAAS,CAACU,IAAI,IAAK,EAAE;IAC3CC,QAAQ,EAAEX,SAAS,CAACW,QAAQ,IAAI,GAAG;IACnCC,MAAM,EAAEZ,SAAS,CAACY,MAAM,IAAI,EAAE;IAC9BC,IAAI,EAAEb,SAAS,CAACa,IAAI,IAAI;EAC5B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,IAAIrC,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,MAAMsC,oBAAoB,CAAC;EACvBtB,QAAQ,GAAG,EAAE;EACbuB,UAAU,GAAG,IAAIlC,OAAO,CAAC,CAAC;EAC1BmC,eAAe,GAAG,IAAInC,OAAO,CAAC,CAAC;EAC/BoC,cAAc,GAAG,CAAC;EAClBC,UAAU,GAAG,CAAC;IAAEb,QAAQ,EAAE,EAAE;IAAED,QAAQ,EAAE,EAAE;IAAEE,IAAI,EAAE,EAAE;IAAEC,QAAQ,EAAE,GAAG;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE,EAAE;IAAEU,KAAK,EAAE;EAAK,CAAC,CAAC;EACzGC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR,IAAI,CAAC7B,QAAQ,GAAG6B,MAAM,CAACC,WAAW,IAAI,EAAE;MACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,EAAEH,MAAM,CAACI,QAAQ,IAAI,iBAAiB,EAAE,IAAI,CAACjC,QAAQ,CAAC;MAClG,IAAI,CAAC0B,UAAU,CAAC,CAAC,CAAC,GAAG;QAAE,GAAGK;MAAc,CAAC;IAC7C;EACJ;EACA,IAAIlB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACa,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACZ,QAAQ;EACxD;EACA,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACc,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACb,QAAQ;EACxD;EACA,IAAIE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACY,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACX,IAAI;EACpD;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACW,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACV,QAAQ;EACxD;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACU,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACT,MAAM;EACtD;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACS,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACR,IAAI;EACpD;EACA,IAAIU,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,CAACE,KAAK;EACrD;EACAO,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAClC,QAAQ;EACxB;EACAmC,UAAUA,CAACC,EAAE,EAAE;IACX,MAAMC,YAAY,GAAG,IAAI,CAACb,eAAe,CAACc,SAAS,CAACF,EAAE,CAAC;IACvD,OAAO,MAAMC,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C;EACAC,YAAYA,CAACJ,EAAE,EAAE;IACb,MAAMC,YAAY,GAAG,IAAI,CAACd,UAAU,CAACe,SAAS,CAACF,EAAE,CAAC;IAClD,OAAO,MAAMC,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C;EACA,IAAIE,IAAIA,CAAA,EAAG;IACP,IAAIC,GAAG,GAAG,GAAG,IAAI,CAAC9B,QAAQ,KAAK,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI,GAAG,EAAE,EAAE;IACjF4B,GAAG,IAAI,GAAG,IAAI,CAAC3B,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,IAAI,EAAE;IAChF,OAAOyB,GAAG;EACd;EACA,IAAIA,GAAGA,CAAA,EAAG;IACN,OAAO,GAAG,IAAI,CAAC3B,QAAQ,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,IAAI,EAAE;EACvD;EACAe,YAAYA,CAACL,KAAK,EAAEe,GAAG,EAAE1C,QAAQ,GAAG,EAAE,EAAE;IACpC;IACA2B,KAAK,GAAGgB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAClB,KAAK,CAAC,CAAC;IACzC,OAAO;MAAE,GAAG7B,QAAQ,CAAC4C,GAAG,EAAE1C,QAAQ,CAAC;MAAE2B;IAAM,CAAC;EAChD;EACAmB,YAAYA,CAACnB,KAAK,EAAEoB,KAAK,EAAEC,MAAM,EAAE;IAC/B,MAAM;MAAEjC,QAAQ;MAAEC,MAAM;MAAEW,KAAK,EAAEsB,WAAW;MAAEhC;IAAK,CAAC,GAAG,IAAI,CAACe,YAAY,CAACL,KAAK,EAAEqB,MAAM,CAAC;IACvF,IAAI,CAACtB,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC,GAAG;MACnC,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC;MACvCV,QAAQ;MACRC,MAAM;MACNC,IAAI;MACJU,KAAK,EAAEsB;IACX,CAAC;EACL;EACAC,SAASA,CAACvB,KAAK,EAAEoB,KAAK,EAAEC,MAAM,EAAE;IAC5B,MAAM;MAAEjC,QAAQ;MAAEC,MAAM;MAAEW,KAAK,EAAEsB,WAAW;MAAEhC;IAAK,CAAC,GAAG,IAAI,CAACe,YAAY,CAACL,KAAK,EAAEqB,MAAM,CAAC;IACvF,IAAI,IAAI,CAACvB,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACC,UAAU,CAACyB,MAAM,CAAC,IAAI,CAAC1B,cAAc,GAAG,CAAC,CAAC;IACnD;IACA,IAAI,CAACC,UAAU,CAAC0B,IAAI,CAAC;MACjB,GAAG,IAAI,CAAC1B,UAAU,CAAC,IAAI,CAACD,cAAc,CAAC;MACvCV,QAAQ;MACRC,MAAM;MACNC,IAAI;MACJU,KAAK,EAAEsB;IACX,CAAC,CAAC;IACF,IAAI,CAACxB,cAAc,GAAG,IAAI,CAACC,UAAU,CAACN,MAAM,GAAG,CAAC;EACpD;EACAiC,OAAOA,CAAA,EAAG;IACN,MAAMC,MAAM,GAAG,IAAI,CAACZ,GAAG;IACvB,MAAMa,OAAO,GAAG,IAAI,CAACtC,IAAI;IACzB,IAAI,IAAI,CAACQ,cAAc,GAAG,IAAI,CAACC,UAAU,CAACN,MAAM,EAAE;MAC9C,IAAI,CAACK,cAAc,EAAE;IACzB;IACA,IAAI,CAAC+B,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAG,IAAIA,CAAA,EAAG;IACH,MAAMH,MAAM,GAAG,IAAI,CAACZ,GAAG;IACvB,MAAMa,OAAO,GAAG,IAAI,CAACtC,IAAI;IACzB,IAAI,IAAI,CAACQ,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,cAAc,EAAE;IACzB;IACA,IAAI,CAAC+B,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAI,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAML,MAAM,GAAG,IAAI,CAACZ,GAAG;IACvB,MAAMa,OAAO,GAAG,IAAI,CAACtC,IAAI;IACzB,MAAM2C,aAAa,GAAG,IAAI,CAACnC,cAAc,GAAGkC,gBAAgB;IAC5D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAAClC,UAAU,CAACN,MAAM,EAAE;MAC9D,IAAI,CAACK,cAAc,GAAGmC,aAAa;IACvC;IACA,IAAI,CAACJ,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAO,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClC,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,UAAUA,CAACD,OAAO,EAAED,MAAM,EAAE;IACxB,IAAI,CAAC9B,eAAe,CAACsC,IAAI,CAAC;MACtBC,IAAI,EAAE,UAAU;MAChBpC,KAAK,EAAE,IAAI,CAACkC,QAAQ,CAAC,CAAC;MACtBP,MAAM;MACNN,MAAM,EAAE,IAAI,CAACN;IACjB,CAAC,CAAC;IACF,IAAIa,OAAO,KAAK,IAAI,CAACtC,IAAI,EAAE;MACvB,IAAI,CAACM,UAAU,CAACuC,IAAI,CAAC;QACjBC,IAAI,EAAE,YAAY;QAClBpC,KAAK,EAAE,IAAI;QACX2B,MAAM;QACNN,MAAM,EAAE,IAAI,CAACN;MACjB,CAAC,CAAC;IACN;EACJ;EACA,OAAOsB,IAAI,YAAAC,6BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF5C,oBAAoB,EAA9BvC,EAAE,CAAAoF,QAAA,CAA8C9C,6BAA6B;EAAA;EACtK,OAAO+C,KAAK,kBAD6ErF,EAAE,CAAAsF,kBAAA;IAAAC,KAAA,EACYhD,oBAAoB;IAAAiD,OAAA,EAApBjD,oBAAoB,CAAA0C;EAAA;AAC/H;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAH6FzF,EAAE,CAAA0F,iBAAA,CAGJnD,oBAAoB,EAAc,CAAC;IAClHyC,IAAI,EAAE3E;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2E,IAAI,EAAEW,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CZ,IAAI,EAAE7E,MAAM;MACZ0F,IAAI,EAAE,CAACvD,6BAA6B;IACxC,CAAC,EAAE;MACC0C,IAAI,EAAE5E;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAM0F,8BAA8B,CAAC;EACjCC,mBAAmB;EACnBlD,WAAWA,CAAA,EAAG;IACV,MAAMmD,kBAAkB,GAAG9F,MAAM,CAACK,kBAAkB,CAAC;IACrD,IAAI,EAAEyF,kBAAkB,YAAYvF,eAAe,CAAC,EAAE;MAClD,MAAM,IAAIiB,KAAK,CAAC,oEAAoE,GAChF,uEAAuE,CAAC;IAChF;IACA,IAAI,CAACqE,mBAAmB,GAAGC,kBAAkB;EACjD;EACAlD,MAAM,GAAG5C,MAAM,CAACoC,6BAA6B,EAAE;IAAE2D,QAAQ,EAAE;EAAK,CAAC,CAAC;EAClE9C,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACL,MAAM,EAAEC,WAAW,IAAI,EAAE;EACzC;EACAK,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAAC0C,mBAAmB,CAACG,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAE9C,EAAE,CAAC;IAChE,OAAO,MAAM,IAAI,CAAC0C,mBAAmB,CAACG,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAE/C,EAAE,CAAC;EACpF;EACAI,YAAYA,CAACJ,EAAE,EAAE;IACb,IAAI,CAAC0C,mBAAmB,CAACG,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAE9C,EAAE,CAAC;IAClE,OAAO,MAAM,IAAI,CAAC0C,mBAAmB,CAACG,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAE/C,EAAE,CAAC;EACtF;EACA,IAAIK,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACqC,mBAAmB,CAACM,YAAY,CAAC1C,GAAG;EACpD;EACA,IAAI9B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAIP,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAAC9B,QAAQ;EACtE;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAIR,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAAC7B,QAAQ;EACtE;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAIT,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAAC5B,IAAI;EAClE;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAIV,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAAC3B,QAAQ;EACtE;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAIX,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAAC1B,MAAM;EACpE;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAIZ,GAAG,CAAC,IAAI,CAACyE,mBAAmB,CAACM,YAAY,CAAC1C,GAAG,CAAC,CAACzB,IAAI;EAClE;EACAiC,SAASA,CAACvB,KAAK,EAAEoB,KAAK,EAAEL,GAAG,EAAE;IACzB,IAAI,CAACoC,mBAAmB,CAAC5B,SAAS,CAACvB,KAAK,EAAEoB,KAAK,EAAEL,GAAG,CAAC;EACzD;EACAI,YAAYA,CAACnB,KAAK,EAAEoB,KAAK,EAAEL,GAAG,EAAE;IAC5B,IAAI,CAACoC,mBAAmB,CAAChC,YAAY,CAACnB,KAAK,EAAEoB,KAAK,EAAEL,GAAG,CAAC;EAC5D;EACAW,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyB,mBAAmB,CAACzB,OAAO,CAAC,CAAC;EACtC;EACAI,IAAIA,CAAA,EAAG;IACH,IAAI,CAACqB,mBAAmB,CAACrB,IAAI,CAAC,CAAC;EACnC;EACAC,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,IAAI,CAACmB,mBAAmB,CAACO,EAAE,CAAC1B,gBAAgB,CAAC;EACjD;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACiB,mBAAmB,CAACM,YAAY,CAACE,eAAe,CAAC,CAAC;EAClE;EACA,OAAOtB,IAAI,YAAAuB,uCAAArB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFW,8BAA8B;EAAA;EACjI,OAAOT,KAAK,kBA5E6ErF,EAAE,CAAAsF,kBAAA;IAAAC,KAAA,EA4EYO,8BAA8B;IAAAN,OAAA,EAA9BM,8BAA8B,CAAAb;EAAA;AACzI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA9E6FzF,EAAE,CAAA0F,iBAAA,CA8EJI,8BAA8B,EAAc,CAAC;IAC5Hd,IAAI,EAAE3E;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMoG,eAAe,GAAG,IAAIxG,cAAc,CAAC,gBAAgB,EAAE;EACzDyG,UAAU,EAAE,MAAM;EAClBlB,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM1C,MAAM,GAAG5C,MAAM,CAACoC,6BAA6B,EAAE;MAAE2D,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxE,MAAMU,YAAY,GAAG,iBAAiB;IACtC,MAAMzD,QAAQ,GAAG,IAAI5B,GAAG,CAACwB,MAAM,EAAEI,QAAQ,IAAIyD,YAAY,EAAEA,YAAY,CAAC;IACxE;IACA;IACA,OAAO,IAAIlG,eAAe,CAACP,MAAM,CAACW,QAAQ,CAAC,EAAEqC,QAAQ,CAACQ,IAAI,CAAC;EAC/D;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA,SAASkD,6BAA6BA,CAAA,EAAG;EACrC,OAAO,CACH;IACIC,OAAO,EAAEtG,kBAAkB;IAC3BuG,UAAU,EAAEA,CAAA,KAAM5G,MAAM,CAACuG,eAAe;EAC5C,CAAC,EACD;IAAEI,OAAO,EAAEnG,gBAAgB;IAAEqG,QAAQ,EAAEjB;EAA+B,CAAC,CAC1E;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMkB,WAAW,CAAC;EACdrE,UAAU,GAAG,EAAE;EACfsE,QAAQ,GAAG,CAAC,IAAIC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;EAC5CC,aAAa,GAAG,CAAC;EACjB;EACAC,QAAQ,GAAG,IAAI9G,OAAO,CAAC,CAAC;EACxB;EACA+G,SAAS,GAAG,EAAE;EACd;EACAC,iBAAiB,GAAG,IAAI;EACxB;EACAC,mBAAmB,GAAG,EAAE;EACxB;EACAC,sBAAsB,GAAG,IAAI;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,sBAAsB,EAAEhE,WAAW,CAAC,CAAC;IAC1C,IAAI,CAAC+D,mBAAmB,GAAG,EAAE;EACjC;EACAG,cAAcA,CAAC/D,GAAG,EAAE;IAChB,IAAI,CAACsD,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACQ,IAAI,GAAGhE,GAAG;EAChD;EACAiE,WAAWA,CAACjE,GAAG,EAAE;IACb,IAAI,CAAC0D,SAAS,GAAG1D,GAAG;EACxB;EACAgE,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACV,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACQ,IAAI;EACjD;EACA7C,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmC,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACvE,KAAK;EAClD;EACAiF,oBAAoBA,CAACF,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAE;IACnC,MAAMC,SAAS,GAAGJ,IAAI,CAACK,QAAQ,CAAC,GAAG,CAAC,GAAGL,IAAI,CAACvF,SAAS,CAAC,CAAC,EAAEuF,IAAI,CAACtF,MAAM,GAAG,CAAC,CAAC,GAAGsF,IAAI;IAChF,MAAMM,QAAQ,GAAG,IAAI,CAACN,IAAI,CAAC,CAAC,CAACK,QAAQ,CAAC,GAAG,CAAC,GACpC,IAAI,CAACL,IAAI,CAAC,CAAC,CAACvF,SAAS,CAAC,CAAC,EAAE,IAAI,CAACuF,IAAI,CAAC,CAAC,CAACtF,MAAM,GAAG,CAAC,CAAC,GAChD,IAAI,CAACsF,IAAI,CAAC,CAAC;IACjB,OAAOM,QAAQ,IAAIF,SAAS,IAAID,KAAK,CAACzF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGyF,KAAK,GAAG,EAAE,CAAC;EACxE;EACAI,cAAcA,CAAClG,QAAQ,EAAE;IACrB,IAAI,CAACoF,QAAQ,CAACrC,IAAI,CAAC;MAAE,KAAK,EAAE/C,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;EAC5E;EACAmG,kBAAkBA,CAACnG,QAAQ,EAAE;IACzB,MAAM2F,IAAI,GAAG,IAAI,CAACS,kBAAkB,CAACpG,QAAQ,CAAC;IAC9C,IAAI,CAACqG,WAAW,CAACV,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;IAChC,IAAI,CAAChF,UAAU,CAAC0B,IAAI,CAAC,QAAQ,GAAGrC,QAAQ,CAAC;IACzC;IACA;IACA,IAAI,CAACoF,QAAQ,CAACrC,IAAI,CAAC;MAAE,KAAK,EAAE/C,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;IACxE,IAAI,CAACoF,QAAQ,CAACrC,IAAI,CAAC;MAAE,KAAK,EAAE/C,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAa,CAAC,CAAC;EAC9E;EACAoG,kBAAkBA,CAACzE,GAAG,EAAE;IACpB,IAAIA,GAAG,CAACtB,MAAM,GAAG,CAAC,IAAI,CAACsB,GAAG,CAAC2E,UAAU,CAAC,GAAG,CAAC,EAAE;MACxC3E,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACA,OAAO,IAAI,CAAC0D,SAAS,GAAG1D,GAAG;EAC/B;EACA2C,EAAEA,CAACqB,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAElF,KAAK,GAAG,IAAI,EAAE;IAC/B+E,IAAI,GAAG,IAAI,CAACS,kBAAkB,CAACT,IAAI,CAAC;IACpC,IAAI,CAACU,WAAW,CAACV,IAAI,EAAEG,KAAK,EAAElF,KAAK,CAAC;IACpC,MAAM2F,aAAa,GAAG,IAAI,CAACtB,QAAQ,CAAC,IAAI,CAACE,aAAa,GAAG,CAAC,CAAC;IAC3D,IAAIoB,aAAa,CAACZ,IAAI,IAAIA,IAAI,IAAIY,aAAa,CAACT,KAAK,IAAIA,KAAK,EAAE;MAC5D;IACJ;IACA,MAAMnE,GAAG,GAAGgE,IAAI,IAAIG,KAAK,CAACzF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGyF,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACnF,UAAU,CAAC0B,IAAI,CAACV,GAAG,CAAC;IACzB,IAAI,CAAC6E,yBAAyB,CAACb,IAAI,GAAG7H,qBAAqB,CAACgI,KAAK,CAAC,EAAElF,KAAK,CAAC;EAC9E;EACAmB,YAAYA,CAAC4D,IAAI,EAAEG,KAAK,GAAG,EAAE,EAAElF,KAAK,GAAG,IAAI,EAAE;IACzC+E,IAAI,GAAG,IAAI,CAACS,kBAAkB,CAACT,IAAI,CAAC;IACpC,MAAMc,OAAO,GAAG,IAAI,CAACxB,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC;IACjDsB,OAAO,CAAC7F,KAAK,GAAGA,KAAK;IACrB,IAAI6F,OAAO,CAACd,IAAI,IAAIA,IAAI,IAAIc,OAAO,CAACX,KAAK,IAAIA,KAAK,EAAE;MAChD;IACJ;IACAW,OAAO,CAACd,IAAI,GAAGA,IAAI;IACnBc,OAAO,CAACX,KAAK,GAAGA,KAAK;IACrB,MAAMnE,GAAG,GAAGgE,IAAI,IAAIG,KAAK,CAACzF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGyF,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACnF,UAAU,CAAC0B,IAAI,CAAC,WAAW,GAAGV,GAAG,CAAC;IACvC,IAAI,CAAC6E,yBAAyB,CAACb,IAAI,GAAG7H,qBAAqB,CAACgI,KAAK,CAAC,EAAElF,KAAK,CAAC;EAC9E;EACA0B,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC6C,aAAa,GAAG,IAAI,CAACF,QAAQ,CAAC5E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC8E,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACrC,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAAC4C,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC7C,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACAJ,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACyC,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACrC,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAAC4C,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC7C,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACAH,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAMC,aAAa,GAAG,IAAI,CAACsC,aAAa,GAAGvC,gBAAgB;IAC3D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACoC,QAAQ,CAAC5E,MAAM,EAAE;MAC5D,IAAI,CAAC8E,aAAa,GAAGtC,aAAa;MAClC,IAAI,CAACuC,QAAQ,CAACrC,IAAI,CAAC;QACf,KAAK,EAAE,IAAI,CAAC4C,IAAI,CAAC,CAAC;QAClB,OAAO,EAAE,IAAI,CAAC7C,QAAQ,CAAC,CAAC;QACxB,KAAK,EAAE,IAAI;QACX,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;EACJ;EACA4D,WAAWA,CAACrF,EAAE,EAAE;IACZ,IAAI,CAACkE,mBAAmB,CAAClD,IAAI,CAAChB,EAAE,CAAC;IACjC,IAAI,CAACmE,sBAAsB,KAAK,IAAI,CAACjE,SAAS,CAAEoF,CAAC,IAAK;MAClD,IAAI,CAACH,yBAAyB,CAACG,CAAC,CAAChF,GAAG,EAAEgF,CAAC,CAAC/F,KAAK,CAAC;IAClD,CAAC,CAAC;IACF,OAAO,MAAM;MACT,MAAMgG,OAAO,GAAG,IAAI,CAACrB,mBAAmB,CAACpF,OAAO,CAACkB,EAAE,CAAC;MACpD,IAAI,CAACkE,mBAAmB,CAACnD,MAAM,CAACwE,OAAO,EAAE,CAAC,CAAC;MAC3C,IAAI,IAAI,CAACrB,mBAAmB,CAAClF,MAAM,KAAK,CAAC,EAAE;QACvC,IAAI,CAACmF,sBAAsB,EAAEhE,WAAW,CAAC,CAAC;QAC1C,IAAI,CAACgE,sBAAsB,GAAG,IAAI;MACtC;IACJ,CAAC;EACL;EACA;EACAgB,yBAAyBA,CAAC7E,GAAG,GAAG,EAAE,EAAEf,KAAK,EAAE;IACvC,IAAI,CAAC2E,mBAAmB,CAACsB,OAAO,CAAExF,EAAE,IAAKA,EAAE,CAACM,GAAG,EAAEf,KAAK,CAAC,CAAC;EAC5D;EACAW,SAASA,CAACuF,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC5B,QAAQ,CAAC7D,SAAS,CAAC;MAC3BwB,IAAI,EAAE+D,MAAM;MACZG,KAAK,EAAEF,OAAO,IAAIpD,SAAS;MAC3BuD,QAAQ,EAAEF,QAAQ,IAAIrD;IAC1B,CAAC,CAAC;EACN;EACAwD,SAASA,CAACxF,GAAG,EAAE;IACX,OAAO,IAAI;EACf;EACA0E,WAAWA,CAACV,IAAI,EAAEG,KAAK,EAAElF,KAAK,EAAE;IAC5B,IAAI,IAAI,CAACuE,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACF,QAAQ,CAAC7C,MAAM,CAAC,IAAI,CAAC+C,aAAa,GAAG,CAAC,CAAC;IAChD;IACA,IAAI,CAACF,QAAQ,CAAC5C,IAAI,CAAC,IAAI6C,aAAa,CAACS,IAAI,EAAEG,KAAK,EAAElF,KAAK,CAAC,CAAC;IACzD,IAAI,CAACuE,aAAa,GAAG,IAAI,CAACF,QAAQ,CAAC5E,MAAM,GAAG,CAAC;EACjD;EACA,OAAO4C,IAAI,YAAAmE,oBAAAjE,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6B,WAAW;EAAA;EAC9G,OAAO3B,KAAK,kBArQ6ErF,EAAE,CAAAsF,kBAAA;IAAAC,KAAA,EAqQYyB,WAAW;IAAAxB,OAAA,EAAXwB,WAAW,CAAA/B;EAAA;AACtH;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAvQ6FzF,EAAE,CAAA0F,iBAAA,CAuQJsB,WAAW,EAAc,CAAC;IACzGhC,IAAI,EAAE3E;EACV,CAAC,CAAC;AAAA;AACV,MAAM6G,aAAa,CAAC;EAChBS,IAAI;EACJG,KAAK;EACLlF,KAAK;EACLC,WAAWA,CAAC8E,IAAI,EAAEG,KAAK,EAAElF,KAAK,EAAE;IAC5B,IAAI,CAAC+E,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAClF,KAAK,GAAGA,KAAK;EACtB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyG,oBAAoB,SAAStJ,gBAAgB,CAAC;EAChDuJ,gBAAgB,GAAG,GAAG;EACtBC,YAAY,GAAG,GAAG;EAClBC,aAAa,GAAG,EAAE;EAClB7G,UAAU,GAAG,EAAE;EACf;EACAyE,QAAQ,GAAG,IAAI9G,OAAO,CAAC,CAAC;EACxBmJ,YAAY,GAAG,EAAE;EACjB5G,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;EACX;EACA6G,gBAAgBA,CAAC/F,GAAG,EAAE;IAClB,IAAI,CAAC4F,YAAY,GAAG5F,GAAG;IACvB,IAAI,CAACyD,QAAQ,CAACrC,IAAI,CAAC,IAAI4E,kBAAkB,CAAC,IAAI,CAAChC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3D;EACAA,IAAIA,CAACiC,WAAW,GAAG,KAAK,EAAE;IACtB,OAAO,IAAI,CAACL,YAAY;EAC5B;EACAnB,kBAAkBA,CAACyB,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACvB,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,gBAAgB,CAACtB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAACsB,gBAAgB,GAAGO,QAAQ,CAACzH,SAAS,CAAC,CAAC,CAAC;IACxD;IACA,OAAO,IAAI,CAACkH,gBAAgB,GAAGO,QAAQ;EAC3C;EACA1F,SAASA,CAAC2F,GAAG,EAAE9F,KAAK,EAAE2D,IAAI,EAAEG,KAAK,EAAE;IAC/B;IACA,IAAI,CAAC2B,YAAY,CAACpF,IAAI,CAACyF,GAAG,CAAC;IAC3B,IAAI,CAACN,aAAa,GAAGxF,KAAK;IAC1B,MAAML,GAAG,GAAGgE,IAAI,IAAIG,KAAK,CAACzF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGyF,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACyB,YAAY,GAAG5F,GAAG;IACvB,MAAMoG,WAAW,GAAG,IAAI,CAAC3B,kBAAkB,CAACzE,GAAG,CAAC;IAChD,IAAI,CAAChB,UAAU,CAAC0B,IAAI,CAAC0F,WAAW,CAAC;EACrC;EACAhG,YAAYA,CAAC+F,GAAG,EAAE9F,KAAK,EAAE2D,IAAI,EAAEG,KAAK,EAAE;IAClC;IACA,IAAI,CAAC2B,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAACpH,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGyH,GAAG;IAC5D,IAAI,CAACN,aAAa,GAAGxF,KAAK;IAC1B,MAAML,GAAG,GAAGgE,IAAI,IAAIG,KAAK,CAACzF,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGyF,KAAK,GAAG,EAAE,CAAC;IACxD,IAAI,CAACyB,YAAY,GAAG5F,GAAG;IACvB,MAAMoG,WAAW,GAAG,IAAI,CAAC3B,kBAAkB,CAACzE,GAAG,CAAC;IAChD,IAAI,CAAChB,UAAU,CAAC0B,IAAI,CAAC,WAAW,GAAG0F,WAAW,CAAC;EACnD;EACA3G,UAAUA,CAACC,EAAE,EAAE;IACX,IAAI,CAAC+D,QAAQ,CAAC7D,SAAS,CAAC;MAAEwB,IAAI,EAAE1B;IAAG,CAAC,CAAC;EACzC;EACA2G,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACV,gBAAgB;EAChC;EACA5E,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC/B,UAAU,CAACN,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACM,UAAU,CAACsH,GAAG,CAAC,CAAC;MACrB,IAAI,CAACR,YAAY,CAACQ,GAAG,CAAC,CAAC;MACvB,MAAMC,OAAO,GAAG,IAAI,CAACvH,UAAU,CAACN,MAAM,GAAG,CAAC,GAAG,IAAI,CAACM,UAAU,CAAC,IAAI,CAACA,UAAU,CAACN,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;MAC7F,IAAI,CAACqH,gBAAgB,CAACQ,OAAO,CAAC;IAClC;EACJ;EACA5F,OAAOA,CAAA,EAAG;IACN,MAAM,iBAAiB;EAC3B;EACAQ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC2E,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAACpH,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EACjE;EACA,OAAO4C,IAAI,YAAAkF,6BAAAhF,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkE,oBAAoB;EAAA;EACvH,OAAOhE,KAAK,kBA1V6ErF,EAAE,CAAAsF,kBAAA;IAAAC,KAAA,EA0VY8D,oBAAoB;IAAA7D,OAAA,EAApB6D,oBAAoB,CAAApE;EAAA;AAC/H;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KA5V6FzF,EAAE,CAAA0F,iBAAA,CA4VJ2D,oBAAoB,EAAc,CAAC;IAClHrE,IAAI,EAAE3E;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,MAAMsJ,kBAAkB,CAAC;EACrB1F,MAAM;EACNgG,GAAG,GAAG,IAAI;EACVjF,IAAI,GAAG,UAAU;EACjBnC,WAAWA,CAACoB,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmG,oBAAoBA,CAAA,EAAG;EAC5B,OAAO,CACH;IAAEvD,OAAO,EAAElG,QAAQ;IAAEoG,QAAQ,EAAEC;EAAY,CAAC,EAC5C;IAAEH,OAAO,EAAEjG,kBAAkB;IAAEmG,QAAQ,EAAEsC;EAAqB,CAAC,CAClE;AACL;AAEA,SAAS/G,6BAA6B,EAAE+G,oBAAoB,EAAE9G,oBAAoB,EAAEyE,WAAW,EAAEoD,oBAAoB,EAAExD,6BAA6B,IAAIyD,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}