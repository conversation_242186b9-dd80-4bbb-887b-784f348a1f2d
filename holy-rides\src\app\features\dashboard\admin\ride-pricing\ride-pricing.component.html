<div class="pricing-container">
  <mat-card class="form-card">
    <mat-card-header>
      <mat-card-title>{{ editMode ? 'Edit Pricing Configuration' : 'Create New Pricing Configuration' }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="pricingForm" (ngSubmit)="onSubmit()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Configuration Name</mat-label>
            <input matInput formControlName="name" placeholder="e.g., Standard, Premium, etc.">
            <mat-error *ngIf="pricingForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Base Fare ($)</mat-label>
            <input matInput type="number" step="0.01" formControlName="base_fare">
            <mat-error *ngIf="pricingForm.get('base_fare')?.hasError('required')">
              Base fare is required
            </mat-error>
            <mat-error *ngIf="pricingForm.get('base_fare')?.hasError('min')">
              Base fare must be positive
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Distance Rate ($ per mile)</mat-label>
            <input matInput type="number" step="0.01" formControlName="distance_rate">
            <mat-error *ngIf="pricingForm.get('distance_rate')?.hasError('required')">
              Distance rate is required
            </mat-error>
            <mat-error *ngIf="pricingForm.get('distance_rate')?.hasError('min')">
              Distance rate must be positive
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Time Rate ($ per minute)</mat-label>
            <input matInput type="number" step="0.01" formControlName="time_rate">
            <mat-error *ngIf="pricingForm.get('time_rate')?.hasError('required')">
              Time rate is required
            </mat-error>
            <mat-error *ngIf="pricingForm.get('time_rate')?.hasError('min')">
              Time rate must be positive
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <div class="active-toggle">
            <mat-slide-toggle formControlName="is_active" color="primary">
              Set as Active Configuration
            </mat-slide-toggle>
          </div>
        </div>

        <div class="sample-calculation">
          <mat-divider></mat-divider>
          <h3>Sample Fare Calculation</h3>
          <p>For a 10-mile, 20-minute ride: <strong>${{ calculateSampleFare() }}</strong></p>
          <mat-divider></mat-divider>
        </div>

        <div class="form-actions">
          <button mat-button type="button" (click)="resetForm()">
            {{ editMode ? 'Cancel' : 'Reset' }}
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="pricingForm.invalid || loading">
            {{ editMode ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <mat-card class="table-card">
    <mat-card-header>
      <mat-card-title>Pricing Configurations</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading pricing configurations...</p>
      </div>

      <div *ngIf="!loading" class="table-container">
        <table mat-table [dataSource]="pricingConfigurations" class="mat-elevation-z2">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Name</th>
            <td mat-cell *matCellDef="let pricing">{{ pricing.name }}</td>
          </ng-container>

          <!-- Base Fare Column -->
          <ng-container matColumnDef="base_fare">
            <th mat-header-cell *matHeaderCellDef>Base Fare</th>
            <td mat-cell *matCellDef="let pricing">${{ pricing.base_fare.toFixed(2) }}</td>
          </ng-container>

          <!-- Distance Rate Column -->
          <ng-container matColumnDef="distance_rate">
            <th mat-header-cell *matHeaderCellDef>Distance Rate</th>
            <td mat-cell *matCellDef="let pricing">${{ pricing.distance_rate.toFixed(2) }}/mile</td>
          </ng-container>

          <!-- Time Rate Column -->
          <ng-container matColumnDef="time_rate">
            <th mat-header-cell *matHeaderCellDef>Time Rate</th>
            <td mat-cell *matCellDef="let pricing">${{ pricing.time_rate.toFixed(2) }}/min</td>
          </ng-container>

          <!-- Active Column -->
          <ng-container matColumnDef="is_active">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let pricing">
              <span class="status-badge" [class.active]="pricing.is_active">
                {{ pricing.is_active ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let pricing">
              <button mat-icon-button color="primary" (click)="editPricing(pricing)" matTooltip="Edit">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="accent" (click)="toggleActive(pricing)" 
                      [matTooltip]="pricing.is_active ? 'Deactivate' : 'Activate'">
                <mat-icon>{{ pricing.is_active ? 'toggle_on' : 'toggle_off' }}</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deletePricing(pricing.id)" matTooltip="Delete"
                      [disabled]="pricing.is_active">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <div *ngIf="pricingConfigurations.length === 0" class="no-data">
          <p>No pricing configurations found. Create one to get started.</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
