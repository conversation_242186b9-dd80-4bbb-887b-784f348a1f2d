{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nexport function expiresAt(expiresIn) {\n  const timeNow = Math.round(Date.now() / 1000);\n  return timeNow + expiresIn;\n}\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    const r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport const isBrowser = () => typeof document !== 'undefined';\nconst localStorageWriteTests = {\n  tested: false,\n  writable: false\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n  if (!isBrowser()) {\n    return false;\n  }\n  try {\n    if (typeof globalThis.localStorage !== 'object') {\n      return false;\n    }\n  } catch (e) {\n    // DOM exception when accessing `localStorage`\n    return false;\n  }\n  if (localStorageWriteTests.tested) {\n    return localStorageWriteTests.writable;\n  }\n  const randomKey = `lswt-${Math.random()}${Math.random()}`;\n  try {\n    globalThis.localStorage.setItem(randomKey, randomKey);\n    globalThis.localStorage.removeItem(randomKey);\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = true;\n  } catch (e) {\n    // localStorage can't be written to\n    // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n    localStorageWriteTests.tested = true;\n    localStorageWriteTests.writable = false;\n  }\n  return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n  const result = {};\n  const url = new URL(href);\n  if (url.hash && url.hash[0] === '#') {\n    try {\n      const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n      hashSearchParams.forEach((value, key) => {\n        result[key] = value;\n      });\n    } catch (e) {\n      // hash is not a query string\n    }\n  }\n  // search parameters take precedence over hash parameters\n  url.searchParams.forEach((value, key) => {\n    result[key] = value;\n  });\n  return result;\n}\nexport const resolveFetch = customFetch => {\n  let _fetch;\n  if (customFetch) {\n    _fetch = customFetch;\n  } else if (typeof fetch === 'undefined') {\n    _fetch = (...args) => import('@supabase/node-fetch').then(({\n      default: fetch\n    }) => fetch(...args));\n  } else {\n    _fetch = fetch;\n  }\n  return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = maybeResponse => {\n  return typeof maybeResponse === 'object' && maybeResponse !== null && 'status' in maybeResponse && 'ok' in maybeResponse && 'json' in maybeResponse && typeof maybeResponse.json === 'function';\n};\n// Storage helpers\nexport const setItemAsync = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (storage, key, data) {\n    yield storage.setItem(key, JSON.stringify(data));\n  });\n  return function setItemAsync(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport const getItemAsync = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (storage, key) {\n    const value = yield storage.getItem(key);\n    if (!value) {\n      return null;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (_a) {\n      return value;\n    }\n  });\n  return function getItemAsync(_x4, _x5) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport const removeItemAsync = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (storage, key) {\n    yield storage.removeItem(key);\n  });\n  return function removeItemAsync(_x6, _x7) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nexport function decodeBase64URL(value) {\n  const key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n  let base64 = '';\n  let chr1, chr2, chr3;\n  let enc1, enc2, enc3, enc4;\n  let i = 0;\n  value = value.replace('-', '+').replace('_', '/');\n  while (i < value.length) {\n    enc1 = key.indexOf(value.charAt(i++));\n    enc2 = key.indexOf(value.charAt(i++));\n    enc3 = key.indexOf(value.charAt(i++));\n    enc4 = key.indexOf(value.charAt(i++));\n    chr1 = enc1 << 2 | enc2 >> 4;\n    chr2 = (enc2 & 15) << 4 | enc3 >> 2;\n    chr3 = (enc3 & 3) << 6 | enc4;\n    base64 = base64 + String.fromCharCode(chr1);\n    if (enc3 != 64 && chr2 != 0) {\n      base64 = base64 + String.fromCharCode(chr2);\n    }\n    if (enc4 != 64 && chr3 != 0) {\n      base64 = base64 + String.fromCharCode(chr3);\n    }\n  }\n  return base64;\n}\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n  constructor() {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    this.promise = new Deferred.promiseConstructor((res, rej) => {\n      // eslint-disable-next-line @typescript-eslint/no-extra-semi\n      ;\n      this.resolve = res;\n      this.reject = rej;\n    });\n  }\n}\nDeferred.promiseConstructor = Promise;\n// Taken from: https://stackoverflow.com/questions/38552003/how-to-decode-jwt-token-in-javascript-without-using-a-library\nexport function decodeJWTPayload(token) {\n  // Regex checks for base64url format\n  const base64UrlRegex = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i;\n  const parts = token.split('.');\n  if (parts.length !== 3) {\n    throw new Error('JWT is not valid: not a JWT structure');\n  }\n  if (!base64UrlRegex.test(parts[1])) {\n    throw new Error('JWT is not valid: payload is not in base64url format');\n  }\n  const base64Url = parts[1];\n  return JSON.parse(decodeBase64URL(base64Url));\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport function sleep(_x8) {\n  return _sleep.apply(this, arguments);\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction _sleep() {\n  _sleep = _asyncToGenerator(function* (time) {\n    return yield new Promise(accept => {\n      setTimeout(() => accept(null), time);\n    });\n  });\n  return _sleep.apply(this, arguments);\n}\nexport function retryable(fn, isRetryable) {\n  const promise = new Promise((accept, reject) => {\n    // eslint-disable-next-line @typescript-eslint/no-extra-semi\n    ;\n    _asyncToGenerator(function* () {\n      for (let attempt = 0; attempt < Infinity; attempt++) {\n        try {\n          const result = yield fn(attempt);\n          if (!isRetryable(attempt, null, result)) {\n            accept(result);\n            return;\n          }\n        } catch (e) {\n          if (!isRetryable(attempt, e)) {\n            reject(e);\n            return;\n          }\n        }\n      }\n    })();\n  });\n  return promise;\n}\nfunction dec2hex(dec) {\n  return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n  const verifierLength = 56;\n  const array = new Uint32Array(verifierLength);\n  if (typeof crypto === 'undefined') {\n    const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n    const charSetLen = charSet.length;\n    let verifier = '';\n    for (let i = 0; i < verifierLength; i++) {\n      verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n    }\n    return verifier;\n  }\n  crypto.getRandomValues(array);\n  return Array.from(array, dec2hex).join('');\n}\nfunction sha256(_x9) {\n  return _sha.apply(this, arguments);\n}\nfunction _sha() {\n  _sha = _asyncToGenerator(function* (randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = yield crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes).map(c => String.fromCharCode(c)).join('');\n  });\n  return _sha.apply(this, arguments);\n}\nfunction base64urlencode(str) {\n  return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nexport function generatePKCEChallenge(_x0) {\n  return _generatePKCEChallenge.apply(this, arguments);\n}\nfunction _generatePKCEChallenge() {\n  _generatePKCEChallenge = _asyncToGenerator(function* (verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined' && typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n      console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n      return verifier;\n    }\n    const hashed = yield sha256(verifier);\n    return base64urlencode(hashed);\n  });\n  return _generatePKCEChallenge.apply(this, arguments);\n}", "map": {"version": 3, "names": ["expiresAt", "expiresIn", "timeNow", "Math", "round", "Date", "now", "uuid", "replace", "c", "r", "random", "v", "toString", "<PERSON><PERSON><PERSON><PERSON>", "document", "localStorageWriteTests", "tested", "writable", "supportsLocalStorage", "globalThis", "localStorage", "e", "<PERSON><PERSON><PERSON>", "setItem", "removeItem", "parseParametersFromURL", "href", "result", "url", "URL", "hash", "hashSearchParams", "URLSearchParams", "substring", "for<PERSON>ach", "value", "key", "searchParams", "resolveFetch", "customFetch", "_fetch", "fetch", "args", "then", "default", "looksLikeFetchResponse", "maybeResponse", "json", "setItemAsync", "_ref", "_asyncToGenerator", "storage", "data", "JSON", "stringify", "_x", "_x2", "_x3", "apply", "arguments", "getItemAsync", "_ref2", "getItem", "parse", "_a", "_x4", "_x5", "removeItemAsync", "_ref3", "_x6", "_x7", "decodeBase64URL", "base64", "chr1", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "i", "length", "indexOf", "char<PERSON>t", "String", "fromCharCode", "Deferred", "constructor", "promise", "promiseConstructor", "res", "rej", "resolve", "reject", "Promise", "decodeJWTPayload", "token", "base64UrlRegex", "parts", "split", "Error", "test", "base64Url", "sleep", "_x8", "_sleep", "time", "accept", "setTimeout", "retryable", "fn", "isRetryable", "attempt", "Infinity", "dec2hex", "dec", "substr", "generatePKCEVerifier", "verifier<PERSON><PERSON><PERSON>", "array", "Uint32Array", "crypto", "charSet", "charSetLen", "verifier", "floor", "getRandomValues", "Array", "from", "join", "sha256", "_x9", "_sha", "randomString", "encoder", "TextEncoder", "encodedData", "encode", "subtle", "digest", "bytes", "Uint8Array", "map", "base64urlencode", "str", "btoa", "generatePKCEChallenge", "_x0", "_generatePKCEChallenge", "hasCryptoSupport", "console", "warn", "hashed"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/helpers.js"], "sourcesContent": ["export function expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nexport function uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nexport const isBrowser = () => typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nexport const supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nexport function parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nexport const resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => import('@supabase/node-fetch').then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nexport const looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nexport const setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nexport const getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nexport const removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\nexport function decodeBase64URL(value) {\n    const key = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n    let base64 = '';\n    let chr1, chr2, chr3;\n    let enc1, enc2, enc3, enc4;\n    let i = 0;\n    value = value.replace('-', '+').replace('_', '/');\n    while (i < value.length) {\n        enc1 = key.indexOf(value.charAt(i++));\n        enc2 = key.indexOf(value.charAt(i++));\n        enc3 = key.indexOf(value.charAt(i++));\n        enc4 = key.indexOf(value.charAt(i++));\n        chr1 = (enc1 << 2) | (enc2 >> 4);\n        chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n        chr3 = ((enc3 & 3) << 6) | enc4;\n        base64 = base64 + String.fromCharCode(chr1);\n        if (enc3 != 64 && chr2 != 0) {\n            base64 = base64 + String.fromCharCode(chr2);\n        }\n        if (enc4 != 64 && chr3 != 0) {\n            base64 = base64 + String.fromCharCode(chr3);\n        }\n    }\n    return base64;\n}\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nexport class Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\n// Taken from: https://stackoverflow.com/questions/38552003/how-to-decode-jwt-token-in-javascript-without-using-a-library\nexport function decodeJWTPayload(token) {\n    // Regex checks for base64url format\n    const base64UrlRegex = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i;\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new Error('JWT is not valid: not a JWT structure');\n    }\n    if (!base64UrlRegex.test(parts[1])) {\n        throw new Error('JWT is not valid: payload is not in base64url format');\n    }\n    const base64Url = parts[1];\n    return JSON.parse(decodeBase64URL(base64Url));\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nexport async function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nexport function retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nexport function generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nfunction base64urlencode(str) {\n    return btoa(str).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nexport async function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return base64urlencode(hashed);\n}\n"], "mappings": ";AAAA,OAAO,SAASA,SAASA,CAACC,SAAS,EAAE;EACjC,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;EAC7C,OAAOJ,OAAO,GAAGD,SAAS;AAC9B;AACA,OAAO,SAASM,IAAIA,CAAA,EAAG;EACnB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;IACxE,MAAMC,CAAC,GAAIP,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;MAAEC,CAAC,GAAGH,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACtE,OAAOE,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM,OAAOC,QAAQ,KAAK,WAAW;AAC9D,MAAMC,sBAAsB,GAAG;EAC3BC,MAAM,EAAE,KAAK;EACbC,QAAQ,EAAE;AACd,CAAC;AACD;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EACtC,IAAI,CAACL,SAAS,CAAC,CAAC,EAAE;IACd,OAAO,KAAK;EAChB;EACA,IAAI;IACA,IAAI,OAAOM,UAAU,CAACC,YAAY,KAAK,QAAQ,EAAE;MAC7C,OAAO,KAAK;IAChB;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE;IACN;IACA,OAAO,KAAK;EAChB;EACA,IAAIN,sBAAsB,CAACC,MAAM,EAAE;IAC/B,OAAOD,sBAAsB,CAACE,QAAQ;EAC1C;EACA,MAAMK,SAAS,GAAG,QAAQpB,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAGR,IAAI,CAACQ,MAAM,CAAC,CAAC,EAAE;EACzD,IAAI;IACAS,UAAU,CAACC,YAAY,CAACG,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;IACrDH,UAAU,CAACC,YAAY,CAACI,UAAU,CAACF,SAAS,CAAC;IAC7CP,sBAAsB,CAACC,MAAM,GAAG,IAAI;IACpCD,sBAAsB,CAACE,QAAQ,GAAG,IAAI;EAC1C,CAAC,CACD,OAAOI,CAAC,EAAE;IACN;IACA;IACAN,sBAAsB,CAACC,MAAM,GAAG,IAAI;IACpCD,sBAAsB,CAACE,QAAQ,GAAG,KAAK;EAC3C;EACA,OAAOF,sBAAsB,CAACE,QAAQ;AAC1C,CAAC;AACD;AACA;AACA;AACA,OAAO,SAASQ,sBAAsBA,CAACC,IAAI,EAAE;EACzC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAACH,IAAI,CAAC;EACzB,IAAIE,GAAG,CAACE,IAAI,IAAIF,GAAG,CAACE,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACjC,IAAI;MACA,MAAMC,gBAAgB,GAAG,IAAIC,eAAe,CAACJ,GAAG,CAACE,IAAI,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC;MACnEF,gBAAgB,CAACG,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QACrCT,MAAM,CAACS,GAAG,CAAC,GAAGD,KAAK;MACvB,CAAC,CAAC;IACN,CAAC,CACD,OAAOd,CAAC,EAAE;MACN;IAAA;EAER;EACA;EACAO,GAAG,CAACS,YAAY,CAACH,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IACrCT,MAAM,CAACS,GAAG,CAAC,GAAGD,KAAK;EACvB,CAAC,CAAC;EACF,OAAOR,MAAM;AACjB;AACA,OAAO,MAAMW,YAAY,GAAIC,WAAW,IAAK;EACzC,IAAIC,MAAM;EACV,IAAID,WAAW,EAAE;IACbC,MAAM,GAAGD,WAAW;EACxB,CAAC,MACI,IAAI,OAAOE,KAAK,KAAK,WAAW,EAAE;IACnCD,MAAM,GAAGA,CAAC,GAAGE,IAAI,KAAK,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,OAAO,EAAEH;IAAM,CAAC,KAAKA,KAAK,CAAC,GAAGC,IAAI,CAAC,CAAC;EACrG,CAAC,MACI;IACDF,MAAM,GAAGC,KAAK;EAClB;EACA,OAAO,CAAC,GAAGC,IAAI,KAAKF,MAAM,CAAC,GAAGE,IAAI,CAAC;AACvC,CAAC;AACD,OAAO,MAAMG,sBAAsB,GAAIC,aAAa,IAAK;EACrD,OAAQ,OAAOA,aAAa,KAAK,QAAQ,IACrCA,aAAa,KAAK,IAAI,IACtB,QAAQ,IAAIA,aAAa,IACzB,IAAI,IAAIA,aAAa,IACrB,MAAM,IAAIA,aAAa,IACvB,OAAOA,aAAa,CAACC,IAAI,KAAK,UAAU;AAChD,CAAC;AACD;AACA,OAAO,MAAMC,YAAY;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,OAAO,EAAEf,GAAG,EAAEgB,IAAI,EAAK;IACtD,MAAMD,OAAO,CAAC5B,OAAO,CAACa,GAAG,EAAEiB,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,CAAC;EACpD,CAAC;EAAA,gBAFYJ,YAAYA,CAAAO,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAExB;AACD,OAAO,MAAMC,YAAY;EAAA,IAAAC,KAAA,GAAAX,iBAAA,CAAG,WAAOC,OAAO,EAAEf,GAAG,EAAK;IAChD,MAAMD,KAAK,SAASgB,OAAO,CAACW,OAAO,CAAC1B,GAAG,CAAC;IACxC,IAAI,CAACD,KAAK,EAAE;MACR,OAAO,IAAI;IACf;IACA,IAAI;MACA,OAAOkB,IAAI,CAACU,KAAK,CAAC5B,KAAK,CAAC;IAC5B,CAAC,CACD,OAAO6B,EAAE,EAAE;MACP,OAAO7B,KAAK;IAChB;EACJ,CAAC;EAAA,gBAXYyB,YAAYA,CAAAK,GAAA,EAAAC,GAAA;IAAA,OAAAL,KAAA,CAAAH,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWxB;AACD,OAAO,MAAMQ,eAAe;EAAA,IAAAC,KAAA,GAAAlB,iBAAA,CAAG,WAAOC,OAAO,EAAEf,GAAG,EAAK;IACnD,MAAMe,OAAO,CAAC3B,UAAU,CAACY,GAAG,CAAC;EACjC,CAAC;EAAA,gBAFY+B,eAAeA,CAAAE,GAAA,EAAAC,GAAA;IAAA,OAAAF,KAAA,CAAAV,KAAA,OAAAC,SAAA;EAAA;AAAA,GAE3B;AACD,OAAO,SAASY,eAAeA,CAACpC,KAAK,EAAE;EACnC,MAAMC,GAAG,GAAG,mEAAmE;EAC/E,IAAIoC,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,EAAEC,IAAI,EAAEC,IAAI;EACpB,IAAIC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI;EAC1B,IAAIC,CAAC,GAAG,CAAC;EACT7C,KAAK,GAAGA,KAAK,CAAC5B,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACjD,OAAOyE,CAAC,GAAG7C,KAAK,CAAC8C,MAAM,EAAE;IACrBL,IAAI,GAAGxC,GAAG,CAAC8C,OAAO,CAAC/C,KAAK,CAACgD,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IACrCH,IAAI,GAAGzC,GAAG,CAAC8C,OAAO,CAAC/C,KAAK,CAACgD,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IACrCF,IAAI,GAAG1C,GAAG,CAAC8C,OAAO,CAAC/C,KAAK,CAACgD,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IACrCD,IAAI,GAAG3C,GAAG,CAAC8C,OAAO,CAAC/C,KAAK,CAACgD,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;IACrCP,IAAI,GAAIG,IAAI,IAAI,CAAC,GAAKC,IAAI,IAAI,CAAE;IAChCH,IAAI,GAAI,CAACG,IAAI,GAAG,EAAE,KAAK,CAAC,GAAKC,IAAI,IAAI,CAAE;IACvCH,IAAI,GAAI,CAACG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAIC,IAAI;IAC/BP,MAAM,GAAGA,MAAM,GAAGY,MAAM,CAACC,YAAY,CAACZ,IAAI,CAAC;IAC3C,IAAIK,IAAI,IAAI,EAAE,IAAIJ,IAAI,IAAI,CAAC,EAAE;MACzBF,MAAM,GAAGA,MAAM,GAAGY,MAAM,CAACC,YAAY,CAACX,IAAI,CAAC;IAC/C;IACA,IAAIK,IAAI,IAAI,EAAE,IAAIJ,IAAI,IAAI,CAAC,EAAE;MACzBH,MAAM,GAAGA,MAAM,GAAGY,MAAM,CAACC,YAAY,CAACV,IAAI,CAAC;IAC/C;EACJ;EACA,OAAOH,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,QAAQ,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACC,OAAO,GAAG,IAAIF,QAAQ,CAACG,kBAAkB,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACzD;MACA;MACA,IAAI,CAACC,OAAO,GAAGF,GAAG;MAClB,IAAI,CAACG,MAAM,GAAGF,GAAG;IACrB,CAAC,CAAC;EACN;AACJ;AACAL,QAAQ,CAACG,kBAAkB,GAAGK,OAAO;AACrC;AACA,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACpC;EACA,MAAMC,cAAc,GAAG,6DAA6D;EACpF,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAID,KAAK,CAACjB,MAAM,KAAK,CAAC,EAAE;IACpB,MAAM,IAAImB,KAAK,CAAC,uCAAuC,CAAC;EAC5D;EACA,IAAI,CAACH,cAAc,CAACI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAChC,MAAM,IAAIE,KAAK,CAAC,sDAAsD,CAAC;EAC3E;EACA,MAAME,SAAS,GAAGJ,KAAK,CAAC,CAAC,CAAC;EAC1B,OAAO7C,IAAI,CAACU,KAAK,CAACQ,eAAe,CAAC+B,SAAS,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA,gBAAsBC,KAAKA,CAAAC,GAAA;EAAA,OAAAC,MAAA,CAAA/C,KAAA,OAAAC,SAAA;AAAA;AAK3B;AACA;AACA;AACA;AACA;AAJA,SAAA8C,OAAA;EAAAA,MAAA,GAAAvD,iBAAA,CALO,WAAqBwD,IAAI,EAAE;IAC9B,aAAa,IAAIZ,OAAO,CAAEa,MAAM,IAAK;MACjCC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAI,CAAC,EAAED,IAAI,CAAC;IACxC,CAAC,CAAC;EACN,CAAC;EAAA,OAAAD,MAAA,CAAA/C,KAAA,OAAAC,SAAA;AAAA;AAMD,OAAO,SAASkD,SAASA,CAACC,EAAE,EAAEC,WAAW,EAAE;EACvC,MAAMvB,OAAO,GAAG,IAAIM,OAAO,CAAC,CAACa,MAAM,EAAEd,MAAM,KAAK;IAC5C;IACA;IACA3C,iBAAA,CAAC,aAAY;MACT,KAAK,IAAI8D,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGC,QAAQ,EAAED,OAAO,EAAE,EAAE;QACjD,IAAI;UACA,MAAMrF,MAAM,SAASmF,EAAE,CAACE,OAAO,CAAC;UAChC,IAAI,CAACD,WAAW,CAACC,OAAO,EAAE,IAAI,EAAErF,MAAM,CAAC,EAAE;YACrCgF,MAAM,CAAChF,MAAM,CAAC;YACd;UACJ;QACJ,CAAC,CACD,OAAON,CAAC,EAAE;UACN,IAAI,CAAC0F,WAAW,CAACC,OAAO,EAAE3F,CAAC,CAAC,EAAE;YAC1BwE,MAAM,CAACxE,CAAC,CAAC;YACT;UACJ;QACJ;MACJ;IACJ,CAAC,EAAE,CAAC;EACR,CAAC,CAAC;EACF,OAAOmE,OAAO;AAClB;AACA,SAAS0B,OAAOA,CAACC,GAAG,EAAE;EAClB,OAAO,CAAC,GAAG,GAAGA,GAAG,CAACvG,QAAQ,CAAC,EAAE,CAAC,EAAEwG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9C;AACA;AACA,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACnC,MAAMC,cAAc,GAAG,EAAE;EACzB,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAACF,cAAc,CAAC;EAC7C,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,OAAO,GAAG,oEAAoE;IACpF,MAAMC,UAAU,GAAGD,OAAO,CAACzC,MAAM;IACjC,IAAI2C,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,cAAc,EAAEtC,CAAC,EAAE,EAAE;MACrC4C,QAAQ,IAAIF,OAAO,CAACvC,MAAM,CAACjF,IAAI,CAAC2H,KAAK,CAAC3H,IAAI,CAACQ,MAAM,CAAC,CAAC,GAAGiH,UAAU,CAAC,CAAC;IACtE;IACA,OAAOC,QAAQ;EACnB;EACAH,MAAM,CAACK,eAAe,CAACP,KAAK,CAAC;EAC7B,OAAOQ,KAAK,CAACC,IAAI,CAACT,KAAK,EAAEL,OAAO,CAAC,CAACe,IAAI,CAAC,EAAE,CAAC;AAC9C;AAAC,SACcC,MAAMA,CAAAC,GAAA;EAAA,OAAAC,IAAA,CAAA1E,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAyE,KAAA;EAAAA,IAAA,GAAAlF,iBAAA,CAArB,WAAsBmF,YAAY,EAAE;IAChC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;IACjC,MAAMC,WAAW,GAAGF,OAAO,CAACG,MAAM,CAACJ,YAAY,CAAC;IAChD,MAAMvG,IAAI,SAAS2F,MAAM,CAACiB,MAAM,CAACC,MAAM,CAAC,SAAS,EAAEH,WAAW,CAAC;IAC/D,MAAMI,KAAK,GAAG,IAAIC,UAAU,CAAC/G,IAAI,CAAC;IAClC,OAAOiG,KAAK,CAACC,IAAI,CAACY,KAAK,CAAC,CACnBE,GAAG,CAAEtI,CAAC,IAAK4E,MAAM,CAACC,YAAY,CAAC7E,CAAC,CAAC,CAAC,CAClCyH,IAAI,CAAC,EAAE,CAAC;EACjB,CAAC;EAAA,OAAAG,IAAA,CAAA1E,KAAA,OAAAC,SAAA;AAAA;AACD,SAASoF,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAOC,IAAI,CAACD,GAAG,CAAC,CAACzI,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC/E;AACA,gBAAsB2I,qBAAqBA,CAAAC,GAAA;EAAA,OAAAC,sBAAA,CAAA1F,KAAA,OAAAC,SAAA;AAAA;AAU1C,SAAAyF,uBAAA;EAAAA,sBAAA,GAAAlG,iBAAA,CAVM,WAAqC0E,QAAQ,EAAE;IAClD,MAAMyB,gBAAgB,GAAG,OAAO5B,MAAM,KAAK,WAAW,IAClD,OAAOA,MAAM,CAACiB,MAAM,KAAK,WAAW,IACpC,OAAOH,WAAW,KAAK,WAAW;IACtC,IAAI,CAACc,gBAAgB,EAAE;MACnBC,OAAO,CAACC,IAAI,CAAC,oGAAoG,CAAC;MAClH,OAAO3B,QAAQ;IACnB;IACA,MAAM4B,MAAM,SAAStB,MAAM,CAACN,QAAQ,CAAC;IACrC,OAAOmB,eAAe,CAACS,MAAM,CAAC;EAClC,CAAC;EAAA,OAAAJ,sBAAA,CAAA1F,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}