{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { FunctionsClient } from '@supabase/functions-js';\nimport { PostgrestClient } from '@supabase/postgrest-js';\nimport { RealtimeClient } from '@supabase/realtime-js';\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js';\nimport { DEFAULT_GLOBAL_OPTIONS, DEFAULT_DB_OPTIONS, DEFAULT_AUTH_OPTIONS, DEFAULT_REALTIME_OPTIONS } from './lib/constants';\nimport { fetchWithAuth } from './lib/fetch';\nimport { stripTrailingSlash, applySettingDefaults } from './lib/helpers';\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient';\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient {\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(supabaseUrl, supabaseKey, options) {\n    var _a, _b, _c;\n    this.supabaseUrl = supabaseUrl;\n    this.supabaseKey = supabaseKey;\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n    if (!supabaseKey) throw new Error('supabaseKey is required.');\n    const _supabaseUrl = stripTrailingSlash(supabaseUrl);\n    this.realtimeUrl = `${_supabaseUrl}/realtime/v1`.replace(/^http/i, 'ws');\n    this.authUrl = `${_supabaseUrl}/auth/v1`;\n    this.storageUrl = `${_supabaseUrl}/storage/v1`;\n    this.functionsUrl = `${_supabaseUrl}/functions/v1`;\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${new URL(this.authUrl).hostname.split('.')[0]}-auth-token`;\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), {\n        storageKey: defaultStorageKey\n      }),\n      global: DEFAULT_GLOBAL_OPTIONS\n    };\n    const settings = applySettingDefaults(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n    this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n    this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n    } else {\n      this.accessToken = settings.accessToken;\n      this.auth = new Proxy({}, {\n        get: (_, prop) => {\n          throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n        }\n      });\n    }\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n    this.realtime = this._initRealtimeClient(Object.assign({\n      headers: this.headers,\n      accessToken: this._getAccessToken.bind(this)\n    }, settings.realtime));\n    this.rest = new PostgrestClient(`${_supabaseUrl}/rest/v1`, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch\n    });\n    if (!settings.accessToken) {\n      this._listenForAuthEvents();\n    }\n  }\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions() {\n    return new FunctionsClient(this.functionsUrl, {\n      headers: this.headers,\n      customFetch: this.fetch\n    });\n  }\n  /**\n   * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n   */\n  get storage() {\n    return new SupabaseStorageClient(this.storageUrl, this.headers, this.fetch);\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    return this.rest.from(relation);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return this.rest.schema(schema);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, options = {}) {\n    return this.rest.rpc(fn, args, options);\n  }\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name, opts = {\n    config: {}\n  }) {\n    return this.realtime.channel(name, opts);\n  }\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels() {\n    return this.realtime.getChannels();\n  }\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel) {\n    return this.realtime.removeChannel(channel);\n  }\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels() {\n    return this.realtime.removeAllChannels();\n  }\n  _getAccessToken() {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.accessToken) {\n        return yield this.accessToken();\n      }\n      const {\n        data\n      } = yield this.auth.getSession();\n      return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n    });\n  }\n  _initSupabaseAuthClient({\n    autoRefreshToken,\n    persistSession,\n    detectSessionInUrl,\n    storage,\n    storageKey,\n    flowType,\n    lock,\n    debug\n  }, headers, fetch) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`\n    };\n    return new SupabaseAuthClient({\n      url: this.authUrl,\n      headers: Object.assign(Object.assign({}, authHeaders), headers),\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers\n    });\n  }\n  _initRealtimeClient(options) {\n    return new RealtimeClient(this.realtimeUrl, Object.assign(Object.assign({}, options), {\n      params: Object.assign({\n        apikey: this.supabaseKey\n      }, options === null || options === void 0 ? void 0 : options.params)\n    }));\n  }\n  _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n    });\n    return data;\n  }\n  _handleTokenChanged(event, source, token) {\n    if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n      this.changedAccessToken = token;\n    } else if (event === 'SIGNED_OUT') {\n      this.realtime.setAuth();\n      if (source == 'STORAGE') this.auth.signOut();\n      this.changedAccessToken = undefined;\n    }\n  }\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "FunctionsClient", "PostgrestClient", "RealtimeClient", "StorageClient", "SupabaseStorageClient", "DEFAULT_GLOBAL_OPTIONS", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "fetchWithAuth", "stripTrailingSlash", "applySettingDefaults", "SupabaseAuthClient", "SupabaseClient", "constructor", "supabaseUrl", "supabase<PERSON>ey", "options", "_a", "_b", "_c", "Error", "_supabaseUrl", "realtimeUrl", "replace", "authUrl", "storageUrl", "functionsUrl", "defaultStorageKey", "URL", "hostname", "split", "DEFAULTS", "db", "realtime", "auth", "Object", "assign", "storageKey", "global", "settings", "headers", "accessToken", "_initSupabaseAuthClient", "fetch", "Proxy", "get", "_", "prop", "String", "_getAccessToken", "bind", "_initRealtimeClient", "rest", "schema", "_listenForAuthEvents", "functions", "customFetch", "storage", "from", "relation", "rpc", "fn", "args", "channel", "name", "opts", "config", "getChannels", "removeChannel", "removeAllChannels", "data", "getSession", "session", "access_token", "autoRefreshToken", "persistSession", "detectSessionInUrl", "flowType", "lock", "debug", "authHeaders", "Authorization", "apikey", "url", "hasCustomAuthorizationHeader", "params", "onAuthStateChange", "event", "_handleTokenChanged", "source", "token", "changedAccessToken", "setAuth", "signOut", "undefined"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { FunctionsClient } from '@supabase/functions-js';\nimport { PostgrestClient, } from '@supabase/postgrest-js';\nimport { RealtimeClient, } from '@supabase/realtime-js';\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js';\nimport { DEFAULT_GLOBAL_OPTIONS, DEFAULT_DB_OPTIONS, DEFAULT_AUTH_OPTIONS, DEFAULT_REALTIME_OPTIONS, } from './lib/constants';\nimport { fetchWithAuth } from './lib/fetch';\nimport { stripTrailingSlash, applySettingDefaults } from './lib/helpers';\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient';\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = stripTrailingSlash(supabaseUrl);\n        this.realtimeUrl = `${_supabaseUrl}/realtime/v1`.replace(/^http/i, 'ws');\n        this.authUrl = `${_supabaseUrl}/auth/v1`;\n        this.storageUrl = `${_supabaseUrl}/storage/v1`;\n        this.functionsUrl = `${_supabaseUrl}/functions/v1`;\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${new URL(this.authUrl).hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: DEFAULT_DB_OPTIONS,\n            realtime: DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = applySettingDefaults(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new PostgrestClient(`${_supabaseUrl}/rest/v1`, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new FunctionsClient(this.functionsUrl, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */\n    get storage() {\n        return new SupabaseStorageClient(this.storageUrl, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new SupabaseAuthClient({\n            url: this.authUrl,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new RealtimeClient(this.realtimeUrl, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAS,wBAAwB;AACzD,SAASC,cAAc,QAAS,uBAAuB;AACvD,SAASC,aAAa,IAAIC,qBAAqB,QAAQ,sBAAsB;AAC7E,SAASC,sBAAsB,EAAEC,kBAAkB,EAAEC,oBAAoB,EAAEC,wBAAwB,QAAS,iBAAiB;AAC7H,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,kBAAkB,EAAEC,oBAAoB,QAAQ,eAAe;AACxE,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMC,cAAc,CAAC;EAChC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,WAAW,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAC3C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IACd,IAAI,CAACL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,WAAW,EACZ,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;IAC/C,IAAI,CAACL,WAAW,EACZ,MAAM,IAAIK,KAAK,CAAC,0BAA0B,CAAC;IAC/C,MAAMC,YAAY,GAAGZ,kBAAkB,CAACK,WAAW,CAAC;IACpD,IAAI,CAACQ,WAAW,GAAG,GAAGD,YAAY,cAAc,CAACE,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;IACxE,IAAI,CAACC,OAAO,GAAG,GAAGH,YAAY,UAAU;IACxC,IAAI,CAACI,UAAU,GAAG,GAAGJ,YAAY,aAAa;IAC9C,IAAI,CAACK,YAAY,GAAG,GAAGL,YAAY,eAAe;IAClD;IACA,MAAMM,iBAAiB,GAAG,MAAM,IAAIC,GAAG,CAAC,IAAI,CAACJ,OAAO,CAAC,CAACK,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa;IACzF,MAAMC,QAAQ,GAAG;MACbC,EAAE,EAAE3B,kBAAkB;MACtB4B,QAAQ,EAAE1B,wBAAwB;MAClC2B,IAAI,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE9B,oBAAoB,CAAC,EAAE;QAAE+B,UAAU,EAAEV;MAAkB,CAAC,CAAC;MAC/FW,MAAM,EAAElC;IACZ,CAAC;IACD,MAAMmC,QAAQ,GAAG7B,oBAAoB,CAACM,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC,EAAEe,QAAQ,CAAC;IACtG,IAAI,CAACM,UAAU,GAAG,CAACpB,EAAE,GAAGsB,QAAQ,CAACL,IAAI,CAACG,UAAU,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;IACrF,IAAI,CAACuB,OAAO,GAAG,CAACtB,EAAE,GAAGqB,QAAQ,CAACD,MAAM,CAACE,OAAO,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACjF,IAAI,CAACqB,QAAQ,CAACE,WAAW,EAAE;MACvB,IAAI,CAACP,IAAI,GAAG,IAAI,CAACQ,uBAAuB,CAAC,CAACvB,EAAE,GAAGoB,QAAQ,CAACL,IAAI,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACqB,OAAO,EAAED,QAAQ,CAACD,MAAM,CAACK,KAAK,CAAC;IAC3I,CAAC,MACI;MACD,IAAI,CAACF,WAAW,GAAGF,QAAQ,CAACE,WAAW;MACvC,IAAI,CAACP,IAAI,GAAG,IAAIU,KAAK,CAAC,CAAC,CAAC,EAAE;QACtBC,GAAG,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAK;UACd,MAAM,IAAI3B,KAAK,CAAC,6GAA6G4B,MAAM,CAACD,IAAI,CAAC,kBAAkB,CAAC;QAChK;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACJ,KAAK,GAAGnC,aAAa,CAACO,WAAW,EAAE,IAAI,CAACkC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,EAAEX,QAAQ,CAACD,MAAM,CAACK,KAAK,CAAC;IAC/F,IAAI,CAACV,QAAQ,GAAG,IAAI,CAACkB,mBAAmB,CAAChB,MAAM,CAACC,MAAM,CAAC;MAAEI,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACQ,eAAe,CAACC,IAAI,CAAC,IAAI;IAAE,CAAC,EAAEX,QAAQ,CAACN,QAAQ,CAAC,CAAC;IACnJ,IAAI,CAACmB,IAAI,GAAG,IAAIpD,eAAe,CAAC,GAAGqB,YAAY,UAAU,EAAE;MACvDmB,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBa,MAAM,EAAEd,QAAQ,CAACP,EAAE,CAACqB,MAAM;MAC1BV,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC;IACF,IAAI,CAACJ,QAAQ,CAACE,WAAW,EAAE;MACvB,IAAI,CAACa,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;EACI,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAIxD,eAAe,CAAC,IAAI,CAAC2B,YAAY,EAAE;MAC1Cc,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBgB,WAAW,EAAE,IAAI,CAACb;IACtB,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI,IAAIc,OAAOA,CAAA,EAAG;IACV,OAAO,IAAItD,qBAAqB,CAAC,IAAI,CAACsB,UAAU,EAAE,IAAI,CAACe,OAAO,EAAE,IAAI,CAACG,KAAK,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;EACIe,IAAIA,CAACC,QAAQ,EAAE;IACX,OAAO,IAAI,CAACP,IAAI,CAACM,IAAI,CAACC,QAAQ,CAAC;EACnC;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIN,MAAMA,CAACA,MAAM,EAAE;IACX,OAAO,IAAI,CAACD,IAAI,CAACC,MAAM,CAACA,MAAM,CAAC;EACnC;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,GAAGA,CAACC,EAAE,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE9C,OAAO,GAAG,CAAC,CAAC,EAAE;IAC7B,OAAO,IAAI,CAACoC,IAAI,CAACQ,GAAG,CAACC,EAAE,EAAEC,IAAI,EAAE9C,OAAO,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+C,OAAOA,CAACC,IAAI,EAAEC,IAAI,GAAG;IAAEC,MAAM,EAAE,CAAC;EAAE,CAAC,EAAE;IACjC,OAAO,IAAI,CAACjC,QAAQ,CAAC8B,OAAO,CAACC,IAAI,EAAEC,IAAI,CAAC;EAC5C;EACA;AACJ;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClC,QAAQ,CAACkC,WAAW,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,aAAaA,CAACL,OAAO,EAAE;IACnB,OAAO,IAAI,CAAC9B,QAAQ,CAACmC,aAAa,CAACL,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;EACIM,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpC,QAAQ,CAACoC,iBAAiB,CAAC,CAAC;EAC5C;EACApB,eAAeA,CAAA,EAAG;IACd,IAAIhC,EAAE,EAAEC,EAAE;IACV,OAAOtC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAChD,IAAI,IAAI,CAAC6D,WAAW,EAAE;QAClB,OAAO,MAAM,IAAI,CAACA,WAAW,CAAC,CAAC;MACnC;MACA,MAAM;QAAE6B;MAAK,CAAC,GAAG,MAAM,IAAI,CAACpC,IAAI,CAACqC,UAAU,CAAC,CAAC;MAC7C,OAAO,CAACrD,EAAE,GAAG,CAACD,EAAE,GAAGqD,IAAI,CAACE,OAAO,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwD,YAAY,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IAChI,CAAC,CAAC;EACN;EACAwB,uBAAuBA,CAAC;IAAEgC,gBAAgB;IAAEC,cAAc;IAAEC,kBAAkB;IAAEnB,OAAO;IAAEpB,UAAU;IAAEwC,QAAQ;IAAEC,IAAI;IAAEC;EAAO,CAAC,EAAEvC,OAAO,EAAEG,KAAK,EAAE;IAC3I,MAAMqC,WAAW,GAAG;MAChBC,aAAa,EAAE,UAAU,IAAI,CAAClE,WAAW,EAAE;MAC3CmE,MAAM,EAAE,GAAG,IAAI,CAACnE,WAAW;IAC/B,CAAC;IACD,OAAO,IAAIJ,kBAAkB,CAAC;MAC1BwE,GAAG,EAAE,IAAI,CAAC3D,OAAO;MACjBgB,OAAO,EAAEL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4C,WAAW,CAAC,EAAExC,OAAO,CAAC;MAC/DH,UAAU,EAAEA,UAAU;MACtBqC,gBAAgB;MAChBC,cAAc;MACdC,kBAAkB;MAClBnB,OAAO;MACPoB,QAAQ;MACRC,IAAI;MACJC,KAAK;MACLpC,KAAK;MACL;MACA;MACAyC,4BAA4B,EAAE,eAAe,IAAI,IAAI,CAAC5C;IAC1D,CAAC,CAAC;EACN;EACAW,mBAAmBA,CAACnC,OAAO,EAAE;IACzB,OAAO,IAAIf,cAAc,CAAC,IAAI,CAACqB,WAAW,EAAEa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,OAAO,CAAC,EAAE;MAAEqE,MAAM,EAAElD,MAAM,CAACC,MAAM,CAAC;QAAE8C,MAAM,EAAE,IAAI,CAACnE;MAAY,CAAC,EAAEC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqE,MAAM;IAAE,CAAC,CAAC,CAAC;EACrN;EACA/B,oBAAoBA,CAAA,EAAG;IACnB,IAAIgB,IAAI,GAAG,IAAI,CAACpC,IAAI,CAACoD,iBAAiB,CAAC,CAACC,KAAK,EAAEf,OAAO,KAAK;MACvD,IAAI,CAACgB,mBAAmB,CAACD,KAAK,EAAE,QAAQ,EAAEf,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,YAAY,CAAC;IACrH,CAAC,CAAC;IACF,OAAOH,IAAI;EACf;EACAkB,mBAAmBA,CAACD,KAAK,EAAEE,MAAM,EAAEC,KAAK,EAAE;IACtC,IAAI,CAACH,KAAK,KAAK,iBAAiB,IAAIA,KAAK,KAAK,WAAW,KACrD,IAAI,CAACI,kBAAkB,KAAKD,KAAK,EAAE;MACnC,IAAI,CAACC,kBAAkB,GAAGD,KAAK;IACnC,CAAC,MACI,IAAIH,KAAK,KAAK,YAAY,EAAE;MAC7B,IAAI,CAACtD,QAAQ,CAAC2D,OAAO,CAAC,CAAC;MACvB,IAAIH,MAAM,IAAI,SAAS,EACnB,IAAI,CAACvD,IAAI,CAAC2D,OAAO,CAAC,CAAC;MACvB,IAAI,CAACF,kBAAkB,GAAGG,SAAS;IACvC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}