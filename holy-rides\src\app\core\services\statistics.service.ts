import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import {
  ActivityItem,
  SystemStatistics,
  DateRange,
  RideReportData,
  RevenueReportData,
  UserActivityReportData,
  ReportOptions
} from '../models/statistics.model';
import { RideService } from './ride.service';
import { UserService } from './user.service';
import { PaymentService } from './payment.service';
import { Ride } from '../models/ride.model';
import { User } from '../models/user.model';


@Injectable({
  providedIn: 'root'
})
export class StatisticsService {
  private statisticsSubject = new BehaviorSubject<SystemStatistics | null>(null);
  statistics$ = this.statisticsSubject.asObservable();

  constructor(
    private userService: UserService,
    private rideService: RideService,
    private paymentService: PaymentService
  ) {}

  async generateSystemStatistics(): Promise<SystemStatistics> {
    // In a real app, you might fetch this data from Supabase
    // For now, we'll generate mock statistics

    const allUsers = await this.userService.getAllUsers();
    const riders = allUsers.filter(user => user.role === 'rider');
    const drivers = allUsers.filter(user => user.role === 'driver');
    const admins = allUsers.filter(user => user.role === 'admin');

    const allRides = await this.rideService.getAllRides();
    const completedRides = allRides.filter(ride => ride.status === 'completed');
    const inProgressRides = allRides.filter(ride => ride.status === 'in-progress');
    const requestedRides = allRides.filter(ride => ride.status === 'requested');
    const canceledRides = allRides.filter(ride => ride.status === 'canceled');

    // Generate mock recent activity
    const recentActivity: ActivityItem[] = [
      {
        id: '1',
        type: 'user_registered',
        timestamp: new Date().toISOString(),
        details: { userId: 'user1', role: 'rider' }
      },
      {
        id: '2',
        type: 'ride_requested',
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        details: { rideId: 'ride1', riderId: 'rider1' }
      },
      {
        id: '3',
        type: 'ride_completed',
        timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        details: { rideId: 'ride2', riderId: 'rider2', driverId: 'driver1' }
      },
      {
        id: '4',
        type: 'driver_approved',
        timestamp: new Date(Date.now() - 10800000).toISOString(), // 3 hours ago
        details: { driverId: 'driver2' }
      }
    ];

    const statistics: SystemStatistics = {
      totalUsers: {
        all: allUsers.length,
        riders: riders.length,
        drivers: drivers.length,
        admins: admins.length
      },
      rides: {
        total: allRides.length,
        completed: completedRides.length,
        inProgress: inProgressRides.length,
        requested: requestedRides.length,
        canceled: canceledRides.length
      },
      recentActivity
    };

    this.statisticsSubject.next(statistics);
    return statistics;
  }

  /**
   * Generate a ride report based on the given options
   */
  async generateRideReport(options: ReportOptions): Promise<RideReportData> {
    const { dateRange } = options;

    // Get all rides
    const allRides = await this.rideService.getAllRides();

    // Filter rides by date range
    const filteredRides = allRides.filter(ride => {
      const rideDate = new Date(ride.created_at);
      return rideDate >= dateRange.startDate && rideDate <= dateRange.endDate;
    });

    // Count rides by status
    const requested = filteredRides.filter(ride => ride.status === 'requested').length;
    const assigned = filteredRides.filter(ride => ride.status === 'assigned').length;
    const inProgress = filteredRides.filter(ride => ride.status === 'in-progress').length;
    const completed = filteredRides.filter(ride => ride.status === 'completed').length;
    const canceled = filteredRides.filter(ride => ride.status === 'canceled').length;

    // Group rides by day
    const ridesByDay = this.groupRidesByDay(filteredRides);

    // Calculate average duration and distance
    const completedRidesWithData = filteredRides.filter(
      ride => ride.status === 'completed' && ride.duration_minutes && ride.distance_miles
    );

    const averageDuration = completedRidesWithData.length > 0
      ? completedRidesWithData.reduce((sum, ride) => sum + (ride.duration_minutes || 0), 0) / completedRidesWithData.length
      : 0;

    const averageDistance = completedRidesWithData.length > 0
      ? completedRidesWithData.reduce((sum, ride) => sum + (ride.distance_miles || 0), 0) / completedRidesWithData.length
      : 0;

    return {
      dateRange,
      ridesByStatus: {
        requested,
        assigned,
        inProgress,
        completed,
        canceled
      },
      ridesByDay,
      averageDuration,
      averageDistance
    };
  }

  /**
   * Generate a revenue report based on the given options
   */
  async generateRevenueReport(options: ReportOptions): Promise<RevenueReportData> {
    const { dateRange } = options;

    // Get all rides
    const allRides = await this.rideService.getAllRides();
    const allUsers = await this.userService.getAllUsers();

    // Filter rides by date range
    const filteredRides = allRides.filter(ride => {
      const rideDate = new Date(ride.created_at);
      return rideDate >= dateRange.startDate && rideDate <= dateRange.endDate;
    });

    // Calculate total revenue
    const totalRevenue = filteredRides.reduce((sum, ride) => sum + (ride.amount || ride.fare || 0), 0);

    // Group revenue by day
    const revenueByDay = this.groupRevenueByDay(filteredRides);

    // Calculate revenue by payment status
    const pending = filteredRides
      .filter(ride => ride.payment_status === 'pending')
      .reduce((sum, ride) => sum + (ride.amount || ride.fare || 0), 0);

    const paid = filteredRides
      .filter(ride => ride.payment_status === 'paid')
      .reduce((sum, ride) => sum + (ride.amount || ride.fare || 0), 0);

    const failed = filteredRides
      .filter(ride => ride.payment_status === 'failed')
      .reduce((sum, ride) => sum + (ride.amount || ride.fare || 0), 0);

    const refunded = filteredRides
      .filter(ride => ride.payment_status === 'refunded')
      .reduce((sum, ride) => sum + (ride.amount || ride.fare || 0), 0);

    // Calculate top drivers by revenue
    const driverRevenueMap = new Map<string, { revenue: number, rideCount: number }>();

    filteredRides
      .filter(ride => ride.driver_id && (ride.payment_status === 'paid' || ride.status === 'completed'))
      .forEach(ride => {
        const driverId = ride.driver_id as string;
        const amount = ride.amount || ride.fare || 0;

        if (driverRevenueMap.has(driverId)) {
          const current = driverRevenueMap.get(driverId)!;
          driverRevenueMap.set(driverId, {
            revenue: current.revenue + amount,
            rideCount: current.rideCount + 1
          });
        } else {
          driverRevenueMap.set(driverId, { revenue: amount, rideCount: 1 });
        }
      });

    const topDriversByRevenue = Array.from(driverRevenueMap.entries())
      .map(([driverId, { revenue, rideCount }]) => {
        const driver = allUsers.find(user => user.id === driverId);
        return {
          driverId,
          driverName: driver?.full_name || 'Unknown Driver',
          revenue,
          rideCount
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    return {
      dateRange,
      totalRevenue,
      revenueByDay,
      revenueByStatus: {
        pending,
        paid,
        failed,
        refunded
      },
      topDriversByRevenue
    };
  }

  /**
   * Generate a user activity report based on the given options
   */
  async generateUserActivityReport(options: ReportOptions): Promise<UserActivityReportData> {
    const { dateRange, userRole } = options;

    // Get all users and rides
    const allUsers = await this.userService.getAllUsers();
    const allRides = await this.rideService.getAllRides();

    // Filter users by role if specified
    const filteredUsers = userRole
      ? allUsers.filter(user => user.role === userRole)
      : allUsers;

    // Filter users by date range (new users)
    const newUsers = filteredUsers.filter(user => {
      const createdDate = new Date(user.created_at);
      return createdDate >= dateRange.startDate && createdDate <= dateRange.endDate;
    });

    // Count new users by role
    const newRiders = newUsers.filter(user => user.role === 'rider').length;
    const newDrivers = newUsers.filter(user => user.role === 'driver').length;

    // Calculate active users (users who had a ride in the date range)
    const activeRiderIds = new Set(
      allRides
        .filter(ride => {
          const rideDate = new Date(ride.created_at);
          return rideDate >= dateRange.startDate && rideDate <= dateRange.endDate;
        })
        .map(ride => ride.rider_id)
    );

    const activeDriverIds = new Set(
      allRides
        .filter(ride => {
          const rideDate = new Date(ride.created_at);
          return ride.driver_id && rideDate >= dateRange.startDate && rideDate <= dateRange.endDate;
        })
        .map(ride => ride.driver_id as string)
    );

    const activeRiders = filteredUsers.filter(user => user.role === 'rider' && activeRiderIds.has(user.id)).length;
    const activeDrivers = filteredUsers.filter(user => user.role === 'driver' && activeDriverIds.has(user.id)).length;

    // Group users by day
    const usersByDay = this.groupUsersByDay(newUsers, allRides, dateRange);

    // Calculate top riders
    const riderActivityMap = new Map<string, { rideCount: number, totalSpent: number }>();

    allRides
      .filter(ride => {
        const rideDate = new Date(ride.created_at);
        return rideDate >= dateRange.startDate && rideDate <= dateRange.endDate;
      })
      .forEach(ride => {
        const riderId = ride.rider_id;
        const amount = ride.amount || ride.fare || 0;

        if (riderActivityMap.has(riderId)) {
          const current = riderActivityMap.get(riderId)!;
          riderActivityMap.set(riderId, {
            rideCount: current.rideCount + 1,
            totalSpent: current.totalSpent + amount
          });
        } else {
          riderActivityMap.set(riderId, { rideCount: 1, totalSpent: amount });
        }
      });

    const topRiders = Array.from(riderActivityMap.entries())
      .map(([riderId, { rideCount, totalSpent }]) => {
        const rider = allUsers.find(user => user.id === riderId);
        return {
          riderId,
          riderName: rider?.full_name || 'Unknown Rider',
          rideCount,
          totalSpent
        };
      })
      .sort((a, b) => b.rideCount - a.rideCount)
      .slice(0, 5);

    return {
      dateRange,
      newUsers: {
        total: newUsers.length,
        riders: newRiders,
        drivers: newDrivers
      },
      activeUsers: {
        total: activeRiders + activeDrivers,
        riders: activeRiders,
        drivers: activeDrivers
      },
      usersByDay,
      topRiders
    };
  }

  /**
   * Group rides by day
   */
  private groupRidesByDay(rides: Ride[]): { date: string, count: number }[] {
    const ridesByDayMap = new Map<string, number>();

    rides.forEach(ride => {
      const date = new Date(ride.created_at);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD

      if (ridesByDayMap.has(dateString)) {
        ridesByDayMap.set(dateString, ridesByDayMap.get(dateString)! + 1);
      } else {
        ridesByDayMap.set(dateString, 1);
      }
    });

    return Array.from(ridesByDayMap.entries())
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Group revenue by day
   */
  private groupRevenueByDay(rides: Ride[]): { date: string, amount: number }[] {
    const revenueByDayMap = new Map<string, number>();

    rides.forEach(ride => {
      const date = new Date(ride.created_at);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD
      const amount = ride.amount || ride.fare || 0;

      if (revenueByDayMap.has(dateString)) {
        revenueByDayMap.set(dateString, revenueByDayMap.get(dateString)! + amount);
      } else {
        revenueByDayMap.set(dateString, amount);
      }
    });

    return Array.from(revenueByDayMap.entries())
      .map(([date, amount]) => ({ date, amount }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Group users by day
   */
  private groupUsersByDay(
    newUsers: User[],
    allRides: Ride[],
    dateRange: DateRange
  ): { date: string, newUsers: number, activeUsers: number }[] {
    const usersByDayMap = new Map<string, { newUsers: number, activeUsers: number }>();

    // Initialize map with all days in the date range
    const currentDate = new Date(dateRange.startDate);
    while (currentDate <= dateRange.endDate) {
      const dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
      usersByDayMap.set(dateString, { newUsers: 0, activeUsers: 0 });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Count new users by day
    newUsers.forEach(user => {
      const date = new Date(user.created_at);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD

      if (usersByDayMap.has(dateString)) {
        const current = usersByDayMap.get(dateString)!;
        usersByDayMap.set(dateString, {
          ...current,
          newUsers: current.newUsers + 1
        });
      }
    });

    // Count active users by day
    allRides.forEach(ride => {
      const date = new Date(ride.created_at);
      const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD

      if (usersByDayMap.has(dateString)) {
        const current = usersByDayMap.get(dateString)!;
        // Only count each user once per day
        usersByDayMap.set(dateString, {
          ...current,
          activeUsers: current.activeUsers + 1
        });
      }
    });

    return Array.from(usersByDayMap.entries())
      .map(([date, { newUsers, activeUsers }]) => ({ date, newUsers, activeUsers }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Export report data to CSV
   */
  exportReportToCsv(reportType: string, data: any): string {
    let csvContent = '';

    switch (reportType) {
      case 'rides':
        csvContent = this.exportRideReportToCsv(data as RideReportData);
        break;
      case 'revenue':
        csvContent = this.exportRevenueReportToCsv(data as RevenueReportData);
        break;
      case 'users':
        csvContent = this.exportUserReportToCsv(data as UserActivityReportData);
        break;
    }

    return csvContent;
  }

  /**
   * Export ride report to CSV
   */
  private exportRideReportToCsv(data: RideReportData): string {
    // Header
    let csv = 'Report Type,Ride Statistics\n';
    csv += `Date Range,${data.dateRange.startDate.toISOString().split('T')[0]} to ${data.dateRange.endDate.toISOString().split('T')[0]}\n\n`;

    // Rides by status
    csv += 'Rides by Status\n';
    csv += 'Status,Count\n';
    csv += `Requested,${data.ridesByStatus.requested}\n`;
    csv += `Assigned,${data.ridesByStatus.assigned}\n`;
    csv += `In Progress,${data.ridesByStatus.inProgress}\n`;
    csv += `Completed,${data.ridesByStatus.completed}\n`;
    csv += `Canceled,${data.ridesByStatus.canceled}\n\n`;

    // Average metrics
    csv += 'Average Metrics\n';
    csv += `Average Duration (minutes),${data.averageDuration.toFixed(2)}\n`;
    csv += `Average Distance (miles),${data.averageDistance.toFixed(2)}\n\n`;

    // Rides by day
    csv += 'Rides by Day\n';
    csv += 'Date,Count\n';
    data.ridesByDay.forEach(day => {
      csv += `${day.date},${day.count}\n`;
    });

    return csv;
  }

  /**
   * Export revenue report to CSV
   */
  private exportRevenueReportToCsv(data: RevenueReportData): string {
    // Header
    let csv = 'Report Type,Revenue Statistics\n';
    csv += `Date Range,${data.dateRange.startDate.toISOString().split('T')[0]} to ${data.dateRange.endDate.toISOString().split('T')[0]}\n`;
    csv += `Total Revenue,$${data.totalRevenue.toFixed(2)}\n\n`;

    // Revenue by status
    csv += 'Revenue by Payment Status\n';
    csv += 'Status,Amount\n';
    csv += `Pending,$${data.revenueByStatus.pending.toFixed(2)}\n`;
    csv += `Paid,$${data.revenueByStatus.paid.toFixed(2)}\n`;
    csv += `Failed,$${data.revenueByStatus.failed.toFixed(2)}\n`;
    csv += `Refunded,$${data.revenueByStatus.refunded.toFixed(2)}\n\n`;

    // Top drivers
    csv += 'Top Drivers by Revenue\n';
    csv += 'Driver,Revenue,Ride Count\n';
    data.topDriversByRevenue.forEach(driver => {
      csv += `${driver.driverName},$${driver.revenue.toFixed(2)},${driver.rideCount}\n`;
    });
    csv += '\n';

    // Revenue by day
    csv += 'Revenue by Day\n';
    csv += 'Date,Amount\n';
    data.revenueByDay.forEach(day => {
      csv += `${day.date},$${day.amount.toFixed(2)}\n`;
    });

    return csv;
  }

  /**
   * Export user report to CSV
   */
  private exportUserReportToCsv(data: UserActivityReportData): string {
    // Header
    let csv = 'Report Type,User Activity Statistics\n';
    csv += `Date Range,${data.dateRange.startDate.toISOString().split('T')[0]} to ${data.dateRange.endDate.toISOString().split('T')[0]}\n\n`;

    // New users
    csv += 'New Users\n';
    csv += 'Type,Count\n';
    csv += `Total,${data.newUsers.total}\n`;
    csv += `Riders,${data.newUsers.riders}\n`;
    csv += `Drivers,${data.newUsers.drivers}\n\n`;

    // Active users
    csv += 'Active Users\n';
    csv += 'Type,Count\n';
    csv += `Total,${data.activeUsers.total}\n`;
    csv += `Riders,${data.activeUsers.riders}\n`;
    csv += `Drivers,${data.activeUsers.drivers}\n\n`;

    // Top riders
    csv += 'Top Riders\n';
    csv += 'Rider,Ride Count,Total Spent\n';
    data.topRiders.forEach(rider => {
      csv += `${rider.riderName},${rider.rideCount},$${rider.totalSpent.toFixed(2)}\n`;
    });
    csv += '\n';

    // Users by day
    csv += 'User Activity by Day\n';
    csv += 'Date,New Users,Active Users\n';
    data.usersByDay.forEach(day => {
      csv += `${day.date},${day.newUsers},${day.activeUsers}\n`;
    });

    return csv;
  }
}
