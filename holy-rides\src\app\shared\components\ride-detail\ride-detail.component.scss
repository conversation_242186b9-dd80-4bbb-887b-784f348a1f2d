mat-card {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  margin-top: 180px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
}

.ride-details {
  margin: 20px 0;
}

.detail-row {
  display: flex;
  margin-bottom: 10px;
}

.label {
  font-weight: 500;
  width: 120px;
  color: #666;
}

.value {
  flex: 1;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9em;
  font-weight: 500;
}

.status-requested {
  background-color: #f0f0f0;
  color: #666;
}

.status-assigned {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-in-progress {
  background-color: #e8f5e9;
  color: #388e3c;
}

.status-completed {
  background-color: #e8f5e9;
  color: #388e3c;
}

.status-canceled {
  background-color: #ffebee;
  color: #d32f2f;
}

// Payment status styles
.payment-pending {
  background-color: #fff8e1;
  color: #f57c00;
}

.payment-completed {
  background-color: #e8f5e9;
  color: #388e3c;
}

.payment-paid {
  background-color: #e8f5e9;
  color: #388e3c;
}

.payment-failed {
  background-color: #ffebee;
  color: #d32f2f;
}

.payment-refunded {
  background-color: #e0f7fa;
  color: #0097a7;
}

.section-divider {
  margin: 20px 0;
}

.map-container {
  margin-top: 20px;
}

.rating-container {
  padding: 20px 0;
}

.already-rated-message {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.already-rated-message mat-icon {
  margin-right: 10px;
}

// Admin fare input styles
.admin-fare-input {
  margin-top: 20px;
  display: flex;
  align-items: start;
  justify-self: start;

}
.mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix {
  padding-top: var(8px);
  padding-bottom: var(8px);
}
.mat-mdc-form-field-infix {
  min-height: 40px !important;}
.admin-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.fare-input {
  width: 150px;  // Set a fixed width for the input
}

// Make sure the input is not too wide
mat-form-field {
  width: 150px;
}
