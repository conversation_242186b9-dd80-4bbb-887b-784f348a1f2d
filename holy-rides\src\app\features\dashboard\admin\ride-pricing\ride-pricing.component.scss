.pricing-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.form-card, .table-card {
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.full-width {
  width: 100%;
}

mat-form-field {
  flex: 1;
  min-width: 150px;
}

.active-toggle {
  margin: 16px 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.54);
}

.no-data {
  text-align: center;
  padding: 20px;
  color: rgba(0, 0, 0, 0.54);
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background-color: #e0e0e0;
  color: #757575;
}

.status-badge.active {
  background-color: #c8e6c9;
  color: #2e7d32;
}

.sample-calculation {
  margin: 20px 0;
  padding: 10px 0;
}

.sample-calculation h3 {
  margin: 10px 0;
  font-size: 16px;
  font-weight: 500;
}

.sample-calculation p {
  margin: 10px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  mat-form-field {
    width: 100%;
  }
}
