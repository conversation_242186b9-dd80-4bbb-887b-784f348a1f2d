import{d as t,f as o}from"./chunk-ODN5LVDJ.js";var c={};o(c,{Headers:()=>s,Request:()=>l,Response:()=>a,default:()=>f,fetch:()=>r});var n,e,r,f,s,l,a,d=t(()=>{n=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},e=n(),r=e.fetch,f=e.fetch.bind(e),s=e.Headers,l=e.Request,a=e.Response});export{r as a,f as b,s as c,l as d,a as e,c as f,d as g};
