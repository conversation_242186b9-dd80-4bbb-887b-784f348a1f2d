import{B as Z,C as ht,Cd as N,Dd as L,Ea as Ft,Ga as Bt,Ha as ut,Ma as I,Na as E,Oa as Vt,Pc as K,Qc as z,Ra as F,Rc as P,Ta as pt,U as ct,V as Mt,Vc as Nt,Wa as _t,Xa as D,Ya as k,Yc as gt,Z as m,_ as x,_a as $,_c as mt,a as S,aa as dt,c as Et,da as a,f as _,fb as At,fc as O,hc as Tt,hd as Lt,ia as G,id as jt,k as Dt,ka as ft,kc as It,mc as w,od as B,pa as U,pd as q,qd as V,ra as T,rc as zt,rd as vt,ud as f,va as C,wa as y,z as Pt,za as R}from"./chunk-ST4QC4E3.js";import{a as X,b as xt}from"./chunk-ODN5LVDJ.js";var j=class{_attachedHost;attach(e){return this._attachedHost=e,e.attach(this)}detach(){let e=this._attachedHost;e!=null&&(this._attachedHost=null,e.detach())}get isAttached(){return this._attachedHost!=null}setAttachedHost(e){this._attachedHost=e}},yt=class extends j{component;viewContainerRef;injector;componentFactoryResolver;projectableNodes;constructor(e,t,i,s,n){super(),this.component=e,this.viewContainerRef=t,this.injector=i,this.projectableNodes=n}},A=class extends j{templateRef;viewContainerRef;context;injector;constructor(e,t,i,s){super(),this.templateRef=e,this.viewContainerRef=t,this.context=i,this.injector=s}get origin(){return this.templateRef.elementRef}attach(e,t=this.context){return this.context=t,super.attach(e)}detach(){return this.context=void 0,super.detach()}},wt=class extends j{element;constructor(e){super(),this.element=e instanceof R?e.nativeElement:e}},Q=class{_attachedPortal;_disposeFn;_isDisposed=!1;hasAttached(){return!!this._attachedPortal}attach(e){if(e instanceof yt)return this._attachedPortal=e,this.attachComponentPortal(e);if(e instanceof A)return this._attachedPortal=e,this.attachTemplatePortal(e);if(this.attachDomPortal&&e instanceof wt)return this._attachedPortal=e,this.attachDomPortal(e)}attachDomPortal=null;detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(e){this._disposeFn=e}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}};var J=class extends Q{outletElement;_appRef;_defaultInjector;_document;constructor(e,t,i,s,n){super(),this.outletElement=e,this._appRef=i,this._defaultInjector=s,this._document=n}attachComponentPortal(e){let t;if(e.viewContainerRef){let i=e.injector||e.viewContainerRef.injector,s=i.get(pt,null,{optional:!0})||void 0;t=e.viewContainerRef.createComponent(e.component,{index:e.viewContainerRef.length,injector:i,ngModuleRef:s,projectableNodes:e.projectableNodes||void 0}),this.setDisposeFn(()=>t.destroy())}else{let i=this._appRef,s=e.injector||this._defaultInjector||T.NULL,n=s.get(G,i.injector);t=It(e.component,{elementInjector:s,environmentInjector:n,projectableNodes:e.projectableNodes||void 0}),i.attachView(t.hostView),this.setDisposeFn(()=>{i.viewCount>0&&i.detachView(t.hostView),t.destroy()})}return this.outletElement.appendChild(this._getComponentRootNode(t)),this._attachedPortal=e,t}attachTemplatePortal(e){let t=e.viewContainerRef,i=t.createEmbeddedView(e.templateRef,e.context,{injector:e.injector});return i.rootNodes.forEach(s=>this.outletElement.appendChild(s)),i.detectChanges(),this.setDisposeFn(()=>{let s=t.indexOf(i);s!==-1&&t.remove(s)}),this._attachedPortal=e,i}attachDomPortal=e=>{let t=e.element;t.parentNode;let i=this._document.createComment("dom-portal");t.parentNode.insertBefore(i,t),this.outletElement.appendChild(t),this._attachedPortal=e,super.setDisposeFn(()=>{i.parentNode&&i.parentNode.replaceChild(t,i)})};dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(e){return e.hostView.rootNodes[0]}};var Ce=(()=>{class o extends A{constructor(){let t=a(I),i=a(F);super(t,i)}static \u0275fac=function(i){return new(i||o)};static \u0275dir=k({type:o,selectors:[["","cdkPortal",""]],exportAs:["cdkPortal"],features:[$]})}return o})();var Se=(()=>{class o extends Q{_moduleRef=a(pt,{optional:!0});_document=a(w);_viewContainerRef=a(F);_isInitialized=!1;_attachedRef;constructor(){super()}get portal(){return this._attachedPortal}set portal(t){this.hasAttached()&&!t&&!this._isInitialized||(this.hasAttached()&&super.detach(),t&&super.attach(t),this._attachedPortal=t||null)}attached=new C;get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(t){t.setAttachedHost(this);let i=t.viewContainerRef!=null?t.viewContainerRef:this._viewContainerRef,s=i.createComponent(t.component,{index:i.length,injector:t.injector||i.injector,projectableNodes:t.projectableNodes||void 0,ngModuleRef:this._moduleRef||void 0});return i!==this._viewContainerRef&&this._getRootNode().appendChild(s.hostView.rootNodes[0]),super.setDisposeFn(()=>s.destroy()),this._attachedPortal=t,this._attachedRef=s,this.attached.emit(s),s}attachTemplatePortal(t){t.setAttachedHost(this);let i=this._viewContainerRef.createEmbeddedView(t.templateRef,t.context,{injector:t.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=t,this._attachedRef=i,this.attached.emit(i),i}attachDomPortal=t=>{let i=t.element;i.parentNode;let s=this._document.createComment("dom-portal");t.setAttachedHost(this),i.parentNode.insertBefore(s,i),this._getRootNode().appendChild(i),this._attachedPortal=t,super.setDisposeFn(()=>{s.parentNode&&s.parentNode.replaceChild(i,s)})};_getRootNode(){let t=this._viewContainerRef.element.nativeElement;return t.nodeType===t.ELEMENT_NODE?t:t.parentNode}static \u0275fac=function(i){return new(i||o)};static \u0275dir=k({type:o,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[0,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],features:[$]})}return o})();var Ht=(()=>{class o{static \u0275fac=function(i){return new(i||o)};static \u0275mod=D({type:o});static \u0275inj=x({})}return o})();var re=20,tt=(()=>{class o{_ngZone=a(y);_platform=a(P);_renderer=a(E).createRenderer(null,null);_cleanupGlobalListener;constructor(){}_scrolled=new _;_scrolledCount=0;scrollContainers=new Map;register(t){this.scrollContainers.has(t)||this.scrollContainers.set(t,t.elementScrolled().subscribe(()=>this._scrolled.next(t)))}deregister(t){let i=this.scrollContainers.get(t);i&&(i.unsubscribe(),this.scrollContainers.delete(t))}scrolled(t=re){return this._platform.isBrowser?new Et(i=>{this._cleanupGlobalListener||(this._cleanupGlobalListener=this._ngZone.runOutsideAngular(()=>this._renderer.listen("document","scroll",()=>this._scrolled.next())));let s=t>0?this._scrolled.pipe(ht(t)).subscribe(i):this._scrolled.subscribe(i);return this._scrolledCount++,()=>{s.unsubscribe(),this._scrolledCount--,this._scrolledCount||(this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0)}}):Dt()}ngOnDestroy(){this._cleanupGlobalListener?.(),this._cleanupGlobalListener=void 0,this.scrollContainers.forEach((t,i)=>this.deregister(i)),this._scrolled.complete()}ancestorScrolled(t,i){let s=this.getAncestorScrollContainers(t);return this.scrolled(i).pipe(Z(n=>!n||s.indexOf(n)>-1))}getAncestorScrollContainers(t){let i=[];return this.scrollContainers.forEach((s,n)=>{this._scrollableContainsElement(n,t)&&i.push(n)}),i}_scrollableContainsElement(t,i){let s=Nt(i),n=t.getElementRef().nativeElement;do if(s==n)return!0;while(s=s.parentElement);return!1}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),ae=(()=>{class o{elementRef=a(R);scrollDispatcher=a(tt);ngZone=a(y);dir=a(N,{optional:!0});_scrollElement=this.elementRef.nativeElement;_destroyed=new _;_renderer=a(Vt);_cleanupScroll;_elementScrolled=new _;constructor(){}ngOnInit(){this._cleanupScroll=this.ngZone.runOutsideAngular(()=>this._renderer.listen(this._scrollElement,"scroll",t=>this._elementScrolled.next(t))),this.scrollDispatcher.register(this)}ngOnDestroy(){this._cleanupScroll?.(),this._elementScrolled.complete(),this.scrollDispatcher.deregister(this),this._destroyed.next(),this._destroyed.complete()}elementScrolled(){return this._elementScrolled}getElementRef(){return this.elementRef}scrollTo(t){let i=this.elementRef.nativeElement,s=this.dir&&this.dir.value=="rtl";t.left==null&&(t.left=s?t.end:t.start),t.right==null&&(t.right=s?t.start:t.end),t.bottom!=null&&(t.top=i.scrollHeight-i.clientHeight-t.bottom),s&&V()!=B.NORMAL?(t.left!=null&&(t.right=i.scrollWidth-i.clientWidth-t.left),V()==B.INVERTED?t.left=t.right:V()==B.NEGATED&&(t.left=t.right?-t.right:t.right)):t.right!=null&&(t.left=i.scrollWidth-i.clientWidth-t.right),this._applyScrollToOptions(t)}_applyScrollToOptions(t){let i=this.elementRef.nativeElement;q()?i.scrollTo(t):(t.top!=null&&(i.scrollTop=t.top),t.left!=null&&(i.scrollLeft=t.left))}measureScrollOffset(t){let i="left",s="right",n=this.elementRef.nativeElement;if(t=="top")return n.scrollTop;if(t=="bottom")return n.scrollHeight-n.clientHeight-n.scrollTop;let r=this.dir&&this.dir.value=="rtl";return t=="start"?t=r?s:i:t=="end"&&(t=r?i:s),r&&V()==B.INVERTED?t==i?n.scrollWidth-n.clientWidth-n.scrollLeft:n.scrollLeft:r&&V()==B.NEGATED?t==i?n.scrollLeft+n.scrollWidth-n.clientWidth:-n.scrollLeft:t==i?n.scrollLeft:n.scrollWidth-n.clientWidth-n.scrollLeft}static \u0275fac=function(i){return new(i||o)};static \u0275dir=k({type:o,selectors:[["","cdk-scrollable",""],["","cdkScrollable",""]]})}return o})(),le=20,et=(()=>{class o{_platform=a(P);_listeners;_viewportSize;_change=new _;_document=a(w,{optional:!0});constructor(){let t=a(y),i=a(E).createRenderer(null,null);t.runOutsideAngular(()=>{if(this._platform.isBrowser){let s=n=>this._change.next(n);this._listeners=[i.listen("window","resize",s),i.listen("window","orientationchange",s)]}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){this._listeners?.forEach(t=>t()),this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();let t={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),t}getViewportRect(){let t=this.getViewportScrollPosition(),{width:i,height:s}=this.getViewportSize();return{top:t.top,left:t.left,bottom:t.top+s,right:t.left+i,height:s,width:i}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};let t=this._document,i=this._getWindow(),s=t.documentElement,n=s.getBoundingClientRect(),r=-n.top||t.body.scrollTop||i.scrollY||s.scrollTop||0,l=-n.left||t.body.scrollLeft||i.scrollX||s.scrollLeft||0;return{top:r,left:l}}change(t=le){return t>0?this._change.pipe(ht(t)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){let t=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:t.innerWidth,height:t.innerHeight}:{width:0,height:0}}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();var bt=(()=>{class o{static \u0275fac=function(i){return new(i||o)};static \u0275mod=D({type:o});static \u0275inj=x({})}return o})(),Ct=(()=>{class o{static \u0275fac=function(i){return new(i||o)};static \u0275mod=D({type:o});static \u0275inj=x({imports:[L,bt,L,bt]})}return o})();var Wt=q(),it=class{_viewportRuler;_previousHTMLStyles={top:"",left:""};_previousScrollPosition;_isEnabled=!1;_document;constructor(e,t){this._viewportRuler=e,this._document=t}attach(){}enable(){if(this._canBeEnabled()){let e=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=e.style.left||"",this._previousHTMLStyles.top=e.style.top||"",e.style.left=f(-this._previousScrollPosition.left),e.style.top=f(-this._previousScrollPosition.top),e.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let e=this._document.documentElement,t=this._document.body,i=e.style,s=t.style,n=i.scrollBehavior||"",r=s.scrollBehavior||"";this._isEnabled=!1,i.left=this._previousHTMLStyles.left,i.top=this._previousHTMLStyles.top,e.classList.remove("cdk-global-scrollblock"),Wt&&(i.scrollBehavior=s.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),Wt&&(i.scrollBehavior=n,s.scrollBehavior=r)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let t=this._document.documentElement,i=this._viewportRuler.getViewportSize();return t.scrollHeight>i.height||t.scrollWidth>i.width}};var st=class{_scrollDispatcher;_ngZone;_viewportRuler;_config;_scrollSubscription=null;_overlayRef;_initialScrollPosition;constructor(e,t,i,s){this._scrollDispatcher=e,this._ngZone=t,this._viewportRuler=i,this._config=s}attach(e){this._overlayRef,this._overlayRef=e}enable(){if(this._scrollSubscription)return;let e=this._scrollDispatcher.scrolled(0).pipe(Z(t=>!t||!this._overlayRef.overlayElement.contains(t.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=e.subscribe(()=>{let t=this._viewportRuler.getViewportScrollPosition().top;Math.abs(t-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=e.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}_detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}},Y=class{enable(){}disable(){}attach(){}};function St(o,e){return e.some(t=>{let i=o.bottom<t.top,s=o.top>t.bottom,n=o.right<t.left,r=o.left>t.right;return i||s||n||r})}function Xt(o,e){return e.some(t=>{let i=o.top<t.top,s=o.bottom>t.bottom,n=o.left<t.left,r=o.right>t.right;return i||s||n||r})}var ot=class{_scrollDispatcher;_viewportRuler;_ngZone;_config;_scrollSubscription=null;_overlayRef;constructor(e,t,i,s){this._scrollDispatcher=e,this._viewportRuler=t,this._ngZone=i,this._config=s}attach(e){this._overlayRef,this._overlayRef=e}enable(){if(!this._scrollSubscription){let e=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(e).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let t=this._overlayRef.overlayElement.getBoundingClientRect(),{width:i,height:s}=this._viewportRuler.getViewportSize();St(t,[{width:i,height:s,bottom:s,right:i,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},qt=(()=>{class o{_scrollDispatcher=a(tt);_viewportRuler=a(et);_ngZone=a(y);_document=a(w);constructor(){}noop=()=>new Y;close=t=>new st(this._scrollDispatcher,this._ngZone,this._viewportRuler,t);block=()=>new it(this._viewportRuler,this._document);reposition=t=>new ot(this._scrollDispatcher,this._viewportRuler,this._ngZone,t);static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),H=class{positionStrategy;scrollStrategy=new Y;panelClass="";hasBackdrop=!1;backdropClass="cdk-overlay-dark-backdrop";width;height;minWidth;minHeight;maxWidth;maxHeight;direction;disposeOnNavigation=!1;constructor(e){if(e){let t=Object.keys(e);for(let i of t)e[i]!==void 0&&(this[i]=e[i])}}};var nt=class{connectionPair;scrollableViewProperties;constructor(e,t){this.connectionPair=e,this.scrollableViewProperties=t}};var Qt=(()=>{class o{_attachedOverlays=[];_document=a(w);_isAttached;constructor(){}ngOnDestroy(){this.detach()}add(t){this.remove(t),this._attachedOverlays.push(t)}remove(t){let i=this._attachedOverlays.indexOf(t);i>-1&&this._attachedOverlays.splice(i,1),this._attachedOverlays.length===0&&this.detach()}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Jt=(()=>{class o extends Qt{_ngZone=a(y);_renderer=a(E).createRenderer(null,null);_cleanupKeydown;add(t){super.add(t),this._isAttached||(this._ngZone.runOutsideAngular(()=>{this._cleanupKeydown=this._renderer.listen("body","keydown",this._keydownListener)}),this._isAttached=!0)}detach(){this._isAttached&&(this._cleanupKeydown?.(),this._isAttached=!1)}_keydownListener=t=>{let i=this._attachedOverlays;for(let s=i.length-1;s>-1;s--)if(i[s]._keydownEvents.observers.length>0){this._ngZone.run(()=>i[s]._keydownEvents.next(t));break}};static \u0275fac=(()=>{let t;return function(s){return(t||(t=U(o)))(s||o)}})();static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),te=(()=>{class o extends Qt{_platform=a(P);_ngZone=a(y);_renderer=a(E).createRenderer(null,null);_cursorOriginalValue;_cursorStyleIsSet=!1;_pointerDownEventTarget;_cleanups;add(t){if(super.add(t),!this._isAttached){let i=this._document.body,s={capture:!0};this._cleanups=this._ngZone.runOutsideAngular(()=>[z(this._renderer,i,"pointerdown",this._pointerDownListener,s),z(this._renderer,i,"click",this._clickListener,s),z(this._renderer,i,"auxclick",this._clickListener,s),z(this._renderer,i,"contextmenu",this._clickListener,s)]),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=i.style.cursor,i.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){this._isAttached&&(this._cleanups?.forEach(t=>t()),this._cleanups=void 0,this._platform.IOS&&this._cursorStyleIsSet&&(this._document.body.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1)}_pointerDownListener=t=>{this._pointerDownEventTarget=K(t)};_clickListener=t=>{let i=K(t),s=t.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:i;this._pointerDownEventTarget=null;let n=this._attachedOverlays.slice();for(let r=n.length-1;r>-1;r--){let l=n[r];if(l._outsidePointerEvents.observers.length<1||!l.hasAttached())continue;if(Zt(l.overlayElement,i)||Zt(l.overlayElement,s))break;let c=l._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>c.next(t)):c.next(t)}};static \u0275fac=(()=>{let t;return function(s){return(t||(t=U(o)))(s||o)}})();static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();function Zt(o,e){let t=typeof ShadowRoot<"u"&&ShadowRoot,i=e;for(;i;){if(i===o)return!0;i=t&&i instanceof ShadowRoot?i.host:i.parentNode}return!1}var ee=(()=>{class o{static \u0275fac=function(i){return new(i||o)};static \u0275cmp=_t({type:o,selectors:[["ng-component"]],hostAttrs:["cdk-overlay-style-loader",""],decls:0,vars:0,template:function(i,s){},styles:[`.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}
`],encapsulation:2,changeDetection:0})}return o})(),ie=(()=>{class o{_platform=a(P);_containerElement;_document=a(w);_styleLoader=a(gt);constructor(){}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._loadStyles(),this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let t="cdk-overlay-container";if(this._platform.isBrowser||vt()){let s=this._document.querySelectorAll(`.${t}[platform="server"], .${t}[platform="test"]`);for(let n=0;n<s.length;n++)s[n].remove()}let i=this._document.createElement("div");i.classList.add(t),vt()?i.setAttribute("platform","test"):this._platform.isBrowser||i.setAttribute("platform","server"),this._document.body.appendChild(i),this._containerElement=i}_loadStyles(){this._styleLoader.load(ee)}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),Rt=class{_renderer;_ngZone;element;_cleanupClick;_cleanupTransitionEnd;_fallbackTimeout;constructor(e,t,i,s){this._renderer=t,this._ngZone=i,this.element=e.createElement("div"),this.element.classList.add("cdk-overlay-backdrop"),this._cleanupClick=t.listen(this.element,"click",s)}detach(){this._ngZone.runOutsideAngular(()=>{let e=this.element;clearTimeout(this._fallbackTimeout),this._cleanupTransitionEnd?.(),this._cleanupTransitionEnd=this._renderer.listen(e,"transitionend",this.dispose),this._fallbackTimeout=setTimeout(this.dispose,500),e.style.pointerEvents="none",e.classList.remove("cdk-overlay-backdrop-showing")})}dispose=()=>{clearTimeout(this._fallbackTimeout),this._cleanupClick?.(),this._cleanupTransitionEnd?.(),this._cleanupClick=this._cleanupTransitionEnd=this._fallbackTimeout=void 0,this.element.remove()}},rt=class{_portalOutlet;_host;_pane;_config;_ngZone;_keyboardDispatcher;_document;_location;_outsideClickDispatcher;_animationsDisabled;_injector;_renderer;_backdropClick=new _;_attachments=new _;_detachments=new _;_positionStrategy;_scrollStrategy;_locationChanges=S.EMPTY;_backdropRef=null;_previousHostParent;_keydownEvents=new _;_outsidePointerEvents=new _;_renders=new _;_afterRenderRef;_afterNextRenderRef;constructor(e,t,i,s,n,r,l,c,d,h=!1,u,b){this._portalOutlet=e,this._host=t,this._pane=i,this._config=s,this._ngZone=n,this._keyboardDispatcher=r,this._document=l,this._location=c,this._outsideClickDispatcher=d,this._animationsDisabled=h,this._injector=u,this._renderer=b,s.scrollStrategy&&(this._scrollStrategy=s.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=s.positionStrategy,this._afterRenderRef=Tt(()=>Bt(()=>{this._renders.next()},{injector:this._injector}))}get overlayElement(){return this._pane}get backdropElement(){return this._backdropRef?.element||null}get hostElement(){return this._host}attach(e){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let t=this._portalOutlet.attach(e);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=ut(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof t?.onDestroy=="function"&&t.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),t}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let e=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),e}dispose(){let e=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._backdropRef?.dispose(),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=this._backdropRef=null,e&&this._detachments.next(),this._detachments.complete(),this._afterRenderRef.destroy(),this._renders.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(e){e!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=e,this.hasAttached()&&(e.attach(this),this.updatePosition()))}updateSize(e){this._config=X(X({},this._config),e),this._updateElementSize()}setDirection(e){this._config=xt(X({},this._config),{direction:e}),this._updateElementDirection()}addPanelClass(e){this._pane&&this._toggleClasses(this._pane,e,!0)}removePanelClass(e){this._pane&&this._toggleClasses(this._pane,e,!1)}getDirection(){let e=this._config.direction;return e?typeof e=="string"?e:e.value:"ltr"}updateScrollStrategy(e){e!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=e,this.hasAttached()&&(e.attach(this),e.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let e=this._pane.style;e.width=f(this._config.width),e.height=f(this._config.height),e.minWidth=f(this._config.minWidth),e.minHeight=f(this._config.minHeight),e.maxWidth=f(this._config.maxWidth),e.maxHeight=f(this._config.maxHeight)}_togglePointerEvents(e){this._pane.style.pointerEvents=e?"":"none"}_attachBackdrop(){let e="cdk-overlay-backdrop-showing";this._backdropRef?.dispose(),this._backdropRef=new Rt(this._document,this._renderer,this._ngZone,t=>{this._backdropClick.next(t)}),this._animationsDisabled&&this._backdropRef.element.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropRef.element,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropRef.element,this._host),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._backdropRef?.element.classList.add(e))}):this._backdropRef.element.classList.add(e)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){this._animationsDisabled?(this._backdropRef?.dispose(),this._backdropRef=null):this._backdropRef?.detach()}_toggleClasses(e,t,i){let s=mt(t||[]).filter(n=>!!n);s.length&&(i?e.classList.add(...s):e.classList.remove(...s))}_detachContentWhenEmpty(){this._ngZone.runOutsideAngular(()=>{let e=this._renders.pipe(ct(Pt(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),e.unsubscribe())})})}_disposeScrollStrategy(){let e=this._scrollStrategy;e?.disable(),e?.detach?.()}},Gt="cdk-overlay-connected-position-bounding-box",he=/([A-Za-z%]+)$/,at=class{_viewportRuler;_document;_platform;_overlayContainer;_overlayRef;_isInitialRender;_lastBoundingBoxSize={width:0,height:0};_isPushed=!1;_canPush=!0;_growAfterOpen=!1;_hasFlexibleDimensions=!0;_positionLocked=!1;_originRect;_overlayRect;_viewportRect;_containerRect;_viewportMargin=0;_scrollables=[];_preferredPositions=[];_origin;_pane;_isDisposed;_boundingBox;_lastPosition;_lastScrollVisibility;_positionChanges=new _;_resizeSubscription=S.EMPTY;_offsetX=0;_offsetY=0;_transformOriginSelector;_appliedPanelClasses=[];_previousPushAmount;positionChanges=this._positionChanges;get positions(){return this._preferredPositions}constructor(e,t,i,s,n){this._viewportRuler=t,this._document=i,this._platform=s,this._overlayContainer=n,this.setOrigin(e)}attach(e){this._overlayRef&&this._overlayRef,this._validatePositions(),e.hostElement.classList.add(Gt),this._overlayRef=e,this._boundingBox=e.hostElement,this._pane=e.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let e=this._originRect,t=this._overlayRect,i=this._viewportRect,s=this._containerRect,n=[],r;for(let l of this._preferredPositions){let c=this._getOriginPoint(e,s,l),d=this._getOverlayPoint(c,t,l),h=this._getOverlayFit(d,t,i,l);if(h.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(l,c);return}if(this._canFitWithFlexibleDimensions(h,d,i)){n.push({position:l,origin:c,overlayRect:t,boundingBoxRect:this._calculateBoundingBoxRect(c,l)});continue}(!r||r.overlayFit.visibleArea<h.visibleArea)&&(r={overlayFit:h,overlayPoint:d,originPoint:c,position:l,overlayRect:t})}if(n.length){let l=null,c=-1;for(let d of n){let h=d.boundingBoxRect.width*d.boundingBoxRect.height*(d.position.weight||1);h>c&&(c=h,l=d)}this._isPushed=!1,this._applyPosition(l.position,l.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(r.position,r.originPoint);return}this._applyPosition(r.position,r.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&M(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(Gt),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let e=this._lastPosition;if(e){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let t=this._getOriginPoint(this._originRect,this._containerRect,e);this._applyPosition(e,t)}else this.apply()}withScrollableContainers(e){return this._scrollables=e,this}withPositions(e){return this._preferredPositions=e,e.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(e){return this._viewportMargin=e,this}withFlexibleDimensions(e=!0){return this._hasFlexibleDimensions=e,this}withGrowAfterOpen(e=!0){return this._growAfterOpen=e,this}withPush(e=!0){return this._canPush=e,this}withLockedPosition(e=!0){return this._positionLocked=e,this}setOrigin(e){return this._origin=e,this}withDefaultOffsetX(e){return this._offsetX=e,this}withDefaultOffsetY(e){return this._offsetY=e,this}withTransformOriginOn(e){return this._transformOriginSelector=e,this}_getOriginPoint(e,t,i){let s;if(i.originX=="center")s=e.left+e.width/2;else{let r=this._isRtl()?e.right:e.left,l=this._isRtl()?e.left:e.right;s=i.originX=="start"?r:l}t.left<0&&(s-=t.left);let n;return i.originY=="center"?n=e.top+e.height/2:n=i.originY=="top"?e.top:e.bottom,t.top<0&&(n-=t.top),{x:s,y:n}}_getOverlayPoint(e,t,i){let s;i.overlayX=="center"?s=-t.width/2:i.overlayX==="start"?s=this._isRtl()?-t.width:0:s=this._isRtl()?0:-t.width;let n;return i.overlayY=="center"?n=-t.height/2:n=i.overlayY=="top"?0:-t.height,{x:e.x+s,y:e.y+n}}_getOverlayFit(e,t,i,s){let n=$t(t),{x:r,y:l}=e,c=this._getOffset(s,"x"),d=this._getOffset(s,"y");c&&(r+=c),d&&(l+=d);let h=0-r,u=r+n.width-i.width,b=0-l,g=l+n.height-i.height,p=this._subtractOverflows(n.width,h,u),v=this._subtractOverflows(n.height,b,g),Ot=p*v;return{visibleArea:Ot,isCompletelyWithinViewport:n.width*n.height===Ot,fitsInViewportVertically:v===n.height,fitsInViewportHorizontally:p==n.width}}_canFitWithFlexibleDimensions(e,t,i){if(this._hasFlexibleDimensions){let s=i.bottom-t.y,n=i.right-t.x,r=Ut(this._overlayRef.getConfig().minHeight),l=Ut(this._overlayRef.getConfig().minWidth),c=e.fitsInViewportVertically||r!=null&&r<=s,d=e.fitsInViewportHorizontally||l!=null&&l<=n;return c&&d}return!1}_pushOverlayOnScreen(e,t,i){if(this._previousPushAmount&&this._positionLocked)return{x:e.x+this._previousPushAmount.x,y:e.y+this._previousPushAmount.y};let s=$t(t),n=this._viewportRect,r=Math.max(e.x+s.width-n.width,0),l=Math.max(e.y+s.height-n.height,0),c=Math.max(n.top-i.top-e.y,0),d=Math.max(n.left-i.left-e.x,0),h=0,u=0;return s.width<=n.width?h=d||-r:h=e.x<this._viewportMargin?n.left-i.left-e.x:0,s.height<=n.height?u=c||-l:u=e.y<this._viewportMargin?n.top-i.top-e.y:0,this._previousPushAmount={x:h,y:u},{x:e.x+h,y:e.y+u}}_applyPosition(e,t){if(this._setTransformOrigin(e),this._setOverlayElementStyles(t,e),this._setBoundingBoxStyles(t,e),e.panelClass&&this._addPanelClasses(e.panelClass),this._positionChanges.observers.length){let i=this._getScrollVisibility();if(e!==this._lastPosition||!this._lastScrollVisibility||!ce(this._lastScrollVisibility,i)){let s=new nt(e,i);this._positionChanges.next(s)}this._lastScrollVisibility=i}this._lastPosition=e,this._isInitialRender=!1}_setTransformOrigin(e){if(!this._transformOriginSelector)return;let t=this._boundingBox.querySelectorAll(this._transformOriginSelector),i,s=e.overlayY;e.overlayX==="center"?i="center":this._isRtl()?i=e.overlayX==="start"?"right":"left":i=e.overlayX==="start"?"left":"right";for(let n=0;n<t.length;n++)t[n].style.transformOrigin=`${i} ${s}`}_calculateBoundingBoxRect(e,t){let i=this._viewportRect,s=this._isRtl(),n,r,l;if(t.overlayY==="top")r=e.y,n=i.height-r+this._viewportMargin;else if(t.overlayY==="bottom")l=i.height-e.y+this._viewportMargin*2,n=i.height-l+this._viewportMargin;else{let g=Math.min(i.bottom-e.y+i.top,e.y),p=this._lastBoundingBoxSize.height;n=g*2,r=e.y-g,n>p&&!this._isInitialRender&&!this._growAfterOpen&&(r=e.y-p/2)}let c=t.overlayX==="start"&&!s||t.overlayX==="end"&&s,d=t.overlayX==="end"&&!s||t.overlayX==="start"&&s,h,u,b;if(d)b=i.width-e.x+this._viewportMargin*2,h=e.x-this._viewportMargin;else if(c)u=e.x,h=i.right-e.x;else{let g=Math.min(i.right-e.x+i.left,e.x),p=this._lastBoundingBoxSize.width;h=g*2,u=e.x-g,h>p&&!this._isInitialRender&&!this._growAfterOpen&&(u=e.x-p/2)}return{top:r,left:u,bottom:l,right:b,width:h,height:n}}_setBoundingBoxStyles(e,t){let i=this._calculateBoundingBoxRect(e,t);!this._isInitialRender&&!this._growAfterOpen&&(i.height=Math.min(i.height,this._lastBoundingBoxSize.height),i.width=Math.min(i.width,this._lastBoundingBoxSize.width));let s={};if(this._hasExactPosition())s.top=s.left="0",s.bottom=s.right=s.maxHeight=s.maxWidth="",s.width=s.height="100%";else{let n=this._overlayRef.getConfig().maxHeight,r=this._overlayRef.getConfig().maxWidth;s.height=f(i.height),s.top=f(i.top),s.bottom=f(i.bottom),s.width=f(i.width),s.left=f(i.left),s.right=f(i.right),t.overlayX==="center"?s.alignItems="center":s.alignItems=t.overlayX==="end"?"flex-end":"flex-start",t.overlayY==="center"?s.justifyContent="center":s.justifyContent=t.overlayY==="bottom"?"flex-end":"flex-start",n&&(s.maxHeight=f(n)),r&&(s.maxWidth=f(r))}this._lastBoundingBoxSize=i,M(this._boundingBox.style,s)}_resetBoundingBoxStyles(){M(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){M(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(e,t){let i={},s=this._hasExactPosition(),n=this._hasFlexibleDimensions,r=this._overlayRef.getConfig();if(s){let h=this._viewportRuler.getViewportScrollPosition();M(i,this._getExactOverlayY(t,e,h)),M(i,this._getExactOverlayX(t,e,h))}else i.position="static";let l="",c=this._getOffset(t,"x"),d=this._getOffset(t,"y");c&&(l+=`translateX(${c}px) `),d&&(l+=`translateY(${d}px)`),i.transform=l.trim(),r.maxHeight&&(s?i.maxHeight=f(r.maxHeight):n&&(i.maxHeight="")),r.maxWidth&&(s?i.maxWidth=f(r.maxWidth):n&&(i.maxWidth="")),M(this._pane.style,i)}_getExactOverlayY(e,t,i){let s={top:"",bottom:""},n=this._getOverlayPoint(t,this._overlayRect,e);if(this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i)),e.overlayY==="bottom"){let r=this._document.documentElement.clientHeight;s.bottom=`${r-(n.y+this._overlayRect.height)}px`}else s.top=f(n.y);return s}_getExactOverlayX(e,t,i){let s={left:"",right:""},n=this._getOverlayPoint(t,this._overlayRect,e);this._isPushed&&(n=this._pushOverlayOnScreen(n,this._overlayRect,i));let r;if(this._isRtl()?r=e.overlayX==="end"?"left":"right":r=e.overlayX==="end"?"right":"left",r==="right"){let l=this._document.documentElement.clientWidth;s.right=`${l-(n.x+this._overlayRect.width)}px`}else s.left=f(n.x);return s}_getScrollVisibility(){let e=this._getOriginRect(),t=this._pane.getBoundingClientRect(),i=this._scrollables.map(s=>s.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:Xt(e,i),isOriginOutsideView:St(e,i),isOverlayClipped:Xt(t,i),isOverlayOutsideView:St(t,i)}}_subtractOverflows(e,...t){return t.reduce((i,s)=>i-Math.max(s,0),e)}_getNarrowedViewportRect(){let e=this._document.documentElement.clientWidth,t=this._document.documentElement.clientHeight,i=this._viewportRuler.getViewportScrollPosition();return{top:i.top+this._viewportMargin,left:i.left+this._viewportMargin,right:i.left+e-this._viewportMargin,bottom:i.top+t-this._viewportMargin,width:e-2*this._viewportMargin,height:t-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(e,t){return t==="x"?e.offsetX==null?this._offsetX:e.offsetX:e.offsetY==null?this._offsetY:e.offsetY}_validatePositions(){}_addPanelClasses(e){this._pane&&mt(e).forEach(t=>{t!==""&&this._appliedPanelClasses.indexOf(t)===-1&&(this._appliedPanelClasses.push(t),this._pane.classList.add(t))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(e=>{this._pane.classList.remove(e)}),this._appliedPanelClasses=[])}_getOriginRect(){let e=this._origin;if(e instanceof R)return e.nativeElement.getBoundingClientRect();if(e instanceof Element)return e.getBoundingClientRect();let t=e.width||0,i=e.height||0;return{top:e.y,bottom:e.y+i,left:e.x,right:e.x+t,height:i,width:t}}};function M(o,e){for(let t in e)e.hasOwnProperty(t)&&(o[t]=e[t]);return o}function Ut(o){if(typeof o!="number"&&o!=null){let[e,t]=o.split(he);return!t||t==="px"?parseFloat(e):null}return o||null}function $t(o){return{top:Math.floor(o.top),right:Math.floor(o.right),bottom:Math.floor(o.bottom),left:Math.floor(o.left),width:Math.floor(o.width),height:Math.floor(o.height)}}function ce(o,e){return o===e?!0:o.isOriginClipped===e.isOriginClipped&&o.isOriginOutsideView===e.isOriginOutsideView&&o.isOverlayClipped===e.isOverlayClipped&&o.isOverlayOutsideView===e.isOverlayOutsideView}var Kt="cdk-global-overlay-wrapper",lt=class{_overlayRef;_cssPosition="static";_topOffset="";_bottomOffset="";_alignItems="";_xPosition="";_xOffset="";_width="";_height="";_isDisposed=!1;attach(e){let t=e.getConfig();this._overlayRef=e,this._width&&!t.width&&e.updateSize({width:this._width}),this._height&&!t.height&&e.updateSize({height:this._height}),e.hostElement.classList.add(Kt),this._isDisposed=!1}top(e=""){return this._bottomOffset="",this._topOffset=e,this._alignItems="flex-start",this}left(e=""){return this._xOffset=e,this._xPosition="left",this}bottom(e=""){return this._topOffset="",this._bottomOffset=e,this._alignItems="flex-end",this}right(e=""){return this._xOffset=e,this._xPosition="right",this}start(e=""){return this._xOffset=e,this._xPosition="start",this}end(e=""){return this._xOffset=e,this._xPosition="end",this}width(e=""){return this._overlayRef?this._overlayRef.updateSize({width:e}):this._width=e,this}height(e=""){return this._overlayRef?this._overlayRef.updateSize({height:e}):this._height=e,this}centerHorizontally(e=""){return this.left(e),this._xPosition="center",this}centerVertically(e=""){return this.top(e),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let e=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement.style,i=this._overlayRef.getConfig(),{width:s,height:n,maxWidth:r,maxHeight:l}=i,c=(s==="100%"||s==="100vw")&&(!r||r==="100%"||r==="100vw"),d=(n==="100%"||n==="100vh")&&(!l||l==="100%"||l==="100vh"),h=this._xPosition,u=this._xOffset,b=this._overlayRef.getConfig().direction==="rtl",g="",p="",v="";c?v="flex-start":h==="center"?(v="center",b?p=u:g=u):b?h==="left"||h==="end"?(v="flex-end",g=u):(h==="right"||h==="start")&&(v="flex-start",p=u):h==="left"||h==="start"?(v="flex-start",g=u):(h==="right"||h==="end")&&(v="flex-end",p=u),e.position=this._cssPosition,e.marginLeft=c?"0":g,e.marginTop=d?"0":this._topOffset,e.marginBottom=this._bottomOffset,e.marginRight=c?"0":p,t.justifyContent=v,t.alignItems=d?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let e=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement,i=t.style;t.classList.remove(Kt),i.justifyContent=i.alignItems=e.marginTop=e.marginBottom=e.marginLeft=e.marginRight=e.position="",this._overlayRef=null,this._isDisposed=!0}},se=(()=>{class o{_viewportRuler=a(et);_document=a(w);_platform=a(P);_overlayContainer=a(ie);constructor(){}global(){return new lt}flexibleConnectedTo(t){return new at(t,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),W=(()=>{class o{scrollStrategies=a(qt);_overlayContainer=a(ie);_positionBuilder=a(se);_keyboardDispatcher=a(Jt);_injector=a(T);_ngZone=a(y);_document=a(w);_directionality=a(N);_location=a(zt);_outsideClickDispatcher=a(te);_animationsModuleType=a(Ft,{optional:!0});_idGenerator=a(Lt);_renderer=a(E).createRenderer(null,null);_appRef;_styleLoader=a(gt);constructor(){}create(t){this._styleLoader.load(ee);let i=this._createHostElement(),s=this._createPaneElement(i),n=this._createPortalOutlet(s),r=new H(t);return r.direction=r.direction||this._directionality.value,new rt(n,i,s,r,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,this._animationsModuleType==="NoopAnimations",this._injector.get(G),this._renderer)}position(){return this._positionBuilder}_createPaneElement(t){let i=this._document.createElement("div");return i.id=this._idGenerator.getId("cdk-overlay-"),i.classList.add("cdk-overlay-pane"),t.appendChild(i),i}_createHostElement(){let t=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(t),t}_createPortalOutlet(t){return this._appRef||(this._appRef=this._injector.get(At)),new J(t,null,this._appRef,this._injector,this._document)}static \u0275fac=function(i){return new(i||o)};static \u0275prov=m({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})(),de=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],oe=new dt("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let o=a(W);return()=>o.scrollStrategies.reposition()}}),kt=(()=>{class o{elementRef=a(R);constructor(){}static \u0275fac=function(i){return new(i||o)};static \u0275dir=k({type:o,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"]})}return o})(),fe=(()=>{class o{_overlay=a(W);_dir=a(N,{optional:!0});_overlayRef;_templatePortal;_backdropSubscription=S.EMPTY;_attachSubscription=S.EMPTY;_detachSubscription=S.EMPTY;_positionSubscription=S.EMPTY;_offsetX;_offsetY;_position;_scrollStrategyFactory=a(oe);_disposeOnNavigation=!1;_ngZone=a(y);origin;positions;positionStrategy;get offsetX(){return this._offsetX}set offsetX(t){this._offsetX=t,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(t){this._offsetY=t,this._position&&this._updatePositionStrategy(this._position)}width;height;minWidth;minHeight;backdropClass;panelClass;viewportMargin=0;scrollStrategy;open=!1;disableClose=!1;transformOriginSelector;hasBackdrop=!1;lockPosition=!1;flexibleDimensions=!1;growAfterOpen=!1;push=!1;get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(t){this._disposeOnNavigation=t}backdropClick=new C;positionChange=new C;attach=new C;detach=new C;overlayKeydown=new C;overlayOutsideClick=new C;constructor(){let t=a(I),i=a(F);this._templatePortal=new A(t,i),this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef?.dispose()}ngOnChanges(t){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef?.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),t.origin&&this.open&&this._position.apply()),t.open&&(this.open?this.attachOverlay():this.detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=de);let t=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=t.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=t.detachments().subscribe(()=>this.detach.emit()),t.keydownEvents().subscribe(i=>{this.overlayKeydown.next(i),i.keyCode===27&&!this.disableClose&&!jt(i)&&(i.preventDefault(),this.detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(i=>{let s=this._getOriginElement(),n=K(i);(!s||s!==n&&!s.contains(n))&&this.overlayOutsideClick.next(i)})}_buildConfig(){let t=this._position=this.positionStrategy||this._createPositionStrategy(),i=new H({direction:this._dir||"ltr",positionStrategy:t,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(i.width=this.width),(this.height||this.height===0)&&(i.height=this.height),(this.minWidth||this.minWidth===0)&&(i.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(i.minHeight=this.minHeight),this.backdropClass&&(i.backdropClass=this.backdropClass),this.panelClass&&(i.panelClass=this.panelClass),i}_updatePositionStrategy(t){let i=this.positions.map(s=>({originX:s.originX,originY:s.originY,overlayX:s.overlayX,overlayY:s.overlayY,offsetX:s.offsetX||this.offsetX,offsetY:s.offsetY||this.offsetY,panelClass:s.panelClass||void 0}));return t.setOrigin(this._getOrigin()).withPositions(i).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let t=this._overlay.position().flexibleConnectedTo(this._getOrigin());return this._updatePositionStrategy(t),t}_getOrigin(){return this.origin instanceof kt?this.origin.elementRef:this.origin}_getOriginElement(){return this.origin instanceof kt?this.origin.elementRef.nativeElement:this.origin instanceof R?this.origin.nativeElement:typeof Element<"u"&&this.origin instanceof Element?this.origin:null}attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(t=>{this.backdropClick.emit(t)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(Mt(()=>this.positionChange.observers.length>0)).subscribe(t=>{this._ngZone.run(()=>this.positionChange.emit(t)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()})),this.open=!0}detachOverlay(){this._overlayRef?.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.open=!1}static \u0275fac=function(i){return new(i||o)};static \u0275dir=k({type:o,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[0,"cdkConnectedOverlayOrigin","origin"],positions:[0,"cdkConnectedOverlayPositions","positions"],positionStrategy:[0,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[0,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[0,"cdkConnectedOverlayOffsetY","offsetY"],width:[0,"cdkConnectedOverlayWidth","width"],height:[0,"cdkConnectedOverlayHeight","height"],minWidth:[0,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[0,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[0,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[0,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[0,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[0,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[0,"cdkConnectedOverlayOpen","open"],disableClose:[0,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[0,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[2,"cdkConnectedOverlayHasBackdrop","hasBackdrop",O],lockPosition:[2,"cdkConnectedOverlayLockPosition","lockPosition",O],flexibleDimensions:[2,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",O],growAfterOpen:[2,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",O],push:[2,"cdkConnectedOverlayPush","push",O],disposeOnNavigation:[2,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",O]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],features:[ft]})}return o})();function ue(o){return()=>o.scrollStrategies.reposition()}var pe={provide:oe,deps:[W],useFactory:ue},_e=(()=>{class o{static \u0275fac=function(i){return new(i||o)};static \u0275mod=D({type:o});static \u0275inj=x({providers:[W,pe],imports:[L,Ht,Ct,Ct]})}return o})();export{yt as a,A as b,Q as c,Ce as d,Se as e,Ht as f,tt as g,ae as h,et as i,bt as j,Ct as k,H as l,ie as m,rt as n,at as o,W as p,kt as q,fe as r,_e as s};
