{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  _platform = inject(Platform);\n  constructor() {}\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InteractivityChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _injector = inject(Injector);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  /** Underlying FocusTrap instance. */\n  focusTrap;\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  _previouslyFocusedElement = null;\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  autoCapture;\n  constructor() {\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTrapFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTrapFocus,\n    selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n    inputs: {\n      enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n      autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n    },\n    exportAs: [\"cdkTrapFocus\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  _ngZone = inject(NgZone);\n  _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _liveElement;\n  _document = inject(DOCUMENT);\n  _previousTimeout;\n  _currentPromise;\n  _currentResolve;\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LiveAnnouncer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LiveAnnouncer,\n    factory: LiveAnnouncer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  _elementRef = inject(ElementRef);\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _contentObserver = inject(ContentObserver);\n  _ngZone = inject(NgZone);\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  _politeness = 'polite';\n  /** Time in milliseconds after which to clear out the announcer element. */\n  duration;\n  _previousAnnouncedText;\n  _subscription;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAriaLive)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAriaLive,\n    selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n    inputs: {\n      politeness: [0, \"cdkAriaLive\", \"politeness\"],\n      duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n    },\n    exportAs: [\"cdkAriaLive\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  _platform = inject(Platform);\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  _hasCheckedHighContrastMode;\n  _document = inject(DOCUMENT);\n  _breakpointSubscription;\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HighContrastModeDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HighContrastModeDetector,\n    factory: HighContrastModeDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || A11yModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: A11yModule,\n    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };", "map": {"version": 3, "names": ["i0", "inject", "Injectable", "afterNextRender", "NgZone", "Injector", "ElementRef", "booleanAttribute", "Directive", "Input", "InjectionToken", "NgModule", "C", "CdkMonitorFocus", "DOCUMENT", "P", "Platform", "c", "_getFocusedElementPierceShadowDom", "_", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "B", "BreakpointObserver", "ContentObserver", "ObserversModule", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "_platform", "constructor", "isDisabled", "element", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "visibility", "isTabbable", "<PERSON><PERSON><PERSON><PERSON>", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "ɵfac", "InteractivityChecker_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "window", "offsetWidth", "offsetHeight", "getClientRects", "length", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "undefined", "getAttribute", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_element", "_checker", "_ngZone", "_document", "_injector", "_startAnchor", "_endAnchor", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "enabled", "_enabled", "value", "_toggleAnchorTabIndex", "deferAnchors", "attachAnchors", "destroy", "startAnchor", "endAnchor", "removeEventListener", "remove", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "querySelectorAll", "i", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "focus", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "anchor", "createElement", "classList", "add", "setAttribute", "isEnabled", "removeAttribute", "toggleAnchors", "fn", "injector", "setTimeout", "FocusTrapFactory", "load", "create", "deferCaptureElements", "FocusTrapFactory_Factory", "CdkTrapFocus", "_elementRef", "_focusTrapFactory", "focusTrap", "_previouslyFocusedElement", "autoCapture", "platform", "nativeElement", "ngOnDestroy", "ngAfterContentInit", "_captureFocus", "ngDoCheck", "ngOnChanges", "changes", "autoCaptureChange", "firstChange", "CdkTrapFocus_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "selector", "alias", "transform", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "uniqueIds", "LiveAnnouncer", "_defaultOptions", "optional", "_liveElement", "_previousTimeout", "_currentPromise", "_currentResolve", "elementToken", "_createLiveElement", "announce", "message", "defaultOptions", "politeness", "duration", "clear", "clearTimeout", "id", "_exposeAnnouncerToModals", "textContent", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "body", "append<PERSON><PERSON><PERSON>", "modals", "modal", "ariaOwns", "indexOf", "LiveAnnouncer_Factory", "CdkAriaLive", "_liveAnnouncer", "_contentObserver", "_politeness", "_subscription", "unsubscribe", "observe", "subscribe", "elementText", "_previousAnnouncedText", "CdkAriaLive_Factory", "HighContrastMode", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "_hasCheckedHighContrastMode", "_breakpointSubscription", "_applyBodyHighContrastModeCssClasses", "getHighContrastMode", "NONE", "testElement", "style", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "WHITE_ON_BLACK", "BLACK_ON_WHITE", "bodyClasses", "mode", "HighContrastModeDetector_Factory", "A11yModule", "A11yModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector", "A", "F", "H", "I", "L", "a", "b", "d", "e", "f", "g"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/a11y-module-BYox5gpI.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-e2l_RpN3.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { _VisuallyHiddenLoader } from './private.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-CljOfYGy.mjs';\nimport { ContentObserver, ObserversModule } from './observers.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    _platform = inject(Platform);\n    constructor() { }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n    _element;\n    _checker;\n    _ngZone;\n    _document;\n    _injector;\n    _startAnchor;\n    _endAnchor;\n    _hasAttached = false;\n    // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n    startAnchorListener = () => this.focusLastTabbableElement();\n    endAnchorListener = () => this.focusFirstTabbableElement();\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    _enabled = true;\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false, \n    /** @breaking-change 20.0.0 param to become required */\n    _injector) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._injector = _injector;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        // TODO: remove this conditional when injector is required in the constructor.\n        if (this._injector) {\n            afterNextRender(fn, { injector: this._injector });\n        }\n        else {\n            setTimeout(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n    _checker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    _injector = inject(Injector);\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    /** Underlying FocusTrap instance. */\n    focusTrap;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n    _previouslyFocusedElement = null;\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap?.enabled || false;\n    }\n    set enabled(value) {\n        if (this.focusTrap) {\n            this.focusTrap.enabled = value;\n        }\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    autoCapture;\n    constructor() {\n        const platform = inject(Platform);\n        if (platform.isBrowser) {\n            this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n        }\n    }\n    ngOnDestroy() {\n        this.focusTrap?.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap?.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (this.focusTrap && !this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap?.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap?.focusInitialElementWhenReady();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTrapFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkTrapFocus, isStandalone: true, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\", booleanAttribute], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                }]\n        }], ctorParameters: () => [], propDecorators: { enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocus', transform: booleanAttribute }]\n            }], autoCapture: [{\n                type: Input,\n                args: [{ alias: 'cdkTrapFocusAutoCapture', transform: booleanAttribute }]\n            }] } });\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n    _ngZone = inject(NgZone);\n    _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _liveElement;\n    _document = inject(DOCUMENT);\n    _previousTimeout;\n    _currentPromise;\n    _currentResolve;\n    constructor() {\n        const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, { optional: true });\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        if (this._liveElement.id) {\n            this._exposeAnnouncerToModals(this._liveElement.id);\n        }\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                // For some reason in tests this can be undefined\n                // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n                this._currentResolve?.();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live announcer element if there is an\n     * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live announcer element.\n     */\n    _exposeAnnouncerToModals(id) {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `SnakBarContainer` and other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    _elementRef = inject(ElementRef);\n    _liveAnnouncer = inject(LiveAnnouncer);\n    _contentObserver = inject(ContentObserver);\n    _ngZone = inject(NgZone);\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    _politeness = 'polite';\n    /** Time in milliseconds after which to clear out the announcer element. */\n    duration;\n    _previousAnnouncedText;\n    _subscription;\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAriaLive, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkAriaLive, isStandalone: true, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"], duration: [\"cdkAriaLiveDuration\", \"duration\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                }]\n        }], ctorParameters: () => [], propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }], duration: [{\n                type: Input,\n                args: ['cdkAriaLiveDuration']\n            }] } });\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n    HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n    HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n    HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    _platform = inject(Platform);\n    /**\n     * Figuring out the high contrast mode and adding the body classes can cause\n     * some expensive layouts. This flag is used to ensure that we only do it once.\n     */\n    _hasCheckedHighContrastMode;\n    _document = inject(DOCUMENT);\n    _breakpointSubscription;\n    constructor() {\n        this._breakpointSubscription = inject(BreakpointObserver)\n            .observe('(forced-colors: active)')\n            .subscribe(() => {\n            if (this._hasCheckedHighContrastMode) {\n                this._hasCheckedHighContrastMode = false;\n                this._applyBodyHighContrastModeCssClasses();\n            }\n        });\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return HighContrastMode.NONE;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            // Pre Windows 11 dark theme.\n            case 'rgb(0,0,0)':\n            // Windows 11 dark themes.\n            case 'rgb(45,50,54)':\n            case 'rgb(32,32,32)':\n                return HighContrastMode.WHITE_ON_BLACK;\n            // Pre Windows 11 light theme.\n            case 'rgb(255,255,255)':\n            // Windows 11 light theme.\n            case 'rgb(255,250,239)':\n                return HighContrastMode.BLACK_ON_WHITE;\n        }\n        return HighContrastMode.NONE;\n    }\n    ngOnDestroy() {\n        this._breakpointSubscription.unsubscribe();\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === HighContrastMode.BLACK_ON_WHITE) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nclass A11yModule {\n    constructor() {\n        inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, imports: [ObserversModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: () => [] });\n\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrapFactory as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, FocusTrap as a, HighContrastMode as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,eAAe,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,eAAe;AAC/J,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,qBAAqB,QAAQ,eAAe;AACrD,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qCAAqC;AAC7E,SAASC,eAAe,EAAEC,eAAe,QAAQ,iBAAiB;;AAElE;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpB;AACJ;AACA;EACIC,gBAAgB,GAAG,KAAK;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,SAAS,GAAG5B,MAAM,CAACe,QAAQ,CAAC;EAC5Bc,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACC,OAAO,EAAE;IAChB;IACA;IACA,OAAOA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACF,OAAO,EAAE;IACf,OAAOG,WAAW,CAACH,OAAO,CAAC,IAAII,gBAAgB,CAACJ,OAAO,CAAC,CAACK,UAAU,KAAK,SAAS;EACrF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACN,OAAO,EAAE;IAChB;IACA,IAAI,CAAC,IAAI,CAACH,SAAS,CAACU,SAAS,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMC,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACV,OAAO,CAAC,CAAC;IACxD,IAAIQ,YAAY,EAAE;MACd;MACA,IAAIG,gBAAgB,CAACH,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QACvC,OAAO,KAAK;MAChB;MACA;MACA,IAAI,CAAC,IAAI,CAACN,SAAS,CAACM,YAAY,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,IAAII,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,aAAa,GAAGH,gBAAgB,CAACX,OAAO,CAAC;IAC7C,IAAIA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,EAAE;MACzC,OAAOa,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAChD;MACA;MACA;MACA,OAAO,KAAK;IAChB;IACA;IACA,IAAI,IAAI,CAACf,SAAS,CAACkB,MAAM,IAAI,IAAI,CAAClB,SAAS,CAACmB,GAAG,IAAI,CAACC,wBAAwB,CAACjB,OAAO,CAAC,EAAE;MACnF,OAAO,KAAK;IAChB;IACA,IAAIY,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA,IAAI,CAACZ,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;QACnC,OAAO,KAAK;MAChB;MACA;MACA;MACA,OAAOa,aAAa,KAAK,CAAC,CAAC;IAC/B;IACA,IAAIF,QAAQ,KAAK,OAAO,EAAE;MACtB;MACA;MACA;MACA;MACA,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB,OAAO,KAAK;MAChB;MACA;MACA;MACA,IAAIA,aAAa,KAAK,IAAI,EAAE;QACxB,OAAO,IAAI;MACf;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACjB,SAAS,CAACqB,OAAO,IAAIlB,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC;IACrE;IACA,OAAOD,OAAO,CAACmB,QAAQ,IAAI,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACpB,OAAO,EAAEqB,MAAM,EAAE;IACzB;IACA;IACA,OAAQC,sBAAsB,CAACtB,OAAO,CAAC,IACnC,CAAC,IAAI,CAACD,UAAU,CAACC,OAAO,CAAC,KACxBqB,MAAM,EAAE1B,gBAAgB,IAAI,IAAI,CAACO,SAAS,CAACF,OAAO,CAAC,CAAC;EAC7D;EACA,OAAOuB,IAAI,YAAAC,6BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF7B,oBAAoB;EAAA;EACvH,OAAO8B,KAAK,kBAD6E1D,EAAE,CAAA2D,kBAAA;IAAAC,KAAA,EACYhC,oBAAoB;IAAAiC,OAAA,EAApBjC,oBAAoB,CAAA2B,IAAA;IAAAO,UAAA,EAAc;EAAM;AACnJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F/D,EAAE,CAAAgE,iBAAA,CAGJpC,oBAAoB,EAAc,CAAC;IAClHqC,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA,SAASrB,eAAeA,CAAC0B,MAAM,EAAE;EAC7B,IAAI;IACA,OAAOA,MAAM,CAAC3B,YAAY;EAC9B,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASL,WAAWA,CAACH,OAAO,EAAE;EAC1B;EACA;EACA,OAAO,CAAC,EAAEA,OAAO,CAACoC,WAAW,IACzBpC,OAAO,CAACqC,YAAY,IACnB,OAAOrC,OAAO,CAACsC,cAAc,KAAK,UAAU,IAAItC,OAAO,CAACsC,cAAc,CAAC,CAAC,CAACC,MAAO,CAAC;AAC1F;AACA;AACA,SAASC,mBAAmBA,CAACxC,OAAO,EAAE;EAClC,IAAIY,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,OAAQD,QAAQ,KAAK,OAAO,IACxBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA,SAAS6B,aAAaA,CAACzC,OAAO,EAAE;EAC5B,OAAO0C,cAAc,CAAC1C,OAAO,CAAC,IAAIA,OAAO,CAACiC,IAAI,IAAI,QAAQ;AAC9D;AACA;AACA,SAASU,gBAAgBA,CAAC3C,OAAO,EAAE;EAC/B,OAAO4C,eAAe,CAAC5C,OAAO,CAAC,IAAIA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;AACnE;AACA;AACA,SAASyC,cAAcA,CAAC1C,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,OAAO;AACpD;AACA;AACA,SAAS+B,eAAeA,CAAC5C,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC,CAAC,IAAI,GAAG;AAChD;AACA;AACA,SAASgC,gBAAgBA,CAAC7C,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,IAAID,OAAO,CAACmB,QAAQ,KAAK2B,SAAS,EAAE;IACrE,OAAO,KAAK;EAChB;EACA,IAAI3B,QAAQ,GAAGnB,OAAO,CAAC+C,YAAY,CAAC,UAAU,CAAC;EAC/C,OAAO,CAAC,EAAE5B,QAAQ,IAAI,CAAC6B,KAAK,CAACC,QAAQ,CAAC9B,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA;AACA,SAASR,gBAAgBA,CAACX,OAAO,EAAE;EAC/B,IAAI,CAAC6C,gBAAgB,CAAC7C,OAAO,CAAC,EAAE;IAC5B,OAAO,IAAI;EACf;EACA;EACA,MAAMmB,QAAQ,GAAG8B,QAAQ,CAACjD,OAAO,CAAC+C,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;EACrE,OAAOC,KAAK,CAAC7B,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAGA,QAAQ;AAC1C;AACA;AACA,SAASF,wBAAwBA,CAACjB,OAAO,EAAE;EACvC,IAAIY,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIqC,SAAS,GAAGtC,QAAQ,KAAK,OAAO,IAAIZ,OAAO,CAACiC,IAAI;EACpD,OAAQiB,SAAS,KAAK,MAAM,IACxBA,SAAS,KAAK,UAAU,IACxBtC,QAAQ,KAAK,QAAQ,IACrBA,QAAQ,KAAK,UAAU;AAC/B;AACA;AACA;AACA;AACA;AACA,SAASU,sBAAsBA,CAACtB,OAAO,EAAE;EACrC;EACA,IAAIyC,aAAa,CAACzC,OAAO,CAAC,EAAE;IACxB,OAAO,KAAK;EAChB;EACA,OAAQwC,mBAAmB,CAACxC,OAAO,CAAC,IAChC2C,gBAAgB,CAAC3C,OAAO,CAAC,IACzBA,OAAO,CAACC,YAAY,CAAC,iBAAiB,CAAC,IACvC4C,gBAAgB,CAAC7C,OAAO,CAAC;AACjC;AACA;AACA,SAASU,SAASA,CAACyC,IAAI,EAAE;EACrB;EACA,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,IAAKlB,MAAM;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,SAAS,CAAC;EACZC,QAAQ;EACRC,QAAQ;EACRC,OAAO;EACPC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,UAAU;EACVC,YAAY,GAAG,KAAK;EACpB;EACAC,mBAAmB,GAAGA,CAAA,KAAM,IAAI,CAACC,wBAAwB,CAAC,CAAC;EAC3DC,iBAAiB,GAAGA,CAAA,KAAM,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAC1D;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,IAAI,CAACT,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACS,qBAAqB,CAACD,KAAK,EAAE,IAAI,CAACT,YAAY,CAAC;MACpD,IAAI,CAACU,qBAAqB,CAACD,KAAK,EAAE,IAAI,CAACR,UAAU,CAAC;IACtD;EACJ;EACAO,QAAQ,GAAG,IAAI;EACftE,WAAWA,CAACyD,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEa,YAAY,GAAG,KAAK,EACxE;EACAZ,SAAS,EAAE;IACP,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACY,YAAY,EAAE;MACf,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,MAAMC,WAAW,GAAG,IAAI,CAACd,YAAY;IACrC,MAAMe,SAAS,GAAG,IAAI,CAACd,UAAU;IACjC,IAAIa,WAAW,EAAE;MACbA,WAAW,CAACE,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACb,mBAAmB,CAAC;MAClEW,WAAW,CAACG,MAAM,CAAC,CAAC;IACxB;IACA,IAAIF,SAAS,EAAE;MACXA,SAAS,CAACC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACX,iBAAiB,CAAC;MAC9DU,SAAS,CAACE,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAACjB,YAAY,GAAG,IAAI,CAACC,UAAU,GAAG,IAAI;IAC1C,IAAI,CAACC,YAAY,GAAG,KAAK;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,aAAaA,CAAA,EAAG;IACZ;IACA,IAAI,IAAI,CAACV,YAAY,EAAE;MACnB,OAAO,IAAI;IACf;IACA,IAAI,CAACL,OAAO,CAACqB,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC,IAAI,CAAClB,YAAY,EAAE;QACpB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACmB,aAAa,CAAC,CAAC;QACxC,IAAI,CAACnB,YAAY,CAACoB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACjB,mBAAmB,CAAC;MACzE;MACA,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,IAAI,CAACkB,aAAa,CAAC,CAAC;QACtC,IAAI,CAAClB,UAAU,CAACmB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACf,iBAAiB,CAAC;MACrE;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACV,QAAQ,CAAC0B,UAAU,EAAE;MAC1B,IAAI,CAAC1B,QAAQ,CAAC0B,UAAU,CAACC,YAAY,CAAC,IAAI,CAACtB,YAAY,EAAE,IAAI,CAACL,QAAQ,CAAC;MACvE,IAAI,CAACA,QAAQ,CAAC0B,UAAU,CAACC,YAAY,CAAC,IAAI,CAACrB,UAAU,EAAE,IAAI,CAACN,QAAQ,CAAC4B,WAAW,CAAC;MACjF,IAAI,CAACrB,YAAY,GAAG,IAAI;IAC5B;IACA,OAAO,IAAI,CAACA,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIsB,4BAA4BA,CAACC,OAAO,EAAE;IAClC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACE,mBAAmB,CAACJ,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,kCAAkCA,CAACL,OAAO,EAAE;IACxC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACrB,yBAAyB,CAACmB,OAAO,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,iCAAiCA,CAACN,OAAO,EAAE;IACvC,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACC,gBAAgB,CAAC,MAAMD,OAAO,CAAC,IAAI,CAACvB,wBAAwB,CAACqB,OAAO,CAAC,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIO,kBAAkBA,CAACC,KAAK,EAAE;IACtB;IACA,MAAMC,OAAO,GAAG,IAAI,CAACvC,QAAQ,CAACwC,gBAAgB,CAAC,qBAAqBF,KAAK,KAAK,GAAG,kBAAkBA,KAAK,KAAK,GAAG,cAAcA,KAAK,GAAG,CAAC;IACvI,IAAI,OAAO9D,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACvD,MAAM,EAAEyD,CAAC,EAAE,EAAE;QACrC;QACA,IAAIF,OAAO,CAACE,CAAC,CAAC,CAAC/F,YAAY,CAAC,aAAa4F,KAAK,EAAE,CAAC,EAAE;UAC/CI,OAAO,CAACC,IAAI,CAAC,gDAAgDL,KAAK,KAAK,GACnE,sBAAsBA,KAAK,4BAA4B,GACvD,qCAAqC,EAAEC,OAAO,CAACE,CAAC,CAAC,CAAC;QAC1D,CAAC,MACI,IAAIF,OAAO,CAACE,CAAC,CAAC,CAAC/F,YAAY,CAAC,oBAAoB4F,KAAK,EAAE,CAAC,EAAE;UAC3DI,OAAO,CAACC,IAAI,CAAC,uDAAuDL,KAAK,KAAK,GAC1E,sBAAsBA,KAAK,sCAAsC,GACjE,2BAA2B,EAAEC,OAAO,CAACE,CAAC,CAAC,CAAC;QAChD;MACJ;IACJ;IACA,IAAIH,KAAK,IAAI,OAAO,EAAE;MAClB,OAAOC,OAAO,CAACvD,MAAM,GAAGuD,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAACK,wBAAwB,CAAC,IAAI,CAAC5C,QAAQ,CAAC;IACrF;IACA,OAAOuC,OAAO,CAACvD,MAAM,GACfuD,OAAO,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC,GAC3B,IAAI,CAAC6D,uBAAuB,CAAC,IAAI,CAAC7C,QAAQ,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIkC,mBAAmBA,CAACJ,OAAO,EAAE;IACzB;IACA,MAAMgB,iBAAiB,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,aAAa,CAAC,uBAAuB,GAAG,mBAAmB,CAAC;IACpG,IAAID,iBAAiB,EAAE;MACnB;MACA,IAAI,CAAC,OAAOtE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9CsE,iBAAiB,CAACpG,YAAY,CAAC,mBAAmB,CAAC,EAAE;QACrDgG,OAAO,CAACC,IAAI,CAAC,yDAAyD,GAClE,0DAA0D,GAC1D,0BAA0B,EAAEG,iBAAiB,CAAC;MACtD;MACA;MACA;MACA,IAAI,CAAC,OAAOtE,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC9C,CAAC,IAAI,CAACyB,QAAQ,CAACpC,WAAW,CAACiF,iBAAiB,CAAC,EAAE;QAC/CJ,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEG,iBAAiB,CAAC;MAC7F;MACA,IAAI,CAAC,IAAI,CAAC7C,QAAQ,CAACpC,WAAW,CAACiF,iBAAiB,CAAC,EAAE;QAC/C,MAAME,cAAc,GAAG,IAAI,CAACJ,wBAAwB,CAACE,iBAAiB,CAAC;QACvEE,cAAc,EAAEC,KAAK,CAACnB,OAAO,CAAC;QAC9B,OAAO,CAAC,CAACkB,cAAc;MAC3B;MACAF,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACnB,yBAAyB,CAACmB,OAAO,CAAC;EAClD;EACA;AACJ;AACA;AACA;EACInB,yBAAyBA,CAACmB,OAAO,EAAE;IAC/B,MAAMgB,iBAAiB,GAAG,IAAI,CAACT,kBAAkB,CAAC,OAAO,CAAC;IAC1D,IAAIS,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACgB,iBAAiB;EAC9B;EACA;AACJ;AACA;AACA;EACIrC,wBAAwBA,CAACqB,OAAO,EAAE;IAC9B,MAAMgB,iBAAiB,GAAG,IAAI,CAACT,kBAAkB,CAAC,KAAK,CAAC;IACxD,IAAIS,iBAAiB,EAAE;MACnBA,iBAAiB,CAACG,KAAK,CAACnB,OAAO,CAAC;IACpC;IACA,OAAO,CAAC,CAACgB,iBAAiB;EAC9B;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3C,YAAY;EAC5B;EACA;EACAqC,wBAAwBA,CAACO,IAAI,EAAE;IAC3B,IAAI,IAAI,CAAClD,QAAQ,CAACpC,WAAW,CAACsF,IAAI,CAAC,IAAI,IAAI,CAAClD,QAAQ,CAAClD,UAAU,CAACoG,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,QAAQ,CAACpE,MAAM,EAAEyD,CAAC,EAAE,EAAE;MACtC,MAAMY,aAAa,GAAGD,QAAQ,CAACX,CAAC,CAAC,CAACa,QAAQ,KAAK,IAAI,CAACnD,SAAS,CAACoD,YAAY,GACpE,IAAI,CAACX,wBAAwB,CAACQ,QAAQ,CAACX,CAAC,CAAC,CAAC,GAC1C,IAAI;MACV,IAAIY,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACAR,uBAAuBA,CAACM,IAAI,EAAE;IAC1B,IAAI,IAAI,CAAClD,QAAQ,CAACpC,WAAW,CAACsF,IAAI,CAAC,IAAI,IAAI,CAAClD,QAAQ,CAAClD,UAAU,CAACoG,IAAI,CAAC,EAAE;MACnE,OAAOA,IAAI;IACf;IACA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC9B,KAAK,IAAIX,CAAC,GAAGW,QAAQ,CAACpE,MAAM,GAAG,CAAC,EAAEyD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMY,aAAa,GAAGD,QAAQ,CAACX,CAAC,CAAC,CAACa,QAAQ,KAAK,IAAI,CAACnD,SAAS,CAACoD,YAAY,GACpE,IAAI,CAACV,uBAAuB,CAACO,QAAQ,CAACX,CAAC,CAAC,CAAC,GACzC,IAAI;MACV,IAAIY,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA7B,aAAaA,CAAA,EAAG;IACZ,MAAMgC,MAAM,GAAG,IAAI,CAACrD,SAAS,CAACsD,aAAa,CAAC,KAAK,CAAC;IAClD,IAAI,CAAC1C,qBAAqB,CAAC,IAAI,CAACF,QAAQ,EAAE2C,MAAM,CAAC;IACjDA,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CH,MAAM,CAACE,SAAS,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAC7CH,MAAM,CAACI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1C,OAAOJ,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIzC,qBAAqBA,CAAC8C,SAAS,EAAEL,MAAM,EAAE;IACrC;IACA;IACAK,SAAS,GAAGL,MAAM,CAACI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,GAAGJ,MAAM,CAACM,eAAe,CAAC,UAAU,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACnD,OAAO,EAAE;IACnB,IAAI,IAAI,CAACP,YAAY,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACS,qBAAqB,CAACH,OAAO,EAAE,IAAI,CAACP,YAAY,CAAC;MACtD,IAAI,CAACU,qBAAqB,CAACH,OAAO,EAAE,IAAI,CAACN,UAAU,CAAC;IACxD;EACJ;EACA;EACA2B,gBAAgBA,CAAC+B,EAAE,EAAE;IACjB;IACA,IAAI,IAAI,CAAC5D,SAAS,EAAE;MAChBxF,eAAe,CAACoJ,EAAE,EAAE;QAAEC,QAAQ,EAAE,IAAI,CAAC7D;MAAU,CAAC,CAAC;IACrD,CAAC,MACI;MACD8D,UAAU,CAACF,EAAE,CAAC;IAClB;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAMG,gBAAgB,CAAC;EACnBlE,QAAQ,GAAGvF,MAAM,CAAC2B,oBAAoB,CAAC;EACvC6D,OAAO,GAAGxF,MAAM,CAACG,MAAM,CAAC;EACxBsF,SAAS,GAAGzF,MAAM,CAACa,QAAQ,CAAC;EAC5B6E,SAAS,GAAG1F,MAAM,CAACI,QAAQ,CAAC;EAC5ByB,WAAWA,CAAA,EAAG;IACV7B,MAAM,CAACmB,sBAAsB,CAAC,CAACuI,IAAI,CAACtI,qBAAqB,CAAC;EAC9D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuI,MAAMA,CAAC5H,OAAO,EAAE6H,oBAAoB,GAAG,KAAK,EAAE;IAC1C,OAAO,IAAIvE,SAAS,CAACtD,OAAO,EAAE,IAAI,CAACwD,QAAQ,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,SAAS,EAAEmE,oBAAoB,EAAE,IAAI,CAAClE,SAAS,CAAC;EACpH;EACA,OAAOpC,IAAI,YAAAuG,yBAAArG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiG,gBAAgB;EAAA;EACnH,OAAOhG,KAAK,kBAnZ6E1D,EAAE,CAAA2D,kBAAA;IAAAC,KAAA,EAmZY8F,gBAAgB;IAAA7F,OAAA,EAAhB6F,gBAAgB,CAAAnG,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArZ6F/D,EAAE,CAAAgE,iBAAA,CAqZJ0F,gBAAgB,EAAc,CAAC;IAC9GzF,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA,MAAMiG,YAAY,CAAC;EACfC,WAAW,GAAG/J,MAAM,CAACK,UAAU,CAAC;EAChC2J,iBAAiB,GAAGhK,MAAM,CAACyJ,gBAAgB,CAAC;EAC5C;EACAQ,SAAS;EACT;EACAC,yBAAyB,GAAG,IAAI;EAChC;EACA,IAAIhE,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC+D,SAAS,EAAE/D,OAAO,IAAI,KAAK;EAC3C;EACA,IAAIA,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,IAAI,CAAC6D,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC/D,OAAO,GAAGE,KAAK;IAClC;EACJ;EACA;AACJ;AACA;AACA;EACI+D,WAAW;EACXtI,WAAWA,CAAA,EAAG;IACV,MAAMuI,QAAQ,GAAGpK,MAAM,CAACe,QAAQ,CAAC;IACjC,IAAIqJ,QAAQ,CAAC9H,SAAS,EAAE;MACpB,IAAI,CAAC2H,SAAS,GAAG,IAAI,CAACD,iBAAiB,CAACL,MAAM,CAAC,IAAI,CAACI,WAAW,CAACM,aAAa,EAAE,IAAI,CAAC;IACxF;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,SAAS,EAAEzD,OAAO,CAAC,CAAC;IACzB;IACA;IACA,IAAI,IAAI,CAAC0D,yBAAyB,EAAE;MAChC,IAAI,CAACA,yBAAyB,CAAC3B,KAAK,CAAC,CAAC;MACtC,IAAI,CAAC2B,yBAAyB,GAAG,IAAI;IACzC;EACJ;EACAK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,SAAS,EAAE1D,aAAa,CAAC,CAAC;IAC/B,IAAI,IAAI,CAAC4D,WAAW,EAAE;MAClB,IAAI,CAACK,aAAa,CAAC,CAAC;IACxB;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACR,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACzB,WAAW,CAAC,CAAC,EAAE;MACjD,IAAI,CAACyB,SAAS,CAAC1D,aAAa,CAAC,CAAC;IAClC;EACJ;EACAmE,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,iBAAiB,GAAGD,OAAO,CAAC,aAAa,CAAC;IAChD,IAAIC,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WAAW,IAC9B,IAAI,CAACV,WAAW,IAChB,IAAI,CAACF,SAAS,EAAEzB,WAAW,CAAC,CAAC,EAAE;MAC/B,IAAI,CAACgC,aAAa,CAAC,CAAC;IACxB;EACJ;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACN,yBAAyB,GAAGjJ,iCAAiC,CAAC,CAAC;IACpE,IAAI,CAACgJ,SAAS,EAAE9C,4BAA4B,CAAC,CAAC;EAClD;EACA,OAAO7D,IAAI,YAAAwH,qBAAAtH,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsG,YAAY;EAAA;EAC/G,OAAOiB,IAAI,kBAvd8EhL,EAAE,CAAAiL,iBAAA;IAAAhH,IAAA,EAudJ8F,YAAY;IAAAmB,SAAA;IAAAC,MAAA;MAAAhF,OAAA,iCAAiG5F,gBAAgB;MAAA6J,WAAA,gDAA2D7J,gBAAgB;IAAA;IAAA6K,QAAA;IAAAC,QAAA,GAvdtMrL,EAAE,CAAAsL,oBAAA;EAAA;AAwd/F;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KAzd6F/D,EAAE,CAAAgE,iBAAA,CAydJ+F,YAAY,EAAc,CAAC;IAC1G9F,IAAI,EAAEzD,SAAS;IACf0D,IAAI,EAAE,CAAC;MACCqH,QAAQ,EAAE,gBAAgB;MAC1BH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEjF,OAAO,EAAE,CAAC;MAClDlC,IAAI,EAAExD,KAAK;MACXyD,IAAI,EAAE,CAAC;QAAEsH,KAAK,EAAE,cAAc;QAAEC,SAAS,EAAElL;MAAiB,CAAC;IACjE,CAAC,CAAC;IAAE6J,WAAW,EAAE,CAAC;MACdnG,IAAI,EAAExD,KAAK;MACXyD,IAAI,EAAE,CAAC;QAAEsH,KAAK,EAAE,yBAAyB;QAAEC,SAAS,EAAElL;MAAiB,CAAC;IAC5E,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmL,4BAA4B,GAAG,IAAIhL,cAAc,CAAC,sBAAsB,EAAE;EAC5EoD,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAE8H;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO,IAAI;AACf;AACA;AACA,MAAMC,8BAA8B,GAAG,IAAIlL,cAAc,CAAC,gCAAgC,CAAC;AAE3F,IAAImL,SAAS,GAAG,CAAC;AACjB,MAAMC,aAAa,CAAC;EAChBrG,OAAO,GAAGxF,MAAM,CAACG,MAAM,CAAC;EACxB2L,eAAe,GAAG9L,MAAM,CAAC2L,8BAA8B,EAAE;IACrDI,QAAQ,EAAE;EACd,CAAC,CAAC;EACFC,YAAY;EACZvG,SAAS,GAAGzF,MAAM,CAACa,QAAQ,CAAC;EAC5BoL,gBAAgB;EAChBC,eAAe;EACfC,eAAe;EACftK,WAAWA,CAAA,EAAG;IACV,MAAMuK,YAAY,GAAGpM,MAAM,CAACyL,4BAA4B,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7E,IAAI,CAACC,YAAY,GAAGI,YAAY,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;EACjE;EACAC,QAAQA,CAACC,OAAO,EAAE,GAAGtI,IAAI,EAAE;IACvB,MAAMuI,cAAc,GAAG,IAAI,CAACV,eAAe;IAC3C,IAAIW,UAAU;IACd,IAAIC,QAAQ;IACZ,IAAIzI,IAAI,CAACK,MAAM,KAAK,CAAC,IAAI,OAAOL,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClDyI,QAAQ,GAAGzI,IAAI,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD,CAACwI,UAAU,EAAEC,QAAQ,CAAC,GAAGzI,IAAI;IACjC;IACA,IAAI,CAAC0I,KAAK,CAAC,CAAC;IACZC,YAAY,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACnC,IAAI,CAACQ,UAAU,EAAE;MACbA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAU,GAAGD,cAAc,CAACC,UAAU,GAAG,QAAQ;IAC1F;IACA,IAAIC,QAAQ,IAAI,IAAI,IAAIF,cAAc,EAAE;MACpCE,QAAQ,GAAGF,cAAc,CAACE,QAAQ;IACtC;IACA;IACA,IAAI,CAACV,YAAY,CAAC9C,YAAY,CAAC,WAAW,EAAEuD,UAAU,CAAC;IACvD,IAAI,IAAI,CAACT,YAAY,CAACa,EAAE,EAAE;MACtB,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACd,YAAY,CAACa,EAAE,CAAC;IACvD;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAACrH,OAAO,CAACqB,iBAAiB,CAAC,MAAM;MACxC,IAAI,CAAC,IAAI,CAACqF,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI7E,OAAO,CAACC,OAAO,IAAK,IAAI,CAAC6E,eAAe,GAAG7E,OAAQ,CAAC;MACnF;MACAsF,YAAY,CAAC,IAAI,CAACX,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAGzC,UAAU,CAAC,MAAM;QACrC,IAAI,CAACwC,YAAY,CAACe,WAAW,GAAGR,OAAO;QACvC,IAAI,OAAOG,QAAQ,KAAK,QAAQ,EAAE;UAC9B,IAAI,CAACT,gBAAgB,GAAGzC,UAAU,CAAC,MAAM,IAAI,CAACmD,KAAK,CAAC,CAAC,EAAED,QAAQ,CAAC;QACpE;QACA;QACA;QACA,IAAI,CAACP,eAAe,GAAG,CAAC;QACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGtH,SAAS;MAC3D,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,IAAI,CAACqH,eAAe;IAC/B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIS,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACX,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACe,WAAW,GAAG,EAAE;IACtC;EACJ;EACAzC,WAAWA,CAAA,EAAG;IACVsC,YAAY,CAAC,IAAI,CAACX,gBAAgB,CAAC;IACnC,IAAI,CAACD,YAAY,EAAEpF,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACoF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACG,eAAe,GAAG,CAAC;IACxB,IAAI,CAACD,eAAe,GAAG,IAAI,CAACC,eAAe,GAAGtH,SAAS;EAC3D;EACAwH,kBAAkBA,CAAA,EAAG;IACjB,MAAMW,YAAY,GAAG,4BAA4B;IACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACxH,SAAS,CAACyH,sBAAsB,CAACF,YAAY,CAAC;IAC5E,MAAMG,MAAM,GAAG,IAAI,CAAC1H,SAAS,CAACsD,aAAa,CAAC,KAAK,CAAC;IAClD;IACA,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkF,gBAAgB,CAAC3I,MAAM,EAAEyD,CAAC,EAAE,EAAE;MAC9CkF,gBAAgB,CAAClF,CAAC,CAAC,CAACnB,MAAM,CAAC,CAAC;IAChC;IACAuG,MAAM,CAACnE,SAAS,CAACC,GAAG,CAAC+D,YAAY,CAAC;IAClCG,MAAM,CAACnE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAC3CkE,MAAM,CAACjE,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC1CiE,MAAM,CAACjE,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAC1CiE,MAAM,CAACN,EAAE,GAAG,sBAAsBjB,SAAS,EAAE,EAAE;IAC/C,IAAI,CAACnG,SAAS,CAAC2H,IAAI,CAACC,WAAW,CAACF,MAAM,CAAC;IACvC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIL,wBAAwBA,CAACD,EAAE,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMS,MAAM,GAAG,IAAI,CAAC7H,SAAS,CAACqC,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,MAAM,CAAChJ,MAAM,EAAEyD,CAAC,EAAE,EAAE;MACpC,MAAMwF,KAAK,GAAGD,MAAM,CAACvF,CAAC,CAAC;MACvB,MAAMyF,QAAQ,GAAGD,KAAK,CAACzI,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAAC0I,QAAQ,EAAE;QACXD,KAAK,CAACrE,YAAY,CAAC,WAAW,EAAE2D,EAAE,CAAC;MACvC,CAAC,MACI,IAAIW,QAAQ,CAACC,OAAO,CAACZ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCU,KAAK,CAACrE,YAAY,CAAC,WAAW,EAAEsE,QAAQ,GAAG,GAAG,GAAGX,EAAE,CAAC;MACxD;IACJ;EACJ;EACA,OAAOvJ,IAAI,YAAAoK,sBAAAlK,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqI,aAAa;EAAA;EAChH,OAAOpI,KAAK,kBA9mB6E1D,EAAE,CAAA2D,kBAAA;IAAAC,KAAA,EA8mBYkI,aAAa;IAAAjI,OAAA,EAAbiI,aAAa,CAAAvI,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC5I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhnB6F/D,EAAE,CAAAgE,iBAAA,CAgnBJ8H,aAAa,EAAc,CAAC;IAC3G7H,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAM8J,WAAW,CAAC;EACd5D,WAAW,GAAG/J,MAAM,CAACK,UAAU,CAAC;EAChCuN,cAAc,GAAG5N,MAAM,CAAC6L,aAAa,CAAC;EACtCgC,gBAAgB,GAAG7N,MAAM,CAACuB,eAAe,CAAC;EAC1CiE,OAAO,GAAGxF,MAAM,CAACG,MAAM,CAAC;EACxB;EACA,IAAIsM,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACqB,WAAW;EAC3B;EACA,IAAIrB,UAAUA,CAACrG,KAAK,EAAE;IAClB,IAAI,CAAC0H,WAAW,GAAG1H,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,WAAW,GAAGA,KAAK,GAAG,QAAQ;IAC9E,IAAI,IAAI,CAAC0H,WAAW,KAAK,KAAK,EAAE;MAC5B,IAAI,IAAI,CAACC,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACC,WAAW,CAAC,CAAC;QAChC,IAAI,CAACD,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MAC1B,IAAI,CAACA,aAAa,GAAG,IAAI,CAACvI,OAAO,CAACqB,iBAAiB,CAAC,MAAM;QACtD,OAAO,IAAI,CAACgH,gBAAgB,CAACI,OAAO,CAAC,IAAI,CAAClE,WAAW,CAAC,CAACmE,SAAS,CAAC,MAAM;UACnE;UACA,MAAMC,WAAW,GAAG,IAAI,CAACpE,WAAW,CAACM,aAAa,CAAC0C,WAAW;UAC9D;UACA;UACA,IAAIoB,WAAW,KAAK,IAAI,CAACC,sBAAsB,EAAE;YAC7C,IAAI,CAACR,cAAc,CAACtB,QAAQ,CAAC6B,WAAW,EAAE,IAAI,CAACL,WAAW,EAAE,IAAI,CAACpB,QAAQ,CAAC;YAC1E,IAAI,CAAC0B,sBAAsB,GAAGD,WAAW;UAC7C;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACAL,WAAW,GAAG,QAAQ;EACtB;EACApB,QAAQ;EACR0B,sBAAsB;EACtBL,aAAa;EACblM,WAAWA,CAAA,EAAG;IACV7B,MAAM,CAACmB,sBAAsB,CAAC,CAACuI,IAAI,CAACtI,qBAAqB,CAAC;EAC9D;EACAkJ,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACyD,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,WAAW,CAAC,CAAC;IACpC;EACJ;EACA,OAAO1K,IAAI,YAAA+K,oBAAA7K,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmK,WAAW;EAAA;EAC9G,OAAO5C,IAAI,kBAtqB8EhL,EAAE,CAAAiL,iBAAA;IAAAhH,IAAA,EAsqBJ2J,WAAW;IAAA1C,SAAA;IAAAC,MAAA;MAAAuB,UAAA;MAAAC,QAAA;IAAA;IAAAvB,QAAA;EAAA;AACtG;AACA;EAAA,QAAArH,SAAA,oBAAAA,SAAA,KAxqB6F/D,EAAE,CAAAgE,iBAAA,CAwqBJ4J,WAAW,EAAc,CAAC;IACzG3J,IAAI,EAAEzD,SAAS;IACf0D,IAAI,EAAE,CAAC;MACCqH,QAAQ,EAAE,eAAe;MACzBH,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEsB,UAAU,EAAE,CAAC;MACrDzI,IAAI,EAAExD,KAAK;MACXyD,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEyI,QAAQ,EAAE,CAAC;MACX1I,IAAI,EAAExD,KAAK;MACXyD,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIqK,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAACA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvDA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EAC3EA,gBAAgB,CAACA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AAC/E,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,wBAAwB,GAAG,kCAAkC;AACnE;AACA,MAAMC,mCAAmC,GAAG,0BAA0B;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3B9M,SAAS,GAAG5B,MAAM,CAACe,QAAQ,CAAC;EAC5B;AACJ;AACA;AACA;EACI4N,2BAA2B;EAC3BlJ,SAAS,GAAGzF,MAAM,CAACa,QAAQ,CAAC;EAC5B+N,uBAAuB;EACvB/M,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+M,uBAAuB,GAAG5O,MAAM,CAACsB,kBAAkB,CAAC,CACpD2M,OAAO,CAAC,yBAAyB,CAAC,CAClCC,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACS,2BAA2B,EAAE;QAClC,IAAI,CAACA,2BAA2B,GAAG,KAAK;QACxC,IAAI,CAACE,oCAAoC,CAAC,CAAC;MAC/C;IACJ,CAAC,CAAC;EACN;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAAClN,SAAS,CAACU,SAAS,EAAE;MAC3B,OAAOgM,gBAAgB,CAACS,IAAI;IAChC;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAG,IAAI,CAACvJ,SAAS,CAACsD,aAAa,CAAC,KAAK,CAAC;IACvDiG,WAAW,CAACC,KAAK,CAACC,eAAe,GAAG,YAAY;IAChDF,WAAW,CAACC,KAAK,CAACE,QAAQ,GAAG,UAAU;IACvC,IAAI,CAAC1J,SAAS,CAAC2H,IAAI,CAACC,WAAW,CAAC2B,WAAW,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMI,cAAc,GAAG,IAAI,CAAC3J,SAAS,CAACL,WAAW,IAAIlB,MAAM;IAC3D,MAAMmL,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAACjN,gBAAgB,GACjEiN,cAAc,CAACjN,gBAAgB,CAAC6M,WAAW,CAAC,GAC5C,IAAI;IACV,MAAMM,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAe,IAAK,EAAE,EAAEK,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAChGP,WAAW,CAACpI,MAAM,CAAC,CAAC;IACpB,QAAQ0I,aAAa;MACjB;MACA,KAAK,YAAY;MACjB;MACA,KAAK,eAAe;MACpB,KAAK,eAAe;QAChB,OAAOhB,gBAAgB,CAACkB,cAAc;MAC1C;MACA,KAAK,kBAAkB;MACvB;MACA,KAAK,kBAAkB;QACnB,OAAOlB,gBAAgB,CAACmB,cAAc;IAC9C;IACA,OAAOnB,gBAAgB,CAACS,IAAI;EAChC;EACAzE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsE,uBAAuB,CAACZ,WAAW,CAAC,CAAC;EAC9C;EACA;EACAa,oCAAoCA,CAAA,EAAG;IACnC,IAAI,CAAC,IAAI,CAACF,2BAA2B,IAAI,IAAI,CAAC/M,SAAS,CAACU,SAAS,IAAI,IAAI,CAACmD,SAAS,CAAC2H,IAAI,EAAE;MACtF,MAAMsC,WAAW,GAAG,IAAI,CAACjK,SAAS,CAAC2H,IAAI,CAACpE,SAAS;MACjD0G,WAAW,CAAC9I,MAAM,CAAC6H,mCAAmC,EAAEF,wBAAwB,EAAEC,wBAAwB,CAAC;MAC3G,IAAI,CAACG,2BAA2B,GAAG,IAAI;MACvC,MAAMgB,IAAI,GAAG,IAAI,CAACb,mBAAmB,CAAC,CAAC;MACvC,IAAIa,IAAI,KAAKrB,gBAAgB,CAACmB,cAAc,EAAE;QAC1CC,WAAW,CAACzG,GAAG,CAACwF,mCAAmC,EAAEF,wBAAwB,CAAC;MAClF,CAAC,MACI,IAAIoB,IAAI,KAAKrB,gBAAgB,CAACkB,cAAc,EAAE;QAC/CE,WAAW,CAACzG,GAAG,CAACwF,mCAAmC,EAAED,wBAAwB,CAAC;MAClF;IACJ;EACJ;EACA,OAAOlL,IAAI,YAAAsM,iCAAApM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFkL,wBAAwB;EAAA;EAC3H,OAAOjL,KAAK,kBAzxB6E1D,EAAE,CAAA2D,kBAAA;IAAAC,KAAA,EAyxBY+K,wBAAwB;IAAA9K,OAAA,EAAxB8K,wBAAwB,CAAApL,IAAA;IAAAO,UAAA,EAAc;EAAM;AACvJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3xB6F/D,EAAE,CAAAgE,iBAAA,CA2xBJ2K,wBAAwB,EAAc,CAAC;IACtH1K,IAAI,EAAE/D,UAAU;IAChBgE,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgM,UAAU,CAAC;EACbhO,WAAWA,CAAA,EAAG;IACV7B,MAAM,CAAC0O,wBAAwB,CAAC,CAACG,oCAAoC,CAAC,CAAC;EAC3E;EACA,OAAOvL,IAAI,YAAAwM,mBAAAtM,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqM,UAAU;EAAA;EAC7G,OAAOE,IAAI,kBAryB8EhQ,EAAE,CAAAiQ,gBAAA;IAAAhM,IAAA,EAqyBS6L,UAAU;IAAAI,OAAA,GAAYzO,eAAe,EAAEmM,WAAW,EAAE7D,YAAY,EAAElJ,eAAe;IAAAsP,OAAA,GAAavC,WAAW,EAAE7D,YAAY,EAAElJ,eAAe;EAAA;EAC5O,OAAOuP,IAAI,kBAtyB8EpQ,EAAE,CAAAqQ,gBAAA;IAAAH,OAAA,GAsyB+BzO,eAAe;EAAA;AAC7I;AACA;EAAA,QAAAsC,SAAA,oBAAAA,SAAA,KAxyB6F/D,EAAE,CAAAgE,iBAAA,CAwyBJ8L,UAAU,EAAc,CAAC;IACxG7L,IAAI,EAAEtD,QAAQ;IACduD,IAAI,EAAE,CAAC;MACCgM,OAAO,EAAE,CAACzO,eAAe,EAAEmM,WAAW,EAAE7D,YAAY,EAAElJ,eAAe,CAAC;MACtEsP,OAAO,EAAE,CAACvC,WAAW,EAAE7D,YAAY,EAAElJ,eAAe;IACxD,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASiP,UAAU,IAAIQ,CAAC,EAAEvG,YAAY,IAAInJ,CAAC,EAAE8I,gBAAgB,IAAI6G,CAAC,EAAE5B,wBAAwB,IAAI6B,CAAC,EAAE5O,oBAAoB,IAAI6O,CAAC,EAAE3E,aAAa,IAAI4E,CAAC,EAAEpL,SAAS,IAAIqL,CAAC,EAAEpC,gBAAgB,IAAIqC,CAAC,EAAElP,iBAAiB,IAAIT,CAAC,EAAE2M,WAAW,IAAIiD,CAAC,EAAEnF,4BAA4B,IAAIoF,CAAC,EAAEnF,oCAAoC,IAAIoF,CAAC,EAAEnF,8BAA8B,IAAIoF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}