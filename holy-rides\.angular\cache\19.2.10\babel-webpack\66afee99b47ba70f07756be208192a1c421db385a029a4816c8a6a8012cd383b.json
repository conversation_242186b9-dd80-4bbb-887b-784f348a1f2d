{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, ANIMATION_MODULE_TYPE, afterRender, ViewChild, Injector, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './icon-button-ImoriYmd.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './index-SYVYjXwK.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  _overlayRef;\n  /** The instance of the component making up the content of the snack bar. */\n  instance;\n  /**\n   * The instance of the component making up the content of the snack bar.\n   * @docs-private\n   */\n  containerInstance;\n  /** Subject for notifying the user that the snack bar has been dismissed. */\n  _afterDismissed = new Subject();\n  /** Subject for notifying the user that the snack bar has opened and appeared. */\n  _afterOpened = new Subject();\n  /** Subject for notifying the user that the snack bar action was called. */\n  _onAction = new Subject();\n  /**\n   * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n   * dismissed before the duration passes.\n   */\n  _durationTimeoutId;\n  /** Whether the snack bar was dismissed using the action button. */\n  _dismissedByAction = false;\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n  politeness = 'assertive';\n  /**\n   * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n   * component or template, the announcement message will default to the specified message.\n   */\n  announcementMessage = '';\n  /**\n   * The view container that serves as the parent for the snackbar for the purposes of dependency\n   * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n   */\n  viewContainerRef;\n  /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n  duration = 0;\n  /** Extra CSS classes to be added to the snack bar container. */\n  panelClass;\n  /** Text layout direction for the snack bar. */\n  direction;\n  /** Data being injected into the child component. */\n  data = null;\n  /** The horizontal position to place the snack bar. */\n  horizontalPosition = 'center';\n  /** The vertical position to place the snack bar. */\n  verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static ɵfac = function MatSnackBarLabel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarLabel)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarLabel,\n    selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static ɵfac = function MatSnackBarActions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarActions)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarActions,\n    selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static ɵfac = function MatSnackBarAction_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarAction)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSnackBarAction,\n    selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  snackBarRef = inject(MatSnackBarRef);\n  data = inject(MAT_SNACK_BAR_DATA);\n  constructor() {}\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static ɵfac = function SimpleSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SimpleSnackBar)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: SimpleSnackBar,\n    selectors: [[\"simple-snack-bar\"]],\n    hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n    exportAs: [\"matSnackBar\"],\n    decls: 3,\n    vars: 2,\n    consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n    template: function SimpleSnackBar_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.hasAction ? 2 : -1);\n      }\n    },\n    dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n    styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"]\n    }]\n  }], () => [], null);\n})();\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  _ngZone = inject(NgZone);\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _platform = inject(Platform);\n  _rendersRef;\n  _animationsDisabled = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations';\n  snackBarConfig = inject(MatSnackBarConfig);\n  _document = inject(DOCUMENT);\n  _trackedModals = new Set();\n  _enterFallback;\n  _exitFallback;\n  _renders = new Subject();\n  /** The number of milliseconds to wait before announcing the snack bar's content. */\n  _announceDelay = 150;\n  /** The timeout for announcing the snack bar's content. */\n  _announceTimeoutId;\n  /** Whether the component has been destroyed. */\n  _destroyed = false;\n  /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n  _portalOutlet;\n  /** Subject for notifying that the snack bar has announced to screen readers. */\n  _onAnnounce = new Subject();\n  /** Subject for notifying that the snack bar has exited from view. */\n  _onExit = new Subject();\n  /** Subject for notifying that the snack bar has finished entering the view. */\n  _onEnter = new Subject();\n  /** The state of the snack bar animations. */\n  _animationState = 'void';\n  /** aria-live value for the live region. */\n  _live;\n  /**\n   * Element that will have the `mdc-snackbar__label` class applied if the attached component\n   * or template does not have it. This ensures that the appropriate structure, typography, and\n   * color is applied to the attached view.\n   */\n  _label;\n  /**\n   * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n   * JAWS does not read out aria-live message.\n   */\n  _role;\n  /** Unique ID of the aria-live element. */\n  _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n  constructor() {\n    super();\n    const config = this.snackBarConfig;\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (config.politeness === 'assertive' && !config.announcementMessage) {\n      this._live = 'assertive';\n    } else if (config.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n    // Note: ideally we'd just do an `afterNextRender` in the places where we need to delay\n    // something, however in some cases (TestBed teardown) the injector can be destroyed at an\n    // unexpected time, causing the `afterRender` to fail.\n    this._rendersRef = afterRender(() => this._renders.next(), {\n      manualCleanup: true\n    });\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the snack bar container.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  };\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(animationName) {\n    if (animationName === EXIT_ANIMATION) {\n      this._completeExit();\n    } else if (animationName === ENTER_ANIMATION) {\n      clearTimeout(this._enterFallback);\n      this._ngZone.run(() => {\n        this._onEnter.next();\n        this._onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n      if (this._animationsDisabled) {\n        this._renders.pipe(take(1)).subscribe(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n        });\n      } else {\n        clearTimeout(this._enterFallback);\n        this._enterFallback = setTimeout(() => {\n          // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n          // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n          this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n          this.onAnimationEnd(ENTER_ANIMATION);\n        }, 200);\n      }\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    if (this._destroyed) {\n      return of(undefined);\n    }\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n      if (this._animationsDisabled) {\n        this._renders.pipe(take(1)).subscribe(() => {\n          this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n        });\n      } else {\n        clearTimeout(this._exitFallback);\n        this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n      }\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n    this._renders.complete();\n    this._rendersRef.destroy();\n  }\n  _completeExit() {\n    clearTimeout(this._exitFallback);\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (this._announceTimeoutId) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._announceTimeoutId = setTimeout(() => {\n        if (this._destroyed) {\n          return;\n        }\n        const element = this._elementRef.nativeElement;\n        const inertElement = element.querySelector('[aria-hidden]');\n        const liveElement = element.querySelector('[aria-live]');\n        if (inertElement && liveElement) {\n          // If an element in the snack bar content is focused before being moved\n          // track it and restore focus after moving to the live region.\n          let focusedElement = null;\n          if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n            focusedElement = document.activeElement;\n          }\n          inertElement.removeAttribute('aria-hidden');\n          liveElement.appendChild(inertElement);\n          focusedElement?.focus();\n          this._onAnnounce.next();\n          this._onAnnounce.complete();\n        }\n      }, this._announceDelay);\n    });\n  }\n  static ɵfac = function MatSnackBarContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSnackBarContainer,\n    selectors: [[\"mat-snack-bar-container\"]],\n    viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\"],\n    hostVars: 6,\n    hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"animationend\", function MatSnackBarContainer_animationend_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        })(\"animationcancel\", function MatSnackBarContainer_animationcancel_HostBindingHandler($event) {\n          return ctx.onAnimationEnd($event.animationName);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-snack-bar-container-enter\", ctx._animationState === \"visible\")(\"mat-snack-bar-container-exit\", ctx._animationState === \"hidden\")(\"mat-snack-bar-container-animations-enabled\", !ctx._animationsDisabled);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 3,\n    consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\", \"mat-mdc-snackbar-surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n    template: function MatSnackBarContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n        i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(5, \"div\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n        '(animationend)': 'onAnimationEnd($event.animationName)',\n        '(animationcancel)': 'onAnimationEnd($event.animationName)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  _overlay = inject(Overlay);\n  _live = inject(LiveAnnouncer);\n  _injector = inject(Injector);\n  _breakpointObserver = inject(BreakpointObserver);\n  _parentSnackBar = inject(MatSnackBar, {\n    optional: true,\n    skipSelf: true\n  });\n  _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n  /**\n   * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n   * If there is a parent snack-bar service, all operations should delegate to that parent\n   * via `_openedSnackBarRef`.\n   */\n  _snackBarRefAtThisLevel = null;\n  /** The component that should be rendered as the snack bar's simple component. */\n  simpleSnackBarComponent = SimpleSnackBar;\n  /** The container component that attaches the provided template or component. */\n  snackBarContainerComponent = MatSnackBarContainer;\n  /** The CSS class to apply for handset mode. */\n  handsetCssClass = 'mat-mdc-snack-bar-handset';\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor() {}\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static ɵfac = function MatSnackBar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBar)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatSnackBar,\n    factory: MatSnackBar.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n  static ɵfac = function MatSnackBarModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatSnackBarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSnackBarModule,\n    imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n    exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MatSnackBar],\n    imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n  // Represents\n  // trigger('state', [\n  //   state(\n  //     'void, hidden',\n  //     style({\n  //       transform: 'scale(0.8)',\n  //       opacity: 0,\n  //     }),\n  //   ),\n  //   state(\n  //     'visible',\n  //     style({\n  //       transform: 'scale(1)',\n  //       opacity: 1,\n  //     }),\n  //   ),\n  //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n  //   transition(\n  //     '* => void, * => hidden',\n  //     animate(\n  //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n  //       style({\n  //         opacity: 0,\n  //       }),\n  //     ),\n  //   ),\n  // ])\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: {\n    type: 7,\n    name: 'state',\n    'definitions': [{\n      type: 0,\n      name: 'void, hidden',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(0.8)',\n          opacity: 0\n        },\n        offset: null\n      }\n    }, {\n      type: 0,\n      name: 'visible',\n      styles: {\n        type: 6,\n        styles: {\n          transform: 'scale(1)',\n          opacity: 1\n        },\n        offset: null\n      }\n    }, {\n      type: 1,\n      expr: '* => visible',\n      animation: {\n        type: 4,\n        styles: null,\n        timings: '150ms cubic-bezier(0, 0, 0.2, 1)'\n      },\n      options: null\n    }, {\n      type: 1,\n      expr: '* => void, * => hidden',\n      animation: {\n        type: 4,\n        styles: {\n          type: 6,\n          styles: {\n            opacity: 0\n          },\n          offset: null\n        },\n        timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)'\n      },\n      options: null\n    }],\n    options: {}\n  }\n};\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "NgZone", "ElementRef", "ChangeDetectorRef", "ANIMATION_MODULE_TYPE", "afterRender", "ViewChild", "Injector", "TemplateRef", "Injectable", "NgModule", "Subject", "of", "MatButton", "MatButtonModule", "DOCUMENT", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "_IdGenerator", "LiveAnnouncer", "Platform", "take", "takeUntil", "BreakpointObserver", "Breakpoints", "Overlay", "OverlayConfig", "OverlayModule", "M", "MatCommonModule", "SimpleSnackBar_Conditional_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SimpleSnackBar_Conditional_2_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "MatSnackBarContainer_ng_template_4_Template", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "_overlayRef", "instance", "containerInstance", "_afterDismissed", "_afterOpened", "_onAction", "_durationTimeoutId", "_dismissedByAction", "constructor", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "viewContainerRef", "panelClass", "direction", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "MatSnackBarLabel_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatSnackBarActions", "MatSnackBarActions_Factory", "MatSnackBarAction", "MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "SimpleSnackBar_Factory", "ɵcmp", "ɵɵdefineComponent", "exportAs", "decls", "vars", "consts", "template", "SimpleSnackBar_Template", "ɵɵtemplate", "message", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "ENTER_ANIMATION", "EXIT_ANIMATION", "MatSnackBarContainer", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "_rendersRef", "_animationsDisabled", "optional", "snackBarConfig", "_document", "_trackedModals", "Set", "_enterFallback", "_exitFallback", "_renders", "_announce<PERSON><PERSON>y", "_announceTimeoutId", "_destroyed", "_portalOutlet", "_onAnnounce", "_animationState", "_live", "_label", "_role", "_liveElementId", "getId", "config", "FIREFOX", "manualCleanup", "attachComponentPortal", "portal", "_assertNotAttached", "result", "_afterPortalAttached", "attachTemplatePortal", "attachDomPortal", "onAnimationEnd", "animationName", "_completeExit", "run", "enter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_screenReaderAnnounce", "pipe", "queueMicrotask", "nativeElement", "classList", "add", "undefined", "setAttribute", "ngOnDestroy", "_clearFromModals", "destroy", "element", "panelClasses", "Array", "isArray", "for<PERSON>ach", "cssClass", "_exposeToModals", "label", "labelClass", "toggle", "querySelector", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "MatSnackBarContainer_Factory", "viewQuery", "MatSnackBarContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "MatSnackBarContainer_HostBindings", "MatSnackBarContainer_animationend_HostBindingHandler", "$event", "MatSnackBarContainer_animationcancel_HostBindingHandler", "ɵɵclassProp", "features", "ɵɵInheritDefinitionFeature", "MatSnackBarContainer_Template", "ɵɵelement", "ɵɵattribute", "<PERSON><PERSON><PERSON>", "static", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "MatSnackBar", "_overlay", "_injector", "_breakpointObserver", "_parentSnackBar", "skipSelf", "_defaultConfig", "_snackBarRefAtThisLevel", "simpleSnackBarComponent", "snackBarContainerComponent", "handsetCssClass", "_openedSnackBarRef", "parent", "value", "openFromComponent", "component", "_attach", "openFromTemplate", "open", "_config", "_attachSnackBarContainer", "overlayRef", "userInjector", "injector", "create", "providers", "provide", "useValue", "containerPortal", "containerRef", "attach", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "detachments", "state", "overlayElement", "matches", "announce", "_animateSnackBar", "overlayConfig", "positionStrategy", "position", "global", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "MatSnackBar_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "DIRECTIVES", "MatSnackBarModule", "MatSnackBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector", "matSnackBarAnimations", "snackBarState", "name", "transform", "opacity", "offset", "expr", "animation", "timings", "options"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, inject, Component, ViewEncapsulation, ChangeDetectionStrategy, NgZone, ElementRef, ChangeDetectorRef, ANIMATION_MODULE_TYPE, afterRender, ViewChild, Injector, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { MatButton, MatButtonModule } from './button.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { _IdGenerator, LiveAnnouncer } from '@angular/cdk/a11y';\nimport { Platform } from '@angular/cdk/platform';\nimport { take, takeUntil } from 'rxjs/operators';\nimport { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport './icon-button-ImoriYmd.mjs';\nimport '@angular/cdk/private';\nimport './ripple-loader-Ce3DAhPW.mjs';\nimport './ripple-BT3tzh6F.mjs';\nimport '@angular/cdk/coercion';\nimport './structural-styles-BQUT6wsL.mjs';\nimport './index-SYVYjXwK.mjs';\nimport '@angular/cdk/bidi';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    _overlayRef;\n    /** The instance of the component making up the content of the snack bar. */\n    instance;\n    /**\n     * The instance of the component making up the content of the snack bar.\n     * @docs-private\n     */\n    containerInstance;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    _afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    _afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    _onAction = new Subject();\n    /**\n     * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n     * dismissed before the duration passes.\n     */\n    _durationTimeoutId;\n    /** Whether the snack bar was dismissed using the action button. */\n    _dismissedByAction = false;\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    announcementMessage = '';\n    /**\n     * The view container that serves as the parent for the snackbar for the purposes of dependency\n     * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n     */\n    viewContainerRef;\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    duration = 0;\n    /** Extra CSS classes to be added to the snack bar container. */\n    panelClass;\n    /** Text layout direction for the snack bar. */\n    direction;\n    /** Data being injected into the child component. */\n    data = null;\n    /** The horizontal position to place the snack bar. */\n    horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    verticalPosition = 'bottom';\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarLabel, isStandalone: true, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarActions, isStandalone: true, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarAction, isStandalone: true, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    snackBarRef = inject(MatSnackBarRef);\n    data = inject(MAT_SNACK_BAR_DATA);\n    constructor() { }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SimpleSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: SimpleSnackBar, isStandalone: true, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"], dependencies: [{ kind: \"component\", type: MatButton, selector: \"    button[mat-button], button[mat-raised-button], button[mat-flat-button],    button[mat-stroked-button]  \", exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\\n\"] }]\n        }], ctorParameters: () => [] });\n\nconst ENTER_ANIMATION = '_mat-snack-bar-enter';\nconst EXIT_ANIMATION = '_mat-snack-bar-exit';\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n    _ngZone = inject(NgZone);\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _platform = inject(Platform);\n    _rendersRef;\n    _animationsDisabled = inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations';\n    snackBarConfig = inject(MatSnackBarConfig);\n    _document = inject(DOCUMENT);\n    _trackedModals = new Set();\n    _enterFallback;\n    _exitFallback;\n    _renders = new Subject();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    _announceDelay = 150;\n    /** The timeout for announcing the snack bar's content. */\n    _announceTimeoutId;\n    /** Whether the component has been destroyed. */\n    _destroyed = false;\n    /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n    _portalOutlet;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    _onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    _onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    _onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    _animationState = 'void';\n    /** aria-live value for the live region. */\n    _live;\n    /**\n     * Element that will have the `mdc-snackbar__label` class applied if the attached component\n     * or template does not have it. This ensures that the appropriate structure, typography, and\n     * color is applied to the attached view.\n     */\n    _label;\n    /**\n     * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n     * JAWS does not read out aria-live message.\n     */\n    _role;\n    /** Unique ID of the aria-live element. */\n    _liveElementId = inject(_IdGenerator).getId('mat-snack-bar-container-live-');\n    constructor() {\n        super();\n        const config = this.snackBarConfig;\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (config.politeness === 'assertive' && !config.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (config.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n        // Note: ideally we'd just do an `afterNextRender` in the places where we need to delay\n        // something, however in some cases (TestBed teardown) the injector can be destroyed at an\n        // unexpected time, causing the `afterRender` to fail.\n        this._rendersRef = afterRender(() => this._renders.next(), { manualCleanup: true });\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    };\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(animationName) {\n        if (animationName === EXIT_ANIMATION) {\n            this._completeExit();\n        }\n        else if (animationName === ENTER_ANIMATION) {\n            clearTimeout(this._enterFallback);\n            this._ngZone.run(() => {\n                this._onEnter.next();\n                this._onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n            // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n            this._changeDetectorRef.markForCheck();\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n            if (this._animationsDisabled) {\n                this._renders.pipe(take(1)).subscribe(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(ENTER_ANIMATION)));\n                });\n            }\n            else {\n                clearTimeout(this._enterFallback);\n                this._enterFallback = setTimeout(() => {\n                    // The snack bar will stay invisible if it fails to animate. Add a fallback class so it\n                    // becomes visible. This can happen in some apps that do `* {animation: none !important}`.\n                    this._elementRef.nativeElement.classList.add('mat-snack-bar-fallback-visible');\n                    this.onAnimationEnd(ENTER_ANIMATION);\n                }, 200);\n            }\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        if (this._destroyed) {\n            return of(undefined);\n        }\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            this._changeDetectorRef.markForCheck();\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n            if (this._animationsDisabled) {\n                this._renders.pipe(take(1)).subscribe(() => {\n                    this._ngZone.run(() => queueMicrotask(() => this.onAnimationEnd(EXIT_ANIMATION)));\n                });\n            }\n            else {\n                clearTimeout(this._exitFallback);\n                this._exitFallback = setTimeout(() => this.onAnimationEnd(EXIT_ANIMATION), 200);\n            }\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n        this._renders.complete();\n        this._rendersRef.destroy();\n    }\n    _completeExit() {\n        clearTimeout(this._exitFallback);\n        queueMicrotask(() => {\n            this._onExit.next();\n            this._onExit.complete();\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (this._announceTimeoutId) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._announceTimeoutId = setTimeout(() => {\n                if (this._destroyed) {\n                    return;\n                }\n                const element = this._elementRef.nativeElement;\n                const inertElement = element.querySelector('[aria-hidden]');\n                const liveElement = element.querySelector('[aria-live]');\n                if (inertElement && liveElement) {\n                    // If an element in the snack bar content is focused before being moved\n                    // track it and restore focus after moving to the live region.\n                    let focusedElement = null;\n                    if (this._platform.isBrowser &&\n                        document.activeElement instanceof HTMLElement &&\n                        inertElement.contains(document.activeElement)) {\n                        focusedElement = document.activeElement;\n                    }\n                    inertElement.removeAttribute('aria-hidden');\n                    liveElement.appendChild(inertElement);\n                    focusedElement?.focus();\n                    this._onAnnounce.next();\n                    this._onAnnounce.complete();\n                }\n            }, this._announceDelay);\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatSnackBarContainer, isStandalone: true, selector: \"mat-snack-bar-container\", host: { listeners: { \"animationend\": \"onAnimationEnd($event.animationName)\", \"animationcancel\": \"onAnimationEnd($event.animationName)\" }, properties: { \"class.mat-snack-bar-container-enter\": \"_animationState === \\\"visible\\\"\", \"class.mat-snack-bar-container-exit\": \"_animationState === \\\"hidden\\\"\", \"class.mat-snack-bar-container-animations-enabled\": \"!_animationsDisabled\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, imports: [CdkPortalOutlet], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container',\n                        '[class.mat-snack-bar-container-enter]': '_animationState === \"visible\"',\n                        '[class.mat-snack-bar-container-exit]': '_animationState === \"hidden\"',\n                        '[class.mat-snack-bar-container-animations-enabled]': '!_animationsDisabled',\n                        '(animationend)': 'onAnimationEnd($event.animationName)',\n                        '(animationcancel)': 'onAnimationEnd($event.animationName)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface mat-mdc-snackbar-surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\"@keyframes _mat-snack-bar-enter{from{transform:scale(0.8);opacity:0}to{transform:scale(1);opacity:1}}@keyframes _mat-snack-bar-exit{from{opacity:1}to{opacity:0}}.mat-mdc-snack-bar-container{display:flex;align-items:center;justify-content:center;box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);margin:8px}.mat-mdc-snack-bar-handset .mat-mdc-snack-bar-container{width:100vw}.mat-snack-bar-container-animations-enabled{opacity:0}.mat-snack-bar-container-animations-enabled.mat-snack-bar-fallback-visible{opacity:1}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-enter{animation:_mat-snack-bar-enter 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-snack-bar-container-animations-enabled.mat-snack-bar-container-exit{animation:_mat-snack-bar-exit 75ms cubic-bezier(0.4, 0, 1, 1) forwards}.mat-mdc-snackbar-surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12);display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;padding-left:0;padding-right:8px}[dir=rtl] .mat-mdc-snackbar-surface{padding-right:0;padding-left:8px}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{min-width:344px;max-width:672px}.mat-mdc-snack-bar-handset .mat-mdc-snackbar-surface{width:100%;min-width:0}@media(forced-colors: active){.mat-mdc-snackbar-surface{outline:solid 1px}}.mat-mdc-snack-bar-container .mat-mdc-snackbar-surface{color:var(--mdc-snackbar-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-snackbar-container-shape, var(--mat-sys-corner-extra-small));background-color:var(--mdc-snackbar-container-color, var(--mat-sys-inverse-surface))}.mdc-snackbar__label{width:100%;flex-grow:1;box-sizing:border-box;margin:0;padding:14px 8px 14px 16px}[dir=rtl] .mdc-snackbar__label{padding-left:8px;padding-right:16px}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-family:var(--mdc-snackbar-supporting-text-font, var(--mat-sys-body-medium-font));font-size:var(--mdc-snackbar-supporting-text-size, var(--mat-sys-body-medium-size));font-weight:var(--mdc-snackbar-supporting-text-weight, var(--mat-sys-body-medium-weight));line-height:var(--mdc-snackbar-supporting-text-line-height, var(--mat-sys-body-medium-line-height))}.mat-mdc-snack-bar-actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled).mat-unthemed{color:var(--mat-snack-bar-button-color, var(--mat-sys-inverse-primary))}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }], _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n    _overlay = inject(Overlay);\n    _live = inject(LiveAnnouncer);\n    _injector = inject(Injector);\n    _breakpointObserver = inject(BreakpointObserver);\n    _parentSnackBar = inject(MatSnackBar, { optional: true, skipSelf: true });\n    _defaultConfig = inject(MAT_SNACK_BAR_DEFAULT_OPTIONS);\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    _snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    handsetCssClass = 'mat-mdc-snack-bar-handset';\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor() { }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        let positionStrategy = this._overlay.position().global();\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, providers: [MatSnackBar], imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        PortalModule,\n                        MatButtonModule,\n                        MatCommonModule,\n                        SimpleSnackBar,\n                        ...DIRECTIVES,\n                    ],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatSnackBar],\n                }]\n        }] });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSnackBarAnimations = {\n    // Represents\n    // trigger('state', [\n    //   state(\n    //     'void, hidden',\n    //     style({\n    //       transform: 'scale(0.8)',\n    //       opacity: 0,\n    //     }),\n    //   ),\n    //   state(\n    //     'visible',\n    //     style({\n    //       transform: 'scale(1)',\n    //       opacity: 1,\n    //     }),\n    //   ),\n    //   transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    //   transition(\n    //     '* => void, * => hidden',\n    //     animate(\n    //       '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n    //       style({\n    //         opacity: 0,\n    //       }),\n    //     ),\n    //   ),\n    // ])\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: {\n        type: 7,\n        name: 'state',\n        'definitions': [\n            {\n                type: 0,\n                name: 'void, hidden',\n                styles: { type: 6, styles: { transform: 'scale(0.8)', opacity: 0 }, offset: null },\n            },\n            {\n                type: 0,\n                name: 'visible',\n                styles: { type: 6, styles: { transform: 'scale(1)', opacity: 1 }, offset: null },\n            },\n            {\n                type: 1,\n                expr: '* => visible',\n                animation: { type: 4, styles: null, timings: '150ms cubic-bezier(0, 0, 0.2, 1)' },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void, * => hidden',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC3P,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,eAAe,QAAQ,cAAc;AACzD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,YAAY,EAAEC,aAAa,QAAQ,mBAAmB;AAC/D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AAChD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,qBAAqB;AACrE,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,OAAO,4BAA4B;AACnC,OAAO,sBAAsB;AAC7B,OAAO,8BAA8B;AACrC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,kCAAkC;AACzC,OAAO,sBAAsB;AAC7B,OAAO,mBAAmB;;AAE1B;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgI6F1C,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,YAsD0Q,CAAC,eAA+D,CAAC;IAtD7U5C,EAAE,CAAA6C,UAAA,mBAAAC,8DAAA;MAAF9C,EAAE,CAAA+C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAFhD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkD,WAAA,CAsDgUF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IAtD5UnD,EAAE,CAAAoD,MAAA,EAsDuW,CAAC;IAtD1WpD,EAAE,CAAAqD,YAAA,CAsDgX,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GAtD7XhD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAsD,SAAA,EAsDuW,CAAC;IAtD1WtD,EAAE,CAAAuD,kBAAA,MAAAP,MAAA,CAAAQ,IAAA,CAAAL,MAAA,KAsDuW,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,SAAAC,4CAAAlB,EAAA,EAAAC,GAAA;AArLvc,MAAMkB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAW;EACX;EACAC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;EACAC,eAAe,GAAG,IAAIjD,OAAO,CAAC,CAAC;EAC/B;EACAkD,YAAY,GAAG,IAAIlD,OAAO,CAAC,CAAC;EAC5B;EACAmD,SAAS,GAAG,IAAInD,OAAO,CAAC,CAAC;EACzB;AACJ;AACA;AACA;EACIoD,kBAAkB;EAClB;EACAC,kBAAkB,GAAG,KAAK;EAC1BC,WAAWA,CAACN,iBAAiB,EAAEF,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACO,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACT,eAAe,CAACU,MAAM,EAAE;MAC9B,IAAI,CAACX,iBAAiB,CAACY,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACX,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACN,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACF,SAAS,CAACY,IAAI,CAAC,CAAC;MACrB,IAAI,CAACZ,SAAS,CAACa,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACN,OAAO,CAAC,CAAC;IAClB;IACAG,YAAY,CAAC,IAAI,CAACT,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACIa,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAI,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACf,kBAAkB,GAAGgB,UAAU,CAAC,MAAM,IAAI,CAACV,OAAO,CAAC,CAAC,EAAEf,IAAI,CAAC0B,GAAG,CAACF,QAAQ,EAAEzB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA4B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACpB,YAAY,CAACS,MAAM,EAAE;MAC3B,IAAI,CAACT,YAAY,CAACa,IAAI,CAAC,CAAC;MACxB,IAAI,CAACb,YAAY,CAACc,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAP,cAAcA,CAAA,EAAG;IACb,IAAI,CAACX,WAAW,CAACyB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACQ,MAAM,EAAE;MACxB,IAAI,CAACR,SAAS,CAACa,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACf,eAAe,CAACc,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACJ,eAAe,CAACe,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxB,eAAe;EAC/B;EACA;EACAyB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAM0B,kBAAkB,GAAG,IAAI7F,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM8F,iBAAiB,CAAC;EACpB;EACAC,UAAU,GAAG,WAAW;EACxB;AACJ;AACA;AACA;EACIC,mBAAmB,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACIC,gBAAgB;EAChB;EACAd,QAAQ,GAAG,CAAC;EACZ;EACAe,UAAU;EACV;EACAC,SAAS;EACT;EACA5C,IAAI,GAAG,IAAI;EACX;EACA6C,kBAAkB,GAAG,QAAQ;EAC7B;EACAC,gBAAgB,GAAG,QAAQ;AAC/B;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnB,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFH,gBAAgB;EAAA;EACnH,OAAOI,IAAI,kBAD8E3G,EAAE,CAAA4G,iBAAA;IAAAC,IAAA,EACJN,gBAAgB;IAAAO,SAAA;IAAAC,SAAA;EAAA;AAC3G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FhH,EAAE,CAAAiH,iBAAA,CAGJV,gBAAgB,EAAc,CAAC;IAC9GM,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;EACrB,OAAOb,IAAI,YAAAc,2BAAAZ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFW,kBAAkB;EAAA;EACrH,OAAOV,IAAI,kBAf8E3G,EAAE,CAAA4G,iBAAA;IAAAC,IAAA,EAeJQ,kBAAkB;IAAAP,SAAA;IAAAC,SAAA;EAAA;AAC7G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjB6FhH,EAAE,CAAAiH,iBAAA,CAiBJI,kBAAkB,EAAc,CAAC;IAChHR,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMG,iBAAiB,CAAC;EACpB,OAAOf,IAAI,YAAAgB,0BAAAd,iBAAA;IAAA,YAAAA,iBAAA,IAAwFa,iBAAiB;EAAA;EACpH,OAAOZ,IAAI,kBA7B8E3G,EAAE,CAAA4G,iBAAA;IAAAC,IAAA,EA6BJU,iBAAiB;IAAAT,SAAA;IAAAC,SAAA;EAAA;AAC5G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/B6FhH,EAAE,CAAAiH,iBAAA,CA+BJM,iBAAiB,EAAc,CAAC;IAC/GV,IAAI,EAAE3G,SAAS;IACfgH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,cAAc,CAAC;EACjBC,WAAW,GAAGvH,MAAM,CAAC2D,cAAc,CAAC;EACpCN,IAAI,GAAGrD,MAAM,CAAC2F,kBAAkB,CAAC;EACjCvB,WAAWA,CAAA,EAAG,CAAE;EAChB;EACApB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACuE,WAAW,CAAC3C,iBAAiB,CAAC,CAAC;EACxC;EACA;EACA,IAAI4C,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAACnE,IAAI,CAACL,MAAM;EAC7B;EACA,OAAOqD,IAAI,YAAAoB,uBAAAlB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFe,cAAc;EAAA;EACjH,OAAOI,IAAI,kBAtD8E7H,EAAE,CAAA8H,iBAAA;IAAAjB,IAAA,EAsDJY,cAAc;IAAAX,SAAA;IAAAC,SAAA;IAAAgB,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAA5F,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAtDZxC,EAAE,CAAA4C,cAAA,YAsD6L,CAAC;QAtDhM5C,EAAE,CAAAoD,MAAA,EAsDmN,CAAC;QAtDtNpD,EAAE,CAAAqD,YAAA,CAsDyN,CAAC;QAtD5NrD,EAAE,CAAAqI,UAAA,IAAA9F,qCAAA,gBAsD8O,CAAC;MAAA;MAAA,IAAAC,EAAA;QAtDjPxC,EAAE,CAAAsD,SAAA,CAsDmN,CAAC;QAtDtNtD,EAAE,CAAAuD,kBAAA,MAAAd,GAAA,CAAAe,IAAA,CAAA8E,OAAA,MAsDmN,CAAC;QAtDtNtI,EAAE,CAAAsD,SAAA,CAsD6X,CAAC;QAtDhYtD,EAAE,CAAAuI,aAAA,CAAA9F,GAAA,CAAAkF,SAAA,SAsD6X,CAAC;MAAA;IAAA;IAAAa,YAAA,GAAsGrH,SAAS,EAAiLoF,gBAAgB,EAA+Dc,kBAAkB,EAAiEE,iBAAiB;IAAAkB,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACh7B;AACA;EAAA,QAAA3B,SAAA,oBAAAA,SAAA,KAxD6FhH,EAAE,CAAAiH,iBAAA,CAwDJQ,cAAc,EAAc,CAAC;IAC5GZ,IAAI,EAAEzG,SAAS;IACf8G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEY,QAAQ,EAAE,aAAa;MAAEW,aAAa,EAAErI,iBAAiB,CAACuI,IAAI;MAAED,eAAe,EAAErI,uBAAuB,CAACuI,MAAM;MAAEC,OAAO,EAAE,CAAC3H,SAAS,EAAEoF,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB,CAAC;MAAEH,IAAI,EAAE;QACjO,OAAO,EAAE;MACb,CAAC;MAAEe,QAAQ,EAAE,0NAA0N;MAAEM,MAAM,EAAE,CAAC,2CAA2C;IAAE,CAAC;EAC5S,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMM,eAAe,GAAG,sBAAsB;AAC9C,MAAMC,cAAc,GAAG,qBAAqB;AAC5C;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAAS3H,gBAAgB,CAAC;EAChD4H,OAAO,GAAG/I,MAAM,CAACI,MAAM,CAAC;EACxB4I,WAAW,GAAGhJ,MAAM,CAACK,UAAU,CAAC;EAChC4I,kBAAkB,GAAGjJ,MAAM,CAACM,iBAAiB,CAAC;EAC9C4I,SAAS,GAAGlJ,MAAM,CAAC0B,QAAQ,CAAC;EAC5ByH,WAAW;EACXC,mBAAmB,GAAGpJ,MAAM,CAACO,qBAAqB,EAAE;IAAE8I,QAAQ,EAAE;EAAK,CAAC,CAAC,KAAK,gBAAgB;EAC5FC,cAAc,GAAGtJ,MAAM,CAAC4F,iBAAiB,CAAC;EAC1C2D,SAAS,GAAGvJ,MAAM,CAACkB,QAAQ,CAAC;EAC5BsI,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1BC,cAAc;EACdC,aAAa;EACbC,QAAQ,GAAG,IAAI9I,OAAO,CAAC,CAAC;EACxB;EACA+I,cAAc,GAAG,GAAG;EACpB;EACAC,kBAAkB;EAClB;EACAC,UAAU,GAAG,KAAK;EAClB;EACAC,aAAa;EACb;EACAC,WAAW,GAAG,IAAInJ,OAAO,CAAC,CAAC;EAC3B;EACAuD,OAAO,GAAG,IAAIvD,OAAO,CAAC,CAAC;EACvB;EACA2E,QAAQ,GAAG,IAAI3E,OAAO,CAAC,CAAC;EACxB;EACAoJ,eAAe,GAAG,MAAM;EACxB;EACAC,KAAK;EACL;AACJ;AACA;AACA;AACA;EACIC,MAAM;EACN;AACJ;AACA;AACA;EACIC,KAAK;EACL;EACAC,cAAc,GAAGtK,MAAM,CAACwB,YAAY,CAAC,CAAC+I,KAAK,CAAC,+BAA+B,CAAC;EAC5EnG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAMoG,MAAM,GAAG,IAAI,CAAClB,cAAc;IAClC;IACA;IACA,IAAIkB,MAAM,CAAC3E,UAAU,KAAK,WAAW,IAAI,CAAC2E,MAAM,CAAC1E,mBAAmB,EAAE;MAClE,IAAI,CAACqE,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIK,MAAM,CAAC3E,UAAU,KAAK,KAAK,EAAE;MAClC,IAAI,CAACsE,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAACjB,SAAS,CAACuB,OAAO,EAAE;MACxB,IAAI,IAAI,CAACN,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;MACxB;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAClB,WAAW,GAAG3I,WAAW,CAAC,MAAM,IAAI,CAACoJ,QAAQ,CAAC/E,IAAI,CAAC,CAAC,EAAE;MAAE6F,aAAa,EAAE;IAAK,CAAC,CAAC;EACvF;EACA;EACAC,qBAAqBA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACd,aAAa,CAACW,qBAAqB,CAACC,MAAM,CAAC;IAC/D,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB;EACA;EACAE,oBAAoBA,CAACJ,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACd,aAAa,CAACgB,oBAAoB,CAACJ,MAAM,CAAC;IAC9D,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIG,eAAe,GAAIL,MAAM,IAAK;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACd,aAAa,CAACiB,eAAe,CAACL,MAAM,CAAC;IACzD,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC3B,OAAOD,MAAM;EACjB,CAAC;EACD;EACAI,cAAcA,CAACC,aAAa,EAAE;IAC1B,IAAIA,aAAa,KAAKtC,cAAc,EAAE;MAClC,IAAI,CAACuC,aAAa,CAAC,CAAC;IACxB,CAAC,MACI,IAAID,aAAa,KAAKvC,eAAe,EAAE;MACxCjE,YAAY,CAAC,IAAI,CAAC+E,cAAc,CAAC;MACjC,IAAI,CAACX,OAAO,CAACsC,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC5F,QAAQ,CAACZ,IAAI,CAAC,CAAC;QACpB,IAAI,CAACY,QAAQ,CAACX,QAAQ,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA;EACAwG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACvB,UAAU,EAAE;MAClB,IAAI,CAACG,eAAe,GAAG,SAAS;MAChC;MACA;MACA,IAAI,CAACjB,kBAAkB,CAACsC,YAAY,CAAC,CAAC;MACtC,IAAI,CAACtC,kBAAkB,CAACuC,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACrC,mBAAmB,EAAE;QAC1B,IAAI,CAACQ,QAAQ,CAAC8B,IAAI,CAAC/J,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC2C,SAAS,CAAC,MAAM;UACxC,IAAI,CAACyE,OAAO,CAACsC,GAAG,CAAC,MAAMM,cAAc,CAAC,MAAM,IAAI,CAACT,cAAc,CAACtC,eAAe,CAAC,CAAC,CAAC;QACtF,CAAC,CAAC;MACN,CAAC,MACI;QACDjE,YAAY,CAAC,IAAI,CAAC+E,cAAc,CAAC;QACjC,IAAI,CAACA,cAAc,GAAGxE,UAAU,CAAC,MAAM;UACnC;UACA;UACA,IAAI,CAAC8D,WAAW,CAAC4C,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC9E,IAAI,CAACZ,cAAc,CAACtC,eAAe,CAAC;QACxC,CAAC,EAAE,GAAG,CAAC;MACX;IACJ;EACJ;EACA;EACAlE,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACqF,UAAU,EAAE;MACjB,OAAOhJ,EAAE,CAACgL,SAAS,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAAChD,OAAO,CAACsC,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAACnB,eAAe,GAAG,QAAQ;MAC/B,IAAI,CAACjB,kBAAkB,CAACsC,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA,IAAI,CAACvC,WAAW,CAAC4C,aAAa,CAACI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACArH,YAAY,CAAC,IAAI,CAACmF,kBAAkB,CAAC;MACrC,IAAI,IAAI,CAACV,mBAAmB,EAAE;QAC1B,IAAI,CAACQ,QAAQ,CAAC8B,IAAI,CAAC/J,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC2C,SAAS,CAAC,MAAM;UACxC,IAAI,CAACyE,OAAO,CAACsC,GAAG,CAAC,MAAMM,cAAc,CAAC,MAAM,IAAI,CAACT,cAAc,CAACrC,cAAc,CAAC,CAAC,CAAC;QACrF,CAAC,CAAC;MACN,CAAC,MACI;QACDlE,YAAY,CAAC,IAAI,CAACgF,aAAa,CAAC;QAChC,IAAI,CAACA,aAAa,GAAGzE,UAAU,CAAC,MAAM,IAAI,CAACgG,cAAc,CAACrC,cAAc,CAAC,EAAE,GAAG,CAAC;MACnF;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACxE,OAAO;EACvB;EACA;EACA4H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAClC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACmC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACd,aAAa,CAAC,CAAC;IACpB,IAAI,CAACxB,QAAQ,CAAC9E,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACqE,WAAW,CAACgD,OAAO,CAAC,CAAC;EAC9B;EACAf,aAAaA,CAAA,EAAG;IACZzG,YAAY,CAAC,IAAI,CAACgF,aAAa,CAAC;IAChCgC,cAAc,CAAC,MAAM;MACjB,IAAI,CAACtH,OAAO,CAACQ,IAAI,CAAC,CAAC;MACnB,IAAI,CAACR,OAAO,CAACS,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIiG,oBAAoBA,CAAA,EAAG;IACnB,MAAMqB,OAAO,GAAG,IAAI,CAACpD,WAAW,CAAC4C,aAAa;IAC9C,MAAMS,YAAY,GAAG,IAAI,CAAC/C,cAAc,CAACtD,UAAU;IACnD,IAAIqG,YAAY,EAAE;MACd,IAAIC,KAAK,CAACC,OAAO,CAACF,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACG,OAAO,CAACC,QAAQ,IAAIL,OAAO,CAACP,SAAS,CAACC,GAAG,CAACW,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDL,OAAO,CAACP,SAAS,CAACC,GAAG,CAACO,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACK,eAAe,CAAC,CAAC;IACtB;IACA;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACvC,MAAM,CAACwB,aAAa;IACvC,MAAMgB,UAAU,GAAG,qBAAqB;IACxCD,KAAK,CAACd,SAAS,CAACgB,MAAM,CAACD,UAAU,EAAE,CAACD,KAAK,CAACG,aAAa,CAAC,IAAIF,UAAU,EAAE,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;EACIF,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,EAAE,GAAG,IAAI,CAACzC,cAAc;IAC9B,MAAM0C,MAAM,GAAG,IAAI,CAACzD,SAAS,CAAC0D,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAAC9D,cAAc,CAACsC,GAAG,CAACsB,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAACpB,YAAY,CAAC,WAAW,EAAEe,EAAE,CAAC;MACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCK,KAAK,CAACpB,YAAY,CAAC,WAAW,EAAEqB,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAb,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC1C,cAAc,CAACgD,OAAO,CAACY,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACnD,cAAc,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAC,CAAC;QACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAACpB,YAAY,CAAC,WAAW,EAAEwB,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACnE,cAAc,CAACoE,KAAK,CAAC,CAAC;EAC/B;EACA;EACA/C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACb,aAAa,CAAC6D,WAAW,CAAC,CAAC,KAAK,OAAOhH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAMiH,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACIrC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC3B,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAI,CAACf,OAAO,CAACgF,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACjE,kBAAkB,GAAG5E,UAAU,CAAC,MAAM;QACvC,IAAI,IAAI,CAAC6E,UAAU,EAAE;UACjB;QACJ;QACA,MAAMqC,OAAO,GAAG,IAAI,CAACpD,WAAW,CAAC4C,aAAa;QAC9C,MAAMoC,YAAY,GAAG5B,OAAO,CAACU,aAAa,CAAC,eAAe,CAAC;QAC3D,MAAMmB,WAAW,GAAG7B,OAAO,CAACU,aAAa,CAAC,aAAa,CAAC;QACxD,IAAIkB,YAAY,IAAIC,WAAW,EAAE;UAC7B;UACA;UACA,IAAIC,cAAc,GAAG,IAAI;UACzB,IAAI,IAAI,CAAChF,SAAS,CAACiF,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CN,YAAY,CAACO,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;YAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;UAC3C;UACAL,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;UAC3CM,WAAW,CAACO,WAAW,CAACR,YAAY,CAAC;UACrCE,cAAc,EAAEO,KAAK,CAAC,CAAC;UACvB,IAAI,CAACxE,WAAW,CAACpF,IAAI,CAAC,CAAC;UACvB,IAAI,CAACoF,WAAW,CAACnF,QAAQ,CAAC,CAAC;QAC/B;MACJ,CAAC,EAAE,IAAI,CAAC+E,cAAc,CAAC;IAC3B,CAAC,CAAC;EACN;EACA,OAAOxD,IAAI,YAAAqI,6BAAAnI,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuC,oBAAoB;EAAA;EACvH,OAAOpB,IAAI,kBAvW8E7H,EAAE,CAAA8H,iBAAA;IAAAjB,IAAA,EAuWJoC,oBAAoB;IAAAnC,SAAA;IAAAgI,SAAA,WAAAC,2BAAAvM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvWlBxC,EAAE,CAAAgP,WAAA,CAuWwkBzN,eAAe;QAvWzlBvB,EAAE,CAAAgP,WAAA,CAAAvL,GAAA;MAAA;MAAA,IAAAjB,EAAA;QAAA,IAAAyM,EAAA;QAAFjP,EAAE,CAAAkP,cAAA,CAAAD,EAAA,GAAFjP,EAAE,CAAAmP,WAAA,QAAA1M,GAAA,CAAA0H,aAAA,GAAA8E,EAAA,CAAAG,KAAA;QAAFpP,EAAE,CAAAkP,cAAA,CAAAD,EAAA,GAAFjP,EAAE,CAAAmP,WAAA,QAAA1M,GAAA,CAAA8H,MAAA,GAAA0E,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAArI,SAAA;IAAAsI,QAAA;IAAAC,YAAA,WAAAC,kCAAA/M,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAA6C,UAAA,0BAAA2M,qDAAAC,MAAA;UAAA,OAuWJhN,GAAA,CAAA4I,cAAA,CAAAoE,MAAA,CAAAnE,aAAmC,CAAC;QAAA,CAAjB,CAAC,6BAAAoE,wDAAAD,MAAA;UAAA,OAApBhN,GAAA,CAAA4I,cAAA,CAAAoE,MAAA,CAAAnE,aAAmC,CAAC;QAAA,CAAjB,CAAC;MAAA;MAAA,IAAA9I,EAAA;QAvWlBxC,EAAE,CAAA2P,WAAA,kCAAAlN,GAAA,CAAA4H,eAAA,KAuWgB,SAAD,CAAC,iCAAA5H,GAAA,CAAA4H,eAAA,aAAD,CAAC,gDAAA5H,GAAA,CAAA8G,mBAAD,CAAC;MAAA;IAAA;IAAAqG,QAAA,GAvWlB5P,EAAE,CAAA6P,0BAAA;IAAA7H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAA2H,8BAAAtN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAA4C,cAAA,YAuW00B,CAAC,eAA6M,CAAC,YAAqI,CAAC;QAvWjqC5C,EAAE,CAAAqI,UAAA,IAAA3E,2CAAA,wBAuWqsC,CAAC;QAvWxsC1D,EAAE,CAAAqD,YAAA,CAuWitC,CAAC;QAvWptCrD,EAAE,CAAA+P,SAAA,SAuWu6C,CAAC;QAvW16C/P,EAAE,CAAAqD,YAAA,CAuWi7C,CAAC,CAAO,CAAC;MAAA;MAAA,IAAAb,EAAA;QAvW57CxC,EAAE,CAAAsD,SAAA,EAuW62C,CAAC;QAvWh3CtD,EAAE,CAAAgQ,WAAA,cAAAvN,GAAA,CAAA6H,KAAA,UAAA7H,GAAA,CAAA+H,KAAA,QAAA/H,GAAA,CAAAgI,cAAA;MAAA;IAAA;IAAAjC,YAAA,GAuW02IjH,eAAe;IAAAkH,MAAA;IAAAC,aAAA;EAAA;AACx9I;AACA;EAAA,QAAA1B,SAAA,oBAAAA,SAAA,KAzW6FhH,EAAE,CAAAiH,iBAAA,CAyWJgC,oBAAoB,EAAc,CAAC;IAClHpC,IAAI,EAAEzG,SAAS;IACf8G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEwB,eAAe,EAAErI,uBAAuB,CAAC2P,OAAO;MAAEvH,aAAa,EAAErI,iBAAiB,CAACuI,IAAI;MAAEE,OAAO,EAAE,CAACvH,eAAe,CAAC;MAAE6F,IAAI,EAAE;QAC7J,OAAO,EAAE,0CAA0C;QACnD,uCAAuC,EAAE,+BAA+B;QACxE,sCAAsC,EAAE,8BAA8B;QACtE,oDAAoD,EAAE,sBAAsB;QAC5E,gBAAgB,EAAE,sCAAsC;QACxD,mBAAmB,EAAE;MACzB,CAAC;MAAEe,QAAQ,EAAE,irBAAirB;MAAEM,MAAM,EAAE,CAAC,q3FAAq3F;IAAE,CAAC;EAC7kH,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE0B,aAAa,EAAE,CAAC;MACxDtD,IAAI,EAAEjG,SAAS;MACfsG,IAAI,EAAE,CAAC3F,eAAe,EAAE;QAAE2O,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE3F,MAAM,EAAE,CAAC;MACT1D,IAAI,EAAEjG,SAAS;MACfsG,IAAI,EAAE,CAAC,OAAO,EAAE;QAAEgJ,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,SAASC,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIpK,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMqK,6BAA6B,GAAG,IAAInQ,cAAc,CAAC,+BAA+B,EAAE;EACtFoQ,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EACdC,QAAQ,GAAGrQ,MAAM,CAAC+B,OAAO,CAAC;EAC1BoI,KAAK,GAAGnK,MAAM,CAACyB,aAAa,CAAC;EAC7B6O,SAAS,GAAGtQ,MAAM,CAACU,QAAQ,CAAC;EAC5B6P,mBAAmB,GAAGvQ,MAAM,CAAC6B,kBAAkB,CAAC;EAChD2O,eAAe,GAAGxQ,MAAM,CAACoQ,WAAW,EAAE;IAAE/G,QAAQ,EAAE,IAAI;IAAEoH,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzEC,cAAc,GAAG1Q,MAAM,CAACiQ,6BAA6B,CAAC;EACtD;AACJ;AACA;AACA;AACA;EACIU,uBAAuB,GAAG,IAAI;EAC9B;EACAC,uBAAuB,GAAGtJ,cAAc;EACxC;EACAuJ,0BAA0B,GAAG/H,oBAAoB;EACjD;EACAgI,eAAe,GAAG,2BAA2B;EAC7C;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACR,eAAe;IACnC,OAAOQ,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACJ,uBAAuB;EAC5E;EACA,IAAII,kBAAkBA,CAACE,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACT,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACO,kBAAkB,GAAGE,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACN,uBAAuB,GAAGM,KAAK;IACxC;EACJ;EACA7M,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI8M,iBAAiBA,CAACC,SAAS,EAAE3G,MAAM,EAAE;IACjC,OAAO,IAAI,CAAC4G,OAAO,CAACD,SAAS,EAAE3G,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6G,gBAAgBA,CAACrJ,QAAQ,EAAEwC,MAAM,EAAE;IAC/B,OAAO,IAAI,CAAC4G,OAAO,CAACpJ,QAAQ,EAAEwC,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACI8G,IAAIA,CAACnJ,OAAO,EAAEnF,MAAM,GAAG,EAAE,EAAEwH,MAAM,EAAE;IAC/B,MAAM+G,OAAO,GAAG;MAAE,GAAG,IAAI,CAACb,cAAc;MAAE,GAAGlG;IAAO,CAAC;IACrD;IACA;IACA+G,OAAO,CAAClO,IAAI,GAAG;MAAE8E,OAAO;MAAEnF;IAAO,CAAC;IAClC;IACA;IACA,IAAIuO,OAAO,CAACzL,mBAAmB,KAAKqC,OAAO,EAAE;MACzCoJ,OAAO,CAACzL,mBAAmB,GAAGiG,SAAS;IAC3C;IACA,OAAO,IAAI,CAACmF,iBAAiB,CAAC,IAAI,CAACN,uBAAuB,EAAEW,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACI/M,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACuM,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACvM,OAAO,CAAC,CAAC;IACrC;EACJ;EACAyH,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAAC0E,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACnM,OAAO,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACIgN,wBAAwBA,CAACC,UAAU,EAAEjH,MAAM,EAAE;IACzC,MAAMkH,YAAY,GAAGlH,MAAM,IAAIA,MAAM,CAACzE,gBAAgB,IAAIyE,MAAM,CAACzE,gBAAgB,CAAC4L,QAAQ;IAC1F,MAAMA,QAAQ,GAAGjR,QAAQ,CAACkR,MAAM,CAAC;MAC7BZ,MAAM,EAAEU,YAAY,IAAI,IAAI,CAACpB,SAAS;MACtCuB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAElM,iBAAiB;QAAEmM,QAAQ,EAAEvH;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAMwH,eAAe,GAAG,IAAI3Q,eAAe,CAAC,IAAI,CAACwP,0BAA0B,EAAErG,MAAM,CAACzE,gBAAgB,EAAE4L,QAAQ,CAAC;IAC/G,MAAMM,YAAY,GAAGR,UAAU,CAACS,MAAM,CAACF,eAAe,CAAC;IACvDC,YAAY,CAACpO,QAAQ,CAACyF,cAAc,GAAGkB,MAAM;IAC7C,OAAOyH,YAAY,CAACpO,QAAQ;EAChC;EACA;AACJ;AACA;EACIuN,OAAOA,CAACe,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAM5H,MAAM,GAAG;MAAE,GAAG,IAAI5E,iBAAiB,CAAC,CAAC;MAAE,GAAG,IAAI,CAAC8K,cAAc;MAAE,GAAG0B;IAAW,CAAC;IACpF,MAAMX,UAAU,GAAG,IAAI,CAACY,cAAc,CAAC7H,MAAM,CAAC;IAC9C,MAAM8H,SAAS,GAAG,IAAI,CAACd,wBAAwB,CAACC,UAAU,EAAEjH,MAAM,CAAC;IACnE,MAAMjD,WAAW,GAAG,IAAI5D,cAAc,CAAC2O,SAAS,EAAEb,UAAU,CAAC;IAC7D,IAAIU,OAAO,YAAYxR,WAAW,EAAE;MAChC,MAAMiK,MAAM,GAAG,IAAItJ,cAAc,CAAC6Q,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAE/H,MAAM,CAACnH,IAAI;QACtBkE;MACJ,CAAC,CAAC;MACFA,WAAW,CAAC1D,QAAQ,GAAGyO,SAAS,CAACtH,oBAAoB,CAACJ,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAM+G,QAAQ,GAAG,IAAI,CAACa,eAAe,CAAChI,MAAM,EAAEjD,WAAW,CAAC;MAC1D,MAAMqD,MAAM,GAAG,IAAIvJ,eAAe,CAAC8Q,OAAO,EAAEpG,SAAS,EAAE4F,QAAQ,CAAC;MAChE,MAAMc,UAAU,GAAGH,SAAS,CAAC3H,qBAAqB,CAACC,MAAM,CAAC;MAC1D;MACArD,WAAW,CAAC1D,QAAQ,GAAG4O,UAAU,CAAC5O,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAAC0M,mBAAmB,CACnBmC,OAAO,CAAC5Q,WAAW,CAAC6Q,eAAe,CAAC,CACpCjH,IAAI,CAAC9J,SAAS,CAAC6P,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzCtO,SAAS,CAACuO,KAAK,IAAI;MACpBpB,UAAU,CAACqB,cAAc,CAACjH,SAAS,CAACgB,MAAM,CAAC,IAAI,CAACiE,eAAe,EAAE+B,KAAK,CAACE,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAIvI,MAAM,CAAC1E,mBAAmB,EAAE;MAC5B;MACAwM,SAAS,CAACrI,WAAW,CAAC3F,SAAS,CAAC,MAAM;QAClC,IAAI,CAAC6F,KAAK,CAAC6I,QAAQ,CAACxI,MAAM,CAAC1E,mBAAmB,EAAE0E,MAAM,CAAC3E,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAACoN,gBAAgB,CAAC1L,WAAW,EAAEiD,MAAM,CAAC;IAC1C,IAAI,CAACuG,kBAAkB,GAAGxJ,WAAW;IACrC,OAAO,IAAI,CAACwJ,kBAAkB;EAClC;EACA;EACAkC,gBAAgBA,CAAC1L,WAAW,EAAEiD,MAAM,EAAE;IAClC;IACAjD,WAAW,CAAChC,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACyM,kBAAkB,IAAIxJ,WAAW,EAAE;QACxC,IAAI,CAACwJ,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIvG,MAAM,CAAC1E,mBAAmB,EAAE;QAC5B,IAAI,CAACqE,KAAK,CAACyD,KAAK,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF;IACA,IAAIpD,MAAM,CAACvF,QAAQ,IAAIuF,MAAM,CAACvF,QAAQ,GAAG,CAAC,EAAE;MACxCsC,WAAW,CAAC/B,WAAW,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAMiD,WAAW,CAACvC,aAAa,CAACwF,MAAM,CAACvF,QAAQ,CAAC,CAAC;IACzF;IACA,IAAI,IAAI,CAAC8L,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAACxL,cAAc,CAAC,CAAC,CAACjB,SAAS,CAAC,MAAM;QACrDiD,WAAW,CAACzD,iBAAiB,CAACwH,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACyF,kBAAkB,CAACvM,OAAO,CAAC,CAAC;IACrC,CAAC,MACI;MACD;MACA+C,WAAW,CAACzD,iBAAiB,CAACwH,KAAK,CAAC,CAAC;IACzC;EACJ;EACA;AACJ;AACA;AACA;EACI+G,cAAcA,CAAC7H,MAAM,EAAE;IACnB,MAAM0I,aAAa,GAAG,IAAIlR,aAAa,CAAC,CAAC;IACzCkR,aAAa,CAACjN,SAAS,GAAGuE,MAAM,CAACvE,SAAS;IAC1C,IAAIkN,gBAAgB,GAAG,IAAI,CAAC9C,QAAQ,CAAC+C,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACxD;IACA,MAAMC,KAAK,GAAG9I,MAAM,CAACvE,SAAS,KAAK,KAAK;IACxC,MAAMsN,MAAM,GAAG/I,MAAM,CAACtE,kBAAkB,KAAK,MAAM,IAC9CsE,MAAM,CAACtE,kBAAkB,KAAK,OAAO,IAAI,CAACoN,KAAM,IAChD9I,MAAM,CAACtE,kBAAkB,KAAK,KAAK,IAAIoN,KAAM;IAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAI/I,MAAM,CAACtE,kBAAkB,KAAK,QAAQ;IACjE,IAAIqN,MAAM,EAAE;MACRJ,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdL,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDP,gBAAgB,CAACQ,kBAAkB,CAAC,CAAC;IACzC;IACA;IACA,IAAInJ,MAAM,CAACrE,gBAAgB,KAAK,KAAK,EAAE;MACnCgN,gBAAgB,CAACS,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDT,gBAAgB,CAACU,MAAM,CAAC,GAAG,CAAC;IAChC;IACAX,aAAa,CAACC,gBAAgB,GAAGA,gBAAgB;IACjD,OAAO,IAAI,CAAC9C,QAAQ,CAACuB,MAAM,CAACsB,aAAa,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIV,eAAeA,CAAChI,MAAM,EAAEjD,WAAW,EAAE;IACjC,MAAMmK,YAAY,GAAGlH,MAAM,IAAIA,MAAM,CAACzE,gBAAgB,IAAIyE,MAAM,CAACzE,gBAAgB,CAAC4L,QAAQ;IAC1F,OAAOjR,QAAQ,CAACkR,MAAM,CAAC;MACnBZ,MAAM,EAAEU,YAAY,IAAI,IAAI,CAACpB,SAAS;MACtCuB,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEnO,cAAc;QAAEoO,QAAQ,EAAExK;MAAY,CAAC,EAClD;QAAEuK,OAAO,EAAEnM,kBAAkB;QAAEoM,QAAQ,EAAEvH,MAAM,CAACnH;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;EACA,OAAOgD,IAAI,YAAAyN,oBAAAvN,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6J,WAAW;EAAA;EAC9G,OAAO2D,KAAK,kBArmB6ElU,EAAE,CAAAmU,kBAAA;IAAAC,KAAA,EAqmBY7D,WAAW;IAAAD,OAAA,EAAXC,WAAW,CAAA/J,IAAA;IAAA6J,UAAA,EAAc;EAAM;AAC1I;AACA;EAAA,QAAArJ,SAAA,oBAAAA,SAAA,KAvmB6FhH,EAAE,CAAAiH,iBAAA,CAumBJsJ,WAAW,EAAc,CAAC;IACzG1J,IAAI,EAAE9F,UAAU;IAChBmG,IAAI,EAAE,CAAC;MAAEmJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMgE,UAAU,GAAG,CAACpL,oBAAoB,EAAE1C,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB,CAAC;AAClG,MAAM+M,iBAAiB,CAAC;EACpB,OAAO9N,IAAI,YAAA+N,0BAAA7N,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4N,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBA/mB8ExU,EAAE,CAAAyU,gBAAA;IAAA5N,IAAA,EA+mBSyN,iBAAiB;IAAAxL,OAAA,GAAY1G,aAAa,EACtIV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfmF,cAAc,EAAEwB,oBAAoB,EAAE1C,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB;IAAAmN,OAAA,GAAapS,eAAe,EAAE2G,oBAAoB,EAAE1C,gBAAgB,EAAEc,kBAAkB,EAAEE,iBAAiB;EAAA;EAChN,OAAOoN,IAAI,kBApnB8E3U,EAAE,CAAA4U,gBAAA;IAAA5C,SAAA,EAonBuC,CAACzB,WAAW,CAAC;IAAAzH,OAAA,GAAY1G,aAAa,EAChKV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfmF,cAAc,EAAEnF,eAAe;EAAA;AAC3C;AACA;EAAA,QAAA0E,SAAA,oBAAAA,SAAA,KA1nB6FhH,EAAE,CAAAiH,iBAAA,CA0nBJqN,iBAAiB,EAAc,CAAC;IAC/GzN,IAAI,EAAE7F,QAAQ;IACdkG,IAAI,EAAE,CAAC;MACC4B,OAAO,EAAE,CACL1G,aAAa,EACbV,YAAY,EACZN,eAAe,EACfkB,eAAe,EACfmF,cAAc,EACd,GAAG4M,UAAU,CAChB;MACDK,OAAO,EAAE,CAACpS,eAAe,EAAE,GAAG+R,UAAU,CAAC;MACzCrC,SAAS,EAAE,CAACzB,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsE,qBAAqB,GAAG;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE;IACXjO,IAAI,EAAE,CAAC;IACPkO,IAAI,EAAE,OAAO;IACb,aAAa,EAAE,CACX;MACIlO,IAAI,EAAE,CAAC;MACPkO,IAAI,EAAE,cAAc;MACpBtM,MAAM,EAAE;QAAE5B,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE;UAAEuM,SAAS,EAAE,YAAY;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACrF,CAAC,EACD;MACIrO,IAAI,EAAE,CAAC;MACPkO,IAAI,EAAE,SAAS;MACftM,MAAM,EAAE;QAAE5B,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE;UAAEuM,SAAS,EAAE,UAAU;UAAEC,OAAO,EAAE;QAAE,CAAC;QAAEC,MAAM,EAAE;MAAK;IACnF,CAAC,EACD;MACIrO,IAAI,EAAE,CAAC;MACPsO,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAE;QAAEvO,IAAI,EAAE,CAAC;QAAE4B,MAAM,EAAE,IAAI;QAAE4M,OAAO,EAAE;MAAmC,CAAC;MACjFC,OAAO,EAAE;IACb,CAAC,EACD;MACIzO,IAAI,EAAE,CAAC;MACPsO,IAAI,EAAE,wBAAwB;MAC9BC,SAAS,EAAE;QACPvO,IAAI,EAAE,CAAC;QACP4B,MAAM,EAAE;UAAE5B,IAAI,EAAE,CAAC;UAAE4B,MAAM,EAAE;YAAEwM,OAAO,EAAE;UAAE,CAAC;UAAEC,MAAM,EAAE;QAAK,CAAC;QACzDG,OAAO,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;IACb,CAAC,CACJ;IACDA,OAAO,EAAE,CAAC;EACd;AACJ,CAAC;AAED,SAASxP,kBAAkB,EAAEsK,6BAA6B,EAAED,qCAAqC,EAAEI,WAAW,EAAEhJ,iBAAiB,EAAEF,kBAAkB,EAAEtB,iBAAiB,EAAEkD,oBAAoB,EAAE1C,gBAAgB,EAAE+N,iBAAiB,EAAExQ,cAAc,EAAE2D,cAAc,EAAEoN,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}