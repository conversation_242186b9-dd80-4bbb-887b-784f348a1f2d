{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"holy-rides": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/holy-rides", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/app/assets", "output": "assets"}, {"glob": "manifest.webmanifest", "input": "src", "output": "./"}, {"glob": "sw.js", "input": "public", "output": "./"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "holy-rides:build:production"}, "development": {"buildTarget": "holy-rides:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/app/assets", "output": "assets"}, {"glob": "manifest.webmanifest", "input": "src", "output": "./"}, {"glob": "sw.js", "input": "public", "output": "./"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "9ee402a3-b885-43d0-9ee9-71d42975aa9b"}}