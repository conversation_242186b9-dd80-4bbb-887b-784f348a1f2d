import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RidePaymentComponent } from './ride-payment.component';
import { PaymentService } from '../../../../core/services/payment.service';
import { RideService } from '../../../../core/services/ride.service';
import { AuthService } from '../../../../core/services/auth.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Ride } from '../../../../core/models/ride.model';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('RidePaymentComponent', () => {
  let component: RidePaymentComponent;
  let fixture: ComponentFixture<RidePaymentComponent>;
  let paymentServiceSpy: jasmine.SpyObj<PaymentService>;
  let rideServiceSpy: jasmine.SpyObj<RideService>;
  let authServiceSpy: jasmine.SpyObj<AuthService>;
  let snackBarSpy: jasmine.SpyObj<MatSnackBar>;

  const mockRide: Ride = {
    id: '123',
    rider_id: 'rider-123',
    driver_id: 'driver-123',
    pickup_location: '123 Main St',
    dropoff_location: '456 Elm St',
    pickup_time: new Date().toISOString(),
    status: 'completed',
    payment_status: 'pending',
    fare: 25.00,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const mockUpdatedRide: Ride = {
    ...mockRide,
    payment_status: 'paid',
    payment_id: 'pi_123456789'
  };

  beforeEach(async () => {
    paymentServiceSpy = jasmine.createSpyObj('PaymentService', [
      'updateRidePaymentStatus',
      'estimateFare'
    ]);

    rideServiceSpy = jasmine.createSpyObj('RideService', [
      'updateRide',
      'getRide'
    ]);

    const mockSupabase = {
      functions: {
        invoke: jasmine.createSpy().and.returnValue(Promise.resolve({
          data: { client_secret: 'cs_123456789' },
          error: null
        }))
      }
    };

    authServiceSpy = jasmine.createSpyObj('AuthService', [], {
      supabase: mockSupabase
    });

    snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [RidePaymentComponent],
      providers: [
        { provide: PaymentService, useValue: paymentServiceSpy },
        { provide: RideService, useValue: rideServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: MatSnackBar, useValue: snackBarSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA] // Ignore Angular Material components
    }).compileComponents();

    fixture = TestBed.createComponent(RidePaymentComponent);
    component = fixture.componentInstance;
    component.ride = mockRide;

    // Mock the getRide method to return the updated ride
    rideServiceSpy.getRide.and.returnValue(Promise.resolve(mockUpdatedRide));
    rideServiceSpy.updateRide.and.returnValue(Promise.resolve(true));

    // Mock Stripe and card element
    component.stripe = {
      createPaymentMethod: jasmine.createSpy().and.returnValue(Promise.resolve({
        paymentMethod: { id: 'pm_123456789' },
        error: null
      })),
      confirmCardPayment: jasmine.createSpy().and.returnValue(Promise.resolve({
        error: null,
        paymentIntent: { id: 'pi_123456789', status: 'succeeded' }
      }))
    };
    component.card = {
      mount: jasmine.createSpy(),
      on: jasmine.createSpy()
    };
    component.cardComplete = true;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update ride payment status after successful payment', async () => {
    // Call the processPayment method
    await component.processPayment();

    // Verify that updateRide was called with the correct parameters
    expect(rideServiceSpy.updateRide).toHaveBeenCalledWith(mockRide.id, {
      payment_status: 'paid',
      payment_id: 'pi_123456789',
      amount: mockRide.fare
    });

    // Verify that getRide was called to refresh the ride data
    expect(rideServiceSpy.getRide).toHaveBeenCalledWith(mockRide.id);

    // Verify that the snackBar was opened with a success message
    expect(snackBarSpy.open).toHaveBeenCalledWith(
      'Payment processed successfully!',
      'Close',
      { duration: 3000 }
    );

    // Verify that the ride was updated in the component
    expect(component.ride).toEqual(mockUpdatedRide);
  });
});
