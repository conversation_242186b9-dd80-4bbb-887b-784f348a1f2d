import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PaymentService } from '../../../../core/services/payment.service';
import { AuthService } from '../../../../core/services/auth.service';
import { DriverPayout } from '../../../../core/models/payout.model';

@Component({
  selector: 'app-driver-earnings',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTableModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatTooltipModule
  ],
  template: '<div class="earnings-container">\n      <mat-card class="summary-card">\n        <mat-card-header>\n          <mat-card-title>Earnings Summary</mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class="summary-grid">\n            <div class="summary-item">\n              <div class="summary-label">Total Earnings</div>\n              <div class="summary-value">{{ totalEarnings.toFixed(2) }}</div>\n            </div>\n            <div class="summary-item">\n              <div class="summary-label">Pending Payouts</div>\n              <div class="summary-value">{{ pendingEarnings.toFixed(2) }}</div>\n            </div>\n            <div class="summary-item">\n              <div class="summary-label">Completed Rides</div>\n              <div class="summary-value">{{ completedRides }}</div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class="payouts-card">\n        <mat-card-header>\n          <mat-card-title>Payout History</mat-card-title>\n          <button mat-icon-button color="primary" (click)="loadPayouts()" matTooltip="Refresh payouts">\n            <mat-icon>refresh</mat-icon>\n          </button>\n        </mat-card-header>\n        <mat-card-content>\n          <div *ngIf="loading" class="loading-container">\n            <mat-spinner diameter="40"></mat-spinner>\n            <p>Loading payouts...</p>\n          </div>\n\n          <div *ngIf="!loading && payouts.length === 0" class="no-payouts">\n            <p>No payouts found.</p>\n          </div>\n\n          <table mat-table [dataSource]="payouts" class="payouts-table" *ngIf="!loading && payouts.length > 0">\n            <ng-container matColumnDef="date">\n              <th mat-header-cell *matHeaderCellDef>Date</th>\n              <td mat-cell *matCellDef="let payout">{{ payout.created_at | date:\'medium\' }}</td>\n            </ng-container>\n\n            <ng-container matColumnDef="ride_id">\n              <th mat-header-cell *matHeaderCellDef>Ride ID</th>\n              <td mat-cell *matCellDef="let payout">{{ payout.ride_id | slice:0:8 }}...</td>\n            </ng-container>\n\n            <ng-container matColumnDef="amount">\n              <th mat-header-cell *matHeaderCellDef>Amount</th>\n              <td mat-cell *matCellDef="let payout">{{ payout.amount.toFixed(2) }}</td>\n            </ng-container>\n\n            <ng-container matColumnDef="status">\n              <th mat-header-cell *matHeaderCellDef>Status</th>\n              <td mat-cell *matCellDef="let payout">\n                <span class="status-chip" [ngClass]="\'status-\' + payout.status">\n                  {{ payout.status }}\n                </span>\n              </td>\n            </ng-container>\n\n            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>\n            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>\n          </table>\n        </mat-card-content>\n      </mat-card>\n    </div>',
  styles: [`
    .earnings-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 20px;
      max-width: 1000px;
      margin: 0 auto;
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 16px;
    }

    .summary-item {
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
    }

    .summary-label {
      font-size: 0.9em;
      color: rgba(0, 0, 0, 0.6);
      margin-bottom: 8px;
    }

    .summary-value {
      font-size: 1.8em;
      font-weight: 500;
      color: #3f51b5;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .no-payouts {
      text-align: center;
      padding: 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .payouts-table {
      width: 100%;
    }

    .status-chip {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 0.85em;
      text-transform: capitalize;
    }

    .status-pending {
      background-color: #ffeb3b;
      color: #000;
    }

    .status-paid {
      background-color: #4caf50;
      color: white;
    }

    .status-failed {
      background-color: #f44336;
      color: white;
    }
  `]
})
export class DriverEarningsComponent implements OnInit {
  payouts: DriverPayout[] = [];
  displayedColumns: string[] = ['date', 'ride_id', 'amount', 'status'];
  loading = false;
  totalEarnings = 0;
  pendingEarnings = 0;
  completedRides = 0;

  constructor(
    private paymentService: PaymentService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadPayouts();
  }

  async loadPayouts(): Promise<void> {
    this.loading = true;

    try {
      const user = await this.authService.getCurrentUser();
      if (!user) throw new Error('User not found');

      // Load payouts
      this.payouts = await this.paymentService.getDriverPayouts(user.id);

      // Calculate earnings
      this.totalEarnings = await this.paymentService.getDriverTotalEarnings(user.id);
      this.pendingEarnings = await this.paymentService.getDriverPendingEarnings(user.id);

      // Count completed rides (based on payouts)
      this.completedRides = this.payouts.length;
    } catch (error) {
      console.error('Error loading payouts:', error);
    } finally {
      this.loading = false;
    }
  }
}
