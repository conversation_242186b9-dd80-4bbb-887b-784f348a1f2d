{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts.scss?ngResource!=!C:\\\\Users\\\\<USER>\\\\code\\\\holy rides\\\\holy-rides\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=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%3D%3D!C:/Users/<USER>/code/holy rides/holy-rides/src/app/features/dashboard/rider/ride-payment/ride-payment.component.ts\";\nimport { Component, Input, ViewChild, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { environment } from '../../../../../environments/environment';\nimport { loadStripe } from '@stripe/stripe-js';\nimport { AuthService } from '../../../../core/services/auth.service';\nlet RidePaymentComponent = class RidePaymentComponent {\n  paymentService;\n  rideService;\n  snackBar;\n  authService;\n  ride;\n  cardElement;\n  paymentCompleted = new EventEmitter();\n  processing = false;\n  stripe;\n  card;\n  sdkLoaded = false;\n  cardError = '';\n  cardComplete = false;\n  paymentResult = null;\n  constructor(paymentService, rideService, snackBar, authService) {\n    this.paymentService = paymentService;\n    this.rideService = rideService;\n    this.snackBar = snackBar;\n    this.authService = authService;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // If the ride doesn't have an amount, calculate it\n      if (_this.ride && !_this.ride.amount && !_this.ride.fare) {\n        _this.estimateFare();\n      }\n      // Load Stripe SDK if the ride can be paid\n      _this.loadStripeScript();\n      const stripe = yield _this._loadStripe(environment.stripePublishableKey);\n    })();\n  }\n  ngAfterViewInit() {\n    // Card element will be initialized after Stripe script is loaded\n    // and the view is initialized\n    this.initializeCard();\n  }\n  _loadStripe(key) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const stripe = yield loadStripe(key);\n      _this2.sdkLoaded = true;\n    })();\n  }\n  loadStripeScript() {\n    if (window.Stripe) {\n      this.initializeStripe();\n      return;\n    }\n    const script = document.createElement('script');\n    script.src = 'https://js.stripe.com/v3/';\n    script.async = true;\n    script.onload = () => {\n      this.initializeStripe();\n    };\n    document.body.appendChild(script);\n  }\n  initializeStripe() {\n    if (!window.Stripe) {\n      this.snackBar.open('Stripe SDK not available', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    try {\n      // Initialize Stripe with the publishable key from environment\n      this.stripe = window.Stripe(environment.stripePublishableKey);\n      // Initialize card element after a short delay to ensure DOM is ready\n      setTimeout(() => this.initializeCard(), 100);\n    } catch (error) {\n      console.error('Error initializing Stripe:', error);\n      this.snackBar.open('Error initializing Stripe payments. Check your credentials.', 'Close', {\n        duration: 5000\n      });\n    }\n  }\n  initializeCard() {\n    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {\n      setTimeout(() => this.initializeCard(), 100);\n      return;\n    }\n    try {\n      const elements = this.stripe.elements();\n      // Create card element\n      this.card = elements.create('card', {\n        style: {\n          base: {\n            iconColor: '#666EE8',\n            color: '#31325F',\n            fontWeight: 400,\n            fontFamily: '\"Helvetica Neue\", Helvetica, sans-serif',\n            fontSize: '16px',\n            '::placeholder': {\n              color: '#CFD7E0'\n            }\n          }\n        }\n      });\n      // Mount the card element\n      this.card.mount(this.cardElement.nativeElement);\n      // Handle card element errors\n      this.card.on('change', event => {\n        this.cardError = event.error ? event.error.message : '';\n        this.cardComplete = event.complete;\n      });\n      this.sdkLoaded = true;\n    } catch (error) {\n      console.error('Error initializing Stripe card:', error);\n      this.snackBar.open('Error initializing Stripe card form', 'Close', {\n        duration: 5000\n      });\n    }\n  }\n  calculateAmount() {\n    // Default amount if no calculation is available\n    return 15.00;\n  }\n  estimateFare() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.ride) return;\n      try {\n        const fare = yield _this3.paymentService.estimateFare(_this3.ride.pickup_location, _this3.ride.dropoff_location);\n        // Update the ride with the estimated fare\n        yield _this3.rideService.updateRide(_this3.ride.id, {\n          fare\n        });\n      } catch (error) {\n        console.error('Error estimating fare:', error);\n      }\n    })();\n  }\n  canPay() {\n    if (!this.ride) return false;\n    // Can pay if the ride is completed and payment is pending\n    return this.ride.status === 'completed' && (!this.ride.payment_status || this.ride.payment_status === 'pending' || this.ride.payment_status === 'failed');\n  }\n  canRequestRefund() {\n    if (!this.ride) return false;\n    // Can request refund if the payment status is paid or completed\n    return this.ride.payment_status === 'paid' || this.ride.payment_status === 'completed';\n  }\n  processPayment() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.ride || !_this4.canPay() || !_this4.stripe || !_this4.card) return;\n      _this4.processing = true;\n      _this4.paymentResult = null;\n      try {\n        const amount = _this4.ride.amount || _this4.ride.fare || _this4.calculateAmount();\n        // Create a payment intent\n        // const { clientSecret } = await this.paymentService.createPaymentIntent(\n        //   this.ride.id,\n        //   amount\n        // );\n        // Create a payment method with the card details\n        const {\n          paymentMethod,\n          error: paymentMethodError\n        } = yield _this4.stripe.createPaymentMethod({\n          type: 'card',\n          card: _this4.card\n        });\n        if (paymentMethodError) {\n          throw paymentMethodError;\n        }\n        if (paymentMethodError) {\n          throw paymentMethodError;\n        }\n        let payment = {\n          amount: amount * 100,\n          // Stripe uses cents\n          currency: 'usd',\n          description: \"Customer pamyment for ride\",\n          payment_method: paymentMethod.id\n        };\n        console.log(payment);\n        // Step 2: Create a payment intent using Supabase Stripe edge function\n        const {\n          data,\n          error\n        } = yield _this4.authService.supabase.functions.invoke('stripe', {\n          body: payment\n        });\n        if (error) {\n          console.error('Error creating payment intent:', error);\n          throw new Error(`Failed to create payment intent: ${error.message}`);\n        }\n        console.log('Payment intent created:', data);\n        if (!data || !data.client_secret) {\n          throw new Error('No client secret returned from payment intent creation');\n        }\n        const clientSecret = data.client_secret;\n        // Step 3: Confirm the payment with the client secret\n        const {\n          error: confirmError,\n          paymentIntent\n        } = yield _this4.stripe.confirmCardPayment(clientSecret, {\n          payment_method: paymentMethod.id\n        });\n        if (confirmError) {\n          throw confirmError;\n        }\n        // Payment succeeded\n        _this4.paymentResult = {\n          success: true,\n          paymentIntent: paymentIntent\n        };\n        // Update the ride with payment details\n        yield _this4.rideService.updateRide(_this4.ride.id, {\n          payment_status: 'paid',\n          payment_id: paymentIntent.id,\n          amount: amount\n        });\n        _this4.snackBar.open('Payment processed successfully!', 'Close', {\n          duration: 3000\n        });\n        // Emit event to notify parent component that payment is completed\n        _this4.paymentCompleted.emit();\n        // Realtime subscription will handle the ride update automatically\n        // const updatedRide = await this.rideService.getRide(this.ride.id);\n        // if (updatedRide) {\n        //   this.ride = updatedRide;\n        // }\n      } catch (error) {\n        console.error('Error processing payment:', error);\n        _this4.paymentResult = {\n          success: false,\n          error: {\n            message: error.message || 'An unknown error occurred'\n          }\n        };\n        _this4.snackBar.open(`Payment error: ${error.message}`, 'Close', {\n          duration: 5000\n        });\n      } finally {\n        _this4.processing = false;\n      }\n    })();\n  }\n  getDisplayAmount() {\n    if (!this.ride) return '0';\n    return (this.ride.amount || this.ride.fare || this.calculateAmount()).toString();\n  }\n  requestRefund() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.ride || !_this5.canRequestRefund()) return;\n      _this5.processing = true;\n      _this5.paymentResult = null;\n      try {\n        // Process the refund\n        const success = yield _this5.paymentService.processRefund(_this5.ride.id);\n        if (success) {\n          _this5.paymentResult = {\n            success: true,\n            refund: true\n          };\n          _this5.snackBar.open('Refund processed successfully!', 'Close', {\n            duration: 3000\n          });\n          // Emit event to notify parent component that refund is completed\n          _this5.paymentCompleted.emit();\n          // Realtime subscription will handle the ride update automatically\n          // const updatedRide = await this.rideService.getRide(this.ride.id);\n          // if (updatedRide) {\n          //   this.ride = updatedRide;\n          // }\n        } else {\n          _this5.paymentResult = {\n            success: false,\n            refund: true,\n            error: {\n              message: 'Failed to process refund'\n            }\n          };\n          _this5.snackBar.open('Refund request failed. Please try again.', 'Close', {\n            duration: 3000\n          });\n        }\n      } catch (error) {\n        console.error('Error processing refund:', error);\n        _this5.paymentResult = {\n          success: false,\n          refund: true,\n          error: {\n            message: error.message || 'An unknown error occurred'\n          }\n        };\n        _this5.snackBar.open(`Refund error: ${error.message}`, 'Close', {\n          duration: 5000\n        });\n      } finally {\n        _this5.processing = false;\n      }\n    })();\n  }\n  static ctorParameters = () => [{\n    type: PaymentService\n  }, {\n    type: RideService\n  }, {\n    type: MatSnackBar\n  }, {\n    type: AuthService\n  }];\n  static propDecorators = {\n    ride: [{\n      type: Input\n    }],\n    cardElement: [{\n      type: ViewChild,\n      args: ['cardElement']\n    }],\n    paymentCompleted: [{\n      type: Output\n    }]\n  };\n};\nRidePaymentComponent = __decorate([Component({\n  selector: 'app-ride-payment',\n  standalone: true,\n  imports: [CommonModule, MatCardModule, MatButtonModule, MatIconModule, MatProgressSpinnerModule, MatDialogModule, MatSnackBarModule, MatDividerModule],\n  template: `\n    <mat-card *ngIf=\"ride\">\n      <mat-card-header>\n        <mat-card-title>Ride Payment</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"payment-details\">\n          <div class=\"detail-row\">\n            <span class=\"label\">Pickup:</span>\n            <span class=\"value\">{{ ride.pickup_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Destination:</span>\n            <span class=\"value\">{{ ride.dropoff_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Date:</span>\n            <span class=\"value\">{{ ride.pickup_time | date:'medium' }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'status-' + ride.status\">{{ ride.status }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Payment Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'payment-' + (ride.payment_status || 'pending')\">\n              {{ ride.payment_status || 'pending' }}\n            </span>\n          </div>\n          <div class=\"detail-row amount\">\n            <span class=\"label\">Amount:</span>\n            <span class=\"value\">{{ '$' + getDisplayAmount() }}</span>\n          </div>\n        </div>\n\n        <mat-divider *ngIf=\"canPay()\" class=\"section-divider\"></mat-divider>\n\n        <div *ngIf=\"canPay() && !sdkLoaded\" class=\"sdk-status\">\n          <p>Loading payment form...</p>\n          <mat-spinner diameter=\"30\"></mat-spinner>\n        </div>\n\n        <div *ngIf=\"canPay() && sdkLoaded\" class=\"stripe-payment-form\">\n          <h3>Payment Information</h3>\n          <p class=\"payment-instruction\">Please enter your card details to complete the payment.</p>\n\n          <div #cardElement class=\"card-element\"></div>\n          <div class=\"card-errors\" *ngIf=\"cardError\">{{ cardError }}</div>\n\n          <div class=\"payment-actions\">\n            <button mat-raised-button color=\"primary\"\n                    [disabled]=\"processing || !cardComplete\"\n                    (click)=\"processPayment()\">\n              <mat-icon>payment</mat-icon>\n              {{ processing ? 'Processing...' : 'Pay Now' }}\n            </button>\n\n            <div *ngIf=\"processing\" class=\"processing-indicator\">\n              <mat-spinner diameter=\"24\"></mat-spinner>\n              <span>Processing your payment...</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"payment-actions\" *ngIf=\"canRequestRefund()\">\n          <button mat-raised-button color=\"warn\"\n                  [disabled]=\"processing\"\n                  (click)=\"requestRefund()\">\n            <mat-icon>money_off</mat-icon>\n            {{ processing ? 'Processing...' : 'Request Refund' }}\n          </button>\n\n          <div *ngIf=\"processing\" class=\"processing-indicator\">\n            <mat-spinner diameter=\"24\"></mat-spinner>\n            <span>Processing your request...</span>\n          </div>\n        </div>\n\n        <div class=\"payment-result\" *ngIf=\"paymentResult\">\n          <h3>Payment Result</h3>\n          <div [ngClass]=\"paymentResult.success ? 'success-message' : 'error-message'\">\n            {{ paymentResult.success ? 'Payment successful!' : 'Payment failed: ' + paymentResult.error?.message }}\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [__NG_CLI_RESOURCE__0]\n})], RidePaymentComponent);\nexport { RidePaymentComponent };", "map": {"version": 3, "names": ["Component", "Input", "ViewChild", "Output", "EventEmitter", "CommonModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBar", "MatSnackBarModule", "MatDialogModule", "MatDividerModule", "PaymentService", "RideService", "environment", "loadStripe", "AuthService", "RidePaymentComponent", "paymentService", "rideService", "snackBar", "authService", "ride", "cardElement", "paymentCompleted", "processing", "stripe", "card", "sdkLoaded", "cardError", "cardComplete", "paymentResult", "constructor", "ngOnInit", "_this", "_asyncToGenerator", "amount", "fare", "estimateFare", "loadStripeScript", "_loadStripe", "stripePublishableKey", "ngAfterViewInit", "initializeCard", "key", "_this2", "window", "Stripe", "initializeStripe", "script", "document", "createElement", "src", "async", "onload", "body", "append<PERSON><PERSON><PERSON>", "open", "duration", "setTimeout", "error", "console", "nativeElement", "elements", "create", "style", "base", "iconColor", "color", "fontWeight", "fontFamily", "fontSize", "mount", "on", "event", "message", "complete", "calculateAmount", "_this3", "pickup_location", "dropoff_location", "updateRide", "id", "canPay", "status", "payment_status", "canRequestRefund", "processPayment", "_this4", "paymentMethod", "paymentMethodError", "createPaymentMethod", "type", "payment", "currency", "description", "payment_method", "log", "data", "supabase", "functions", "invoke", "Error", "client_secret", "clientSecret", "confirmError", "paymentIntent", "confirmCardPayment", "success", "payment_id", "emit", "getDisplayAmount", "toString", "requestRefund", "_this5", "processRefund", "refund", "args", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\features\\dashboard\\rider\\ride-payment\\ride-payment.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, AfterViewInit, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { Ride } from '../../../../core/models/ride.model';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { environment } from '../../../../../environments/environment';\nimport { loadStripe, StripeConstructor } from '@stripe/stripe-js';\nimport { AuthService } from '../../../../core/services/auth.service';\n\ndeclare global {\n  interface Window {\n    Stripe?: StripeConstructor;\n  }\n}\n\n@Component({\n  selector: 'app-ride-payment',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatProgressSpinnerModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatDividerModule\n  ],\n  template: `\n    <mat-card *ngIf=\"ride\">\n      <mat-card-header>\n        <mat-card-title>Ride Payment</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"payment-details\">\n          <div class=\"detail-row\">\n            <span class=\"label\">Pickup:</span>\n            <span class=\"value\">{{ ride.pickup_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Destination:</span>\n            <span class=\"value\">{{ ride.dropoff_location }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Date:</span>\n            <span class=\"value\">{{ ride.pickup_time | date:'medium' }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'status-' + ride.status\">{{ ride.status }}</span>\n          </div>\n          <div class=\"detail-row\">\n            <span class=\"label\">Payment Status:</span>\n            <span class=\"value status-badge\" [ngClass]=\"'payment-' + (ride.payment_status || 'pending')\">\n              {{ ride.payment_status || 'pending' }}\n            </span>\n          </div>\n          <div class=\"detail-row amount\">\n            <span class=\"label\">Amount:</span>\n            <span class=\"value\">{{ '$' + getDisplayAmount() }}</span>\n          </div>\n        </div>\n\n        <mat-divider *ngIf=\"canPay()\" class=\"section-divider\"></mat-divider>\n\n        <div *ngIf=\"canPay() && !sdkLoaded\" class=\"sdk-status\">\n          <p>Loading payment form...</p>\n          <mat-spinner diameter=\"30\"></mat-spinner>\n        </div>\n\n        <div *ngIf=\"canPay() && sdkLoaded\" class=\"stripe-payment-form\">\n          <h3>Payment Information</h3>\n          <p class=\"payment-instruction\">Please enter your card details to complete the payment.</p>\n\n          <div #cardElement class=\"card-element\"></div>\n          <div class=\"card-errors\" *ngIf=\"cardError\">{{ cardError }}</div>\n\n          <div class=\"payment-actions\">\n            <button mat-raised-button color=\"primary\"\n                    [disabled]=\"processing || !cardComplete\"\n                    (click)=\"processPayment()\">\n              <mat-icon>payment</mat-icon>\n              {{ processing ? 'Processing...' : 'Pay Now' }}\n            </button>\n\n            <div *ngIf=\"processing\" class=\"processing-indicator\">\n              <mat-spinner diameter=\"24\"></mat-spinner>\n              <span>Processing your payment...</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"payment-actions\" *ngIf=\"canRequestRefund()\">\n          <button mat-raised-button color=\"warn\"\n                  [disabled]=\"processing\"\n                  (click)=\"requestRefund()\">\n            <mat-icon>money_off</mat-icon>\n            {{ processing ? 'Processing...' : 'Request Refund' }}\n          </button>\n\n          <div *ngIf=\"processing\" class=\"processing-indicator\">\n            <mat-spinner diameter=\"24\"></mat-spinner>\n            <span>Processing your request...</span>\n          </div>\n        </div>\n\n        <div class=\"payment-result\" *ngIf=\"paymentResult\">\n          <h3>Payment Result</h3>\n          <div [ngClass]=\"paymentResult.success ? 'success-message' : 'error-message'\">\n            {{ paymentResult.success ? 'Payment successful!' : 'Payment failed: ' + paymentResult.error?.message }}\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  `,\n  styles: [`\n    :host {\n      display: block;\n      margin: 20px;\n    }\n\n    .payment-details {\n      margin-bottom: 20px;\n    }\n\n    .detail-row {\n      display: flex;\n      margin-bottom: 8px;\n      align-items: center;\n    }\n\n    .label {\n      font-weight: 500;\n      width: 120px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .value {\n      flex: 1;\n    }\n\n    .amount {\n      font-size: 1.2em;\n      font-weight: 500;\n      margin-top: 16px;\n    }\n\n    .amount .value {\n      color: #3f51b5;\n    }\n\n    .status-badge {\n      display: inline-block;\n      padding: 4px 8px;\n      border-radius: 4px;\n      text-transform: capitalize;\n      font-size: 0.9em;\n    }\n\n    .status-requested {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .status-assigned {\n      background-color: #2196f3;\n      color: white;\n    }\n\n    .status-in-progress {\n      background-color: #ff9800;\n      color: white;\n    }\n\n    .status-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .status-canceled {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-pending {\n      background-color: #ffeb3b;\n      color: #000;\n    }\n\n    .payment-paid, .payment-completed {\n      background-color: #4caf50;\n      color: white;\n    }\n\n    .payment-failed {\n      background-color: #f44336;\n      color: white;\n    }\n\n    .payment-refunded {\n      background-color: #9e9e9e;\n      color: white;\n    }\n\n    .payment-actions {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n      align-items: flex-start;\n      margin-top: 16px;\n    }\n\n    .processing-indicator {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 8px;\n    }\n\n    .section-divider {\n      margin: 24px 0;\n    }\n\n    .stripe-payment-form {\n      margin-top: 24px;\n    }\n\n    .payment-instruction {\n      margin-bottom: 16px;\n      color: rgba(0, 0, 0, 0.6);\n    }\n\n    .card-element {\n      border: 1px solid #e0e0e0;\n      border-radius: 4px;\n      padding: 12px;\n      background-color: white;\n      margin-bottom: 16px;\n    }\n\n    .card-errors {\n      color: #f44336;\n      font-size: 0.9em;\n      margin-bottom: 16px;\n    }\n\n    .sdk-status {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 16px;\n      margin: 24px 0;\n    }\n\n    .payment-result {\n      margin-top: 24px;\n      padding: 16px;\n      border-radius: 4px;\n      background-color: #f5f5f5;\n    }\n\n    .success-message {\n      color: #4caf50;\n      font-weight: 500;\n    }\n\n    .error-message {\n      color: #f44336;\n      font-weight: 500;\n    }\n  `]\n})\nexport class RidePaymentComponent implements OnInit, AfterViewInit {\n  @Input() ride!: Ride;\n  @ViewChild('cardElement') cardElement!: ElementRef;\n  @Output() paymentCompleted = new EventEmitter<void>();\n\n  processing = false;\n  stripe: any;\n  card: any;\n  sdkLoaded = false;\n  cardError = '';\n  cardComplete = false;\n  paymentResult: any = null;\n\n  constructor(\n    private paymentService: PaymentService,\n    private rideService: RideService,\n    private snackBar: MatSnackBar,\n    private authService: AuthService\n  ) {\n\n  }\n\n\n  async ngOnInit() {\n    // If the ride doesn't have an amount, calculate it\n    if (this.ride && !this.ride.amount && !this.ride.fare) {\n      this.estimateFare();\n\n    }\n\n    // Load Stripe SDK if the ride can be paid\n\n      this.loadStripeScript();\n     const stripe = await this._loadStripe(environment.stripePublishableKey);\n  }\n\n  ngAfterViewInit(): void {\n    // Card element will be initialized after Stripe script is loaded\n    // and the view is initialized\n\n      this.initializeCard();\n\n  }\n  async _loadStripe(key:string){\n\n    const stripe = await loadStripe(key);\n    this.sdkLoaded = true;\n  }\n  loadStripeScript(): void {\n    if (window.Stripe) {\n      this.initializeStripe();\n\n      return;\n    }\n\n    const script = document.createElement('script');\n    script.src = 'https://js.stripe.com/v3/';\n    script.async = true;\n    script.onload = () => {\n      this.initializeStripe();\n    };\n    document.body.appendChild(script);\n  }\n\n  initializeStripe(): void {\n    if (!window.Stripe) {\n      this.snackBar.open('Stripe SDK not available', 'Close', { duration: 3000 });\n      return;\n    }\n\n    try {\n      // Initialize Stripe with the publishable key from environment\n      this.stripe = window.Stripe(environment.stripePublishableKey);\n\n      // Initialize card element after a short delay to ensure DOM is ready\n      setTimeout(() => this.initializeCard(), 100);\n    } catch (error) {\n      console.error('Error initializing Stripe:', error);\n      this.snackBar.open('Error initializing Stripe payments. Check your credentials.', 'Close', { duration: 5000 });\n    }\n  }\n\n  initializeCard(): void {\n    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {\n      setTimeout(() => this.initializeCard(), 100);\n      return;\n    }\n\n    try {\n      const elements = this.stripe.elements();\n\n      // Create card element\n      this.card = elements.create('card', {\n        style: {\n          base: {\n            iconColor: '#666EE8',\n            color: '#31325F',\n            fontWeight: 400,\n            fontFamily: '\"Helvetica Neue\", Helvetica, sans-serif',\n            fontSize: '16px',\n            '::placeholder': {\n              color: '#CFD7E0'\n            }\n          }\n        }\n      });\n\n      // Mount the card element\n      this.card.mount(this.cardElement.nativeElement);\n\n      // Handle card element errors\n      this.card.on('change', (event: any) => {\n        this.cardError = event.error ? event.error.message : '';\n        this.cardComplete = event.complete;\n      });\n\n      this.sdkLoaded = true;\n    } catch (error) {\n      console.error('Error initializing Stripe card:', error);\n      this.snackBar.open('Error initializing Stripe card form', 'Close', { duration: 5000 });\n    }\n  }\n\n  calculateAmount(): number {\n    // Default amount if no calculation is available\n    return 15.00;\n  }\n\n  async estimateFare(): Promise<void> {\n    if (!this.ride) return;\n\n    try {\n      const fare = await this.paymentService.estimateFare(\n        this.ride.pickup_location,\n        this.ride.dropoff_location\n      );\n\n      // Update the ride with the estimated fare\n      await this.rideService.updateRide(this.ride.id, { fare });\n    } catch (error) {\n      console.error('Error estimating fare:', error);\n    }\n  }\n\n  canPay(): boolean {\n    if (!this.ride) return false;\n\n    // Can pay if the ride is completed and payment is pending\n    return (\n      this.ride.status === 'completed' &&\n      (!this.ride.payment_status || this.ride.payment_status === 'pending' || this.ride.payment_status === 'failed')\n    );\n  }\n\n  canRequestRefund(): boolean {\n    if (!this.ride) return false;\n\n    // Can request refund if the payment status is paid or completed\n    return this.ride.payment_status === 'paid' || this.ride.payment_status === 'completed';\n  }\n\n  async processPayment(): Promise<void> {\n    if (!this.ride || !this.canPay() || !this.stripe || !this.card) return;\n\n    this.processing = true;\n    this.paymentResult = null;\n\n    try {\n      const amount = this.ride.amount || this.ride.fare || this.calculateAmount();\n\n      // Create a payment intent\n      // const { clientSecret } = await this.paymentService.createPaymentIntent(\n      //   this.ride.id,\n      //   amount\n      // );\n\n      // Create a payment method with the card details\n      const { paymentMethod, error: paymentMethodError } = await this.stripe.createPaymentMethod({\n        type: 'card',\n        card: this.card,\n      });\n\n      if (paymentMethodError) {\n        throw paymentMethodError;\n      }\n    if (paymentMethodError) {\n        throw paymentMethodError;\n      }\n        let payment = {\n        amount: amount * 100, // Stripe uses cents\n        currency: 'usd',\n        description: \"Customer pamyment for ride\",\n        payment_method: paymentMethod.id\n      };\n        console.log(payment)\n      // Step 2: Create a payment intent using Supabase Stripe edge function\n      const { data, error } = await this.authService.supabase.functions.invoke('stripe', {\n        body:payment });\n\n      if (error) {\n        console.error('Error creating payment intent:', error);\n        throw new Error(`Failed to create payment intent: ${error.message}`);\n      }\n        console.log('Payment intent created:', data);\n\n      if (!data || !data.client_secret) {\n        throw new Error('No client secret returned from payment intent creation');\n      }\n\n      const clientSecret = data.client_secret;\n\n      // Step 3: Confirm the payment with the client secret\n      const { error: confirmError, paymentIntent } = await this.stripe.confirmCardPayment(clientSecret, {\n        payment_method: paymentMethod.id\n      });\n\n      if (confirmError) {\n        throw confirmError;\n      }\n\n      // Payment succeeded\n      this.paymentResult = {\n        success: true,\n        paymentIntent: paymentIntent\n      };\n\n      // Update the ride with payment details\n      await this.rideService.updateRide(this.ride.id, {\n        payment_status: 'paid',\n        payment_id: paymentIntent.id,\n        amount: amount\n      });\n\n      this.snackBar.open('Payment processed successfully!', 'Close', { duration: 3000 });\n\n      // Emit event to notify parent component that payment is completed\n      this.paymentCompleted.emit();\n\n      // Realtime subscription will handle the ride update automatically\n      // const updatedRide = await this.rideService.getRide(this.ride.id);\n      // if (updatedRide) {\n      //   this.ride = updatedRide;\n      // }\n\n\n    } catch (error: any) {\n      console.error('Error processing payment:', error);\n\n      this.paymentResult = {\n        success: false,\n        error: {\n          message: error.message || 'An unknown error occurred'\n        }\n      };\n\n      this.snackBar.open(`Payment error: ${error.message}`, 'Close', { duration: 5000 });\n    } finally {\n      this.processing = false;\n    }\n  }\n\n  getDisplayAmount(): string {\n    if (!this.ride) return '0';\n    return (this.ride.amount || this.ride.fare || this.calculateAmount()).toString();\n  }\n\n  async requestRefund(): Promise<void> {\n    if (!this.ride || !this.canRequestRefund()) return;\n\n    this.processing = true;\n    this.paymentResult = null;\n\n    try {\n      // Process the refund\n      const success = await this.paymentService.processRefund(this.ride.id);\n\n      if (success) {\n        this.paymentResult = {\n          success: true,\n          refund: true\n        };\n\n        this.snackBar.open('Refund processed successfully!', 'Close', { duration: 3000 });\n\n        // Emit event to notify parent component that refund is completed\n        this.paymentCompleted.emit();\n\n        // Realtime subscription will handle the ride update automatically\n        // const updatedRide = await this.rideService.getRide(this.ride.id);\n        // if (updatedRide) {\n        //   this.ride = updatedRide;\n        // }\n      } else {\n        this.paymentResult = {\n          success: false,\n          refund: true,\n          error: {\n            message: 'Failed to process refund'\n          }\n        };\n\n        this.snackBar.open('Refund request failed. Please try again.', 'Close', { duration: 3000 });\n      }\n    } catch (error: any) {\n      console.error('Error processing refund:', error);\n\n      this.paymentResult = {\n        success: false,\n        refund: true,\n        error: {\n          message: error.message || 'An unknown error occurred'\n        }\n      };\n\n      this.snackBar.open(`Refund error: ${error.message}`, 'Close', { duration: 5000 });\n    } finally {\n      this.processing = false;\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,EAAyBC,SAAS,EAAcC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACpH,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,6BAA6B;AAC5E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,UAAU,QAA2B,mBAAmB;AACjE,SAASC,WAAW,QAAQ,wCAAwC;AAyQ7D,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAcrBC,cAAA;EACAC,WAAA;EACAC,QAAA;EACAC,WAAA;EAhBDC,IAAI;EACaC,WAAW;EAC3BC,gBAAgB,GAAG,IAAItB,YAAY,EAAQ;EAErDuB,UAAU,GAAG,KAAK;EAClBC,MAAM;EACNC,IAAI;EACJC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,EAAE;EACdC,YAAY,GAAG,KAAK;EACpBC,aAAa,GAAQ,IAAI;EAEzBC,YACUd,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB,EACrBC,WAAwB;IAHxB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,WAAW,GAAXA,WAAW;EAGrB;EAGMY,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ;MACA,IAAID,KAAI,CAACZ,IAAI,IAAI,CAACY,KAAI,CAACZ,IAAI,CAACc,MAAM,IAAI,CAACF,KAAI,CAACZ,IAAI,CAACe,IAAI,EAAE;QACrDH,KAAI,CAACI,YAAY,EAAE;MAErB;MAEA;MAEEJ,KAAI,CAACK,gBAAgB,EAAE;MACxB,MAAMb,MAAM,SAASQ,KAAI,CAACM,WAAW,CAAC1B,WAAW,CAAC2B,oBAAoB,CAAC;IAAC;EAC3E;EAEAC,eAAeA,CAAA;IACb;IACA;IAEE,IAAI,CAACC,cAAc,EAAE;EAEzB;EACMH,WAAWA,CAACI,GAAU;IAAA,IAAAC,MAAA;IAAA,OAAAV,iBAAA;MAE1B,MAAMT,MAAM,SAASX,UAAU,CAAC6B,GAAG,CAAC;MACpCC,MAAI,CAACjB,SAAS,GAAG,IAAI;IAAC;EACxB;EACAW,gBAAgBA,CAAA;IACd,IAAIO,MAAM,CAACC,MAAM,EAAE;MACjB,IAAI,CAACC,gBAAgB,EAAE;MAEvB;IACF;IAEA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,GAAG,GAAG,2BAA2B;IACxCH,MAAM,CAACI,KAAK,GAAG,IAAI;IACnBJ,MAAM,CAACK,MAAM,GAAG,MAAK;MACnB,IAAI,CAACN,gBAAgB,EAAE;IACzB,CAAC;IACDE,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;EACnC;EAEAD,gBAAgBA,CAAA;IACd,IAAI,CAACF,MAAM,CAACC,MAAM,EAAE;MAClB,IAAI,CAAC3B,QAAQ,CAACqC,IAAI,CAAC,0BAA0B,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MAC3E;IACF;IAEA,IAAI;MACF;MACA,IAAI,CAAChC,MAAM,GAAGoB,MAAM,CAACC,MAAM,CAACjC,WAAW,CAAC2B,oBAAoB,CAAC;MAE7D;MACAkB,UAAU,CAAC,MAAM,IAAI,CAAChB,cAAc,EAAE,EAAE,GAAG,CAAC;IAC9C,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAACxC,QAAQ,CAACqC,IAAI,CAAC,6DAA6D,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;IAChH;EACF;EAEAf,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACpB,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACuC,aAAa,IAAI,CAAC,IAAI,CAACpC,MAAM,EAAE;MACxEiC,UAAU,CAAC,MAAM,IAAI,CAAChB,cAAc,EAAE,EAAE,GAAG,CAAC;MAC5C;IACF;IAEA,IAAI;MACF,MAAMoB,QAAQ,GAAG,IAAI,CAACrC,MAAM,CAACqC,QAAQ,EAAE;MAEvC;MACA,IAAI,CAACpC,IAAI,GAAGoC,QAAQ,CAACC,MAAM,CAAC,MAAM,EAAE;QAClCC,KAAK,EAAE;UACLC,IAAI,EAAE;YACJC,SAAS,EAAE,SAAS;YACpBC,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,GAAG;YACfC,UAAU,EAAE,yCAAyC;YACrDC,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE;cACfH,KAAK,EAAE;;;;OAId,CAAC;MAEF;MACA,IAAI,CAACzC,IAAI,CAAC6C,KAAK,CAAC,IAAI,CAACjD,WAAW,CAACuC,aAAa,CAAC;MAE/C;MACA,IAAI,CAACnC,IAAI,CAAC8C,EAAE,CAAC,QAAQ,EAAGC,KAAU,IAAI;QACpC,IAAI,CAAC7C,SAAS,GAAG6C,KAAK,CAACd,KAAK,GAAGc,KAAK,CAACd,KAAK,CAACe,OAAO,GAAG,EAAE;QACvD,IAAI,CAAC7C,YAAY,GAAG4C,KAAK,CAACE,QAAQ;MACpC,CAAC,CAAC;MAEF,IAAI,CAAChD,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAACxC,QAAQ,CAACqC,IAAI,CAAC,qCAAqC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;IACxF;EACF;EAEAmB,eAAeA,CAAA;IACb;IACA,OAAO,KAAK;EACd;EAEMvC,YAAYA,CAAA;IAAA,IAAAwC,MAAA;IAAA,OAAA3C,iBAAA;MAChB,IAAI,CAAC2C,MAAI,CAACxD,IAAI,EAAE;MAEhB,IAAI;QACF,MAAMe,IAAI,SAASyC,MAAI,CAAC5D,cAAc,CAACoB,YAAY,CACjDwC,MAAI,CAACxD,IAAI,CAACyD,eAAe,EACzBD,MAAI,CAACxD,IAAI,CAAC0D,gBAAgB,CAC3B;QAED;QACA,MAAMF,MAAI,CAAC3D,WAAW,CAAC8D,UAAU,CAACH,MAAI,CAACxD,IAAI,CAAC4D,EAAE,EAAE;UAAE7C;QAAI,CAAE,CAAC;MAC3D,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;IAAC;EACH;EAEAuB,MAAMA,CAAA;IACJ,IAAI,CAAC,IAAI,CAAC7D,IAAI,EAAE,OAAO,KAAK;IAE5B;IACA,OACE,IAAI,CAACA,IAAI,CAAC8D,MAAM,KAAK,WAAW,KAC/B,CAAC,IAAI,CAAC9D,IAAI,CAAC+D,cAAc,IAAI,IAAI,CAAC/D,IAAI,CAAC+D,cAAc,KAAK,SAAS,IAAI,IAAI,CAAC/D,IAAI,CAAC+D,cAAc,KAAK,QAAQ,CAAC;EAElH;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAChE,IAAI,EAAE,OAAO,KAAK;IAE5B;IACA,OAAO,IAAI,CAACA,IAAI,CAAC+D,cAAc,KAAK,MAAM,IAAI,IAAI,CAAC/D,IAAI,CAAC+D,cAAc,KAAK,WAAW;EACxF;EAEME,cAAcA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MAClB,IAAI,CAACqD,MAAI,CAAClE,IAAI,IAAI,CAACkE,MAAI,CAACL,MAAM,EAAE,IAAI,CAACK,MAAI,CAAC9D,MAAM,IAAI,CAAC8D,MAAI,CAAC7D,IAAI,EAAE;MAEhE6D,MAAI,CAAC/D,UAAU,GAAG,IAAI;MACtB+D,MAAI,CAACzD,aAAa,GAAG,IAAI;MAEzB,IAAI;QACF,MAAMK,MAAM,GAAGoD,MAAI,CAAClE,IAAI,CAACc,MAAM,IAAIoD,MAAI,CAAClE,IAAI,CAACe,IAAI,IAAImD,MAAI,CAACX,eAAe,EAAE;QAE3E;QACA;QACA;QACA;QACA;QAEA;QACA,MAAM;UAAEY,aAAa;UAAE7B,KAAK,EAAE8B;QAAkB,CAAE,SAASF,MAAI,CAAC9D,MAAM,CAACiE,mBAAmB,CAAC;UACzFC,IAAI,EAAE,MAAM;UACZjE,IAAI,EAAE6D,MAAI,CAAC7D;SACZ,CAAC;QAEF,IAAI+D,kBAAkB,EAAE;UACtB,MAAMA,kBAAkB;QAC1B;QACF,IAAIA,kBAAkB,EAAE;UACpB,MAAMA,kBAAkB;QAC1B;QACE,IAAIG,OAAO,GAAG;UACdzD,MAAM,EAAEA,MAAM,GAAG,GAAG;UAAE;UACtB0D,QAAQ,EAAE,KAAK;UACfC,WAAW,EAAE,4BAA4B;UACzCC,cAAc,EAAEP,aAAa,CAACP;SAC/B;QACCrB,OAAO,CAACoC,GAAG,CAACJ,OAAO,CAAC;QACtB;QACA,MAAM;UAAEK,IAAI;UAAEtC;QAAK,CAAE,SAAS4B,MAAI,CAACnE,WAAW,CAAC8E,QAAQ,CAACC,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;UACjF9C,IAAI,EAACsC;SAAS,CAAC;QAEjB,IAAIjC,KAAK,EAAE;UACTC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtD,MAAM,IAAI0C,KAAK,CAAC,oCAAoC1C,KAAK,CAACe,OAAO,EAAE,CAAC;QACtE;QACEd,OAAO,CAACoC,GAAG,CAAC,yBAAyB,EAAEC,IAAI,CAAC;QAE9C,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACK,aAAa,EAAE;UAChC,MAAM,IAAID,KAAK,CAAC,wDAAwD,CAAC;QAC3E;QAEA,MAAME,YAAY,GAAGN,IAAI,CAACK,aAAa;QAEvC;QACA,MAAM;UAAE3C,KAAK,EAAE6C,YAAY;UAAEC;QAAa,CAAE,SAASlB,MAAI,CAAC9D,MAAM,CAACiF,kBAAkB,CAACH,YAAY,EAAE;UAChGR,cAAc,EAAEP,aAAa,CAACP;SAC/B,CAAC;QAEF,IAAIuB,YAAY,EAAE;UAChB,MAAMA,YAAY;QACpB;QAEA;QACAjB,MAAI,CAACzD,aAAa,GAAG;UACnB6E,OAAO,EAAE,IAAI;UACbF,aAAa,EAAEA;SAChB;QAED;QACA,MAAMlB,MAAI,CAACrE,WAAW,CAAC8D,UAAU,CAACO,MAAI,CAAClE,IAAI,CAAC4D,EAAE,EAAE;UAC9CG,cAAc,EAAE,MAAM;UACtBwB,UAAU,EAAEH,aAAa,CAACxB,EAAE;UAC5B9C,MAAM,EAAEA;SACT,CAAC;QAEFoD,MAAI,CAACpE,QAAQ,CAACqC,IAAI,CAAC,iCAAiC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAElF;QACA8B,MAAI,CAAChE,gBAAgB,CAACsF,IAAI,EAAE;QAE5B;QACA;QACA;QACA;QACA;MAGF,CAAC,CAAC,OAAOlD,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QAEjD4B,MAAI,CAACzD,aAAa,GAAG;UACnB6E,OAAO,EAAE,KAAK;UACdhD,KAAK,EAAE;YACLe,OAAO,EAAEf,KAAK,CAACe,OAAO,IAAI;;SAE7B;QAEDa,MAAI,CAACpE,QAAQ,CAACqC,IAAI,CAAC,kBAAkBG,KAAK,CAACe,OAAO,EAAE,EAAE,OAAO,EAAE;UAAEjB,QAAQ,EAAE;QAAI,CAAE,CAAC;MACpF,CAAC,SAAS;QACR8B,MAAI,CAAC/D,UAAU,GAAG,KAAK;MACzB;IAAC;EACH;EAEAsF,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzF,IAAI,EAAE,OAAO,GAAG;IAC1B,OAAO,CAAC,IAAI,CAACA,IAAI,CAACc,MAAM,IAAI,IAAI,CAACd,IAAI,CAACe,IAAI,IAAI,IAAI,CAACwC,eAAe,EAAE,EAAEmC,QAAQ,EAAE;EAClF;EAEMC,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/E,iBAAA;MACjB,IAAI,CAAC+E,MAAI,CAAC5F,IAAI,IAAI,CAAC4F,MAAI,CAAC5B,gBAAgB,EAAE,EAAE;MAE5C4B,MAAI,CAACzF,UAAU,GAAG,IAAI;MACtByF,MAAI,CAACnF,aAAa,GAAG,IAAI;MAEzB,IAAI;QACF;QACA,MAAM6E,OAAO,SAASM,MAAI,CAAChG,cAAc,CAACiG,aAAa,CAACD,MAAI,CAAC5F,IAAI,CAAC4D,EAAE,CAAC;QAErE,IAAI0B,OAAO,EAAE;UACXM,MAAI,CAACnF,aAAa,GAAG;YACnB6E,OAAO,EAAE,IAAI;YACbQ,MAAM,EAAE;WACT;UAEDF,MAAI,CAAC9F,QAAQ,CAACqC,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UAEjF;UACAwD,MAAI,CAAC1F,gBAAgB,CAACsF,IAAI,EAAE;UAE5B;UACA;UACA;UACA;UACA;QACF,CAAC,MAAM;UACLI,MAAI,CAACnF,aAAa,GAAG;YACnB6E,OAAO,EAAE,KAAK;YACdQ,MAAM,EAAE,IAAI;YACZxD,KAAK,EAAE;cACLe,OAAO,EAAE;;WAEZ;UAEDuC,MAAI,CAAC9F,QAAQ,CAACqC,IAAI,CAAC,0CAA0C,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;QAC7F;MACF,CAAC,CAAC,OAAOE,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAEhDsD,MAAI,CAACnF,aAAa,GAAG;UACnB6E,OAAO,EAAE,KAAK;UACdQ,MAAM,EAAE,IAAI;UACZxD,KAAK,EAAE;YACLe,OAAO,EAAEf,KAAK,CAACe,OAAO,IAAI;;SAE7B;QAEDuC,MAAI,CAAC9F,QAAQ,CAACqC,IAAI,CAAC,iBAAiBG,KAAK,CAACe,OAAO,EAAE,EAAE,OAAO,EAAE;UAAEjB,QAAQ,EAAE;QAAI,CAAE,CAAC;MACnF,CAAC,SAAS;QACRwD,MAAI,CAACzF,UAAU,GAAG,KAAK;MACzB;IAAC;EACH;;;;;;;;;;;;YA7TC1B;IAAK;;YACLC,SAAS;MAAAqH,IAAA,GAAC,aAAa;IAAA;;YACvBpH;IAAM;;;AAHIgB,oBAAoB,GAAAqG,UAAA,EAjQhCxH,SAAS,CAAC;EACTyH,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtH,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,wBAAwB,EACxBG,eAAe,EACfD,iBAAiB,EACjBE,gBAAgB,CACjB;EACD+G,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAsFT;;CA6JF,CAAC,C,EACWzG,oBAAoB,CA+ThC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}