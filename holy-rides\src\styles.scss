/* You can add global styles to this file, and also import other style files */

@use '@angular/material' as mat;

// Import pre-built themes
@import '@angular/material/prebuilt-themes/indigo-pink.css';

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;

background: url('/assets/hr.jpg');
background-position-x: -210px;
}

.mat-mdc-form-field-icon-prefix {
  margin-left: 10px;
}

.mat-toolbar.mat-primary {
  --mat-toolbar-container-background-color: #222CE0;
  --mat-toolbar-container-text-color: white;
}


.header .mat-icon {
  
  color: #ffffff !important;

}
.mat-icon{
  color: #222CE0
}