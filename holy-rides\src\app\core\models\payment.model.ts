/**
 * Interface for creating a payment with Square API
 */
export interface CreatePaymentRequest {
  /** The payment source token obtained from Square's Web Payments SDK */
  sourceId: string;
  
  /** A unique key to prevent duplicate payments */
  idempotencyKey: string;
  
  /** The amount and currency of the payment */
  amountMoney: {
    /** The amount in the smallest currency unit (e.g., cents for USD) */
    amount: bigint;
    /** The currency code (e.g., 'USD') */
    currency: string;
  };
  
  /** The location ID where the payment is being processed */
  locationId: string;
  
  /** Test card details for sandbox testing */
  testPayment?: {
    /** The test card scenario to simulate */
    testCard: string;
  };
}