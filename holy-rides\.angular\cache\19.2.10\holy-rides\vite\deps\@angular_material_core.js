import {
  AnimationCurves,
  AnimationDurations,
  MAT_NATIVE_DATE_FORMATS,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter
} from "./chunk-2RURFSAE.js";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY
} from "./chunk-HVQWQILN.js";
import {
  MatLine,
  MatLineModule,
  setLines
} from "./chunk-EWDFWH4P.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-V56HCWHU.js";
import {
  MatPseudoCheckbox,
  MatPseudoCheckboxModule
} from "./chunk-UQO46AZZ.js";
import {
  _MatInternalFormField
} from "./chunk-TNXNUD6X.js";
import {
  <PERSON>rrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-MMTDSRN6.js";
import {
  MatRippleLoader
} from "./chunk-POPXR7KG.js";
import {
  MatRippleModule
} from "./chunk-TZQICYM5.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-C7RGEYGR.js";
import "./chunk-EGCTXELW.js";
import "./chunk-42FJBLFI.js";
import "./chunk-JXBCBRYI.js";
import {
  _StructuralStylesLoader
} from "./chunk-CQUUUE3M.js";
import "./chunk-2O4WY5GE.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-U55PHZQP.js";
import "./chunk-JPFBSXTV.js";
import "./chunk-IXETMNAU.js";
import "./chunk-LO5N6AV4.js";
import "./chunk-DH3MKIG7.js";
import "./chunk-BKRENP7J.js";
import "./chunk-PF3VI5CT.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-S35DAJRX.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
