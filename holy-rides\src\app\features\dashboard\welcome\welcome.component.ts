import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-welcome',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    RouterLink
  ],
  template: `
    <div class="welcome-container">
      <!-- <div class="logo-container">
        <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">
      </div> -->
      <mat-card class="welcome-card">
        <mat-card-header>
          <mat-card-title>Welcome to Holy Rides</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>Your trusted ride-sharing platform for the community.</p>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" routerLink="/auth/login">Login</button>
          <button mat-button routerLink="/auth/register">Register</button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .welcome-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30px;
    }

    .logo {
      width: 120px;
      margin-bottom: 10px;
      filter: brightness(0) invert(1); /* Make logo white */
    }

    .welcome-card {
      width: 100%;
      max-width: 500px;
      padding: 20px;
      text-align: center;
    }

    mat-card-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      padding: 16px;
    }
  `]
})
export class WelcomeComponent {}
