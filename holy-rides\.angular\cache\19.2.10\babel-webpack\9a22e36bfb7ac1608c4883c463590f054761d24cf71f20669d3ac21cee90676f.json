{"ast": null, "code": "import GoTrueClient from './GoTrueClient';\nconst AuthClient = GoTrueClient;\nexport default AuthClient;", "map": {"version": 3, "names": ["GoTrueClient", "AuthClient"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/AuthClient.js"], "sourcesContent": ["import GoTrueClient from './GoTrueClient';\nconst AuthClient = GoTrueClient;\nexport default AuthClient;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AACzC,MAAMC,UAAU,GAAGD,YAAY;AAC/B,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}