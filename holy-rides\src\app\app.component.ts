import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterLink, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { AuthService } from './core/services/auth.service';
import { Subscription } from 'rxjs';
import { MessageNotificationComponent } from './shared/components/message-notification/message-notification.component';
import { OfflineIndicatorComponent } from './shared/components/offline-indicator/offline-indicator.component';
import { InstallPromptComponent } from './shared/components/install-prompt/install-prompt.component';
import { UpdateService } from './core/services/update.service';
import { NotificationService } from './core/services/notification.service';

@Component({
  selector: 'app-root',

  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterLink,
    MatButtonModule,
    MatToolbarModule,
    MatIconModule,
    MatCardModule,
    MessageNotificationComponent,
    OfflineIndicatorComponent,
    InstallPromptComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit, OnDestroy {
  private authSubscription: Subscription;
  private notificationSubscription: Subscription | null = null;
  private updateCheckInterval: any = null;
  showNotificationPrompt = false;
  pwaComponentsReady = false;
  title = 'holy-rides';

  constructor(
    public authService: AuthService,
    private router: Router,
    private updateService: UpdateService,
    private notificationService: NotificationService
  ) {
    // Subscribe to auth state changes but don't automatically redirect
    // This prevents conflicts with the auth guard
    this.authSubscription = this.authService.user$.subscribe((user: any) => {
      // Just track the auth state, let the guard handle redirects
      console.log('Auth state changed:', user ? 'authenticated' : 'not authenticated');

      // Re-enable notification permission check
      if (user) {
        try {
          console.log('User is logged in, checking notification permission');
          this.checkNotificationPermission();
        } catch (error) {
          console.error('Error checking notification permission:', error);
        }
      }
    });
  }

  ngOnInit(): void {
    console.log('App component initialized');

    // Re-enable PWA initialization with a more cautious approach
    try {
      // Enable offline indicator after a delay
      setTimeout(() => {
        this.pwaComponentsReady = true;
        console.log('Offline indicator enabled');
      }, 2000);

      // Re-enable update service after a longer delay
      setTimeout(() => {
        try {
          this.updateService.checkForUpdates();
          console.log('Update service initialized');

          // Set up periodic update checks
          this.updateCheckInterval = setInterval(() => {
            this.updateService.checkForUpdates();
          }, 30 * 60 * 1000); // Check every 30 minutes
        } catch (error) {
          console.error('Error setting up update checks:', error);
        }
      }, 5000);
    } catch (error) {
      console.error('Error in ngOnInit:', error);
    }
  }

  async logout() {
    try {
      await this.authService.logout();
      await this.router.navigate(['/auth/login']);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }

  async navigateToDashboard(): Promise<void> {
    try {
      const role = await this.authService.getUserRole();
      if (role) {
        await this.router.navigate([`/dashboard/${role}`]);
      } else {
        await this.router.navigate(['/auth/login']);
      }
    } catch (error) {
      console.error('Error navigating to dashboard:', error);
      await this.router.navigate(['/auth/login']);
    }
  }

  /**
   * Check if notification permission is granted and prompt if needed
   */
  checkNotificationPermission(): void {
    try {
      // Clean up previous subscription if it exists
      if (this.notificationSubscription) {
        this.notificationSubscription.unsubscribe();
      }

      // Subscribe to notification status
      this.notificationSubscription = this.notificationService.getNotificationStatus().subscribe((enabled: any) => {
        this.showNotificationPrompt = !enabled;
      });
    } catch (error) {
      console.error('Error checking notification permission:', error);
    }
  }

  /**
   * Request permission for notifications
   */
  requestNotifications(): void {
    this.notificationService.requestNotificationPermission().then(granted => {
      this.showNotificationPrompt = !granted;
    });
  }

  ngOnDestroy() {
    // Clean up auth subscription
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }

    // Re-enable other cleanup
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }

    // Clear intervals
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
    }
  }
}
