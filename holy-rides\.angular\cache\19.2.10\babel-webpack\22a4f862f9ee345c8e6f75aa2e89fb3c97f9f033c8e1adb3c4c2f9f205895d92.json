{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n  constructor(context) {\n    super(context.message);\n    this.name = 'PostgrestError';\n    this.details = context.details;\n    this.hint = context.hint;\n    this.code = context.code;\n  }\n}\nexports.default = PostgrestError;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "PostgrestError", "Error", "constructor", "context", "message", "name", "details", "hint", "code", "default"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports.default = PostgrestError;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASC,KAAK,CAAC;EAC/BC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAACC,OAAO,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,gBAAgB;IAC5B,IAAI,CAACC,OAAO,GAAGH,OAAO,CAACG,OAAO;IAC9B,IAAI,CAACC,IAAI,GAAGJ,OAAO,CAACI,IAAI;IACxB,IAAI,CAACC,IAAI,GAAGL,OAAO,CAACK,IAAI;EAC5B;AACJ;AACAV,OAAO,CAACW,OAAO,GAAGT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}