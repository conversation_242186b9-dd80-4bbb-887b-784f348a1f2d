{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZ<PERSON>, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { B as BasePortalOutlet, f as CdkPortalOutlet, C as ComponentPortal, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nexport { d as ɵɵCdkPortal, g as ɵɵPortalHostDirective, e as ɵɵTemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-BYox5gpI.mjs';\nimport { c as OverlayRef, a as Overlay, O as OverlayContainer, f as OverlayConfig, m as OverlayModule } from './overlay-module-BUj0D19H.mjs';\nimport { F as FocusMonitor } from './focus-monitor-e2l_RpN3.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer, of } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef;\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector;\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id;\n  /** The ARIA role of the dialog element. */\n  role = 'dialog';\n  /** Optional CSS class or classes applied to the overlay panel. */\n  panelClass = '';\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop = true;\n  /** Optional CSS class or classes applied to the overlay backdrop. */\n  backdropClass = '';\n  /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n  disableClose = false;\n  /** Width of the dialog. */\n  width = '';\n  /** Height of the dialog. */\n  height = '';\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth;\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight;\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n  maxWidth;\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight;\n  /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n  positionStrategy;\n  /** Data being injected into the child component. */\n  data = null;\n  /** Layout direction for the dialog's content. */\n  direction;\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy = null;\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy = null;\n  /** Dialog label applied via `aria-label` */\n  ariaLabel = null;\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal = false;\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus = 'first-tabbable';\n  /**\n   * Whether the dialog should restore focus to the previously-focused element upon closing.\n   * Has the following behavior based on the type that is passed in:\n   * - `boolean` - when true, will return focus to the element that was focused before the dialog\n   *    was opened, otherwise won't restore focus at all.\n   * - `string` - focus will be restored to the first element that matches the CSS selector.\n   * - `HTMLElement` - focus will be restored to the specific element.\n   */\n  restoreFocus = true;\n  /**\n   * Scroll strategy to be used for the dialog. This determines how\n   * the dialog responds to scrolling underneath the panel element.\n   */\n  scrollStrategy;\n  /**\n   * Whether the dialog should close when the user navigates backwards or forwards through browser\n   * history. This does not apply to navigation via anchor element unless using URL-hash based\n   * routing (`HashLocationStrategy` in the Angular router).\n   */\n  closeOnNavigation = true;\n  /**\n   * Whether the dialog should close when the dialog service is destroyed. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead.\n   */\n  closeOnDestroy = true;\n  /**\n   * Whether the dialog should close when the underlying overlay is detached. This is useful if\n   * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n   * external detachment can happen as a result of a scroll strategy triggering it or when the\n   * browser location changes.\n   */\n  closeOnOverlayDetachments = true;\n  /**\n   * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n   * @deprecated No longer used. Will be removed.\n   * @breaking-change 20.0.0\n   */\n  componentFactoryResolver;\n  /**\n   * Providers that will be exposed to the contents of the dialog. Can also\n   * be provided as a function in order to generate the providers lazily.\n   */\n  providers;\n  /**\n   * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n   * A configuration object can be passed in to customize the providers that will be exposed\n   * to the dialog container.\n   */\n  container;\n  /**\n   * Context that will be passed to template-based dialogs.\n   * A function can be passed in to resolve the context lazily.\n   */\n  templateContext;\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  _config;\n  _interactivityChecker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _overlayRef = inject(OverlayRef);\n  _focusMonitor = inject(FocusMonitor);\n  _renderer = inject(Renderer2);\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** The portal outlet inside of this container into which the dialog content will be loaded. */\n  _portalOutlet;\n  /** The class that traps and manages focus within the dialog. */\n  _focusTrap = null;\n  /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n  _elementFocusedBeforeDialogWasOpened = null;\n  /**\n   * Type of interaction that led to the dialog being closed. This is used to determine\n   * whether the focus style will be applied when returning focus to its original location\n   * after the dialog is closed.\n   */\n  _closeInteractionType = null;\n  /**\n   * Queue of the IDs of the dialog's label element, based on their definition order. The first\n   * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n   * where there are two or more titles in the DOM at a time and the first one is destroyed while\n   * the rest are present.\n   */\n  _ariaLabelledByQueue = [];\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _injector = inject(Injector);\n  _isDestroyed = false;\n  constructor() {\n    super();\n    // Callback is primarily for some internal tests\n    // that were instantiating the dialog container manually.\n    this._config = inject(DialogConfig, {\n      optional: true\n    }) || new DialogConfig();\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n  _addAriaLabelledBy(id) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n  _removeAriaLabelledBy(id) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._isDestroyed = true;\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attaches a DOM portal to the dialog container.\n   * @param portal Portal to be attached.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  attachDomPortal = portal => {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._contentAttached();\n    return result;\n  };\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          deregisterBlur();\n          deregisterMousedown();\n          element.removeAttribute('tabindex');\n        };\n        const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n        const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus(options) {\n    if (this._isDestroyed) {\n      return;\n    }\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait until after the next render.\n    afterNextRender(() => {\n      const element = this._elementRef.nativeElement;\n      switch (this._config.autoFocus) {\n        case false:\n        case 'dialog':\n          // Ensure that focus is on the dialog container. It's possible that a different\n          // component tried to move focus while the open animation was running. See:\n          // https://github.com/angular/components/issues/16215. Note that we only want to do this\n          // if the focus isn't inside the dialog already, because it's possible that the consumer\n          // turned off `autoFocus` in order to move focus themselves.\n          if (!this._containsFocus()) {\n            element.focus(options);\n          }\n          break;\n        case true:\n        case 'first-tabbable':\n          const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer(options);\n          }\n          break;\n        case 'first-heading':\n          this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n          break;\n        default:\n          this._focusByCssSelector(this._config.autoFocus, options);\n          break;\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer(options) {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n  static ɵfac = function CdkDialogContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkDialogContainer)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: CdkDialogContainer,\n    selectors: [[\"cdk-dialog-container\"]],\n    viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n      }\n    },\n    hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n    hostVars: 6,\n    hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n      }\n    },\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 1,\n    vars: 0,\n    consts: [[\"cdkPortalOutlet\", \"\"]],\n    template: function CdkDialogContainer_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n      }\n    },\n    dependencies: [CdkPortalOutlet],\n    styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet />\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"]\n    }]\n  }], () => [], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  overlayRef;\n  config;\n  /**\n   * Instance of component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentInstance;\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  componentRef;\n  /** Instance of the container that is rendering out the dialog content. */\n  containerInstance;\n  /** Whether the user is allowed to close the dialog. */\n  disableClose;\n  /** Emits when the dialog has been closed. */\n  closed = new Subject();\n  /** Emits when the backdrop of the dialog is clicked. */\n  backdropClick;\n  /** Emits when on keyboard events within the dialog. */\n  keydownEvents;\n  /** Emits on pointer events that happen outside of the dialog. */\n  outsidePointerEvents;\n  /** Unique ID for the dialog. */\n  id;\n  /** Subscription to external detachments of the dialog. */\n  _detachSubscription;\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass Dialog {\n  _overlay = inject(Overlay);\n  _injector = inject(Injector);\n  _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, {\n    optional: true\n  });\n  _parentDialog = inject(Dialog, {\n    optional: true,\n    skipSelf: true\n  });\n  _overlayContainer = inject(OverlayContainer);\n  _idGenerator = inject(_IdGenerator);\n  _openDialogsAtThisLevel = [];\n  _afterAllClosedAtThisLevel = new Subject();\n  _afterOpenedAtThisLevel = new Subject();\n  _ariaHiddenElements = new Map();\n  _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n  constructor() {}\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }));\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  static ɵfac = function Dialog_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dialog)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Dialog,\n    factory: Dialog.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {\n  static ɵfac = function DialogModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DialogModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DialogModule,\n    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n    exports: [\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule, CdkDialogContainer]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Dialog],\n    imports: [OverlayModule, PortalModule, A11yModule,\n    // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n    // don't have to remember to import it or be faced with an unhelpful error.\n    PortalModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      providers: [Dialog]\n    }]\n  }], null, null);\n})();\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "inject", "ElementRef", "NgZone", "Renderer2", "ChangeDetectorRef", "Injector", "afterNextRender", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "InjectionToken", "TemplateRef", "Injectable", "NgModule", "B", "BasePortalOutlet", "f", "CdkPortalOutlet", "C", "ComponentPortal", "T", "TemplatePortal", "h", "PortalModule", "CdkDialogContainer_ng_template_0_Template", "rf", "ctx", "d", "ɵɵCdkPortal", "g", "ɵɵPortalHostDirective", "e", "ɵɵTemplatePortalDirective", "F", "FocusTrapFactory", "I", "InteractivityChecker", "A", "A11yModule", "c", "OverlayRef", "a", "Overlay", "O", "OverlayContainer", "OverlayConfig", "m", "OverlayModule", "FocusMonitor", "P", "Platform", "_getFocusedElementPierceShadowDom", "Subject", "defer", "of", "ESCAPE", "hasModifierKey", "startWith", "_", "_IdGenerator", "D", "Directionality", "DialogConfig", "viewContainerRef", "injector", "id", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "positionStrategy", "data", "direction", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "scrollStrategy", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "componentFactoryResolver", "providers", "container", "templateContext", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_overlayRef", "_focusMonitor", "_renderer", "_platform", "_document", "optional", "_portalOutlet", "_focusTrap", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "_ariaLabelledByQueue", "_changeDetectorRef", "_injector", "_isDestroyed", "constructor", "push", "_addAriaLabelledBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_removeAriaLabelledBy", "index", "indexOf", "splice", "_contentAttached", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "portal", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "attachTemplatePortal", "attachDomPortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "deregisterBlur", "deregisterMousedown", "removeAttribute", "listen", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "focusedSuccessfully", "focusInitialElement", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "create", "backdropClick", "subscribe", "ɵfac", "CdkDialogContainer_Factory", "__ngFactoryType__", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "CdkDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkDialogContainer_HostBindings", "ɵɵattribute", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CdkDialogContainer_Template", "ɵɵtemplate", "dependencies", "styles", "encapsulation", "ɵsetClassMetadata", "args", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "static", "DialogRef", "overlayRef", "config", "componentInstance", "componentRef", "containerInstance", "closed", "keydownEvents", "outsidePointerEvents", "_detachSubscription", "event", "keyCode", "preventDefault", "close", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "detachments", "closedSubject", "unsubscribe", "dispose", "next", "complete", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "block", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "Dialog", "_overlay", "_defaultOptions", "_parentDialog", "skipSelf", "_overlayContainer", "_idGenerator", "_openDialogsAtThisLevel", "_afterAllClosedAtThisLevel", "_afterOpenedAtThisLevel", "_ariaHiddenElements", "Map", "_scrollStrategy", "openDialogs", "afterOpened", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "open", "componentOrTemplateRef", "defaults", "getId", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "position", "global", "centerHorizontally", "centerVertically", "disposeOnNavigation", "userInjector", "useValue", "containerType", "containerPortal", "parent", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "contentRef", "fallbackInjector", "get", "value", "change", "emitEvent", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "Dialog_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "items", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector", "ɵɵCdkPortalOutlet"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZ<PERSON>, Renderer2, ChangeDetectorRef, Injector, afterNextRender, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, InjectionToken, TemplateRef, Injectable, NgModule } from '@angular/core';\nimport { B as BasePortalOutlet, f as CdkPortalOutlet, C as ComponentPortal, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nexport { d as ɵɵCdkPortal, g as ɵɵPortalHostDirective, e as ɵɵTemplatePortalDirective } from './portal-directives-Bw5woq8I.mjs';\nimport { F as FocusTrapFactory, I as InteractivityChecker, A as A11yModule } from './a11y-module-BYox5gpI.mjs';\nimport { c as OverlayRef, a as Overlay, O as OverlayContainer, f as OverlayConfig, m as OverlayModule } from './overlay-module-BUj0D19H.mjs';\nimport { F as FocusMonitor } from './focus-monitor-e2l_RpN3.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as _getFocusedElementPierceShadowDom } from './shadow-dom-B0oHn41l.mjs';\nimport { Subject, defer, of } from 'rxjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { startWith } from 'rxjs/operators';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport './style-loader-Cu9AvjH9.mjs';\nimport './private.mjs';\nimport './breakpoints-observer-CljOfYGy.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './observers.mjs';\nimport './element-x4z00URv.mjs';\nimport './backwards-compatibility-DHR38MsD.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './scrolling.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport './bidi.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\nimport './fake-event-detection-DWOdFTFz.mjs';\nimport './passive-listeners-esHZRgIN.mjs';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    /**\n     * Where the attached component should live in Angular's *logical* component tree.\n     * This affects what is available for injection and the change detection order for the\n     * component instantiated inside of the dialog. This does not affect where the dialog\n     * content will be rendered.\n     */\n    viewContainerRef;\n    /**\n     * Injector used for the instantiation of the component to be attached. If provided,\n     * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n     */\n    injector;\n    /** ID for the dialog. If omitted, a unique one will be generated. */\n    id;\n    /** The ARIA role of the dialog element. */\n    role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    disableClose = false;\n    /** Width of the dialog. */\n    width = '';\n    /** Height of the dialog. */\n    height = '';\n    /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n    minWidth;\n    /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n    minHeight;\n    /** Max-width of the dialog. If a number is provided, assumes pixel units. Defaults to 80vw. */\n    maxWidth;\n    /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n    maxHeight;\n    /** Strategy to use when positioning the dialog. Defaults to centering it on the page. */\n    positionStrategy;\n    /** Data being injected into the child component. */\n    data = null;\n    /** Layout direction for the dialog's content. */\n    direction;\n    /** ID of the element that describes the dialog. */\n    ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    ariaLabel = null;\n    /**\n     * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n     * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n     * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n     */\n    ariaModal = false;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    restoreFocus = true;\n    /**\n     * Scroll strategy to be used for the dialog. This determines how\n     * the dialog responds to scrolling underneath the panel element.\n     */\n    scrollStrategy;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    closeOnOverlayDetachments = true;\n    /**\n     * Alternate `ComponentFactoryResolver` to use when resolving the associated component.\n     * @deprecated No longer used. Will be removed.\n     * @breaking-change 20.0.0\n     */\n    componentFactoryResolver;\n    /**\n     * Providers that will be exposed to the contents of the dialog. Can also\n     * be provided as a function in order to generate the providers lazily.\n     */\n    providers;\n    /**\n     * Component into which the dialog content will be rendered. Defaults to `CdkDialogContainer`.\n     * A configuration object can be passed in to customize the providers that will be exposed\n     * to the dialog container.\n     */\n    container;\n    /**\n     * Context that will be passed to template-based dialogs.\n     * A function can be passed in to resolve the context lazily.\n     */\n    templateContext;\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    _elementRef = inject(ElementRef);\n    _focusTrapFactory = inject(FocusTrapFactory);\n    _config;\n    _interactivityChecker = inject(InteractivityChecker);\n    _ngZone = inject(NgZone);\n    _overlayRef = inject(OverlayRef);\n    _focusMonitor = inject(FocusMonitor);\n    _renderer = inject(Renderer2);\n    _platform = inject(Platform);\n    _document = inject(DOCUMENT, { optional: true });\n    /** The portal outlet inside of this container into which the dialog content will be loaded. */\n    _portalOutlet;\n    /** The class that traps and manages focus within the dialog. */\n    _focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    _elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    _closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    _ariaLabelledByQueue = [];\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _injector = inject(Injector);\n    _isDestroyed = false;\n    constructor() {\n        super();\n        // Callback is primarily for some internal tests\n        // that were instantiating the dialog container manually.\n        this._config = (inject(DialogConfig, { optional: true }) || new DialogConfig());\n        if (this._config.ariaLabelledBy) {\n            this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n        }\n    }\n    _addAriaLabelledBy(id) {\n        this._ariaLabelledByQueue.push(id);\n        this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n        const index = this._ariaLabelledByQueue.indexOf(id);\n        if (index > -1) {\n            this._ariaLabelledByQueue.splice(index, 1);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._isDestroyed = true;\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    attachDomPortal = (portal) => {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachDomPortal(portal);\n        this._contentAttached();\n        return result;\n    };\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    deregisterBlur();\n                    deregisterMousedown();\n                    element.removeAttribute('tabindex');\n                };\n                const deregisterBlur = this._renderer.listen(element, 'blur', callback);\n                const deregisterMousedown = this._renderer.listen(element, 'mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus(options) {\n        if (this._isDestroyed) {\n            return;\n        }\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait until after the next render.\n        afterNextRender(() => {\n            const element = this._elementRef.nativeElement;\n            switch (this._config.autoFocus) {\n                case false:\n                case 'dialog':\n                    // Ensure that focus is on the dialog container. It's possible that a different\n                    // component tried to move focus while the open animation was running. See:\n                    // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                    // if the focus isn't inside the dialog already, because it's possible that the consumer\n                    // turned off `autoFocus` in order to move focus themselves.\n                    if (!this._containsFocus()) {\n                        element.focus(options);\n                    }\n                    break;\n                case true:\n                case 'first-tabbable':\n                    const focusedSuccessfully = this._focusTrap?.focusInitialElement(options);\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer(options);\n                    }\n                    break;\n                case 'first-heading':\n                    this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]', options);\n                    break;\n                default:\n                    this._focusByCssSelector(this._config.autoFocus, options);\n                    break;\n            }\n        }, { injector: this._injector });\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer(options) {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus(options);\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            // Save the previously focused element. This element will be re-focused\n            // when the dialog closes.\n            if (this._document) {\n                this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n            }\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkDialogContainer, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkDialogContainer, isStandalone: true, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, imports: [CdkPortalOutlet], host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    overlayRef;\n    config;\n    /**\n     * Instance of component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentInstance;\n    /**\n     * `ComponentRef` of the component opened into the dialog. Will be\n     * null when the dialog is opened using a `TemplateRef`.\n     */\n    componentRef;\n    /** Instance of the container that is rendering out the dialog content. */\n    containerInstance;\n    /** Whether the user is allowed to close the dialog. */\n    disableClose;\n    /** Emits when the dialog has been closed. */\n    closed = new Subject();\n    /** Emits when the backdrop of the dialog is clicked. */\n    backdropClick;\n    /** Emits when on keyboard events within the dialog. */\n    keydownEvents;\n    /** Emits on pointer events that happen outside of the dialog. */\n    outsidePointerEvents;\n    /** Unique ID for the dialog. */\n    id;\n    /** Subscription to external detachments of the dialog. */\n    _detachSubscription;\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.block();\n    },\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass Dialog {\n    _overlay = inject(Overlay);\n    _injector = inject(Injector);\n    _defaultOptions = inject(DEFAULT_DIALOG_CONFIG, { optional: true });\n    _parentDialog = inject(Dialog, { optional: true, skipSelf: true });\n    _overlayContainer = inject(OverlayContainer);\n    _idGenerator = inject(_IdGenerator);\n    _openDialogsAtThisLevel = [];\n    _afterAllClosedAtThisLevel = new Subject();\n    _afterOpenedAtThisLevel = new Subject();\n    _ariaHiddenElements = new Map();\n    _scrollStrategy = inject(DIALOG_SCROLL_STRATEGY);\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    afterAllClosed = defer(() => this.openDialogs.length\n        ? this._getAfterAllClosed()\n        : this._getAfterAllClosed().pipe(startWith(undefined)));\n    constructor() { }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || this._idGenerator.getId('cdk-dialog-');\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }));\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, providers: [Dialog], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    providers: [Dialog],\n                }]\n        }] });\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError, CdkPortalOutlet as ɵɵCdkPortalOutlet };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AACxO,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AAAC,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AAC7J,SAASC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,yBAAyB,QAAQ,kCAAkC;AAC/H,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC9G,SAASC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,gBAAgB,EAAE5B,CAAC,IAAI6B,aAAa,EAAEC,CAAC,IAAIC,aAAa,QAAQ,+BAA+B;AAC5I,SAASd,CAAC,IAAIe,YAAY,QAAQ,8BAA8B;AAChE,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASX,CAAC,IAAIY,iCAAiC,QAAQ,2BAA2B;AAClF,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzC,SAASzB,CAAC,IAAI0B,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,OAAO,6BAA6B;AACpC,OAAO,eAAe;AACtB,OAAO,qCAAqC;AAC5C,OAAO,sBAAsB;AAC7B,OAAO,iBAAiB;AACxB,OAAO,wBAAwB;AAC/B,OAAO,wCAAwC;AAC/C,OAAO,iCAAiC;AACxC,OAAO,gCAAgC;AACvC,OAAO,iBAAiB;AACxB,OAAO,0BAA0B;AACjC,OAAO,YAAY;AACnB,OAAO,+CAA+C;AACtD,OAAO,4BAA4B;AACnC,OAAO,qCAAqC;AAC5C,OAAO,kCAAkC;;AAEzC;AACA,MAAMC,YAAY,CAAC;EACf;AACJ;AACA;AACA;AACA;AACA;EACIC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,QAAQ;EACR;EACAC,EAAE;EACF;EACAC,IAAI,GAAG,QAAQ;EACf;EACAC,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,IAAI;EAClB;EACAC,aAAa,GAAG,EAAE;EAClB;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,KAAK,GAAG,EAAE;EACV;EACAC,MAAM,GAAG,EAAE;EACX;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,gBAAgB;EAChB;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,SAAS;EACT;EACAC,eAAe,GAAG,IAAI;EACtB;EACAC,cAAc,GAAG,IAAI;EACrB;EACAC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;EACIC,SAAS,GAAG,gBAAgB;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB;AACJ;AACA;AACA;EACIC,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;AACA;AACA;EACIC,yBAAyB,GAAG,IAAI;EAChC;AACJ;AACA;AACA;AACA;EACIC,wBAAwB;EACxB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,eAAe;AACnB;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASjF,gBAAgB,CAAC;EAC9CkF,WAAW,GAAGlG,MAAM,CAACC,UAAU,CAAC;EAChCkG,iBAAiB,GAAGnG,MAAM,CAACmC,gBAAgB,CAAC;EAC5CiE,OAAO;EACPC,qBAAqB,GAAGrG,MAAM,CAACqC,oBAAoB,CAAC;EACpDiE,OAAO,GAAGtG,MAAM,CAACE,MAAM,CAAC;EACxBqG,WAAW,GAAGvG,MAAM,CAACyC,UAAU,CAAC;EAChC+D,aAAa,GAAGxG,MAAM,CAACiD,YAAY,CAAC;EACpCwD,SAAS,GAAGzG,MAAM,CAACG,SAAS,CAAC;EAC7BuG,SAAS,GAAG1G,MAAM,CAACmD,QAAQ,CAAC;EAC5BwD,SAAS,GAAG3G,MAAM,CAACF,QAAQ,EAAE;IAAE8G,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChD;EACAC,aAAa;EACb;EACAC,UAAU,GAAG,IAAI;EACjB;EACAC,oCAAoC,GAAG,IAAI;EAC3C;AACJ;AACA;AACA;AACA;EACIC,qBAAqB,GAAG,IAAI;EAC5B;AACJ;AACA;AACA;AACA;AACA;EACIC,oBAAoB,GAAG,EAAE;EACzBC,kBAAkB,GAAGlH,MAAM,CAACI,iBAAiB,CAAC;EAC9C+G,SAAS,GAAGnH,MAAM,CAACK,QAAQ,CAAC;EAC5B+G,YAAY,GAAG,KAAK;EACpBC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP;IACA;IACA,IAAI,CAACjB,OAAO,GAAIpG,MAAM,CAAC+D,YAAY,EAAE;MAAE6C,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,IAAI7C,YAAY,CAAC,CAAE;IAC/E,IAAI,IAAI,CAACqC,OAAO,CAAClB,cAAc,EAAE;MAC7B,IAAI,CAAC+B,oBAAoB,CAACK,IAAI,CAAC,IAAI,CAAClB,OAAO,CAAClB,cAAc,CAAC;IAC/D;EACJ;EACAqC,kBAAkBA,CAACrD,EAAE,EAAE;IACnB,IAAI,CAAC+C,oBAAoB,CAACK,IAAI,CAACpD,EAAE,CAAC;IAClC,IAAI,CAACgD,kBAAkB,CAACM,YAAY,CAAC,CAAC;EAC1C;EACAC,qBAAqBA,CAACvD,EAAE,EAAE;IACtB,MAAMwD,KAAK,GAAG,IAAI,CAACT,oBAAoB,CAACU,OAAO,CAACzD,EAAE,CAAC;IACnD,IAAIwD,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACT,oBAAoB,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,CAACR,kBAAkB,CAACM,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAK,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAACe,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAACC,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACxB,aAAa,CAACyB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFxC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMyC,MAAM,GAAG,IAAI,CAAC3B,aAAa,CAACuB,qBAAqB,CAACC,MAAM,CAAC;IAC/D,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACvB,OAAOW,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIC,oBAAoBA,CAACJ,MAAM,EAAE;IACzB,IAAI,IAAI,CAACxB,aAAa,CAACyB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFxC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMyC,MAAM,GAAG,IAAI,CAAC3B,aAAa,CAAC4B,oBAAoB,CAACJ,MAAM,CAAC;IAC9D,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACvB,OAAOW,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,eAAe,GAAIL,MAAM,IAAK;IAC1B,IAAI,IAAI,CAACxB,aAAa,CAACyB,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFxC,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMyC,MAAM,GAAG,IAAI,CAAC3B,aAAa,CAAC6B,eAAe,CAACL,MAAM,CAAC;IACzD,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACvB,OAAOW,MAAM;EACjB,CAAC;EACD;EACA;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACxB,IAAI,CAACX,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIY,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC1C,qBAAqB,CAAC2C,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAAC3C,OAAO,CAAC4C,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACnBC,cAAc,CAAC,CAAC;UAChBC,mBAAmB,CAAC,CAAC;UACrBP,OAAO,CAACQ,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACD,MAAMF,cAAc,GAAG,IAAI,CAAC3C,SAAS,CAAC8C,MAAM,CAACT,OAAO,EAAE,MAAM,EAAEK,QAAQ,CAAC;QACvE,MAAME,mBAAmB,GAAG,IAAI,CAAC5C,SAAS,CAAC8C,MAAM,CAACT,OAAO,EAAE,WAAW,EAAEK,QAAQ,CAAC;MACrF,CAAC,CAAC;IACN;IACAL,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIU,mBAAmBA,CAACC,QAAQ,EAAEX,OAAO,EAAE;IACnC,IAAIY,cAAc,GAAG,IAAI,CAACzD,WAAW,CAAC0D,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACd,WAAW,CAACc,cAAc,EAAEZ,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACId,UAAUA,CAACc,OAAO,EAAE;IAChB,IAAI,IAAI,CAAC3B,YAAY,EAAE;MACnB;IACJ;IACA;IACA;IACA;IACA9G,eAAe,CAAC,MAAM;MAClB,MAAMwI,OAAO,GAAG,IAAI,CAAC5C,WAAW,CAAC0D,aAAa;MAC9C,QAAQ,IAAI,CAACxD,OAAO,CAACf,SAAS;QAC1B,KAAK,KAAK;QACV,KAAK,QAAQ;UACT;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAACuD,cAAc,CAAC,CAAC,EAAE;YACxBE,OAAO,CAACU,KAAK,CAACT,OAAO,CAAC;UAC1B;UACA;QACJ,KAAK,IAAI;QACT,KAAK,gBAAgB;UACjB,MAAMe,mBAAmB,GAAG,IAAI,CAAChD,UAAU,EAAEiD,mBAAmB,CAAChB,OAAO,CAAC;UACzE;UACA;UACA,IAAI,CAACe,mBAAmB,EAAE;YACtB,IAAI,CAACE,qBAAqB,CAACjB,OAAO,CAAC;UACvC;UACA;QACJ,KAAK,eAAe;UAChB,IAAI,CAACU,mBAAmB,CAAC,0CAA0C,EAAEV,OAAO,CAAC;UAC7E;QACJ;UACI,IAAI,CAACU,mBAAmB,CAAC,IAAI,CAACrD,OAAO,CAACf,SAAS,EAAE0D,OAAO,CAAC;UACzD;MACR;IACJ,CAAC,EAAE;MAAE9E,QAAQ,EAAE,IAAI,CAACkD;IAAU,CAAC,CAAC;EACpC;EACA;EACAgB,aAAaA,CAAA,EAAG;IACZ,MAAM8B,WAAW,GAAG,IAAI,CAAC7D,OAAO,CAACd,YAAY;IAC7C,IAAI4E,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjCC,kBAAkB,GAAG,IAAI,CAACvD,SAAS,CAACkD,aAAa,CAACI,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAAClD,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIkD,WAAW,EAAE;MAClBC,kBAAkB,GAAGD,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAAC7D,OAAO,CAACd,YAAY,IACzB4E,kBAAkB,IAClB,OAAOA,kBAAkB,CAACV,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMW,aAAa,GAAG/G,iCAAiC,CAAC,CAAC;MACzD,MAAM0F,OAAO,GAAG,IAAI,CAAC5C,WAAW,CAAC0D,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACO,aAAa,IACdA,aAAa,KAAK,IAAI,CAACxD,SAAS,CAACyD,IAAI,IACrCD,aAAa,KAAKrB,OAAO,IACzBA,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAAC3D,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAAC8D,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAAClD,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDkD,kBAAkB,CAACV,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAAC1C,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACyD,OAAO,CAAC,CAAC;IAC7B;EACJ;EACA;EACAP,qBAAqBA,CAACjB,OAAO,EAAE;IAC3B;IACA,IAAI,IAAI,CAAC7C,WAAW,CAAC0D,aAAa,CAACJ,KAAK,EAAE;MACtC,IAAI,CAACtD,WAAW,CAAC0D,aAAa,CAACJ,KAAK,CAACT,OAAO,CAAC;IACjD;EACJ;EACA;EACAH,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAAC5C,WAAW,CAAC0D,aAAa;IAC9C,MAAMO,aAAa,GAAG/G,iCAAiC,CAAC,CAAC;IACzD,OAAO0F,OAAO,KAAKqB,aAAa,IAAIrB,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACArC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpB,SAAS,CAAC8D,SAAS,EAAE;MAC1B,IAAI,CAAC1D,UAAU,GAAG,IAAI,CAACX,iBAAiB,CAACsE,MAAM,CAAC,IAAI,CAACvE,WAAW,CAAC0D,aAAa,CAAC;MAC/E;MACA;MACA,IAAI,IAAI,CAACjD,SAAS,EAAE;QAChB,IAAI,CAACI,oCAAoC,GAAG3D,iCAAiC,CAAC,CAAC;MACnF;IACJ;EACJ;EACA;EACA2E,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,CAACxB,WAAW,CAACmE,aAAa,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MAC7C,IAAI,IAAI,CAACvE,OAAO,CAAC7B,YAAY,EAAE;QAC3B,IAAI,CAACoE,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACA,OAAOiC,IAAI,YAAAC,2BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF7E,kBAAkB;EAAA;EACrH,OAAO8E,IAAI,kBAD8EhL,EAAE,CAAAiL,iBAAA;IAAAC,IAAA,EACJhF,kBAAkB;IAAAiF,SAAA;IAAAC,SAAA,WAAAC,yBAAA1J,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADhB3B,EAAE,CAAAsL,WAAA,CACygBnK,eAAe;MAAA;MAAA,IAAAQ,EAAA;QAAA,IAAA4J,EAAA;QAD1hBvL,EAAE,CAAAwL,cAAA,CAAAD,EAAA,GAAFvL,EAAE,CAAAyL,WAAA,QAAA7J,GAAA,CAAAkF,aAAA,GAAAyE,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA,eACwG,IAAI;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAAnK,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD9G3B,EAAE,CAAA+L,WAAA,OAAAnK,GAAA,CAAAyE,OAAA,CAAAlC,EAAA,IACU,IAAI,UAAAvC,GAAA,CAAAyE,OAAA,CAAAjC,IAAA,gBAAAxC,GAAA,CAAAyE,OAAA,CAAAhB,SAAA,qBAAAzD,GAAA,CAAAyE,OAAA,CAAAjB,SAAA,GAAE,IAAI,GAAAxD,GAAA,CAAAsF,oBAAA,CAAwB,CAAC,iBAAAtF,GAAA,CAAAyE,OAAA,CAAAjB,SAAA,sBAAAxD,GAAA,CAAAyE,OAAA,CAAAnB,eAAA,IAAtB,IAAI;MAAA;IAAA;IAAA8G,QAAA,GAD7BhM,EAAE,CAAAiM,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAA3K,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF3B,EAAE,CAAAuM,UAAA,IAAA7K,yCAAA,wBAC4oB,CAAC;MAAA;IAAA;IAAA8K,YAAA,GAAgKrL,eAAe;IAAAsL,MAAA;IAAAC,aAAA;EAAA;AAC35B;AACA;EAAA,QAAAlE,SAAA,oBAAAA,SAAA,KAH6FxI,EAAE,CAAA2M,iBAAA,CAGJzG,kBAAkB,EAAc,CAAC;IAChHgF,IAAI,EAAE1K,SAAS;IACfoM,IAAI,EAAE,CAAC;MAAEjD,QAAQ,EAAE,sBAAsB;MAAE+C,aAAa,EAAEjM,iBAAiB,CAACoM,IAAI;MAAEC,eAAe,EAAEpM,uBAAuB,CAACqM,OAAO;MAAEC,OAAO,EAAE,CAAC7L,eAAe,CAAC;MAAE8L,IAAI,EAAE;QAC1J,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEZ,QAAQ,EAAE,mCAAmC;MAAEI,MAAM,EAAE,CAAC,qGAAqG;IAAE,CAAC;EAC/K,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE3F,aAAa,EAAE,CAAC;MACxDoE,IAAI,EAAEvK,SAAS;MACfiM,IAAI,EAAE,CAACzL,eAAe,EAAE;QAAE+L,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,UAAU;EACVC,MAAM;EACN;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACAC,iBAAiB;EACjB;EACAhJ,YAAY;EACZ;EACAiJ,MAAM,GAAG,IAAInK,OAAO,CAAC,CAAC;EACtB;EACAqH,aAAa;EACb;EACA+C,aAAa;EACb;EACAC,oBAAoB;EACpB;EACAxJ,EAAE;EACF;EACAyJ,mBAAmB;EACnBtG,WAAWA,CAAC8F,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC7I,YAAY,GAAG6I,MAAM,CAAC7I,YAAY;IACvC,IAAI,CAACmG,aAAa,GAAGyC,UAAU,CAACzC,aAAa,CAAC,CAAC;IAC/C,IAAI,CAAC+C,aAAa,GAAGN,UAAU,CAACM,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGP,UAAU,CAACO,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAACxJ,EAAE,GAAGkJ,MAAM,CAAClJ,EAAE,CAAC,CAAC;IACrB,IAAI,CAACuJ,aAAa,CAAC9C,SAAS,CAACiD,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAKrK,MAAM,IAAI,CAAC,IAAI,CAACe,YAAY,IAAI,CAACd,cAAc,CAACmK,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAACvD,aAAa,CAACC,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAACpG,YAAY,EAAE;QACpB,IAAI,CAACwJ,KAAK,CAACC,SAAS,EAAE;UAAEC,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,mBAAmB,GAAGR,UAAU,CAACe,WAAW,CAAC,CAAC,CAACvD,SAAS,CAAC,MAAM;MAChE;MACA,IAAIyC,MAAM,CAAC1H,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAACqI,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAACvF,MAAM,EAAEO,OAAO,EAAE;IACnB,IAAI,IAAI,CAACwE,iBAAiB,EAAE;MACxB,MAAMY,aAAa,GAAG,IAAI,CAACX,MAAM;MACjC,IAAI,CAACD,iBAAiB,CAACvG,qBAAqB,GAAG+B,OAAO,EAAEkF,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACN,mBAAmB,CAACS,WAAW,CAAC,CAAC;MACtC,IAAI,CAACjB,UAAU,CAACkB,OAAO,CAAC,CAAC;MACzBF,aAAa,CAACG,IAAI,CAAC9F,MAAM,CAAC;MAC1B2F,aAAa,CAACI,QAAQ,CAAC,CAAC;MACxB,IAAI,CAAClB,iBAAiB,GAAG,IAAI,CAACE,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAiB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACrB,UAAU,CAACqB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAACjK,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAAC0I,UAAU,CAACsB,UAAU,CAAC;MAAEjK,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACAiK,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACxB,UAAU,CAACuB,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACxB,UAAU,CAACyB,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAME,sBAAsB,GAAG,IAAIlO,cAAc,CAAC,sBAAsB,EAAE;EACtEmO,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGhP,MAAM,CAAC2C,OAAO,CAAC;IAC/B,OAAO,MAAMqM,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACjD;AACJ,CAAC,CAAC;AACF;AACA,MAAMC,WAAW,GAAG,IAAIxO,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAMyO,qBAAqB,GAAG,IAAIzO,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAAS0O,uCAAuCA,CAACL,OAAO,EAAE;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,+BAA+B,GAAG;EACpCC,OAAO,EAAEV,sBAAsB;EAC/BW,IAAI,EAAE,CAAC7M,OAAO,CAAC;EACf8M,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,MAAM,CAAC;EACTC,QAAQ,GAAG3P,MAAM,CAAC2C,OAAO,CAAC;EAC1BwE,SAAS,GAAGnH,MAAM,CAACK,QAAQ,CAAC;EAC5BuP,eAAe,GAAG5P,MAAM,CAACoP,qBAAqB,EAAE;IAAExI,QAAQ,EAAE;EAAK,CAAC,CAAC;EACnEiJ,aAAa,GAAG7P,MAAM,CAAC0P,MAAM,EAAE;IAAE9I,QAAQ,EAAE,IAAI;IAAEkJ,QAAQ,EAAE;EAAK,CAAC,CAAC;EAClEC,iBAAiB,GAAG/P,MAAM,CAAC6C,gBAAgB,CAAC;EAC5CmN,YAAY,GAAGhQ,MAAM,CAAC4D,YAAY,CAAC;EACnCqM,uBAAuB,GAAG,EAAE;EAC5BC,0BAA0B,GAAG,IAAI7M,OAAO,CAAC,CAAC;EAC1C8M,uBAAuB,GAAG,IAAI9M,OAAO,CAAC,CAAC;EACvC+M,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/BC,eAAe,GAAGtQ,MAAM,CAAC6O,sBAAsB,CAAC;EAChD;EACA,IAAI0B,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,aAAa,GAAG,IAAI,CAACA,aAAa,CAACU,WAAW,GAAG,IAAI,CAACN,uBAAuB;EAC7F;EACA;EACA,IAAIO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACX,aAAa,GAAG,IAAI,CAACA,aAAa,CAACW,WAAW,GAAG,IAAI,CAACL,uBAAuB;EAC7F;EACA;AACJ;AACA;AACA;EACIM,cAAc,GAAGnN,KAAK,CAAC,MAAM,IAAI,CAACiN,WAAW,CAACG,MAAM,GAC9C,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAAClN,SAAS,CAACsK,SAAS,CAAC,CAAC,CAAC;EAC3D3G,WAAWA,CAAA,EAAG,CAAE;EAChBwJ,IAAIA,CAACC,sBAAsB,EAAE1D,MAAM,EAAE;IACjC,MAAM2D,QAAQ,GAAI,IAAI,CAACnB,eAAe,IAAI,IAAI7L,YAAY,CAAC,CAAE;IAC7DqJ,MAAM,GAAG;MAAE,GAAG2D,QAAQ;MAAE,GAAG3D;IAAO,CAAC;IACnCA,MAAM,CAAClJ,EAAE,GAAGkJ,MAAM,CAAClJ,EAAE,IAAI,IAAI,CAAC8L,YAAY,CAACgB,KAAK,CAAC,aAAa,CAAC;IAC/D,IAAI5D,MAAM,CAAClJ,EAAE,IACT,IAAI,CAAC+M,aAAa,CAAC7D,MAAM,CAAClJ,EAAE,CAAC,KAC5B,OAAOqE,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMvC,KAAK,CAAC,mBAAmBoH,MAAM,CAAClJ,EAAE,iDAAiD,CAAC;IAC9F;IACA,MAAMgN,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC/D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG,IAAI,CAACwC,QAAQ,CAAClF,MAAM,CAACyG,aAAa,CAAC;IACtD,MAAME,SAAS,GAAG,IAAIlE,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAMiE,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACnE,UAAU,EAAEiE,SAAS,EAAEhE,MAAM,CAAC;IAC5EgE,SAAS,CAAC7D,iBAAiB,GAAG8D,eAAe;IAC7C,IAAI,CAACE,oBAAoB,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAEjE,MAAM,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACmD,WAAW,CAACG,MAAM,EAAE;MAC1B,IAAI,CAACc,4CAA4C,CAAC,CAAC;IACvD;IACA,IAAI,CAACjB,WAAW,CAACjJ,IAAI,CAAC8J,SAAS,CAAC;IAChCA,SAAS,CAAC5D,MAAM,CAAC7C,SAAS,CAAC,MAAM,IAAI,CAAC8G,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACZ,WAAW,CAAClC,IAAI,CAAC8C,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIM,QAAQA,CAAA,EAAG;IACPC,cAAc,CAAC,IAAI,CAACpB,WAAW,EAAEqB,MAAM,IAAIA,MAAM,CAAC7D,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIkD,aAAaA,CAAC/M,EAAE,EAAE;IACd,OAAO,IAAI,CAACqM,WAAW,CAACsB,IAAI,CAACD,MAAM,IAAIA,MAAM,CAAC1N,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACAgE,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACAyJ,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACxE,MAAM,CAAC3H,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAACgM,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC1B,uBAAuB,EAAE2B,MAAM,IAAIA,MAAM,CAAC7D,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAACmC,0BAA0B,CAAC3B,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAAC4B,uBAAuB,CAAC5B,QAAQ,CAAC,CAAC;IACvC,IAAI,CAAC0B,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIkB,iBAAiBA,CAAC/D,MAAM,EAAE;IACtB,MAAM0E,KAAK,GAAG,IAAIhP,aAAa,CAAC;MAC5BgC,gBAAgB,EAAEsI,MAAM,CAACtI,gBAAgB,IACrC,IAAI,CAAC6K,QAAQ,CAACoC,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;MAC7E3M,cAAc,EAAE6H,MAAM,CAAC7H,cAAc,IAAI,IAAI,CAAC+K,eAAe,CAAC,CAAC;MAC/DlM,UAAU,EAAEgJ,MAAM,CAAChJ,UAAU;MAC7BC,WAAW,EAAE+I,MAAM,CAAC/I,WAAW;MAC/BW,SAAS,EAAEoI,MAAM,CAACpI,SAAS;MAC3BN,QAAQ,EAAE0I,MAAM,CAAC1I,QAAQ;MACzBC,SAAS,EAAEyI,MAAM,CAACzI,SAAS;MAC3BC,QAAQ,EAAEwI,MAAM,CAACxI,QAAQ;MACzBC,SAAS,EAAEuI,MAAM,CAACvI,SAAS;MAC3BL,KAAK,EAAE4I,MAAM,CAAC5I,KAAK;MACnBC,MAAM,EAAE2I,MAAM,CAAC3I,MAAM;MACrB0N,mBAAmB,EAAE/E,MAAM,CAAC5H;IAChC,CAAC,CAAC;IACF,IAAI4H,MAAM,CAAC9I,aAAa,EAAE;MACtBwN,KAAK,CAACxN,aAAa,GAAG8I,MAAM,CAAC9I,aAAa;IAC9C;IACA,OAAOwN,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,gBAAgBA,CAACtC,OAAO,EAAEoC,SAAS,EAAEhE,MAAM,EAAE;IACzC,MAAMgF,YAAY,GAAGhF,MAAM,CAACnJ,QAAQ,IAAImJ,MAAM,CAACpJ,gBAAgB,EAAEC,QAAQ;IACzE,MAAM2B,SAAS,GAAG,CACd;MAAE2J,OAAO,EAAExL,YAAY;MAAEsO,QAAQ,EAAEjF;IAAO,CAAC,EAC3C;MAAEmC,OAAO,EAAErC,SAAS;MAAEmF,QAAQ,EAAEjB;IAAU,CAAC,EAC3C;MAAE7B,OAAO,EAAE9M,UAAU;MAAE4P,QAAQ,EAAErD;IAAQ,CAAC,CAC7C;IACD,IAAIsD,aAAa;IACjB,IAAIlF,MAAM,CAACvH,SAAS,EAAE;MAClB,IAAI,OAAOuH,MAAM,CAACvH,SAAS,KAAK,UAAU,EAAE;QACxCyM,aAAa,GAAGlF,MAAM,CAACvH,SAAS;MACpC,CAAC,MACI;QACDyM,aAAa,GAAGlF,MAAM,CAACvH,SAAS,CAACoF,IAAI;QACrCrF,SAAS,CAAC0B,IAAI,CAAC,GAAG8F,MAAM,CAACvH,SAAS,CAACD,SAAS,CAACwH,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDkF,aAAa,GAAGrM,kBAAkB;IACtC;IACA,MAAMsM,eAAe,GAAG,IAAInR,eAAe,CAACkR,aAAa,EAAElF,MAAM,CAACpJ,gBAAgB,EAAE3D,QAAQ,CAACoK,MAAM,CAAC;MAAE+H,MAAM,EAAEJ,YAAY,IAAI,IAAI,CAACjL,SAAS;MAAEvB;IAAU,CAAC,CAAC,CAAC;IAC3J,MAAM6M,YAAY,GAAGzD,OAAO,CAAC0D,MAAM,CAACH,eAAe,CAAC;IACpD,OAAOE,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,oBAAoBA,CAACT,sBAAsB,EAAEM,SAAS,EAAEC,eAAe,EAAEjE,MAAM,EAAE;IAC7E,IAAI0D,sBAAsB,YAAYlQ,WAAW,EAAE;MAC/C,MAAMqD,QAAQ,GAAG,IAAI,CAAC2O,eAAe,CAACxF,MAAM,EAAEgE,SAAS,EAAEC,eAAe,EAAErD,SAAS,CAAC;MACpF,IAAI6E,OAAO,GAAG;QAAEC,SAAS,EAAE1F,MAAM,CAACrI,IAAI;QAAEqM;MAAU,CAAC;MACnD,IAAIhE,MAAM,CAACtH,eAAe,EAAE;QACxB+M,OAAO,GAAG;UACN,GAAGA,OAAO;UACV,IAAI,OAAOzF,MAAM,CAACtH,eAAe,KAAK,UAAU,GAC1CsH,MAAM,CAACtH,eAAe,CAAC,CAAC,GACxBsH,MAAM,CAACtH,eAAe;QAChC,CAAC;MACL;MACAuL,eAAe,CAAC5I,oBAAoB,CAAC,IAAInH,cAAc,CAACwP,sBAAsB,EAAE,IAAI,EAAE+B,OAAO,EAAE5O,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAAC2O,eAAe,CAACxF,MAAM,EAAEgE,SAAS,EAAEC,eAAe,EAAE,IAAI,CAAClK,SAAS,CAAC;MACzF,MAAM4L,UAAU,GAAG1B,eAAe,CAACjJ,qBAAqB,CAAC,IAAIhH,eAAe,CAAC0P,sBAAsB,EAAE1D,MAAM,CAACpJ,gBAAgB,EAAEC,QAAQ,CAAC,CAAC;MACxImN,SAAS,CAAC9D,YAAY,GAAGyF,UAAU;MACnC3B,SAAS,CAAC/D,iBAAiB,GAAG0F,UAAU,CAACJ,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACxF,MAAM,EAAEgE,SAAS,EAAEC,eAAe,EAAE2B,gBAAgB,EAAE;IAClE,MAAMZ,YAAY,GAAGhF,MAAM,CAACnJ,QAAQ,IAAImJ,MAAM,CAACpJ,gBAAgB,EAAEC,QAAQ;IACzE,MAAM2B,SAAS,GAAG,CACd;MAAE2J,OAAO,EAAEJ,WAAW;MAAEkD,QAAQ,EAAEjF,MAAM,CAACrI;IAAK,CAAC,EAC/C;MAAEwK,OAAO,EAAErC,SAAS;MAAEmF,QAAQ,EAAEjB;IAAU,CAAC,CAC9C;IACD,IAAIhE,MAAM,CAACxH,SAAS,EAAE;MAClB,IAAI,OAAOwH,MAAM,CAACxH,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAAC0B,IAAI,CAAC,GAAG8F,MAAM,CAACxH,SAAS,CAACwL,SAAS,EAAEhE,MAAM,EAAEiE,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDzL,SAAS,CAAC0B,IAAI,CAAC,GAAG8F,MAAM,CAACxH,SAAS,CAAC;MACvC;IACJ;IACA,IAAIwH,MAAM,CAACpI,SAAS,KACf,CAACoN,YAAY,IACV,CAACA,YAAY,CAACa,GAAG,CAACnP,cAAc,EAAE,IAAI,EAAE;MAAE8C,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClEhB,SAAS,CAAC0B,IAAI,CAAC;QACXiI,OAAO,EAAEzL,cAAc;QACvBuO,QAAQ,EAAE;UAAEa,KAAK,EAAE9F,MAAM,CAACpI,SAAS;UAAEmO,MAAM,EAAE5P,EAAE,CAAC;QAAE;MACtD,CAAC,CAAC;IACN;IACA,OAAOlD,QAAQ,CAACoK,MAAM,CAAC;MAAE+H,MAAM,EAAEJ,YAAY,IAAIY,gBAAgB;MAAEpN;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI6L,iBAAiBA,CAACL,SAAS,EAAEgC,SAAS,EAAE;IACpC,MAAM1L,KAAK,GAAG,IAAI,CAAC6I,WAAW,CAAC5I,OAAO,CAACyJ,SAAS,CAAC;IACjD,IAAI1J,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAAC6I,WAAW,CAAC3I,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAAC6I,WAAW,CAACG,MAAM,EAAE;QAC1B,IAAI,CAACN,mBAAmB,CAACiD,OAAO,CAAC,CAACC,aAAa,EAAExK,OAAO,KAAK;UACzD,IAAIwK,aAAa,EAAE;YACfxK,OAAO,CAACyK,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACDxK,OAAO,CAACQ,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC8G,mBAAmB,CAACoD,KAAK,CAAC,CAAC;QAChC,IAAIJ,SAAS,EAAE;UACX,IAAI,CAACzC,kBAAkB,CAAC,CAAC,CAACrC,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA;EACAkD,4CAA4CA,CAAA,EAAG;IAC3C,MAAMiC,gBAAgB,GAAG,IAAI,CAAC1D,iBAAiB,CAAC2D,mBAAmB,CAAC,CAAC;IACrE;IACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAAClD,MAAM,GAAG,CAAC,EAAEoD,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;QAC3B,IAAIC,OAAO,KAAKN,gBAAgB,IAC5BM,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAAC7D,mBAAmB,CAAC8D,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACR,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA5C,kBAAkBA,CAAA,EAAG;IACjB,MAAM6B,MAAM,GAAG,IAAI,CAAC3C,aAAa;IACjC,OAAO2C,MAAM,GAAGA,MAAM,CAAC7B,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACT,0BAA0B;EACjF;EACA,OAAOtF,IAAI,YAAAwJ,eAAAtJ,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4E,MAAM;EAAA;EACzG,OAAO2E,KAAK,kBArZ6EtU,EAAE,CAAAuU,kBAAA;IAAAC,KAAA,EAqZY7E,MAAM;IAAAX,OAAA,EAANW,MAAM,CAAA9E,IAAA;IAAAkE,UAAA,EAAc;EAAM;AACrI;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KAvZ6FxI,EAAE,CAAA2M,iBAAA,CAuZJgD,MAAM,EAAc,CAAC;IACpGzE,IAAI,EAAEpK,UAAU;IAChB8L,IAAI,EAAE,CAAC;MAAEmC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAAS6C,cAAcA,CAAC6C,KAAK,EAAErL,QAAQ,EAAE;EACrC,IAAI2K,CAAC,GAAGU,KAAK,CAAC9D,MAAM;EACpB,OAAOoD,CAAC,EAAE,EAAE;IACR3K,QAAQ,CAACqL,KAAK,CAACV,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAMW,YAAY,CAAC;EACf,OAAO7J,IAAI,YAAA8J,qBAAA5J,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2J,YAAY;EAAA;EAC/G,OAAOE,IAAI,kBAxa8E5U,EAAE,CAAA6U,gBAAA;IAAA3J,IAAA,EAwaSwJ,YAAY;IAAA1H,OAAA,GAAY/J,aAAa,EAAExB,YAAY,EAAEe,UAAU,EAAE0D,kBAAkB;IAAA4O,OAAA;IAC/K;IACA;IACArT,YAAY,EACZyE,kBAAkB;EAAA;EAC1B,OAAO6O,IAAI,kBA7a8E/U,EAAE,CAAAgV,gBAAA;IAAAnP,SAAA,EA6akC,CAAC8J,MAAM,CAAC;IAAA3C,OAAA,GAAY/J,aAAa,EAAExB,YAAY,EAAEe,UAAU;IAChL;IACA;IACAf,YAAY;EAAA;AACxB;AACA;EAAA,QAAA+G,SAAA,oBAAAA,SAAA,KAlb6FxI,EAAE,CAAA2M,iBAAA,CAkbJ+H,YAAY,EAAc,CAAC;IAC1GxJ,IAAI,EAAEnK,QAAQ;IACd6L,IAAI,EAAE,CAAC;MACCI,OAAO,EAAE,CAAC/J,aAAa,EAAExB,YAAY,EAAEe,UAAU,EAAE0D,kBAAkB,CAAC;MACtE4O,OAAO,EAAE;MACL;MACA;MACArT,YAAY,EACZyE,kBAAkB,CACrB;MACDL,SAAS,EAAE,CAAC8J,MAAM;IACtB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASzJ,kBAAkB,EAAEmJ,qBAAqB,EAAED,WAAW,EAAEN,sBAAsB,EAAES,+BAA+B,EAAED,uCAAuC,EAAEK,MAAM,EAAE3L,YAAY,EAAE0Q,YAAY,EAAEvH,SAAS,EAAEnH,sCAAsC,EAAE7E,eAAe,IAAI8T,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}