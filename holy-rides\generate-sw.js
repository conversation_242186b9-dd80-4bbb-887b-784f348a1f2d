const fs = require('fs');
const path = require('path');

// Simple script to generate a basic service worker file
const swContent = `
// This is a simple service worker for the Holy Rides PWA
const CACHE_NAME = 'holy-rides-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/manifest.webmanifest',
  '/assets/icons/icon-192x192.png',
  '/assets/icons/icon-512x512.png'
];

// Install event - cache basic assets
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache if available, otherwise fetch from network
self.addEventListener('fetch', event => {
  // Skip Supabase authentication routes
  const url = new URL(event.request.url);
  if (url.hostname.includes('supabase.co') &&
      (url.pathname.includes('/auth/') ||
       url.pathname.includes('/rest/v1/auth/') ||
       url.pathname.includes('/storage/v1/') ||
       url.pathname.includes('/functions/v1/'))) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
`;

// Write the service worker file
fs.writeFileSync(path.join(__dirname, 'public', 'ngsw-worker.js'), swContent);
console.log('Service worker file generated successfully!');
