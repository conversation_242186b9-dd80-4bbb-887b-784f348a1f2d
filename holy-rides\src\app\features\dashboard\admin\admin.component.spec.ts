import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { AdminComponent } from './admin.component';
import { RideService } from '../../../core/services/ride.service';
import { StatisticsService } from '../../../core/services/statistics.service';
import { UserService } from '../../../core/services/user.service';


describe('AdminComponent', () => {
  let component: AdminComponent;
  let fixture: ComponentFixture<AdminComponent>;
  let userServiceSpy: jasmine.SpyObj<UserService>;
  let rideServiceSpy: jasmine.SpyObj<RideService>;
  let statisticsServiceSpy: jasmine.SpyObj<StatisticsService>;

  beforeEach(async () => {
    userServiceSpy = jasmine.createSpyObj('UserService', ['getAllUsers', 'approveDriver']);
    rideServiceSpy = jasmine.createSpyObj('RideService', ['getAllRides', 'updateRideStatus', 'assignRideToDriver']);
    statisticsServiceSpy = jasmine.createSpyObj('StatisticsService', ['generateSystemStatistics']);

    userServiceSpy.getAllUsers.and.returnValue(Promise.resolve([]));
    rideServiceSpy.getAllRides.and.returnValue(Promise.resolve([]));
    statisticsServiceSpy.generateSystemStatistics.and.returnValue(Promise.resolve({
      totalUsers: { all: 0, riders: 0, drivers: 0, admins: 0 },
      rides: { total: 0, completed: 0, inProgress: 0, requested: 0, canceled: 0 },
      recentActivity: []
    }));

    await TestBed.configureTestingModule({
      imports: [
        AdminComponent,
        NoopAnimationsModule,
        MatSnackBarModule
      ],
      providers: [
        { provide: UserService, useValue: userServiceSpy },
        { provide: RideService, useValue: rideServiceSpy },
        { provide: StatisticsService, useValue: statisticsServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AdminComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load users, rides, and statistics on init', async () => {
    await component.ngOnInit();
    expect(userServiceSpy.getAllUsers).toHaveBeenCalled();
    expect(rideServiceSpy.getAllRides).toHaveBeenCalled();
    expect(statisticsServiceSpy.generateSystemStatistics).toHaveBeenCalled();
  });

  it('should format date correctly', () => {
    const testDate = '2023-05-15T10:30:00Z';
    const formattedDate = component.formatDate(testDate);
    expect(formattedDate).toBeTruthy();
    expect(typeof formattedDate).toBe('string');
  });

  it('should get role display name correctly', () => {
    expect(component.getRoleDisplayName('rider')).toBe('Rider');
    expect(component.getRoleDisplayName('driver')).toBe('Driver');
    expect(component.getRoleDisplayName('admin')).toBe('Admin');
  });

  it('should get status display name correctly', () => {
    expect(component.getStatusDisplayName('requested')).toBe('Requested');
    expect(component.getStatusDisplayName('in-progress')).toBe('In Progress');
  });
});
