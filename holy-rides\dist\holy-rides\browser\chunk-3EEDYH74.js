import{$ as Or,B as G,D as te,E as re,Fa as xr,G as xt,H as le,Ha as _r,Hc as tn,J as Qe,K as Q,Ka as jr,L as _t,M as wr,Mc as rn,O as br,Oa as kr,Pa as fe,Qa as Lr,Ra as $r,S as Ir,T as P,U as Ar,Ua as zr,Va as Ye,W as y,Wa as Fr,X as C,Xa as qr,Ya as Xe,Z as R,_ as Tr,a as mr,aa as A,ac as Kr,b as Rr,ba as jt,bb as Br,ca as K,cb as Vr,d as Et,da as d,db as $t,dc as Ft,e as Ut,eb as zt,f as V,fb as Hr,fc as Je,g as U,ga as Mr,gb as Wr,i as H,ia as he,j as b,ja as j,k as h,ka as Ke,l as Te,lc as Zr,m as Sr,mc as Yr,n as yr,oc as Xr,pa as kt,pc as Oe,q as v,qa as Dr,qc as <PERSON>,r as Ge,ra as Lt,rc as pe,s as I,sb as Gr,sc as en,t as Nt,ta as Er,u as Cr,ua as Ur,v as Pt,va as de,wa as Ze,ya as Nr,yb as Qr,za as Pr}from"./chunk-ST4QC4E3.js";import{a as l,b as E}from"./chunk-ODN5LVDJ.js";var f="primary",ze=Symbol("RouteTitle"),Wt=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function oe(t){return new Wt(t)}function hn(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let s=0;s<r.length;s++){let o=r[s],a=t[s];if(o[0]===":")i[o.substring(1)]=a;else if(o!==a.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function si(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!k(t[e],n[e]))return!1;return!0}function k(t,n){let e=t?Gt(t):void 0,r=n?Gt(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!dn(t[i],n[i]))return!1;return!0}function Gt(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function dn(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,s)=>r[s]===i)}else return t===n}function fn(t){return t.length>0?t[t.length-1]:null}function ee(t){return Sr(t)?t:Vr(t)?b(Promise.resolve(t)):h(t)}var ai={exact:gn,subset:vn},pn={exact:ui,subset:ci,ignored:()=>!0};function nn(t,n,e){return ai[e.paths](t.root,n.root,e.matrixParams)&&pn[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function ui(t,n){return k(t,n)}function gn(t,n,e){if(!ne(t.segments,n.segments)||!rt(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!gn(t.children[r],n.children[r],e))return!1;return!0}function ci(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>dn(t[e],n[e]))}function vn(t,n,e){return mn(t,n,n.segments,e)}function mn(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!ne(i,e)||n.hasChildren()||!rt(i,e,r))}else if(t.segments.length===e.length){if(!ne(t.segments,e)||!rt(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!vn(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),s=e.slice(t.segments.length);return!ne(t.segments,i)||!rt(t.segments,i,r)||!t.children[f]?!1:mn(t.children[f],n,s,r)}}function rt(t,n,e){return n.every((r,i)=>pn[e](t[i].parameters,r.parameters))}var $=class{root;queryParams;fragment;_queryParamMap;constructor(n=new g([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=oe(this.queryParams),this._queryParamMap}toString(){return di.serialize(this)}},g=class{segments;children;parent=null;constructor(n,e){this.segments=n,this.children=e,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return nt(this)}},Z=class{path;parameters;_parameterMap;constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=oe(this.parameters),this._parameterMap}toString(){return Sn(this)}};function li(t,n){return ne(t,n)&&t.every((e,r)=>k(e.parameters,n[r].parameters))}function ne(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function hi(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===f&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==f&&(e=e.concat(n(i,r)))}),e}var se=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:()=>new Y,providedIn:"root"})}return t})(),Y=class{parse(n){let e=new Kt(n);return new $(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${Me(n.root,!0)}`,r=gi(n.queryParams),i=typeof n.fragment=="string"?`#${fi(n.fragment)}`:"";return`${e}${r}${i}`}},di=new Y;function nt(t){return t.segments.map(n=>Sn(n)).join("/")}function Me(t,n){if(!t.hasChildren())return nt(t);if(n){let e=t.children[f]?Me(t.children[f],!1):"",r=[];return Object.entries(t.children).forEach(([i,s])=>{i!==f&&r.push(`${i}:${Me(s,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=hi(t,(r,i)=>i===f?[Me(t.children[f],!1)]:[`${i}:${Me(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[f]!=null?`${nt(t)}/${e[0]}`:`${nt(t)}/(${e.join("//")})`}}function Rn(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function et(t){return Rn(t).replace(/%3B/gi,";")}function fi(t){return encodeURI(t)}function Qt(t){return Rn(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function it(t){return decodeURIComponent(t)}function on(t){return it(t.replace(/\+/g,"%20"))}function Sn(t){return`${Qt(t.path)}${pi(t.parameters)}`}function pi(t){return Object.entries(t).map(([n,e])=>`;${Qt(n)}=${Qt(e)}`).join("")}function gi(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${et(e)}=${et(i)}`).join("&"):`${et(e)}=${et(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var vi=/^[^\/()?;#]+/;function qt(t){let n=t.match(vi);return n?n[0]:""}var mi=/^[^\/()?;=#]+/;function Ri(t){let n=t.match(mi);return n?n[0]:""}var Si=/^[^=?&#]+/;function yi(t){let n=t.match(Si);return n?n[0]:""}var Ci=/^[^&#]+/;function wi(t){let n=t.match(Ci);return n?n[0]:""}var Kt=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new g([],{}):new g([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[f]=new g(n,e)),r}parseSegment(){let n=qt(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(n),new Z(it(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=Ri(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=qt(this.remaining);i&&(r=i,this.capture(r))}n[it(e)]=it(r)}parseQueryParam(n){let e=yi(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let o=wi(this.remaining);o&&(r=o,this.capture(r))}let i=on(e),s=on(r);if(n.hasOwnProperty(i)){let o=n[i];Array.isArray(o)||(o=[o],n[i]=o),o.push(s)}else n[i]=s}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=qt(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new C(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):n&&(s=f);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[f]:new g([],o),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new C(4011,!1)}};function yn(t){return t.segments.length>0?new g([],{[f]:t}):t}function Cn(t){let n={};for(let[r,i]of Object.entries(t.children)){let s=Cn(i);if(r===f&&s.segments.length===0&&s.hasChildren())for(let[o,a]of Object.entries(s.children))n[o]=a;else(s.segments.length>0||s.hasChildren())&&(n[r]=s)}let e=new g(t.segments,n);return bi(e)}function bi(t){if(t.numberOfChildren===1&&t.children[f]){let n=t.children[f];return new g(t.segments.concat(n.segments),n.children)}return t}function X(t){return t instanceof $}function wn(t,n,e=null,r=null){let i=bn(t);return In(i,n,e,r)}function bn(t){let n;function e(s){let o={};for(let u of s.children){let c=e(u);o[u.outlet]=c}let a=new g(s.url,o);return s===t&&(n=a),a}let r=e(t.root),i=yn(r);return n??i}function In(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return Bt(i,i,i,e,r);let s=Ii(n);if(s.toRoot())return Bt(i,i,new g([],{}),e,r);let o=Ai(s,i,t),a=o.processChildren?Ee(o.segmentGroup,o.index,s.commands):Tn(o.segmentGroup,o.index,s.commands);return Bt(i,o.segmentGroup,a,e,r)}function st(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function Ne(t){return typeof t=="object"&&t!=null&&t.outlets}function Bt(t,n,e,r,i){let s={};r&&Object.entries(r).forEach(([u,c])=>{s[u]=Array.isArray(c)?c.map(p=>`${p}`):`${c}`});let o;t===n?o=e:o=An(t,n,e);let a=yn(Cn(o));return new $(a,s,i)}function An(t,n,e){let r={};return Object.entries(t.children).forEach(([i,s])=>{s===n?r[i]=e:r[i]=An(s,n,e)}),new g(t.segments,r)}var at=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&st(r[0]))throw new C(4003,!1);let i=r.find(Ne);if(i&&i!==fn(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Ii(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new at(!0,0,t);let n=0,e=!1,r=t.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let a={};return Object.entries(s.outlets).forEach(([u,c])=>{a[u]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((a,u)=>{u==0&&a==="."||(u==0&&a===""?e=!0:a===".."?n++:a!=""&&i.push(a))}),i):[...i,s]},[]);return new at(e,n,r)}var me=class{segmentGroup;processChildren;index;constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function Ai(t,n,e){if(t.isAbsolute)return new me(n,!0,0);if(!e)return new me(n,!1,NaN);if(e.parent===null)return new me(e,!0,0);let r=st(t.commands[0])?0:1,i=e.segments.length-1+r;return Ti(e,i,t.numberOfDoubleDots)}function Ti(t,n,e){let r=t,i=n,s=e;for(;s>i;){if(s-=i,r=r.parent,!r)throw new C(4005,!1);i=r.segments.length}return new me(r,!1,i-s)}function Oi(t){return Ne(t[0])?t[0].outlets:{[f]:t}}function Tn(t,n,e){if(t??=new g([],{}),t.segments.length===0&&t.hasChildren())return Ee(t,n,e);let r=Mi(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let s=new g(t.segments.slice(0,r.pathIndex),{});return s.children[f]=new g(t.segments.slice(r.pathIndex),t.children),Ee(s,0,i)}else return r.match&&i.length===0?new g(t.segments,{}):r.match&&!t.hasChildren()?Zt(t,n,e):r.match?Ee(t,0,i):Zt(t,n,e)}function Ee(t,n,e){if(e.length===0)return new g(t.segments,{});{let r=Oi(e),i={};if(Object.keys(r).some(s=>s!==f)&&t.children[f]&&t.numberOfChildren===1&&t.children[f].segments.length===0){let s=Ee(t.children[f],n,e);return new g(t.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=Tn(t.children[s],n,o))}),Object.entries(t.children).forEach(([s,o])=>{r[s]===void 0&&(i[s]=o)}),new g(t.segments,i)}}function Mi(t,n,e){let r=0,i=n,s={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return s;let o=t.segments[i],a=e[r];if(Ne(a))break;let u=`${a}`,c=r<e.length-1?e[r+1]:null;if(i>0&&u===void 0)break;if(u&&c&&typeof c=="object"&&c.outlets===void 0){if(!an(u,c,o))return s;r+=2}else{if(!an(u,{},o))return s;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function Zt(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let s=e[i];if(Ne(s)){let u=Di(s.outlets);return new g(r,u)}if(i===0&&st(e[0])){let u=t.segments[n];r.push(new Z(u.path,sn(e[0]))),i++;continue}let o=Ne(s)?s.outlets[f]:`${s}`,a=i<e.length-1?e[i+1]:null;o&&a&&st(a)?(r.push(new Z(o,sn(a))),i+=2):(r.push(new Z(o,{})),i++)}return new g(r,{})}function Di(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=Zt(new g([],{}),0,r))}),n}function sn(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function an(t,n,e){return t==e.path&&k(n,e.parameters)}var ot="imperative",m=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(m||{}),D=class{id;url;constructor(n,e){this.id=n,this.url=e}},J=class extends D{type=m.NavigationStart;navigationTrigger;restoredState;constructor(n,e,r="imperative",i=null){super(n,e),this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},N=class extends D{urlAfterRedirects;type=m.NavigationEnd;constructor(n,e,r){super(n,e),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},T=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(T||{}),Se=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Se||{}),L=class extends D{reason;code;type=m.NavigationCancel;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},z=class extends D{reason;code;type=m.NavigationSkipped;constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i}},ye=class extends D{error;target;type=m.NavigationError;constructor(n,e,r,i){super(n,e),this.error=r,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Pe=class extends D{urlAfterRedirects;state;type=m.RoutesRecognized;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ut=class extends D{urlAfterRedirects;state;type=m.GuardsCheckStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ct=class extends D{urlAfterRedirects;state;shouldActivate;type=m.GuardsCheckEnd;constructor(n,e,r,i,s){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},lt=class extends D{urlAfterRedirects;state;type=m.ResolveStart;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ht=class extends D{urlAfterRedirects;state;type=m.ResolveEnd;constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},dt=class{route;type=m.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},ft=class{route;type=m.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},pt=class{snapshot;type=m.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},gt=class{snapshot;type=m.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},vt=class{snapshot;type=m.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},mt=class{snapshot;type=m.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ce=class{routerEvent;position;anchor;type=m.Scroll;constructor(n,e,r){this.routerEvent=n,this.position=e,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},xe=class{},we=class{url;navigationBehaviorOptions;constructor(n,e){this.url=n,this.navigationBehaviorOptions=e}};function Ei(t,n){return t.providers&&!t._injector&&(t._injector=Ye(t.providers,n,`Route: ${t.path}`)),t._injector??n}function x(t){return t.outlet||f}function Ui(t,n){let e=t.filter(r=>x(r)===n);return e.push(...t.filter(r=>x(r)!==n)),e}function Fe(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var Rt=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Fe(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new ae(this.rootInjector)}},ae=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new Rt(this.rootInjector),this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(r){return new(r||t)(K(he))};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),St=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=Yt(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=Yt(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=Xt(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return Xt(n,this._root).map(e=>e.value)}};function Yt(t,n){if(t===n.value)return n;for(let e of n.children){let r=Yt(t,e);if(r)return r}return null}function Xt(t,n){if(t===n.value)return[n];for(let e of n.children){let r=Xt(t,e);if(r.length)return r.unshift(n),r}return[]}var M=class{value;children;constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function ve(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var _e=class extends St{snapshot;constructor(n,e){super(n),this.snapshot=e,sr(this,n)}toString(){return this.snapshot.toString()}};function On(t){let n=Ni(t),e=new U([new Z("",{})]),r=new U({}),i=new U({}),s=new U({}),o=new U(""),a=new F(e,r,s,o,i,f,t,n.root);return a.snapshot=n.root,new _e(new M(a,[]),n)}function Ni(t){let n={},e={},r={},i="",s=new ie([],n,r,i,e,f,t,null,{});return new je("",new M(s,[]))}var F=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,e,r,i,s,o,a,u){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=a,this._futureSnapshot=u,this.title=this.dataSubject?.pipe(v(c=>c[ze]))??h(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(v(n=>oe(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(v(n=>oe(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function yt(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:l(l({},n.params),t.params),data:l(l({},n.data),t.data),resolve:l(l(l(l({},t.data),n.data),i?.data),t._resolvedData)}:r={params:l({},t.params),data:l({},t.data),resolve:l(l({},t.data),t._resolvedData??{})},i&&Dn(i)&&(r.resolve[ze]=i.title),r}var ie=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[ze]}constructor(n,e,r,i,s,o,a,u,c){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s,this.outlet=o,this.component=a,this.routeConfig=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=oe(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=oe(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},je=class extends St{url;constructor(n,e){super(e),this.url=n,sr(this,e)}toString(){return Mn(this._root)}};function sr(t,n){n.value._routerState=t,n.children.forEach(e=>sr(t,e))}function Mn(t){let n=t.children.length>0?` { ${t.children.map(Mn).join(", ")} } `:"";return`${t.value}${n}`}function Vt(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,k(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),k(n.params,e.params)||t.paramsSubject.next(e.params),si(n.url,e.url)||t.urlSubject.next(e.url),k(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Jt(t,n){let e=k(t.params,n.params)&&li(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||Jt(t.parent,n.parent))}function Dn(t){return typeof t.title=="string"||t.title===null}var En=new A(""),ar=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=f;activateEvents=new de;deactivateEvents=new de;attachEvents=new de;detachEvents=new de;routerOutletData=Nr(void 0);parentContexts=d(ae);location=d($r);changeDetector=d(Ft);inputBinder=d(qe,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,u=new er(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(o,{index:i.length,injector:u,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||t)};static \u0275dir=Xe({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ke]})}return t})(),er=class{route;childContexts;parent;outletData;constructor(n,e,r,i){this.route=n,this.childContexts=e,this.parent=r,this.outletData=i}get(n,e){return n===F?this.route:n===ae?this.childContexts:n===En?this.outletData:this.parent.get(n,e)}},qe=new A(""),ur=(()=>{class t{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(e){this.unsubscribeFromRouteData(e),this.subscribeToRouteData(e)}unsubscribeFromRouteData(e){this.outletDataSubscriptions.get(e)?.unsubscribe(),this.outletDataSubscriptions.delete(e)}subscribeToRouteData(e){let{activatedRoute:r}=e,i=Ge([r.queryParams,r.params,r.data]).pipe(P(([s,o,a],u)=>(a=l(l(l({},s),o),a),u===0?h(a):Promise.resolve(a)))).subscribe(s=>{if(!e.isActivated||!e.activatedComponentRef||e.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(e);return}let o=Zr(r.component);if(!o){this.unsubscribeFromRouteData(e);return}for(let{templateName:a}of o.inputs)e.activatedComponentRef.setInput(a,s[a])});this.outletDataSubscriptions.set(e,i)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:t.\u0275fac})}return t})();function Pi(t,n,e){let r=ke(t,n._root,e?e._root:void 0);return new _e(r,n)}function ke(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=xi(t,n,e);return new M(r,i)}else{if(t.shouldAttach(n.value)){let s=t.retrieve(n.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=n.value,o.children=n.children.map(a=>ke(t,a)),o}}let r=_i(n.value),i=n.children.map(s=>ke(t,s));return new M(r,i)}}function xi(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return ke(t,r,i);return ke(t,r)})}function _i(t){return new F(new U(t.url),new U(t.params),new U(t.queryParams),new U(t.fragment),new U(t.data),t.outlet,t.component,t)}var be=class{redirectTo;navigationBehaviorOptions;constructor(n,e){this.redirectTo=n,this.navigationBehaviorOptions=e}},Un="ngNavigationCancelingError";function Ct(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=X(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=Nn(!1,T.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function Nn(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[Un]=!0,e.cancellationCode=n,e}function ji(t){return Pn(t)&&X(t.url)}function Pn(t){return!!t&&t[Un]}var ki=(t,n,e,r)=>v(i=>(new tr(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),tr=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,e,r,i,s){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=s}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),Vt(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=ve(e);n.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],r),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(i===s)if(i.component){let o=r.getContext(i.outlet);o&&this.deactivateChildRoutes(n,e,o.children)}else this.deactivateChildRoutes(n,e,r);else s&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=ve(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(r&&r.outlet){let o=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:o,route:n,contexts:a})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=ve(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=ve(e);n.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],r),this.forwardEvent(new mt(s.value.snapshot))}),n.children.length&&this.forwardEvent(new gt(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(Vt(i),i===s)if(i.component){let o=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,o.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let o=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(a.contexts),o.attachRef=a.componentRef,o.route=a.route.value,o.outlet&&o.outlet.attach(a.componentRef,a.route.value),Vt(a.route.value),this.activateChildRoutes(n,null,o.children)}else o.attachRef=null,o.route=i,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(n,null,o.children)}else this.activateChildRoutes(n,null,r)}},wt=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Re=class{component;route;constructor(n,e){this.component=n,this.route=e}};function Li(t,n,e){let r=t._root,i=n?n._root:null;return De(r,i,e,[r.value])}function $i(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function Ae(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!Or(t)?t:n.get(t):r}function De(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=ve(n);return t.children.forEach(o=>{zi(o,s[o.value.outlet],e,r.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,a])=>Ue(a,e.getContext(o),i)),i}function zi(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=t.value,o=n?n.value:null,a=e?e.getContext(t.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let u=Fi(o,s,s.routeConfig.runGuardsAndResolvers);u?i.canActivateChecks.push(new wt(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?De(t,n,a?a.children:null,r,i):De(t,n,e,r,i),u&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Re(a.outlet.component,o))}else o&&Ue(n,a,i),i.canActivateChecks.push(new wt(r)),s.component?De(t,null,a?a.children:null,r,i):De(t,null,e,r,i);return i}function Fi(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!ne(t.url,n.url);case"pathParamsOrQueryParamsChange":return!ne(t.url,n.url)||!k(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Jt(t,n)||!k(t.queryParams,n.queryParams);case"paramsChange":default:return!Jt(t,n)}}function Ue(t,n,e){let r=ve(t),i=t.value;Object.entries(r).forEach(([s,o])=>{i.component?n?Ue(o,n.children.getContext(s),e):Ue(o,null,e):Ue(o,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new Re(n.outlet.component,i)):e.canDeactivateChecks.push(new Re(null,i)):e.canDeactivateChecks.push(new Re(null,i))}function Be(t){return typeof t=="function"}function qi(t){return typeof t=="boolean"}function Bi(t){return t&&Be(t.canLoad)}function Vi(t){return t&&Be(t.canActivate)}function Hi(t){return t&&Be(t.canActivateChild)}function Wi(t){return t&&Be(t.canDeactivate)}function Gi(t){return t&&Be(t.canMatch)}function xn(t){return t instanceof yr||t?.name==="EmptyError"}var tt=Symbol("INITIAL_VALUE");function Ie(){return P(t=>Ge(t.map(n=>n.pipe(le(1),Ir(tt)))).pipe(v(n=>{for(let e of n)if(e!==!0){if(e===tt)return tt;if(e===!1||Qi(e))return e}return!0}),G(n=>n!==tt),le(1)))}function Qi(t){return X(t)||t instanceof be}function Ki(t,n){return I(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?h(E(l({},e),{guardsResult:!0})):Zi(o,r,i,t).pipe(I(a=>a&&qi(a)?Yi(r,s,t,n):h(a)),v(a=>E(l({},e),{guardsResult:a})))})}function Zi(t,n,e,r){return b(t).pipe(I(i=>ro(i.component,i.route,e,n,r)),Q(i=>i!==!0,!0))}function Yi(t,n,e,r){return b(n).pipe(re(i=>Cr(Ji(i.route.parent,r),Xi(i.route,r),to(t,i.path,e),eo(t,i.route,e))),Q(i=>i!==!0,!0))}function Xi(t,n){return t!==null&&n&&n(new vt(t)),h(!0)}function Ji(t,n){return t!==null&&n&&n(new pt(t)),h(!0)}function eo(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return h(!0);let i=r.map(s=>Pt(()=>{let o=Fe(n)??e,a=Ae(s,o),u=Vi(a)?a.canActivate(n,t):j(o,()=>a(n,t));return ee(u).pipe(Q())}));return h(i).pipe(Ie())}function to(t,n,e){let r=n[n.length-1],s=n.slice(0,n.length-1).reverse().map(o=>$i(o)).filter(o=>o!==null).map(o=>Pt(()=>{let a=o.guards.map(u=>{let c=Fe(o.node)??e,p=Ae(u,c),S=Hi(p)?p.canActivateChild(r,t):j(c,()=>p(r,t));return ee(S).pipe(Q())});return h(a).pipe(Ie())}));return h(s).pipe(Ie())}function ro(t,n,e,r,i){let s=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!s||s.length===0)return h(!0);let o=s.map(a=>{let u=Fe(n)??i,c=Ae(a,u),p=Wi(c)?c.canDeactivate(t,n,e,r):j(u,()=>c(t,n,e,r));return ee(p).pipe(Q())});return h(o).pipe(Ie())}function no(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return h(!0);let s=i.map(o=>{let a=Ae(o,t),u=Bi(a)?a.canLoad(n,e):j(t,()=>a(n,e));return ee(u)});return h(s).pipe(Ie(),_n(r))}function _n(t){return Rr(y(n=>{if(typeof n!="boolean")throw Ct(t,n)}),v(n=>n===!0))}function io(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return h(!0);let s=i.map(o=>{let a=Ae(o,t),u=Gi(a)?a.canMatch(n,e):j(t,()=>a(n,e));return ee(u)});return h(s).pipe(Ie(),_n(r))}var Le=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},$e=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function ge(t){return Te(new Le(t))}function oo(t){return Te(new C(4e3,!1))}function so(t){return Te(Nn(!1,T.GuardRejected))}var rr=class{urlSerializer;urlTree;constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return h(r);if(i.numberOfChildren>1||!i.children[f])return oo(`${n.redirectTo}`);i=i.children[f]}}applyRedirectCommands(n,e,r,i,s){if(typeof e!="string"){let a=e,{queryParams:u,fragment:c,routeConfig:p,url:S,outlet:O,params:_,data:w,title:W}=i,B=j(s,()=>a({params:_,data:w,queryParams:u,fragment:c,routeConfig:p,url:S,outlet:O,title:W}));if(B instanceof $)throw new $e(B);e=B}let o=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e[0]==="/")throw new $e(o);return o}applyRedirectCreateUrlTree(n,e,r,i){let s=this.createSegmentGroup(n,e.root,r,i);return new $(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,s])=>{if(typeof s=="string"&&s[0]===":"){let a=s.substring(1);r[i]=e[a]}else r[i]=s}),r}createSegmentGroup(n,e,r,i){let s=this.createSegments(n,e.segments,r,i),o={};return Object.entries(e.children).forEach(([a,u])=>{o[a]=this.createSegmentGroup(n,u,r,i)}),new g(s,o)}createSegments(n,e,r,i){return e.map(s=>s.path[0]===":"?this.findPosParam(n,s,i):this.findOrReturn(s,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new C(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},nr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function ao(t,n,e,r,i){let s=jn(t,n,e);return s.matched?(r=Ei(n,r),io(r,n,e,i).pipe(v(o=>o===!0?s:l({},nr)))):h(s)}function jn(t,n,e){if(n.path==="**")return uo(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?l({},nr):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||hn)(e,t,n);if(!i)return l({},nr);let s={};Object.entries(i.posParams??{}).forEach(([a,u])=>{s[a]=u.path});let o=i.consumed.length>0?l(l({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function uo(t){return{matched:!0,parameters:t.length>0?fn(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function un(t,n,e,r){return e.length>0&&ho(t,e,r)?{segmentGroup:new g(n,lo(r,new g(e,t.children))),slicedSegments:[]}:e.length===0&&fo(t,e,r)?{segmentGroup:new g(t.segments,co(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new g(t.segments,t.children),slicedSegments:e}}function co(t,n,e,r){let i={};for(let s of e)if(It(t,n,s)&&!r[x(s)]){let o=new g([],{});i[x(s)]=o}return l(l({},r),i)}function lo(t,n){let e={};e[f]=n;for(let r of t)if(r.path===""&&x(r)!==f){let i=new g([],{});e[x(r)]=i}return e}function ho(t,n,e){return e.some(r=>It(t,n,r)&&x(r)!==f)}function fo(t,n,e){return e.some(r=>It(t,n,r))}function It(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function po(t,n,e){return n.length===0&&!t.children[e]}var ir=class{};function go(t,n,e,r,i,s,o="emptyOnly"){return new or(t,n,e,r,i,o,s).recognize()}var vo=31,or=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,e,r,i,s,o,a){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=a,this.applyRedirects=new rr(this.urlSerializer,this.urlTree)}noMatchError(n){return new C(4002,`'${n.segmentGroup}'`)}recognize(){let n=un(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(v(({children:e,rootSnapshot:r})=>{let i=new M(r,e),s=new je("",i),o=wn(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),{state:s,tree:o}}))}match(n){let e=new ie([],Object.freeze({}),Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),f,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,f,e).pipe(v(r=>({children:r,rootSnapshot:e})),te(r=>{if(r instanceof $e)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Le?this.noMatchError(r):r}))}processSegmentGroup(n,e,r,i,s){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r,s):this.processSegment(n,e,r,r.segments,i,!0,s).pipe(v(o=>o instanceof M?[o]:[]))}processChildren(n,e,r,i){let s=[];for(let o of Object.keys(r.children))o==="primary"?s.unshift(o):s.push(o);return b(s).pipe(re(o=>{let a=r.children[o],u=Ui(e,o);return this.processSegmentGroup(n,u,a,o,i)}),br((o,a)=>(o.push(...a),o)),xt(null),wr(),I(o=>{if(o===null)return ge(r);let a=kn(o);return mo(a),h(a)}))}processSegment(n,e,r,i,s,o,a){return b(e).pipe(re(u=>this.processSegmentAgainstRoute(u._injector??n,e,u,r,i,s,o,a).pipe(te(c=>{if(c instanceof Le)return h(null);throw c}))),Q(u=>!!u),te(u=>{if(xn(u))return po(r,i,s)?h(new ir):ge(r);throw u}))}processSegmentAgainstRoute(n,e,r,i,s,o,a,u){return x(r)!==o&&(o===f||!It(i,s,r))?ge(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,s,o,u):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,s,o,u):ge(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,s,o,a){let{matched:u,parameters:c,consumedSegments:p,positionalParamSegments:S,remainingSegments:O}=jn(e,i,s);if(!u)return ge(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>vo&&(this.allowRedirects=!1));let _=new ie(s,c,Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,cn(i),x(i),i.component??i._loadedComponent??null,i,ln(i)),w=yt(_,a,this.paramsInheritanceStrategy);_.params=Object.freeze(w.params),_.data=Object.freeze(w.data);let W=this.applyRedirects.applyRedirectCommands(p,i.redirectTo,S,_,n);return this.applyRedirects.lineralizeSegments(i,W).pipe(I(B=>this.processSegment(n,r,e,B.concat(O),o,!1,a)))}matchSegmentAgainstRoute(n,e,r,i,s,o){let a=ao(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),a.pipe(P(u=>u.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(P(({routes:c})=>{let p=r._loadedInjector??n,{parameters:S,consumedSegments:O,remainingSegments:_}=u,w=new ie(O,S,Object.freeze(l({},this.urlTree.queryParams)),this.urlTree.fragment,cn(r),x(r),r.component??r._loadedComponent??null,r,ln(r)),W=yt(w,o,this.paramsInheritanceStrategy);w.params=Object.freeze(W.params),w.data=Object.freeze(W.data);let{segmentGroup:B,slicedSegments:Dt}=un(e,O,_,c);if(Dt.length===0&&B.hasChildren())return this.processChildren(p,c,B,w).pipe(v(We=>new M(w,We)));if(c.length===0&&Dt.length===0)return h(new M(w,[]));let ii=x(r)===s;return this.processSegment(p,c,B,Dt,ii?f:s,!0,w).pipe(v(We=>new M(w,We instanceof M?[We]:[])))}))):ge(e)))}getChildConfig(n,e,r){return e.children?h({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?h({routes:e._loadedRoutes,injector:e._loadedInjector}):no(n,e,r,this.urlSerializer).pipe(I(i=>i?this.configLoader.loadChildren(n,e).pipe(y(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):so(e))):h({routes:[],injector:n})}};function mo(t){t.sort((n,e)=>n.value.outlet===f?-1:e.value.outlet===f?1:n.value.outlet.localeCompare(e.value.outlet))}function Ro(t){let n=t.value.routeConfig;return n&&n.path===""}function kn(t){let n=[],e=new Set;for(let r of t){if(!Ro(r)){n.push(r);continue}let i=n.find(s=>r.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=kn(r.children);n.push(new M(r.value,i))}return n.filter(r=>!e.has(r))}function cn(t){return t.data||{}}function ln(t){return t.resolve||{}}function So(t,n,e,r,i,s){return I(o=>go(t,n,e,r,o.extractedUrl,i,s).pipe(v(({state:a,tree:u})=>E(l({},o),{targetSnapshot:a,urlAfterRedirects:u}))))}function yo(t,n){return I(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return h(e);let s=new Set(i.map(u=>u.route)),o=new Set;for(let u of s)if(!o.has(u))for(let c of Ln(u))o.add(c);let a=0;return b(o).pipe(re(u=>s.has(u)?Co(u,r,t,n):(u.data=yt(u,u.parent,t).resolve,h(void 0))),y(()=>a++),_t(1),I(u=>a===o.size?h(e):H))})}function Ln(t){let n=t.children.map(e=>Ln(e)).flat();return[t,...n]}function Co(t,n,e,r){let i=t.routeConfig,s=t._resolve;return i?.title!==void 0&&!Dn(i)&&(s[ze]=i.title),wo(s,t,n,r).pipe(v(o=>(t._resolvedData=o,t.data=yt(t,t.parent,e).resolve,null)))}function wo(t,n,e,r){let i=Gt(t);if(i.length===0)return h({});let s={};return b(i).pipe(I(o=>bo(t[o],n,e,r).pipe(Q(),y(a=>{if(a instanceof be)throw Ct(new Y,a);s[o]=a}))),_t(1),v(()=>s),te(o=>xn(o)?H:Te(o)))}function bo(t,n,e,r){let i=Fe(n)??r,s=Ae(t,i),o=s.resolve?s.resolve(n,e):j(i,()=>s(n,e));return ee(o)}function Ht(t){return P(n=>{let e=t(n);return e?b(e).pipe(v(()=>n)):h(n)})}var cr=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(s=>s.outlet===f);return r}getResolvedTitleForRoute(e){return e.data[ze]}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:()=>d($n),providedIn:"root"})}return t})(),$n=(()=>{class t extends cr{title;constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||t)(K(rn))};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ue=new A("",{providedIn:"root",factory:()=>({})}),lr=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275cmp=Fr({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,i){r&1&&Gr(0,"router-outlet")},dependencies:[ar],encapsulation:2})}return t})();function hr(t){let n=t.children&&t.children.map(hr),e=n?E(l({},t),{children:n}):l({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==f&&(e.component=lr),e}var ce=new A(""),At=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=d(Kr);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return h(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=ee(e.loadComponent()).pipe(v(Fn),y(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),Qe(()=>{this.componentLoaders.delete(e)})),i=new Ut(r,()=>new V).pipe(Et());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return h({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let s=zn(r,this.compiler,e,this.onLoadEndListener).pipe(Qe(()=>{this.childrenLoaders.delete(r)})),o=new Ut(s,()=>new V).pipe(Et());return this.childrenLoaders.set(r,o),o}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function zn(t,n,e,r){return ee(t.loadChildren()).pipe(v(Fn),I(i=>i instanceof zr||Array.isArray(i)?h(i):b(n.compileModuleAsync(i))),v(i=>{r&&r(t);let s,o,a=!1;return Array.isArray(i)?(o=i,a=!0):(s=i.create(e).injector,o=s.get(ce,[],{optional:!0,self:!0}).flat()),{routes:o.map(hr),injector:s}}))}function Io(t){return t&&typeof t=="object"&&"default"in t}function Fn(t){return Io(t)?t.default:t}var Tt=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:()=>d(Ao),providedIn:"root"})}return t})(),Ao=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),dr=new A(""),fr=new A("");function qn(t,n,e){let r=t.get(fr),i=t.get(Yr);return t.get(Ze).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(c=>setTimeout(c));let s,o=new Promise(c=>{s=c}),a=i.startViewTransition(()=>(s(),To(t))),{onViewTransitionCreated:u}=r;return u&&j(t,()=>u({transition:a,from:n,to:e})),o})}function To(t){return new Promise(n=>{_r({read:()=>setTimeout(n)},{injector:t})})}var pr=new A(""),Ot=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new V;transitionAbortSubject=new V;configLoader=d(At);environmentInjector=d(he);destroyRef=d(Er);urlSerializer=d(se);rootContexts=d(ae);location=d(pe);inputBindingEnabled=d(qe,{optional:!0})!==null;titleStrategy=d(cr);options=d(ue,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=d(Tt);createViewTransition=d(dr,{optional:!0});navigationErrorHandler=d(pr,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>h(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new dt(i)),r=i=>this.events.next(new ft(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(E(l({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(e){return this.transitions=new U(null),this.transitions.pipe(G(r=>r!==null),P(r=>{let i=!1,s=!1;return h(r).pipe(P(o=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",T.SupersededByNewNavigation),H;this.currentTransition=r,this.currentNavigation={id:o.id,initialUrl:o.rawUrl,extractedUrl:o.extractedUrl,targetBrowserUrl:typeof o.extras.browserUrl=="string"?this.urlSerializer.parse(o.extras.browserUrl):o.extras.browserUrl,trigger:o.source,extras:o.extras,previousNavigation:this.lastSuccessfulNavigation?E(l({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=o.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!a&&u!=="reload"){let c="";return this.events.next(new z(o.id,this.urlSerializer.serialize(o.rawUrl),c,Se.IgnoredSameUrlNavigation)),o.resolve(!1),H}if(this.urlHandlingStrategy.shouldProcessUrl(o.rawUrl))return h(o).pipe(P(c=>(this.events.next(new J(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?H:Promise.resolve(c))),So(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),y(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=E(l({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let p=new Pe(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(p)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(o.currentRawUrl)){let{id:c,extractedUrl:p,source:S,restoredState:O,extras:_}=o,w=new J(c,this.urlSerializer.serialize(p),S,O);this.events.next(w);let W=On(this.rootComponentType).snapshot;return this.currentTransition=r=E(l({},o),{targetSnapshot:W,urlAfterRedirects:p,extras:E(l({},_),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=p,h(r)}else{let c="";return this.events.next(new z(o.id,this.urlSerializer.serialize(o.extractedUrl),c,Se.IgnoredByUrlHandlingStrategy)),o.resolve(!1),H}}),y(o=>{let a=new ut(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(a)}),v(o=>(this.currentTransition=r=E(l({},o),{guards:Li(o.targetSnapshot,o.currentSnapshot,this.rootContexts)}),r)),Ki(this.environmentInjector,o=>this.events.next(o)),y(o=>{if(r.guardsResult=o.guardsResult,o.guardsResult&&typeof o.guardsResult!="boolean")throw Ct(this.urlSerializer,o.guardsResult);let a=new ct(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot,!!o.guardsResult);this.events.next(a)}),G(o=>o.guardsResult?!0:(this.cancelNavigationTransition(o,"",T.GuardRejected),!1)),Ht(o=>{if(o.guards.canActivateChecks.length!==0)return h(o).pipe(y(a=>{let u=new lt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}),P(a=>{let u=!1;return h(a).pipe(yo(this.paramsInheritanceStrategy,this.environmentInjector),y({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(a,"",T.NoDataFromResolver)}}))}),y(a=>{let u=new ht(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}))}),Ht(o=>{let a=u=>{let c=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(u.routeConfig).pipe(y(p=>{u.component=p}),v(()=>{})));for(let p of u.children)c.push(...a(p));return c};return Ge(a(o.targetSnapshot.root)).pipe(xt(null),le(1))}),Ht(()=>this.afterPreactivation()),P(()=>{let{currentSnapshot:o,targetSnapshot:a}=r,u=this.createViewTransition?.(this.environmentInjector,o.root,a.root);return u?b(u).pipe(v(()=>r)):h(r)}),v(o=>{let a=Pi(e.routeReuseStrategy,o.targetSnapshot,o.currentRouterState);return this.currentTransition=r=E(l({},o),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),y(()=>{this.events.next(new xe)}),ki(this.rootContexts,e.routeReuseStrategy,o=>this.events.next(o),this.inputBindingEnabled),le(1),y({next:o=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new N(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects))),this.titleStrategy?.updateTitle(o.targetRouterState.snapshot),o.resolve(!0)},complete:()=>{i=!0}}),Ar(this.transitionAbortSubject.pipe(y(o=>{throw o}))),Qe(()=>{!i&&!s&&this.cancelNavigationTransition(r,"",T.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),te(o=>{if(this.destroyed)return r.resolve(!1),H;if(s=!0,Pn(o))this.events.next(new L(r.id,this.urlSerializer.serialize(r.extractedUrl),o.message,o.cancellationCode)),ji(o)?this.events.next(new we(o.url,o.navigationBehaviorOptions)):r.resolve(!1);else{let a=new ye(r.id,this.urlSerializer.serialize(r.extractedUrl),o,r.targetSnapshot??void 0);try{let u=j(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(u instanceof be){let{message:c,cancellationCode:p}=Ct(this.urlSerializer,u);this.events.next(new L(r.id,this.urlSerializer.serialize(r.extractedUrl),c,p)),this.events.next(new we(u.redirectTo,u.navigationBehaviorOptions))}else throw this.events.next(a),o}catch(u){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(u)}}return H}))}))}cancelNavigationTransition(e,r,i){let s=new L(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Oo(t){return t!==ot}var Bn=(()=>{class t{static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:()=>d(Mo),providedIn:"root"})}return t})(),bt=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},Mo=(()=>{class t extends bt{static \u0275fac=(()=>{let e;return function(i){return(e||(e=kt(t)))(i||t)}})();static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Vn=(()=>{class t{urlSerializer=d(se);options=d(ue,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=d(pe);urlHandlingStrategy=d(Tt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new $;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:r,targetBrowserUrl:i}){let s=e!==void 0?this.urlHandlingStrategy.merge(e,r):r,o=i??s;return o instanceof $?this.urlSerializer.serialize(o):o}commitTransition({targetRouterState:e,finalUrl:r,initialUrl:i}){r&&e?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,i),this.routerState=e):this.rawUrlTree=i}routerState=On(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:()=>d(Do),providedIn:"root"})}return t})(),Do=(()=>{class t extends Vn{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{e(r.url,r.state,"popstate")})})}handleRouterEvent(e,r){e instanceof J?this.updateStateMemento():e instanceof z?this.commitTransition(r):e instanceof Pe?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof xe?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):e instanceof L&&(e.code===T.GuardRejected||e.code===T.NoDataFromResolver)?this.restoreHistory(r):e instanceof ye?this.restoreHistory(r,!0):e instanceof N&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:r,id:i}){let{replaceUrl:s,state:o}=r;if(this.location.isCurrentPathEqualTo(e)||s){let a=this.browserPageId,u=l(l({},o),this.generateNgRouterState(i,a));this.location.replaceState(e,"",u)}else{let a=l(l({},o),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.getCurrentUrlTree()===e.finalUrl&&s===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=kt(t)))(i||t)}})();static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Mt(t,n){t.events.pipe(G(e=>e instanceof N||e instanceof L||e instanceof ye||e instanceof z),v(e=>e instanceof N||e instanceof z?0:(e instanceof L?e.code===T.Redirect||e.code===T.SupersededByNewNavigation:!1)?2:1),G(e=>e!==2),le(1)).subscribe(()=>{n()})}var Eo={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},Uo={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},q=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=d(Br);stateManager=d(Vn);options=d(ue,{optional:!0})||{};pendingTasks=d(Ur);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=d(Ot);urlSerializer=d(se);location=d(pe);urlHandlingStrategy=d(Tt);_events=new V;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=d(Bn);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=d(ce,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!d(qe,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new mr;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(r,s),r instanceof L&&r.code!==T.Redirect&&r.code!==T.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof N)this.navigated=!0;else if(r instanceof we){let o=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),u=l({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||Oo(i.source)},o);this.scheduleNavigation(a,ot,null,u,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}Po(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),ot,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r,i)=>{this.navigateToSyncWithBrowser(e,i,r)})}navigateToSyncWithBrowser(e,r,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let u=l({},i);delete u.navigationId,delete u.\u0275routerPageId,Object.keys(u).length!==0&&(s.state=u)}let a=this.parseUrl(e);this.scheduleNavigation(a,r,o,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(hr),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:a,preserveFragment:u}=r,c=u?this.currentUrlTree.fragment:o,p=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":p=l(l({},this.currentUrlTree.queryParams),s);break;case"preserve":p=this.currentUrlTree.queryParams;break;default:p=s||null}p!==null&&(p=this.removeEmptyProps(p));let S;try{let O=i?i.snapshot:this.routerState.snapshot.root;S=bn(O)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),S=this.currentUrlTree.root}return In(S,e,p,c??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=X(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,ot,null,r)}navigate(e,r={skipLocationChange:!1}){return No(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=l({},Eo):r===!1?i=l({},Uo):i=r,X(e))return nn(this.currentUrlTree,e,i);let s=this.parseUrl(e);return nn(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,s])=>(s!=null&&(r[i]=s),r),{})}scheduleNavigation(e,r,i,s,o){if(this.disposed)return Promise.resolve(!1);let a,u,c;o?(a=o.resolve,u=o.reject,c=o.promise):c=new Promise((S,O)=>{a=S,u=O});let p=this.pendingTasks.add();return Mt(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(p))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:a,reject:u,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(S=>Promise.reject(S))}static \u0275fac=function(r){return new(r||t)};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function No(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new C(4008,!1)}function Po(t){return!(t instanceof xe)&&!(t instanceof we)}var Hn=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new V;constructor(e,r,i,s,o,a){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=s,this.el=o,this.locationStrategy=a;let u=o.nativeElement.tagName?.toLowerCase();this.isAnchorElement=u==="a"||u==="area",this.isAnchorElement?this.subscription=e.events.subscribe(c=>{c instanceof N&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(X(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,r,i,s,o){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||r||i||s||o||typeof this.target=="string"&&this.target!="_self"))return!0;let u={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,u),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let r=this.href===null?null:jr(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(e,r){let i=this.renderer,s=this.el.nativeElement;r!==null?i.setAttribute(s,e,r):i.removeAttribute(s,e)}get urlTree(){return this.routerLinkInput===null?null:X(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||t)(fe(q),fe(F),Dr("tabindex"),fe(kr),fe(Pr),fe(Oe))};static \u0275dir=Xe({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Qr("click",function(o){return i.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),r&2&&Wr("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Je],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Je],replaceUrl:[2,"replaceUrl","replaceUrl",Je],routerLink:"routerLink"},features:[Ke]})}return t})();var Ve=class{};var Wn=(()=>{class t{router;injector;preloadingStrategy;loader;subscription;constructor(e,r,i,s){this.router=e,this.injector=r,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(G(e=>e instanceof N),re(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,r){let i=[];for(let s of r){s.providers&&!s._injector&&(s._injector=Ye(s.providers,e,`Route: ${s.path}`));let o=s._injector??e,a=s._loadedInjector??o;(s.loadChildren&&!s._loadedRoutes&&s.canLoad===void 0||s.loadComponent&&!s._loadedComponent)&&i.push(this.preloadConfig(o,s)),(s.children||s._loadedRoutes)&&i.push(this.processRoutes(a,s.children??s._loadedRoutes))}return b(i).pipe(Nt())}preloadConfig(e,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(e,r):i=h(null);let s=i.pipe(I(o=>o===null?h(void 0):(r._loadedRoutes=o.routes,r._loadedInjector=o.injector,this.processRoutes(o.injector??e,o.routes))));if(r.loadComponent&&!r._loadedComponent){let o=this.loader.loadComponent(r);return b([s,o]).pipe(Nt())}else return s})}static \u0275fac=function(r){return new(r||t)(K(q),K(he),K(Ve),K(At))};static \u0275prov=R({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Gn=new A(""),xo=(()=>{class t{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(e,r,i,s,o={}){this.urlSerializer=e,this.transitions=r,this.viewportScroller=i,this.zone=s,this.options=o,o.scrollPositionRestoration||="disabled",o.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof J?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof N?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof z&&e.code===Se.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof Ce&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Ce(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){Lr()};static \u0275prov=R({token:t,factory:t.\u0275fac})}return t})();function _o(t,...n){return Mr([{provide:ce,multi:!0,useValue:t},[],{provide:F,useFactory:Qn,deps:[q]},{provide:zt,multi:!0,useFactory:Kn},n.map(e=>e.\u0275providers)])}function Qn(t){return t.routerState.root}function He(t,n){return{\u0275kind:t,\u0275providers:n}}function Kn(){let t=d(Lt);return n=>{let e=t.get(Hr);if(n!==e.components[0])return;let r=t.get(q),i=t.get(Zn);t.get(vr)===1&&r.initialNavigation(),t.get(Jn,null,jt.Optional)?.setUpPreloading(),t.get(Gn,null,jt.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var Zn=new A("",{factory:()=>new V}),vr=new A("",{providedIn:"root",factory:()=>1});function Yn(){let t=[{provide:vr,useValue:0},$t(()=>{let n=d(Lt);return n.get(Xr,Promise.resolve()).then(()=>new Promise(r=>{let i=n.get(q),s=n.get(Zn);Mt(i,()=>{r(!0)}),n.get(Ot).afterPreactivation=()=>(r(!0),s.closed?h(void 0):s),i.initialNavigation()}))})];return He(2,t)}function Xn(){let t=[$t(()=>{d(q).setUpLocationChangeListener()}),{provide:vr,useValue:2}];return He(3,t)}var Jn=new A("");function ei(t){return He(0,[{provide:Jn,useExisting:Wn},{provide:Ve,useExisting:t}])}function ti(){return He(8,[ur,{provide:qe,useExisting:ur}])}function ri(t){xr("NgRouterViewTransitions");let n=[{provide:dr,useValue:qn},{provide:fr,useValue:l({skipNextTransition:!!t?.skipInitialTransition},t)}];return He(9,n)}var ni=[pe,{provide:se,useClass:Y},q,ae,{provide:F,useFactory:Qn,deps:[q]},At,[]],jo=(()=>{class t{constructor(){}static forRoot(e,r){return{ngModule:t,providers:[ni,[],{provide:ce,multi:!0,useValue:e},[],r?.errorHandler?{provide:pr,useValue:r.errorHandler}:[],{provide:ue,useValue:r||{}},r?.useHash?Lo():$o(),ko(),r?.preloadingStrategy?ei(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?zo(r):[],r?.bindToComponentInputs?ti().\u0275providers:[],r?.enableViewTransitions?ri().\u0275providers:[],Fo()]}}static forChild(e){return{ngModule:t,providers:[{provide:ce,multi:!0,useValue:e}]}}static \u0275fac=function(r){return new(r||t)};static \u0275mod=qr({type:t});static \u0275inj=Tr({})}return t})();function ko(){return{provide:Gn,useFactory:()=>{let t=d(tn),n=d(Ze),e=d(ue),r=d(Ot),i=d(se);return e.scrollOffset&&t.setOffset(e.scrollOffset),new xo(i,r,t,n,e)}}}function Lo(){return{provide:Oe,useClass:en}}function $o(){return{provide:Oe,useClass:Jr}}function zo(t){return[t.initialNavigation==="disabled"?Xn().\u0275providers:[],t.initialNavigation==="enabledBlocking"?Yn().\u0275providers:[]]}var gr=new A("");function Fo(){return[{provide:gr,useFactory:Kn},{provide:zt,multi:!0,useExisting:gr}]}export{F as a,ar as b,q as c,Hn as d,_o as e,jo as f};
