#!/bin/bash

# Create icons directory if it doesn't exist
mkdir -p public/assets/icons

# Create placeholder icons of different sizes
# This is a simple script to create colored squares with size text
# In a real app, you would use proper icons

SIZES=(72 96 128 144 152 192 384 512)
COLOR="#3f51b5"  # Primary color

for size in "${SIZES[@]}"; do
  echo "Creating icon of size ${size}x${size}"
  
  # Use ImageMagick if available, otherwise just create empty files
  if command -v convert &> /dev/null; then
    convert -size "${size}x${size}" xc:"${COLOR}" \
      -gravity center -pointsize $((size/4)) -fill white \
      -annotate 0 "${size}" \
      "public/assets/icons/icon-${size}x${size}.png"
  else
    # If ImageMagick is not available, just create empty files
    touch "public/assets/icons/icon-${size}x${size}.png"
    echo "Warning: ImageMagick not found. Created empty placeholder file."
  fi
done

echo "Icon creation complete!"
