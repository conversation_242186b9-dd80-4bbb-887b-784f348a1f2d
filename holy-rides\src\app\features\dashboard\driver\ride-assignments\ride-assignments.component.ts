import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, signal, ViewChild, AfterViewInit, computed, effect } from '@angular/core';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RideService } from '../../../../core/services/ride.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Ride, RideStatus } from '../../../../core/models/ride.model';
import { User } from '../../../../core/models/user.model';
import { RideNavigationComponent } from '../ride-navigation/ride-navigation.component';
import { MessageService } from '../../../../core/services/message.service';
import { RideDetailComponent } from '../../../../shared/components/ride-detail/ride-detail.component';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'app-ride-assignments',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    MatTableModule,
    MatChipsModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatBadgeModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    RideNavigationComponent,
    RideDetailComponent,
    MatSortModule,
    MatPaginatorModule,
    MatExpansionModule
  ],
  template: `
    <div class="assignments-container">
      <mat-tab-group>
        <mat-tab label="Available Rides" [tabIndex]="0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>Available Ride Requests <span class="realtime-indicator" title="Realtime updates active">●</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div> 
            
            <div *ngIf="!loading && availableRides().length === 0" class="no-rides">
              <p>No available ride requests at this time.</p>
            </div>

            <ng-container *ngIf="!loading && availableRides().length > 0">
              <div class="desktop-view">
                <table mat-table [dataSource]="availableDataSource"  #availableSort="matSort"  matSort class="ride-table">
                  <ng-container matColumnDef="pickup_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="dropoff_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="pickup_time">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="fare">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.fare ? ( ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let ride">
                      <button mat-raised-button color="primary" (click)="acceptRide(ride.id)">
                        Accept
                      </button>
                    </td>
                  </ng-container>
    
                  <tr mat-header-row *matHeaderRowDef="availableColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: availableColumns;"></tr>
                </table>
                <mat-paginator #availablePaginator [pageSizeOptions]="[3,5, 10, 25, 100]" [pageSize]="3" showFirstLastButtons aria-label="Select page of available rides"></mat-paginator>
              </div>

              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let ride of availableRides()">
                    <mat-expansion-panel-header>
                      <mat-panel-description>
                        {{ ride.pickup_location }}
                      </mat-panel-description>
                      <mat-panel-title>
                        {{ ride.pickup_time | date:'shortTime' }}
                      </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div class="ride-details">
                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>
                    </div>
                    <mat-action-row>
                      <button mat-raised-button color="primary" (click)="acceptRide(ride.id)">
                        Accept
                      </button>
                    </mat-action-row>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </ng-container>
          </div>
        </mat-tab>

        <mat-tab label="My Rides" [tabIndex]="1" [disabled]="myRides().length === 0">
          <div class="table-container">
            <div class="header-with-actions">
              <h3>My Assigned Rides <span class="realtime-indicator" title="Realtime updates active">●</span></h3>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Manual refresh">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
            
            <div class="filter-container" *ngIf="myRides().length > 0">
              <div class="filter-buttons" aria-label="Filter rides by status">
                <button mat-stroked-button
                        [color]="statusFilter() === 'all' ? 'primary' : ''"
                        (click)="statusFilter.set('all')"
                        class="filter-button">All</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'assigned' ? 'primary' : ''"
                        (click)="statusFilter.set('assigned')"
                        class="filter-button">Assigned</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'in-progress' ? 'primary' : ''"
                        (click)="statusFilter.set('in-progress')"
                        class="filter-button">In Progress</button>
                <button mat-stroked-button
                        [color]="statusFilter() === 'completed' ? 'primary' : ''"
                        (click)="statusFilter.set('completed')"
                        class="filter-button">Completed</button>
              </div>
            </div>

            <div *ngIf="loading" class="loading-container">
              <mat-spinner diameter="40"></mat-spinner>
              <p>Loading rides...</p>
            </div>
            <div *ngIf="!loading && myRides().length === 0" class="no-rides">
              <p>You don't have any assigned rides.</p>
            </div>
            <div *ngIf="!loading && filteredMyRides().length === 0 && myRides().length > 0" class="no-rides">
              <p>No rides match the current filter.</p>
            </div>

            <ng-container *ngIf="!loading && filteredMyRides().length > 0">
              <div class="desktop-view">
                <table mat-table [dataSource]="myRidesDataSource" matSort #myRidesSort="matSort" class="ride-table">
                  <ng-container matColumnDef="pickup_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="dropoff_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Destination</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="pickup_time">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Time</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_time | date:'short' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                    <td mat-cell *matCellDef="let ride">
                      <span class="status-chip" [ngClass]="'status-' + ride.status">
                        {{ ride.status }}
                      </span>
                    </td>
                  </ng-container>
    
                  <ng-container matColumnDef="fare">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Fare</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.fare ? (ride.fare * .7 | currency:'USD') : 'TBD' }}</td>
                  </ng-container>
    
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let ride">
                      <button mat-raised-button color="primary" *ngIf="ride.status === 'assigned'" (click)="startRide(ride.id)">
                        Start Ride
                      </button>
                      <button mat-raised-button color="warn" *ngIf="ride.status === 'assigned'" (click)="cancelAssignment(ride.id)" style="margin-left: 8px;">
                        Cancel Assignment
                      </button>
                      <button mat-raised-button color="accent" *ngIf="ride.status === 'in-progress'" (click)="completeRide(ride.id)">
                        Complete
                      </button>
                      <button mat-icon-button color="primary" *ngIf="ride.status !== 'completed'" (click)="showNavigation(ride)">
                        <mat-icon>navigation</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </td>
                  </ng-container>
    
                  <tr mat-header-row *matHeaderRowDef="myRidesColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: myRidesColumns;"></tr>
                </table>
                <mat-paginator #myRidesPaginator [pageSizeOptions]="[5, 10, 25, 100]" [pageSize]="5" showFirstLastButtons aria-label="Select page of my rides"></mat-paginator>
              </div>

              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let ride of filteredMyRides()">
                    <mat-expansion-panel-header>
                      <mat-panel-description>
                        {{ ride.pickup_location }}
                                                  <button mat-icon-button color="primary" *ngIf="ride.status === 'assigned'" (click)="startRide(ride.id); $event.stopPropagation()" matTooltip="Start Ride">
                            <mat-icon>play_arrow</mat-icon>
                          </button>
                          <button mat-icon-button color="warn" *ngIf="ride.status === 'assigned'" (click)="cancelAssignment(ride.id); $event.stopPropagation()" matTooltip="Cancel Assignment">
                            <mat-icon>cancel</mat-icon>
                          </button>
                          <button mat-icon-button color="accent" *ngIf="ride.status === 'in-progress'" (click)="completeRide(ride.id); $event.stopPropagation()" matTooltip="Complete Ride">
                            <mat-icon>check_circle</mat-icon>
                          </button>
                      </mat-panel-description>
                      <mat-panel-title>
                        <div class="ride-actions-header">
                          <span class="status-chip" [ngClass]="'status-' + ride.status">{{ ride.status }}</span>

                        </div>
                      </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div class="ride-details">
                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                      <p><strong>Time:</strong> {{ ride.pickup_time | date:'short' }}</p>
                      <p><strong>Fare:</strong> {{ ride.fare ? (ride.fare * 0.7 | currency:'USD') : 'TBD' }}</p>
                    </div>
                    <mat-action-row>
                      <button mat-icon-button color="primary" *ngIf="ride.status !== 'completed'" (click)="showNavigation(ride)" matTooltip="Navigation">
                        <mat-icon>navigation</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </mat-action-row>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </ng-container>
          </div>
        </mat-tab>
      </mat-tab-group>

      <app-ride-navigation
        *ngIf="selectedRide"
        [ride]="selectedRide"
        (close)="selectedRide = null">
      </app-ride-navigation>

      <div *ngIf="selectedRideId" class="ride-detail-overlay">
        <app-ride-detail
          [rideId]="selectedRideId"
          [onClose]="closeRideDetails.bind(this)"
          (rideUpdated)="onRideUpdated($event)">
        </app-ride-detail>
      </div>
    </div>
  `,
  styles: [`
.mat-expansion-panel-header-description {
    flex-grow: 4 !important;
}

    .assignments-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .table-container {
      margin: 20px;
    }

    .ride-table {
      width: 100%;
    }

    
    .no-rides {
      padding: 20px;
      text-align: center;
      color: #666;
      font-style: italic;
    }

    .status-chip {
      border-radius: 16px;
      padding: 4px 12px;
      color: white;
      font-weight: 500;
    }

    .status-requested {
      background-color: #ff9800;
    }

    .status-assigned {
      background-color: #2196f3;
    }

    .status-in-progress {
      background-color: #673ab7;
    }

    .status-completed {
      background-color: #4caf50;
    }

    .status-canceled {
      background-color: #f44336;
    }

    .header-with-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      color: #666;
    }

    .loading-container p {
      margin-top: 10px;
    }

    .ride-detail-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .realtime-indicator {
      color: #4caf50;
      font-size: 12px;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .desktop-view {
      display: block;
    }
    .mobile-view {
      display: none;
    }

    .filter-container {
      margin: 0 20px 20px;
    }
    
    .ride-actions-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    @media (max-width: 600px) {
      .desktop-view {
        display: none;
      }
      .mobile-view {
        display: block;
      }
      .assignments-container {
        padding: 0;
      }
      .table-container {
        margin: 0;
      }
      .mat-tab-body-content {
        overflow: hidden;
      }
      .filter-container {
        margin: 0 0 16px;
      }
    }

    .mobile-view .mat-expansion-panel {
      margin: 8px 0;
    }
    .mobile-view .mat-expansion-panel-header {
      font-size: 14px;
    }
    .mobile-view .mat-panel-title {
      font-weight: 500;
    }
    .mobile-view .mat-panel-description {
      justify-content: flex-end;
      align-items: center;
    }
    .mobile-view .ride-details {
      padding: 0 24px 16px;
      font-size: 14px;
    }
    .mobile-view .ride-details p {
      margin: 4px 0;
    }
    .mobile-view .mat-action-row {
      justify-content: flex-end;
      padding: 8px 12px 8px 24px;
    }
  `]
})
export class RideAssignmentsComponent implements OnInit, OnDestroy, AfterViewInit {

  currentUser: User | null = null;
  selectedRide: Ride | null = null;
  selectedRideId: string | null = null;
  loading: boolean = false;
  private ridesSubscription: Subscription | null = null;

  availableRides = signal<Ride[]>([]);
  myRides = signal<Ride[]>([]);
  
  statusFilter = signal<RideStatus | 'all'>('all');
  filteredMyRides = computed(() => {
    const rides = this.myRides();
    const filter = this.statusFilter();
    if (filter === 'all') {
      return rides;
    }
    return rides.filter(ride => ride.status === filter);
  });

  availableColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'fare', 'actions'];
  myRidesColumns: string[] = ['pickup_location', 'dropoff_location', 'pickup_time', 'status', 'fare', 'actions'];

  availableDataSource = new MatTableDataSource<Ride>([]);
  myRidesDataSource = new MatTableDataSource<Ride>([]);

  @ViewChild('availableSort') availableSort!: MatSort;
  @ViewChild('availablePaginator') availablePaginator!: MatPaginator;
  @ViewChild('myRidesSort') myRidesSort!: MatSort;
  @ViewChild('myRidesPaginator') myRidesPaginator!: MatPaginator;

  constructor(
    private rideService: RideService,
    private authService: AuthService,
    private messageService: MessageService,
    private router: Router,
    private snackBar: MatSnackBar
  ) { 
    effect(() => {
      const filter = this.statusFilter();
      const allMyRides = this.myRides();
      if (filter === 'all') {
          this.myRidesDataSource.data = allMyRides;
      } else {
          this.myRidesDataSource.data = allMyRides.filter(ride => ride.status === filter);
      }
    });
  }

  async ngOnInit() {
    try {
      await this.loadCurrentUser();
      if (this.currentUser) {
        // Set up realtime subscription to rides
        this.setupRealtimeSubscription();
        // Initial load of rides
        await this.loadRides();
        console.log('✅ Driver dashboard initialized with realtime updates');
      }
    } catch (error) {
      console.error('❌ Error loading current user:', error);
      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });
    }
  }

  ngAfterViewInit() {
    // Setup data sources after view is initialized
    setTimeout(() => {
      this.setupDataSources();
    }, 0);
  }

  ngOnDestroy(): void {
    if (this.ridesSubscription) {
      this.ridesSubscription.unsubscribe();
    }
  }




  private async loadCurrentUser(): Promise<void> {
    try {
      this.loading = true;
      this.currentUser = await this.authService.getCurrentUser();
    } catch (error) {
      console.error('Error loading current user:', error);
      this.snackBar.open('Failed to load user information', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  private setupRealtimeSubscription(): void {
    if (!this.currentUser) return;

    console.log('🔄 Setting up realtime subscription for driver:', this.currentUser.id);

    this.ridesSubscription = this.rideService.rides$.subscribe((allRides: Ride[]) => {
      if (allRides && allRides.length >= 0) {
        const available = allRides.filter(ride => ride.status === 'requested');
        const myRides = allRides.filter(ride =>
          ride.driver_id === this.currentUser?.id &&
          (['assigned', 'in-progress', 'completed'] as RideStatus[]).includes(ride.status)
        );

        this.availableRides.set(available);
        this.myRides.set(myRides);

        this.availableDataSource.data = available;
        
        const currentAvailableCount = this.availableDataSource.data.length;
        if (available.length > currentAvailableCount && currentAvailableCount > 0) {
          this.snackBar.open(`${available.length - currentAvailableCount} new ride(s) available!`, 'View', {
            duration: 5000,
            panelClass: ['success-snackbar']
          });
        }
      }
    });
  }
  private setupDataSources() {
    console.log('Setting up data sources...');
    if (this.availableSort) {
      this.availableDataSource.sort = this.availableSort;
    }
    if (this.availablePaginator) {
      this.availableDataSource.paginator = this.availablePaginator;
    }

    if (this.myRidesSort) {
      this.myRidesDataSource.sort = this.myRidesSort;
    }
    if (this.myRidesPaginator) {
      this.myRidesDataSource.paginator = this.myRidesPaginator;
    }

    this.availableDataSource.sortingDataAccessor = (item: Ride, property: string) => {
      switch (property) {
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        case 'pickup_location':
        case 'dropoff_location':
          return (item as any)[property]?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };

    this.myRidesDataSource.sortingDataAccessor = (item: Ride, property: string) => {
      switch (property) {
        case 'pickup_time':
          return item[property] ? new Date(item[property]).getTime() : 0;
        case 'fare':
          return item.fare || 0;
        case 'pickup_location':
        case 'dropoff_location':
        case 'status':
          return (item as any)[property]?.toLowerCase() || '';
        default:
          return (item as any)[property] || '';
      }
    };
  }


  async loadRides(): Promise<void> {
    if (!this.currentUser) return;
    console.log('🔄 Loading rides...');
    this.loading = true;
    try {
      await this.rideService.getAllRides();

      const [available, assigned] = await Promise.all([
        this.rideService.getAvailableRides(),
        this.rideService.getDriverRides(this.currentUser.id)
      ]);

      this.availableRides.set(available);
      this.myRides.set(assigned);

      this.availableDataSource.data = available;

      setTimeout(() => {
        this.setupDataSources();
      }, 0);

      this.snackBar.open('Rides refreshed', 'Close', { duration: 2000 });
    } catch (error: any) {
      console.error('❌ Error loading rides:', error);
      this.snackBar.open(error.message || 'Failed to load rides', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  async acceptRide(rideId: string): Promise<void> {
    if (!this.currentUser) return;

    try {
      await this.rideService.acceptRide(rideId, this.currentUser.id);
      this.snackBar.open('Ride accepted successfully', 'Close', { duration: 3000 });
    } catch (error: any) {
      console.error('Error accepting ride:', error);
      this.snackBar.open(error.message || 'Failed to accept ride', 'Close', { duration: 3000 });
    }
  }

  async startRide(rideId: string): Promise<void> {
    try {
      await this.rideService.startRide(rideId);
      this.snackBar.open('Ride started successfully', 'Close', { duration: 3000 });
    } catch (error: any) {
      console.error('Error starting ride:', error);
      this.snackBar.open(error.message || 'Failed to start ride', 'Close', { duration: 3000 });
    }
  }

  async completeRide(rideId: string): Promise<void> {
    try {
      await this.rideService.completeRide(rideId);
      this.snackBar.open('Ride completed successfully', 'Close', { duration: 3000 });
    } catch (error: any) {
      console.error('Error completing ride:', error);
      this.snackBar.open(error.message || 'Failed to complete ride', 'Close', { duration: 3000 });
    }
  }

  async cancelAssignment(rideId: string): Promise<void> {
    try {
      await this.rideService.cancelDriverAssignment(rideId);
      this.snackBar.open('Assignment cancelled successfully. Ride returned to available requests.', 'Close', { duration: 4000 });
    } catch (error: any) {
      console.error('Error cancelling assignment:', error);
      this.snackBar.open(error.message || 'Failed to cancel assignment', 'Close', { duration: 3000 });
    }
  }

  showNavigation(ride: Ride): void {
    this.selectedRide = ride;
  }

  closeNavigation(): void {
    this.selectedRide = null;
  }

  async openChat(rideId: string) {
    try {
      const thread = await this.messageService.getOrCreateThreadForRide(rideId);
      await this.router.navigate(['/dashboard', 'driver', 'messages', thread.id]);
    } catch (error: any) {
      console.error('Error opening chat:', error);
      this.snackBar.open(error.message || 'Failed to open chat', 'Close', { duration: 3000 });
    }
  }

  viewRideDetails(rideId: string) {
    this.selectedRideId = rideId;
  }

  closeRideDetails() {
    this.selectedRideId = null;
  }

  onRideUpdated(_ride: Ride) {
  }

  trackByRideId(_index: number, item: Ride): string {
    return item.id;
  }
}
