{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse } from './lib/fetch';\nimport { resolveFetch } from './lib/helpers';\nimport { isAuthError } from './lib/errors';\nexport default class GoTrueAdminApi {\n  constructor({\n    url = '',\n    headers = {},\n    fetch\n  }) {\n    this.url = url;\n    this.headers = headers;\n    this.fetch = resolveFetch(fetch);\n    this.mfa = {\n      listFactors: this._listFactors.bind(this),\n      deleteFactor: this._deleteFactor.bind(this)\n    };\n  }\n  /**\n   * Removes a logged-in session.\n   * @param jwt A valid, logged-in JWT.\n   * @param scope The logout sope.\n   */\n  signOut(_x) {\n    var _this = this;\n    return _asyncToGenerator(function* (jwt, scope = 'global') {\n      try {\n        yield _request(_this.fetch, 'POST', `${_this.url}/logout?scope=${scope}`, {\n          headers: _this.headers,\n          jwt,\n          noResolveJson: true\n        });\n        return {\n          data: null,\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Sends an invite link to an email address.\n   * @param email The email address of the user.\n   * @param options Additional options to be included when inviting.\n   */\n  inviteUserByEmail(_x2) {\n    var _this2 = this;\n    return _asyncToGenerator(function* (email, options = {}) {\n      try {\n        return yield _request(_this2.fetch, 'POST', `${_this2.url}/invite`, {\n          body: {\n            email,\n            data: options.data\n          },\n          headers: _this2.headers,\n          redirectTo: options.redirectTo,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Generates email links and OTPs to be sent via a custom email provider.\n   * @param email The user's email.\n   * @param options.password User password. For signup only.\n   * @param options.data Optional user metadata. For signup only.\n   * @param options.redirectTo The redirect url which should be appended to the generated link\n   */\n  generateLink(params) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n            options\n          } = params,\n          rest = __rest(params, [\"options\"]);\n        const body = Object.assign(Object.assign({}, rest), options);\n        if ('newEmail' in rest) {\n          // replace newEmail with new_email in request body\n          body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n          delete body['newEmail'];\n        }\n        return yield _request(_this3.fetch, 'POST', `${_this3.url}/admin/generate_link`, {\n          body: body,\n          headers: _this3.headers,\n          xform: _generateLinkResponse,\n          redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              properties: null,\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  // User Admin API\n  /**\n   * Creates a new user.\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  createUser(attributes) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _request(_this4.fetch, 'POST', `${_this4.url}/admin/users`, {\n          body: attributes,\n          headers: _this4.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Get a list of users.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n   */\n  listUsers(params) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d, _e, _f, _g;\n      try {\n        const pagination = {\n          nextPage: null,\n          lastPage: 0,\n          total: 0\n        };\n        const response = yield _request(_this5.fetch, 'GET', `${_this5.url}/admin/users`, {\n          headers: _this5.headers,\n          noResolveJson: true,\n          query: {\n            page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n            per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : ''\n          },\n          xform: _noResolveJsonResponse\n        });\n        if (response.error) throw response.error;\n        const users = yield response.json();\n        const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n        const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n        if (links.length > 0) {\n          links.forEach(link => {\n            const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n            const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n            pagination[`${rel}Page`] = page;\n          });\n          pagination.total = parseInt(total);\n        }\n        return {\n          data: Object.assign(Object.assign({}, users), pagination),\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              users: []\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Get user by id.\n   *\n   * @param uid The user's unique identifier\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  getUserById(uid) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _request(_this6.fetch, 'GET', `${_this6.url}/admin/users/${uid}`, {\n          headers: _this6.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Updates the user data.\n   *\n   * @param attributes The data you want to update.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  updateUserById(uid, attributes) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _request(_this7.fetch, 'PUT', `${_this7.url}/admin/users/${uid}`, {\n          body: attributes,\n          headers: _this7.headers,\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Delete a user. Requires a `service_role` key.\n   *\n   * @param id The user id you want to remove.\n   * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema.\n   * Defaults to false for backward compatibility.\n   *\n   * This function should only be called on a server. Never expose your `service_role` key in the browser.\n   */\n  deleteUser(_x3) {\n    var _this8 = this;\n    return _asyncToGenerator(function* (id, shouldSoftDelete = false) {\n      try {\n        return yield _request(_this8.fetch, 'DELETE', `${_this8.url}/admin/users/${id}`, {\n          headers: _this8.headers,\n          body: {\n            should_soft_delete: shouldSoftDelete\n          },\n          xform: _userResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  _listFactors(params) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _request(_this9.fetch, 'GET', `${_this9.url}/admin/users/${params.userId}/factors`, {\n          headers: _this9.headers,\n          xform: factors => {\n            return {\n              data: {\n                factors\n              },\n              error: null\n            };\n          }\n        });\n        return {\n          data,\n          error\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  _deleteFactor(params) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const data = yield _request(_this0.fetch, 'DELETE', `${_this0.url}/admin/users/${params.userId}/factors/${params.id}`, {\n          headers: _this0.headers\n        });\n        return {\n          data,\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n}", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "_generateLinkResponse", "_noResolveJsonResponse", "_request", "_userResponse", "resolveFetch", "isAuthError", "GoTrueAdminApi", "constructor", "url", "headers", "fetch", "mfa", "listFactors", "_listFactors", "bind", "deleteFactor", "_deleteFactor", "signOut", "_x", "_this", "_asyncToGenerator", "jwt", "scope", "noResolveJson", "data", "error", "apply", "arguments", "inviteUserByEmail", "_x2", "_this2", "email", "options", "body", "redirectTo", "xform", "user", "generateLink", "params", "_this3", "rest", "assign", "new_email", "newEmail", "properties", "createUser", "attributes", "_this4", "listUsers", "_this5", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "pagination", "nextPage", "lastPage", "total", "response", "query", "page", "toString", "per_page", "perPage", "users", "json", "get", "links", "split", "for<PERSON>ach", "link", "parseInt", "substring", "rel", "JSON", "parse", "getUserById", "uid", "_this6", "updateUserById", "_this7", "deleteUser", "_x3", "_this8", "id", "shouldSoftDelete", "should_soft_delete", "_this9", "userId", "factors", "_this0"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js"], "sourcesContent": ["var __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { _generateLinkResponse, _noResolveJsonResponse, _request, _userResponse, } from './lib/fetch';\nimport { resolveFetch } from './lib/helpers';\nimport { isAuthError } from './lib/errors';\nexport default class GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = resolveFetch(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = 'global') {\n        try {\n            await _request(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await _request(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await _request(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        try {\n            return await _request(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        try {\n            return await _request(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        try {\n            return await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _userResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        try {\n            const { data, error } = await _request(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        try {\n            const data = await _request(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n"], "mappings": ";AAAA,IAAIA,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAC/ED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACf,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAC/D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACpE,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAC1ER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACzB;EACJ,OAAOR,CAAC;AACZ,CAAC;AACD,SAASW,qBAAqB,EAAEC,sBAAsB,EAAEC,QAAQ,EAAEC,aAAa,QAAS,aAAa;AACrG,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,WAAW,QAAQ,cAAc;AAC1C,eAAe,MAAMC,cAAc,CAAC;EAChCC,WAAWA,CAAC;IAAEC,GAAG,GAAG,EAAE;IAAEC,OAAO,GAAG,CAAC,CAAC;IAAEC;EAAO,CAAC,EAAE;IAC5C,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGN,YAAY,CAACM,KAAK,CAAC;IAChC,IAAI,CAACC,GAAG,GAAG;MACPC,WAAW,EAAE,IAAI,CAACC,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC;MACzCC,YAAY,EAAE,IAAI,CAACC,aAAa,CAACF,IAAI,CAAC,IAAI;IAC9C,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACUG,OAAOA,CAAAC,EAAA,EAAwB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,YAAvBC,GAAG,EAAEC,KAAK,GAAG,QAAQ;MAC/B,IAAI;QACA,MAAMpB,QAAQ,CAACiB,KAAI,CAACT,KAAK,EAAE,MAAM,EAAE,GAAGS,KAAI,CAACX,GAAG,iBAAiBc,KAAK,EAAE,EAAE;UACpEb,OAAO,EAAEU,KAAI,CAACV,OAAO;UACrBY,GAAG;UACHE,aAAa,EAAE;QACnB,CAAC,CAAC;QACF,OAAO;UAAEC,IAAI,EAAE,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MACtC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC,GAAAC,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;AACA;EACUC,iBAAiBA,CAAAC,GAAA,EAAsB;IAAA,IAAAC,MAAA;IAAA,OAAAV,iBAAA,YAArBW,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC;MACvC,IAAI;QACA,aAAa9B,QAAQ,CAAC4B,MAAI,CAACpB,KAAK,EAAE,MAAM,EAAE,GAAGoB,MAAI,CAACtB,GAAG,SAAS,EAAE;UAC5DyB,IAAI,EAAE;YAAEF,KAAK;YAAEP,IAAI,EAAEQ,OAAO,CAACR;UAAK,CAAC;UACnCf,OAAO,EAAEqB,MAAI,CAACrB,OAAO;UACrByB,UAAU,EAAEF,OAAO,CAACE,UAAU;UAC9BC,KAAK,EAAEhC;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAOsB,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAEY,IAAI,EAAE;YAAK,CAAC;YAAEX;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC,GAAAC,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUU,YAAYA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACvB,IAAI;QACA,MAAM;YAAEY;UAAQ,CAAC,GAAGM,MAAM;UAAEE,IAAI,GAAGtD,MAAM,CAACoD,MAAM,EAAE,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAML,IAAI,GAAG1C,MAAM,CAACkD,MAAM,CAAClD,MAAM,CAACkD,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC,EAAER,OAAO,CAAC;QAC5D,IAAI,UAAU,IAAIQ,IAAI,EAAE;UACpB;UACAP,IAAI,CAACS,SAAS,GAAGF,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,QAAQ;UAC1E,OAAOV,IAAI,CAAC,UAAU,CAAC;QAC3B;QACA,aAAa/B,QAAQ,CAACqC,MAAI,CAAC7B,KAAK,EAAE,MAAM,EAAE,GAAG6B,MAAI,CAAC/B,GAAG,sBAAsB,EAAE;UACzEyB,IAAI,EAAEA,IAAI;UACVxB,OAAO,EAAE8B,MAAI,CAAC9B,OAAO;UACrB0B,KAAK,EAAEnC,qBAAqB;UAC5BkC,UAAU,EAAEF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE;QAC1E,CAAC,CAAC;MACN,CAAC,CACD,OAAOT,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YACHD,IAAI,EAAE;cACFoB,UAAU,EAAE,IAAI;cAChBR,IAAI,EAAE;YACV,CAAC;YACDX;UACJ,CAAC;QACL;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;EACA;AACJ;AACA;AACA;EACUoB,UAAUA,CAACC,UAAU,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MACzB,IAAI;QACA,aAAalB,QAAQ,CAAC6C,MAAI,CAACrC,KAAK,EAAE,MAAM,EAAE,GAAGqC,MAAI,CAACvC,GAAG,cAAc,EAAE;UACjEyB,IAAI,EAAEa,UAAU;UAChBrC,OAAO,EAAEsC,MAAI,CAACtC,OAAO;UACrB0B,KAAK,EAAEhC;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAOsB,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAEY,IAAI,EAAE;YAAK,CAAC;YAAEX;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACUuB,SAASA,CAACV,MAAM,EAAE;IAAA,IAAAW,MAAA;IAAA,OAAA7B,iBAAA;MACpB,IAAI8B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAC9B,IAAI;QACA,MAAMC,UAAU,GAAG;UAAEC,QAAQ,EAAE,IAAI;UAAEC,QAAQ,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAC;QAC5D,MAAMC,QAAQ,SAAS3D,QAAQ,CAAC+C,MAAI,CAACvC,KAAK,EAAE,KAAK,EAAE,GAAGuC,MAAI,CAACzC,GAAG,cAAc,EAAE;UAC1EC,OAAO,EAAEwC,MAAI,CAACxC,OAAO;UACrBc,aAAa,EAAE,IAAI;UACnBuC,KAAK,EAAE;YACHC,IAAI,EAAE,CAACZ,EAAE,GAAG,CAACD,EAAE,GAAGZ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACyB,IAAI,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;YACtKc,QAAQ,EAAE,CAACZ,EAAE,GAAG,CAACD,EAAE,GAAGd,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4B,OAAO,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;UAC/K,CAAC;UACDlB,KAAK,EAAElC;QACX,CAAC,CAAC;QACF,IAAI4D,QAAQ,CAACpC,KAAK,EACd,MAAMoC,QAAQ,CAACpC,KAAK;QACxB,MAAM0C,KAAK,SAASN,QAAQ,CAACO,IAAI,CAAC,CAAC;QACnC,MAAMR,KAAK,GAAG,CAACN,EAAE,GAAGO,QAAQ,CAACpD,OAAO,CAAC4D,GAAG,CAAC,eAAe,CAAC,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;QAC7F,MAAMgB,KAAK,GAAG,CAACd,EAAE,GAAG,CAACD,EAAE,GAAGM,QAAQ,CAACpD,OAAO,CAAC4D,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;QAC/I,IAAIc,KAAK,CAACxE,MAAM,GAAG,CAAC,EAAE;UAClBwE,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAK;YACpB,MAAMV,IAAI,GAAGW,QAAQ,CAACD,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,MAAMC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxDd,UAAU,CAAC,GAAGmB,GAAG,MAAM,CAAC,GAAGb,IAAI;UACnC,CAAC,CAAC;UACFN,UAAU,CAACG,KAAK,GAAGc,QAAQ,CAACd,KAAK,CAAC;QACtC;QACA,OAAO;UAAEpC,IAAI,EAAEjC,MAAM,CAACkD,MAAM,CAAClD,MAAM,CAACkD,MAAM,CAAC,CAAC,CAAC,EAAE0B,KAAK,CAAC,EAAEV,UAAU,CAAC;UAAEhC,KAAK,EAAE;QAAK,CAAC;MACrF,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAE2C,KAAK,EAAE;YAAG,CAAC;YAAE1C;UAAM,CAAC;QACzC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUsD,WAAWA,CAACC,GAAG,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACnB,IAAI;QACA,aAAalB,QAAQ,CAAC+E,MAAI,CAACvE,KAAK,EAAE,KAAK,EAAE,GAAGuE,MAAI,CAACzE,GAAG,gBAAgBwE,GAAG,EAAE,EAAE;UACvEvE,OAAO,EAAEwE,MAAI,CAACxE,OAAO;UACrB0B,KAAK,EAAEhC;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAOsB,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAEY,IAAI,EAAE;YAAK,CAAC;YAAEX;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUyD,cAAcA,CAACF,GAAG,EAAElC,UAAU,EAAE;IAAA,IAAAqC,MAAA;IAAA,OAAA/D,iBAAA;MAClC,IAAI;QACA,aAAalB,QAAQ,CAACiF,MAAI,CAACzE,KAAK,EAAE,KAAK,EAAE,GAAGyE,MAAI,CAAC3E,GAAG,gBAAgBwE,GAAG,EAAE,EAAE;UACvE/C,IAAI,EAAEa,UAAU;UAChBrC,OAAO,EAAE0E,MAAI,CAAC1E,OAAO;UACrB0B,KAAK,EAAEhC;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAOsB,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAEY,IAAI,EAAE;YAAK,CAAC;YAAEX;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU2D,UAAUA,CAAAC,GAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAlE,iBAAA,YAA9BmE,EAAE,EAAEC,gBAAgB,GAAG,KAAK;MACzC,IAAI;QACA,aAAatF,QAAQ,CAACoF,MAAI,CAAC5E,KAAK,EAAE,QAAQ,EAAE,GAAG4E,MAAI,CAAC9E,GAAG,gBAAgB+E,EAAE,EAAE,EAAE;UACzE9E,OAAO,EAAE6E,MAAI,CAAC7E,OAAO;UACrBwB,IAAI,EAAE;YACFwD,kBAAkB,EAAED;UACxB,CAAC;UACDrD,KAAK,EAAEhC;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAOsB,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE;cAAEY,IAAI,EAAE;YAAK,CAAC;YAAEX;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC,GAAAC,KAAA,OAAAC,SAAA;EACL;EACMd,YAAYA,CAACyB,MAAM,EAAE;IAAA,IAAAoD,MAAA;IAAA,OAAAtE,iBAAA;MACvB,IAAI;QACA,MAAM;UAAEI,IAAI;UAAEC;QAAM,CAAC,SAASvB,QAAQ,CAACwF,MAAI,CAAChF,KAAK,EAAE,KAAK,EAAE,GAAGgF,MAAI,CAAClF,GAAG,gBAAgB8B,MAAM,CAACqD,MAAM,UAAU,EAAE;UAC1GlF,OAAO,EAAEiF,MAAI,CAACjF,OAAO;UACrB0B,KAAK,EAAGyD,OAAO,IAAK;YAChB,OAAO;cAAEpE,IAAI,EAAE;gBAAEoE;cAAQ,CAAC;cAAEnE,KAAK,EAAE;YAAK,CAAC;UAC7C;QACJ,CAAC,CAAC;QACF,OAAO;UAAED,IAAI;UAAEC;QAAM,CAAC;MAC1B,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACMT,aAAaA,CAACsB,MAAM,EAAE;IAAA,IAAAuD,MAAA;IAAA,OAAAzE,iBAAA;MACxB,IAAI;QACA,MAAMI,IAAI,SAAStB,QAAQ,CAAC2F,MAAI,CAACnF,KAAK,EAAE,QAAQ,EAAE,GAAGmF,MAAI,CAACrF,GAAG,gBAAgB8B,MAAM,CAACqD,MAAM,YAAYrD,MAAM,CAACiD,EAAE,EAAE,EAAE;UAC/G9E,OAAO,EAAEoF,MAAI,CAACpF;QAClB,CAAC,CAAC;QACF,OAAO;UAAEe,IAAI;UAAEC,KAAK,EAAE;QAAK,CAAC;MAChC,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIpB,WAAW,CAACoB,KAAK,CAAC,EAAE;UACpB,OAAO;YAAED,IAAI,EAAE,IAAI;YAAEC;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}