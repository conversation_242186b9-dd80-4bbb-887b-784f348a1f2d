import { Injectable } from '@angular/core';
import { createClient, SupabaseClient, AuthError, PostgrestError, AuthResponse, User as SupabaseUser, navigatorLock } from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { User } from '../models/user.model';

export type UserRole = 'rider' | 'driver' | 'admin';

interface AuthResult {
  data: AuthResponse['data'] | null;
  error: AuthError | PostgrestError | null;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private _supabase: SupabaseClient;
  private userSubject = new BehaviorSubject<User | null>(null);
  user$ = this.userSubject.asObservable();
private readonly MAX_RETRIES = 1; // Increase from 3
private readonly RETRY_DELAY = 10000; // Increase from 1000

  // Expose the Supabase client as a getter to be used by other services
  get supabase(): SupabaseClient {
    return this._supabase;
  }

  constructor() {
    // Create a custom lock function with a longer timeout
    // const customLock = (name: string, acquireTimeout: number, fn: () => Promise<any>) => {
    //   // Increase the timeout to 5000ms (5 seconds) to give more time for lock acquisition
    //   const adjustedTimeout = Math.max(acquireTimeout, 10000);
    //   console.log(`Attempting to acquire lock "${name}" with timeout ${adjustedTimeout}ms`);
    //   return navigatorLock(name, adjustedTimeout, fn);
    // };

    this._supabase = createClient(environment.supabaseUrl, environment.supabaseKey,
       {
      auth: {
        storageKey: 'holy-rides-auth',
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        debug: false, // Set to false to reduce console noise
     //   lock: customLock
      }
    });

    // Initialize auth with a longer delay to avoid lock conflicts
      this.initializeAuth();
    setTimeout(() => {
    
    }, 2500);
  }

  private async initializeAuth() {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Get initial session
      const { data: { session } } =await this.supabase.auth.getSession()

      if (session?.user) {
        try {
          // Convert Supabase user to our app user model
          const appUser = await this.convertToAppUser(session.user);
          // Set the user in the subject
          this.userSubject.next(appUser);

          // Then check the role in the background
          const role = await this.getUserRole();
          if (!role) {
            console.warn('User has no role, setting auth state to null');
            this.userSubject.next(null);
          }
        } catch (error) {
          console.error('Error during user conversion in init:', error);
          this.userSubject.next(null);
        }
      } else {
        this.userSubject.next(null);
      }

      // Listen for auth changes
      this.supabase.auth.onAuthStateChange(async (_event, session) => {
        try {
          if (session?.user) {
            try {
              // Convert Supabase user to our app user model
              const appUser = await this.convertToAppUser(session.user);
              // Set the user in the subject
              this.userSubject.next(appUser);

              // Then check the role in the background
              const role = await this.getUserRole();
              if (!role) {
                console.warn('User has no role on auth change, setting auth state to null');
                this.userSubject.next(null);
              }
            } catch (conversionError) {
              console.error('Error during user conversion in auth change:', conversionError);
              this.userSubject.next(null);
            }
          } else {
            this.userSubject.next(null);
          }
        } catch (error) {
          console.error('Auth state change error:', error);
          this.userSubject.next(null);
        }
      });
    } catch (error) {
      console.error('Auth initialization error:', error);
      this.userSubject.next(null);
    }
  }

  private async getSessionWithRetry() {
    let attempts = 0;
    const maxRetries = this.MAX_RETRIES;
    // Increase max backoff to 10 seconds
    const backoffDelay = (attempt: number) => Math.min(this.RETRY_DELAY * Math.pow(2, attempt), 20000);

    while (attempts < maxRetries) {
      try {
        // Add a small random delay before each attempt to reduce contention
        const jitter = Math.random() * 500;
        await new Promise(resolve => setTimeout(resolve, jitter));

        return await this.supabase.auth.getSession();
      } catch (error) {
        attempts++;
        console.warn(`Auth session attempt ${attempts} failed:`, error);

        if (attempts < maxRetries) {
          const delay = backoffDelay(attempts);
          console.log(`Retrying in ${delay}ms... (attempt ${attempts}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        // If it's not a lock error and we've exhausted retries, throw
        if (!this.isLockError(error)) {
          throw error;
        }
      }
    }

    // If we've exhausted retries and it was likely due to lock errors,
    // try one more time with a clean session
    console.log('Final attempt with clean session...');
    try {
      // Clear local session first
      await this.supabase.auth.signOut({ scope: 'local' });
      // Wait a bit longer before the final attempt
      await new Promise(resolve => setTimeout(resolve, 2000));
      return await this.supabase.auth.getSession();
    } catch (finalError) {
      console.error('Final session attempt failed:', finalError);
      throw new Error('Failed to acquire auth token after multiple attempts');
    }
  }

  private isLockError(error: unknown): boolean {
    if (!(error instanceof Error)) {
      return false;
    }

    // Check for specific lock error types
    if (error.name === 'NavigatorLockAcquireTimeoutError' ||
        error.name === 'LockAcquireTimeoutError') {
      return true;
    }

    // Check for lock-related error messages (case insensitive)
    const errorMsg = error.message?.toLowerCase() || '';
    return errorMsg.includes('lock') ||
           errorMsg.includes('timeout') ||
           errorMsg.includes('navigator') ||
           errorMsg.includes('acquire') ||
           errorMsg.includes('concurrent');
  }

  /**
   * Converts a Supabase User to our application's User model
   * This ensures type compatibility throughout the application
   */
  private async convertToAppUser(supabaseUser: SupabaseUser): Promise<User | null> {
    try {
      // Get profile data from the profiles table
      const { data: profile, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', supabaseUser.id)
        .single();

      if (error || !profile) {
        console.error('Error fetching profile for user conversion:', error);
        return null;
      }

      // Return a properly formatted User object
      return {
        id: supabaseUser.id,
        email: supabaseUser.email || '', // Ensure email is never undefined
        full_name: profile.full_name,
        phone: profile.phone,
        avatar_url: profile.avatar_url,
        role: profile.role,
        created_at: profile.created_at,
        updated_at: profile.updated_at,
        is_approved: profile.is_approved,
//         is_active: profile.is_active
      };
    } catch (error) {
      console.error('Error converting Supabase user to app user:', error);
      return null;
    }
  }

  async login(email: string, password: string): Promise<AuthResult> {
    try {
      const result = await this.supabase.auth.signInWithPassword({ email, password });
      // Remove or comment out this line during testing if it's causing issues
      // let r = await this.supabase.functions.invoke('hello', { body: { name: 'Functions' } });

      return { data: result.data, error: result.error };
    } catch (error) {
      if (this.isLockError(error)) {
        console.log('Lock error during login, retrying...');
        return this.retryOperation(() =>
          this.supabase.auth.signInWithPassword({ email, password })
        );
      }
      throw error;
    }
  }

  async register(email: string, password: string, role: UserRole, phone?: string, full_name?: string): Promise<AuthResult> {
    try {
      const { data: authData, error: authError } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            role,
            phone,
            full_name
          }
        }
      });

      if (authError) {
        return { data: null, error: authError };
      }

      // Set initial status based on role
      // Admins start as approved, others need approval
      const isApproved = role === 'admin' || role === 'rider';

      const { error: profileError } = await this.supabase
        .from('profiles')
        .insert([
          {
            id: authData.user?.id,
            email: email,
            phone: phone,
            full_name: full_name,
            role: role,
            created_at: new Date().toISOString(),
            //is_active: isActive,
            is_approved: isApproved
          }
        ]);

      return { data: authData, error: profileError };
    } catch (error) {
      if (this.isLockError(error)) {
        return this.retryOperation(() => this.register(email, password, role, phone, full_name));
      }
      throw error;
    }
  }
private async retryOperation<T>(operation: () => Promise<T>): Promise<T> {
  console.log("Starting retry operation");
  let attempts = 0;
  const maxRetries = this.MAX_RETRIES;
  const maxBackoff = 15000; // 15 seconds max backoff

  while (attempts < maxRetries) {
    try {
      // Add a small random delay before each attempt to reduce contention
      if (attempts > 0) {
        const jitter = Math.random() * 500;
        await new Promise(resolve => setTimeout(resolve, jitter));
      }

      return await operation();
    } catch (error) {
      attempts++;

      // Always log the error
      console.warn(`Operation attempt ${attempts} failed:`, error);

      // If it's a lock error or we have more retries, try again
      if ((this.isLockError(error) || attempts < maxRetries)) {
        // Use exponential backoff with a maximum limit
        const delay = Math.min(this.RETRY_DELAY * Math.pow(2, attempts - 1), maxBackoff);
        console.log(`Retry attempt ${attempts}/${maxRetries}, waiting ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // If it's not a lock error and we've exhausted retries, throw
      throw error;
    }
  }

  throw new Error(`Operation failed after ${maxRetries} retry attempts`);
}

  async logout(): Promise<{ error: AuthError | null }> {
    console.log("LOGOUT")
    try {
      console.log("LOGOUT 2")
      return await this.supabase.auth.signOut();
      console.log("LOGOUT 3")
    } catch (error) {
      if (this.isLockError(error)) {
        console.log('Lock error during logout, retrying...', error);
        return this.retryOperation(() => this.supabase.auth.signOut());
      }
      throw error;
    }
  }

  async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    return this.retryOperation(() =>
      this.supabase.auth.resetPasswordForEmail(email)
    );
  }

  async updatePassword(newPassword: string): Promise<{ data: { user: User | null }; error: AuthError | null }> {
    try {
      const result = await this.retryOperation(() =>
        this.supabase.auth.updateUser({ password: newPassword })
      );

      if (result.error || !result.data.user) {
        return { data: { user: null }, error: result.error };
      }

      // Convert Supabase user to our app user model
      const appUser = await this.convertToAppUser(result.data.user);
      return { data: { user: appUser }, error: null };
    } catch (error) {
      console.error('Error updating password:', error);
      return { data: { user: null }, error: error as AuthError };
    }
  }

  // Removed deprecated _getAuthUser method - use getCurrentUser() instead

  async getUserRole(): Promise<UserRole | null> {
    try {
      const { data: { session } } = await this.supabase.auth.getSession();
      if (!session?.user) return null;

      const { data: profile, error } = await this.supabase
        .from('profiles')
        .select('role')
        .eq('id', session.user.id)
        .single();

      if (error || !profile) {
        console.error('Error fetching user role:', error);
        return null;
      }

      return profile.role as UserRole;
    } catch (error) {
      console.error('Error in getUserRole:', error);
      return null;
    }
  }

  getDashboardRouteForRole(role: UserRole): string {
    return `/dashboard/${role}`;
  }

  async updateProfile(data: Partial<{
    full_name: string;
    phone: string;
    avatar_url: string;
  }>): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        throw new Error('No user logged in');
      }

      const { error } = await this.supabase
        .from('profiles')
        .update(data)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error updating profile:', error);
      return false;
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const { data, error } = await this.supabase.auth.getUser();
      if (error || !data.user) {
        return null;
      }

      // Use our helper method to convert Supabase user to our app user model
      return await this.convertToAppUser(data.user);
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  }
}
