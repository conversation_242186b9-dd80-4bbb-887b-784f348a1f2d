import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';

import { RideService } from '../../../../core/services/ride.service';
import { UserService } from '../../../../core/services/user.service';
import { Ride } from '../../../../core/models/ride.model';
import { User } from '../../../../core/models/user.model';
import { DriverPayout } from '../../../../core/models/payout.model';
import { PaymentService } from '../../../../core/services/payment.service';

@Component({
  selector: 'app-driver-payment',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTableModule,
    MatChipsModule,
    MatIconModule,
    MatTooltipModule,
    MatCheckboxModule,
    MatRadioModule
  ],
  template: `
    <div class="container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Driver Payment</mat-card-title>
          <mat-card-subtitle>Send test payments to drivers for completed rides</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading data...</p>
          </div>

          <div *ngIf="!loading">
            <form [formGroup]="paymentForm" (ngSubmit)="processPayment()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Select Completed Ride</mat-label>
                <mat-select formControlName="rideId">
                  <mat-option *ngFor="let ride of completedRides" [value]="ride.id">
                    {{ ride.pickup_location }} to {{ ride.dropoff_location }} -
                    Driver: {{ getDriverName(ride.driver_id) }} -
                    {{ ride.created_at | date:'short' }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="paymentForm.get('rideId')?.hasError('required')">
                  Please select a ride
                </mat-error>
              </mat-form-field>

              <div *ngIf="selectedRide" class="ride-details">
                <h3>Ride Details</h3>
                <div class="details-grid">
                  <div class="detail-item">
                    <span class="label">Pickup:</span>
                    <span class="value">{{ selectedRide.pickup_location }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Dropoff:</span>
                    <span class="value">{{ selectedRide.dropoff_location }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Driver:</span>
                    <span class="value">{{ getDriverName(selectedRide.driver_id) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Rider:</span>
                    <span class="value">{{ getRiderName(selectedRide.rider_id) }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Fare:</span>
                    <span class="value">{{ selectedRide.fare ? '$' + selectedRide.fare : 'N/A' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Payment Status:</span>
                    <span class="value">
                      <span class="status-chip" [ngClass]="'status-' + (selectedRide.payment_status || 'pending')">
                        {{ selectedRide.payment_status || 'pending' }}
                      </span>
                    </span>
                  </div>
                </div>

                <div class="payout-section">
                  <h3>Driver Payout</h3>
                  <div class="payout-controls">
                    <mat-checkbox formControlName="useCustomPayout">Set custom payout</mat-checkbox>

                    <div *ngIf="paymentForm.get('useCustomPayout')?.value" class="payout-options">
                      <mat-radio-group formControlName="payoutType" class="payout-type-group">
                        <mat-radio-button value="amount">Fixed Amount</mat-radio-button>
                        <mat-radio-button value="percentage">Percentage</mat-radio-button>
                      </mat-radio-group>

                      <div class="payout-inputs">
                        <!-- Amount input -->
                        <mat-form-field appearance="outline" class="amount-field"
                          *ngIf="paymentForm.get('payoutType')?.value === 'amount'">
                          <mat-label>Payout Amount</mat-label>
                          <span matPrefix>$&nbsp;</span>
                          <input matInput type="number" formControlName="payoutAmount" min="0.01" step="0.01">
                          <mat-error *ngIf="paymentForm.get('payoutAmount')?.hasError('required')">
                            Amount is required
                          </mat-error>
                          <mat-error *ngIf="paymentForm.get('payoutAmount')?.hasError('min')">
                            Amount must be greater than $0
                          </mat-error>
                        </mat-form-field>

                        <!-- Percentage input -->
                        <div *ngIf="paymentForm.get('payoutType')?.value === 'percentage'" class="percentage-container">
                          <mat-form-field appearance="outline" class="percentage-field">
                            <mat-label>Driver Percentage</mat-label>
                            <input matInput type="number" formControlName="payoutPercentage" min="1" max="100" step="1">
                            <span matSuffix>%</span>
                            <mat-error *ngIf="paymentForm.get('payoutPercentage')?.hasError('required')">
                              Percentage is required
                            </mat-error>
                            <mat-error *ngIf="paymentForm.get('payoutPercentage')?.hasError('min')">
                              Percentage must be at least 1%
                            </mat-error>
                            <mat-error *ngIf="paymentForm.get('payoutPercentage')?.hasError('max')">
                              Percentage cannot exceed 100%
                            </mat-error>
                          </mat-form-field>

                          <div class="calculated-amount">
                            <span class="label">Calculated Amount:</span>
                            <span class="value"> {{ this.calculatedPayoutAmount | currency:'USD':true:'1.2-2' }}  </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="button-container">
                <button mat-raised-button color="primary" type="submit"
                  [disabled]="paymentForm.invalid || processing || !canProcessPayment()">
                  <span *ngIf="!processing">Process Payment</span>
                  <mat-spinner *ngIf="processing" diameter="24"></mat-spinner>
                </button>
              </div>
            </form>

            <div *ngIf="payouts.length > 0" class="payouts-section">
              <h3>Recent Driver Payouts</h3>
              <table mat-table [dataSource]="payouts" class="payouts-table">
                <ng-container matColumnDef="date">
                  <th mat-header-cell *matHeaderCellDef>Date</th>
                  <td mat-cell *matCellDef="let payout">{{ payout.created_at | date:'medium' }}</td>
                </ng-container>

                <ng-container matColumnDef="driver">
                  <th mat-header-cell *matHeaderCellDef>Driver</th>
                  <td mat-cell *matCellDef="let payout">{{ getDriverName(payout.driver_id) }}</td>
                </ng-container>

                <ng-container matColumnDef="amount">
                  <th mat-header-cell *matHeaderCellDef>Amount</th>
                  <td mat-cell *matCellDef="let payout">{{ '$' + payout.amount.toFixed(2) }}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let payout">
                    <span class="status-chip" [ngClass]="'status-' + payout.status">
                      {{ payout.status }}
                    </span>
                  </td>
                </ng-container>

                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let payout">
                    <button mat-icon-button color="primary"
                      *ngIf="payout.status === 'pending'"
                      (click)="updatePayoutStatus(payout.driver_id, payout.ride_id, 'paid')"
                      matTooltip="Mark as Paid">
                      <mat-icon>payments</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
              </table>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .container {
      max-width: 800px;
      margin: 20px auto;
    }
    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
    }
    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
    .ride-details {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 15px;
      margin: 15px 0;
    }
    .details-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 10px;
    }
    .detail-item {
      margin-bottom: 8px;
    }
    .label {
      font-weight: 500;
      margin-right: 8px;
      color: rgba(0, 0, 0, 0.6);
    }
    .value {
      color: rgba(0, 0, 0, 0.87);
    }
    .payouts-section {
      margin-top: 30px;
    }
    .payouts-table {
      width: 100%;
    }
    .status-chip {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 16px;
      font-size: 0.85em;
      text-transform: capitalize;
    }
    .status-pending {
      background-color: #ffeb3b;
      color: #000;
    }
    .status-paid {
      background-color: #4caf50;
      color: white;
    }
    .status-failed {
      background-color: #f44336;
      color: white;
    }
    .payout-section {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
    }
    .payout-controls {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-top: 10px;
    }
    .amount-field, .percentage-field {
      width: 150px;
    }
    .payout-options {
      margin-top: 15px;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    .payout-type-group {
      display: flex;
      gap: 20px;
    }
    .payout-inputs {
      margin-top: 10px;
    }
    .percentage-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .calculated-amount {
      background-color: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;
      display: inline-flex;
      align-items: center;
    }
  `]
})
export class DriverPaymentComponent implements OnInit {
  paymentForm: FormGroup;
  loading = false;
  processing = false;
  completedRides: Ride[] = [];
  selectedRide: Ride | null = null;
  users: User[] = [];
  payouts: DriverPayout[] = [];
  displayedColumns: string[] = ['date', 'driver', 'amount', 'status', 'actions'];
  calculatedPayoutAmount = 0;

  constructor(
    private formBuilder: FormBuilder,
    private rideService: RideService,
    private paymentService: PaymentService,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {
    this.paymentForm = this.formBuilder.group({
      rideId: ['', Validators.required],
      useCustomPayout: [false],
      payoutType: ['amount'],
      payoutAmount: ['', [Validators.required, Validators.min(0.01)]],
      payoutPercentage: [80, [Validators.required, Validators.min(1), Validators.max(100)]]
    });

    // Listen for ride selection changes
    this.paymentForm.get('rideId')?.valueChanges.subscribe(rideId => {
      if (rideId) {
        this.selectedRide = this.completedRides.find(ride => ride.id === rideId) || null;

        // Set default payout amount based on ride fare
        if (this.selectedRide && this.selectedRide.fare) {
          this.paymentForm.get('payoutAmount')?.setValue(this.selectedRide.fare);
          this.updateCalculatedAmount();
        }
      } else {
        this.selectedRide = null;
        this.paymentForm.get('payoutAmount')?.setValue('');
      }
    });

    // Toggle custom payout fields based on checkbox
    this.paymentForm.get('useCustomPayout')?.valueChanges.subscribe(useCustom => {
      if (useCustom) {
        this.paymentForm.get('payoutType')?.enable();
        this.updatePayoutFieldsBasedOnType();
      } else {
        this.paymentForm.get('payoutType')?.disable();
        this.paymentForm.get('payoutAmount')?.disable();
        this.paymentForm.get('payoutPercentage')?.disable();

        // Reset to default values
        if (this.selectedRide && this.selectedRide.fare) {
          this.paymentForm.get('payoutAmount')?.setValue(this.selectedRide.fare);
        }
      }
    });

    // Toggle fields based on payout type
    this.paymentForm.get('payoutType')?.valueChanges.subscribe(() => {
      this.updatePayoutFieldsBasedOnType();
    });

    // Update calculated amount when percentage changes
    this.paymentForm.get('payoutPercentage')?.valueChanges.subscribe(() => {
      this.updateCalculatedAmount();
    });

    // Initialize form state
    this.paymentForm.get('payoutType')?.disable();
    this.paymentForm.get('payoutAmount')?.disable();
    this.paymentForm.get('payoutPercentage')?.disable();
  }

  // Helper method to update fields based on payout type
  private updatePayoutFieldsBasedOnType(): void {
    const payoutType = this.paymentForm.get('payoutType')?.value;

    if (payoutType === 'amount') {
      this.paymentForm.get('payoutAmount')?.enable();
      this.paymentForm.get('payoutPercentage')?.disable();
    } else {
      this.paymentForm.get('payoutAmount')?.disable();
      this.paymentForm.get('payoutPercentage')?.enable();
      this.updateCalculatedAmount();
    }
  }

  // Helper method to calculate amount based on percentage
  private updateCalculatedAmount(): void {
    if (this.selectedRide?.fare && this.paymentForm.get('payoutType')?.value === 'percentage') {
      const percentage = this.paymentForm.get('payoutPercentage')?.value || 0;
      const calculatedAmount = (percentage / 100) * this.selectedRide.fare;
      this.calculatedPayoutAmount = +calculatedAmount.toFixed(2);
    }
  }

  ngOnInit(): void {
    this.loadData();
  }

  async loadData(): Promise<void> {
    this.loading = true;
    try {
      // Load users
      this.users = await this.userService.getAllUsers();

      // Load completed rides
      const allRides = await this.rideService.getAllRides();
      this.completedRides = allRides.filter(ride =>
        ride.status === 'completed' &&
        ride.driver_id &&
        (!ride.payment_status || ride.payment_status === 'pending')
      );

      // Load all payouts
      const drivers = this.users.filter(user => user.role === 'driver');
      let allPayouts: DriverPayout[] = [];

      for (const driver of drivers) {
        const driverPayouts = await this.paymentService.getDriverPayouts(driver.id);
        allPayouts = [...allPayouts, ...driverPayouts];
      }

      // Sort payouts by creation date (newest first)
      this.payouts = allPayouts.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    } catch (error) {
      console.error('Error loading data:', error);
      this.snackBar.open('Error loading data', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  getDriverName(driverId?: string): string {
    if (!driverId) return 'N/A';
    const driver = this.users.find(user => user.id === driverId);
    return driver?.full_name || 'Unknown Driver';
  }

  getRiderName(riderId: string): string {
    const rider = this.users.find(user => user.id === riderId);
    return rider?.full_name || 'Unknown Rider';
  }

  canProcessPayment(): boolean {
    if (!this.selectedRide) return false;

    return (
      this.selectedRide.status === 'completed' &&
      (!this.selectedRide.payment_status || this.selectedRide.payment_status === 'pending')
    );
  }

  async processPayment(): Promise<void> {
    if (this.paymentForm.invalid || !this.selectedRide || !this.canProcessPayment()) {
      return;
    }

    this.processing = true;
    const rideId = this.paymentForm.get('rideId')?.value;

    try {
      // Ensure the ride has a fare
      if (!this.selectedRide.fare) {
        // If no fare is set, estimate one
        const {fare} = await this.paymentService.estimateFare(
          this.selectedRide.pickup_location,
          this.selectedRide.dropoff_location
        );

        // Update the ride with the estimated fare
        await this.rideService.updateRide(rideId, { fare });
        this.selectedRide.fare = fare;
      }

      // Create a payment intent using the ride fare (what the rider pays)
      const riderAmount = this.selectedRide.fare;
      await this.paymentService.createPaymentIntent(
        rideId,
        riderAmount
      );

      // Process the payment - this will create a driver payout record with the default amount
      const success = await this.paymentService.processPayment(
        rideId,
        'pm_test_payment_method'
      );

      if (success) {
        // If admin set a custom payout, update the driver payout record
        if (this.paymentForm.get('useCustomPayout')?.value && this.selectedRide.driver_id) {
          // Determine the payout amount and type
          let finalPayoutAmount: number;
          const payoutType = this.paymentForm.get('payoutType')?.value as 'amount' | 'percentage';

          if (payoutType === 'amount') {
            // Use the fixed amount
            finalPayoutAmount = this.paymentForm.get('payoutAmount')?.value;
          } else {
            // Use the calculated percentage amount
            const percentage = this.paymentForm.get('payoutPercentage')?.value || 0;
            finalPayoutAmount = +(percentage / 100 * this.selectedRide.fare).toFixed(2);
          }

          // Only update if the amount is different from the default
          if (finalPayoutAmount !== this.selectedRide.fare) {
            // Get the most recent payout for this ride
            const driverPayouts = await this.paymentService.getDriverPayouts(this.selectedRide.driver_id);
            const ridePayout = driverPayouts.find(p => p.ride_id === rideId);

            if (ridePayout) {
              if (payoutType === 'percentage') {
                const percentage = this.paymentForm.get('payoutPercentage')?.value;
                // Update the payout amount with percentage info
                await this.paymentService.updateDriverPayoutAmount(
                  this.selectedRide.driver_id,
                  rideId,
                  finalPayoutAmount,
                  'percentage',
                  percentage
                );
              } else {
                // Update with fixed amount
                await this.paymentService.updateDriverPayoutAmount(
                  this.selectedRide.driver_id,
                  rideId,
                  finalPayoutAmount,
                  'amount'
                );
              }
            }
          }
        }

        this.snackBar.open('Payment processed successfully!', 'Close', { duration: 3000 });

        // Refresh data
        await this.loadData();

        // Reset form
        this.paymentForm.reset();
        this.selectedRide = null;
      } else {
        this.snackBar.open('Payment failed. Please try again.', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      this.snackBar.open('An error occurred while processing payment', 'Close', { duration: 3000 });
    } finally {
      this.processing = false;
    }
  }

  async updatePayoutStatus(driverId: string, rideId: string, status: 'paid' | 'failed'): Promise<void> {
    try {
      // This is a custom method we need to add to the PaymentService
      await this.paymentService.updateDriverPayoutStatus(driverId, rideId, status);
      this.snackBar.open(`Payout marked as ${status}`, 'Close', { duration: 3000 });

      // Refresh data
      await this.loadData();
    } catch (error) {
      console.error('Error updating payout status:', error);
      this.snackBar.open('Error updating payout status', 'Close', { duration: 3000 });
    }
  }
}
