import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, fromEvent, Observable, merge, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class OnlineStatusService implements OnDestroy {
  private online$ = new BehaviorSubject<boolean>(navigator.onLine);

  private subscription: Subscription | null = null;

  constructor() {
    try {
      // Listen for online/offline events
      this.subscription = merge(
        fromEvent(window, 'online').pipe(map(() => true)),
        fromEvent(window, 'offline').pipe(map(() => false))
      ).subscribe(online => this.online$.next(online));
    } catch (error) {
      console.error('Error setting up online status listeners:', error);
    }
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  /**
   * Get the current online status
   */
  public isOnline(): boolean {
    return this.online$.getValue();
  }

  /**
   * Get an observable of the online status
   */
  public getOnlineStatus(): Observable<boolean> {
    return this.online$.asObservable();
  }
}
