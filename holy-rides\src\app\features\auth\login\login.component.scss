.login-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;


  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;

    .logo {
      width: 100px;
   
      margin-bottom: 10px;
    }

    .app-name {
      font-size: 24px;
      font-weight: 500;
      color: #3f51b5;
      margin: 0;
    }
  }

  mat-card {
    width: 100%;
    max-width: 400px;
    padding: 20px;
  }

  mat-form-field {
    width: 100%;
    margin-bottom: 16px;
  }

  .button-container {
    margin-top: 24px;
    display: flex;
    justify-content: center;

    button {
      width: 100%;
      padding: 8px;
    }
  }

  .links {
    margin-top: 16px;
    text-align: center;

    a {
      color: #3f51b5;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .error-message {
    color: #f44336;
    text-align: center;
    margin: 8px 0;
  }
}