import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MessageService } from '../../../core/services/message.service';
import { AuthService } from '../../../core/services/auth.service';
import { RideService } from '../../../core/services/ride.service';

@Component({
  selector: 'app-ride-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  template: `
    <mat-card class="chat-card">
      <mat-card-header>
        <mat-card-title>Chat</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="messages-container">
          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
            <p>Loading messages...</p>
          </div>
          
          <div *ngIf="!loading && messages.length === 0" class="no-messages">
            <p>No messages yet. Start the conversation!</p>
          </div>
          
          <div *ngIf="!loading && messages.length > 0" class="messages-list">
            <div *ngFor="let message of messages" 
                 class="message-bubble" 
                 [ngClass]="{'sent': message.sender_id === currentUserId, 'received': message.sender_id !== currentUserId}">
              <div class="message-content">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.created_at) }}</div>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <form (ngSubmit)="sendMessage()" class="message-form">
          <mat-form-field appearance="outline" class="message-input">
            <input matInput [(ngModel)]="newMessage" name="newMessage" placeholder="Type a message..." autocomplete="off">
          </mat-form-field>
          <button mat-raised-button color="primary" type="submit" [disabled]="!newMessage || sending">
            <mat-icon>send</mat-icon>
          </button>
        </form>
      </mat-card-actions>
    </mat-card>
  `,
  styles: [`
    .chat-card {
      margin: 16px;
      max-width: 800px;
    }
    
    .messages-container {
      height: 300px;
      overflow-y: auto;
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    
    .no-messages {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: rgba(0, 0, 0, 0.5);
    }
    
    .messages-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .message-bubble {
      max-width: 80%;
      padding: 8px 12px;
      border-radius: 16px;
      position: relative;
    }
    
    .sent {
      align-self: flex-end;
      background-color: #2196f3;
      color: white;
      border-bottom-right-radius: 4px;
    }
    
    .received {
      align-self: flex-start;
      background-color: white;
      border-bottom-left-radius: 4px;
    }
    
    .message-content {
      word-break: break-word;
    }
    
    .message-time {
      font-size: 0.7em;
      opacity: 0.7;
      text-align: right;
      margin-top: 4px;
    }
    
    .message-form {
      display: flex;
      gap: 8px;
      width: 100%;
      padding: 0 16px 16px;
    }
    
    .message-input {
      flex: 1;
    }
  `]
})
export class RideChatComponent implements OnInit {
  @Input() threadId!: string;
  @Input() rideId!: string;
  
  messages: any[] = [];
  newMessage = '';
  loading = false;
  sending = false;
  currentUserId = '';
  receiverId = '';
  
  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private rideService: RideService
  ) {}
  
  async ngOnInit() {
    this.loading = true;
    
    try {
      // Get current user
      const user = await this.authService.getCurrentUser();
      if (user) {
        this.currentUserId = user.id;
      }
      
      // If we have a rideId but no threadId, get or create the thread
      if (this.rideId && !this.threadId) {
        const thread = await this.messageService.getOrCreateThreadForRide(this.rideId);
        this.threadId = thread.id;
      }
      
      // Get the ride to determine the receiver
      if (this.rideId) {
        const ride = await this.rideService.getRide(this.rideId);
        if (ride) {
          this.receiverId = ride.rider_id === this.currentUserId ? ride.driver_id! : ride.rider_id;
        }
      }
      
      // Load messages
      if (this.threadId) {
        this.messages = await this.messageService.getThreadMessages(this.threadId);
        
        // Mark messages as read
        await this.messageService.markMessagesAsRead(this.threadId);
      }
    } catch (error) {
      console.error('Error initializing chat:', error);
    } finally {
      this.loading = false;
    }
  }
  
  async sendMessage() {
    if (!this.newMessage || !this.threadId || !this.receiverId) return;
    
    this.sending = true;
    
    try {
      await this.messageService.sendMessage(this.threadId, this.receiverId, this.newMessage);
      this.newMessage = '';
      
      // Refresh messages
      this.messages = await this.messageService.getThreadMessages(this.threadId);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      this.sending = false;
    }
  }
  
  formatTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
}
