import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { RideService } from '../../../../core/services/ride.service';
import { AuthService } from '../../../../core/services/auth.service';
import { PaymentService } from '../../../../core/services/payment.service';
import { MapDisplayComponent } from '../../../../shared/components/map-display/map-display.component';
import { Coordinates, LocationService } from '../../../../core/services/location.service';
import { NgxMatTimepickerModule } from 'ngx-mat-timepicker';

@Component({
  selector: 'app-ride-request',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MapDisplayComponent,
    NgxMatTimepickerModule
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Request a Ride</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="rideForm" (ngSubmit)="onSubmit()">
          <div class="location-fields">
            <mat-form-field appearance="outline">
              <mat-label>Pickup Location</mat-label>
              <input matInput formControlName="pickup_location" placeholder="Enter pickup location">
              <button mat-icon-button matSuffix type="button" (click)="useCurrentLocation()" title="Use current location">
                <mat-icon>my_location</mat-icon>
              </button>
              <mat-error *ngIf="rideForm.get('pickup_location')?.errors?.['required']">
                Pickup location is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Dropoff Location</mat-label>
              <input matInput formControlName="dropoff_location" placeholder="Enter dropoff location">
              <mat-error *ngIf="rideForm.get('dropoff_location')?.errors?.['required']">
                Dropoff location is required
              </mat-error>
            </mat-form-field>
          </div>

          <div *ngIf="showMap && rideForm.get('pickup_location')?.value && rideForm.get('dropoff_location')?.value">
            <app-map-display
              [origin]="rideForm.get('pickup_location')?.value"
              [destination]="rideForm.get('dropoff_location')?.value">
            </app-map-display>

            <div *ngIf="estimatedFare" class="fare-estimate">
              <p>Estimated fare: <strong>{{estimatedFare ? '$' + estimatedFare.toFixed(2) : ''}}</strong></p>
              <p *ngIf="estimatedDistance">Distance: {{estimatedDistance}} miles</p>
              <p *ngIf="estimatedDuration">Duration: {{estimatedDuration}} minutes</p>
            </div>
          </div>
<!--
          <mat-form-field appearance="outline">
            <mat-label>Number of Passengers</mat-label>
            <input matInput type="number" formControlName="passengers" min="1" max="4">
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['required']">
              Number of passengers is required
            </mat-error>
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['min']">
              Must have at least 1 passenger
            </mat-error>
            <mat-error *ngIf="rideForm.get('passengers')?.errors?.['max']">
              Maximum 4 passengers allowed
            </mat-error>
          </mat-form-field> -->

          <mat-form-field appearance="outline">
            <mat-label>Pickup Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="pickup_date">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="rideForm.get('pickup_date')?.errors?.['required']">
              Pickup date is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Pick a time</mat-label>
            <input matInput [ngxMatTimepicker]="timepicker" formControlName="pickup_time">
            <ngx-mat-timepicker-toggle matSuffix [for]="timepicker">
              <mat-icon ngxMatTimepickerToggleIcon>keyboard_arrow_down</mat-icon>
            </ngx-mat-timepicker-toggle>
            <ngx-mat-timepicker #timepicker></ngx-mat-timepicker>
            <mat-error *ngIf="rideForm.get('pickup_time')?.errors?.['required']">
              Pickup time is required
            </mat-error>
          </mat-form-field>

          <!-- <mat-form-field appearance="outline">
            <mat-label>Special Notes</mat-label>
            <textarea matInput formControlName="notes" placeholder="Any special requirements?"></textarea>
          </mat-form-field> -->

          <div class="button-container">
            <button mat-raised-button color="primary" type="submit" [disabled]="rideForm.invalid || loading">
              {{ loading ? 'Requesting...' : 'Request Ride' }}
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    :host {
      display: block;
      margin: 20px;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 600px;
      margin: 0 auto;
    }

    .location-fields {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .button-container {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }

    textarea {
      min-height: 100px;
    }

    .fare-estimate {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      margin-top: 16px;
      margin-bottom: 16px;
    }

    .fare-estimate p {
      margin: 8px 0;
    }
  `]
})
export class RideRequestComponent implements OnInit {
  rideForm: FormGroup;
  loading = false;
  showMap = false;
  estimatedFare: number | null = null;
  estimatedDistance: number | null = null;
  estimatedDuration: number | null = null;

  private locationCoordinates: {
    pickup?: Coordinates;
    dropoff?: Coordinates;
  } = {};

  constructor(
    private formBuilder: FormBuilder,
    private rideService: RideService,
    private authService: AuthService,
    private locationService: LocationService,
    private paymentService: PaymentService,
    private snackBar: MatSnackBar
  ) {
    this.rideForm = this.formBuilder.group({
      pickup_location: ['', Validators.required],
      dropoff_location: ['', Validators.required],
      //passengers: [1, [Validators.required, Validators.min(1), Validators.max(4)]],
      pickup_date: [new Date(), Validators.required],
      pickup_time: ['12:00 PM', Validators.required],
     // notes: ['']
    });
  }

  ngOnInit(): void {
    // Listen for changes to pickup and dropoff locations
    this.rideForm.get('pickup_location')?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });

    this.rideForm.get('dropoff_location')?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });
  }

  async useCurrentLocation(): Promise<void> {
    try {
      const coords = await this.locationService.getCurrentLocation();

      // For demo purposes, we'll just set a placeholder address
      // In a real app, you would use reverse geocoding to get the address
      this.rideForm.patchValue({
        pickup_location: `Current Location (${coords.latitude.toFixed(6)}, ${coords.longitude.toFixed(6)})`
      });

      this.locationCoordinates.pickup = coords;
      this.snackBar.open('Current location detected', 'Close', { duration: 2000 });
    } catch (error: any) {
      this.snackBar.open(error.message || 'Failed to get current location', 'Close', { duration: 3000 });
    }
  }

  async updateRouteEstimates(): Promise<void> {
    const pickup = this.rideForm.get('pickup_location')?.value;
    const dropoff = this.rideForm.get('dropoff_location')?.value;

    if (pickup && dropoff) {
      this.showMap = true;

      try {
        // In a real app, you would use the LocationService to calculate the route
        // For demo purposes, we'll use the PaymentService's mock implementation
       const {fare, routeInfo} = await this.paymentService.estimateFare(pickup, dropoff);
          this.estimatedFare = fare;
          this.estimatedDistance = routeInfo.distance;
          this.estimatedDuration = routeInfo.duration;
        // Mock distance and duration values
       // this.estimatedDistance = Math.floor(Math.random() * 18) + 2; // 2-20 miles
        //this.estimatedDuration = Math.floor(Math.random() * 55) + 5; // 5-60 minutes
      } catch (error) {
        console.error('Error calculating route:', error);
      }
    } else {
      this.showMap = false;
      this.estimatedFare = null;
      this.estimatedDistance = null;
      this.estimatedDuration = null;
    }
  }

  async onSubmit() {
    if (this.rideForm.invalid) return;

    this.loading = true;

    try {
      const user = await this.authService.getCurrentUser();
      if (!user) throw new Error('User not found');

      // Get coordinates for pickup and dropoff locations if not already set
      if (!this.locationCoordinates.pickup) {
        this.locationCoordinates.pickup = await this.locationService.geocodeAddress(this.rideForm.value.pickup_location);
      }

      if (!this.locationCoordinates.dropoff) {
        this.locationCoordinates.dropoff = await this.locationService.geocodeAddress(this.rideForm.value.dropoff_location);
      }

      // Calculate route information
      const routeInfo = await this.locationService.calculateRoute(
        this.locationCoordinates.pickup,
        this.locationCoordinates.dropoff
      );
      console.log(routeInfo)

      // Combine date and time
      const pickupDate = this.rideForm.value.pickup_date;
      const pickupTime = this.rideForm.value.pickup_time;

      // Create a combined date-time object
      const combinedDateTime = new Date(pickupDate);

      // Parse the time string (assuming format like "12:00 PM")
      const timeParts = pickupTime.match(/(\d+):(\d+)\s?(AM|PM)?/i);
      if (timeParts) {
        let hours = parseInt(timeParts[1], 10);
        const minutes = parseInt(timeParts[2], 10);
        const period = timeParts[3] ? timeParts[3].toUpperCase() : null;

        // Convert to 24-hour format if needed
        if (period === 'PM' && hours < 12) {
          hours += 12;
        } else if (period === 'AM' && hours === 12) {
          hours = 0;
        }

        combinedDateTime.setHours(hours, minutes, 0, 0);
      }

      const ride = {
        ...this.rideForm.value,
        rider_id: user.id,
        status: 'requested',
        pickup_time: combinedDateTime.toISOString(),
        // Add location coordinates
        pickup_latitude: this.locationCoordinates.pickup?.latitude,
        pickup_longitude: this.locationCoordinates.pickup?.longitude,
        dropoff_latitude: this.locationCoordinates.dropoff?.latitude,
        dropoff_longitude: this.locationCoordinates.dropoff?.longitude,
        // Add route information
        distance_miles: routeInfo.distance,
        duration_minutes: routeInfo.duration,
        // Add fare
        fare: this.estimatedFare || await this.paymentService.estimateFare(
          this.rideForm.value.pickup_location,
          this.rideForm.value.dropoff_location
        )
      };

      await this.rideService.createRide(ride);

      this.snackBar.open('Ride requested successfully!', 'Close', { duration: 3000 });
      this.rideForm.reset({
        passengers: 1,
        pickup_date: new Date(),
        pickup_time: '12:00 PM'
      });

      // Reset state
      this.showMap = false;
      this.estimatedFare = null;
      this.estimatedDistance = null;
      this.estimatedDuration = null;
      this.locationCoordinates = {};
    } catch (error: any) {
      this.snackBar.open(error.message || 'Failed to request ride', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }
}