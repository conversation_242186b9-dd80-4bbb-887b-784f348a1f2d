{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  _platform = inject(Platform);\n  _nonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  _matchMedia;\n  constructor() {\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n  static ɵfac = function MediaMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MediaMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaMatcher,\n    factory: MediaMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n  _mediaMatcher = inject(MediaMatcher);\n  _zone = inject(NgZone);\n  /**  A map of all media queries currently being listened for. */\n  _queries = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  _destroySubject = new Subject();\n  constructor() {}\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n  static ɵfac = function BreakpointObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BreakpointObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreakpointObserver,\n    factory: BreakpointObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\nexport { BreakpointObserver as B, MediaMatcher as M };", "map": {"version": 3, "names": ["i0", "inject", "CSP_NONCE", "Injectable", "NgZone", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "P", "Platform", "c", "coerce<PERSON><PERSON><PERSON>", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "_platform", "_nonce", "optional", "_matchMedia", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "ɵfac", "MediaMatcher_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "nonce", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "_mediaMatcher", "_zone", "_queries", "Map", "_destroySubject", "ngOnDestroy", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "get", "queryObservable", "observer", "handler", "run", "output", "set", "BreakpointObserver_Factory", "split", "reduce", "a1", "a2", "trim", "B", "M"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/breakpoints-observer-CljOfYGy.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    _platform = inject(Platform);\n    _nonce = inject(CSP_NONCE, { optional: true });\n    /** The internal matchMedia method to return back a MediaQueryList like object. */\n    _matchMedia;\n    constructor() {\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query, this._nonce);\n        }\n        return this._matchMedia(query);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            if (nonce) {\n                mediaQueryStyleNode.setAttribute('nonce', nonce);\n            }\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n    _mediaMatcher = inject(MediaMatcher);\n    _zone = inject(NgZone);\n    /**  A map of all media queries currently being listened for. */\n    _queries = new Map();\n    /** A subject for all other observables to takeUntil based on. */\n    _destroySubject = new Subject();\n    constructor() { }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\nexport { BreakpointObserver as B, MediaMatcher as M };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACrE,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,WAAW,QAAQ,sBAAsB;;AAEvD;AACA,MAAMC,kCAAkC,GAAG,IAAIC,GAAG,CAAC,CAAC;AACpD;AACA,IAAIC,mBAAmB;AACvB;AACA,MAAMC,YAAY,CAAC;EACfC,SAAS,GAAGtB,MAAM,CAACe,QAAQ,CAAC;EAC5BQ,MAAM,GAAGvB,MAAM,CAACC,SAAS,EAAE;IAAEuB,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC9C;EACAC,WAAW;EACXC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,WAAW,GACZ,IAAI,CAACH,SAAS,CAACK,SAAS,IAAIC,MAAM,CAACC,UAAU;IACvC;IACE;IACAD,MAAM,CAACC,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC,GAChCG,cAAc;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,UAAUA,CAACG,KAAK,EAAE;IACd,IAAI,IAAI,CAACV,SAAS,CAACW,MAAM,IAAI,IAAI,CAACX,SAAS,CAACY,KAAK,EAAE;MAC/CC,oBAAoB,CAACH,KAAK,EAAE,IAAI,CAACT,MAAM,CAAC;IAC5C;IACA,OAAO,IAAI,CAACE,WAAW,CAACO,KAAK,CAAC;EAClC;EACA,OAAOI,IAAI,YAAAC,qBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFjB,YAAY;EAAA;EAC/G,OAAOkB,KAAK,kBAD6ExC,EAAE,CAAAyC,kBAAA;IAAAC,KAAA,EACYpB,YAAY;IAAAqB,OAAA,EAAZrB,YAAY,CAAAe,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC3I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6F7C,EAAE,CAAA8C,iBAAA,CAGJxB,YAAY,EAAc,CAAC;IAC1GyB,IAAI,EAAE5C,UAAU;IAChB6C,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASR,oBAAoBA,CAACH,KAAK,EAAEgB,KAAK,EAAE;EACxC,IAAI9B,kCAAkC,CAAC+B,GAAG,CAACjB,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACZ,mBAAmB,EAAE;MACtBA,mBAAmB,GAAG8B,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACrD,IAAIH,KAAK,EAAE;QACP5B,mBAAmB,CAACgC,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAAC;MACpD;MACA5B,mBAAmB,CAACgC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAClC,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAACmC,KAAK,EAAE;MAC3BnC,mBAAmB,CAACmC,KAAK,CAACC,UAAU,CAAC,UAAUxB,KAAK,YAAY,EAAE,CAAC,CAAC;MACpEd,kCAAkC,CAACuC,GAAG,CAACzB,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAO0B,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAAS3B,cAAcA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACH6B,OAAO,EAAE7B,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxC8B,KAAK,EAAE9B,KAAK;IACZ+B,WAAW,EAAEA,CAAA,KAAM,CAAE,CAAC;IACtBC,cAAc,EAAEA,CAAA,KAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AACA,MAAMC,kBAAkB,CAAC;EACrBC,aAAa,GAAGlE,MAAM,CAACqB,YAAY,CAAC;EACpC8C,KAAK,GAAGnE,MAAM,CAACG,MAAM,CAAC;EACtB;EACAiE,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EACpB;EACAC,eAAe,GAAG,IAAIlE,OAAO,CAAC,CAAC;EAC/BsB,WAAWA,CAAA,EAAG,CAAE;EAChB;EACA6C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,eAAe,CAACE,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACF,eAAe,CAACG,QAAQ,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAACC,KAAK,EAAE;IACb,MAAMC,OAAO,GAAGC,YAAY,CAAC5D,WAAW,CAAC0D,KAAK,CAAC,CAAC;IAChD,OAAOC,OAAO,CAACE,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACpB,OAAO,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqB,OAAOA,CAACP,KAAK,EAAE;IACX,MAAMC,OAAO,GAAGC,YAAY,CAAC5D,WAAW,CAAC0D,KAAK,CAAC,CAAC;IAChD,MAAMQ,WAAW,GAAGP,OAAO,CAACjE,GAAG,CAACqB,KAAK,IAAI,IAAI,CAACgD,cAAc,CAAChD,KAAK,CAAC,CAACoD,UAAU,CAAC;IAC/E,IAAIC,eAAe,GAAGhF,aAAa,CAAC8E,WAAW,CAAC;IAChD;IACAE,eAAe,GAAG/E,MAAM,CAAC+E,eAAe,CAACC,IAAI,CAAC9E,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE6E,eAAe,CAACC,IAAI,CAAC7E,IAAI,CAAC,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvG,OAAO2E,eAAe,CAACC,IAAI,CAAC3E,GAAG,CAAC4E,gBAAgB,IAAI;MAChD,MAAMC,QAAQ,GAAG;QACb3B,OAAO,EAAE,KAAK;QACd4B,WAAW,EAAE,CAAC;MAClB,CAAC;MACDF,gBAAgB,CAACG,OAAO,CAAC,CAAC;QAAE7B,OAAO;QAAE7B;MAAM,CAAC,KAAK;QAC7CwD,QAAQ,CAAC3B,OAAO,GAAG2B,QAAQ,CAAC3B,OAAO,IAAIA,OAAO;QAC9C2B,QAAQ,CAACC,WAAW,CAACzD,KAAK,CAAC,GAAG6B,OAAO;MACzC,CAAC,CAAC;MACF,OAAO2B,QAAQ;IACnB,CAAC,CAAC,CAAC;EACP;EACA;EACAR,cAAcA,CAAChD,KAAK,EAAE;IAClB;IACA,IAAI,IAAI,CAACoC,QAAQ,CAACnB,GAAG,CAACjB,KAAK,CAAC,EAAE;MAC1B,OAAO,IAAI,CAACoC,QAAQ,CAACuB,GAAG,CAAC3D,KAAK,CAAC;IACnC;IACA,MAAMiD,GAAG,GAAG,IAAI,CAACf,aAAa,CAACrC,UAAU,CAACG,KAAK,CAAC;IAChD;IACA,MAAM4D,eAAe,GAAG,IAAIrF,UAAU,CAAEsF,QAAQ,IAAK;MACjD;MACA;MACA;MACA;MACA;MACA,MAAMC,OAAO,GAAIpC,CAAC,IAAK,IAAI,CAACS,KAAK,CAAC4B,GAAG,CAAC,MAAMF,QAAQ,CAACrB,IAAI,CAACd,CAAC,CAAC,CAAC;MAC7DuB,GAAG,CAAClB,WAAW,CAAC+B,OAAO,CAAC;MACxB,OAAO,MAAM;QACTb,GAAG,CAACjB,cAAc,CAAC8B,OAAO,CAAC;MAC/B,CAAC;IACL,CAAC,CAAC,CAACR,IAAI,CAAC1E,SAAS,CAACqE,GAAG,CAAC,EAAEtE,GAAG,CAAC,CAAC;MAAEkD;IAAQ,CAAC,MAAM;MAAE7B,KAAK;MAAE6B;IAAQ,CAAC,CAAC,CAAC,EAAEhD,SAAS,CAAC,IAAI,CAACyD,eAAe,CAAC,CAAC;IACpG;IACA,MAAM0B,MAAM,GAAG;MAAEZ,UAAU,EAAEQ,eAAe;MAAEX;IAAI,CAAC;IACnD,IAAI,CAACb,QAAQ,CAAC6B,GAAG,CAACjE,KAAK,EAAEgE,MAAM,CAAC;IAChC,OAAOA,MAAM;EACjB;EACA,OAAO5D,IAAI,YAAA8D,2BAAA5D,iBAAA;IAAA,YAAAA,iBAAA,IAAwF2B,kBAAkB;EAAA;EACrH,OAAO1B,KAAK,kBA3H6ExC,EAAE,CAAAyC,kBAAA;IAAAC,KAAA,EA2HYwB,kBAAkB;IAAAvB,OAAA,EAAlBuB,kBAAkB,CAAA7B,IAAA;IAAAO,UAAA,EAAc;EAAM;AACjJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7H6F7C,EAAE,CAAA8C,iBAAA,CA6HJoB,kBAAkB,EAAc,CAAC;IAChHnB,IAAI,EAAE5C,UAAU;IAChB6C,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAASkC,YAAYA,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACTjE,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACmE,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAAC/F,MAAM,CAACgG,EAAE,CAAC,CAAC,CACjC3F,GAAG,CAACqB,KAAK,IAAIA,KAAK,CAACuE,IAAI,CAAC,CAAC,CAAC;AACnC;AAEA,SAAStC,kBAAkB,IAAIuC,CAAC,EAAEnF,YAAY,IAAIoF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}