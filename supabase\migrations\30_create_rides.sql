-- Create rides table
CREATE TABLE IF NOT EXISTS rides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  rider_id UUID REFERENCES profiles(id) NOT NULL,
  driver_id UUID REFERENCES profiles(id),
  pickup_location TEXT NOT NULL,
  dropoff_location TEXT NOT NULL,
  pickup_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('requested', 'assigned', 'in-progress', 'completed', 'canceled')),
  fare DECIMAL(10, 2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Set up Row Level Security (RLS)
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Riders can view their own rides."
  ON rides FOR SELECT
  USING (auth.uid() = rider_id);

CREATE POLICY "Drivers can view their assigned rides."
  ON rides FOR SELECT
  USING (auth.uid() = driver_id);

CREATE POLICY "Admins can view all rides."
  ON rides FOR SELECT
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Riders can create ride requests."
  ON rides FOR INSERT
  WITH CHECK (
    auth.uid() = rider_id AND
    status = 'requested'
  );

CREATE POLICY "Admins can update any ride."
  ON rides FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Drivers can update their assigned rides."
  ON rides FOR UPDATE
  USING (
    auth.uid() = driver_id AND
    status IN ('assigned', 'in-progress')
  )
  WITH CHECK (
    auth.uid() = driver_id AND
    status IN ('in-progress', 'completed')
  );

-- Create an updated_at trigger
CREATE OR REPLACE FUNCTION update_rides_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER rides_updated_at
  BEFORE UPDATE ON rides
  FOR EACH ROW
  EXECUTE PROCEDURE update_rides_updated_at();
