import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { DriverProfileComponent } from './driver-profile/driver-profile.component';
import { DriverEarningsComponent } from './driver-earnings/driver-earnings.component';
import { RideAssignmentsComponent } from './ride-assignments/ride-assignments.component';


@Component({
  selector: 'app-driver',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    DriverProfileComponent,
    RideAssignmentsComponent,
    DriverEarningsComponent
  ],
  templateUrl: './driver.component.html',
  styleUrl: './driver.component.scss'
})
export class DriverComponent {}
