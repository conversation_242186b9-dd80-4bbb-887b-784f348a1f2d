import{a as k,b as ce}from"./chunk-ODN5LVDJ.js";function It(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ct=It(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function T(e){return typeof e=="function"}var _o=It(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Bt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var K=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){t=i instanceof _o?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Gu(i)}catch(s){t=t??[],s instanceof _o?t=[...t,...s.errors]:t.push(s)}}if(t)throw new _o(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Gu(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Bt(n,t)}remove(t){let{_finalizers:n}=this;n&&Bt(n,t),t instanceof e&&t._removeParent(this)}};K.EMPTY=(()=>{let e=new K;return e.closed=!0,e})();var Ks=K.EMPTY;function Eo(e){return e instanceof K||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function Gu(e){T(e)?e():e.unsubscribe()}var Le={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Dn={setTimeout(e,t,...n){let{delegate:r}=Dn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Dn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function wo(e){Dn.setTimeout(()=>{let{onUnhandledError:t}=Le;if(t)t(e);else throw e})}function Vt(){}var Wu=Qs("C",void 0,void 0);function qu(e){return Qs("E",void 0,e)}function Zu(e){return Qs("N",e,void 0)}function Qs(e,t,n){return{kind:e,value:t,error:n}}var Ht=null;function _n(e){if(Le.useDeprecatedSynchronousErrorHandling){let t=!Ht;if(t&&(Ht={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Ht;if(Ht=null,n)throw r}}else e()}function Yu(e){Le.useDeprecatedSynchronousErrorHandling&&Ht&&(Ht.errorThrown=!0,Ht.error=e)}var Ut=class extends K{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Eo(t)&&t.add(this)):this.destination=fb}static create(t,n,r){return new je(t,n,r)}next(t){this.isStopped?Js(Zu(t),this):this._next(t)}error(t){this.isStopped?Js(qu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Js(Wu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ub=Function.prototype.bind;function Xs(e,t){return ub.call(e,t)}var ea=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Io(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Io(r)}else Io(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Io(n)}}},je=class extends Ut{constructor(t,n,r){super();let o;if(T(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Le.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Xs(t.next,i),error:t.error&&Xs(t.error,i),complete:t.complete&&Xs(t.complete,i)}):o=t}this.destination=new ea(o)}};function Io(e){Le.useDeprecatedSynchronousErrorHandling?Yu(e):wo(e)}function db(e){throw e}function Js(e,t){let{onStoppedNotification:n}=Le;n&&Dn.setTimeout(()=>n(e,t))}var fb={closed:!0,next:Vt,error:db,complete:Vt};function pb(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new je({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new ct)}});e.subscribe(i)})}function ta(e){return T(e?.lift)}function I(e){return t=>{if(ta(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function E(e,t,n,r,o){return new na(e,t,n,r,o)}var na=class extends Ut{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Te(e,t){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>e.call(t,i,o++)&&r.next(i)))})}var En=typeof Symbol=="function"&&Symbol.observable||"@@observable";function he(e){return e}function mb(...e){return ra(e)}function ra(e){return e.length===0?he:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var N=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=gb(n)?n:new je(n,r,o);return _n(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ku(r),new r((o,i)=>{let s=new je({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[En](){return this}pipe(...n){return ra(n)(this)}toPromise(n){return n=Ku(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ku(e){var t;return(t=e??Le.Promise)!==null&&t!==void 0?t:Promise}function hb(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function gb(e){return e&&e instanceof Ut||hb(e)&&Eo(e)}function oa(){return I((e,t)=>{let n=null;e._refCount++;let r=E(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var ia=class extends N{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,ta(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new K;let n=this.getSubject();t.add(this.source.subscribe(E(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=K.EMPTY)}return t}refCount(){return oa()(this)}};var Qu=It(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var B=(()=>{class e extends N{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Co(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Qu}next(n){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){_n(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ks:(this.currentObservers=null,i.push(n),new K(()=>{this.currentObservers=null,Bt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new N;return n.source=this,n}}return e.create=(t,n)=>new Co(t,n),e})(),Co=class extends B{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Ks}};var $t=class extends B{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var ur={now(){return(ur.delegate||Date).now()},delegate:void 0};var dr=class extends B{constructor(t=1/0,n=1/0,r=ur){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Mo=class extends K{constructor(t,n){super()}schedule(t,n=0){return this}};var fr={setInterval(e,t,...n){let{delegate:r}=fr;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=fr;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var To=class extends Mo{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return fr.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&fr.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Bt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var wn=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};wn.now=ur.now;var xo=class extends wn{constructor(t,n=wn.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var lt=new xo(To),Xu=lt;var zt=new N(e=>e.complete());function So(e){return e&&T(e.schedule)}function sa(e){return e[e.length-1]}function No(e){return T(sa(e))?e.pop():void 0}function qe(e){return So(sa(e))?e.pop():void 0}function Ju(e,t){return typeof sa(e)=="number"?e.pop():t}function td(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function ed(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Gt(e){return this instanceof Gt?(this.v=e,this):new Gt(e)}function nd(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(v,w){i.push([f,g,v,w])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{l(r[f](h))}catch(g){p(i[0][3],g)}}function l(f){f.value instanceof Gt?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function rd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ed=="function"?ed(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Ao=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ro(e){return T(e?.then)}function Oo(e){return T(e[En])}function ko(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function Fo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function bb(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Po=bb();function Lo(e){return T(e?.[Po])}function jo(e){return nd(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Gt(n.read());if(o)return yield Gt(void 0);yield yield Gt(r)}}finally{n.releaseLock()}})}function Bo(e){return T(e?.getReader)}function P(e){if(e instanceof N)return e;if(e!=null){if(Oo(e))return yb(e);if(Ao(e))return vb(e);if(Ro(e))return Db(e);if(ko(e))return od(e);if(Lo(e))return _b(e);if(Bo(e))return Eb(e)}throw Fo(e)}function yb(e){return new N(t=>{let n=e[En]();if(T(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function vb(e){return new N(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Db(e){return new N(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,wo)})}function _b(e){return new N(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function od(e){return new N(t=>{wb(e,t).catch(n=>t.error(n))})}function Eb(e){return od(jo(e))}function wb(e,t){var n,r,o,i;return td(this,void 0,void 0,function*(){try{for(n=rd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function le(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Vo(e,t=0){return I((n,r)=>{n.subscribe(E(r,o=>le(r,e,()=>r.next(o),t),()=>le(r,e,()=>r.complete(),t),o=>le(r,e,()=>r.error(o),t)))})}function Ho(e,t=0){return I((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function id(e,t){return P(e).pipe(Ho(t),Vo(t))}function sd(e,t){return P(e).pipe(Ho(t),Vo(t))}function ad(e,t){return new N(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function cd(e,t){return new N(n=>{let r;return le(n,t,()=>{r=e[Po](),le(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function Uo(e,t){if(!e)throw new Error("Iterable cannot be null");return new N(n=>{le(n,t,()=>{let r=e[Symbol.asyncIterator]();le(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function ld(e,t){return Uo(jo(e),t)}function ud(e,t){if(e!=null){if(Oo(e))return id(e,t);if(Ao(e))return ad(e,t);if(Ro(e))return sd(e,t);if(ko(e))return Uo(e,t);if(Lo(e))return cd(e,t);if(Bo(e))return ld(e,t)}throw Fo(e)}function Ae(e,t){return t?ud(e,t):P(e)}function In(...e){let t=qe(e);return Ae(e,t)}function Ib(e,t){let n=T(e)?e:()=>e,r=o=>o.error(n());return new N(t?o=>t.schedule(r,0,o):r)}function Cb(e){return!!e&&(e instanceof N||T(e.lift)&&T(e.subscribe))}function $o(e){return e instanceof Date&&!isNaN(e)}var Mb=It(e=>function(n=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n});function Tb(e,t){let{first:n,each:r,with:o=xb,scheduler:i=t??lt,meta:s=null}=$o(e)?{first:e}:typeof e=="number"?{each:e}:e;if(n==null&&r==null)throw new TypeError("No timeout provided.");return I((a,c)=>{let l,u,d=null,p=0,f=h=>{u=le(c,i,()=>{try{l.unsubscribe(),P(o({meta:s,lastValue:d,seen:p})).subscribe(c)}catch(g){c.error(g)}},h)};l=a.subscribe(E(c,h=>{u?.unsubscribe(),p++,c.next(d=h),r>0&&f(r)},void 0,void 0,()=>{u?.closed||u?.unsubscribe(),d=null})),!p&&f(n!=null?typeof n=="number"?n:+n-i.now():r)})}function xb(e){throw new Mb(e)}function X(e,t){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Sb}=Array;function Nb(e,t){return Sb(t)?e(...t):e(t)}function zo(e){return X(t=>Nb(e,t))}var{isArray:Ab}=Array,{getPrototypeOf:Rb,prototype:Ob,keys:kb}=Object;function Go(e){if(e.length===1){let t=e[0];if(Ab(t))return{args:t,keys:null};if(Fb(t)){let n=kb(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Fb(e){return e&&typeof e=="object"&&Rb(e)===Ob}function Wo(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function aa(...e){let t=qe(e),n=No(e),{args:r,keys:o}=Go(e);if(r.length===0)return Ae([],t);let i=new N(Pb(r,t,o?s=>Wo(o,s):he));return n?i.pipe(zo(n)):i}function Pb(e,t,n=he){return r=>{dd(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)dd(t,()=>{let l=Ae(e[c],t),u=!1;l.subscribe(E(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function dd(e,t,n){e?le(n,e,t):t()}function fd(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let v=!1;P(n(g,u++)).subscribe(E(t,w=>{o?.(w),i?f(w):t.next(w)},()=>{v=!0},void 0,()=>{if(v)try{for(l--;c.length&&l<r;){let w=c.shift();s?le(t,s,()=>h(w)):h(w)}p()}catch(w){t.error(w)}}))};return e.subscribe(E(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Wt(e,t,n=1/0){return T(t)?Wt((r,o)=>X((i,s)=>t(r,i,o,s))(P(e(r,o))),n):(typeof t=="number"&&(n=t),I((r,o)=>fd(r,o,e,n)))}function pr(e=1/0){return Wt(he,e)}function pd(){return pr(1)}function Cn(...e){return pd()(Ae(e,qe(e)))}function Lb(e){return new N(t=>{P(e()).subscribe(t)})}function jb(...e){let t=No(e),{args:n,keys:r}=Go(e),o=new N(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;P(n[u]).subscribe(E(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?Wo(r,a):a),i.complete())}))}});return t?o.pipe(zo(t)):o}function mr(e=0,t,n=Xu){let r=-1;return t!=null&&(So(t)?n=t:r=t),new N(o=>{let i=$o(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Bb(e=0,t=lt){return e<0&&(e=0),mr(e,e,t)}function Vb(...e){let t=qe(e),n=Ju(e,1/0),r=e;return r.length?r.length===1?P(r[0]):pr(n)(Ae(r,t)):zt}var Hb=new N(Vt);function md(e){return I((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(E(n,l=>{r=!0,o=l,i||P(e(l)).subscribe(i=E(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Ub(e,t=lt){return md(()=>mr(e,t))}function hd(e){return I((t,n)=>{let r=null,o=!1,i;r=t.subscribe(E(n,void 0,void 0,s=>{i=P(e(s,hd(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function gd(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(E(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ca(e,t){return T(t)?Wt(e,t,1):Wt(e,1)}function qt(e,t=lt){return I((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(E(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function hr(e){return I((t,n)=>{let r=!1;t.subscribe(E(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function gr(e){return e<=0?()=>zt:I((t,n)=>{let r=0;t.subscribe(E(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function la(e,t=he){return e=e??$b,I((n,r)=>{let o,i=!0;n.subscribe(E(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function $b(e,t){return e===t}function qo(e=zb){return I((t,n)=>{let r=!1;t.subscribe(E(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function zb(){return new ct}function ua(e){return I((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Gb(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Te((o,i)=>e(o,i,r)):he,gr(1),n?hr(t):qo(()=>new ct))}function da(e){return e<=0?()=>zt:I((t,n)=>{let r=[];t.subscribe(E(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Wb(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Te((o,i)=>e(o,i,r)):he,da(1),n?hr(t):qo(()=>new ct))}function qb(){return I((e,t)=>{let n,r=!1;e.subscribe(E(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Zb(e,t){return I(gd(e,t,arguments.length>=2,!0))}function pa(e={}){let{connector:t=()=>new B,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,c,l=0,u=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=c=void 0,u=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return I((g,v)=>{l++,!d&&!u&&p();let w=c=c??t();v.add(()=>{l--,l===0&&!d&&!u&&(a=fa(h,o))}),w.subscribe(v),!s&&l>0&&(s=new je({next:$=>w.next($),error:$=>{d=!0,p(),a=fa(f,n,$),w.error($)},complete:()=>{u=!0,p(),a=fa(f,r),w.complete()}}),P(g).subscribe(s))})(i)}}function fa(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new je({next:()=>{r.unsubscribe(),e()}});return P(t(...n)).subscribe(r)}function Yb(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,pa({connector:()=>new dr(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function br(e){return Te((t,n)=>e<=n)}function ma(...e){let t=qe(e);return I((n,r)=>{(t?Cn(e,n,t):Cn(e,n)).subscribe(r)})}function ha(e,t){return I((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(E(r,c=>{o?.unsubscribe();let l=0,u=i++;P(e(c,u)).subscribe(o=E(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Mn(e){return I((t,n)=>{P(e).subscribe(E(n,()=>n.complete(),Vt)),!n.closed&&t.subscribe(n)})}function Kb(e,t=!1){return I((n,r)=>{let o=0;n.subscribe(E(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function ga(e,t,n){let r=T(e)||t||n?{next:e,error:t,complete:n}:e;return r?I((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(E(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):he}function Da(e,t){return Object.is(e,t)}var ne=null,Zo=!1,_a=1,be=Symbol("SIGNAL");function A(e){let t=ne;return ne=e,t}function Ea(){return ne}var Tn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function vr(e){if(Zo)throw new Error("");if(ne===null)return;ne.consumerOnSignalRead(e);let t=ne.nextProducerIndex++;if(Jo(ne),t<ne.producerNode.length&&ne.producerNode[t]!==e&&yr(ne)){let n=ne.producerNode[t];Xo(n,ne.producerIndexOfThis[t])}ne.producerNode[t]!==e&&(ne.producerNode[t]=e,ne.producerIndexOfThis[t]=yr(ne)?yd(e,ne,t):0),ne.producerLastReadVersion[t]=e.version}function bd(){_a++}function wa(e){if(!(yr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===_a)){if(!e.producerMustRecompute(e)&&!Qo(e)){va(e);return}e.producerRecomputeValue(e),va(e)}}function Ia(e){if(e.liveConsumerNode===void 0)return;let t=Zo;Zo=!0;try{for(let n of e.liveConsumerNode)n.dirty||Qb(n)}finally{Zo=t}}function Ca(){return ne?.consumerAllowSignalWrites!==!1}function Qb(e){e.dirty=!0,Ia(e),e.consumerMarkedDirty?.(e)}function va(e){e.dirty=!1,e.lastCleanEpoch=_a}function Dr(e){return e&&(e.nextProducerIndex=0),A(e)}function Ko(e,t){if(A(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(yr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Xo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Qo(e){Jo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(wa(n),r!==n.version))return!0}return!1}function _r(e){if(Jo(e),yr(e))for(let t=0;t<e.producerNode.length;t++)Xo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function yd(e,t,n){if(vd(e),e.liveConsumerNode.length===0&&Dd(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=yd(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Xo(e,t){if(vd(e),e.liveConsumerNode.length===1&&Dd(e))for(let r=0;r<e.producerNode.length;r++)Xo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Jo(o),o.producerIndexOfThis[r]=t}}function yr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Jo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function vd(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Dd(e){return e.producerNode!==void 0}function ei(e,t){let n=Object.create(Xb);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(wa(n),vr(n),n.value===Yo)throw n.error;return n.value};return r[be]=n,r}var ba=Symbol("UNSET"),ya=Symbol("COMPUTING"),Yo=Symbol("ERRORED"),Xb=ce(k({},Tn),{value:ba,dirty:!0,error:null,equal:Da,kind:"computed",producerMustRecompute(e){return e.value===ba||e.value===ya},producerRecomputeValue(e){if(e.value===ya)throw new Error("Detected cycle in computations.");let t=e.value;e.value=ya;let n=Dr(e),r,o=!1;try{r=e.computation(),A(null),o=t!==ba&&t!==Yo&&r!==Yo&&e.equal(t,r)}catch(i){r=Yo,e.error=i}finally{Ko(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function Jb(){throw new Error}var _d=Jb;function Ed(e){_d(e)}function Ma(e){_d=e}var ey=null;function Ta(e,t){let n=Object.create(ti);n.value=e,t!==void 0&&(n.equal=t);let r=()=>(vr(n),n.value);return r[be]=n,r}function Er(e,t){Ca()||Ed(e),e.equal(e.value,t)||(e.value=t,ty(e))}function xa(e,t){Ca()||Ed(e),Er(e,t(e.value))}var ti=ce(k({},Tn),{equal:Da,value:void 0,kind:"signal"});function ty(e){e.version++,bd(),Ia(e),ey?.()}function Sa(e){let t=A(null);try{return e()}finally{A(t)}}var Na;function wr(){return Na}function ut(e){let t=Na;return Na=e,t}var ni=Symbol("NotFound");var yf="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",D=class extends Error{code;constructor(t,n){super(ol(t,n)),this.code=t}};function iy(e){return`NG0${Math.abs(e)}`}function ol(e,t){return`${iy(e)}${t?": "+t:""}`}var vf=Symbol("InputSignalNode#UNSET"),sy=ce(k({},ti),{transformFn:void 0,applyValueToInputSignal(e,t){Er(e,t)}});function Df(e,t){let n=Object.create(sy);n.value=e,n.transformFn=t?.transform;function r(){if(vr(n),n.value===vf){let o=null;throw new D(-950,o)}return n.value}return r[be]=n,r}function jr(e){return{toString:e}.toString()}var ri="__parameters__";function ay(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function _f(e,t,n){return jr(()=>{let r=ay(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(ri)?c[ri]:Object.defineProperty(c,ri,{value:[]})[ri];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ze=globalThis;function V(e){for(let t in e)if(e[t]===V)return t;throw Error("Could not find renamed property on target object.")}function cy(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function De(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(De).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Wa(e,t){return e?t?`${e} ${t}`:e:t||""}var ly=V({__forward_ref__:V});function Ef(e){return e.__forward_ref__=Ef,e.toString=function(){return De(this())},e}function se(e){return wf(e)?e():e}function wf(e){return typeof e=="function"&&e.hasOwnProperty(ly)&&e.__forward_ref__===Ef}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function oe(e){return{providers:e.providers||[],imports:e.imports||[]}}function Vi(e){return wd(e,If)||wd(e,Cf)}function SO(e){return Vi(e)!==null}function wd(e,t){return e.hasOwnProperty(t)?e[t]:null}function uy(e){let t=e&&(e[If]||e[Cf]);return t||null}function Id(e){return e&&(e.hasOwnProperty(Cd)||e.hasOwnProperty(dy))?e[Cd]:null}var If=V({\u0275prov:V}),Cd=V({\u0275inj:V}),Cf=V({ngInjectableDef:V}),dy=V({ngInjectorDef:V}),y=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Mf(e){return e&&!!e.\u0275providers}var fy=V({\u0275cmp:V}),py=V({\u0275dir:V}),my=V({\u0275pipe:V}),hy=V({\u0275mod:V}),mi=V({\u0275fac:V}),Tr=V({__NG_ELEMENT_ID__:V}),Md=V({__NG_ENV_ID__:V});function kn(e){return typeof e=="string"?e:e==null?"":String(e)}function gy(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():kn(e)}function Tf(e,t){throw new D(-200,e)}function il(e,t){throw new D(-201,!1)}var R=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(R||{}),qa;function xf(){return qa}function ye(e){let t=qa;return qa=e,t}function Sf(e,t,n){let r=Vi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&R.Optional)return null;if(t!==void 0)return t;il(e,"Injector")}var by={},Yt=by,Za="__NG_DI_FLAG__",hi=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=n;return this.injector.get(t,r.optional?ni:Yt,r)}},gi="ngTempTokenPath",yy="ngTokenPath",vy=/\n/gm,Dy="\u0275",Td="__source";function _y(e,t=R.Default){if(wr()===void 0)throw new D(-203,!1);if(wr()===null)return Sf(e,void 0,t);{let n=wr(),r;return n instanceof hi?r=n.injector:r=n,r.get(e,t&R.Optional?null:void 0,t)}}function C(e,t=R.Default){return(xf()||_y)(se(e),t)}function m(e,t=R.Default){return C(e,Hi(t))}function Hi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ya(e){let t=[];for(let n=0;n<e.length;n++){let r=se(e[n]);if(Array.isArray(r)){if(r.length===0)throw new D(900,!1);let o,i=R.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Ey(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(C(o,i))}else t.push(C(r))}return t}function Nf(e,t){return e[Za]=t,e.prototype[Za]=t,e}function Ey(e){return e[Za]}function wy(e,t,n,r){let o=e[gi];throw t[Td]&&o.unshift(t[Td]),e.message=Iy(`
`+e.message,o,n,r),e[yy]=o,e[gi]=null,e}function Iy(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Dy?e.slice(2):e;let o=De(t);if(Array.isArray(t))o=t.map(De).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):De(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(vy,`
  `)}`}var sl=Nf(_f("Optional"),8);var Af=Nf(_f("SkipSelf"),4);function Qt(e,t){let n=e.hasOwnProperty(mi);return n?e[mi]:null}function Cy(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function My(e){return e.flat(Number.POSITIVE_INFINITY)}function al(e,t){e.forEach(n=>Array.isArray(n)?al(n,t):t(n))}function Rf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function bi(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Ty(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function xy(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function Ui(e,t,n){let r=Br(e,t);return r>=0?e[r|1]=n:(r=~r,xy(e,r,t,n)),r}function Aa(e,t){let n=Br(e,t);if(n>=0)return e[n|1]}function Br(e,t){return Sy(e,t,1)}function Sy(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ke={},ge=[],xr=new y(""),Of=new y("",-1),kf=new y(""),yi=class{get(t,n=Yt){if(n===Yt){let r=new Error(`NullInjectorError: No provider for ${De(t)}!`);throw r.name="NullInjectorError",r}return n}};function Ff(e,t){let n=e[hy]||null;if(!n&&t===!0)throw new Error(`Type ${De(e)} does not have '\u0275mod' property.`);return n}function Tt(e){return e[fy]||null}function Pf(e){return e[py]||null}function Ny(e){return e[my]||null}function Vr(e){return{\u0275providers:e}}function Ay(...e){return{\u0275providers:Lf(!0,e),\u0275fromNgModule:!0}}function Lf(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return al(t,s=>{let a=s;Ka(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&jf(o,i),n}function jf(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];cl(o,i=>{t(i,r)})}}function Ka(e,t,n,r){if(e=se(e),!e)return!1;let o=null,i=Id(e),s=!i&&Tt(e);if(!i&&!s){let c=e.ngModule;if(i=Id(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Ka(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{al(i.imports,u=>{Ka(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&jf(l,t)}if(!a){let l=Qt(o)||(()=>new o);t({provide:o,useFactory:l,deps:ge},o),t({provide:kf,useValue:o,multi:!0},o),t({provide:xr,useValue:()=>C(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;cl(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function cl(e,t){for(let n of e)Mf(n)&&(n=n.\u0275providers),Array.isArray(n)?cl(n,t):t(n)}var Ry=V({provide:String,useValue:V});function Bf(e){return e!==null&&typeof e=="object"&&Ry in e}function Oy(e){return!!(e&&e.useExisting)}function ky(e){return!!(e&&e.useFactory)}function Fn(e){return typeof e=="function"}function Fy(e){return!!e.useClass}var $i=new y(""),ci={},xd={},Ra;function zi(){return Ra===void 0&&(Ra=new yi),Ra}var Oe=class{},Sr=class extends Oe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Xa(t,s=>this.processProvider(s)),this.records.set(Of,xn(void 0,this)),o.has("environment")&&this.records.set(Oe,xn(void 0,this));let i=this.records.get($i);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(kf,ge,R.Self))}retrieve(t,n){let r=n;return this.get(t,r.optional?ni:Yt,r)}destroy(){Cr(this),this._destroyed=!0;let t=A(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),A(t)}}onDestroy(t){return Cr(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Cr(this);let n=ut(this),r=ye(void 0),o;try{return t()}finally{ut(n),ye(r)}}get(t,n=Yt,r=R.Default){if(Cr(this),t.hasOwnProperty(Md))return t[Md](this);r=Hi(r);let o,i=ut(this),s=ye(void 0);try{if(!(r&R.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=Vy(t)&&Vi(t);l&&this.injectableDefInScope(l)?c=xn(Qa(t),ci):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&R.Self?zi():this.parent;return n=r&R.Optional&&n===Yt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[gi]=a[gi]||[]).unshift(De(t)),i)throw a;return wy(a,t,"R3InjectorError",this.source)}else throw a}finally{ye(s),ut(i)}}resolveInjectorInitializers(){let t=A(null),n=ut(this),r=ye(void 0),o;try{let i=this.get(xr,ge,R.Self);for(let s of i)s()}finally{ut(n),ye(r),A(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(De(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=se(t);let n=Fn(t)?t:se(t&&t.provide),r=Ly(t);if(!Fn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=xn(void 0,ci,!0),o.factory=()=>Ya(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=A(null);try{return n.value===xd?Tf(De(t)):n.value===ci&&(n.value=xd,n.value=n.factory()),typeof n.value=="object"&&n.value&&By(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{A(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=se(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Qa(e){let t=Vi(e),n=t!==null?t.factory:Qt(e);if(n!==null)return n;if(e instanceof y)throw new D(204,!1);if(e instanceof Function)return Py(e);throw new D(204,!1)}function Py(e){if(e.length>0)throw new D(204,!1);let n=uy(e);return n!==null?()=>n.factory(e):()=>new e}function Ly(e){if(Bf(e))return xn(void 0,e.useValue);{let t=Vf(e);return xn(t,ci)}}function Vf(e,t,n){let r;if(Fn(e)){let o=se(e);return Qt(o)||Qa(o)}else if(Bf(e))r=()=>se(e.useValue);else if(ky(e))r=()=>e.useFactory(...Ya(e.deps||[]));else if(Oy(e))r=()=>C(se(e.useExisting));else{let o=se(e&&(e.useClass||e.provide));if(jy(e))r=()=>new o(...Ya(e.deps));else return Qt(o)||Qa(o)}return r}function Cr(e){if(e.destroyed)throw new D(205,!1)}function xn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function jy(e){return!!e.deps}function By(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Vy(e){return typeof e=="function"||typeof e=="object"&&e instanceof y}function Xa(e,t){for(let n of e)Array.isArray(n)?Xa(n,t):n&&Mf(n)?Xa(n.\u0275providers,t):t(n)}function Gi(e,t){let n;e instanceof Sr?(Cr(e),n=e):n=new hi(e);let r,o=ut(n),i=ye(void 0);try{return t()}finally{ut(o),ye(i)}}function Hf(){return xf()!==void 0||wr()!=null}function ll(e){if(!Hf())throw new D(-203,!1)}function Hy(e){return typeof e=="function"}var mt=0,x=1,M=2,de=3,He=4,_e=5,Pn=6,vi=7,re=8,Xt=9,dt=10,W=11,Nr=12,Sd=13,zn=14,xe=15,Jt=16,Sn=17,ft=18,Wi=19,Uf=20,Ct=21,Oa=22,en=23,Re=24,Rn=25,Q=26,$f=1;var tn=7,Di=8,Ln=9,ue=10;function Mt(e){return Array.isArray(e)&&typeof e[$f]=="object"}function ht(e){return Array.isArray(e)&&e[$f]===!0}function ul(e){return(e.flags&4)!==0}function Gn(e){return e.componentOffset>-1}function qi(e){return(e.flags&1)===1}function Qe(e){return!!e.template}function _i(e){return(e[M]&512)!==0}function Wn(e){return(e[M]&256)===256}var Ja=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function zf(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Zi=(()=>{let e=()=>Gf;return e.ngInherit=!0,e})();function Gf(e){return e.type.prototype.ngOnChanges&&(e.setInput=$y),Uy}function Uy(){let e=qf(this),t=e?.current;if(t){let n=e.previous;if(n===Ke)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function $y(e,t,n,r,o){let i=this.declaredInputs[r],s=qf(e)||zy(e,{previous:Ke,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Ja(l&&l.currentValue,n,c===Ke),zf(e,t,o,n)}var Wf="__ngSimpleChanges__";function qf(e){return e[Wf]||null}function zy(e,t){return e[Wf]=t}var Nd=null;var L=function(e,t=null,n){Nd?.(e,t,n)},Zf="svg",Gy="math";function Xe(e){for(;Array.isArray(e);)e=e[mt];return e}function Yf(e,t){return Xe(t[e])}function ot(e,t){return Xe(t[e.index])}function dl(e,t){return e.data[t]}function Hr(e,t){return e[t]}function Je(e,t){let n=t[e];return Mt(n)?n:n[mt]}function Wy(e){return(e[M]&4)===4}function fl(e){return(e[M]&128)===128}function qy(e){return ht(e[de])}function xt(e,t){return t==null?null:e[t]}function Kf(e){e[Sn]=0}function Qf(e){e[M]&1024||(e[M]|=1024,fl(e)&&qn(e))}function Zy(e,t){for(;e>0;)t=t[zn],e--;return t}function Yi(e){return!!(e[M]&9216||e[Re]?.dirty)}function ec(e){e[dt].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),Yi(e)&&qn(e)}function qn(e){e[dt].changeDetectionScheduler?.notify(0);let t=nn(e);for(;t!==null&&!(t[M]&8192||(t[M]|=8192,!fl(t)));)t=nn(t)}function Xf(e,t){if(Wn(e))throw new D(911,!1);e[Ct]===null&&(e[Ct]=[]),e[Ct].push(t)}function Yy(e,t){if(e[Ct]===null)return;let n=e[Ct].indexOf(t);n!==-1&&e[Ct].splice(n,1)}function nn(e){let t=e[de];return ht(t)?t[de]:t}function pl(e){return e[vi]??=[]}function ml(e){return e.cleanup??=[]}function Ky(e,t,n,r){let o=pl(t);o.push(n),e.firstCreatePass&&ml(e).push(r,o.length-1)}var S={lFrame:op(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var tc=!1;function Qy(){return S.lFrame.elementDepthCount}function Xy(){S.lFrame.elementDepthCount++}function Jy(){S.lFrame.elementDepthCount--}function hl(){return S.bindingsEnabled}function Jf(){return S.skipHydrationRootTNode!==null}function ev(e){return S.skipHydrationRootTNode===e}function tv(){S.skipHydrationRootTNode=null}function _(){return S.lFrame.lView}function z(){return S.lFrame.tView}function NO(e){return S.lFrame.contextLView=e,e[re]}function AO(e){return S.lFrame.contextLView=null,e}function fe(){let e=ep();for(;e!==null&&e.type===64;)e=e.parent;return e}function ep(){return S.lFrame.currentTNode}function nv(){let e=S.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function At(e,t){let n=S.lFrame;n.currentTNode=e,n.isParent=t}function gl(){return S.lFrame.isParent}function bl(){S.lFrame.isParent=!1}function rv(){return S.lFrame.contextLView}function tp(){return tc}function Ei(e){let t=tc;return tc=e,t}function ln(){let e=S.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ov(){return S.lFrame.bindingIndex}function iv(e){return S.lFrame.bindingIndex=e}function un(){return S.lFrame.bindingIndex++}function yl(e){let t=S.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function sv(){return S.lFrame.inI18n}function av(e,t){let n=S.lFrame;n.bindingIndex=n.bindingRootIndex=e,nc(t)}function cv(){return S.lFrame.currentDirectiveIndex}function nc(e){S.lFrame.currentDirectiveIndex=e}function lv(e){let t=S.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function vl(){return S.lFrame.currentQueryIndex}function Ki(e){S.lFrame.currentQueryIndex=e}function uv(e){let t=e[x];return t.type===2?t.declTNode:t.type===1?e[_e]:null}function np(e,t,n){if(n&R.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&R.Host);)if(o=uv(i),o===null||(i=i[zn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=S.lFrame=rp();return r.currentTNode=t,r.lView=e,!0}function Dl(e){let t=rp(),n=e[x];S.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function rp(){let e=S.lFrame,t=e===null?null:e.child;return t===null?op(e):t}function op(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function ip(){let e=S.lFrame;return S.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var sp=ip;function _l(){let e=ip();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function dv(e){return(S.lFrame.contextLView=Zy(e,S.lFrame.contextLView))[re]}function gt(){return S.lFrame.selectedIndex}function rn(e){S.lFrame.selectedIndex=e}function Ur(){let e=S.lFrame;return dl(e.tView,e.selectedIndex)}function RO(){S.lFrame.currentNamespace=Zf}function OO(){fv()}function fv(){S.lFrame.currentNamespace=null}function pv(){return S.lFrame.currentNamespace}var ap=!0;function Qi(){return ap}function Xi(e){ap=e}function mv(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Gf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function El(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function li(e,t,n){cp(e,t,3,n)}function ui(e,t,n,r){(e[M]&3)===n&&cp(e,t,n,r)}function ka(e,t){let n=e[M];(n&3)===t&&(n&=16383,n+=1,e[M]=n)}function cp(e,t,n,r){let o=r!==void 0?e[Sn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Sn]+=65536),(a<i||i==-1)&&(hv(e,n,t,c),e[Sn]=(e[Sn]&**********)+c+2),c++}function Ad(e,t){L(4,e,t);let n=A(null);try{t.call(e)}finally{A(n),L(5,e,t)}}function hv(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[M]>>14<e[Sn]>>16&&(e[M]&3)===t&&(e[M]+=16384,Ad(a,i)):Ad(a,i)}var On=-1,on=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function gv(e){return(e.flags&8)!==0}function bv(e){return(e.flags&16)!==0}function yv(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];vv(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function lp(e){return e===3||e===4||e===6}function vv(e){return e.charCodeAt(0)===64}function jn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Rd(e,n,o,null,t[++r]):Rd(e,n,o,null,null))}}return e}function Rd(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function up(e){return e!==On}function wi(e){return e&32767}function Dv(e){return e>>16}function Ii(e,t){let n=Dv(e),r=t;for(;n>0;)r=r[zn],n--;return r}var rc=!0;function Ci(e){let t=rc;return rc=e,t}var _v=256,dp=_v-1,fp=5,Ev=0,Ye={};function wv(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Tr)&&(r=n[Tr]),r==null&&(r=n[Tr]=Ev++);let o=r&dp,i=1<<o;t.data[e+(o>>fp)]|=i}function Mi(e,t){let n=pp(e,t);if(n!==-1)return n;let r=t[x];r.firstCreatePass&&(e.injectorIndex=t.length,Fa(r.data,e),Fa(t,null),Fa(r.blueprint,null));let o=wl(e,t),i=e.injectorIndex;if(up(o)){let s=wi(o),a=Ii(o,t),c=a[x].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Fa(e,t){e.push(0,0,0,0,0,0,0,0,t)}function pp(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function wl(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=yp(o),r===null)return On;if(n++,o=o[zn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return On}function oc(e,t,n){wv(e,t,n)}function Iv(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(lp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function mp(e,t,n){if(n&R.Optional||e!==void 0)return e;il(t,"NodeInjector")}function hp(e,t,n,r){if(n&R.Optional&&r===void 0&&(r=null),(n&(R.Self|R.Host))===0){let o=e[Xt],i=ye(void 0);try{return o?o.get(t,r,n&R.Optional):Sf(t,r,n&R.Optional)}finally{ye(i)}}return mp(r,t,n)}function gp(e,t,n,r=R.Default,o){if(e!==null){if(t[M]&2048&&!(r&R.Self)){let s=xv(e,t,n,r,Ye);if(s!==Ye)return s}let i=bp(e,t,n,r,Ye);if(i!==Ye)return i}return hp(t,n,r,o)}function bp(e,t,n,r,o){let i=Mv(n);if(typeof i=="function"){if(!np(t,e,r))return r&R.Host?mp(o,n,r):hp(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&R.Optional))il(n);else return s}finally{sp()}}else if(typeof i=="number"){let s=null,a=pp(e,t),c=On,l=r&R.Host?t[xe][_e]:null;for((a===-1||r&R.SkipSelf)&&(c=a===-1?wl(e,t):t[a+8],c===On||!kd(r,!1)?a=-1:(s=t[x],a=wi(c),t=Ii(c,t)));a!==-1;){let u=t[x];if(Od(i,a,u.data)){let d=Cv(a,t,n,s,r,l);if(d!==Ye)return d}c=t[a+8],c!==On&&kd(r,t[x].data[a+8]===l)&&Od(i,a,t)?(s=u,a=wi(c),t=Ii(c,t)):a=-1}}return o}function Cv(e,t,n,r,o,i){let s=t[x],a=s.data[e+8],c=r==null?Gn(a)&&rc:r!=s&&(a.type&3)!==0,l=o&R.Host&&i===a,u=di(a,s,n,c,l);return u!==null?Ar(t,s,u,a):Ye}function di(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&Qe(f)&&f.type===n)return c}return null}function Ar(e,t,n,r){let o=e[n],i=t.data;if(o instanceof on){let s=o;s.resolving&&Tf(gy(i[n]));let a=Ci(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?ye(s.injectImpl):null,u=np(e,r,R.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&mv(n,i[n],t)}finally{l!==null&&ye(l),Ci(a),s.resolving=!1,sp()}}return o}function Mv(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Tr)?e[Tr]:void 0;return typeof t=="number"?t>=0?t&dp:Tv:t}function Od(e,t,n){let r=1<<e;return!!(n[t+(e>>fp)]&r)}function kd(e,t){return!(e&R.Self)&&!(e&R.Host&&t)}var Kt=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return gp(this._tNode,this._lView,t,Hi(r),n)}};function Tv(){return new Kt(fe(),_())}function Il(e){return jr(()=>{let t=e.prototype.constructor,n=t[mi]||ic(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[mi]||ic(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ic(e){return wf(e)?()=>{let t=ic(se(e));return t&&t()}:Qt(e)}function xv(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[M]&2048&&!_i(s);){let a=bp(i,s,n,r|R.Self,Ye);if(a!==Ye)return a;let c=i.parent;if(!c){let l=s[Uf];if(l){let u=l.get(n,Ye,r);if(u!==Ye)return u}c=yp(s),s=s[zn]}i=c}return o}function yp(e){let t=e[x],n=t.type;return n===2?t.declTNode:n===1?e[_e]:null}function vp(e){return Iv(fe(),e)}function Fd(e,t=null,n=null,r){let o=Dp(e,t,n,r);return o.resolveInjectorInitializers(),o}function Dp(e,t=null,n=null,r,o=new Set){let i=[n||ge,Ay(e)];return r=r||(typeof e=="object"?void 0:De(e)),new Sr(i,t||zi(),r||null,o)}var J=class e{static THROW_IF_NOT_FOUND=Yt;static NULL=new yi;static create(t,n){if(Array.isArray(t))return Fd({name:""},n,t,"");{let r=t.name??"";return Fd({name:r},t.parent,t.providers,r)}}static \u0275prov=b({token:e,providedIn:"any",factory:()=>C(Of)});static __NG_ELEMENT_ID__=-1};var Pd=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>vp(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},Sv=new y("");Sv.__NG_ELEMENT_ID__=e=>{let t=fe();if(t===null)throw new D(204,!1);if(t.type&2)return t.value;if(e&R.Optional)return null;throw new D(204,!1)};var _p=!1,Ji=(()=>{class e{static __NG_ELEMENT_ID__=Nv;static __NG_ENV_ID__=n=>n}return e})(),Ti=class extends Ji{_lView;constructor(t){super(),this._lView=t}onDestroy(t){let n=this._lView;return Wn(n)?(t(),()=>{}):(Xf(n,t),()=>Yy(n,t))}};function Nv(){return new Ti(_())}var sn=class{},es=new y("",{providedIn:"root",factory:()=>!1});var Ep=new y(""),wp=new y(""),Zn=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new $t(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})();var sc=class extends B{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Hf()&&(this.destroyRef=m(Ji,{optional:!0})??void 0,this.pendingTasks=m(Zn,{optional:!0})??void 0)}emit(t){let n=A(null);try{super.next(t)}finally{A(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof K&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ve=sc;function Rr(...e){}function Ip(e){let t,n;function r(){e=Rr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Ld(e){return queueMicrotask(()=>e()),()=>{e=Rr}}var Cl="isAngularZone",xi=Cl+"_ID",Av=0,O=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ve(!1);onMicrotaskEmpty=new ve(!1);onStable=new ve(!1);onError=new ve(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=_p}=t;if(typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,kv(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Cl)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Rv,Rr,Rr);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Rv={};function Ml(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ov(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Ip(()=>{e.callbackScheduled=!1,ac(e),e.isCheckStableRunning=!0,Ml(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),ac(e)}function kv(e){let t=()=>{Ov(e)},n=Av++;e._inner=e._inner.fork({name:"angular",properties:{[Cl]:!0,[xi]:n,[xi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Fv(c))return r.invokeTask(i,s,a,c);try{return jd(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Bd(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return jd(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Pv(c)&&t(),Bd(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ac(e),Ml(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ac(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function jd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Bd(e){e._nesting--,Ml(e)}var cc=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ve;onMicrotaskEmpty=new ve;onStable=new ve;onError=new ve;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Fv(e){return Cp(e,"__ignore_ng_zone__")}function Pv(e){return Cp(e,"__scheduler_tick__")}function Cp(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var et=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Lv=new y("",{providedIn:"root",factory:()=>{let e=m(O),t=m(et);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function Vd(e,t){return Df(e,t)}function jv(e){return Df(vf,e)}var kO=(Vd.required=jv,Vd);function Bv(){return Yn(fe(),_())}function Yn(e,t){return new ee(ot(e,t))}var ee=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Bv}return e})();function Mp(e){return e instanceof ee?e.nativeElement:e}function ts(e){return typeof e=="function"&&e[be]!==void 0}function Tl(e,t){let n=Ta(e,t?.equal),r=n[be];return n.set=o=>Er(r,o),n.update=o=>xa(r,o),n.asReadonly=Vv.bind(n),n}function Vv(){let e=this[be];if(e.readonlyFn===void 0){let t=()=>this();t[be]=e,e.readonlyFn=t}return e.readonlyFn}function Tp(e){return ts(e)&&typeof e.set=="function"}function Hv(){return this._results[Symbol.iterator]()}var Bn=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new B}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=My(t);(this._changesDetected=!Cy(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Hv};function xp(e){return(e.flags&128)===128}var Sp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Sp||{}),Np=new Map,Uv=0;function $v(){return Uv++}function zv(e){Np.set(e[Wi],e)}function lc(e){Np.delete(e[Wi])}var Hd="__ngContext__";function Kn(e,t){Mt(t)?(e[Hd]=t[Wi],zv(t)):e[Hd]=t}function Ap(e){return Op(e[Nr])}function Rp(e){return Op(e[He])}function Op(e){for(;e!==null&&!ht(e);)e=e[He];return e}var uc;function kp(e){uc=e}function Gv(){if(uc!==void 0)return uc;if(typeof document<"u")return document;throw new D(210,!1)}var Rt=new y("",{providedIn:"root",factory:()=>Wv}),Wv="ng",xl=new y(""),it=new y("",{providedIn:"platform",factory:()=>"unknown"});var Qn=new y(""),$r=new y("",{providedIn:"root",factory:()=>Gv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var qv="h",Zv="b";var Fp=!1,Yv=new y("",{providedIn:"root",factory:()=>Fp});var Sl=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Sl||{}),Xn=new y(""),Ud=new Set;function dn(e){Ud.has(e)||(Ud.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Nl=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Kv}return e})();function Kv(){return new Nl(_(),fe())}var Nn=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Nn||{}),Pp=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})(),Qv=[Nn.EarlyRead,Nn.Write,Nn.MixedReadWrite,Nn.Read],Xv=(()=>{class e{ngZone=m(O);scheduler=m(sn);errorHandler=m(et,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){m(Xn,{optional:!0})}execute(){let n=this.sequences.size>0;n&&L(16),this.executing=!0;for(let r of Qv)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&L(17)}register(n){let{view:r}=n;r!==void 0?((r[Rn]??=[]).push(n),qn(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Sl.AFTER_NEXT_RENDER,n):n()}static \u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}return e})(),dc=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Rn];t&&(this.view[Rn]=t.filter(n=>n!==this))}};function Jv(e,t){!t?.injector&&ll(Jv);let n=t?.injector??m(J);return dn("NgAfterRender"),Lp(e,n,t,!1)}function Al(e,t){!t?.injector&&ll(Al);let n=t?.injector??m(J);return dn("NgAfterNextRender"),Lp(e,n,t,!0)}function eD(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Lp(e,t,n,r){let o=t.get(Pp);o.impl??=t.get(Xv);let i=t.get(Xn,null,{optional:!0}),s=n?.phase??Nn.MixedReadWrite,a=n?.manualCleanup!==!0?t.get(Ji):null,c=t.get(Nl,null,{optional:!0}),l=new dc(o.impl,eD(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var tD=()=>null;function jp(e,t,n=!1){return tD(e,t,n)}function Bp(e,t){let n=e.contentQueries;if(n!==null){let r=A(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Ki(i),a.contentQueries(2,t[s],s)}}}finally{A(r)}}}function fc(e,t,n){Ki(0);let r=A(null);try{t(e,n)}finally{A(r)}}function Rl(e,t,n){if(ul(t)){let r=A(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{A(r)}}}var tt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(tt||{}),oi;function nD(){if(oi===void 0&&(oi=null,Ze.trustedTypes))try{oi=Ze.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return oi}function ns(e){return nD()?.createHTML(e)||e}var ii;function rD(){if(ii===void 0&&(ii=null,Ze.trustedTypes))try{ii=Ze.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ii}function $d(e){return rD()?.createScriptURL(e)||e}var pt=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${yf})`}},pc=class extends pt{getTypeName(){return"HTML"}},mc=class extends pt{getTypeName(){return"Style"}},hc=class extends pt{getTypeName(){return"Script"}},gc=class extends pt{getTypeName(){return"URL"}},bc=class extends pt{getTypeName(){return"ResourceURL"}};function Ue(e){return e instanceof pt?e.changingThisBreaksApplicationSecurity:e}function Ot(e,t){let n=oD(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${yf})`)}return n===t}function oD(e){return e instanceof pt&&e.getTypeName()||null}function Vp(e){return new pc(e)}function Hp(e){return new mc(e)}function Up(e){return new hc(e)}function $p(e){return new gc(e)}function zp(e){return new bc(e)}function iD(e){let t=new vc(e);return sD()?new yc(t):t}var yc=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(ns(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},vc=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=ns(t),n}};function sD(){try{return!!new window.DOMParser().parseFromString(ns(""),"text/html")}catch{return!1}}var aD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function rs(e){return e=String(e),e.match(aD)?e:"unsafe:"+e}function bt(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function zr(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Gp=bt("area,br,col,hr,img,wbr"),Wp=bt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),qp=bt("rp,rt"),cD=zr(qp,Wp),lD=zr(Wp,bt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),uD=zr(qp,bt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),zd=zr(Gp,lD,uD,cD),Zp=bt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),dD=bt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),fD=bt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),pD=zr(Zp,dD,fD),mD=bt("script,style,template"),Dc=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=bD(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=gD(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Gd(t).toLowerCase();if(!zd.hasOwnProperty(n))return this.sanitizedSomething=!0,!mD.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!pD.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Zp[a]&&(c=rs(c)),this.buf.push(" ",s,'="',Wd(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Gd(t).toLowerCase();zd.hasOwnProperty(n)&&!Gp.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Wd(t))}};function hD(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function gD(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Yp(t);return t}function bD(e){let t=e.firstChild;if(t&&hD(e,t))throw Yp(t);return t}function Gd(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Yp(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var yD=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,vD=/([^\#-~ |!])/g;function Wd(e){return e.replace(/&/g,"&amp;").replace(yD,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(vD,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var si;function Kp(e,t){let n=null;try{si=si||iD(e);let r=t?String(t):"";n=si.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=si.getInertBodyElement(r)}while(r!==i);let a=new Dc().sanitizeChildren(qd(n)||n);return ns(a)}finally{if(n){let r=qd(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function qd(e){return"content"in e&&DD(e)?e.content:null}function DD(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var st=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(st||{});function _D(e){let t=Qp();return t?t.sanitize(st.URL,e)||"":Ot(e,"URL")?Ue(e):rs(kn(e))}function ED(e){let t=Qp();if(t)return $d(t.sanitize(st.RESOURCE_URL,e)||"");if(Ot(e,"ResourceURL"))return $d(Ue(e));throw new D(904,!1)}function wD(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?ED:_D}function FO(e,t,n){return wD(t,n)(e)}function Qp(){let e=_();return e&&e[dt].sanitizer}var ID=/^>|^->|<!--|-->|--!>|<!-$/g,CD=/(<|>)/g,MD="\u200B$1\u200B";function TD(e){return e.replace(ID,t=>t.replace(CD,MD))}function Xp(e){return e instanceof Function?e():e}function xD(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Jp="ng-template";function SD(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&xD(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Ol(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Ol(e){return e.type===4&&e.value!==Jp}function ND(e,t,n){let r=e.type===4&&!n?Jp:e.value;return t===r}function AD(e,t,n){let r=4,o=e.attrs,i=o!==null?kD(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Be(r)&&!Be(c))return!1;if(s&&Be(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!ND(e,c,n)||c===""&&t.length===1){if(Be(r))return!1;s=!0}}else if(r&8){if(o===null||!SD(e,o,c,n)){if(Be(r))return!1;s=!0}}else{let l=t[++a],u=RD(c,o,Ol(e),n);if(u===-1){if(Be(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(Be(r))return!1;s=!0}}}}return Be(r)||s}function Be(e){return(e&1)===0}function RD(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return FD(t,e)}function em(e,t,n=!1){for(let r=0;r<t.length;r++)if(AD(e,t[r],n))return!0;return!1}function OD(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function kD(e){for(let t=0;t<e.length;t++){let n=e[t];if(lp(n))return t}return e.length}function FD(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function PD(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Zd(e,t){return e?":not("+t.trim()+")":t}function LD(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Be(s)&&(t+=Zd(i,o),o=""),r=s,i=i||!Be(r);n++}return o!==""&&(t+=Zd(i,o)),t}function jD(e){return e.map(LD).join(",")}function BD(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Be(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ee={};function VD(e,t){return e.createText(t)}function HD(e,t,n){e.setValue(t,n)}function UD(e,t){return e.createComment(TD(t))}function tm(e,t,n){return e.createElement(t,n)}function Si(e,t,n,r,o){e.insertBefore(t,n,r,o)}function nm(e,t,n){e.appendChild(t,n)}function Yd(e,t,n,r,o){r!==null?Si(e,t,n,r,o):nm(e,t,n)}function $D(e,t,n){e.removeChild(null,t,n)}function zD(e,t,n){e.setAttribute(t,"style",n)}function GD(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function rm(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&yv(e,t,r),o!==null&&GD(e,t,o),i!==null&&zD(e,t,i)}function kl(e,t,n,r,o,i,s,a,c,l,u){let d=Q+r,p=d+o,f=WD(d,p),h=typeof l=="function"?l():l;return f[x]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function WD(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ee);return n}function qD(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=kl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Fl(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[mt]=o,d[M]=r|4|128|8|64|1024,(l!==null||e&&e[M]&2048)&&(d[M]|=2048),Kf(d),d[de]=d[zn]=e,d[re]=n,d[dt]=s||e&&e[dt],d[W]=a||e&&e[W],d[Xt]=c||e&&e[Xt]||null,d[_e]=i,d[Wi]=$v(),d[Pn]=u,d[Uf]=l,d[xe]=t.type==2?e[xe]:d,d}function ZD(e,t,n){let r=ot(t,e),o=qD(n),i=e[dt].rendererFactory,s=Pl(e,Fl(e,o,null,om(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function om(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function im(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Pl(e,t){return e[Nr]?e[Sd][He]=t:e[Nr]=t,e[Sd]=t,t}function PO(e=1){sm(z(),_(),gt()+e,!1)}function sm(e,t,n,r){if(!r)if((t[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&li(t,i,n)}else{let i=e.preOrderHooks;i!==null&&ui(t,i,0,n)}rn(n)}var os=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(os||{});function _c(e,t,n,r){let o=A(null);try{let[i,s,a]=e.inputs[n],c=null;(s&os.SignalBased)!==0&&(c=t[i][be]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):zf(t,c,i,r)}finally{A(o)}}function am(e,t,n,r,o){let i=gt(),s=r&2;try{rn(-1),s&&t.length>Q&&sm(e,t,Q,!1),L(s?2:0,o),n(r,o)}finally{rn(i),L(s?3:1,o)}}function is(e,t,n){e_(e,t,n),(n.flags&64)===64&&t_(e,t,n)}function Ll(e,t,n=ot){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function YD(e,t,n,r){let i=r.get(Yv,Fp)||n===tt.ShadowDom,s=e.selectRootElement(t,i);return KD(s),s}function KD(e){QD(e)}var QD=()=>null;function XD(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function ss(e,t,n,r,o,i,s,a){if(!a&&Bl(t,e,n,r,o)){Gn(t)&&JD(n,t.index);return}if(t.type&3){let c=ot(t,n);r=XD(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)}else t.type&12}function JD(e,t){let n=Je(t,e);n[M]&16||(n[M]|=64)}function e_(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Gn(n)&&ZD(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Mi(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Ar(t,e,s,n);if(Kn(c,t),i!==null&&i_(t,s-r,c,a,n,i),Qe(a)){let l=Je(n.index,t);l[re]=Ar(t,e,s,n)}}}function t_(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=cv();try{rn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];nc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&n_(c,l)}}finally{rn(-1),nc(s)}}function n_(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function jl(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];em(t,i.selectors,!1)&&(r??=[],Qe(i)?r.unshift(i):r.push(i))}return r}function r_(e,t,n,r,o,i){let s=ot(e,t);o_(t[W],s,i,e.value,n,r,o)}function o_(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?kn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function i_(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];_c(r,n,c,l)}}function s_(e,t){let n=e[Xt],r=n?n.get(et,null):null;r&&r.handleError(t)}function Bl(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];_c(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];_c(u,l,r,o),a=!0}return a}function a_(e,t){let n=Je(t,e),r=n[x];c_(r,n);let o=n[mt];o!==null&&n[Pn]===null&&(n[Pn]=jp(o,n[Xt])),L(18),Vl(r,n,n[re]),L(19,n[re])}function c_(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Vl(e,t,n){Dl(t);try{let r=e.viewQuery;r!==null&&fc(1,r,n);let o=e.template;o!==null&&am(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ft]?.finishViewCreation(e),e.staticContentQueries&&Bp(e,t),e.staticViewQueries&&fc(2,e.viewQuery,n);let i=e.components;i!==null&&l_(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[M]&=-5,_l()}}function l_(e,t){for(let n=0;n<t.length;n++)a_(e,t[n])}function Gr(e,t,n,r){let o=A(null);try{let i=t.tView,a=e[M]&4096?4096:16,c=Fl(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Jt]=l;let u=e[ft];return u!==null&&(c[ft]=u.createEmbeddedView(i)),Vl(i,c,n),c}finally{A(o)}}function Vn(e,t){return!t||t.firstChild===null||xp(e)}var u_;function Hl(e,t){return u_(e,t)}var nt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(nt||{});function Ul(e){return(e.flags&32)===32}function An(e,t,n,r,o){if(r!=null){let i,s=!1;ht(r)?i=r:Mt(r)&&(s=!0,r=r[mt]);let a=Xe(r);e===0&&n!==null?o==null?nm(t,n,a):Si(t,n,a,o||null,!0):e===1&&n!==null?Si(t,n,a,o||null,!0):e===2?$D(t,a,s):e===3&&t.destroyNode(a),i!=null&&D_(t,e,i,n,o)}}function d_(e,t){cm(e,t),t[mt]=null,t[_e]=null}function f_(e,t,n,r,o,i){r[mt]=o,r[_e]=t,ls(e,r,n,1,o,i)}function cm(e,t){t[dt].changeDetectionScheduler?.notify(9),ls(e,t,t[W],2,null,null)}function p_(e){let t=e[Nr];if(!t)return Pa(e[x],e);for(;t;){let n=null;if(Mt(t))n=t[Nr];else{let r=t[ue];r&&(n=r)}if(!n){for(;t&&!t[He]&&t!==e;)Mt(t)&&Pa(t[x],t),t=t[de];t===null&&(t=e),Mt(t)&&Pa(t[x],t),n=t&&t[He]}t=n}}function $l(e,t){let n=e[Ln],r=n.indexOf(t);n.splice(r,1)}function as(e,t){if(Wn(t))return;let n=t[W];n.destroyNode&&ls(e,t,n,3,null,null),p_(t)}function Pa(e,t){if(Wn(t))return;let n=A(null);try{t[M]&=-129,t[M]|=256,t[Re]&&_r(t[Re]),h_(e,t),m_(e,t),t[x].type===1&&t[W].destroy();let r=t[Jt];if(r!==null&&ht(t[de])){r!==t[de]&&$l(r,t);let o=t[ft];o!==null&&o.detachView(e)}lc(t)}finally{A(n)}}function m_(e,t){let n=e.cleanup,r=t[vi];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[vi]=null);let o=t[Ct];if(o!==null){t[Ct]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[en];if(i!==null){t[en]=null;for(let s of i)s.destroy()}}function h_(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof on)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];L(4,a,c);try{c.call(a)}finally{L(5,a,c)}}else{L(4,o,i);try{i.call(o)}finally{L(5,o,i)}}}}}function lm(e,t,n){return g_(e,t.parent,n)}function g_(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[mt];if(Gn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===tt.None||o===tt.Emulated)return null}return ot(r,n)}function um(e,t,n){return y_(e,t,n)}function b_(e,t,n){return e.type&40?ot(e,n):null}var y_=b_,Kd;function cs(e,t,n,r){let o=lm(e,r,t),i=t[W],s=r.parent||t[_e],a=um(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Yd(i,o,n[c],a,!1);else Yd(i,o,n,a,!1);Kd!==void 0&&Kd(i,r,t,n,o)}function Mr(e,t){if(t!==null){let n=t.type;if(n&3)return ot(t,e);if(n&4)return Ec(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Mr(e,r);{let o=e[t.index];return ht(o)?Ec(-1,o):Xe(o)}}else{if(n&128)return Mr(e,t.next);if(n&32)return Hl(t,e)()||Xe(e[t.index]);{let r=dm(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=nn(e[xe]);return Mr(o,r)}else return Mr(e,t.next)}}}return null}function dm(e,t){if(t!==null){let r=e[xe][_e],o=t.projection;return r.projection[o]}return null}function Ec(e,t){let n=ue+e+1;if(n<t.length){let r=t[n],o=r[x].firstChild;if(o!==null)return Mr(r,o)}return t[tn]}function zl(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Kn(Xe(a),r),n.flags|=2),!Ul(n))if(c&8)zl(e,t,n.child,r,o,i,!1),An(t,e,o,a,i);else if(c&32){let l=Hl(n,r),u;for(;u=l();)An(t,e,o,u,i);An(t,e,o,a,i)}else c&16?fm(e,t,r,n,o,i):An(t,e,o,a,i);n=s?n.projectionNext:n.next}}function ls(e,t,n,r,o,i){zl(n,r,e.firstChild,t,o,i,!1)}function v_(e,t,n){let r=t[W],o=lm(e,n,t),i=n.parent||t[_e],s=um(i,n,t);fm(r,0,t,n,o,s)}function fm(e,t,n,r,o,i){let s=n[xe],c=s[_e].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];An(t,e,o,u,i)}else{let l=c,u=s[de];xp(r)&&(l.flags|=128),zl(e,t,l,u,o,i,!0)}}function D_(e,t,n,r,o){let i=n[tn],s=Xe(n);i!==s&&An(t,e,r,i,o);for(let a=ue;a<n.length;a++){let c=n[a];ls(c[x],c,e,t,r,i)}}function __(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:nt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=nt.Important),e.setStyle(n,r,o,i))}}function Ni(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Xe(i)),ht(i)&&E_(i,r);let s=n.type;if(s&8)Ni(e,t,n.child,r);else if(s&32){let a=Hl(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=dm(t,n);if(Array.isArray(a))r.push(...a);else{let c=nn(t[xe]);Ni(c[x],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function E_(e,t){for(let n=ue;n<e.length;n++){let r=e[n],o=r[x].firstChild;o!==null&&Ni(r[x],r,o,t)}e[tn]!==e[mt]&&t.push(e[tn])}function pm(e){if(e[Rn]!==null){for(let t of e[Rn])t.impl.addSequence(t);e[Rn].length=0}}var mm=[];function w_(e){return e[Re]??I_(e)}function I_(e){let t=mm.pop()??Object.create(M_);return t.lView=e,t}function C_(e){e.lView[Re]!==e&&(e.lView=null,mm.push(e))}var M_=ce(k({},Tn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{qn(e.lView)},consumerOnSignalRead(){this.lView[Re]=this}});function T_(e){let t=e[Re]??Object.create(x_);return t.lView=e,t}var x_=ce(k({},Tn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=nn(e.lView);for(;t&&!hm(t[x]);)t=nn(t);t&&Qf(t)},consumerOnSignalRead(){this.lView[Re]=this}});function hm(e){return e.type!==2}function gm(e){if(e[en]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[en])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[M]&8192)}}var S_=100;function bm(e,t=!0,n=0){let o=e[dt].rendererFactory,i=!1;i||o.begin?.();try{N_(e,n)}catch(s){throw t&&s_(e,s),s}finally{i||o.end?.()}}function N_(e,t){let n=tp();try{Ei(!0),wc(e,t);let r=0;for(;Yi(e);){if(r===S_)throw new D(103,!1);r++,wc(e,1)}}finally{Ei(n)}}function A_(e,t,n,r){if(Wn(t))return;let o=t[M],i=!1,s=!1;Dl(t);let a=!0,c=null,l=null;i||(hm(e)?(l=w_(t),c=Dr(l)):Ea()===null?(a=!1,l=T_(t),c=Dr(l)):t[Re]&&(_r(t[Re]),t[Re]=null));try{Kf(t),iv(e.bindingStartIndex),n!==null&&am(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&li(t,f,null)}else{let f=e.preOrderHooks;f!==null&&ui(t,f,0,null),ka(t,0)}if(s||R_(t),gm(t),ym(t,0),e.contentQueries!==null&&Bp(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&li(t,f)}else{let f=e.contentHooks;f!==null&&ui(t,f,1),ka(t,1)}k_(e,t);let d=e.components;d!==null&&Dm(t,d,0);let p=e.viewQuery;if(p!==null&&fc(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&li(t,f)}else{let f=e.viewHooks;f!==null&&ui(t,f,2),ka(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Oa]){for(let f of t[Oa])f();t[Oa]=null}i||(pm(t),t[M]&=-73)}catch(u){throw i||qn(t),u}finally{l!==null&&(Ko(l,c),a&&C_(l)),_l()}}function ym(e,t){for(let n=Ap(e);n!==null;n=Rp(n))for(let r=ue;r<n.length;r++){let o=n[r];vm(o,t)}}function R_(e){for(let t=Ap(e);t!==null;t=Rp(t)){if(!(t[M]&2))continue;let n=t[Ln];for(let r=0;r<n.length;r++){let o=n[r];Qf(o)}}}function O_(e,t,n){L(18);let r=Je(t,e);vm(r,n),L(19,r[re])}function vm(e,t){fl(e)&&wc(e,t)}function wc(e,t){let r=e[x],o=e[M],i=e[Re],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Qo(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)A_(r,e,r.template,e[re]);else if(o&8192){gm(e),ym(e,1);let a=r.components;a!==null&&Dm(e,a,1),pm(e)}}function Dm(e,t,n){for(let r=0;r<t.length;r++)O_(e,t[r],n)}function k_(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)rn(~o);else{let i=o,s=n[++r],a=n[++r];av(s,i);let c=t[i];L(24,c),a(2,c),L(25,c)}}}finally{rn(-1)}}function Gl(e,t){let n=tp()?64:1088;for(e[dt].changeDetectionScheduler?.notify(t);e;){e[M]|=n;let r=nn(e);if(_i(e)&&!r)return e;e=r}return null}function _m(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Em(e,t){let n=ue+t;if(n<e.length)return e[n]}function Wr(e,t,n,r=!0){let o=t[x];if(F_(o,t,e,n),r){let s=Ec(n,e),a=t[W],c=a.parentNode(e[tn]);c!==null&&f_(o,e[_e],a,t,c,s)}let i=t[Pn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function wm(e,t){let n=Or(e,t);return n!==void 0&&as(n[x],n),n}function Or(e,t){if(e.length<=ue)return;let n=ue+t,r=e[n];if(r){let o=r[Jt];o!==null&&o!==e&&$l(o,r),t>0&&(e[n-1][He]=r[He]);let i=bi(e,ue+t);d_(r[x],r);let s=i[ft];s!==null&&s.detachView(i[x]),r[de]=null,r[He]=null,r[M]&=-129}return r}function F_(e,t,n,r){let o=ue+r,i=n.length;r>0&&(n[o-1][He]=t),r<i-ue?(t[He]=n[o],Rf(n,ue+r,t)):(n.push(t),t[He]=null),t[de]=n;let s=t[Jt];s!==null&&n!==s&&Im(s,t);let a=t[ft];a!==null&&a.insertView(e),ec(t),t[M]|=128}function Im(e,t){let n=e[Ln],r=t[de];if(Mt(r))e[M]|=2;else{let o=r[de][xe];t[xe]!==o&&(e[M]|=2)}n===null?e[Ln]=[t]:n.push(t)}var kr=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let t=this._lView,n=t[x];return Ni(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r}get context(){return this._lView[re]}set context(t){this._lView[re]=t}get destroyed(){return Wn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[de];if(ht(t)){let n=t[Di],r=n?n.indexOf(this):-1;r>-1&&(Or(t,r),bi(n,r))}this._attachedToViewContainer=!1}as(this._lView[x],this._lView)}onDestroy(t){Xf(this._lView,t)}markForCheck(){Gl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){ec(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,bm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=_i(this._lView),n=this._lView[Jt];n!==null&&!t&&$l(n,this._lView),cm(this._lView[x],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t;let n=_i(this._lView),r=this._lView[Jt];r!==null&&!n&&Im(r,this._lView),ec(this._lView)}};var St=(()=>{class e{static __NG_ELEMENT_ID__=j_}return e})(),P_=St,L_=class extends P_{_declarationLView;_declarationTContainer;elementRef;constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Gr(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new kr(o)}};function j_(){return us(fe(),_())}function us(e,t){return e.type&4?new L_(t,e,Yn(e,t)):null}function Jn(e,t,n,r,o){let i=e.data[t];if(i===null)i=B_(e,t,n,r,o),sv()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=nv();i.injectorIndex=s===null?-1:s.injectorIndex}return At(i,!0),i}function B_(e,t,n,r,o){let i=ep(),s=gl(),a=s?i:i&&i.parent,c=e.data[t]=H_(e,a,n,t,r,o);return V_(e,c,i,s),c}function V_(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function H_(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Jf()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var BO=new RegExp(`^(\\d+)*(${Zv}|${qv})*(.*)`);var U_=()=>null;function Hn(e,t){return U_(e,t)}var $_=class{},Cm=class{},Ic=class{resolveComponentFactory(t){throw Error(`No component factory found for ${De(t)}.`)}},ds=class{static NULL=new Ic},rt=class{},qr=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>z_()}return e})();function z_(){let e=_(),t=fe(),n=Je(t.index,e);return(Mt(n)?n:e)[W]}var G_=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>null})}return e})();var La={},Cc=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Hi(r);let o=this.injector.get(t,La,r);return o!==La||n===La?o:this.parentInjector.get(t,n,r)}};function Mc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Wa(o,a);else if(i==2){let c=a,l=t[++s];r=Wa(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function H(e,t=R.Default){let n=_();if(n===null)return C(e,t);let r=fe();return gp(r,n,se(e),t)}function $O(){let e="invalid";throw new Error(e)}function Wl(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a,c=null,l=null,u=q_(s);u===null?a=s:[a,c,l]=u,K_(e,t,n,a,i,c,l)}i!==null&&r!==null&&W_(n,r,i)}function W_(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new D(-301,!1);r.push(t[o],i)}}function q_(e){let t=null,n=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&Qe(a)&&(t=a),a.findHostDirectiveDefs!==null){n=!0;break}}if(!n)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,Z_(s,r,i,o)),s===t&&(r??=[],r.push(s));return r!==null?(r.push(...t===null?e:e.slice(1)),[r,o,i]):null}function Z_(e,t,n,r){let o=t.length;e.findHostDirectiveDefs(e,t,r),n.set(e,[o,t.length-1])}function Y_(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function K_(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&Qe(f)&&(c=!0,Y_(e,n,p)),oc(Mi(n,t),e,f.type)}nE(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=im(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=jn(n.mergedAttrs,f.hostAttrs),X_(e,n,t,d,f),tE(d,f,o),s!==null&&s.has(f)){let[g,v]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,v+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}Q_(e,n,i)}function Q_(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Qd(0,t,o,r),Qd(1,t,o,r),Jd(t,r,!1);else{let i=n.get(o);Xd(0,t,i,r),Xd(1,t,i,r),Jd(t,r,!0)}}}function Qd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Mm(t,i)}}function Xd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Mm(t,s)}}function Mm(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Jd(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||Ol(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function X_(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Qt(o.type,!0)),s=new on(i,Qe(o),H);e.blueprint[r]=s,n[r]=s,J_(e,t,r,im(e,n,o.hostVars,Ee),o)}function J_(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;eE(s)!=a&&s.push(a),s.push(n,r,i)}}function eE(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function tE(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Qe(t)&&(n[""]=e)}}function nE(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Tm(e,t,n,r,o,i,s,a){let c=t.consts,l=xt(c,s),u=Jn(t,e,2,r,l);return i&&Wl(t,n,u,xt(c,a),o),u.mergedAttrs=jn(u.mergedAttrs,u.attrs),u.attrs!==null&&Mc(u,u.attrs,!1),u.mergedAttrs!==null&&Mc(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function xm(e,t){El(e,t),ul(t)&&e.queries.elementEnd(t)}var Ai=class extends ds{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Tt(t);return new an(n,this.ngModule)}};function rE(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&os.SignalBased)!==0};return o&&(i.transform=o),i})}function oE(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function iE(e,t,n){let r=t instanceof Oe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Cc(n,r):n}function sE(e){let t=e.get(rt,null);if(t===null)throw new D(407,!1);let n=e.get(G_,null),r=e.get(sn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r}}function aE(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return tm(t,n,n==="svg"?Zf:n==="math"?Gy:null)}var an=class extends Cm{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=rE(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=oE(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=jD(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o){L(22);let i=A(null);try{let s=this.componentDef,a=r?["ng-version","19.2.9"]:BD(this.componentDef.selectors[0]),c=kl(0,null,null,1,0,null,null,null,null,[a],null),l=iE(s,o||this.ngModule,t),u=sE(l),d=u.rendererFactory.createRenderer(null,s),p=r?YD(d,r,s.encapsulation,l):aE(s,d),f=Fl(null,c,null,512|om(s),null,null,u,d,l,null,jp(p,l,!0));f[Q]=p,Dl(f);let h=null;try{let g=Tm(Q,c,f,"#host",()=>[this.componentDef],!0,0);p&&(rm(d,p,g),Kn(p,f)),is(c,f,g),Rl(c,g,f),xm(c,g),n!==void 0&&cE(g,this.ngContentSelectors,n),h=Je(g.index,f),f[re]=h[re],Vl(c,f,null)}catch(g){throw h!==null&&lc(h),lc(f),g}finally{L(23),_l()}return new Tc(this.componentType,f)}finally{A(i)}}},Tc=class extends $_{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n){super(),this._rootLView=n,this._tNode=dl(n[x],Q),this.location=Yn(this._tNode,n),this.instance=Je(this._tNode.index,n)[re],this.hostView=this.changeDetectorRef=new kr(n,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=Bl(r,o[x],o,t,n);this.previousInputValues.set(t,n);let s=Je(r.index,o);Gl(s,1)}get injector(){return new Kt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function cE(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var kt=(()=>{class e{static __NG_ELEMENT_ID__=lE}return e})();function lE(){let e=fe();return Nm(e,_())}var uE=kt,Sm=class extends uE{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Yn(this._hostTNode,this._hostLView)}get injector(){return new Kt(this._hostTNode,this._hostLView)}get parentInjector(){let t=wl(this._hostTNode,this._hostLView);if(up(t)){let n=Ii(t,this._hostLView),r=wi(t),o=n[x].data[r+8];return new Kt(o,n)}else return new Kt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=ef(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ue}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Hn(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Vn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Hy(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new an(Tt(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let g=(s?l:this.parentInjector).get(Oe,null);g&&(i=g)}let u=Tt(c.componentType??{}),d=Hn(this._lContainer,u?.id??null),p=d?.firstChild??null,f=c.create(l,o,p,i);return this.insertImpl(f.hostView,a,Vn(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(qy(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[de],l=new Sm(c,c[_e],c[de]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Wr(s,o,i,r),t.attachToViewContainerRef(),Rf(ja(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=ef(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Or(this._lContainer,n);r&&(bi(ja(this._lContainer),n),as(r[x],r))}detach(t){let n=this._adjustIndex(t,-1),r=Or(this._lContainer,n);return r&&bi(ja(this._lContainer),n)!=null?new kr(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function ef(e){return e[Di]}function ja(e){return e[Di]||(e[Di]=[])}function Nm(e,t){let n,r=t[e.index];return ht(r)?n=r:(n=_m(r,t,null,e),t[e.index]=n,Pl(t,n)),fE(n,t,e,r),new Sm(n,e,t)}function dE(e,t){let n=e[W],r=n.createComment(""),o=ot(t,e),i=n.parentNode(o);return Si(n,i,r,n.nextSibling(o),!1),r}var fE=hE,pE=()=>!1;function mE(e,t,n){return pE(e,t,n)}function hE(e,t,n,r){if(e[tn])return;let o;n.type&8?o=Xe(r):o=dE(t,n),e[tn]=o}var xc=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Sc=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Zl(t,n).matches!==null&&this.queries[n].setDirty()}},Ri=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=_E(t):this.predicate=t}},Nc=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Ac=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,gE(n,i)),this.matchTNodeWithReadOption(t,n,di(n,t,i,!1,!1))}else r===St?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,di(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===ee||o===kt||o===St&&n.type&4)this.addMatch(n.index,-2);else{let i=di(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function gE(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function bE(e,t){return e.type&11?Yn(e,t):e.type&4?us(e,t):null}function yE(e,t,n,r){return n===-1?bE(t,e):n===-2?vE(e,t,r):Ar(e,e[x],n,t)}function vE(e,t,n){if(n===ee)return Yn(t,e);if(n===St)return us(t,e);if(n===kt)return Nm(t,e)}function Am(e,t,n,r){let o=t[ft].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(yE(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Rc(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Am(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=ue;d<u.length;d++){let p=u[d];p[Jt]===p[de]&&Rc(p[x],p,l,r)}if(u[Ln]!==null){let d=u[Ln];for(let p=0;p<d.length;p++){let f=d[p];Rc(f[x],f,l,r)}}}}}return r}function ql(e,t){return e[ft].queries[t].queryList}function Rm(e,t,n){let r=new Bn((n&4)===4);return Ky(e,t,r,r.destroy),(t[ft]??=new Sc).queries.push(new xc(r))-1}function DE(e,t,n){let r=z();return r.firstCreatePass&&(km(r,new Ri(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Rm(r,_(),t)}function Om(e,t,n,r){let o=z();if(o.firstCreatePass){let i=fe();km(o,new Ri(t,n,r),i.index),EE(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Rm(o,_(),n)}function _E(e){return e.split(",").map(t=>t.trim())}function km(e,t,n){e.queries===null&&(e.queries=new Nc),e.queries.track(new Ac(t,n))}function EE(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Zl(e,t){return e.queries.getByIndex(t)}function Fm(e,t){let n=e[x],r=Zl(n,t);return r.crossesNgTemplate?Rc(n,e,t,[]):Am(n,e,r,t)}function Pm(e,t,n){let r,o=ei(()=>{r._dirtyCounter();let i=ME(r,e);if(t&&i===void 0)throw new D(-951,!1);return i});return r=o[be],r._dirtyCounter=Tl(0),r._flatValue=void 0,o}function wE(e){return Pm(!0,!1,e)}function IE(e){return Pm(!0,!0,e)}function CE(e,t){let n=e[be];n._lView=_(),n._queryIndex=t,n._queryList=ql(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function ME(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[M]&4)return t?void 0:ge;let o=ql(n,r),i=Fm(n,r);return o.reset(i,Mp),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}function tf(e,t){return wE(t)}function TE(e,t){return IE(t)}var GO=(tf.required=TE,tf);var Un=class{},xE=class{};var Oc=class extends Un{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ai(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Ff(t);this._bootstrapComponents=Xp(i.bootstrap),this._r3Injector=Dp(t,n,[{provide:Un,useValue:this},{provide:ds,useValue:this.componentFactoryResolver},...r],De(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},kc=class extends xE{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Oc(this.moduleType,t,[])}};var Oi=class extends Un{injector;componentFactoryResolver=new Ai(this);instance=null;constructor(t){super();let n=new Sr([...t.providers,{provide:Un,useValue:this},{provide:ds,useValue:this.componentFactoryResolver}],t.parent||zi(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function SE(e,t,n=null){return new Oi({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var NE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Lf(!1,n.type),o=r.length>0?SE([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(C(Oe))})}return e})();function we(e){return jr(()=>{let t=Lm(e),n=ce(k({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Sp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(NE).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||tt.Emulated,styles:e.styles||ge,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&dn("NgStandalone"),jm(n);let r=e.dependencies;return n.directiveDefs=nf(r,!1),n.pipeDefs=nf(r,!0),n.id=FE(n),n})}function AE(e){return Tt(e)||Pf(e)}function RE(e){return e!==null}function ie(e){return jr(()=>({type:e.type,bootstrap:e.bootstrap||ge,declarations:e.declarations||ge,imports:e.imports||ge,exports:e.exports||ge,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function OE(e,t){if(e==null)return Ke;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=os.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function kE(e){if(e==null)return Ke;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function q(e){return jr(()=>{let t=Lm(e);return jm(t),t})}function er(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Lm(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ke,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||ge,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:OE(e.inputs,t),outputs:kE(e.outputs),debugInfo:null}}function jm(e){e.features?.forEach(t=>t(e))}function nf(e,t){if(!e)return null;let n=t?Ny:AE;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(RE)}function FE(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function PE(e){return Object.getPrototypeOf(e.prototype).constructor}function tr(e){let t=PE(e.type),n=!0,r=[e];for(;t;){let o;if(Qe(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Ba(e.inputs),s.declaredInputs=Ba(e.declaredInputs),s.outputs=Ba(e.outputs);let a=o.hostBindings;a&&HE(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&BE(e,c),l&&VE(e,l),LE(e,o),cy(e.outputs,o.outputs),Qe(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===tr&&(n=!1)}}t=Object.getPrototypeOf(t)}jE(r)}function LE(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function jE(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=jn(o.hostAttrs,n=jn(n,o.hostAttrs))}}function Ba(e){return e===Ke?{}:e===ge?[]:e}function BE(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function VE(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function HE(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function ZO(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.findHostDirectiveDefs=Bm,n.hostDirectives=r?e.map(Fc):[e]):r?n.hostDirectives.unshift(...e.map(Fc)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function Bm(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)rf(Fc(i),t,n)}else rf(r,t,n)}function rf(e,t,n){let r=Pf(e.directive);UE(r.declaredInputs,e.inputs),Bm(r,t,n),n.set(r,e),t.push(r)}function Fc(e){return typeof e=="function"?{directive:se(e),inputs:Ke,outputs:Ke}:{directive:se(e.directive),inputs:of(e.inputs),outputs:of(e.outputs)}}function of(e){if(e===void 0||e.length===0)return Ke;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function UE(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function Vm(e){return Yl(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function $E(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Yl(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Zr(e,t,n){return e[t]=n}function zE(e,t){return e[t]}function Se(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Fr(e,t,n,r){let o=Se(e,t,n);return Se(e,t+1,r)||o}function GE(e,t,n,r,o){let i=Fr(e,t,n,r);return Se(e,t+2,o)||i}function WE(e,t,n,r,o,i){let s=Fr(e,t,n,r);return Fr(e,t+2,o,i)||s}function qE(e,t,n,r,o,i,s,a,c){let l=t.consts,u=Jn(t,e,4,s||null,a||null);hl()&&Wl(t,n,u,xt(l,c),jl),u.mergedAttrs=jn(u.mergedAttrs,u.attrs),El(t,u);let d=u.tView=kl(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function ki(e,t,n,r,o,i,s,a,c,l){let u=n+Q,d=t.firstCreatePass?qE(u,t,e,r,o,i,s,a,c):t.data[u];At(d,!1);let p=YE(t,e,d,n);Qi()&&cs(t,e,p,d),Kn(p,e);let f=_m(p,e,p,d);return e[u]=f,Pl(e,f),mE(f,d,e),qi(d)&&is(t,e,d),c!=null&&Ll(e,d,l),d}function ZE(e,t,n,r,o,i,s,a){let c=_(),l=z(),u=xt(l.consts,i);return ki(c,l,e,t,n,r,o,u,s,a),ZE}var YE=KE;function KE(e,t,n,r){return Xi(!0),t[W].createComment("")}var QE=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Hm=new y("");var Um=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>new Pc})}return e})(),Pc=class{queuedEffectCount=0;queues=new Map;schedule(t){this.enqueue(t)}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),this.queuedEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||(this.queuedEffectCount++,r.add(t))}flush(){for(;this.queuedEffectCount>0;)for(let[t,n]of this.queues)t===null?this.flushQueue(n):t.run(()=>this.flushQueue(n))}flushQueue(t){for(let n of t)t.delete(n),this.queuedEffectCount--,n.run()}};function fs(e){return!!e&&typeof e.then=="function"}function Kl(e){return!!e&&typeof e.subscribe=="function"}var $m=new y("");function YO(e){return Vr([{provide:$m,multi:!0,useValue:e}])}var zm=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=m($m,{optional:!0})??[];injector=m(J);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Gi(this.injector,o);if(fs(i))n.push(i);else if(Kl(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),XE=new y("");function JE(){Ma(()=>{throw new D(600,!1)})}function ew(e){return e.isBoundToModule}var tw=10;var Nt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=m(Lv);afterRenderManager=m(Pp);zonelessEnabled=m(es);rootEffectScheduler=m(Um);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new B;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=m(Zn).hasPendingTasks.pipe(X(n=>!n));constructor(){m(Xn,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=m(Oe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=J.NULL){L(10);let i=n instanceof Cm;if(!this._injector.get(zm).done){let f="";throw new D(405,f)}let a;i?a=n:a=this._injector.get(ds).resolveComponentFactory(n),this.componentTypes.push(a.componentType);let c=ew(a)?void 0:this._injector.get(Un),l=r||a.selector,u=a.create(o,[],l,c),d=u.location.nativeElement,p=u.injector.get(Hm,null);return p?.registerApplication(d),u.onDestroy(()=>{this.detachView(u.hostView),fi(this.components,u),p?.unregisterApplication(d)}),this._loadComponent(u),L(11,u),u}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){L(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Sl.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new D(101,!1);let n=A(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,A(n),this.afterTick.next(),L(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(rt,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<tw;)L(14),this.synchronizeOnce(),L(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let n=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)nw(r,o,n,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Yi(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;fi(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n),this._injector.get(XE,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>fi(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fi(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function nw(e,t,n,r){if(!n&&!Yi(e))return;bm(e,t,n&&!r?0:1)}function fn(e,t,n,r){let o=_(),i=un();if(Se(o,i,t)){let s=z(),a=Ur();r_(a,o,e,t,n,r)}return fn}function Ql(e,t,n,r){return Se(e,un(),n)?t+kn(n)+r:Ee}function rw(e,t,n,r,o,i){let s=ov(),a=Fr(e,s,n,o);return yl(2),a?t+kn(n)+r+kn(o)+i:Ee}function ai(e,t){return e<<17|t<<2}function cn(e){return e>>17&32767}function ow(e){return(e&2)==2}function iw(e,t){return e&131071|t<<17}function Lc(e){return e|2}function $n(e){return(e&131068)>>2}function Va(e,t){return e&-131069|t<<2}function sw(e){return(e&1)===1}function jc(e){return e|1}function aw(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=cn(s),c=$n(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Br(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=cn(e[a+1]);e[r+1]=ai(p,a),p!==0&&(e[p+1]=Va(e[p+1],r)),e[a+1]=iw(e[a+1],r)}else e[r+1]=ai(a,0),a!==0&&(e[a+1]=Va(e[a+1],r)),a=r;else e[r+1]=ai(c,0),a===0?a=r:e[c+1]=Va(e[c+1],r),c=r;l&&(e[r+1]=Lc(e[r+1])),sf(e,u,r,!0),sf(e,u,r,!1),cw(t,u,e,r,i),s=ai(a,c),i?t.classBindings=s:t.styleBindings=s}function cw(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Br(i,t)>=0&&(n[r+1]=jc(n[r+1]))}function sf(e,t,n,r){let o=e[n+1],i=t===null,s=r?cn(o):$n(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];lw(c,t)&&(a=!0,e[s+1]=r?jc(l):Lc(l)),s=r?cn(l):$n(l)}a&&(e[n+1]=r?Lc(o):jc(o))}function lw(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Br(e,t)>=0:!1}var Ve={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function uw(e){return e.substring(Ve.key,Ve.keyEnd)}function dw(e){return fw(e),Gm(e,Wm(e,0,Ve.textEnd))}function Gm(e,t){let n=Ve.textEnd;return n===t?-1:(t=Ve.keyEnd=pw(e,Ve.key=t,n),Wm(e,t,n))}function fw(e){Ve.key=0,Ve.keyEnd=0,Ve.value=0,Ve.valueEnd=0,Ve.textEnd=e.length}function Wm(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function pw(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function mw(e,t,n){let r=_(),o=un();if(Se(r,o,t)){let i=z(),s=Ur();ss(i,s,r,e,t,r[W],n,!1)}return mw}function Bc(e,t,n,r,o){Bl(t,e,n,o?"class":"style",r)}function hw(e,t,n){return Zm(e,t,n,!1),hw}function Ne(e,t){return Zm(e,t,null,!0),Ne}function Yr(e){Ym(_w,qm,e,!0)}function qm(e,t){for(let n=dw(t);n>=0;n=Gm(t,n))Ui(e,uw(t),!0)}function Zm(e,t,n,r){let o=_(),i=z(),s=yl(2);if(i.firstUpdatePass&&Qm(i,e,s,r),t!==Ee&&Se(o,s,t)){let a=i.data[gt()];Xm(i,a,o,o[W],e,o[s+1]=ww(t,n),r,s)}}function Ym(e,t,n,r){let o=z(),i=yl(2);o.firstUpdatePass&&Qm(o,null,i,r);let s=_();if(n!==Ee&&Se(s,i,n)){let a=o.data[gt()];if(Jm(a,r)&&!Km(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Wa(c,n||"")),Bc(o,a,s,n,r)}else Ew(o,a,s,s[W],s[i+1],s[i+1]=Dw(e,t,n),r,i)}}function Km(e,t){return t>=e.expandoStartIndex}function Qm(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[gt()],s=Km(e,n);Jm(i,r)&&t===null&&!s&&(t=!1),t=gw(o,i,t,r),aw(o,i,t,n,s,r)}}function gw(e,t,n,r){let o=lv(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Ha(null,e,t,n,r),n=Pr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Ha(o,e,t,n,r),i===null){let c=bw(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Ha(null,e,t,c[1],r),c=Pr(c,t.attrs,r),yw(e,t,r,c))}else i=vw(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function bw(e,t,n){let r=n?t.classBindings:t.styleBindings;if($n(r)!==0)return e[cn(r)]}function yw(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[cn(o)]=r}function vw(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Pr(r,s,n)}return Pr(r,t.attrs,n)}function Ha(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Pr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Pr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Ui(e,s,n?!0:t[++i]))}return e===void 0?null:e}function Dw(e,t,n){if(n==null||n==="")return ge;let r=[],o=Ue(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function _w(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&Ui(e,r,n)}function Ew(e,t,n,r,o,i,s,a){o===Ee&&(o=ge);let c=0,l=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=l<i.length?i[l+1]:void 0,h=null,g;u===d?(c+=2,l+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(c+=2,h=u):(l+=2,h=d,g=f),h!==null&&Xm(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,d=l<i.length?i[l]:null}}function Xm(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=sw(l)?af(c,t,n,o,$n(l),s):void 0;if(!Fi(u)){Fi(i)||ow(l)&&(i=af(c,null,n,o,a,s));let d=Yf(gt(),n);__(r,s,d,o,i)}}function af(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===Ee&&(p=d?ge:void 0);let f=d?Aa(p,r):u===r?p:void 0;if(l&&!Fi(f)&&(f=Aa(c,r)),Fi(f)&&(a=f,s))return a;let h=e[o+1];o=s?cn(h):$n(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Aa(c,r))}return a}function Fi(e){return e!==void 0}function ww(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=De(Ue(e)))),e}function Jm(e,t){return(e.flags&(t?8:16))!==0}function KO(e,t,n){let r=_(),o=Ql(r,e,t,n);Ym(Ui,qm,o,!0)}var Vc=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function Ua(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function Iw(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=Ua(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],h=Ua(s,p,c,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,c--;continue}let g=n(i,l),v=n(s,p),w=n(i,u);if(Object.is(w,v)){let $=n(c,f);Object.is($,g)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new Pi,o??=lf(e,i,s,n),Hc(e,r,i,w))e.updateValue(i,u),i++,s++;else if(o.has(w))r.set(g,e.detach(i)),s--;else{let $=e.create(i,t[i]);e.attach(i,$),i++,s++}}for(;i<=c;)cf(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=Ua(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new Pi,o??=lf(e,i,s,n);let f=n(i,d);if(Hc(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)cf(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Hc(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function cf(e,t,n,r,o){if(Hc(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function lf(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Pi=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function QO(e,t){dn("NgControlFlow");let n=_(),r=un(),o=n[r]!==Ee?n[r]:-1,i=o!==-1?Li(n,Q+o):void 0,s=0;if(Se(n,r,e)){let a=A(null);try{if(i!==void 0&&wm(i,s),e!==-1){let c=Q+e,l=Li(n,c),u=Gc(n[x],c),d=Hn(l,u.tView.ssrId),p=Gr(n,u,t,{dehydratedView:d});Wr(l,p,s,Vn(u,d))}}finally{A(a)}}else if(i!==void 0){let a=Em(i,s);a!==void 0&&(a[re]=t)}}var Uc=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-ue}};function XO(e,t){return t}var $c=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function JO(e,t,n,r,o,i,s,a,c,l,u,d,p){dn("NgControlFlow");let f=_(),h=z(),g=c!==void 0,v=_(),w=a?s.bind(v[xe][re]):s,$=new $c(g,w);v[Q+e]=$,ki(f,h,e+1,t,n,r,o,xt(h.consts,i)),g&&ki(f,h,e+2,c,l,u,d,xt(h.consts,p))}var zc=class extends Vc{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-ue}at(t){return this.getLView(t)[re].$implicit}attach(t,n){let r=n[Pn];this.needsIndexUpdate||=t!==this.length,Wr(this.lContainer,n,t,Vn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,Cw(this.lContainer,t)}create(t,n){let r=Hn(this.lContainer,this.templateTNode.tView.ssrId),o=Gr(this.hostLView,this.templateTNode,new Uc(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){as(t[x],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[re].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[re].$index=t}getLView(t){return Mw(this.lContainer,t)}};function ek(e){let t=A(null),n=gt();try{let r=_(),o=r[x],i=r[n],s=n+1,a=Li(r,s);if(i.liveCollection===void 0){let l=Gc(o,s);i.liveCollection=new zc(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(Iw(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=un(),u=c.length===0;if(Se(r,l,u)){let d=n+2,p=Li(r,d);if(u){let f=Gc(o,d),h=Hn(p,f.tView.ssrId),g=Gr(r,f,void 0,{dehydratedView:h});Wr(p,g,0,Vn(f,h))}else wm(p,0)}}}finally{A(t)}}function Li(e,t){return e[t]}function Cw(e,t){return Or(e,t)}function Mw(e,t){return Em(e,t)}function Gc(e,t){return dl(e,t)}function pn(e,t,n,r){let o=_(),i=z(),s=Q+e,a=o[W],c=i.firstCreatePass?Tm(s,i,o,t,jl,hl(),n,r):i.data[s],l=Tw(i,o,c,a,t,e);o[s]=l;let u=qi(c);return At(c,!0),rm(a,l,c),!Ul(c)&&Qi()&&cs(i,o,l,c),(Qy()===0||u)&&Kn(l,o),Xy(),u&&(is(i,o,c),Rl(i,c,o)),r!==null&&Ll(o,c),pn}function mn(){let e=fe();gl()?bl():(e=e.parent,At(e,!1));let t=e;ev(t)&&tv(),Jy();let n=z();return n.firstCreatePass&&xm(n,t),t.classesWithoutHost!=null&&gv(t)&&Bc(n,t,_(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&bv(t)&&Bc(n,t,_(),t.stylesWithoutHost,!1),mn}function yt(e,t,n,r){return pn(e,t,n,r),mn(),yt}var Tw=(e,t,n,r,o,i)=>(Xi(!0),tm(r,o,pv()));function xw(e,t,n,r,o){let i=t.consts,s=xt(i,r),a=Jn(t,e,8,"ng-container",s);s!==null&&Mc(a,s,!0);let c=xt(i,o);return hl()&&Wl(t,n,a,c,jl),a.mergedAttrs=jn(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function eh(e,t,n){let r=_(),o=z(),i=e+Q,s=o.firstCreatePass?xw(i,o,r,t,n):o.data[i];At(s,!0);let a=Nw(o,r,s,e);return r[i]=a,Qi()&&cs(o,r,a,s),Kn(a,r),qi(s)&&(is(o,r,s),Rl(o,s,r)),n!=null&&Ll(r,s),eh}function th(){let e=fe(),t=z();return gl()?bl():(e=e.parent,At(e,!1)),t.firstCreatePass&&(El(t,e),ul(e)&&t.queries.elementEnd(e)),th}function Sw(e,t,n){return eh(e,t,n),th(),Sw}var Nw=(e,t,n,r)=>(Xi(!0),UD(t[W],""));function tk(){return _()}function Aw(e,t,n){let r=_(),o=un();if(Se(r,o,t)){let i=z(),s=Ur();ss(i,s,r,e,t,r[W],n,!0)}return Aw}var Zt=void 0;function Rw(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var Ow=["en",[["a","p"],["AM","PM"],Zt],[["AM","PM"],Zt,Zt],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Zt,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Zt,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Zt,"{1} 'at' {0}",Zt],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Rw],$a={};function Ie(e){let t=kw(e),n=uf(t);if(n)return n;let r=t.split("-")[0];if(n=uf(r),n)return n;if(r==="en")return Ow;throw new D(701,!1)}function uf(e){return e in $a||($a[e]=Ze.ng&&Ze.ng.common&&Ze.ng.common.locales&&Ze.ng.common.locales[e]),$a[e]}var Z=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(Z||{});function kw(e){return e.toLowerCase().replace(/_/g,"-")}var ji="en-US",Fw="USD";var Pw=ji;function Lw(e){typeof e=="string"&&(Pw=e.toLowerCase().replace(/_/g,"-"))}function df(e,t,n){return function r(o){if(o===Function)return n;let i=Gn(e)?Je(e.index,t):t;Gl(i,5);let s=t[re],a=ff(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=ff(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function ff(e,t,n,r){let o=A(null);try{return L(6,t,n),n(r)!==!1}catch(i){return jw(e,i),!1}finally{L(7,t,n),A(o)}}function jw(e,t){let n=e[Xt],r=n?n.get(et,null):null;r&&r.handleError(t)}function pf(e,t,n,r,o,i){let s=t[n],a=t[x],l=a.data[n].outputs[r],u=s[l],d=a.firstCreatePass?ml(a):null,p=pl(t),f=u.subscribe(i),h=p.length;p.push(i,f),d&&d.push(o,e.index,h,-(h+1))}var Bw=new Map;function Vw(e,t,n,r){let o=_(),i=z(),s=fe();return nh(i,o,o[W],s,e,t,r),Vw}function Hw(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[vi],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function nh(e,t,n,r,o,i,s){let a=qi(r),l=e.firstCreatePass?ml(e):null,u=pl(t),d=!0;if(r.type&3||s){let p=ot(r,t),f=s?s(p):p,h=u.length,g=s?w=>s(Xe(w[r.index])):r.index,v=null;if(!s&&a&&(v=Hw(e,t,o,r.index)),v!==null){let w=v.__ngLastListenerFn__||v;w.__ngNextListenerFn__=i,v.__ngLastListenerFn__=i,d=!1}else{i=df(r,t,i);let w=t[Xt].get(Rt);Bw.get(w)?.(f,o,i);let Y=n.listen(f,o,i);u.push(i,Y),l&&l.push(o,g,h,h+1)}}else i=df(r,t,i);if(d){let p=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let h=0;h<f.length;h+=2){let g=f[h],v=f[h+1];pf(r,t,g,v,o,i)}if(p&&p.length)for(let h of p)pf(r,t,h,o,o,i)}}function nk(e=1){return dv(e)}function Uw(e,t){let n=null,r=OD(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?em(e,i,!0):PD(r,i))return o}return n}function Ft(e){let t=_()[xe][_e];if(!t.projection){let n=e?e.length:1,r=t.projection=Ty(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Uw(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Ce(e,t=0,n,r,o,i){let s=_(),a=z(),c=r?e+1:null;c!==null&&ki(s,a,c,r,o,i,null,n);let l=Jn(a,Q+e,16,null,n||null);l.projection===null&&(l.projection=t),bl();let d=!s[Pn]||Jf();s[xe][_e].projection[l.projection]===null&&c!==null?$w(s,a,c):d&&!Ul(l)&&v_(a,s,l)}function $w(e,t,n){let r=Q+n,o=t.data[r],i=e[r],s=Hn(i,o.tView.ssrId),a=Gr(e,o,void 0,{dehydratedView:s});Wr(i,a,0,Vn(o,s))}function zw(e,t,n,r,o){let i=_(),s=Ql(i,t,n,r);if(s!==Ee){let a=z(),c=Ur();ss(a,c,i,e,s,i[W],o,!1)}return zw}function rk(e,t,n,r){Om(e,t,n,r)}function ok(e,t,n){DE(e,t,n)}function ik(e){let t=_(),n=z(),r=vl();Ki(r+1);let o=Zl(n,r);if(e.dirty&&Wy(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Fm(t,r);e.reset(i,Mp),e.notifyOnChanges()}return!0}return!1}function sk(){return ql(_(),vl())}function ak(e,t,n,r,o){CE(t,Om(e,n,r,o))}function ck(e=1){Ki(vl()+e)}function rh(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function lk(e){let t=rv();return Hr(t,Q+e)}function uk(e,t=""){let n=_(),r=z(),o=e+Q,i=r.firstCreatePass?Jn(r,o,1,t,null):r.data[o],s=Gw(r,n,i,t,e);n[o]=s,Qi()&&cs(r,n,s,i),At(i,!1)}var Gw=(e,t,n,r,o)=>(Xi(!0),VD(t[W],r));function Ww(e){return oh("",e,""),Ww}function oh(e,t,n){let r=_(),o=Ql(r,e,t,n);return o!==Ee&&ih(r,gt(),o),oh}function qw(e,t,n,r,o){let i=_(),s=rw(i,e,t,n,r,o);return s!==Ee&&ih(i,gt(),s),qw}function ih(e,t,n){let r=Yf(t,e);HD(e[W],r,n)}function Zw(e,t,n){Tp(t)&&(t=t());let r=_(),o=un();if(Se(r,o,t)){let i=z(),s=Ur();ss(i,s,r,e,t,r[W],n,!1)}return Zw}function dk(e,t){let n=Tp(e);return n&&e.set(t),n}function Yw(e,t){let n=_(),r=z(),o=fe();return nh(r,n,n[W],o,e,t),Yw}var Kw={};function Qw(e){let t=z(),n=_(),r=e+Q,o=Jn(t,r,128,null,null);return At(o,!1),rh(t,n,r,Kw),Qw}function Xw(e,t,n){let r=z();if(r.firstCreatePass){let o=Qe(e);Wc(n,r.data,r.blueprint,o,!0),Wc(t,r.data,r.blueprint,o,!1)}}function Wc(e,t,n,r,o){if(e=se(e),Array.isArray(e))for(let i=0;i<e.length;i++)Wc(e[i],t,n,r,o);else{let i=z(),s=_(),a=fe(),c=Fn(e)?e:se(e.provide),l=Vf(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Fn(e)||!e.multi){let f=new on(l,o,H),h=Ga(c,t,o?u:u+p,d);h===-1?(oc(Mi(a,s),i,c),za(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=Ga(c,t,u+p,d),h=Ga(c,t,u,u+p),g=f>=0&&n[f],v=h>=0&&n[h];if(o&&!v||!o&&!g){oc(Mi(a,s),i,c);let w=tI(o?eI:Jw,n.length,o,r,l);!o&&v&&(n[h].providerFactory=w),za(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(w),s.push(w)}else{let w=sh(n[o?h:f],l,!o&&r);za(i,e,f>-1?f:h,w)}!o&&r&&v&&n[h].componentProviders++}}}function za(e,t,n,r){let o=Fn(t),i=Fy(t);if(o||i){let c=(i?se(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function sh(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Ga(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Jw(e,t,n,r){return qc(this.multi,[])}function eI(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Ar(n,n[x],this.providerFactory.index,r);i=a.slice(0,s),qc(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],qc(o,i);return i}function qc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function tI(e,t,n,r,o){let i=new on(e,n,H);return i.multi=[],i.index=t,i.componentProviders=0,sh(i,o,r&&!n),i}function nI(e,t=[]){return n=>{n.providersResolver=(r,o)=>Xw(r,o?o(e):e,t)}}function fk(e,t,n){let r=ln()+e,o=_();return o[r]===Ee?Zr(o,r,n?t.call(n):t()):zE(o,r)}function pk(e,t,n,r){return ah(_(),ln(),e,t,n,r)}function mk(e,t,n,r,o){return ch(_(),ln(),e,t,n,r,o)}function ps(e,t){let n=e[t];return n===Ee?void 0:n}function ah(e,t,n,r,o,i){let s=t+n;return Se(e,s,o)?Zr(e,s+1,i?r.call(i,o):r(o)):ps(e,s+1)}function ch(e,t,n,r,o,i,s){let a=t+n;return Fr(e,a,o,i)?Zr(e,a+2,s?r.call(s,o,i):r(o,i)):ps(e,a+2)}function rI(e,t,n,r,o,i,s,a){let c=t+n;return GE(e,c,o,i,s)?Zr(e,c+3,a?r.call(a,o,i,s):r(o,i,s)):ps(e,c+3)}function oI(e,t,n,r,o,i,s,a,c){let l=t+n;return WE(e,l,o,i,s,a)?Zr(e,l+4,c?r.call(c,o,i,s,a):r(o,i,s,a)):ps(e,l+4)}function hk(e,t){let n=z(),r,o=e+Q;n.firstCreatePass?(r=iI(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=Qt(r.type,!0)),s,a=ye(H);try{let c=Ci(!1),l=i();return Ci(c),rh(n,_(),o,l),l}finally{ye(a)}}function iI(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function gk(e,t,n){let r=e+Q,o=_(),i=Hr(o,r);return ms(o,r)?ah(o,ln(),t,i.transform,n,i):i.transform(n)}function bk(e,t,n,r){let o=e+Q,i=_(),s=Hr(i,o);return ms(i,o)?ch(i,ln(),t,s.transform,n,r,s):s.transform(n,r)}function yk(e,t,n,r,o){let i=e+Q,s=_(),a=Hr(s,i);return ms(s,i)?rI(s,ln(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function vk(e,t,n,r,o,i){let s=e+Q,a=_(),c=Hr(a,s);return ms(a,s)?oI(a,ln(),t,c.transform,n,r,o,i,c):c.transform(n,r,o,i)}function ms(e,t){return e[x].data[t].pure}function Dk(e,t){return us(e,t)}var Lr=class{full;major;minor;patch;constructor(t){this.full=t;let n=t.split(".");this.major=n[0],this.minor=n[1],this.patch=n.slice(2).join(".")}},Xl=new Lr("19.2.9"),Zc=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},_k=(()=>{class e{compileModuleSync(n){return new kc(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Ff(n),i=Xp(o.declarations).reduce((s,a)=>{let c=Tt(a);return c&&s.push(new an(c)),s},[]);return new Zc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var sI=(()=>{class e{zone=m(O);changeDetectionScheduler=m(sn);applicationRef=m(Nt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),aI=new y("",{factory:()=>!1});function lh({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new O(ce(k({},uh()),{scheduleInRootZone:n})),[{provide:O,useFactory:e},{provide:xr,multi:!0,useFactory:()=>{let r=m(sI,{optional:!0});return()=>r.initialize()}},{provide:xr,multi:!0,useFactory:()=>{let r=m(cI);return()=>{r.initialize()}}},t===!0?{provide:Ep,useValue:!0}:[],{provide:wp,useValue:n??_p}]}function Ek(e){let t=e?.ignoreChangesOutsideZone,n=e?.scheduleInRootZone,r=lh({ngZoneFactory:()=>{let o=uh(e);return o.scheduleInRootZone=n,o.shouldCoalesceEventChangeDetection&&dn("NgZone_CoalesceEvent"),new O(o)},ignoreChangesOutsideZone:t,scheduleInRootZone:n});return Vr([{provide:aI,useValue:!0},{provide:es,useValue:!1},r])}function uh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var cI=(()=>{class e{subscription=new K;initialized=!1;zone=m(O);pendingTasks=m(Zn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{O.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{O.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var lI=(()=>{class e{appRef=m(Nt);taskService=m(Zn);ngZone=m(O);zonelessEnabled=m(es);tracing=m(Xn,{optional:!0});disableScheduling=m(Ep,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new K;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(xi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(m(wp,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof cc||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Ld:Ip;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(xi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Ld(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function uI(){return typeof $localize<"u"&&$localize.locale||ji}var nr=new y("",{providedIn:"root",factory:()=>m(nr,R.Optional|R.SkipSelf)||uI()}),dh=new y("",{providedIn:"root",factory:()=>Fw});var Yc=new y(""),dI=new y("");function Ir(e){return!e.moduleRef}function fI(e){let t=Ir(e)?e.r3Injector:e.moduleRef.injector,n=t.get(O);return n.run(()=>{Ir(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(et,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),Ir(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Yc);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Yc);s.add(i),e.moduleRef.onDestroy(()=>{fi(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return mI(r,n,()=>{let i=t.get(zm);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(nr,ji);if(Lw(s||ji),!t.get(dI,!0))return Ir(e)?t.get(Nt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ir(e)){let c=t.get(Nt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return pI(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function pI(e,t){let n=e.injector.get(Nt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new D(-403,!1);t.push(e)}function mI(e,t,n){try{let r=n();return fs(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var pi=null;function hI(e=[],t){return J.create({name:t,providers:[{provide:$i,useValue:"platform"},{provide:Yc,useValue:new Set([()=>pi=null])},...e]})}function gI(e=[]){if(pi)return pi;let t=hI(e);return pi=t,JE(),bI(t),t}function bI(e){let t=e.get(xl,null);Gi(e,()=>{t?.forEach(n=>n())})}var Jl=(()=>{class e{static __NG_ELEMENT_ID__=yI}return e})();function yI(e){return vI(fe(),_(),(e&16)===16)}function vI(e,t,n){if(Gn(e)&&!n){let r=Je(e.index,t);return new kr(r,r)}else if(e.type&175){let r=t[xe];return new kr(r,t)}return null}var Kc=class{constructor(){}supports(t){return Vm(t)}create(t){return new Qc(t)}},DI=(e,t)=>t,Qc=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||DI}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<mf(r,o,i)?n:r,a=mf(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let p=0;p<l;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;u<=h&&h<l&&(i[p]=f+1)}let d=s.previousIndex;i[d]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Vm(t))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,$E(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Xc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Bi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Bi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Xc=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Jc=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Bi=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Jc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function mf(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var el=class{constructor(){}supports(t){return t instanceof Map||Yl(t)}create(){return new tl}},tl=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Yl(t)))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new nl(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},nl=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(t){this.key=t}};function hf(){return new eu([new Kc])}var eu=(()=>{class e{factories;static \u0275prov=b({token:e,providedIn:"root",factory:hf});constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||hf()),deps:[[e,new Af,new sl]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new D(901,!1)}}return e})();function gf(){return new tu([new el])}var tu=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:gf});factories;constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||gf()),deps:[[e,new Af,new sl]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new D(901,!1)}}return e})();function fh(e){L(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=gI(r),i=[lh({}),{provide:sn,useExisting:lI},...n||[]],s=new Oi({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return fI({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{L(9)}}function ke(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function ph(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function nu(e){return Sa(e)}function wk(e,t){return ei(e,t?.equal)}var rl=class{[be];constructor(t){this[be]=t}destroy(){this[be].destroy()}};function ru(e,t){!t?.injector&&ll(ru);let n=t?.injector??m(J),r=t?.manualCleanup!==!0?n.get(Ji):null,o,i=n.get(Nl,null,{optional:!0}),s=n.get(sn);return i!==null&&!t?.forceRoot?(o=wI(i.view,s,e),r instanceof Ti&&r._lView===i.view&&(r=null)):o=II(e,n.get(Um),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new rl(o)}var mh=ce(k({},Tn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Rr,run(){if(this.dirty=!1,this.hasRun&&!Qo(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Dr(this),n=Ei(!1);try{this.maybeCleanup(),this.fn(e)}finally{Ei(n),Ko(this,t)}},maybeCleanup(){if(this.cleanupFns?.length)try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[]}}}),_I=ce(k({},mh),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){_r(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),EI=ce(k({},mh),{consumerMarkedDirty(){this.view[M]|=8192,qn(this.view),this.notifier.notify(13)},destroy(){_r(this),this.onDestroyFn(),this.maybeCleanup(),this.view[en]?.delete(this)}});function wI(e,t,n){let r=Object.create(EI);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[en]??=new Set,e[en].add(r),r.consumerMarkedDirty(r),r}function II(e,t,n){let r=Object.create(_I);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.schedule(r),r.notifier.notify(12),r}function hh(e,t){let n=Tt(e),r=t.elementInjector||zi();return new an(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector)}function Ik(e){let t=Tt(e);if(!t)return null;let n=new an(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var F=new y("");var yh=null;function vt(){return yh}function ou(e){yh??=e}var Kr=class{},Qr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>m(vh),providedIn:"platform"})}return e})(),CI=new y(""),vh=(()=>{class e extends Qr{_location;_history;_doc=m(F);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return vt().getBaseHref(this._doc)}onPopState(n){let r=vt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=vt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function hs(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function gh(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function $e(e){return e&&e[0]!=="?"?`?${e}`:e}var rr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:()=>m(Dh),providedIn:"root"})}return e})(),gs=new y(""),Dh=(()=>{class e extends rr{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??m(F).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return hs(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+$e(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(Qr),C(gs,8))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),_h=(()=>{class e{_subject=new B;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=xI(gh(bh(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+$e(r))}normalize(n){return e.stripTrailingSlash(TI(this._basePath,bh(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$e(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+$e(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=$e;static joinWithSlash=hs;static stripTrailingSlash=gh;static \u0275fac=function(r){return new(r||e)(C(rr))};static \u0275prov=b({token:e,factory:()=>MI(),providedIn:"root"})}return e})();function MI(){return new _h(C(rr))}function TI(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function bh(e){return e.replace(/\/index.html$/,"")}function xI(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var SI=(()=>{class e extends rr{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=hs(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i))||this._platformLocation.pathname;this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+$e(i))||this._platformLocation.pathname;this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(C(Qr),C(gs,8))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),xh={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},Ms=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(Ms||{});var pe=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(pe||{}),j=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(j||{}),Me=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Me||{}),ae={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Sh(e){return Ie(e)[Z.LocaleId]}function Nh(e,t,n){let r=Ie(e),o=[r[Z.DayPeriodsFormat],r[Z.DayPeriodsStandalone]],i=Fe(o,t);return Fe(i,n)}function Ah(e,t,n){let r=Ie(e),o=[r[Z.DaysFormat],r[Z.DaysStandalone]],i=Fe(o,t);return Fe(i,n)}function Rh(e,t,n){let r=Ie(e),o=[r[Z.MonthsFormat],r[Z.MonthsStandalone]],i=Fe(o,t);return Fe(i,n)}function Oh(e,t){let r=Ie(e)[Z.Eras];return Fe(r,t)}function Xr(e,t){let n=Ie(e);return Fe(n[Z.DateFormat],t)}function Jr(e,t){let n=Ie(e);return Fe(n[Z.TimeFormat],t)}function eo(e,t){let r=Ie(e)[Z.DateTimeFormat];return Fe(r,t)}function Ge(e,t){let n=Ie(e),r=n[Z.NumberSymbols][t];if(typeof r>"u"){if(t===ae.CurrencyDecimal)return n[Z.NumberSymbols][ae.Decimal];if(t===ae.CurrencyGroup)return n[Z.NumberSymbols][ae.Group]}return r}function mu(e,t){return Ie(e)[Z.NumberFormats][t]}function NI(e){return Ie(e)[Z.Currencies]}function kh(e){if(!e[Z.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[Z.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function Fh(e){let t=Ie(e);return kh(t),(t[Z.ExtraData][2]||[]).map(r=>typeof r=="string"?iu(r):[iu(r[0]),iu(r[1])])}function Ph(e,t,n){let r=Ie(e);kh(r);let o=[r[Z.ExtraData][0],r[Z.ExtraData][1]],i=Fe(o,t)||[];return Fe(i,n)||[]}function Fe(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function iu(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}function Lh(e,t,n="en"){let r=NI(n)[e]||xh[e]||[],o=r[1];return t==="narrow"&&typeof o=="string"?o:r[0]||e}var AI=2;function jh(e){let t,n=xh[e];return n&&(t=n[2]),typeof t=="number"?t:AI}var RI=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,bs={},OI=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function Bh(e,t,n,r){let o=UI(e);t=Dt(n,t)||t;let s=[],a;for(;t;)if(a=OI.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=Hh(r,c),o=HI(o,r));let l="";return s.forEach(u=>{let d=BI(u);l+=d?d(o,n,c):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function Es(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Dt(e,t){let n=Sh(e);if(bs[n]??={},bs[n][t])return bs[n][t];let r="";switch(t){case"shortDate":r=Xr(e,Me.Short);break;case"mediumDate":r=Xr(e,Me.Medium);break;case"longDate":r=Xr(e,Me.Long);break;case"fullDate":r=Xr(e,Me.Full);break;case"shortTime":r=Jr(e,Me.Short);break;case"mediumTime":r=Jr(e,Me.Medium);break;case"longTime":r=Jr(e,Me.Long);break;case"fullTime":r=Jr(e,Me.Full);break;case"short":let o=Dt(e,"shortTime"),i=Dt(e,"shortDate");r=ys(eo(e,Me.Short),[o,i]);break;case"medium":let s=Dt(e,"mediumTime"),a=Dt(e,"mediumDate");r=ys(eo(e,Me.Medium),[s,a]);break;case"long":let c=Dt(e,"longTime"),l=Dt(e,"longDate");r=ys(eo(e,Me.Long),[c,l]);break;case"full":let u=Dt(e,"fullTime"),d=Dt(e,"fullDate");r=ys(eo(e,Me.Full),[u,d]);break}return r&&(bs[n][t]=r),r}function ys(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function ze(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function kI(e,t){return ze(e,3).substring(0,t)}function te(e,t,n=0,r=!1,o=!1){return function(i,s){let a=FI(e,i);if((n>0||a>-n)&&(a+=n),e===3)a===0&&n===-12&&(a=12);else if(e===6)return kI(a,t);let c=Ge(s,ae.MinusSign);return ze(a,t,c,r,o)}}function FI(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function U(e,t,n=pe.Format,r=!1){return function(o,i){return PI(o,i,e,t,n,r)}}function PI(e,t,n,r,o,i){switch(n){case 2:return Rh(t,o,r)[e.getMonth()];case 1:return Ah(t,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let l=Fh(t),u=Ph(t,o,r),d=l.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,g=s>=f.hours&&a>=f.minutes,v=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(g&&v)return!0}else if(g||v)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return Nh(t,o,r)[s<12?0:1];case 3:return Oh(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function vs(e){return function(t,n,r){let o=-1*r,i=Ge(n,ae.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+ze(s,2,i)+ze(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+ze(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+ze(s,2,i)+":"+ze(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+ze(s,2,i)+":"+ze(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var LI=0,_s=4;function jI(e){let t=Es(e,LI,1).getDay();return Es(e,0,1+(t<=_s?_s:_s+7)-t)}function Vh(e){let t=e.getDay(),n=t===0?-3:_s-t;return Es(e.getFullYear(),e.getMonth(),e.getDate()+n)}function su(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Vh(n),s=jI(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return ze(o,e,Ge(r,ae.MinusSign))}}function Ds(e,t=!1){return function(n,r){let i=Vh(n).getFullYear();return ze(i,e,Ge(r,ae.MinusSign),t)}}var au={};function BI(e){if(au[e])return au[e];let t;switch(e){case"G":case"GG":case"GGG":t=U(3,j.Abbreviated);break;case"GGGG":t=U(3,j.Wide);break;case"GGGGG":t=U(3,j.Narrow);break;case"y":t=te(0,1,0,!1,!0);break;case"yy":t=te(0,2,0,!0,!0);break;case"yyy":t=te(0,3,0,!1,!0);break;case"yyyy":t=te(0,4,0,!1,!0);break;case"Y":t=Ds(1);break;case"YY":t=Ds(2,!0);break;case"YYY":t=Ds(3);break;case"YYYY":t=Ds(4);break;case"M":case"L":t=te(1,1,1);break;case"MM":case"LL":t=te(1,2,1);break;case"MMM":t=U(2,j.Abbreviated);break;case"MMMM":t=U(2,j.Wide);break;case"MMMMM":t=U(2,j.Narrow);break;case"LLL":t=U(2,j.Abbreviated,pe.Standalone);break;case"LLLL":t=U(2,j.Wide,pe.Standalone);break;case"LLLLL":t=U(2,j.Narrow,pe.Standalone);break;case"w":t=su(1);break;case"ww":t=su(2);break;case"W":t=su(1,!0);break;case"d":t=te(2,1);break;case"dd":t=te(2,2);break;case"c":case"cc":t=te(7,1);break;case"ccc":t=U(1,j.Abbreviated,pe.Standalone);break;case"cccc":t=U(1,j.Wide,pe.Standalone);break;case"ccccc":t=U(1,j.Narrow,pe.Standalone);break;case"cccccc":t=U(1,j.Short,pe.Standalone);break;case"E":case"EE":case"EEE":t=U(1,j.Abbreviated);break;case"EEEE":t=U(1,j.Wide);break;case"EEEEE":t=U(1,j.Narrow);break;case"EEEEEE":t=U(1,j.Short);break;case"a":case"aa":case"aaa":t=U(0,j.Abbreviated);break;case"aaaa":t=U(0,j.Wide);break;case"aaaaa":t=U(0,j.Narrow);break;case"b":case"bb":case"bbb":t=U(0,j.Abbreviated,pe.Standalone,!0);break;case"bbbb":t=U(0,j.Wide,pe.Standalone,!0);break;case"bbbbb":t=U(0,j.Narrow,pe.Standalone,!0);break;case"B":case"BB":case"BBB":t=U(0,j.Abbreviated,pe.Format,!0);break;case"BBBB":t=U(0,j.Wide,pe.Format,!0);break;case"BBBBB":t=U(0,j.Narrow,pe.Format,!0);break;case"h":t=te(3,1,-12);break;case"hh":t=te(3,2,-12);break;case"H":t=te(3,1);break;case"HH":t=te(3,2);break;case"m":t=te(4,1);break;case"mm":t=te(4,2);break;case"s":t=te(5,1);break;case"ss":t=te(5,2);break;case"S":t=te(6,1);break;case"SS":t=te(6,2);break;case"SSS":t=te(6,3);break;case"Z":case"ZZ":case"ZZZ":t=vs(0);break;case"ZZZZZ":t=vs(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=vs(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=vs(2);break;default:return null}return au[e]=t,t}function Hh(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function VI(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function HI(e,t,n){let o=e.getTimezoneOffset(),i=Hh(t,o);return VI(e,-1*(i-o))}function UI(e){if(Eh(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Es(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(RI))return $I(r)}let t=new Date(e);if(!Eh(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function $I(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,l),t}function Eh(e){return e instanceof Date&&!isNaN(e.valueOf())}var zI=/^(\d+)?\.((\d+)(-(\d+))?)?$/,wh=22,ws=".",to="0",GI=";",WI=",",cu="#",Ih="\xA4";function Uh(e,t,n,r,o,i,s=!1){let a="",c=!1;if(!isFinite(e))a=Ge(n,ae.Infinity);else{let l=ZI(e);s&&(l=qI(l));let u=t.minInt,d=t.minFrac,p=t.maxFrac;if(i){let $=i.match(zI);if($===null)throw new Error(`${i} is not a valid digit info`);let Y=$[1],jt=$[3],Do=$[5];Y!=null&&(u=lu(Y)),jt!=null&&(d=lu(jt)),Do!=null?p=lu(Do):jt!=null&&d>p&&(p=d)}YI(l,d,p);let f=l.digits,h=l.integerLen,g=l.exponent,v=[];for(c=f.every($=>!$);h<u;h++)f.unshift(0);for(;h<0;h++)f.unshift(0);h>0?v=f.splice(h,f.length):(v=f,f=[0]);let w=[];for(f.length>=t.lgSize&&w.unshift(f.splice(-t.lgSize,f.length).join(""));f.length>t.gSize;)w.unshift(f.splice(-t.gSize,f.length).join(""));f.length&&w.unshift(f.join("")),a=w.join(Ge(n,r)),v.length&&(a+=Ge(n,o)+v.join("")),g&&(a+=Ge(n,ae.Exponential)+"+"+g)}return e<0&&!c?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function $h(e,t,n,r,o){let i=mu(t,Ms.Currency),s=Gh(i,Ge(t,ae.MinusSign));return s.minFrac=jh(r),s.maxFrac=s.minFrac,Uh(e,s,t,ae.CurrencyGroup,ae.CurrencyDecimal,o).replace(Ih,n).replace(Ih,"").trim()}function zh(e,t,n){let r=mu(t,Ms.Decimal),o=Gh(r,Ge(t,ae.MinusSign));return Uh(e,o,t,ae.Group,ae.Decimal,n)}function Gh(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(GI),o=r[0],i=r[1],s=o.indexOf(ws)!==-1?o.split(ws):[o.substring(0,o.lastIndexOf(to)+1),o.substring(o.lastIndexOf(to)+1)],a=s[0],c=s[1]||"";n.posPre=a.substring(0,a.indexOf(cu));for(let u=0;u<c.length;u++){let d=c.charAt(u);d===to?n.minFrac=n.maxFrac=u+1:d===cu?n.maxFrac=u+1:n.posSuf+=d}let l=a.split(WI);if(n.gSize=l[1]?l[1].length:0,n.lgSize=l[2]||l[1]?(l[2]||l[1]).length:0,i){let u=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(cu);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+u).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function qI(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function ZI(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(ws))>-1&&(t=t.replace(ws,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===to;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===to;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>wh&&(r=r.splice(0,wh-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function YI(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let c=i!==0,l=t+e.integerLen,u=r.reduceRight(function(d,p,f,h){return p=p+d,h[f]=p<10?p:p-10,c&&(h[f]===0&&f>=l?h.pop():c=!1),p>=10?1:0},0);u&&(r.unshift(u),e.integerLen++)}function lu(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}var uu=/\s+/,Ch=[],KI=(()=>{class e{_ngEl;_renderer;initialClasses=Ch;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(uu):Ch}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(uu):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(uu).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(H(ee),H(qr))};static \u0275dir=q({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var Is=class{$implicit;ngForOf;index;count;constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Wh=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Is(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Mh(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Mh(i,o)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(kt),H(St),H(eu))};static \u0275dir=q({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Mh(e,t){e.context.$implicit=t.item}var QI=(()=>{class e{_viewContainer;_context=new Cs;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Th(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Th(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(H(kt),H(St))};static \u0275dir=q({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Cs=class{$implicit=null;ngIf=null};function Th(e,t){if(e&&!e.createEmbeddedView)throw new D(2020,!1)}var du=class{_viewContainerRef;_templateRef;_created=!1;constructor(t,n){this._viewContainerRef=t,this._templateRef=n}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(t){t&&!this._created?this.create():!t&&this._created&&this.destroy()}},qh=(()=>{class e{_defaultViews=[];_defaultUsed=!1;_caseCount=0;_lastCaseCheckIndex=0;_lastCasesMatched=!1;_ngSwitch;set ngSwitch(n){this._ngSwitch=n,this._caseCount===0&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(n){this._defaultViews.push(n)}_matchCase(n){let r=n===this._ngSwitch;return this._lastCasesMatched||=r,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),r}_updateDefaultCases(n){if(this._defaultViews.length>0&&n!==this._defaultUsed){this._defaultUsed=n;for(let r of this._defaultViews)r.enforceState(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"}})}return e})(),XI=(()=>{class e{ngSwitch;_view;ngSwitchCase;constructor(n,r,o){this.ngSwitch=o,o._addCase(),this._view=new du(n,r)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}static \u0275fac=function(r){return new(r||e)(H(kt),H(St),H(qh,9))};static \u0275dir=q({type:e,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"}})}return e})();var JI=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(n,r,o){this._ngEl=n,this._differs=r,this._renderer=o}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,r){let[o,i]=n.split("."),s=o.indexOf("-")===-1?void 0:nt.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(n){n.forEachRemovedItem(r=>this._setStyle(r.key,null)),n.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),n.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static \u0275fac=function(r){return new(r||e)(H(ee),H(tu),H(qr))};static \u0275dir=q({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})(),eC=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(n){this._viewContainerRef=n}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(H(kt))};static \u0275dir=q({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Zi]})}return e})();function no(e,t){return new D(2100,!1)}var fu=class{createSubscription(t,n){return nu(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){nu(()=>t.unsubscribe())}},pu=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},tC=new pu,nC=new fu,rC=(()=>{class e{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;constructor(n){this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(fs(n))return tC;if(Kl(n))return nC;throw no(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(r){return new(r||e)(H(Jl,16))};static \u0275pipe=er({name:"async",type:e,pure:!1})}return e})();var oC="mediumDate",Zh=new y(""),Yh=new y(""),iC=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??oC,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Bh(n,s,i||this.locale,a)}catch(s){throw no(e,s.message)}}static \u0275fac=function(r){return new(r||e)(H(nr,16),H(Zh,24),H(Yh,24))};static \u0275pipe=er({name:"date",type:e,pure:!0})}return e})();var sC=(()=>{class e{_locale;constructor(n){this._locale=n}transform(n,r,o){if(!Kh(n))return null;o||=this._locale;try{let i=Qh(n);return zh(i,o,r)}catch(i){throw no(e,i.message)}}static \u0275fac=function(r){return new(r||e)(H(nr,16))};static \u0275pipe=er({name:"number",type:e,pure:!0})}return e})();var aC=(()=>{class e{_locale;_defaultCurrencyCode;constructor(n,r="USD"){this._locale=n,this._defaultCurrencyCode=r}transform(n,r=this._defaultCurrencyCode,o="symbol",i,s){if(!Kh(n))return null;s||=this._locale,typeof o=="boolean"&&(o=o?"symbol":"code");let a=r||this._defaultCurrencyCode;o!=="code"&&(o==="symbol"||o==="symbol-narrow"?a=Lh(a,o==="symbol"?"wide":"narrow",s):a=o);try{let c=Qh(n);return $h(c,s,a,r,i)}catch(c){throw no(e,c.message)}}static \u0275fac=function(r){return new(r||e)(H(nr,16),H(dh,16))};static \u0275pipe=er({name:"currency",type:e,pure:!0})}return e})();function Kh(e){return!(e==null||e===""||e!==e)}function Qh(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var cC=(()=>{class e{transform(n,r,o){if(n==null)return null;if(!(typeof n=="string"||Array.isArray(n)))throw no(e,n);return n.slice(r,o)}static \u0275fac=function(r){return new(r||e)};static \u0275pipe=er({name:"slice",type:e,pure:!1})}return e})();var Xh=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({})}return e})();function ro(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Ts="browser",Jh="server";function hu(e){return e===Ts}function xs(e){return e===Jh}var hn=class{};var kF=(()=>{class e{static \u0275prov=b({token:e,providedIn:"root",factory:()=>new gu(m(F),window)})}return e})(),gu=class{document;window;offset=()=>[0,0];constructor(t,n){this.document=t,this.window=n}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=lC(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function lC(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var As=new y(""),Du=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new D(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(C(As),C(O))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),oo=class{_doc;constructor(t){this._doc=t}manager},Ss="ng-app-id";function eg(e){for(let t of e)t.remove()}function tg(e,t){let n=t.createElement("style");return n.textContent=e,n}function uC(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Ss}="${t}"],link[${Ss}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Ss),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function yu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var _u=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=xs(i),uC(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,tg);r?.forEach(o=>this.addUsage(o,this.external,yu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(eg(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])eg(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,tg(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,yu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(Ss,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(C(F),C(Rt),C($r,8),C(it))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),bu={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Eu=/%COMP%/g;var rg="%COMP%",dC=`_nghost-${rg}`,fC=`_ngcontent-${rg}`,pC=!0,mC=new y("",{providedIn:"root",factory:()=>pC});function hC(e){return fC.replace(Eu,e)}function gC(e){return dC.replace(Eu,e)}function og(e,t){return t.map(n=>n.replace(Eu,e))}var wu=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=xs(a),this.defaultRenderer=new io(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===tt.ShadowDom&&(r=ce(k({},r),{encapsulation:tt.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Ns?o.applyToHost(n):o instanceof so&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case tt.Emulated:i=new Ns(c,l,r,this.appId,u,s,a,d,p);break;case tt.ShadowDom:return new vu(c,l,n,r,s,a,this.nonce,d,p);default:i=new so(c,l,r,u,s,a,d,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(C(Du),C(_u),C(Rt),C(mC),C(F),C(it),C(O),C($r),C(Xn,8))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),io=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(bu[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(ng(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(ng(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new D(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=bu[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=bu[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(nt.DashCase|nt.Important)?t.style.setProperty(n,r,o&nt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&nt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=vt().getGlobalEventTarget(this.doc,t),!t))throw new D(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function ng(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var vu=class extends io{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=og(o.id,u);for(let p of u){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let p of d){let f=yu(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},so=class extends io{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?og(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ns=class extends so{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=hC(u),this.hostAttr=gC(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var Rs=class e extends Kr{supportsDOMEvents=!0;static makeCurrent(){ou(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=bC();return n==null?null:yC(n)}resetBaseElement(){ao=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return ro(document.cookie,t)}},ao=null;function bC(){return ao=ao||document.querySelector("base"),ao?ao.getAttribute("href"):null}function yC(e){return new URL(e,document.baseURI).pathname}var vC=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),sg=(()=>{class e extends oo{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),ig=["alt","control","meta","shift"],DC={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},_C={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},ag=(()=>{class e extends oo{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>vt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),ig.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=DC[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),ig.forEach(s=>{if(s!==o){let a=_C[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();function EC(e,t){return fh(k({rootComponent:e},wC(t)))}function wC(e){return{appProviders:[...xC,...e?.providers??[]],platformProviders:TC}}function IC(){Rs.makeCurrent()}function CC(){return new et}function MC(){return kp(document),document}var TC=[{provide:it,useValue:Ts},{provide:xl,useValue:IC,multi:!0},{provide:F,useFactory:MC}];var xC=[{provide:$i,useValue:"root"},{provide:et,useFactory:CC},{provide:As,useClass:sg,multi:!0,deps:[F]},{provide:As,useClass:ag,multi:!0,deps:[F]},wu,_u,Du,{provide:rt,useExisting:wu},{provide:hn,useClass:vC},[]];var ir=class{},co=class{},Pt=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var ks=class{encodeKey(t){return cg(t)}encodeValue(t){return cg(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function SC(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var NC=/%(\d[a-f0-9])/gi,AC={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function cg(e){return encodeURIComponent(e).replace(NC,(t,n)=>AC[n]??t)}function Os(e){return`${e}`}var _t=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new ks,t.fromString){if(t.fromObject)throw new D(2805,!1);this.map=SC(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(Os):[Os(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Os(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(Os(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Fs=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function RC(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function lg(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function ug(e){return typeof Blob<"u"&&e instanceof Blob}function dg(e){return typeof FormData<"u"&&e instanceof FormData}function OC(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var fg="Content-Type",pg="Accept",mg="X-Request-URL",hg="text/plain",gg="application/json",kC=`${gg}, ${hg}, */*`,or=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(RC(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Pt,this.context??=new Fs,!this.params)this.params=new _t,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||lg(this.body)||ug(this.body)||dg(this.body)||OC(this.body)?this.body:this.body instanceof _t?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||dg(this.body)?null:ug(this.body)?this.body.type||null:lg(this.body)?null:typeof this.body=="string"?hg:this.body instanceof _t?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?gg:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((p,f)=>p.set(f,t.setHeaders[f]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((p,f)=>p.set(f,t.setParams[f]),u)),new e(n,r,s,{params:u,headers:l,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},gn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(gn||{}),sr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new Pt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Ps=class e extends sr{constructor(t={}){super(t)}type=gn.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},lo=class e extends sr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=gn.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},uo=class extends sr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},FC=200,PC=204;function Iu(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var bg=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof or)i=n;else{let c;o.headers instanceof Pt?c=o.headers:c=new Pt(o.headers);let l;o.params&&(o.params instanceof _t?l=o.params:l=new _t({fromObject:o.params})),i=new or(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=In(i).pipe(ca(c=>this.handler.handle(c)));if(n instanceof or||o.observe==="events")return s;let a=s.pipe(Te(c=>c instanceof lo));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(X(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new D(2806,!1);return c.body}));case"blob":return a.pipe(X(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new D(2807,!1);return c.body}));case"text":return a.pipe(X(c=>{if(c.body!==null&&typeof c.body!="string")throw new D(2808,!1);return c.body}));case"json":default:return a.pipe(X(c=>c.body))}case"response":return a;default:throw new D(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new _t().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Iu(o,r))}post(n,r,o={}){return this.request("POST",n,Iu(o,r))}put(n,r,o={}){return this.request("PUT",n,Iu(o,r))}static \u0275fac=function(r){return new(r||e)(C(ir))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();var LC=new y("");function jC(e,t){return t(e)}function BC(e,t,n){return(r,o)=>Gi(n,()=>t(r,i=>e(i,o)))}var yg=new y(""),vg=new y(""),Dg=new y("",{providedIn:"root",factory:()=>!0});var Ls=(()=>{class e extends ir{backend;injector;chain=null;pendingTasks=m(Zn);contributeToStability=m(Dg);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(yg),...this.injector.get(vg,[])]));this.chain=r.reduceRight((o,i)=>BC(o,i,this.injector),jC)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(ua(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(C(co),C(Oe))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();var VC=/^\)\]\}',?\n/,HC=RegExp(`^${mg}:`,"m");function UC(e){return"responseURL"in e&&e.responseURL?e.responseURL:HC.test(e.getAllResponseHeaders())?e.getResponseHeader(mg):null}var Cu=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new D(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?Ae(r.\u0275loadImpl()):In(null)).pipe(ha(()=>new N(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,v)=>s.setRequestHeader(g,v.join(","))),n.headers.has(pg)||s.setRequestHeader(pg,kC),!n.headers.has(fg)){let g=n.detectContentTypeHeader();g!==null&&s.setRequestHeader(fg,g)}if(n.responseType){let g=n.responseType.toLowerCase();s.responseType=g!=="json"?g:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let g=s.statusText||"OK",v=new Pt(s.getAllResponseHeaders()),w=UC(s)||n.url;return c=new Ps({headers:v,status:s.status,statusText:g,url:w}),c},u=()=>{let{headers:g,status:v,statusText:w,url:$}=l(),Y=null;v!==PC&&(Y=typeof s.response>"u"?s.responseText:s.response),v===0&&(v=Y?FC:0);let jt=v>=200&&v<300;if(n.responseType==="json"&&typeof Y=="string"){let Do=Y;Y=Y.replace(VC,"");try{Y=Y!==""?JSON.parse(Y):null}catch(lb){Y=Do,jt&&(jt=!1,Y={error:lb,text:Y})}}jt?(i.next(new lo({body:Y,headers:g,status:v,statusText:w,url:$||void 0})),i.complete()):i.error(new uo({error:Y,headers:g,status:v,statusText:w,url:$||void 0}))},d=g=>{let{url:v}=l(),w=new uo({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:v||void 0});i.error(w)},p=!1,f=g=>{p||(i.next(l()),p=!0);let v={type:gn.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),n.responseType==="text"&&s.responseText&&(v.partialText=s.responseText),i.next(v)},h=g=>{let v={type:gn.UploadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),i.next(v)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",h)),s.send(a),i.next({type:gn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",h)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(C(hn))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})(),_g=new y(""),$C="XSRF-TOKEN",zC=new y("",{providedIn:"root",factory:()=>$C}),GC="X-XSRF-TOKEN",WC=new y("",{providedIn:"root",factory:()=>GC}),fo=class{},qC=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=ro(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(C(F),C(it),C(zC))};static \u0275prov=b({token:e,factory:e.\u0275fac})}return e})();function ZC(e,t){let n=e.url.toLowerCase();if(!m(_g)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=m(fo).getToken(),o=m(WC);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}function YC(...e){let t=[bg,Cu,Ls,{provide:ir,useExisting:Ls},{provide:co,useFactory:()=>m(LC,{optional:!0})??m(Cu)},{provide:yg,useValue:ZC,multi:!0},{provide:_g,useValue:!0},{provide:fo,useClass:qC}];for(let n of e)t.push(...n.\u0275providers);return Vr(t)}var i1=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var KC=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:function(r){let o=null;return r?o=new(r||e):o=C(QC),o},providedIn:"root"})}return e})(),QC=(()=>{class e extends KC{_doc;constructor(n){super(),this._doc=n}sanitize(n,r){if(r==null)return null;switch(n){case st.NONE:return r;case st.HTML:return Ot(r,"HTML")?Ue(r):Kp(this._doc,String(r)).toString();case st.STYLE:return Ot(r,"Style")?Ue(r):r;case st.SCRIPT:if(Ot(r,"Script"))return Ue(r);throw new D(5200,!1);case st.URL:return Ot(r,"URL")?Ue(r):rs(String(r));case st.RESOURCE_URL:if(Ot(r,"ResourceURL"))return Ue(r);throw new D(5201,!1);default:throw new D(5202,!1)}}bypassSecurityTrustHtml(n){return Vp(n)}bypassSecurityTrustStyle(n){return Hp(n)}bypassSecurityTrustScript(n){return Up(n)}bypassSecurityTrustUrl(n){return $p(n)}bypassSecurityTrustResourceUrl(n){return zp(n)}static \u0275fac=function(r){return new(r||e)(C(F))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function po(e){return e.buttons===0||e.detail===0}function mo(e){let t=e.touches&&e.touches[0]||e.changedTouches&&e.changedTouches[0];return!!t&&t.identifier===-1&&(t.radiusX==null||t.radiusX===1)&&(t.radiusY==null||t.radiusY===1)}var Mu;function Eg(){if(Mu==null){let e=typeof document<"u"?document.head:null;Mu=!!(e&&(e.createShadowRoot||e.attachShadow))}return Mu}function Tu(e){if(Eg()){let t=e.getRootNode?e.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&t instanceof ShadowRoot)return t}return null}function xu(){let e=typeof document<"u"&&document?document.activeElement:null;for(;e&&e.shadowRoot;){let t=e.shadowRoot.activeElement;if(t===e)break;e=t}return e}function We(e){return e.composedPath?e.composedPath()[0]:e.target}function bn(e,t,n,r,o){let i=parseInt(Xl.major),s=parseInt(Xl.minor);return i>19||i===19&&s>0||i===0&&s===0?e.listen(t,n,r,o):(t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)})}var Su;try{Su=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Su=!1}var me=(()=>{class e{_platformId=m(it);isBrowser=this._platformId?hu(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||Su)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var ho;function wg(){if(ho==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>ho=!0}))}finally{ho=ho||!1}return ho}function ar(e){return wg()?e:!!e.capture}function Nu(e,t=0){return Ig(e)?Number(e):arguments.length===2?t:0}function Ig(e){return!isNaN(parseFloat(e))&&!isNaN(Number(e))}function at(e){return e instanceof ee?e.nativeElement:e}var Cg=new y("cdk-input-modality-detector-options"),Mg={ignoreKeys:[18,17,224,91,16]},Tg=650,Au={passive:!0,capture:!0},xg=(()=>{class e{_platform=m(me);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new $t(null);_options;_lastTouchMs=0;_onKeydown=n=>{this._options?.ignoreKeys?.some(r=>r===n.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=We(n))};_onMousedown=n=>{Date.now()-this._lastTouchMs<Tg||(this._modality.next(po(n)?"keyboard":"mouse"),this._mostRecentTarget=We(n))};_onTouchstart=n=>{if(mo(n)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=We(n)};constructor(){let n=m(O),r=m(F),o=m(Cg,{optional:!0});if(this._options=k(k({},Mg),o),this.modalityDetected=this._modality.pipe(br(1)),this.modalityChanged=this.modalityDetected.pipe(la()),this._platform.isBrowser){let i=m(rt).createRenderer(null,null);this._listenerCleanups=n.runOutsideAngular(()=>[bn(i,r,"keydown",this._onKeydown,Au),bn(i,r,"mousedown",this._onMousedown,Au),bn(i,r,"touchstart",this._onTouchstart,Au)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(n=>n())}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),go=function(e){return e[e.IMMEDIATE=0]="IMMEDIATE",e[e.EVENTUAL=1]="EVENTUAL",e}(go||{}),Sg=new y("cdk-focus-monitor-default-options"),js=ar({passive:!0,capture:!0}),Bs=(()=>{class e{_ngZone=m(O);_platform=m(me);_inputModalityDetector=m(xg);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=m(F,{optional:!0});_stopInputModalityDetector=new B;constructor(){let n=m(Sg,{optional:!0});this._detectionMode=n?.detectionMode||go.IMMEDIATE}_rootNodeFocusAndBlurListener=n=>{let r=We(n);for(let o=r;o;o=o.parentElement)n.type==="focus"?this._onFocus(n,o):this._onBlur(n,o)};monitor(n,r=!1){let o=at(n);if(!this._platform.isBrowser||o.nodeType!==1)return In();let i=Tu(o)||this._getDocument(),s=this._elementInfo.get(o);if(s)return r&&(s.checkChildren=!0),s.subject;let a={checkChildren:r,subject:new B,rootNode:i};return this._elementInfo.set(o,a),this._registerGlobalListeners(a),a.subject}stopMonitoring(n){let r=at(n),o=this._elementInfo.get(r);o&&(o.subject.complete(),this._setClasses(r),this._elementInfo.delete(r),this._removeGlobalListeners(o))}focusVia(n,r,o){let i=at(n),s=this._getDocument().activeElement;i===s?this._getClosestElementsInfo(i).forEach(([a,c])=>this._originChanged(a,r,c)):(this._setOrigin(r),typeof i.focus=="function"&&i.focus(o))}ngOnDestroy(){this._elementInfo.forEach((n,r)=>this.stopMonitoring(r))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(n){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(n)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:n&&this._isLastInteractionFromInputLabel(n)?"mouse":"program"}_shouldBeAttributedToTouch(n){return this._detectionMode===go.EVENTUAL||!!n?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(n,r){n.classList.toggle("cdk-focused",!!r),n.classList.toggle("cdk-touch-focused",r==="touch"),n.classList.toggle("cdk-keyboard-focused",r==="keyboard"),n.classList.toggle("cdk-mouse-focused",r==="mouse"),n.classList.toggle("cdk-program-focused",r==="program")}_setOrigin(n,r=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=n,this._originFromTouchInteraction=n==="touch"&&r,this._detectionMode===go.IMMEDIATE){clearTimeout(this._originTimeoutId);let o=this._originFromTouchInteraction?Tg:1;this._originTimeoutId=setTimeout(()=>this._origin=null,o)}})}_onFocus(n,r){let o=this._elementInfo.get(r),i=We(n);!o||!o.checkChildren&&r!==i||this._originChanged(r,this._getFocusOrigin(i),o)}_onBlur(n,r){let o=this._elementInfo.get(r);!o||o.checkChildren&&n.relatedTarget instanceof Node&&r.contains(n.relatedTarget)||(this._setClasses(r),this._emitOrigin(o,null))}_emitOrigin(n,r){n.subject.observers.length&&this._ngZone.run(()=>n.subject.next(r))}_registerGlobalListeners(n){if(!this._platform.isBrowser)return;let r=n.rootNode,o=this._rootNodeFocusListenerCount.get(r)||0;o||this._ngZone.runOutsideAngular(()=>{r.addEventListener("focus",this._rootNodeFocusAndBlurListener,js),r.addEventListener("blur",this._rootNodeFocusAndBlurListener,js)}),this._rootNodeFocusListenerCount.set(r,o+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Mn(this._stopInputModalityDetector)).subscribe(i=>{this._setOrigin(i,!0)}))}_removeGlobalListeners(n){let r=n.rootNode;if(this._rootNodeFocusListenerCount.has(r)){let o=this._rootNodeFocusListenerCount.get(r);o>1?this._rootNodeFocusListenerCount.set(r,o-1):(r.removeEventListener("focus",this._rootNodeFocusAndBlurListener,js),r.removeEventListener("blur",this._rootNodeFocusAndBlurListener,js),this._rootNodeFocusListenerCount.delete(r))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(n,r,o){this._setClasses(n,r),this._emitOrigin(o,r),this._lastFocusOrigin=r}_getClosestElementsInfo(n){let r=[];return this._elementInfo.forEach((o,i)=>{(i===n||o.checkChildren&&i.contains(n))&&r.push([i,o])}),r}_isLastInteractionFromInputLabel(n){let{_mostRecentTarget:r,mostRecentModality:o}=this._inputModalityDetector;if(o!=="mouse"||!r||r===n||n.nodeName!=="INPUT"&&n.nodeName!=="TEXTAREA"||n.disabled)return!1;let i=n.labels;if(i){for(let s=0;s<i.length;s++)if(i[s].contains(r))return!0}return!1}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),XC=(()=>{class e{_elementRef=m(ee);_focusMonitor=m(Bs);_monitorSubscription;_focusOrigin=null;cdkFocusChange=new ve;constructor(){}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let n=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(n,n.nodeType===1&&n.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(r=>{this._focusOrigin=r,this.cdkFocusChange.emit(r)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"]})}return e})();var Vs=new WeakMap,Et=(()=>{class e{_appRef;_injector=m(J);_environmentInjector=m(Oe);load(n){let r=this._appRef=this._appRef||this._injector.get(Nt),o=Vs.get(r);o||(o={loaders:new Set,refs:[]},Vs.set(r,o),r.onDestroy(()=>{Vs.get(r)?.refs.forEach(i=>i.destroy()),Vs.delete(r)})),o.loaders.has(n)||(o.loaders.add(n),o.refs.push(hh(n,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Hs=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(r,o){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return e})();function Ru(e){return Array.isArray(e)?e:[e]}var Ng=new Set,yn,JC=(()=>{class e{_platform=m(me);_nonce=m($r,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):t0}matchMedia(n){return(this._platform.WEBKIT||this._platform.BLINK)&&e0(n,this._nonce),this._matchMedia(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function e0(e,t){if(!Ng.has(e))try{yn||(yn=document.createElement("style"),t&&yn.setAttribute("nonce",t),yn.setAttribute("type","text/css"),document.head.appendChild(yn)),yn.sheet&&(yn.sheet.insertRule(`@media ${e} {body{ }}`,0),Ng.add(e))}catch(n){console.error(n)}}function t0(e){return{matches:e==="all"||e==="",media:e,addListener:()=>{},removeListener:()=>{}}}var Rg=(()=>{class e{_mediaMatcher=m(JC);_zone=m(O);_queries=new Map;_destroySubject=new B;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(n){return Ag(Ru(n)).some(o=>this._registerQuery(o).mql.matches)}observe(n){let o=Ag(Ru(n)).map(s=>this._registerQuery(s).observable),i=aa(o);return i=Cn(i.pipe(gr(1)),i.pipe(br(1),qt(0))),i.pipe(X(s=>{let a={matches:!1,breakpoints:{}};return s.forEach(({matches:c,query:l})=>{a.matches=a.matches||c,a.breakpoints[l]=c}),a}))}_registerQuery(n){if(this._queries.has(n))return this._queries.get(n);let r=this._mediaMatcher.matchMedia(n),i={observable:new N(s=>{let a=c=>this._zone.run(()=>s.next(c));return r.addListener(a),()=>{r.removeListener(a)}}).pipe(ma(r),X(({matches:s})=>({query:n,matches:s})),Mn(this._destroySubject)),mql:r};return this._queries.set(n,i),i}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ag(e){return e.map(t=>t.split(",")).reduce((t,n)=>t.concat(n)).map(t=>t.trim())}function n0(e){if(e.type==="characterData"&&e.target instanceof Comment)return!0;if(e.type==="childList"){for(let t=0;t<e.addedNodes.length;t++)if(!(e.addedNodes[t]instanceof Comment))return!1;for(let t=0;t<e.removedNodes.length;t++)if(!(e.removedNodes[t]instanceof Comment))return!1;return!0}return!1}var Og=(()=>{class e{create(n){return typeof MutationObserver>"u"?null:new MutationObserver(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kg=(()=>{class e{_mutationObserverFactory=m(Og);_observedElements=new Map;_ngZone=m(O);constructor(){}ngOnDestroy(){this._observedElements.forEach((n,r)=>this._cleanupObserver(r))}observe(n){let r=at(n);return new N(o=>{let s=this._observeElement(r).pipe(X(a=>a.filter(c=>!n0(c))),Te(a=>!!a.length)).subscribe(a=>{this._ngZone.run(()=>{o.next(a)})});return()=>{s.unsubscribe(),this._unobserveElement(r)}})}_observeElement(n){return this._ngZone.runOutsideAngular(()=>{if(this._observedElements.has(n))this._observedElements.get(n).count++;else{let r=new B,o=this._mutationObserverFactory.create(i=>r.next(i));o&&o.observe(n,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(n,{observer:o,stream:r,count:1})}return this._observedElements.get(n).stream})}_unobserveElement(n){this._observedElements.has(n)&&(this._observedElements.get(n).count--,this._observedElements.get(n).count||this._cleanupObserver(n))}_cleanupObserver(n){if(this._observedElements.has(n)){let{observer:r,stream:o}=this._observedElements.get(n);r&&r.disconnect(),o.complete(),this._observedElements.delete(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),aL=(()=>{class e{_contentObserver=m(kg);_elementRef=m(ee);event=new ve;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._disabled?this._unsubscribe():this._subscribe()}_disabled=!1;get debounce(){return this._debounce}set debounce(n){this._debounce=Nu(n),this._subscribe()}_debounce;_currentSubscription=null;constructor(){}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let n=this._contentObserver.observe(this._elementRef);this._currentSubscription=(this.debounce?n.pipe(qt(this.debounce)):n).subscribe(this.event)}_unsubscribe(){this._currentSubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[2,"cdkObserveContentDisabled","disabled",ke],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"]})}return e})(),Fg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({providers:[Og]})}return e})();var Bg=(()=>{class e{_platform=m(me);constructor(){}isDisabled(n){return n.hasAttribute("disabled")}isVisible(n){return o0(n)&&getComputedStyle(n).visibility==="visible"}isTabbable(n){if(!this._platform.isBrowser)return!1;let r=r0(f0(n));if(r&&(Pg(r)===-1||!this.isVisible(r)))return!1;let o=n.nodeName.toLowerCase(),i=Pg(n);return n.hasAttribute("contenteditable")?i!==-1:o==="iframe"||o==="object"||this._platform.WEBKIT&&this._platform.IOS&&!u0(n)?!1:o==="audio"?n.hasAttribute("controls")?i!==-1:!1:o==="video"?i===-1?!1:i!==null?!0:this._platform.FIREFOX||n.hasAttribute("controls"):n.tabIndex>=0}isFocusable(n,r){return d0(n)&&!this.isDisabled(n)&&(r?.ignoreVisibility||this.isVisible(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function r0(e){try{return e.frameElement}catch{return null}}function o0(e){return!!(e.offsetWidth||e.offsetHeight||typeof e.getClientRects=="function"&&e.getClientRects().length)}function i0(e){let t=e.nodeName.toLowerCase();return t==="input"||t==="select"||t==="button"||t==="textarea"}function s0(e){return c0(e)&&e.type=="hidden"}function a0(e){return l0(e)&&e.hasAttribute("href")}function c0(e){return e.nodeName.toLowerCase()=="input"}function l0(e){return e.nodeName.toLowerCase()=="a"}function Vg(e){if(!e.hasAttribute("tabindex")||e.tabIndex===void 0)return!1;let t=e.getAttribute("tabindex");return!!(t&&!isNaN(parseInt(t,10)))}function Pg(e){if(!Vg(e))return null;let t=parseInt(e.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}function u0(e){let t=e.nodeName.toLowerCase(),n=t==="input"&&e.type;return n==="text"||n==="password"||t==="select"||t==="textarea"}function d0(e){return s0(e)?!1:i0(e)||a0(e)||e.hasAttribute("contenteditable")||Vg(e)}function f0(e){return e.ownerDocument&&e.ownerDocument.defaultView||window}var Us=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(t){this._enabled=t,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_enabled=!0;constructor(t,n,r,o,i=!1,s){this._element=t,this._checker=n,this._ngZone=r,this._document=o,this._injector=s,i||this.attachAnchors()}destroy(){let t=this._startAnchor,n=this._endAnchor;t&&(t.removeEventListener("focus",this.startAnchorListener),t.remove()),n&&(n.removeEventListener("focus",this.endAnchorListener),n.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusInitialElement(t)))})}focusFirstTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusFirstTabbableElement(t)))})}focusLastTabbableElementWhenReady(t){return new Promise(n=>{this._executeOnStable(()=>n(this.focusLastTabbableElement(t)))})}_getRegionBoundary(t){let n=this._element.querySelectorAll(`[cdk-focus-region-${t}], [cdkFocusRegion${t}], [cdk-focus-${t}]`);return t=="start"?n.length?n[0]:this._getFirstTabbableElement(this._element):n.length?n[n.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(t){let n=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(n){if(!this._checker.isFocusable(n)){let r=this._getFirstTabbableElement(n);return r?.focus(t),!!r}return n.focus(t),!0}return this.focusFirstTabbableElement(t)}focusFirstTabbableElement(t){let n=this._getRegionBoundary("start");return n&&n.focus(t),!!n}focusLastTabbableElement(t){let n=this._getRegionBoundary("end");return n&&n.focus(t),!!n}hasAttached(){return this._hasAttached}_getFirstTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=0;r<n.length;r++){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(n[r]):null;if(o)return o}return null}_getLastTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;let n=t.children;for(let r=n.length-1;r>=0;r--){let o=n[r].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(n[r]):null;if(o)return o}return null}_createAnchor(){let t=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,t),t.classList.add("cdk-visually-hidden"),t.classList.add("cdk-focus-trap-anchor"),t.setAttribute("aria-hidden","true"),t}_toggleAnchorTabIndex(t,n){t?n.setAttribute("tabindex","0"):n.removeAttribute("tabindex")}toggleAnchors(t){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_executeOnStable(t){this._injector?Al(t,{injector:this._injector}):setTimeout(t)}},Hg=(()=>{class e{_checker=m(Bg);_ngZone=m(O);_document=m(F);_injector=m(J);constructor(){m(Et).load(Hs)}create(n,r=!1){return new Us(n,this._checker,this._ngZone,this._document,r,this._injector)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),p0=(()=>{class e{_elementRef=m(ee);_focusTrapFactory=m(Hg);focusTrap;_previouslyFocusedElement=null;get enabled(){return this.focusTrap?.enabled||!1}set enabled(n){this.focusTrap&&(this.focusTrap.enabled=n)}autoCapture;constructor(){m(me).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(n){let r=n.autoCapture;r&&!r.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=xu(),this.focusTrap?.focusInitialElementWhenReady()}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",ke],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",ke]},exportAs:["cdkTrapFocus"],features:[Zi]})}return e})(),Ug=new y("liveAnnouncerElement",{providedIn:"root",factory:$g});function $g(){return null}var zg=new y("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),m0=0,h0=(()=>{class e{_ngZone=m(O);_defaultOptions=m(zg,{optional:!0});_liveElement;_document=m(F);_previousTimeout;_currentPromise;_currentResolve;constructor(){let n=m(Ug,{optional:!0});this._liveElement=n||this._createLiveElement()}announce(n,...r){let o=this._defaultOptions,i,s;return r.length===1&&typeof r[0]=="number"?s=r[0]:[i,s]=r,this.clear(),clearTimeout(this._previousTimeout),i||(i=o&&o.politeness?o.politeness:"polite"),s==null&&o&&(s=o.duration),this._liveElement.setAttribute("aria-live",i),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(a=>this._currentResolve=a)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=n,typeof s=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),s)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let n="cdk-live-announcer-element",r=this._document.getElementsByClassName(n),o=this._document.createElement("div");for(let i=0;i<r.length;i++)r[i].remove();return o.classList.add(n),o.classList.add("cdk-visually-hidden"),o.setAttribute("aria-atomic","true"),o.setAttribute("aria-live","polite"),o.id=`cdk-live-announcer-${m0++}`,this._document.body.appendChild(o),o}_exposeAnnouncerToModals(n){let r=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let o=0;o<r.length;o++){let i=r[o],s=i.getAttribute("aria-owns");s?s.indexOf(n)===-1&&i.setAttribute("aria-owns",s+" "+n):i.setAttribute("aria-owns",n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Lt=function(e){return e[e.NONE=0]="NONE",e[e.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",e[e.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",e}(Lt||{}),Lg="cdk-high-contrast-black-on-white",jg="cdk-high-contrast-white-on-black",Ou="cdk-high-contrast-active",$s=(()=>{class e{_platform=m(me);_hasCheckedHighContrastMode;_document=m(F);_breakpointSubscription;constructor(){this._breakpointSubscription=m(Rg).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return Lt.NONE;let n=this._document.createElement("div");n.style.backgroundColor="rgb(1,2,3)",n.style.position="absolute",this._document.body.appendChild(n);let r=this._document.defaultView||window,o=r&&r.getComputedStyle?r.getComputedStyle(n):null,i=(o&&o.backgroundColor||"").replace(/ /g,"");switch(n.remove(),i){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return Lt.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return Lt.BLACK_ON_WHITE}return Lt.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let n=this._document.body.classList;n.remove(Ou,Lg,jg),this._hasCheckedHighContrastMode=!0;let r=this.getHighContrastMode();r===Lt.BLACK_ON_WHITE?n.add(Ou,Lg):r===Lt.WHITE_ON_BLACK&&n.add(Ou,jg)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),g0=(()=>{class e{constructor(){m($s)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[Fg]})}return e})();var ku={},b0=(()=>{class e{_appId=m(Rt);getId(n){return this._appId!=="ng"&&(n+=this._appId),ku.hasOwnProperty(n)||(ku[n]=0),`${n}${ku[n]++}`}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var y0=200,zs=class{_letterKeyStream=new B;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new B;selectedItem=this._selectedItem;constructor(t,n){let r=typeof n?.debounceInterval=="number"?n.debounceInterval:y0;n?.skipPredicate&&(this._skipPredicateFn=n.skipPredicate),this.setItems(t),this._setupKeyHandler(r)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){let n=t.keyCode;t.key&&t.key.length===1?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(n>=65&&n<=90||n>=48&&n<=57)&&this._letterKeyStream.next(String.fromCharCode(n))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe(ga(n=>this._pressedLetters.push(n)),qt(t),Te(()=>this._pressedLetters.length>0),X(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(n=>{for(let r=1;r<this._items.length+1;r++){let o=(this._selectedItemIndex+r)%this._items.length,i=this._items[o];if(!this._skipPredicateFn?.(i)&&i.getLabel?.().toLocaleUpperCase().trim().indexOf(n)===0){this._selectedItem.next(i);break}}this._pressedLetters=[]})}};function Gg(e,...t){return t.length?t.some(n=>e[n]):e.altKey||e.shiftKey||e.ctrlKey||e.metaKey}var cr=class{_items;_activeItemIndex=-1;_activeItem=Tl(null);_wrap=!1;_typeaheadSubscription=K.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=t=>t.disabled;constructor(t,n){this._items=t,t instanceof Bn?this._itemChangesSubscription=t.changes.subscribe(r=>this._itemsChanged(r.toArray())):ts(t)&&(this._effectRef=ru(()=>this._itemsChanged(t()),{injector:n}))}tabOut=new B;change=new B;skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();let n=this._getItemsArray();return this._typeahead=new zs(n,{debounceInterval:typeof t=="number"?t:void 0,skipPredicate:r=>this._skipPredicateFn(r)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(r=>{this.setActiveItem(r)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,n=10){return this._pageUpAndDown={enabled:t,delta:n},this}setActiveItem(t){let n=this._activeItem();this.updateActiveItem(t),this._activeItem()!==n&&this.change.next(this._activeItemIndex)}onKeydown(t){let n=t.keyCode,o=["altKey","ctrlKey","metaKey","shiftKey"].every(i=>!t[i]||this._allowedModifierKeys.indexOf(i)>-1);switch(n){case 9:this.tabOut.next();return;case 40:if(this._vertical&&o){this.setNextItemActive();break}else return;case 38:if(this._vertical&&o){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&o){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&o){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&o){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&o){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(i>0?i:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&o){let i=this._activeItemIndex+this._pageUpAndDown.delta,s=this._getItemsArray().length;this._setActiveItemByIndex(i<s?i:s-1,-1);break}else return;default:(o||Gg(t,"shiftKey"))&&this._typeahead?.handleKey(t);return}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){let n=this._getItemsArray(),r=typeof t=="number"?t:n.indexOf(t),o=n[r];this._activeItem.set(o??null),this._activeItemIndex=r,this._typeahead?.setCurrentSelectedItemIndex(r)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){let n=this._getItemsArray();for(let r=1;r<=n.length;r++){let o=(this._activeItemIndex+t*r+n.length)%n.length,i=n[o];if(!this._skipPredicateFn(i)){this.setActiveItem(o);return}}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex+t,t)}_setActiveItemByIndex(t,n){let r=this._getItemsArray();if(r[t]){for(;this._skipPredicateFn(r[t]);)if(t+=n,!r[t])return;this.setActiveItem(t)}}_getItemsArray(){return ts(this._items)?this._items():this._items instanceof Bn?this._items.toArray():this._items}_itemsChanged(t){this._typeahead?.setItems(t);let n=this._activeItem();if(n){let r=t.indexOf(n);r>-1&&r!==this._activeItemIndex&&(this._activeItemIndex=r,this._typeahead?.setCurrentSelectedItemIndex(r))}}};var Fu=class extends cr{setActiveItem(t){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(t),this.activeItem&&this.activeItem.setActiveStyles()}};var Pu=class extends cr{_origin="program";setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}};var qg=" ";function v0(e,t,n){let r=Ws(e,t);n=n.trim(),!r.some(o=>o.trim()===n)&&(r.push(n),e.setAttribute(t,r.join(qg)))}function D0(e,t,n){let r=Ws(e,t);n=n.trim();let o=r.filter(i=>i!==n);o.length?e.setAttribute(t,o.join(qg)):e.removeAttribute(t)}function Ws(e,t){return e.getAttribute(t)?.match(/\S+/g)??[]}var Zg="cdk-describedby-message",Gs="cdk-describedby-host",ju=0,sj=(()=>{class e{_platform=m(me);_document=m(F);_messageRegistry=new Map;_messagesContainer=null;_id=`${ju++}`;constructor(){m(Et).load(Hs),this._id=m(Rt)+"-"+ju++}describe(n,r,o){if(!this._canBeDescribed(n,r))return;let i=Lu(r,o);typeof r!="string"?(Wg(r,this._id),this._messageRegistry.set(i,{messageElement:r,referenceCount:0})):this._messageRegistry.has(i)||this._createMessageElement(r,o),this._isElementDescribedByMessage(n,i)||this._addMessageReference(n,i)}removeDescription(n,r,o){if(!r||!this._isElementNode(n))return;let i=Lu(r,o);if(this._isElementDescribedByMessage(n,i)&&this._removeMessageReference(n,i),typeof r=="string"){let s=this._messageRegistry.get(i);s&&s.referenceCount===0&&this._deleteMessageElement(i)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let n=this._document.querySelectorAll(`[${Gs}="${this._id}"]`);for(let r=0;r<n.length;r++)this._removeCdkDescribedByReferenceIds(n[r]),n[r].removeAttribute(Gs);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(n,r){let o=this._document.createElement("div");Wg(o,this._id),o.textContent=n,r&&o.setAttribute("role",r),this._createMessagesContainer(),this._messagesContainer.appendChild(o),this._messageRegistry.set(Lu(n,r),{messageElement:o,referenceCount:0})}_deleteMessageElement(n){this._messageRegistry.get(n)?.messageElement?.remove(),this._messageRegistry.delete(n)}_createMessagesContainer(){if(this._messagesContainer)return;let n="cdk-describedby-message-container",r=this._document.querySelectorAll(`.${n}[platform="server"]`);for(let i=0;i<r.length;i++)r[i].remove();let o=this._document.createElement("div");o.style.visibility="hidden",o.classList.add(n),o.classList.add("cdk-visually-hidden"),this._platform.isBrowser||o.setAttribute("platform","server"),this._document.body.appendChild(o),this._messagesContainer=o}_removeCdkDescribedByReferenceIds(n){let r=Ws(n,"aria-describedby").filter(o=>o.indexOf(Zg)!=0);n.setAttribute("aria-describedby",r.join(" "))}_addMessageReference(n,r){let o=this._messageRegistry.get(r);v0(n,"aria-describedby",o.messageElement.id),n.setAttribute(Gs,this._id),o.referenceCount++}_removeMessageReference(n,r){let o=this._messageRegistry.get(r);o.referenceCount--,D0(n,"aria-describedby",o.messageElement.id),n.removeAttribute(Gs)}_isElementDescribedByMessage(n,r){let o=Ws(n,"aria-describedby"),i=this._messageRegistry.get(r),s=i&&i.messageElement.id;return!!s&&o.indexOf(s)!=-1}_canBeDescribed(n,r){if(!this._isElementNode(n))return!1;if(r&&typeof r=="object")return!0;let o=r==null?"":`${r}`.trim(),i=n.getAttribute("aria-label");return o?!i||i.trim()!==o:!1}_isElementNode(n){return n.nodeType===this._document.ELEMENT_NODE}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Lu(e,t){return typeof e=="string"?`${t||""}/${e}`:e}function Wg(e,t){e.id||(e.id=`${Zg}-${t}-${ju++}`)}var bo=function(e){return e[e.NORMAL=0]="NORMAL",e[e.NEGATED=1]="NEGATED",e[e.INVERTED=2]="INVERTED",e}(bo||{}),qs,vn;function mj(){if(vn==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return vn=!1,vn;if("scrollBehavior"in document.documentElement.style)vn=!0;else{let e=Element.prototype.scrollTo;e?vn=!/\{\s*\[native code\]\s*\}/.test(e.toString()):vn=!1}}return vn}function hj(){if(typeof document!="object"||!document)return bo.NORMAL;if(qs==null){let e=document.createElement("div"),t=e.style;e.dir="rtl",t.width="1px",t.overflow="auto",t.visibility="hidden",t.pointerEvents="none",t.position="absolute";let n=document.createElement("div"),r=n.style;r.width="2px",r.height="1px",e.appendChild(n),document.body.appendChild(e),qs=bo.NORMAL,e.scrollLeft===0&&(e.scrollLeft=1,qs=e.scrollLeft===0?bo.NEGATED:bo.INVERTED),e.remove()}return qs}function bj(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}var lr,Yg=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function vj(){if(lr)return lr;if(typeof document!="object"||!document)return lr=new Set(Yg),lr;let e=document.createElement("input");return lr=new Set(Yg.filter(t=>(e.setAttribute("type",t),e.type===t))),lr}function _0(e){return e!=null&&`${e}`!="false"}function Mj(e){return e==null?"":typeof e=="string"?e:`${e}px`}function xj(e,t=/\s+/){let n=[];if(e!=null){let r=Array.isArray(e)?e:`${e}`.split(t);for(let o of r){let i=`${o}`.trim();i&&n.push(i)}}return n}var Pe=function(e){return e[e.FADING_IN=0]="FADING_IN",e[e.VISIBLE=1]="VISIBLE",e[e.FADING_OUT=2]="FADING_OUT",e[e.HIDDEN=3]="HIDDEN",e}(Pe||{}),Bu=class{_renderer;element;config;_animationForciblyDisabledThroughCss;state=Pe.HIDDEN;constructor(t,n,r,o=!1){this._renderer=t,this.element=n,this.config=r,this._animationForciblyDisabledThroughCss=o}fadeOut(){this._renderer.fadeOutRipple(this)}},Kg=ar({passive:!0,capture:!0}),Vu=class{_events=new Map;addHandler(t,n,r,o){let i=this._events.get(n);if(i){let s=i.get(r);s?s.add(o):i.set(r,new Set([o]))}else this._events.set(n,new Map([[r,new Set([o])]])),t.runOutsideAngular(()=>{document.addEventListener(n,this._delegateEventHandler,Kg)})}removeHandler(t,n,r){let o=this._events.get(t);if(!o)return;let i=o.get(n);i&&(i.delete(r),i.size===0&&o.delete(n),o.size===0&&(this._events.delete(t),document.removeEventListener(t,this._delegateEventHandler,Kg)))}_delegateEventHandler=t=>{let n=We(t);n&&this._events.get(t.type)?.forEach((r,o)=>{(o===n||o.contains(n))&&r.forEach(i=>i.handleEvent(t))})}},yo={enterDuration:225,exitDuration:150},E0=800,Qg=ar({passive:!0,capture:!0}),Xg=["mousedown","touchstart"],Jg=["mouseup","mouseleave","touchend","touchcancel"],w0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["ng-component"]],hostAttrs:["mat-ripple-style-loader",""],decls:0,vars:0,template:function(r,o){},styles:[`.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale3d(0, 0, 0);background-color:var(--mat-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface) 10%, transparent))}@media(forced-colors: active){.mat-ripple-element{display:none}}.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}
`],encapsulation:2,changeDetection:0})}return e})(),vo=class e{_target;_ngZone;_platform;_containerElement;_triggerElement;_isPointerDown=!1;_activeRipples=new Map;_mostRecentTransientRipple;_lastTouchStartEvent;_pointerUpEventsRegistered=!1;_containerRect;static _eventManager=new Vu;constructor(t,n,r,o,i){this._target=t,this._ngZone=n,this._platform=o,o.isBrowser&&(this._containerElement=at(r)),i&&i.get(Et).load(w0)}fadeInRipple(t,n,r={}){let o=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),i=k(k({},yo),r.animation);r.centered&&(t=o.left+o.width/2,n=o.top+o.height/2);let s=r.radius||I0(t,n,o),a=t-o.left,c=n-o.top,l=i.enterDuration,u=document.createElement("div");u.classList.add("mat-ripple-element"),u.style.left=`${a-s}px`,u.style.top=`${c-s}px`,u.style.height=`${s*2}px`,u.style.width=`${s*2}px`,r.color!=null&&(u.style.backgroundColor=r.color),u.style.transitionDuration=`${l}ms`,this._containerElement.appendChild(u);let d=window.getComputedStyle(u),p=d.transitionProperty,f=d.transitionDuration,h=p==="none"||f==="0s"||f==="0s, 0s"||o.width===0&&o.height===0,g=new Bu(this,u,r,h);u.style.transform="scale3d(1, 1, 1)",g.state=Pe.FADING_IN,r.persistent||(this._mostRecentTransientRipple=g);let v=null;return!h&&(l||i.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let w=()=>{v&&(v.fallbackTimer=null),clearTimeout(Y),this._finishRippleTransition(g)},$=()=>this._destroyRipple(g),Y=setTimeout($,l+100);u.addEventListener("transitionend",w),u.addEventListener("transitioncancel",$),v={onTransitionEnd:w,onTransitionCancel:$,fallbackTimer:Y}}),this._activeRipples.set(g,v),(h||!l)&&this._finishRippleTransition(g),g}fadeOutRipple(t){if(t.state===Pe.FADING_OUT||t.state===Pe.HIDDEN)return;let n=t.element,r=k(k({},yo),t.config.animation);n.style.transitionDuration=`${r.exitDuration}ms`,n.style.opacity="0",t.state=Pe.FADING_OUT,(t._animationForciblyDisabledThroughCss||!r.exitDuration)&&this._finishRippleTransition(t)}fadeOutAll(){this._getActiveRipples().forEach(t=>t.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(t=>{t.config.persistent||t.fadeOut()})}setupTriggerEvents(t){let n=at(t);!this._platform.isBrowser||!n||n===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=n,Xg.forEach(r=>{e._eventManager.addHandler(this._ngZone,r,n,this)}))}handleEvent(t){t.type==="mousedown"?this._onMousedown(t):t.type==="touchstart"?this._onTouchStart(t):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Jg.forEach(n=>{this._triggerElement.addEventListener(n,this,Qg)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(t){t.state===Pe.FADING_IN?this._startFadeOutTransition(t):t.state===Pe.FADING_OUT&&this._destroyRipple(t)}_startFadeOutTransition(t){let n=t===this._mostRecentTransientRipple,{persistent:r}=t.config;t.state=Pe.VISIBLE,!r&&(!n||!this._isPointerDown)&&t.fadeOut()}_destroyRipple(t){let n=this._activeRipples.get(t)??null;this._activeRipples.delete(t),this._activeRipples.size||(this._containerRect=null),t===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),t.state=Pe.HIDDEN,n!==null&&(t.element.removeEventListener("transitionend",n.onTransitionEnd),t.element.removeEventListener("transitioncancel",n.onTransitionCancel),n.fallbackTimer!==null&&clearTimeout(n.fallbackTimer)),t.element.remove()}_onMousedown(t){let n=po(t),r=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+E0;!this._target.rippleDisabled&&!n&&!r&&(this._isPointerDown=!0,this.fadeInRipple(t.clientX,t.clientY,this._target.rippleConfig))}_onTouchStart(t){if(!this._target.rippleDisabled&&!mo(t)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let n=t.changedTouches;if(n)for(let r=0;r<n.length;r++)this.fadeInRipple(n[r].clientX,n[r].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(t=>{let n=t.state===Pe.VISIBLE||t.config.terminateOnPointerUp&&t.state===Pe.FADING_IN;!t.config.persistent&&n&&t.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let t=this._triggerElement;t&&(Xg.forEach(n=>e._eventManager.removeHandler(n,t,this)),this._pointerUpEventsRegistered&&(Jg.forEach(n=>t.removeEventListener(n,this,Qg)),this._pointerUpEventsRegistered=!1))}};function I0(e,t,n){let r=Math.max(Math.abs(e-n.left),Math.abs(e-n.right)),o=Math.max(Math.abs(t-n.top),Math.abs(t-n.bottom));return Math.sqrt(r*r+o*o)}var Hu=new y("mat-ripple-global-options"),Uj=(()=>{class e{_elementRef=m(ee);_animationMode=m(Qn,{optional:!0});color;unbounded;centered;radius=0;animation;get disabled(){return this._disabled}set disabled(n){n&&this.fadeOutAllNonPersistent(),this._disabled=n,this._setupTriggerEventsIfEnabled()}_disabled=!1;get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(n){this._trigger=n,this._setupTriggerEventsIfEnabled()}_trigger;_rippleRenderer;_globalOptions;_isInitialized=!1;constructor(){let n=m(O),r=m(me),o=m(Hu,{optional:!0}),i=m(J);this._globalOptions=o||{},this._rippleRenderer=new vo(this,n,this._elementRef,r,i)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:k(k(k({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(n,r=0,o){return typeof n=="number"?this._rippleRenderer.fadeInRipple(n,r,k(k({},this.rippleConfig),o)):this._rippleRenderer.fadeInRipple(0,0,k(k({},this.rippleConfig),n))}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(r,o){r&2&&Ne("mat-ripple-unbounded",o.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"]})}return e})();var C0={capture:!0},M0=["focus","mousedown","mouseenter","touchstart"],Uu="mat-ripple-loader-uninitialized",$u="mat-ripple-loader-class-name",eb="mat-ripple-loader-centered",Zs="mat-ripple-loader-disabled",tb=(()=>{class e{_document=m(F);_animationMode=m(Qn,{optional:!0});_globalRippleOptions=m(Hu,{optional:!0});_platform=m(me);_ngZone=m(O);_injector=m(J);_eventCleanups;_hosts=new Map;constructor(){let n=m(rt).createRenderer(null,null);this._eventCleanups=this._ngZone.runOutsideAngular(()=>M0.map(r=>bn(n,this._document,r,this._onInteraction,C0)))}ngOnDestroy(){let n=this._hosts.keys();for(let r of n)this.destroyRipple(r);this._eventCleanups.forEach(r=>r())}configureRipple(n,r){n.setAttribute(Uu,this._globalRippleOptions?.namespace??""),(r.className||!n.hasAttribute($u))&&n.setAttribute($u,r.className||""),r.centered&&n.setAttribute(eb,""),r.disabled&&n.setAttribute(Zs,"")}setDisabled(n,r){let o=this._hosts.get(n);o?(o.target.rippleDisabled=r,!r&&!o.hasSetUpEvents&&(o.hasSetUpEvents=!0,o.renderer.setupTriggerEvents(n))):r?n.setAttribute(Zs,""):n.removeAttribute(Zs)}_onInteraction=n=>{let r=We(n);if(r instanceof HTMLElement){let o=r.closest(`[${Uu}="${this._globalRippleOptions?.namespace??""}"]`);o&&this._createRipple(o)}};_createRipple(n){if(!this._document||this._hosts.has(n))return;n.querySelector(".mat-ripple")?.remove();let r=this._document.createElement("span");r.classList.add("mat-ripple",n.getAttribute($u)),n.append(r);let o=this._animationMode==="NoopAnimations",i=this._globalRippleOptions,s=o?0:i?.animation?.enterDuration??yo.enterDuration,a=o?0:i?.animation?.exitDuration??yo.exitDuration,c={rippleDisabled:o||i?.disabled||n.hasAttribute(Zs),rippleConfig:{centered:n.hasAttribute(eb),terminateOnPointerUp:i?.terminateOnPointerUp,animation:{enterDuration:s,exitDuration:a}}},l=new vo(c,this._ngZone,r,this._platform,this._injector),u=!c.rippleDisabled;u&&l.setupTriggerEvents(n),this._hosts.set(n,{target:c,renderer:l,hasSetUpEvents:u}),n.removeAttribute(Uu)}destroyRipple(n){let r=this._hosts.get(n);r&&(r.renderer._removeTriggerEvents(),this._hosts.delete(n))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var nb=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["structural-styles"]],decls:0,vars:0,template:function(r,o){},styles:[`.mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:""}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}
`],encapsulation:2,changeDetection:0})}return e})();var T0=["mat-icon-button",""],x0=["*"];var S0=new y("MAT_BUTTON_CONFIG");var N0=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],Ys=(()=>{class e{_elementRef=m(ee);_ngZone=m(O);_animationMode=m(Qn,{optional:!0});_focusMonitor=m(Bs);_rippleLoader=m(tb);_isFab=!1;color;get disableRipple(){return this._disableRipple}set disableRipple(n){this._disableRipple=n,this._updateRippleDisabled()}_disableRipple=!1;get disabled(){return this._disabled}set disabled(n){this._disabled=n,this._updateRippleDisabled()}_disabled=!1;ariaDisabled;disabledInteractive;constructor(){m(Et).load(nb);let n=m(S0,{optional:!0}),r=this._elementRef.nativeElement,o=r.classList;this.disabledInteractive=n?.disabledInteractive??!1,this.color=n?.color??null,this._rippleLoader?.configureRipple(r,{className:"mat-mdc-button-ripple"});for(let{attribute:i,mdcClasses:s}of N0)r.hasAttribute(i)&&o.add(...s)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(n="program",r){n?this._focusMonitor.focusVia(this._elementRef.nativeElement,n,r):this._elementRef.nativeElement.focus(r)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",ke],disabled:[2,"disabled","disabled",ke],ariaDisabled:[2,"aria-disabled","ariaDisabled",ke],disabledInteractive:[2,"disabledInteractive","disabledInteractive",ke]}})}return e})();var A0=(()=>{class e extends Ys{constructor(){super(),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Yr(o.color?"mat-"+o.color:""),Ne("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[tr],attrs:T0,ngContentSelectors:x0,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(),yt(0,"span",0),Ce(1),yt(2,"span",1)(3,"span",2))},styles:[`.mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 40px);height:var(--mdc-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 40px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size, 24px);color:var(--mdc-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:""}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display, block)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size, 24px);height:var(--mdc-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return e})();var R0=new y("cdk-dir-doc",{providedIn:"root",factory:O0});function O0(){return m(F)}var k0=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function rb(e){let t=e?.toLowerCase()||"";return t==="auto"&&typeof navigator<"u"&&navigator?.language?k0.test(navigator.language)?"rtl":"ltr":t==="rtl"?"rtl":"ltr"}var F0=(()=>{class e{value="ltr";change=new ve;constructor(){let n=m(R0,{optional:!0});if(n){let r=n.body?n.body.dir:null,o=n.documentElement?n.documentElement.dir:null;this.value=rb(r||o||"ltr")}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var zu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({})}return e})();var wt=(()=>{class e{constructor(){m($s)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[zu,zu]})}return e})();var ob=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,wt]})}return e})();var P0=["mat-button",""],sb=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],ab=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var L0=["mat-mini-fab",""],j0=`.mat-mdc-fab-base{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;width:56px;height:56px;padding:0;border:none;fill:currentColor;text-decoration:none;cursor:pointer;-moz-appearance:none;-webkit-appearance:none;overflow:visible;transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1),opacity 15ms linear 30ms,transform 270ms 0ms cubic-bezier(0, 0, 0.2, 1);flex-shrink:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-fab-base .mat-mdc-button-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple,.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-fab-base .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-fab-base .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-fab-base .mdc-button__label,.mat-mdc-fab-base .mat-icon{z-index:1;position:relative}.mat-mdc-fab-base .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-fab-base:focus>.mat-focus-indicator::before{content:""}.mat-mdc-fab-base._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-fab-base::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mat-mdc-fab-base[hidden]{display:none}.mat-mdc-fab-base::-moz-focus-inner{padding:0;border:0}.mat-mdc-fab-base:active,.mat-mdc-fab-base:focus{outline:none}.mat-mdc-fab-base:hover{cursor:pointer}.mat-mdc-fab-base>svg{width:100%}.mat-mdc-fab-base .mat-icon,.mat-mdc-fab-base .material-icons{transition:transform 180ms 90ms cubic-bezier(0, 0, 0.2, 1);fill:currentColor;will-change:transform}.mat-mdc-fab-base .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-fab-base[disabled],.mat-mdc-fab-base[disabled]:focus,.mat-mdc-fab-base.mat-mdc-button-disabled,.mat-mdc-fab-base.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-fab-base.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab{background-color:var(--mdc-fab-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-container-shape, var(--mat-sys-corner-large));color:var(--mat-fab-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:hover{box-shadow:var(--mdc-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-fab:focus{box-shadow:var(--mdc-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab:active,.mat-mdc-fab:focus:active{box-shadow:var(--mdc-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-fab[disabled],.mat-mdc-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-touch-target-display, block)}.mat-mdc-fab .mat-ripple-element{background-color:var(--mat-fab-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-disabled-state-layer-color)}.mat-mdc-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-mini-fab{width:40px;height:40px;background-color:var(--mdc-fab-small-container-color, var(--mat-sys-primary-container));border-radius:var(--mdc-fab-small-container-shape, var(--mat-sys-corner-medium));color:var(--mat-fab-small-foreground-color, var(--mat-sys-on-primary-container, inherit));box-shadow:var(--mdc-fab-small-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:hover{box-shadow:var(--mdc-fab-small-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-mini-fab:focus{box-shadow:var(--mdc-fab-small-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab:active,.mat-mdc-mini-fab:focus:active{box-shadow:var(--mdc-fab-small-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-mini-fab[disabled],.mat-mdc-mini-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-fab-small-disabled-state-foreground-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mat-fab-small-disabled-state-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-mini-fab .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-fab-small-touch-target-display)}.mat-mdc-mini-fab .mat-ripple-element{background-color:var(--mat-fab-small-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary-container) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-mini-fab .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-state-layer-color, var(--mat-sys-on-primary-container))}.mat-mdc-mini-fab.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-fab-small-disabled-state-layer-color)}.mat-mdc-mini-fab:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-mini-fab.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-mini-fab.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-mini-fab:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-fab-small-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-extended-fab{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;border-radius:24px;padding-left:20px;padding-right:20px;width:auto;max-width:100%;line-height:normal;height:var(--mdc-extended-fab-container-height, 56px);border-radius:var(--mdc-extended-fab-container-shape, var(--mat-sys-corner-large));font-family:var(--mdc-extended-fab-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-extended-fab-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-extended-fab-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-extended-fab-label-text-tracking, var(--mat-sys-label-large-tracking));box-shadow:var(--mdc-extended-fab-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:hover{box-shadow:var(--mdc-extended-fab-hover-container-elevation-shadow, var(--mat-sys-level4))}.mat-mdc-extended-fab:focus{box-shadow:var(--mdc-extended-fab-focus-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab:active,.mat-mdc-extended-fab:focus:active{box-shadow:var(--mdc-extended-fab-pressed-container-elevation-shadow, var(--mat-sys-level3))}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab.mat-mdc-button-disabled{cursor:default;pointer-events:none}.mat-mdc-extended-fab[disabled],.mat-mdc-extended-fab[disabled]:focus,.mat-mdc-extended-fab.mat-mdc-button-disabled,.mat-mdc-extended-fab.mat-mdc-button-disabled:focus{box-shadow:none}.mat-mdc-extended-fab.mat-mdc-button-disabled-interactive{pointer-events:auto}[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.mat-icon,[dir=rtl] .mat-mdc-extended-fab .mdc-button__label+.material-icons,.mat-mdc-extended-fab>.mat-icon,.mat-mdc-extended-fab>.material-icons{margin-left:-8px;margin-right:12px}.mat-mdc-extended-fab .mdc-button__label+.mat-icon,.mat-mdc-extended-fab .mdc-button__label+.material-icons,[dir=rtl] .mat-mdc-extended-fab>.mat-icon,[dir=rtl] .mat-mdc-extended-fab>.material-icons{margin-left:12px;margin-right:-8px}.mat-mdc-extended-fab .mat-mdc-button-touch-target{width:100%}
`,FB=(()=>{class e extends Ys{static \u0275fac=(()=>{let n;return function(o){return(n||(n=Il(e)))(o||e)}})();static \u0275cmp=we({type:e,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Yr(o.color?"mat-"+o.color:""),Ne("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[tr],attrs:P0,ngContentSelectors:ab,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(sb),yt(0,"span",0),Ce(1),pn(2,"span",1),Ce(3,1),mn(),Ce(4,2),yt(5,"span",2)(6,"span",3)),r&2&&Ne("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:[`.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 12px);height:var(--mdc-text-button-container-height, 40px);font-family:var(--mdc-text-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-sys-label-large-weight))}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-sys-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 16px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, -4px);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, -4px)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display, block)}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-filled-button-container-height, 40px);font-family:var(--mdc-filled-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-filled-button-horizontal-padding, 24px)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -8px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -8px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-sys-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-unelevated-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display, block)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-sys-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-sys-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-filled-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-sys-level1));height:var(--mdc-protected-button-container-height, 40px);font-family:var(--mdc-protected-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-sys-label-large-weight));padding:0 var(--mat-protected-button-horizontal-padding, 24px)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -8px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -8px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-raised-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-raised-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display, block)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-sys-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-sys-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-sys-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-sys-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-sys-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));background-color:var(--mdc-protected-button-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-sys-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mdc-outlined-button-container-height, 40px);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-sys-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-sys-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-sys-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-sys-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-sys-corner-full));border-width:var(--mdc-outlined-button-outline-width, 1px);padding:0 var(--mat-outlined-button-horizontal-padding, 24px)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -8px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -8px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color, color-mix(in srgb, var(--mat-sys-primary) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-sys-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-outlined-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-outlined-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display, block)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-sys-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-sys-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:var(--mdc-outlined-button-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-focus-indicator,.mat-mdc-unelevated-button .mat-focus-indicator,.mat-mdc-raised-button .mat-focus-indicator,.mat-mdc-outlined-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus>.mat-focus-indicator::before,.mat-mdc-unelevated-button:focus>.mat-focus-indicator::before,.mat-mdc-raised-button:focus>.mat-focus-indicator::before,.mat-mdc-outlined-button:focus>.mat-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-focus-indicator::before,.mat-mdc-raised-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}
`,`@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}
`],encapsulation:2,changeDetection:0})}return e})();var B0=new y("mat-mdc-fab-default-options",{providedIn:"root",factory:cb});function cb(){return{color:"accent"}}var ib=cb();var PB=(()=>{class e extends Ys{_options=m(B0,{optional:!0});_isFab=!0;constructor(){super(),this._options=this._options||ib,this.color=this._options.color||ib.color}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["button","mat-mini-fab",""]],hostVars:14,hostBindings:function(r,o){r&2&&(fn("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Yr(o.color?"mat-"+o.color:""),Ne("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],features:[tr],attrs:L0,ngContentSelectors:ab,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(r,o){r&1&&(Ft(sb),yt(0,"span",0),Ce(1),pn(2,"span",1),Ce(3,1),mn(),Ce(4,2),yt(5,"span",2)(6,"span",3)),r&2&&Ne("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:[j0],encapsulation:2,changeDetection:0})}return e})();var LB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,ob,wt]})}return e})();var V0=["*"];var H0=[[["","mat-card-avatar",""],["","matCardAvatar",""]],[["mat-card-title"],["mat-card-subtitle"],["","mat-card-title",""],["","mat-card-subtitle",""],["","matCardTitle",""],["","matCardSubtitle",""]],"*"],U0=["[mat-card-avatar], [matCardAvatar]",`mat-card-title, mat-card-subtitle,
      [mat-card-title], [mat-card-subtitle],
      [matCardTitle], [matCardSubtitle]`,"*"],$0=new y("MAT_CARD_CONFIG"),qB=(()=>{class e{appearance;constructor(){let n=m($0,{optional:!0});this.appearance=n?.appearance||"raised"}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["mat-card"]],hostAttrs:[1,"mat-mdc-card","mdc-card"],hostVars:4,hostBindings:function(r,o){r&2&&Ne("mat-mdc-card-outlined",o.appearance==="outlined")("mdc-card--outlined",o.appearance==="outlined")},inputs:{appearance:"appearance"},exportAs:["matCard"],ngContentSelectors:V0,decls:1,vars:0,template:function(r,o){r&1&&(Ft(),Ce(0))},styles:[`.mat-mdc-card{display:flex;flex-direction:column;box-sizing:border-box;position:relative;border-style:solid;border-width:0;background-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-color:var(--mdc-elevated-card-container-color, var(--mat-sys-surface-container-low));border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium));box-shadow:var(--mdc-elevated-card-container-elevation, var(--mat-sys-level1))}.mat-mdc-card::after{position:absolute;top:0;left:0;width:100%;height:100%;border:solid 1px rgba(0,0,0,0);content:"";display:block;pointer-events:none;box-sizing:border-box;border-radius:var(--mdc-elevated-card-container-shape, var(--mat-sys-corner-medium))}.mat-mdc-card-outlined{background-color:var(--mdc-outlined-card-container-color, var(--mat-sys-surface));border-radius:var(--mdc-outlined-card-container-shape, var(--mat-sys-corner-medium));border-width:var(--mdc-outlined-card-outline-width, 1px);border-color:var(--mdc-outlined-card-outline-color, var(--mat-sys-outline-variant));box-shadow:var(--mdc-outlined-card-container-elevation, var(--mat-sys-level0))}.mat-mdc-card-outlined::after{border:none}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:""}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mat-mdc-card-actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font, var(--mat-sys-title-large-font));line-height:var(--mat-card-title-text-line-height, var(--mat-sys-title-large-line-height));font-size:var(--mat-card-title-text-size, var(--mat-sys-title-large-size));letter-spacing:var(--mat-card-title-text-tracking, var(--mat-sys-title-large-tracking));font-weight:var(--mat-card-title-text-weight, var(--mat-sys-title-large-weight))}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color, var(--mat-sys-on-surface));font-family:var(--mat-card-subtitle-text-font, var(--mat-sys-title-medium-font));line-height:var(--mat-card-subtitle-text-line-height, var(--mat-sys-title-medium-line-height));font-size:var(--mat-card-subtitle-text-size, var(--mat-sys-title-medium-size));letter-spacing:var(--mat-card-subtitle-text-tracking, var(--mat-sys-title-medium-tracking));font-weight:var(--mat-card-subtitle-text-weight, var(--mat-sys-title-medium-weight))}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}
`],encapsulation:2,changeDetection:0})}return e})(),ZB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-title"],["","mat-card-title",""],["","matCardTitle",""]],hostAttrs:[1,"mat-mdc-card-title"]})}return e})();var YB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-content"]],hostAttrs:[1,"mat-mdc-card-content"]})}return e})(),KB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-subtitle"],["","mat-card-subtitle",""],["","matCardSubtitle",""]],hostAttrs:[1,"mat-mdc-card-subtitle"]})}return e})(),QB=(()=>{class e{align="start";static \u0275fac=function(r){return new(r||e)};static \u0275dir=q({type:e,selectors:[["mat-card-actions"]],hostAttrs:[1,"mat-mdc-card-actions","mdc-card__actions"],hostVars:2,hostBindings:function(r,o){r&2&&Ne("mat-mdc-card-actions-align-end",o.align==="end")},inputs:{align:"align"},exportAs:["matCardActions"]})}return e})(),XB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=we({type:e,selectors:[["mat-card-header"]],hostAttrs:[1,"mat-mdc-card-header"],ngContentSelectors:U0,decls:4,vars:0,consts:[[1,"mat-mdc-card-header-text"]],template:function(r,o){r&1&&(Ft(H0),Ce(0),pn(1,"div",0),Ce(2,1),mn(),Ce(3,2))},encapsulation:2,changeDetection:0})}return e})();var JB=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ie({type:e});static \u0275inj=oe({imports:[wt,wt]})}return e})();var tV={production:!1,supabaseUrl:"https://fiaqzhajyfkjbwushngn.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v8UgUwEetoAIwSqMbYSU77pK8ACM7Wryl2BCebW80ZQ",squareApplicationId:"sandbox-sq0idb-e6g3DAdm9fjgWfBGyXyk_g",squareLocationId:"sq0idp-6i0oiAmtrJ-bKkU9PIk9uQ",squareAccessToken:"EAAAl-JzTB0lwXa7T2FV7NJrF2gK80wgPwlrl3RfH3joiIH3A1HEq57Ak_og5KqY",stripePublishableKey:"pk_live_51QXPoUKBQR9HiWVf963rrIm0mm1h9v0RanSMS3u4t3OrQh6930W67JsmVn2E7EVR6eBzLhMfejvfJL0umcIbukFW00ELXE0ANo",stripeSecretKey:"",googleMapsApiKey:"AIzaSyDNfNjbZAkM9p_SmPwc_SZLDyKNMw07bc8",twilioAccountSid:"**********************************",twilioAuthToken:"60bdb78931f35cce4e8f47c3fba3f8ad",twilioPhoneNumber:"+***********"};export{K as a,mb as b,N as c,oa as d,ia as e,B as f,$t as g,dr as h,zt as i,Ae as j,In as k,Ib as l,Cb as m,ct as n,pb as o,Tb as p,X as q,aa as r,Wt as s,pr as t,Cn as u,Lb as v,jb as w,mr as x,Bb as y,Vb as z,Hb as A,Te as B,Ub as C,hd as D,ca as E,qt as F,hr as G,gr as H,la as I,ua as J,Gb as K,da as L,Wb as M,qb as N,Zb as O,pa as P,Yb as Q,br as R,ma as S,ha as T,Mn as U,Kb as V,ga as W,D as X,Ef as Y,b as Z,oe as _,SO as $,y as aa,R as ba,C as ca,m as da,sl as ea,Af as fa,Vr as ga,Ay as ha,Oe as ia,Gi as ja,Zi as ka,NO as la,AO as ma,RO as na,OO as oa,Il as pa,vp as qa,J as ra,Pd as sa,Ji as ta,Zn as ua,ve as va,O as wa,et as xa,kO as ya,ee as za,ts as Aa,Tl as Ba,Bn as Ca,it as Da,Qn as Ea,dn as Fa,Jv as Ga,Al as Ha,st as Ia,_D as Ja,FO as Ka,PO as La,St as Ma,rt as Na,qr as Oa,H as Pa,$O as Qa,kt as Ra,GO as Sa,Un as Ta,xE as Ua,SE as Va,we as Wa,ie as Xa,q as Ya,er as Za,tr as _a,ZO as $a,ZE as ab,QE as bb,fs as cb,YO as db,XE as eb,Nt as fb,fn as gb,mw as hb,hw as ib,Ne as jb,Yr as kb,KO as lb,QO as mb,XO as nb,JO as ob,ek as pb,pn as qb,mn as rb,yt as sb,eh as tb,th as ub,Sw as vb,tk as wb,Aw as xb,Vw as yb,nk as zb,Ft as Ab,Ce as Bb,zw as Cb,rk as Db,ok as Eb,ik as Fb,sk as Gb,ak as Hb,ck as Ib,lk as Jb,uk as Kb,Ww as Lb,oh as Mb,qw as Nb,Zw as Ob,dk as Pb,Yw as Qb,Qw as Rb,nI as Sb,fk as Tb,pk as Ub,mk as Vb,hk as Wb,gk as Xb,bk as Yb,yk as Zb,vk as _b,Dk as $b,_k as ac,Ek as bc,nr as cc,Jl as dc,eu as ec,ke as fc,ph as gc,nu as hc,wk as ic,ru as jc,hh as kc,Ik as lc,F as mc,vt as nc,CI as oc,rr as pc,Dh as qc,_h as rc,SI as sc,KI as tc,Wh as uc,QI as vc,qh as wc,XI as xc,JI as yc,eC as zc,rC as Ac,iC as Bc,sC as Cc,aC as Dc,cC as Ec,Xh as Fc,hu as Gc,kF as Hc,wu as Ic,EC as Jc,bg as Kc,YC as Lc,i1 as Mc,KC as Nc,xu as Oc,We as Pc,bn as Qc,me as Rc,ar as Sc,Nu as Tc,Ig as Uc,at as Vc,Bs as Wc,XC as Xc,Et as Yc,Hs as Zc,Ru as _c,Rg as $c,aL as ad,Fg as bd,Bg as cd,Hg as dd,p0 as ed,h0 as fd,g0 as gd,b0 as hd,Gg as id,Fu as jd,Pu as kd,v0 as ld,D0 as md,sj as nd,bo as od,mj as pd,hj as qd,bj as rd,vj as sd,_0 as td,Mj as ud,xj as vd,vo as wd,Hu as xd,Uj as yd,tb as zd,nb as Ad,A0 as Bd,F0 as Cd,zu as Dd,wt as Ed,ob as Fd,FB as Gd,B0 as Hd,PB as Id,LB as Jd,tV as Kd,qB as Ld,ZB as Md,YB as Nd,KB as Od,QB as Pd,XB as Qd,JB as Rd};
