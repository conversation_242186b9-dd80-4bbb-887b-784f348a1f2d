import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const publicGuard: CanActivateFn = async (_route, state) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  try {
    // Check if this is a login or register route
    const isAuthRoute = state.url.includes('/auth/login') || state.url.includes('/auth/register');
    // Check if this is the root route - be more explicit about checking
    const isRootRoute = state.url === '/' || state.url === '';

    console.log('PublicGuard - Current URL:', state.url);
    console.log('PublicGuard - Is root route:', isRootRoute);

    // First check if we already have a session using getCurrentUser
    // This is more reliable than the observable for immediate checks
    const currentUser = await authService.getCurrentUser();
    console.log('PublicGuard - Current user:', currentUser ? 'Authenticated' : 'Not authenticated');

    if (currentUser) {
      // User is already authenticated
      const role = await authService.getUserRole();
      console.log('PublicGuard - User role:', role);

      if (role) {
        if (isAuthRoute || isRootRoute) {
          // If authenticated user is trying to access login/register page or root, redirect to dashboard
          const dashboardRoute = authService.getDashboardRouteForRole(role);
          console.log(`PublicGuard - Authenticated user at ${state.url}, redirecting to ${dashboardRoute}`);
          await router.navigate([dashboardRoute]);
          return false;
        }
      }

      // For other public routes, allow authenticated users access
      return true;
    }

    // User is not authenticated
    if (isRootRoute) {
      // If at root route, redirect to login
      console.log('PublicGuard - Unauthenticated user at root, redirecting to login');
      await router.navigate(['/auth/login']);
      return false;
    }

    if (!isAuthRoute) {
      // If not on login/register page, redirect to login
      console.log('PublicGuard - Unauthenticated user trying to access protected route, redirecting to login');
      await router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url }});
      return false;
    }

    // Allow unauthenticated users to access login/register pages
    console.log('PublicGuard - Allowing unauthenticated user to access auth route');
    return true;
  } catch (error) {
    console.error('Public guard error:', error);
    // On error and not on login/register page, redirect to login
    if (!state.url.includes('/auth/login') && !state.url.includes('/auth/register')) {
      console.log('PublicGuard - Error occurred, redirecting to login');
      await router.navigate(['/auth/login']);
    }
    return true;
  }
};