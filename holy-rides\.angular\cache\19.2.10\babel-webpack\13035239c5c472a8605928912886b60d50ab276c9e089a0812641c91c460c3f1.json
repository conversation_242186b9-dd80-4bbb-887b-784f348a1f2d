{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { StorageApiError, StorageUnknownError } from './errors';\nimport { resolveResponse } from './helpers';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n  const Res = yield resolveResponse();\n  if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n    error.json().then(err => {\n      reject(new StorageApiError(_getErrorMessage(err), error.status || 500));\n    }).catch(err => {\n      reject(new StorageUnknownError(_getErrorMessage(err), err));\n    });\n  } else {\n    reject(new StorageUnknownError(_getErrorMessage(error), error));\n  }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  if (body) {\n    params.body = JSON.stringify(body);\n  }\n  return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n      fetcher(url, _getRequestParams(method, options, parameters, body)).then(result => {\n        if (!result.ok) throw result;\n        if (options === null || options === void 0 ? void 0 : options.noResolveJson) return result;\n        return result.json();\n      }).then(data => resolve(data)).catch(error => handleError(error, reject, options));\n    });\n  });\n}\nexport function get(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'GET', url, options, parameters);\n  });\n}\nexport function post(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n  });\n}\nexport function put(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n  });\n}\nexport function head(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), {\n      noResolveJson: true\n    }), parameters);\n  });\n}\nexport function remove(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n  });\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "StorageApiError", "StorageUnknownError", "resolveResponse", "_getErrorMessage", "err", "msg", "message", "error_description", "error", "JSON", "stringify", "handleError", "options", "Res", "noResolveJson", "json", "status", "catch", "_getRequestParams", "method", "parameters", "body", "params", "headers", "Object", "assign", "_handleRequest", "fetcher", "url", "ok", "data", "get", "post", "put", "head", "remove"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/lib/fetch.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { StorageApiError, StorageUnknownError } from './errors';\nimport { resolveResponse } from './helpers';\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n    const Res = yield resolveResponse();\n    if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n        error\n            .json()\n            .then((err) => {\n            reject(new StorageApiError(_getErrorMessage(err), error.status || 500));\n        })\n            .catch((err) => {\n            reject(new StorageUnknownError(_getErrorMessage(err), err));\n        });\n    }\n    else {\n        reject(new StorageUnknownError(_getErrorMessage(error), error));\n    }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json' }, options === null || options === void 0 ? void 0 : options.headers);\n    if (body) {\n        params.body = JSON.stringify(body);\n    }\n    return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            fetcher(url, _getRequestParams(method, options, parameters, body))\n                .then((result) => {\n                if (!result.ok)\n                    throw result;\n                if (options === null || options === void 0 ? void 0 : options.noResolveJson)\n                    return result;\n                return result.json();\n            })\n                .then((data) => resolve(data))\n                .catch((error) => handleError(error, reject, options));\n        });\n    });\n}\nexport function get(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'GET', url, options, parameters);\n    });\n}\nexport function post(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n    });\n}\nexport function put(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n    });\n}\nexport function head(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), { noResolveJson: true }), parameters);\n    });\n}\nexport function remove(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n    });\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,SAASO,eAAe,EAAEC,mBAAmB,QAAQ,UAAU;AAC/D,SAASC,eAAe,QAAQ,WAAW;AAC3C,MAAMC,gBAAgB,GAAIC,GAAG,IAAKA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,iBAAiB,IAAIH,GAAG,CAACI,KAAK,IAAIC,IAAI,CAACC,SAAS,CAACN,GAAG,CAAC;AACrH,MAAMO,WAAW,GAAGA,CAACH,KAAK,EAAElB,MAAM,EAAEsB,OAAO,KAAK/B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;EAC3F,MAAMgC,GAAG,GAAG,MAAMX,eAAe,CAAC,CAAC;EACnC,IAAIM,KAAK,YAAYK,GAAG,IAAI,EAAED,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,aAAa,CAAC,EAAE;IACpGN,KAAK,CACAO,IAAI,CAAC,CAAC,CACNjB,IAAI,CAAEM,GAAG,IAAK;MACfd,MAAM,CAAC,IAAIU,eAAe,CAACG,gBAAgB,CAACC,GAAG,CAAC,EAAEI,KAAK,CAACQ,MAAM,IAAI,GAAG,CAAC,CAAC;IAC3E,CAAC,CAAC,CACGC,KAAK,CAAEb,GAAG,IAAK;MAChBd,MAAM,CAAC,IAAIW,mBAAmB,CAACE,gBAAgB,CAACC,GAAG,CAAC,EAAEA,GAAG,CAAC,CAAC;IAC/D,CAAC,CAAC;EACN,CAAC,MACI;IACDd,MAAM,CAAC,IAAIW,mBAAmB,CAACE,gBAAgB,CAACK,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;EACnE;AACJ,CAAC,CAAC;AACF,MAAMU,iBAAiB,GAAGA,CAACC,MAAM,EAAEP,OAAO,EAAEQ,UAAU,EAAEC,IAAI,KAAK;EAC7D,MAAMC,MAAM,GAAG;IAAEH,MAAM;IAAEI,OAAO,EAAE,CAACX,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,OAAO,KAAK,CAAC;EAAE,CAAC;EAC7G,IAAIJ,MAAM,KAAK,KAAK,EAAE;IAClB,OAAOG,MAAM;EACjB;EACAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;IAAE,cAAc,EAAE;EAAmB,CAAC,EAAEb,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,OAAO,CAAC;EACzI,IAAIF,IAAI,EAAE;IACNC,MAAM,CAACD,IAAI,GAAGZ,IAAI,CAACC,SAAS,CAACW,IAAI,CAAC;EACtC;EACA,OAAOG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC,EAAEF,UAAU,CAAC;AAC/D,CAAC;AACD,SAASM,cAAcA,CAACC,OAAO,EAAER,MAAM,EAAES,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAEC,IAAI,EAAE;EACrE,OAAOxC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO,IAAIQ,OAAO,CAAC,CAACD,OAAO,EAAEE,MAAM,KAAK;MACpCqC,OAAO,CAACC,GAAG,EAAEV,iBAAiB,CAACC,MAAM,EAAEP,OAAO,EAAEQ,UAAU,EAAEC,IAAI,CAAC,CAAC,CAC7DvB,IAAI,CAAEF,MAAM,IAAK;QAClB,IAAI,CAACA,MAAM,CAACiC,EAAE,EACV,MAAMjC,MAAM;QAChB,IAAIgB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,aAAa,EACvE,OAAOlB,MAAM;QACjB,OAAOA,MAAM,CAACmB,IAAI,CAAC,CAAC;MACxB,CAAC,CAAC,CACGjB,IAAI,CAAEgC,IAAI,IAAK1C,OAAO,CAAC0C,IAAI,CAAC,CAAC,CAC7Bb,KAAK,CAAET,KAAK,IAAKG,WAAW,CAACH,KAAK,EAAElB,MAAM,EAAEsB,OAAO,CAAC,CAAC;IAC9D,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,OAAO,SAASmB,GAAGA,CAACJ,OAAO,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAE;EACnD,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO6C,cAAc,CAACC,OAAO,EAAE,KAAK,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,CAAC;EACnE,CAAC,CAAC;AACN;AACA,OAAO,SAASY,IAAIA,CAACL,OAAO,EAAEC,GAAG,EAAEP,IAAI,EAAET,OAAO,EAAEQ,UAAU,EAAE;EAC1D,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO6C,cAAc,CAACC,OAAO,EAAE,MAAM,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAEC,IAAI,CAAC;EAC1E,CAAC,CAAC;AACN;AACA,OAAO,SAASY,GAAGA,CAACN,OAAO,EAAEC,GAAG,EAAEP,IAAI,EAAET,OAAO,EAAEQ,UAAU,EAAE;EACzD,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO6C,cAAc,CAACC,OAAO,EAAE,KAAK,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAEC,IAAI,CAAC;EACzE,CAAC,CAAC;AACN;AACA,OAAO,SAASa,IAAIA,CAACP,OAAO,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAE;EACpD,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO6C,cAAc,CAACC,OAAO,EAAE,MAAM,EAAEC,GAAG,EAAEJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,OAAO,CAAC,EAAE;MAAEE,aAAa,EAAE;IAAK,CAAC,CAAC,EAAEM,UAAU,CAAC;EAC/H,CAAC,CAAC;AACN;AACA,OAAO,SAASe,MAAMA,CAACR,OAAO,EAAEC,GAAG,EAAEP,IAAI,EAAET,OAAO,EAAEQ,UAAU,EAAE;EAC5D,OAAOvC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;IAChD,OAAO6C,cAAc,CAACC,OAAO,EAAE,QAAQ,EAAEC,GAAG,EAAEhB,OAAO,EAAEQ,UAAU,EAAEC,IAAI,CAAC;EAC5E,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}