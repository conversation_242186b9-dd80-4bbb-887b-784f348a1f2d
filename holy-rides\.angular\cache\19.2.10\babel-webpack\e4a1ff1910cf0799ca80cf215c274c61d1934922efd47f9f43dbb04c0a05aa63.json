{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nexport function uuid() {\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n    var r = Math.random() * 16 | 0,\n      v = c == 'x' ? r : r & 0x3 | 0x8;\n    return v.toString(16);\n  });\n}\nexport function stripTrailingSlash(url) {\n  return url.replace(/\\/$/, '');\n}\nexport const isBrowser = () => typeof window !== 'undefined';\nexport function applySettingDefaults(options, defaults) {\n  const {\n    db: dbOptions,\n    auth: authOptions,\n    realtime: realtimeOptions,\n    global: globalOptions\n  } = options;\n  const {\n    db: DEFAULT_DB_OPTIONS,\n    auth: DEFAULT_AUTH_OPTIONS,\n    realtime: DEFAULT_REALTIME_OPTIONS,\n    global: DEFAULT_GLOBAL_OPTIONS\n  } = defaults;\n  const result = {\n    db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n    auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n    realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n    global: Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions),\n    accessToken: () => __awaiter(this, void 0, void 0, function* () {\n      return '';\n    })\n  };\n  if (options.accessToken) {\n    result.accessToken = options.accessToken;\n  } else {\n    // hack around Required<>\n    delete result.accessToken;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "uuid", "replace", "c", "r", "Math", "random", "v", "toString", "stripTrailingSlash", "url", "<PERSON><PERSON><PERSON><PERSON>", "window", "applySettingDefaults", "options", "defaults", "db", "dbOptions", "auth", "authOptions", "realtime", "realtimeOptions", "global", "globalOptions", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "DEFAULT_GLOBAL_OPTIONS", "Object", "assign", "accessToken"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nexport function uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nexport function stripTrailingSlash(url) {\n    return url.replace(/\\/$/, '');\n}\nexport const isBrowser = () => typeof window !== 'undefined';\nexport function applySettingDefaults(options, defaults) {\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,OAAO,SAASO,IAAIA,CAAA,EAAG;EACnB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAE;IACxE,IAAIC,CAAC,GAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;MAAEC,CAAC,GAAGJ,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;IACpE,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC,CAAC;AACN;AACA,OAAO,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EACpC,OAAOA,GAAG,CAACR,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACjC;AACA,OAAO,MAAMS,SAAS,GAAGA,CAAA,KAAM,OAAOC,MAAM,KAAK,WAAW;AAC5D,OAAO,SAASC,oBAAoBA,CAACC,OAAO,EAAEC,QAAQ,EAAE;EACpD,MAAM;IAAEC,EAAE,EAAEC,SAAS;IAAEC,IAAI,EAAEC,WAAW;IAAEC,QAAQ,EAAEC,eAAe;IAAEC,MAAM,EAAEC;EAAe,CAAC,GAAGT,OAAO;EACvG,MAAM;IAAEE,EAAE,EAAEQ,kBAAkB;IAAEN,IAAI,EAAEO,oBAAoB;IAAEL,QAAQ,EAAEM,wBAAwB;IAAEJ,MAAM,EAAEK;EAAwB,CAAC,GAAGZ,QAAQ;EAC5I,MAAMlB,MAAM,GAAG;IACXmB,EAAE,EAAEY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,kBAAkB,CAAC,EAAEP,SAAS,CAAC;IACnEC,IAAI,EAAEU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,oBAAoB,CAAC,EAAEN,WAAW,CAAC;IACzEC,QAAQ,EAAEQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,wBAAwB,CAAC,EAAEL,eAAe,CAAC;IACrFC,MAAM,EAAEM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,sBAAsB,CAAC,EAAEJ,aAAa,CAAC;IAC/EO,WAAW,EAAEA,CAAA,KAAMhD,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;MAAE,OAAO,EAAE;IAAE,CAAC;EAClF,CAAC;EACD,IAAIgC,OAAO,CAACgB,WAAW,EAAE;IACrBjC,MAAM,CAACiC,WAAW,GAAGhB,OAAO,CAACgB,WAAW;EAC5C,CAAC,MACI;IACD;IACA,OAAOjC,MAAM,CAACiC,WAAW;EAC7B;EACA,OAAOjC,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}