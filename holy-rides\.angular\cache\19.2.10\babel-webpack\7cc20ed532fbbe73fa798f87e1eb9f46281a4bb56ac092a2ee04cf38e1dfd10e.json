{"ast": null, "code": "import GoTrueAdminApi from './GoTrueAdminApi';\nconst AuthAdminApi = GoTrueAdminApi;\nexport default AuthAdminApi;", "map": {"version": 3, "names": ["GoTrueAdminApi", "AuthAdminApi"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js"], "sourcesContent": ["import GoTrueAdminApi from './GoTrueAdminApi';\nconst AuthAdminApi = GoTrueAdminApi;\nexport default AuthAdminApi;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,MAAMC,YAAY,GAAGD,cAAc;AACnC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}