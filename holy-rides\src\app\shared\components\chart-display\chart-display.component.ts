import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, AfterViewInit } from '@angular/core';
import { NgIf, NgFor } from '@angular/common';
import { Chart, ChartConfiguration, ChartType } from 'chart.js';
import { BaseChartDirective } from 'ng2-charts';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-chart-display',
  standalone: true,
  imports: [
    NgIf,
    NgFor,
    BaseChartDirective,
    MatCardModule
  ],
  templateUrl: './chart-display.component.html',
  styleUrls: ['./chart-display.component.scss']
})
export class ChartDisplayComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() chartType: ChartType = 'bar';
  @Input() chartData: any[] = [];
  @Input() chartLabels: string[] = [];
  @Input() chartTitle: string = '';
  @Input() chartLegend: boolean = true;
  @Input() chartOptions: any = {};
  @Input() chartColors: any[] = [];

  @ViewChild(BaseChartDirective) chart?: BaseChartDirective;

  public chartConfig: ChartConfiguration['data'] = {
    datasets: [],
    labels: []
  };

  public chartPlugins = [];

  constructor() {}

  ngOnInit(): void {
    this.updateChartConfig();
  }

  ngAfterViewInit(): void {
    // Register Chart.js components
    Chart.register({
      id: 'chartjs-plugin-datalabels',
      beforeDraw: (chart) => {
        const ctx = chart.ctx;
        ctx.save();
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, chart.width, chart.height);
        ctx.restore();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['chartData'] || changes['chartLabels']) {
      this.updateChartConfig();
      this.updateChart();
    }
  }

  private updateChartConfig(): void {
    this.chartConfig = {
      labels: this.chartLabels,
      datasets: this.chartData
    };
  }

  private updateChart(): void {
    if (this.chart) {
      this.chart.update();
    }
  }
}
