{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { RidePaymentComponent } from './ride-payment.component';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\ndescribe('RidePaymentComponent', () => {\n  let component;\n  let fixture;\n  let paymentServiceSpy;\n  let rideServiceSpy;\n  let authServiceSpy;\n  let snackBarSpy;\n  const mockRide = {\n    id: '123',\n    rider_id: 'rider-123',\n    driver_id: 'driver-123',\n    pickup_location: '123 Main St',\n    dropoff_location: '456 Elm St',\n    pickup_time: new Date().toISOString(),\n    status: 'completed',\n    payment_status: 'pending',\n    fare: 25.00,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString()\n  };\n  const mockUpdatedRide = {\n    ...mockRide,\n    payment_status: 'paid',\n    payment_id: 'pi_123456789'\n  };\n  beforeEach(/*#__PURE__*/_asyncToGenerator(function* () {\n    paymentServiceSpy = jasmine.createSpyObj('PaymentService', ['updateRidePaymentStatus', 'estimateFare']);\n    rideServiceSpy = jasmine.createSpyObj('RideService', ['updateRide', 'getRide']);\n    const mockSupabase = {\n      functions: {\n        invoke: jasmine.createSpy().and.returnValue(Promise.resolve({\n          data: {\n            client_secret: 'cs_123456789'\n          },\n          error: null\n        }))\n      }\n    };\n    authServiceSpy = jasmine.createSpyObj('AuthService', [], {\n      supabase: mockSupabase\n    });\n    snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);\n    yield TestBed.configureTestingModule({\n      imports: [RidePaymentComponent],\n      providers: [{\n        provide: PaymentService,\n        useValue: paymentServiceSpy\n      }, {\n        provide: RideService,\n        useValue: rideServiceSpy\n      }, {\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: MatSnackBar,\n        useValue: snackBarSpy\n      }],\n      schemas: [NO_ERRORS_SCHEMA] // Ignore Angular Material components\n    }).compileComponents();\n    fixture = TestBed.createComponent(RidePaymentComponent);\n    component = fixture.componentInstance;\n    component.ride = mockRide;\n    // Mock the getRide method to return the updated ride\n    rideServiceSpy.getRide.and.returnValue(Promise.resolve(mockUpdatedRide));\n    rideServiceSpy.updateRide.and.returnValue(Promise.resolve(true));\n    // Mock Stripe and card element\n    component.stripe = {\n      createPaymentMethod: jasmine.createSpy().and.returnValue(Promise.resolve({\n        paymentMethod: {\n          id: 'pm_123456789'\n        },\n        error: null\n      })),\n      confirmCardPayment: jasmine.createSpy().and.returnValue(Promise.resolve({\n        error: null,\n        paymentIntent: {\n          id: 'pi_123456789',\n          status: 'succeeded'\n        }\n      }))\n    };\n    component.card = {\n      mount: jasmine.createSpy(),\n      on: jasmine.createSpy()\n    };\n    component.cardComplete = true;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should update ride payment status after successful payment', /*#__PURE__*/_asyncToGenerator(function* () {\n    // Mock the Supabase functions invoke\n    authServiceSpy.supabase = {\n      functions: {\n        invoke: jasmine.createSpy().and.returnValue(Promise.resolve({\n          data: {\n            client_secret: 'cs_123456789'\n          },\n          error: null\n        }))\n      }\n    };\n    // Call the processPayment method\n    yield component.processPayment();\n    // Verify that updateRide was called with the correct parameters\n    expect(rideServiceSpy.updateRide).toHaveBeenCalledWith(mockRide.id, {\n      payment_status: 'paid',\n      payment_id: 'pi_123456789',\n      amount: mockRide.fare\n    });\n    // Verify that getRide was called to refresh the ride data\n    expect(rideServiceSpy.getRide).toHaveBeenCalledWith(mockRide.id);\n    // Verify that the snackBar was opened with a success message\n    expect(snackBarSpy.open).toHaveBeenCalledWith('Payment processed successfully!', 'Close', {\n      duration: 3000\n    });\n    // Verify that the ride was updated in the component\n    expect(component.ride).toEqual(mockUpdatedRide);\n  }));\n});", "map": {"version": 3, "names": ["TestBed", "RidePaymentComponent", "PaymentService", "RideService", "AuthService", "MatSnackBar", "NO_ERRORS_SCHEMA", "describe", "component", "fixture", "paymentServiceSpy", "rideServiceSpy", "authServiceSpy", "snackBarSpy", "mockRide", "id", "rider_id", "driver_id", "pickup_location", "dropoff_location", "pickup_time", "Date", "toISOString", "status", "payment_status", "fare", "created_at", "updated_at", "mockUpdatedRide", "payment_id", "beforeEach", "_asyncToGenerator", "jasmine", "createSpyObj", "mockSupabase", "functions", "invoke", "createSpy", "and", "returnValue", "Promise", "resolve", "data", "client_secret", "error", "supabase", "configureTestingModule", "imports", "providers", "provide", "useValue", "schemas", "compileComponents", "createComponent", "componentInstance", "ride", "getRide", "updateRide", "stripe", "createPaymentMethod", "paymentMethod", "confirmCardPayment", "paymentIntent", "card", "mount", "on", "cardComplete", "detectChanges", "it", "expect", "toBeTruthy", "processPayment", "toHaveBeenCalledWith", "amount", "open", "duration", "toEqual"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\features\\dashboard\\rider\\ride-payment\\ride-payment.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { RidePaymentComponent } from './ride-payment.component';\nimport { PaymentService } from '../../../../core/services/payment.service';\nimport { RideService } from '../../../../core/services/ride.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { Ride } from '../../../../core/models/ride.model';\nimport { of } from 'rxjs';\nimport { NO_ERRORS_SCHEMA } from '@angular/core';\n\ndescribe('RidePaymentComponent', () => {\n  let component: RidePaymentComponent;\n  let fixture: ComponentFixture<RidePaymentComponent>;\n  let paymentServiceSpy: jasmine.SpyObj<PaymentService>;\n  let rideServiceSpy: jasmine.SpyObj<RideService>;\n  let authServiceSpy: jasmine.SpyObj<AuthService>;\n  let snackBarSpy: jasmine.SpyObj<MatSnackBar>;\n\n  const mockRide: Ride = {\n    id: '123',\n    rider_id: 'rider-123',\n    driver_id: 'driver-123',\n    pickup_location: '123 Main St',\n    dropoff_location: '456 Elm St',\n    pickup_time: new Date().toISOString(),\n    status: 'completed',\n    payment_status: 'pending',\n    fare: 25.00,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString()\n  };\n\n  const mockUpdatedRide: Ride = {\n    ...mockRide,\n    payment_status: 'paid',\n    payment_id: 'pi_123456789'\n  };\n\n  beforeEach(async () => {\n    paymentServiceSpy = jasmine.createSpyObj('PaymentService', [\n      'updateRidePaymentStatus',\n      'estimateFare'\n    ]);\n\n    rideServiceSpy = jasmine.createSpyObj('RideService', [\n      'updateRide',\n      'getRide'\n    ]);\n\n    const mockSupabase = {\n      functions: {\n        invoke: jasmine.createSpy().and.returnValue(Promise.resolve({\n          data: { client_secret: 'cs_123456789' },\n          error: null\n        }))\n      }\n    };\n\n    authServiceSpy = jasmine.createSpyObj('AuthService', [], {\n      supabase: mockSupabase\n    });\n\n    snackBarSpy = jasmine.createSpyObj('MatSnackBar', ['open']);\n\n    await TestBed.configureTestingModule({\n      imports: [RidePaymentComponent],\n      providers: [\n        { provide: PaymentService, useValue: paymentServiceSpy },\n        { provide: RideService, useValue: rideServiceSpy },\n        { provide: AuthService, useValue: authServiceSpy },\n        { provide: MatSnackBar, useValue: snackBarSpy }\n      ],\n      schemas: [NO_ERRORS_SCHEMA] // Ignore Angular Material components\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(RidePaymentComponent);\n    component = fixture.componentInstance;\n    component.ride = mockRide;\n\n    // Mock the getRide method to return the updated ride\n    rideServiceSpy.getRide.and.returnValue(Promise.resolve(mockUpdatedRide));\n    rideServiceSpy.updateRide.and.returnValue(Promise.resolve(true));\n\n    // Mock Stripe and card element\n    component.stripe = {\n      createPaymentMethod: jasmine.createSpy().and.returnValue(Promise.resolve({\n        paymentMethod: { id: 'pm_123456789' },\n        error: null\n      })),\n      confirmCardPayment: jasmine.createSpy().and.returnValue(Promise.resolve({\n        error: null,\n        paymentIntent: { id: 'pi_123456789', status: 'succeeded' }\n      }))\n    };\n    component.card = {\n      mount: jasmine.createSpy(),\n      on: jasmine.createSpy()\n    };\n    component.cardComplete = true;\n\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should update ride payment status after successful payment', async () => {\n    // Mock the Supabase functions invoke\n    authServiceSpy.supabase = {\n      functions: {\n        invoke: jasmine.createSpy().and.returnValue(Promise.resolve({\n          data: { client_secret: 'cs_123456789' },\n          error: null\n        }))\n      }\n    };\n\n    // Call the processPayment method\n    await component.processPayment();\n\n    // Verify that updateRide was called with the correct parameters\n    expect(rideServiceSpy.updateRide).toHaveBeenCalledWith(mockRide.id, {\n      payment_status: 'paid',\n      payment_id: 'pi_123456789',\n      amount: mockRide.fare\n    });\n\n    // Verify that getRide was called to refresh the ride data\n    expect(rideServiceSpy.getRide).toHaveBeenCalledWith(mockRide.id);\n\n    // Verify that the snackBar was opened with a success message\n    expect(snackBarSpy.open).toHaveBeenCalledWith(\n      'Payment processed successfully!',\n      'Close',\n      { duration: 3000 }\n    );\n\n    // Verify that the ride was updated in the component\n    expect(component.ride).toEqual(mockUpdatedRide);\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,cAAc,QAAQ,2CAA2C;AAC1E,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,WAAW,QAAQ,wCAAwC;AACpE,SAASC,WAAW,QAAQ,6BAA6B;AAGzD,SAASC,gBAAgB,QAAQ,eAAe;AAEhDC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EACnD,IAAIC,iBAAiD;EACrD,IAAIC,cAA2C;EAC/C,IAAIC,cAA2C;EAC/C,IAAIC,WAAwC;EAE5C,MAAMC,QAAQ,GAAS;IACrBC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE,YAAY;IACvBC,eAAe,EAAE,aAAa;IAC9BC,gBAAgB,EAAE,YAAY;IAC9BC,WAAW,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACrCC,MAAM,EAAE,WAAW;IACnBC,cAAc,EAAE,SAAS;IACzBC,IAAI,EAAE,KAAK;IACXC,UAAU,EAAE,IAAIL,IAAI,EAAE,CAACC,WAAW,EAAE;IACpCK,UAAU,EAAE,IAAIN,IAAI,EAAE,CAACC,WAAW;GACnC;EAED,MAAMM,eAAe,GAAS;IAC5B,GAAGd,QAAQ;IACXU,cAAc,EAAE,MAAM;IACtBK,UAAU,EAAE;GACb;EAEDC,UAAU,cAAAC,iBAAA,CAAC,aAAW;IACpBrB,iBAAiB,GAAGsB,OAAO,CAACC,YAAY,CAAC,gBAAgB,EAAE,CACzD,yBAAyB,EACzB,cAAc,CACf,CAAC;IAEFtB,cAAc,GAAGqB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CACnD,YAAY,EACZ,SAAS,CACV,CAAC;IAEF,MAAMC,YAAY,GAAG;MACnBC,SAAS,EAAE;QACTC,MAAM,EAAEJ,OAAO,CAACK,SAAS,EAAE,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC;UAC1DC,IAAI,EAAE;YAAEC,aAAa,EAAE;UAAc,CAAE;UACvCC,KAAK,EAAE;SACR,CAAC;;KAEL;IAEDhC,cAAc,GAAGoB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,EAAE,EAAE;MACvDY,QAAQ,EAAEX;KACX,CAAC;IAEFrB,WAAW,GAAGmB,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IAE3D,MAAMjC,OAAO,CAAC8C,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAAC9C,oBAAoB,CAAC;MAC/B+C,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE/C,cAAc;QAAEgD,QAAQ,EAAExC;MAAiB,CAAE,EACxD;QAAEuC,OAAO,EAAE9C,WAAW;QAAE+C,QAAQ,EAAEvC;MAAc,CAAE,EAClD;QAAEsC,OAAO,EAAE7C,WAAW;QAAE8C,QAAQ,EAAEtC;MAAc,CAAE,EAClD;QAAEqC,OAAO,EAAE5C,WAAW;QAAE6C,QAAQ,EAAErC;MAAW,CAAE,CAChD;MACDsC,OAAO,EAAE,CAAC7C,gBAAgB,CAAC,CAAC;KAC7B,CAAC,CAAC8C,iBAAiB,EAAE;IAEtB3C,OAAO,GAAGT,OAAO,CAACqD,eAAe,CAACpD,oBAAoB,CAAC;IACvDO,SAAS,GAAGC,OAAO,CAAC6C,iBAAiB;IACrC9C,SAAS,CAAC+C,IAAI,GAAGzC,QAAQ;IAEzB;IACAH,cAAc,CAAC6C,OAAO,CAAClB,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAACb,eAAe,CAAC,CAAC;IACxEjB,cAAc,CAAC8C,UAAU,CAACnB,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC;IAEhE;IACAjC,SAAS,CAACkD,MAAM,GAAG;MACjBC,mBAAmB,EAAE3B,OAAO,CAACK,SAAS,EAAE,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC;QACvEmB,aAAa,EAAE;UAAE7C,EAAE,EAAE;QAAc,CAAE;QACrC6B,KAAK,EAAE;OACR,CAAC,CAAC;MACHiB,kBAAkB,EAAE7B,OAAO,CAACK,SAAS,EAAE,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC;QACtEG,KAAK,EAAE,IAAI;QACXkB,aAAa,EAAE;UAAE/C,EAAE,EAAE,cAAc;UAAEQ,MAAM,EAAE;QAAW;OACzD,CAAC;KACH;IACDf,SAAS,CAACuD,IAAI,GAAG;MACfC,KAAK,EAAEhC,OAAO,CAACK,SAAS,EAAE;MAC1B4B,EAAE,EAAEjC,OAAO,CAACK,SAAS;KACtB;IACD7B,SAAS,CAAC0D,YAAY,GAAG,IAAI;IAE7BzD,OAAO,CAAC0D,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC7D,SAAS,CAAC,CAAC8D,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,4DAA4D,eAAArC,iBAAA,CAAE,aAAW;IAC1E;IACAnB,cAAc,CAACiC,QAAQ,GAAG;MACxBV,SAAS,EAAE;QACTC,MAAM,EAAEJ,OAAO,CAACK,SAAS,EAAE,CAACC,GAAG,CAACC,WAAW,CAACC,OAAO,CAACC,OAAO,CAAC;UAC1DC,IAAI,EAAE;YAAEC,aAAa,EAAE;UAAc,CAAE;UACvCC,KAAK,EAAE;SACR,CAAC;;KAEL;IAED;IACA,MAAMpC,SAAS,CAAC+D,cAAc,EAAE;IAEhC;IACAF,MAAM,CAAC1D,cAAc,CAAC8C,UAAU,CAAC,CAACe,oBAAoB,CAAC1D,QAAQ,CAACC,EAAE,EAAE;MAClES,cAAc,EAAE,MAAM;MACtBK,UAAU,EAAE,cAAc;MAC1B4C,MAAM,EAAE3D,QAAQ,CAACW;KAClB,CAAC;IAEF;IACA4C,MAAM,CAAC1D,cAAc,CAAC6C,OAAO,CAAC,CAACgB,oBAAoB,CAAC1D,QAAQ,CAACC,EAAE,CAAC;IAEhE;IACAsD,MAAM,CAACxD,WAAW,CAAC6D,IAAI,CAAC,CAACF,oBAAoB,CAC3C,iCAAiC,EACjC,OAAO,EACP;MAAEG,QAAQ,EAAE;IAAI,CAAE,CACnB;IAED;IACAN,MAAM,CAAC7D,SAAS,CAAC+C,IAAI,CAAC,CAACqB,OAAO,CAAChD,eAAe,CAAC;EACjD,CAAC,EAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}