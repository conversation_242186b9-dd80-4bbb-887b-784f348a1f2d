{"ast": null, "code": "// Generated by genversion.\nexport const version = '2.61.0';", "map": {"version": 3, "names": ["version"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/version.js"], "sourcesContent": ["// Generated by genversion.\nexport const version = '2.61.0';\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,OAAO,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}