import { Component, Input, OnInit, Inject, ViewChild, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Ride, PaymentStatus } from '../../../core/models/ride.model';
import { User, UserRole } from '../../../core/models/user.model';
import { RideService } from '../../../core/services/ride.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { RatingService } from '../../../core/services/rating.service';
import { PaymentService } from '../../../core/services/payment.service';
import { RatingFormComponent } from '../rating-form/rating-form.component';
import { RatingDisplayComponent } from '../rating-display/rating-display.component';
import { RideChatComponent } from '../ride-chat/ride-chat.component';
import { MapDisplayComponent } from '../map-display/map-display.component';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

@Component({
  selector: 'app-ride-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatTabsModule,
    MatSnackBarModule,
    MatInputModule,
    MatFormFieldModule,
    RatingFormComponent,
    RatingDisplayComponent,
    RideChatComponent,
    MapDisplayComponent
  ],
  templateUrl: './ride-detail.component.html',
  styleUrls: ['./ride-detail.component.scss']
})
export class RideDetailComponent implements OnInit {
  @Input() rideId!: string;
  @Input() onClose: () => void = () => {};
  @Output() paymentRequested = new EventEmitter<Ride>();
  @Output() rideUpdated = new EventEmitter<Ride>();
  loadingUsers: boolean = false;
  ride: Ride | null = null;
  currentUser: User | null = null;
  otherUser: User | null = null;
  isRider: boolean = false;
  isDriver: boolean = false;
  canRate: boolean = false;
  hasRated: boolean = false;
  filteredUsers: User[] = [];
  @ViewChild('userPaginator') userPaginator!: MatPaginator;

  userDisplayedColumns: string[] = ['email', 'full_name', 'role', 'created_at', 'status', 'actions'];
  userRoleFilter: UserRole | '' = '';
  userSearchTerm: string = '';
  @ViewChild('userSort') userSort!: MatSort;

  users: User[] = [];

  // Admin fare update
  fareControl = new FormControl<number | null>(null, [
    Validators.required,
    Validators.min(0),
    Validators.pattern(/^\d+(\.\d{1,2})?$/) // Allow decimal numbers with up to 2 decimal places
  ]);
  isAdmin: boolean = false;
  updatingFare: boolean = false;
  updatingPaymentStatus: boolean = false;
  constructor(
    private rideService: RideService,
    private userService: UserService,
    private authService: AuthService,
    private ratingService: RatingService,
    private paymentService: PaymentService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadRideDetails();
    this.checkIfAdmin();
    this.loadUsers()
  }

  async checkIfAdmin(): Promise<void> {
    const userRole = await this.authService.getUserRole();
    this.isAdmin = userRole === 'admin';
  }
  async loadUsers() {
    this.loadingUsers = true;
    try {
      this.users = await this.userService.getAllUsers();
      this.applyUserFilters();
    } catch (error) {
      console.error('Error loading users:', error);
      this.snackBar.open('Failed to load users', 'Close', { duration: 3000 });
    } finally {
      this.loadingUsers = false;
    }
  }
  async loadRideDetails(): Promise<void> {
    if (!this.rideId) return;

    try {
      // Get current user
      this.currentUser = await this.authService.getCurrentUser();
      if (!this.currentUser) return;

      // Get ride details
      this.ride = await this.rideService.getRide(this.rideId);
      if (!this.ride) return;

      // Set fare control value if ride has a fare
      if (this.ride.fare !== undefined && this.ride.fare !== null) {
        this.fareControl.setValue(this.ride.fare);
      }

      // Determine if current user is rider or driver
      this.isRider = this.currentUser.id === this.ride.rider_id;
      this.isDriver = this.ride.driver_id ? this.currentUser.id === this.ride.driver_id : false;

      // Get the other user (rider or driver)
      const otherUserId = this.isRider ? this.ride.driver_id : this.ride.rider_id;
      if (otherUserId) {
        this.otherUser = await this.userService.getUserById(otherUserId);
      }

      // Check if user can rate this ride
      this.canRate = this.canUserRate();

      // Check if user has already rated this ride
      if (this.canRate && this.otherUser) {
        this.hasRated = await this.ratingService.hasUserRated(
          this.ride.id,
          this.currentUser.id,
          this.otherUser.id
        );
      }
    } catch (error) {
      console.error('Error loading ride details:', error);
      this.snackBar.open('Failed to load ride details', 'Close', { duration: 3000 });
    }
  }
  applyUserFilters() {
    let filtered = [...this.users];

    // Apply role filter
    if (this.userRoleFilter) {
      filtered = filtered.filter(user => user.role === this.userRoleFilter);
    }

    // Apply search filter
    if (this.userSearchTerm) {
      const searchTerm = this.userSearchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.email.toLowerCase().includes(searchTerm) ||
        (user.full_name && user.full_name.toLowerCase().includes(searchTerm))
      );
    }

    this.filteredUsers = filtered;

    // Reset pagination and sorting if they exist
    if (this.userPaginator) {
      this.userPaginator.firstPage();
    }

    if (this.userSort) {
      this.userSort.sort({ id: '', start: 'asc', disableClear: false });
    }
  }
  canUserRate(): boolean {
    if (!this.ride || !this.currentUser) return false;

    // Can rate if:
    // 1. Ride is completed
    // 2. User is either the rider or driver
    // 3. There is another user to rate (driver for rider, rider for driver)
    return (
      this.ride.status === 'completed' &&
      (this.currentUser.id === this.ride.rider_id || this.currentUser.id === this.ride.driver_id) &&
      (this.isRider ? !!this.ride.driver_id : true)
    );
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  formatStatus(status: string): string {
    return status.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  }

  async onRatingSubmitted(success: boolean): Promise<void> {
    if (success) {
      this.hasRated = true;
      this.snackBar.open('Rating submitted successfully', 'Close', { duration: 3000 });
    }
  }

  onRatingCancelled(): void {
    // Handle rating cancellation if needed
  }

  getUserName(userId: string): string {
    if (!userId) return 'N/A';

    return this.users.filter(x=>x.id===userId)[0]!.full_name || '';
  }

  async updateFare(): Promise<void> {
    if (!this.ride || this.fareControl.invalid) return;

    this.updatingFare = true;

    try {
      const fareValue = this.fareControl.value;

      if (fareValue === null) {
        this.snackBar.open('Please enter a valid fare amount', 'Close', { duration: 3000 });
        return;
      }

      const success = await this.rideService.updateRide(this.ride.id, { fare: fareValue });

      if (success) {
        this.snackBar.open('Fare updated successfully', 'Close', { duration: 3000 });
        // Refresh ride details
        this.ride = await this.rideService.getRide(this.ride.id);
        // Emit event to notify parent component
        if (this.ride) {
          this.rideUpdated.emit(this.ride);
        }
      } else {
        this.snackBar.open('Failed to update fare', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error updating fare:', error);
      this.snackBar.open('An error occurred while updating the fare', 'Close', { duration: 3000 });
    } finally {
      this.updatingFare = false;
    }
  }

  viewPayment(ride: Ride): void {
    this.paymentRequested.emit(ride);
  }

  async markPaymentCompleted(): Promise<void> {
    if (!this.ride) return;

    this.updatingPaymentStatus = true;

    try {
      // Update the payment status to 'completed'
      const success = await this.paymentService.updateRidePaymentStatus(this.ride.id, 'completed');

      if (success) {
        this.snackBar.open('Payment status marked as completed', 'Close', { duration: 3000 });

        // Refresh ride details to show updated status
        this.ride = await this.rideService.getRide(this.ride.id);

        // Emit event to notify parent component
        if (this.ride) {
          this.rideUpdated.emit(this.ride);
        }
      } else {
        this.snackBar.open('Failed to update payment status', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      this.snackBar.open('An error occurred while updating the payment status', 'Close', { duration: 3000 });
    } finally {
      this.updatingPaymentStatus = false;
    }
  }
}
