import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AuthService } from '../../../../core/services/auth.service';

interface PaymentMethod {
  id: string;
  brand: string;
  last4: string;
  expMonth: number;
  expYear: number;
  isDefault: boolean;
}

@Component({
  selector: 'app-payment-methods',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatListModule,
    MatProgressSpinnerModule
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Payment Methods</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngIf="loading" class="loading-container">
          <mat-spinner diameter="40"></mat-spinner>
          <p>Loading payment methods...</p>
        </div>

        <div *ngIf="!loading">
          <div *ngIf="paymentMethods.length === 0" class="no-methods">
            <p>You don't have any payment methods yet.</p>
          </div>

          <mat-list *ngIf="paymentMethods.length > 0">
            <mat-list-item *ngFor="let method of paymentMethods">
              <div class="payment-method-item">
                <div class="payment-method-info">
                  <mat-icon>credit_card</mat-icon>
                  <span>{{ method.brand }} •••• {{ method.last4 }}</span>
                  <span class="expiry">Expires {{ method.expMonth }}/{{ method.expYear }}</span>
                  <span *ngIf="method.isDefault" class="default-badge">Default</span>
                </div>
                <div class="payment-method-actions">
                  <button mat-icon-button color="primary" *ngIf="!method.isDefault"
                          (click)="setDefaultPaymentMethod(method.id)" matTooltip="Set as default">
                    <mat-icon>star_outline</mat-icon>
                  </button>
                  <button mat-icon-button color="warn" (click)="removePaymentMethod(method.id)" matTooltip="Remove">
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </div>
            </mat-list-item>
          </mat-list>

          <mat-divider *ngIf="paymentMethods.length > 0" class="divider"></mat-divider>

          <div class="add-payment-section">
            <h3>Add New Payment Method</h3>
            <form [formGroup]="paymentForm" (ngSubmit)="addPaymentMethod()">
              <mat-form-field appearance="outline">
                <mat-label>Card Number</mat-label>
                <input matInput formControlName="cardNumber" placeholder="1234 5678 9012 3456">
                <mat-error *ngIf="paymentForm.get('cardNumber')?.errors?.['required']">
                  Card number is required
                </mat-error>
                <mat-error *ngIf="paymentForm.get('cardNumber')?.errors?.['pattern']">
                  Invalid card number
                </mat-error>
              </mat-form-field>

              <div class="form-row">
                <mat-form-field appearance="outline">
                  <mat-label>Expiration Month</mat-label>
                  <input matInput type="number" formControlName="expMonth" placeholder="MM" min="1" max="12">
                  <mat-error *ngIf="paymentForm.get('expMonth')?.errors?.['required']">
                    Expiration month is required
                  </mat-error>
                  <mat-error *ngIf="paymentForm.get('expMonth')?.errors?.['min'] || paymentForm.get('expMonth')?.errors?.['max']">
                    Month must be between 1 and 12
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline">
                  <mat-label>Expiration Year</mat-label>
                  <input matInput type="number" formControlName="expYear" placeholder="YYYY" min="2023">
                  <mat-error *ngIf="paymentForm.get('expYear')?.errors?.['required']">
                    Expiration year is required
                  </mat-error>
                  <mat-error *ngIf="paymentForm.get('expYear')?.errors?.['min']">
                    Year must be {{ currentYear }} or later
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline">
                <mat-label>CVV</mat-label>
                <input matInput formControlName="cvv" placeholder="123">
                <mat-error *ngIf="paymentForm.get('cvv')?.errors?.['required']">
                  CVV is required
                </mat-error>
                <mat-error *ngIf="paymentForm.get('cvv')?.errors?.['pattern']">
                  CVV must be 3 or 4 digits
                </mat-error>
              </mat-form-field>

              <div class="button-container">
                <button mat-raised-button color="primary" type="submit" [disabled]="paymentForm.invalid || submitting">
                  {{ submitting ? 'Adding...' : 'Add Payment Method' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    :host {
      display: block;
      margin: 20px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }

    .no-methods {
      text-align: center;
      padding: 20px;
      color: rgba(0, 0, 0, 0.6);
    }

    .payment-method-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 8px 0;
    }

    .payment-method-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .expiry {
      color: rgba(0, 0, 0, 0.6);
      font-size: 0.9em;
      margin-left: 8px;
    }

    .default-badge {
      background-color: #4caf50;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      margin-left: 8px;
    }

    .divider {
      margin: 20px 0;
    }

    .add-payment-section {
      margin-top: 20px;
    }

    form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 500px;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .button-container {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  `]
})
export class PaymentMethodsComponent implements OnInit {
  paymentMethods: PaymentMethod[] = [];
  paymentForm: FormGroup;
  loading = false;
  submitting = false;
  currentYear = new Date().getFullYear();

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.paymentForm = this.formBuilder.group({
      cardNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{16}$/)]],
      expMonth: ['', [Validators.required, Validators.min(1), Validators.max(12)]],
      expYear: ['', [Validators.required, Validators.min(this.currentYear)]],
      cvv: ['', [Validators.required, Validators.pattern(/^[0-9]{3,4}$/)]]
    });
  }

  ngOnInit(): void {
    this.loadPaymentMethods();
  }

  loadPaymentMethods(): void {
    this.loading = true;
    
    // Mock implementation - in a real app, you would fetch from your backend
    setTimeout(() => {
      // For demo purposes, we'll create some mock payment methods
      this.paymentMethods = [
        {
          id: 'pm_1',
          brand: 'Visa',
          last4: '4242',
          expMonth: 12,
          expYear: 2025,
          isDefault: true
        },
        {
          id: 'pm_2',
          brand: 'Mastercard',
          last4: '5555',
          expMonth: 10,
          expYear: 2024,
          isDefault: false
        }
      ];
      this.loading = false;
    }, 1000);
  }

  addPaymentMethod(): void {
    if (this.paymentForm.invalid) return;

    this.submitting = true;

    // Mock implementation - in a real app, you would call Square API
    setTimeout(() => {
      const formValue = this.paymentForm.value;
      
      // Create a new payment method
      const newMethod: PaymentMethod = {
        id: `pm_${Math.random().toString(36).substring(2, 9)}`,
        brand: this.getCardBrand(formValue.cardNumber),
        last4: formValue.cardNumber.slice(-4),
        expMonth: formValue.expMonth,
        expYear: formValue.expYear,
        isDefault: this.paymentMethods.length === 0 // Make default if it's the first one
      };

      this.paymentMethods.push(newMethod);
      this.paymentForm.reset();
      this.submitting = false;
      this.snackBar.open('Payment method added successfully', 'Close', { duration: 3000 });
    }, 1500);
  }

  setDefaultPaymentMethod(id: string): void {
    // Update default status
    this.paymentMethods = this.paymentMethods.map(method => ({
      ...method,
      isDefault: method.id === id
    }));

    this.snackBar.open('Default payment method updated', 'Close', { duration: 3000 });
  }

  removePaymentMethod(id: string): void {
    const isDefault = this.paymentMethods.find(m => m.id === id)?.isDefault;
    
    // Remove the payment method
    this.paymentMethods = this.paymentMethods.filter(method => method.id !== id);

    // If we removed the default method and there are other methods, make the first one default
    if (isDefault && this.paymentMethods.length > 0) {
      this.paymentMethods[0].isDefault = true;
    }

    this.snackBar.open('Payment method removed', 'Close', { duration: 3000 });
  }

  private getCardBrand(cardNumber: string): string {
    // Simple logic to determine card brand based on first digit
    const firstDigit = cardNumber.charAt(0);
    
    switch (firstDigit) {
      case '4':
        return 'Visa';
      case '5':
        return 'Mastercard';
      case '3':
        return 'Amex';
      case '6':
        return 'Discover';
      default:
        return 'Card';
    }
  }
}
