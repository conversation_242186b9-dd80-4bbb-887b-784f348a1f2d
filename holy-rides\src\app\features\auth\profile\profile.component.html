<div class="profile-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>Profile</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" readonly>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Full Name</mat-label>
          <input matInput formControlName="full_name" placeholder="Enter your full name">
          <mat-error *ngIf="profileForm.get('full_name')?.errors?.['required']">Full name is required</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Phone Number</mat-label>
          <input matInput formControlName="phone" placeholder="Enter your phone number">
          <mat-error *ngIf="profileForm.get('phone')?.errors?.['required']">Phone number is required</mat-error>
        </mat-form-field>

        <div class="button-container">
          <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || loading">
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
