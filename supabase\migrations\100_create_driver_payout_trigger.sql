-- Create a function to automatically create a driver payout when a ride's payment_status is updated to 'paid'
CREATE OR REPLACE FUNCTION create_driver_payout_on_payment()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if payment_status is being updated to 'paid'
  IF (OLD.payment_status IS DISTINCT FROM NEW.payment_status AND NEW.payment_status = 'paid') THEN
    -- Check if driver_id exists and amount is set
    IF (NEW.driver_id IS NOT NULL AND NEW.amount IS NOT NULL) THEN
      -- Check if a payout already exists for this ride to prevent duplicates
      IF NOT EXISTS (SELECT 1 FROM driver_payouts WHERE ride_id = NEW.id) THEN
        -- Insert a new driver payout record
        INSERT INTO driver_payouts (
          driver_id,
          ride_id,
          amount,
          status,
          payout_type
        ) VALUES (
          NEW.driver_id,
          NEW.id,
          NEW.amount,
          'pending',
          'amount'
        );
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger that fires after an update on the rides table
DROP TRIGGER IF EXISTS create_driver_payout_trigger ON rides;
CREATE TRIGGER create_driver_payout_trigger
  AFTER UPDATE ON rides
  FOR EACH ROW
  EXECUTE PROCEDURE create_driver_payout_on_payment();

-- Add a comment to explain the trigger
COMMENT ON TRIGGER create_driver_payout_trigger ON rides IS 'Creates a driver payout record when a ride payment status is updated to paid';
