import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class GoogleMapsLoaderService {
  private isLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  /**
   * Load the Google Maps API script
   */
  loadGoogleMapsApi(): Promise<void> {
    // Return if not in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      return Promise.resolve();
    }

    // Return existing promise if already loading
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    // Return resolved promise if already loaded
    if (this.isLoaded) {
      return Promise.resolve();
    }

    // Create a new loading promise
    this.loadingPromise = new Promise<void>((resolve, reject) => {
      // Check if the API is already loaded
      if (window.google && window.google.maps) {
        this.isLoaded = true;
        resolve();
        return;
      }

      // Create a callback function name
      const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1000000)}`;

      // Add the callback to the window object
      (window as any)[callbackName] = () => {
        this.isLoaded = true;
        resolve();
        delete (window as any)[callbackName];
      };

      // Create the script element
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${environment.googleMapsApiKey}&libraries=places&callback=${callbackName}`;
      script.async = true;
      script.defer = true;
      script.onerror = (error) => {
        reject(new Error(`Failed to load Google Maps API: ${error}`));
        delete (window as any)[callbackName];
      };

      // Append the script to the document
      document.head.appendChild(script);
    });

    return this.loadingPromise;
  }

  /**
   * Check if the Google Maps API is loaded
   */
  isGoogleMapsLoaded(): boolean {
    return this.isLoaded;
  }
}
