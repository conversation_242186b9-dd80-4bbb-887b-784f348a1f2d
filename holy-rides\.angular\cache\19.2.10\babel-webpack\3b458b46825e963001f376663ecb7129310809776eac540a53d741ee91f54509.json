{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { AuthService } from './auth.service';\nlet UserService = class UserService {\n  authService;\n  supabase;\n  usersSubject = new BehaviorSubject([]);\n  users$ = this.usersSubject.asObservable();\n  constructor(authService) {\n    this.authService = authService;\n    this.supabase = authService.supabase;\n  }\n  getAllUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this.supabase.from('profiles').select('*').order('created_at', {\n          ascending: false\n        });\n        if (error) {\n          throw error;\n        }\n        _this.usersSubject.next(data);\n        return data;\n      } catch (error) {\n        console.error('Error fetching users:', error);\n        return [];\n      }\n    })();\n  }\n  getUsersByRole(role) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this2.supabase.from('profiles').select('*').eq('role', role).order('created_at', {\n          ascending: false\n        });\n        if (error) {\n          throw error;\n        }\n        return data;\n      } catch (error) {\n        console.error(`Error fetching ${role}s:`, error);\n        return [];\n      }\n    })();\n  }\n  approveDriver(userId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          error\n        } = yield _this3.supabase.from('profiles').update({\n          is_approved: \"TRUE\"\n          //  is_active: true // Automatically activate when approved\n        }).eq('id', userId);\n        if (error) {\n          throw error;\n        }\n        // Update the local users list\n        const currentUsers = _this3.usersSubject.value;\n        const updatedUsers = currentUsers.map(user => user.id === userId ? {\n          ...user,\n          is_approved: true,\n          is_active: true\n        } : user);\n        _this3.usersSubject.next(updatedUsers);\n        return true;\n      } catch (error) {\n        console.error('Error approving driver:', error);\n        return false;\n      }\n    })();\n  }\n  updateUserStatus(userId, isActive) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // First get the user's current data\n        const {\n          data: user,\n          error: userError\n        } = yield _this4.supabase.from('profiles').select('role, is_approved').eq('id', userId).single();\n        if (userError) throw userError;\n        // If trying to activate a rider/driver who isn't approved, prevent it\n        if (isActive && user.role !== 'admin' && !user.is_approved) {\n          throw new Error('Cannot activate unapproved user');\n        }\n        const {\n          error\n        } = yield _this4.supabase.from('profiles').update({\n          is_approved: isActive,\n          updated_at: new Date().toISOString()\n        }).eq('id', userId);\n        if (error) throw error;\n        // Update the local users list\n        const currentUsers = _this4.usersSubject.value;\n        const updatedUsers = currentUsers.map(u => u.id === userId ? {\n          ...u,\n          is_approved: isActive\n        } : u);\n        _this4.usersSubject.next(updatedUsers);\n        return true;\n      } catch (error) {\n        console.error('Error updating user status:', error);\n        return false;\n      }\n    })();\n  }\n  getUserById(userId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this5.supabase.from('profiles').select('*').eq('id', userId).single();\n        if (error) {\n          throw error;\n        }\n        return data;\n      } catch (error) {\n        console.error('Error fetching user by ID:', error);\n        return null;\n      }\n    })();\n  }\n  filterUsers(users, filter) {\n    return users.filter(user => {\n      // Filter by role if specified\n      if (filter.role && user.role !== filter.role) {\n        return false;\n      }\n      // Filter by approval status if specified and user is a driver\n      if (filter.isApproved !== undefined && user.role === 'driver') {\n        if (user.is_approved !== filter.isApproved) {\n          return false;\n        }\n      }\n      // Filter by search term if specified\n      if (filter.searchTerm) {\n        const searchTerm = filter.searchTerm.toLowerCase();\n        const fullName = user.full_name?.toLowerCase() || '';\n        const email = user.email.toLowerCase();\n        const phone = user.phone?.toLowerCase() || '';\n        if (!fullName.includes(searchTerm) && !email.includes(searchTerm) && !phone.includes(searchTerm)) {\n          return false;\n        }\n      }\n      return true;\n    });\n  }\n  static ctorParameters = () => [{\n    type: AuthService\n  }];\n};\nUserService = __decorate([Injectable({\n  providedIn: 'root'\n})], UserService);\nexport { UserService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "AuthService", "UserService", "authService", "supabase", "usersSubject", "users$", "asObservable", "constructor", "getAllUsers", "_this", "_asyncToGenerator", "data", "error", "from", "select", "order", "ascending", "next", "console", "getUsersByRole", "role", "_this2", "eq", "approveDriver", "userId", "_this3", "update", "is_approved", "currentUsers", "value", "updatedUsers", "map", "user", "id", "is_active", "updateUserStatus", "isActive", "_this4", "userError", "single", "Error", "updated_at", "Date", "toISOString", "u", "getUserById", "_this5", "filterUsers", "users", "filter", "isApproved", "undefined", "searchTerm", "toLowerCase", "fullName", "full_name", "email", "phone", "includes", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\core\\services\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { BehaviorSubject, Observable, from, map } from 'rxjs';\nimport { User, UserFilter, UserRole } from '../models/user.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private supabase: SupabaseClient;\n  private usersSubject = new BehaviorSubject<User[]>([]);\n  users$ = this.usersSubject.asObservable();\n\n  constructor(private authService: AuthService) {\n    this.supabase = authService.supabase;\n  }\n\n  async getAllUsers(): Promise<User[]> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        throw error;\n      }\n\n      this.usersSubject.next(data);\n      return data;\n    } catch (error) {\n      console.error('Error fetching users:', error);\n      return [];\n    }\n  }\n\n  async getUsersByRole(role: UserRole): Promise<User[]> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .eq('role', role)\n        .order('created_at', { ascending: false });\n\n      if (error) {\n        throw error;\n      }\n\n      return data;\n    } catch (error) {\n      console.error(`Error fetching ${role}s:`, error);\n      return [];\n    }\n  }\n\n  async approveDriver(userId: string): Promise<boolean> {\n    try {\n      const { error } = await this.supabase\n        .from('profiles')\n        .update({\n          is_approved: \"TRUE\",\n        //  is_active: true // Automatically activate when approved\n        })\n        .eq('id', userId)\n        ;\n\n      if (error) {\n        throw error;\n      }\n\n      // Update the local users list\n      const currentUsers = this.usersSubject.value;\n      const updatedUsers = currentUsers.map(user =>\n        user.id === userId ? { ...user, is_approved: true, is_active: true } : user\n      );\n      this.usersSubject.next(updatedUsers);\n\n      return true;\n    } catch (error) {\n      console.error('Error approving driver:', error);\n      return false;\n    }\n  }\n\n  async updateUserStatus(userId: string, isActive: boolean): Promise<boolean> {\n    try {\n      // First get the user's current data\n      const { data: user, error: userError } = await this.supabase\n        .from('profiles')\n        .select('role, is_approved')\n        .eq('id', userId)\n        .single();\n\n      if (userError) throw userError;\n\n      // If trying to activate a rider/driver who isn't approved, prevent it\n      if (isActive && user.role !== 'admin' && !user.is_approved) {\n        throw new Error('Cannot activate unapproved user');\n      }\n\n      const { error } = await this.supabase\n        .from('profiles')\n        .update({\n          is_approved: isActive,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', userId);\n\n      if (error) throw error;\n\n      // Update the local users list\n      const currentUsers = this.usersSubject.value;\n      const updatedUsers = currentUsers.map(u =>\n        u.id === userId ? { ...u, is_approved: isActive } : u\n      );\n      this.usersSubject.next(updatedUsers);\n\n      return true;\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      return false;\n    }\n  }\n\n  async getUserById(userId: string): Promise<User | null> {\n    try {\n      const { data, error } = await this.supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      return data;\n    } catch (error) {\n      console.error('Error fetching user by ID:', error);\n      return null;\n    }\n  }\n\n  filterUsers(users: User[], filter: UserFilter): User[] {\n    return users.filter(user => {\n      // Filter by role if specified\n      if (filter.role && user.role !== filter.role) {\n        return false;\n      }\n\n      // Filter by approval status if specified and user is a driver\n      if (filter.isApproved !== undefined && user.role === 'driver') {\n        if (user.is_approved !== filter.isApproved) {\n          return false;\n        }\n      }\n\n      // Filter by search term if specified\n      if (filter.searchTerm) {\n        const searchTerm = filter.searchTerm.toLowerCase();\n        const fullName = user.full_name?.toLowerCase() || '';\n        const email = user.email.toLowerCase();\n        const phone = user.phone?.toLowerCase() || '';\n\n        if (!fullName.includes(searchTerm) &&\n            !email.includes(searchTerm) &&\n            !phone.includes(searchTerm)) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,eAAe,QAA+B,MAAM;AAE7D,SAASC,WAAW,QAAQ,gBAAgB;AAKrC,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EAKFC,WAAA;EAJZC,QAAQ;EACRC,YAAY,GAAG,IAAIL,eAAe,CAAS,EAAE,CAAC;EACtDM,MAAM,GAAG,IAAI,CAACD,YAAY,CAACE,YAAY,EAAE;EAEzCC,YAAoBL,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAC7B,IAAI,CAACC,QAAQ,GAAGD,WAAW,CAACC,QAAQ;EACtC;EAEMK,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASH,KAAI,CAACN,QAAQ,CACxCU,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAE5C,IAAIJ,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEAH,KAAI,CAACL,YAAY,CAACa,IAAI,CAACN,IAAI,CAAC;QAC5B,OAAOA,IAAI;MACb,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO,EAAE;MACX;IAAC;EACH;EAEMO,cAAcA,CAACC,IAAc;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MACjC,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASS,MAAI,CAAClB,QAAQ,CACxCU,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXQ,EAAE,CAAC,MAAM,EAAEF,IAAI,CAAC,CAChBL,KAAK,CAAC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAE5C,IAAIJ,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEA,OAAOD,IAAI;MACb,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,kBAAkBQ,IAAI,IAAI,EAAER,KAAK,CAAC;QAChD,OAAO,EAAE;MACX;IAAC;EACH;EAEMW,aAAaA,CAACC,MAAc;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MAChC,IAAI;QACF,MAAM;UAAEE;QAAK,CAAE,SAASa,MAAI,CAACtB,QAAQ,CAClCU,IAAI,CAAC,UAAU,CAAC,CAChBa,MAAM,CAAC;UACNC,WAAW,EAAE;UACf;SACC,CAAC,CACDL,EAAE,CAAC,IAAI,EAAEE,MAAM,CAAC;QAGnB,IAAIZ,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEA;QACA,MAAMgB,YAAY,GAAGH,MAAI,CAACrB,YAAY,CAACyB,KAAK;QAC5C,MAAMC,YAAY,GAAGF,YAAY,CAACG,GAAG,CAACC,IAAI,IACxCA,IAAI,CAACC,EAAE,KAAKT,MAAM,GAAG;UAAE,GAAGQ,IAAI;UAAEL,WAAW,EAAE,IAAI;UAAEO,SAAS,EAAE;QAAI,CAAE,GAAGF,IAAI,CAC5E;QACDP,MAAI,CAACrB,YAAY,CAACa,IAAI,CAACa,YAAY,CAAC;QAEpC,OAAO,IAAI;MACb,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,OAAO,KAAK;MACd;IAAC;EACH;EAEMuB,gBAAgBA,CAACX,MAAc,EAAEY,QAAiB;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MACtD,IAAI;QACF;QACA,MAAM;UAAEC,IAAI,EAAEqB,IAAI;UAAEpB,KAAK,EAAE0B;QAAS,CAAE,SAASD,MAAI,CAAClC,QAAQ,CACzDU,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,mBAAmB,CAAC,CAC3BQ,EAAE,CAAC,IAAI,EAAEE,MAAM,CAAC,CAChBe,MAAM,EAAE;QAEX,IAAID,SAAS,EAAE,MAAMA,SAAS;QAE9B;QACA,IAAIF,QAAQ,IAAIJ,IAAI,CAACZ,IAAI,KAAK,OAAO,IAAI,CAACY,IAAI,CAACL,WAAW,EAAE;UAC1D,MAAM,IAAIa,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEA,MAAM;UAAE5B;QAAK,CAAE,SAASyB,MAAI,CAAClC,QAAQ,CAClCU,IAAI,CAAC,UAAU,CAAC,CAChBa,MAAM,CAAC;UACNC,WAAW,EAAES,QAAQ;UACrBK,UAAU,EAAE,IAAIC,IAAI,EAAE,CAACC,WAAW;SACnC,CAAC,CACDrB,EAAE,CAAC,IAAI,EAAEE,MAAM,CAAC;QAEnB,IAAIZ,KAAK,EAAE,MAAMA,KAAK;QAEtB;QACA,MAAMgB,YAAY,GAAGS,MAAI,CAACjC,YAAY,CAACyB,KAAK;QAC5C,MAAMC,YAAY,GAAGF,YAAY,CAACG,GAAG,CAACa,CAAC,IACrCA,CAAC,CAACX,EAAE,KAAKT,MAAM,GAAG;UAAE,GAAGoB,CAAC;UAAEjB,WAAW,EAAES;QAAQ,CAAE,GAAGQ,CAAC,CACtD;QACDP,MAAI,CAACjC,YAAY,CAACa,IAAI,CAACa,YAAY,CAAC;QAEpC,OAAO,IAAI;MACb,CAAC,CAAC,OAAOlB,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO,KAAK;MACd;IAAC;EACH;EAEMiC,WAAWA,CAACrB,MAAc;IAAA,IAAAsB,MAAA;IAAA,OAAApC,iBAAA;MAC9B,IAAI;QACF,MAAM;UAAEC,IAAI;UAAEC;QAAK,CAAE,SAASkC,MAAI,CAAC3C,QAAQ,CACxCU,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXQ,EAAE,CAAC,IAAI,EAAEE,MAAM,CAAC,CAChBe,MAAM,EAAE;QAEX,IAAI3B,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEA,OAAOD,IAAI;MACb,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,OAAO,IAAI;MACb;IAAC;EACH;EAEAmC,WAAWA,CAACC,KAAa,EAAEC,MAAkB;IAC3C,OAAOD,KAAK,CAACC,MAAM,CAACjB,IAAI,IAAG;MACzB;MACA,IAAIiB,MAAM,CAAC7B,IAAI,IAAIY,IAAI,CAACZ,IAAI,KAAK6B,MAAM,CAAC7B,IAAI,EAAE;QAC5C,OAAO,KAAK;MACd;MAEA;MACA,IAAI6B,MAAM,CAACC,UAAU,KAAKC,SAAS,IAAInB,IAAI,CAACZ,IAAI,KAAK,QAAQ,EAAE;QAC7D,IAAIY,IAAI,CAACL,WAAW,KAAKsB,MAAM,CAACC,UAAU,EAAE;UAC1C,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAID,MAAM,CAACG,UAAU,EAAE;QACrB,MAAMA,UAAU,GAAGH,MAAM,CAACG,UAAU,CAACC,WAAW,EAAE;QAClD,MAAMC,QAAQ,GAAGtB,IAAI,CAACuB,SAAS,EAAEF,WAAW,EAAE,IAAI,EAAE;QACpD,MAAMG,KAAK,GAAGxB,IAAI,CAACwB,KAAK,CAACH,WAAW,EAAE;QACtC,MAAMI,KAAK,GAAGzB,IAAI,CAACyB,KAAK,EAAEJ,WAAW,EAAE,IAAI,EAAE;QAE7C,IAAI,CAACC,QAAQ,CAACI,QAAQ,CAACN,UAAU,CAAC,IAC9B,CAACI,KAAK,CAACE,QAAQ,CAACN,UAAU,CAAC,IAC3B,CAACK,KAAK,CAACC,QAAQ,CAACN,UAAU,CAAC,EAAE;UAC/B,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;;;;;AArKWnD,WAAW,GAAA0D,UAAA,EAHvB7D,UAAU,CAAC;EACV8D,UAAU,EAAE;CACb,CAAC,C,EACW3D,WAAW,CAsKvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}