{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(globalThis && supportsLocalStorage() && globalThis.localStorage && globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true')\n};\nexport class LockAcquireTimeoutError extends Error {\n  constructor(message) {\n    super(message);\n    this.isAcquireTimeout = true;\n  }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @experimental\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport function navigatorLock(_x, _x2, _x3) {\n  return _navigatorLock.apply(this, arguments);\n}\nfunction _navigatorLock() {\n  _navigatorLock = _asyncToGenerator(function* (name, acquireTimeout, fn) {\n    if (internals.debug) {\n      console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n      setTimeout(() => {\n        abortController.abort();\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n        }\n      }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    return yield globalThis.navigator.locks.request(name, acquireTimeout === 0 ? {\n      mode: 'exclusive',\n      ifAvailable: true\n    } : {\n      mode: 'exclusive',\n      signal: abortController.signal\n    }, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (lock) {\n        if (lock) {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n          }\n          try {\n            return yield fn();\n          } finally {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n            }\n          }\n        } else {\n          if (acquireTimeout === 0) {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n            }\n            throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n          } else {\n            if (internals.debug) {\n              try {\n                const result = yield globalThis.navigator.locks.query();\n                console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n              } catch (e) {\n                console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n              }\n            }\n            // Browser is not following the Navigator LockManager spec, it\n            // returned a null lock when we didn't use ifAvailable. So we can\n            // pretend the lock is acquired in the name of backward compatibility\n            // and user experience and just run the function.\n            console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n            return yield fn();\n          }\n        }\n      });\n      return function (_x4) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  });\n  return _navigatorLock.apply(this, arguments);\n}", "map": {"version": 3, "names": ["supportsLocalStorage", "internals", "debug", "globalThis", "localStorage", "getItem", "LockAcquireTimeoutError", "Error", "constructor", "message", "isAcquireTimeout", "NavigatorLockAcquireTimeoutError", "navigator<PERSON><PERSON>", "_x", "_x2", "_x3", "_navigatorLock", "apply", "arguments", "_asyncToGenerator", "name", "acquireTimeout", "fn", "console", "log", "abortController", "AbortController", "setTimeout", "abort", "navigator", "locks", "request", "mode", "ifAvailable", "signal", "_ref", "lock", "result", "query", "JSON", "stringify", "e", "warn", "_x4"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/locks.js"], "sourcesContent": ["import { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        supportsLocalStorage() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\nexport class LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @experimental\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    return await globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    });\n}\n"], "mappings": ";AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAChD;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAG;EACrB;AACJ;AACA;EACIC,KAAK,EAAE,CAAC,EAAEC,UAAU,IAChBH,oBAAoB,CAAC,CAAC,IACtBG,UAAU,CAACC,YAAY,IACvBD,UAAU,CAACC,YAAY,CAACC,OAAO,CAAC,gCAAgC,CAAC,KAAK,MAAM;AACpF,CAAC;AACD,OAAO,MAAMC,uBAAuB,SAASC,KAAK,CAAC;EAC/CC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAChC;AACJ;AACA,OAAO,MAAMC,gCAAgC,SAASL,uBAAuB,CAAC;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAsBM,aAAaA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA8DlC,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CA9DM,WAA6BC,IAAI,EAAEC,cAAc,EAAEC,EAAE,EAAE;IAC1D,IAAIrB,SAAS,CAACC,KAAK,EAAE;MACjBqB,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEJ,IAAI,EAAEC,cAAc,CAAC;IACzF;IACA,MAAMI,eAAe,GAAG,IAAItB,UAAU,CAACuB,eAAe,CAAC,CAAC;IACxD,IAAIL,cAAc,GAAG,CAAC,EAAE;MACpBM,UAAU,CAAC,MAAM;QACbF,eAAe,CAACG,KAAK,CAAC,CAAC;QACvB,IAAI3B,SAAS,CAACC,KAAK,EAAE;UACjBqB,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEJ,IAAI,CAAC;QAC7E;MACJ,CAAC,EAAEC,cAAc,CAAC;IACtB;IACA;IACA,aAAalB,UAAU,CAAC0B,SAAS,CAACC,KAAK,CAACC,OAAO,CAACX,IAAI,EAAEC,cAAc,KAAK,CAAC,GACpE;MACEW,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE;IACjB,CAAC,GACC;MACED,IAAI,EAAE,WAAW;MACjBE,MAAM,EAAET,eAAe,CAACS;IAC5B,CAAC;MAAA,IAAAC,IAAA,GAAAhB,iBAAA,CAAE,WAAOiB,IAAI,EAAK;QACnB,IAAIA,IAAI,EAAE;UACN,IAAInC,SAAS,CAACC,KAAK,EAAE;YACjBqB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,IAAI,EAAEgB,IAAI,CAAChB,IAAI,CAAC;UAChF;UACA,IAAI;YACA,aAAaE,EAAE,CAAC,CAAC;UACrB,CAAC,SACO;YACJ,IAAIrB,SAAS,CAACC,KAAK,EAAE;cACjBqB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,IAAI,EAAEgB,IAAI,CAAChB,IAAI,CAAC;YAChF;UACJ;QACJ,CAAC,MACI;UACD,IAAIC,cAAc,KAAK,CAAC,EAAE;YACtB,IAAIpB,SAAS,CAACC,KAAK,EAAE;cACjBqB,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAEJ,IAAI,CAAC;YACtF;YACA,MAAM,IAAIT,gCAAgC,CAAC,sDAAsDS,IAAI,sBAAsB,CAAC;UAChI,CAAC,MACI;YACD,IAAInB,SAAS,CAACC,KAAK,EAAE;cACjB,IAAI;gBACA,MAAMmC,MAAM,SAASlC,UAAU,CAAC0B,SAAS,CAACC,KAAK,CAACQ,KAAK,CAAC,CAAC;gBACvDf,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEe,IAAI,CAACC,SAAS,CAACH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;cACvG,CAAC,CACD,OAAOI,CAAC,EAAE;gBACNlB,OAAO,CAACmB,IAAI,CAAC,sEAAsE,EAAED,CAAC,CAAC;cAC3F;YACJ;YACA;YACA;YACA;YACA;YACAlB,OAAO,CAACmB,IAAI,CAAC,yPAAyP,CAAC;YACvQ,aAAapB,EAAE,CAAC,CAAC;UACrB;QACJ;MACJ,CAAC;MAAA,iBAAAqB,GAAA;QAAA,OAAAR,IAAA,CAAAlB,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACN,CAAC;EAAA,OAAAF,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}