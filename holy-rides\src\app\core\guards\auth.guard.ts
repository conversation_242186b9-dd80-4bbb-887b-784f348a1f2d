import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { firstValueFrom } from 'rxjs';
import { filter, take, timeout } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';

export const authGuard: CanActivateFn = async (route, state) => {
  const router = inject(Router);
  const authService = inject(AuthService);
  const snackBar = inject(MatSnackBar);

  try {
    // First check if we already have a session
    
    const user = await authService.getCurrentUser();
    console.log('User from getCurrentUser:', user);

    if (user) {
      // Check if user is active
      if (!user.is_approved) {
        // Show message based on approval status
        const message = user.role === 'admin' ? 
          'Your account has been deactivated. Please contact support.' :
          'Your account is pending approval. Please wait for administrator approval.';
        
        snackBar.open(message, 'Close', { duration: 5000 });
        await authService.logout();
        router.navigate(['/auth/login']);
        return false;
      }
      // User is authenticated and active, allow access
      return true;
    }

    // Otherwise wait for the auth state to be initialized with a timeout
    try {
      const user = await firstValueFrom(
        authService.user$.pipe(
          timeout(3000),
          filter(user => user !== undefined),
          take(1)
        )
      );

      if (user) {
        // Also check active status for user from observable
        if (!user.is_approved) {
          const message = user.role === 'admin' ? 
            'Your account has been deactivated. Please contact support.' :
            'Your account is pending approval. Please wait for administrator approval.';
          
          snackBar.open(message, 'Close', { duration: 5000 });
          await authService.logout();
          router.navigate(['/auth/login']);
          return false;
        }
        return true;
      } else {
        router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url }});
        return false;
      }
    } catch (timeoutError) {
      console.warn('Auth state initialization timed out:', timeoutError);
      router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url }});
      return false;
    }
  } catch (error) {
    console.error('Auth guard error:', error);
    router.navigate(['/auth/login']);
    return false;
  }
};
