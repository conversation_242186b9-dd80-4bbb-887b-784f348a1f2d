{"ast": null, "code": "import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n  getItem: key => {\n    if (!supportsLocalStorage()) {\n      return null;\n    }\n    return globalThis.localStorage.getItem(key);\n  },\n  setItem: (key, value) => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.setItem(key, value);\n  },\n  removeItem: key => {\n    if (!supportsLocalStorage()) {\n      return;\n    }\n    globalThis.localStorage.removeItem(key);\n  }\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n  return {\n    getItem: key => {\n      return store[key] || null;\n    },\n    setItem: (key, value) => {\n      store[key] = value;\n    },\n    removeItem: key => {\n      delete store[key];\n    }\n  };\n}", "map": {"version": 3, "names": ["supportsLocalStorage", "localStorageAdapter", "getItem", "key", "globalThis", "localStorage", "setItem", "value", "removeItem", "memoryLocalStorageAdapter", "store"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js"], "sourcesContent": ["import { supportsLocalStorage } from './helpers';\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nexport const localStorageAdapter = {\n    getItem: (key) => {\n        if (!supportsLocalStorage()) {\n            return null;\n        }\n        return globalThis.localStorage.getItem(key);\n    },\n    setItem: (key, value) => {\n        if (!supportsLocalStorage()) {\n            return;\n        }\n        globalThis.localStorage.setItem(key, value);\n    },\n    removeItem: (key) => {\n        if (!supportsLocalStorage()) {\n            return;\n        }\n        globalThis.localStorage.removeItem(key);\n    },\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nexport function memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAChD;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAG;EAC/BC,OAAO,EAAGC,GAAG,IAAK;IACd,IAAI,CAACH,oBAAoB,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;IACf;IACA,OAAOI,UAAU,CAACC,YAAY,CAACH,OAAO,CAACC,GAAG,CAAC;EAC/C,CAAC;EACDG,OAAO,EAAEA,CAACH,GAAG,EAAEI,KAAK,KAAK;IACrB,IAAI,CAACP,oBAAoB,CAAC,CAAC,EAAE;MACzB;IACJ;IACAI,UAAU,CAACC,YAAY,CAACC,OAAO,CAACH,GAAG,EAAEI,KAAK,CAAC;EAC/C,CAAC;EACDC,UAAU,EAAGL,GAAG,IAAK;IACjB,IAAI,CAACH,oBAAoB,CAAC,CAAC,EAAE;MACzB;IACJ;IACAI,UAAU,CAACC,YAAY,CAACG,UAAU,CAACL,GAAG,CAAC;EAC3C;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,SAASM,yBAAyBA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE;EAClD,OAAO;IACHR,OAAO,EAAGC,GAAG,IAAK;MACd,OAAOO,KAAK,CAACP,GAAG,CAAC,IAAI,IAAI;IAC7B,CAAC;IACDG,OAAO,EAAEA,CAACH,GAAG,EAAEI,KAAK,KAAK;MACrBG,KAAK,CAACP,GAAG,CAAC,GAAGI,KAAK;IACtB,CAAC;IACDC,UAAU,EAAGL,GAAG,IAAK;MACjB,OAAOO,KAAK,CAACP,GAAG,CAAC;IACrB;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}