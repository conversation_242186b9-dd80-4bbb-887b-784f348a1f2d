<mat-toolbar color="primary">
  <a (click)="navigateToDashboard()" class="logo-container">
    <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">
  </a>
  <span class="spacer"></span>
  <app-message-notification></app-message-notification>

  <button  *ngIf="authService.user$ | async" mat-button (click)="logout()">
    <mat-icon>logout</mat-icon>
    Logout
  </button>
</mat-toolbar>

<router-outlet></router-outlet>

<!-- PWA Components disabled due to Angular DevTools error -->
<!--
<app-offline-indicator *ngIf="pwaComponentsReady"></app-offline-indicator>
<app-install-prompt *ngIf="pwaComponentsReady"></app-install-prompt>
-->

<!-- Notification Permission Prompt disabled due to Angular DevTools error -->
<!--
<div *ngIf="showNotificationPrompt" class="notification-prompt">
  <mat-card>
    <mat-card-content>
      <h3>Enable Notifications</h3>
      <p>Get notified about new messages, ride updates, and more.</p>
      <div class="notification-actions">
        <button mat-button color="primary" (click)="requestNotifications()">
          Enable
        </button>
        <button mat-button (click)="showNotificationPrompt = false">
          Not Now
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>
-->
