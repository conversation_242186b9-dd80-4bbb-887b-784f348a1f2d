import { Injectable, Optional } from '@angular/core';
import { SwPush } from '@angular/service-worker';
import { MatSnackBar } from '@angular/material/snack-bar';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly VAPID_PUBLIC_KEY = 'YOUR_VAPID_PUBLIC_KEY'; // Replace with your actual VAPID key
  private notificationsEnabled$ = new BehaviorSubject<boolean>(false);
  private swPushAvailable = false;

  constructor(
    @Optional() private swPush: SwPush,
    private snackBar: MatSnackBar
  ) {
    // Check if SwPush is available
    this.swPushAvailable = !!this.swPush;

    // Check if notifications are already enabled
    this.checkNotificationPermission();

    // Listen for push messages if SwPush is available
    if (this.swPushAvailable && this.swPush.isEnabled) {
      this.swPush.messages.subscribe(message => {
        this.showNotification(message);
      });
    }
  }

  /**
   * Check if notification permission is granted
   */
  private checkNotificationPermission(): void {
    if ('Notification' in window) {
      const permission = Notification.permission;
      this.notificationsEnabled$.next(permission === 'granted');
    }
  }

  /**
   * Request permission to show notifications
   */
  requestNotificationPermission(): Promise<boolean> {
    // Check if SwPush is available
    if (!this.swPushAvailable) {
      this.snackBar.open('Push notification service is not available', 'Dismiss', {
        duration: 3000
      });
      return Promise.resolve(false);
    }

    // Check if push is enabled in the browser
    if (!this.swPush.isEnabled) {
      this.snackBar.open('Push notifications are not supported in this browser', 'Dismiss', {
        duration: 3000
      });
      return Promise.resolve(false);
    }

    return this.swPush.requestSubscription({
      serverPublicKey: this.VAPID_PUBLIC_KEY
    })
      .then(subscription => {
        // Here you would send the subscription to your server
        // For now, we'll just update the local state
        this.notificationsEnabled$.next(true);
        return true;
      })
      .catch(error => {
        console.error('Could not subscribe to notifications', error);
        this.snackBar.open('Could not subscribe to notifications', 'Dismiss', {
          duration: 3000
        });
        return false;
      });
  }

  /**
   * Show a notification to the user
   */
  private showNotification(data: any): void {
    // Display a snackbar notification
    this.snackBar.open(
      data.notification?.title || 'New notification',
      'View',
      { duration: 5000 }
    ).onAction().subscribe(() => {
      // Handle notification click (e.g., navigate to relevant page)
      console.log('Notification clicked', data);
    });
  }

  /**
   * Get an observable of the notification permission status
   */
  getNotificationStatus(): Observable<boolean> {
    return this.notificationsEnabled$.asObservable();
  }
}
