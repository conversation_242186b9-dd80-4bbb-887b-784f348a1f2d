-- Add is_active column to profiles table with default based on role and approval
ALTER TABLE profiles ADD COLUMN is_active boolean GENERATED ALWAYS AS (
  CASE 
    WHEN role = 'admin' THEN true
    ELSE is_approved
  END
) STORED;

-- Create an index on is_active for better query performance
CREATE INDEX idx_profiles_is_active ON profiles(is_active);

-- Update RLS policies to consider is_active status
CREATE POLICY "Inactive users cannot access any data"
  ON profiles
  USING (is_active = true OR role = 'admin');

-- Allow admin users to view all profiles regardless of active status
CREATE POLICY "Admin users can view all profiles"
  ON profiles FOR SELECT
  TO authenticated
  USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'admin'
  );