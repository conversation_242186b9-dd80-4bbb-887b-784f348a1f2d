export interface MessageThread {
  id: string;
  ride_id: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  thread_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

export interface MessageWithSenderInfo extends Message {
  sender_name?: string;
  sender_avatar?: string;
  is_current_user: boolean;
}
