<div class="register-container">
  <!-- <div class="logo-container">
    <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">

  </div> -->
  <mat-card>
    <mat-card-header>
      <mat-card-title>Register</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>Full Name</mat-label>
          <input matInput type="text" formControlName="full_name" placeholder="Enter your full name">
          <mat-error *ngIf="registerForm.get('full_name')?.errors?.['required']">Full name is required</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter your email">
          <mat-error *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</mat-error>
          <mat-error *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Password</mat-label>
          <input matInput type="password" formControlName="password" placeholder="Enter your password">
          <mat-error *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</mat-error>
          <mat-error *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Confirm Password</mat-label>
          <input matInput type="password" formControlName="confirmPassword" placeholder="Confirm your password">
          <mat-error *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Password confirmation is required</mat-error>
          <mat-error *ngIf="registerForm.errors?.['mismatch']">Passwords do not match</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Phone Number</mat-label>
          <input matInput type="tel" formControlName="phone" placeholder="Enter your phone number">
          <mat-error *ngIf="registerForm.get('phone')?.errors?.['required']">Phone number is required</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Role</mat-label>
          <mat-select formControlName="role">
            <mat-option *ngFor="let role of roles" [value]="role.value">
              {{role.label}}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="registerForm.get('role')?.errors?.['required']">Role is required</mat-error>
        </mat-form-field>

        <div class="error-message" *ngIf="error">{{ error }}</div>

        <div class="button-container">
          <button mat-raised-button color="primary" type="submit" [disabled]="registerForm.invalid || loading">
            {{ loading ? 'Registering...' : 'Register' }}
          </button>
        </div>

        <div class="links">
          <a routerLink="/auth/login">Already have an account? Login</a>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
