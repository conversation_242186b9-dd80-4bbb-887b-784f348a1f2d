-- Add payment-related fields to rides table
ALTER TABLE rides
ADD COLUMN IF NOT EXISTS payment_status TEXT CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
ADD COLUMN IF NOT EXISTS payment_id TEXT,
ADD COLUMN IF NOT EXISTS amount DECIMAL(10, 2);

-- Update existing rides to have a default payment_status for completed rides
UPDATE rides SET payment_status = 'pending' WHERE status = 'completed' AND payment_status IS NULL;

-- Create driver_payouts table
CREATE TABLE IF NOT EXISTS driver_payouts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  driver_id UUID REFERENCES profiles(id) NOT NULL,
  ride_id UUID REFERENCES rides(id) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Set up Row Level Security (RLS)
ALTER TABLE driver_payouts ENABLE ROW LEVEL SECURITY;

-- Create policies for driver_payouts
CREATE POLICY "Drivers can view their own payouts."
  ON driver_payouts FOR SELECT
  USING (auth.uid() = driver_id);

CREATE POLICY "Admins can view all payouts."
  ON driver_payouts FOR SELECT
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Admins can create payouts."
  ON driver_payouts FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Admins can update payouts."
  ON driver_payouts FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

-- Create an updated_at trigger for driver_payouts
CREATE OR REPLACE FUNCTION update_driver_payouts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER driver_payouts_updated_at
  BEFORE UPDATE ON driver_payouts
  FOR EACH ROW
  EXECUTE PROCEDURE update_driver_payouts_updated_at();
