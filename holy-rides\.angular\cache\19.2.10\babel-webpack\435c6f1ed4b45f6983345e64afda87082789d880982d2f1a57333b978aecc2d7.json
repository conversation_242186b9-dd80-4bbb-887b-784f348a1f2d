{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator as _ };", "map": {"version": 3, "names": ["i0", "inject", "APP_ID", "Injectable", "counters", "_IdGenerator", "_appId", "getId", "prefix", "hasOwnProperty", "ɵfac", "_IdGenerator_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "_"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/id-generator-Dw_9dSDu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n    _appId = inject(APP_ID);\n    /**\n     * Generates a unique ID with a specific prefix.\n     * @param prefix Prefix to add to the ID.\n     */\n    getId(prefix) {\n        // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n        // Angular app on them, we can reduce the amount of breakages by not adding it.\n        if (this._appId !== 'ng') {\n            prefix += this._appId;\n        }\n        if (!counters.hasOwnProperty(prefix)) {\n            counters[prefix] = 0;\n        }\n        return `${prefix}${counters[prefix]++}`;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _IdGenerator, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nexport { _IdGenerator as _ };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,QAAQ,eAAe;;AAE1D;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;AACnB;AACA,MAAMC,YAAY,CAAC;EACfC,MAAM,GAAGL,MAAM,CAACC,MAAM,CAAC;EACvB;AACJ;AACA;AACA;EACIK,KAAKA,CAACC,MAAM,EAAE;IACV;IACA;IACA,IAAI,IAAI,CAACF,MAAM,KAAK,IAAI,EAAE;MACtBE,MAAM,IAAI,IAAI,CAACF,MAAM;IACzB;IACA,IAAI,CAACF,QAAQ,CAACK,cAAc,CAACD,MAAM,CAAC,EAAE;MAClCJ,QAAQ,CAACI,MAAM,CAAC,GAAG,CAAC;IACxB;IACA,OAAO,GAAGA,MAAM,GAAGJ,QAAQ,CAACI,MAAM,CAAC,EAAE,EAAE;EAC3C;EACA,OAAOE,IAAI,YAAAC,qBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFP,YAAY;EAAA;EAC/G,OAAOQ,KAAK,kBAD6Eb,EAAE,CAAAc,kBAAA;IAAAC,KAAA,EACYV,YAAY;IAAAW,OAAA,EAAZX,YAAY,CAAAK,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC3I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FlB,EAAE,CAAAmB,iBAAA,CAGJd,YAAY,EAAc,CAAC;IAC1Ge,IAAI,EAAEjB,UAAU;IAChBkB,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AAEV,SAASZ,YAAY,IAAIiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}