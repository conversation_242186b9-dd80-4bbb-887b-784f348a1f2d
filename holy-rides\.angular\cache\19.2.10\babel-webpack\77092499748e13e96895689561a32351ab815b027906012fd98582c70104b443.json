{"ast": null, "code": "import { DEFAULT_TIMEOUT } from '../lib/constants';\nexport default class Push {\n  /**\n   * Initializes the Push\n   *\n   * @param channel The Channel\n   * @param event The event, for example `\"phx_join\"`\n   * @param payload The payload, for example `{user_id: 123}`\n   * @param timeout The push timeout in milliseconds\n   */\n  constructor(channel, event, payload = {}, timeout = DEFAULT_TIMEOUT) {\n    this.channel = channel;\n    this.event = event;\n    this.payload = payload;\n    this.timeout = timeout;\n    this.sent = false;\n    this.timeoutTimer = undefined;\n    this.ref = '';\n    this.receivedResp = null;\n    this.recHooks = [];\n    this.refEvent = null;\n  }\n  resend(timeout) {\n    this.timeout = timeout;\n    this._cancelRefEvent();\n    this.ref = '';\n    this.refEvent = null;\n    this.receivedResp = null;\n    this.sent = false;\n    this.send();\n  }\n  send() {\n    if (this._hasReceived('timeout')) {\n      return;\n    }\n    this.startTimeout();\n    this.sent = true;\n    this.channel.socket.push({\n      topic: this.channel.topic,\n      event: this.event,\n      payload: this.payload,\n      ref: this.ref,\n      join_ref: this.channel._joinRef()\n    });\n  }\n  updatePayload(payload) {\n    this.payload = Object.assign(Object.assign({}, this.payload), payload);\n  }\n  receive(status, callback) {\n    var _a;\n    if (this._hasReceived(status)) {\n      callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n    }\n    this.recHooks.push({\n      status,\n      callback\n    });\n    return this;\n  }\n  startTimeout() {\n    if (this.timeoutTimer) {\n      return;\n    }\n    this.ref = this.channel.socket._makeRef();\n    this.refEvent = this.channel._replyEventName(this.ref);\n    const callback = payload => {\n      this._cancelRefEvent();\n      this._cancelTimeout();\n      this.receivedResp = payload;\n      this._matchReceive(payload);\n    };\n    this.channel._on(this.refEvent, {}, callback);\n    this.timeoutTimer = setTimeout(() => {\n      this.trigger('timeout', {});\n    }, this.timeout);\n  }\n  trigger(status, response) {\n    if (this.refEvent) this.channel._trigger(this.refEvent, {\n      status,\n      response\n    });\n  }\n  destroy() {\n    this._cancelRefEvent();\n    this._cancelTimeout();\n  }\n  _cancelRefEvent() {\n    if (!this.refEvent) {\n      return;\n    }\n    this.channel._off(this.refEvent, {});\n  }\n  _cancelTimeout() {\n    clearTimeout(this.timeoutTimer);\n    this.timeoutTimer = undefined;\n  }\n  _matchReceive({\n    status,\n    response\n  }) {\n    this.recHooks.filter(h => h.status === status).forEach(h => h.callback(response));\n  }\n  _hasReceived(status) {\n    return this.receivedResp && this.receivedResp.status === status;\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_TIMEOUT", "<PERSON><PERSON>", "constructor", "channel", "event", "payload", "timeout", "sent", "timeoutTimer", "undefined", "ref", "receivedResp", "rec<PERSON>ooks", "refEvent", "resend", "_cancelRefEvent", "send", "_hasReceived", "startTimeout", "socket", "push", "topic", "join_ref", "_joinRef", "updatePayload", "Object", "assign", "receive", "status", "callback", "_a", "response", "_makeRef", "_replyEventName", "_cancelTimeout", "_matchReceive", "_on", "setTimeout", "trigger", "_trigger", "destroy", "_off", "clearTimeout", "filter", "h", "for<PERSON>ach"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/lib/push.js"], "sourcesContent": ["import { DEFAULT_TIMEOUT } from '../lib/constants';\nexport default class Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kBAAkB;AAClD,eAAe,MAAMC,IAAI,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,OAAO,EAAEC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAEC,OAAO,GAAGN,eAAe,EAAE;IACjE,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,YAAY,GAAGC,SAAS;IAC7B,IAAI,CAACC,GAAG,GAAG,EAAE;IACb,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACxB;EACAC,MAAMA,CAACR,OAAO,EAAE;IACZ,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACS,eAAe,CAAC,CAAC;IACtB,IAAI,CAACL,GAAG,GAAG,EAAE;IACb,IAAI,CAACG,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACF,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,IAAI,GAAG,KAAK;IACjB,IAAI,CAACS,IAAI,CAAC,CAAC;EACf;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACC,YAAY,CAAC,SAAS,CAAC,EAAE;MAC9B;IACJ;IACA,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACX,IAAI,GAAG,IAAI;IAChB,IAAI,CAACJ,OAAO,CAACgB,MAAM,CAACC,IAAI,CAAC;MACrBC,KAAK,EAAE,IAAI,CAAClB,OAAO,CAACkB,KAAK;MACzBjB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBK,GAAG,EAAE,IAAI,CAACA,GAAG;MACbY,QAAQ,EAAE,IAAI,CAACnB,OAAO,CAACoB,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN;EACAC,aAAaA,CAACnB,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAGoB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrB,OAAO,CAAC,EAAEA,OAAO,CAAC;EAC1E;EACAsB,OAAOA,CAACC,MAAM,EAAEC,QAAQ,EAAE;IACtB,IAAIC,EAAE;IACN,IAAI,IAAI,CAACb,YAAY,CAACW,MAAM,CAAC,EAAE;MAC3BC,QAAQ,CAAC,CAACC,EAAE,GAAG,IAAI,CAACnB,YAAY,MAAM,IAAI,IAAImB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,QAAQ,CAAC;IACvF;IACA,IAAI,CAACnB,QAAQ,CAACQ,IAAI,CAAC;MAAEQ,MAAM;MAAEC;IAAS,CAAC,CAAC;IACxC,OAAO,IAAI;EACf;EACAX,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACV,YAAY,EAAE;MACnB;IACJ;IACA,IAAI,CAACE,GAAG,GAAG,IAAI,CAACP,OAAO,CAACgB,MAAM,CAACa,QAAQ,CAAC,CAAC;IACzC,IAAI,CAACnB,QAAQ,GAAG,IAAI,CAACV,OAAO,CAAC8B,eAAe,CAAC,IAAI,CAACvB,GAAG,CAAC;IACtD,MAAMmB,QAAQ,GAAIxB,OAAO,IAAK;MAC1B,IAAI,CAACU,eAAe,CAAC,CAAC;MACtB,IAAI,CAACmB,cAAc,CAAC,CAAC;MACrB,IAAI,CAACvB,YAAY,GAAGN,OAAO;MAC3B,IAAI,CAAC8B,aAAa,CAAC9B,OAAO,CAAC;IAC/B,CAAC;IACD,IAAI,CAACF,OAAO,CAACiC,GAAG,CAAC,IAAI,CAACvB,QAAQ,EAAE,CAAC,CAAC,EAAEgB,QAAQ,CAAC;IAC7C,IAAI,CAACrB,YAAY,GAAG6B,UAAU,CAAC,MAAM;MACjC,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAChC,OAAO,CAAC;EACpB;EACAgC,OAAOA,CAACV,MAAM,EAAEG,QAAQ,EAAE;IACtB,IAAI,IAAI,CAAClB,QAAQ,EACb,IAAI,CAACV,OAAO,CAACoC,QAAQ,CAAC,IAAI,CAAC1B,QAAQ,EAAE;MAAEe,MAAM;MAAEG;IAAS,CAAC,CAAC;EAClE;EACAS,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzB,eAAe,CAAC,CAAC;IACtB,IAAI,CAACmB,cAAc,CAAC,CAAC;EACzB;EACAnB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACF,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAACV,OAAO,CAACsC,IAAI,CAAC,IAAI,CAAC5B,QAAQ,EAAE,CAAC,CAAC,CAAC;EACxC;EACAqB,cAAcA,CAAA,EAAG;IACbQ,YAAY,CAAC,IAAI,CAAClC,YAAY,CAAC;IAC/B,IAAI,CAACA,YAAY,GAAGC,SAAS;EACjC;EACA0B,aAAaA,CAAC;IAAEP,MAAM;IAAEG;EAAU,CAAC,EAAE;IACjC,IAAI,CAACnB,QAAQ,CACR+B,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAChB,MAAM,KAAKA,MAAM,CAAC,CAClCiB,OAAO,CAAED,CAAC,IAAKA,CAAC,CAACf,QAAQ,CAACE,QAAQ,CAAC,CAAC;EAC7C;EACAd,YAAYA,CAACW,MAAM,EAAE;IACjB,OAAO,IAAI,CAACjB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACiB,MAAM,KAAKA,MAAM;EACnE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}