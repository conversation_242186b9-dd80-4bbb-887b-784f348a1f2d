{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport GoTrueAdminApi from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN, GOTRUE_URL, STORAGE_KEY } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse } from './lib/fetch';\nimport { decodeJWTPayload, Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, generatePKCEVerifier, generatePKCEChallenge, supportsLocalStorage, parseParametersFromURL } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError } from './lib/locks';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n  url: GOTRUE_URL,\n  storageKey: STORAGE_KEY,\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  headers: DEFAULT_HEADERS,\n  flowType: 'implicit',\n  debug: false\n};\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\nfunction lockNoOp(_x, _x2, _x3) {\n  return _lockNoOp.apply(this, arguments);\n}\nfunction _lockNoOp() {\n  _lockNoOp = _asyncToGenerator(function* (name, acquireTimeout, fn) {\n    return yield fn();\n  });\n  return _lockNoOp.apply(this, arguments);\n}\nexport default class GoTrueClient {\n  /**\n   * Create a new client for use in the browser.\n   */\n  constructor(options) {\n    var _this = this;\n    var _a;\n    this.memoryStorage = null;\n    this.stateChangeEmitters = new Map();\n    this.autoRefreshTicker = null;\n    this.visibilityChangedCallback = null;\n    this.refreshingDeferred = null;\n    /**\n     * Keeps track of the async client initialization.\n     * When null or not yet resolved the auth state is `unknown`\n     * Once resolved the the auth state is known and it's save to call any further client methods.\n     * Keep extra care to never reject or throw uncaught errors\n     */\n    this.initializePromise = null;\n    this.detectSessionInUrl = true;\n    this.lockAcquired = false;\n    this.pendingInLock = [];\n    /**\n     * Used to broadcast state change events to other tabs listening.\n     */\n    this.broadcastChannel = null;\n    this.logger = console.log;\n    this.instanceID = GoTrueClient.nextInstanceID;\n    GoTrueClient.nextInstanceID += 1;\n    if (this.instanceID > 0 && isBrowser()) {\n      console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n    }\n    const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n    this.logDebugMessages = !!settings.debug;\n    if (typeof settings.debug === 'function') {\n      this.logger = settings.debug;\n    }\n    this.persistSession = settings.persistSession;\n    this.storageKey = settings.storageKey;\n    this.autoRefreshToken = settings.autoRefreshToken;\n    this.admin = new GoTrueAdminApi({\n      url: settings.url,\n      headers: settings.headers,\n      fetch: settings.fetch\n    });\n    this.url = settings.url;\n    this.headers = settings.headers;\n    this.fetch = resolveFetch(settings.fetch);\n    this.lock = settings.lock || lockNoOp;\n    this.detectSessionInUrl = settings.detectSessionInUrl;\n    this.flowType = settings.flowType;\n    this.mfa = {\n      verify: this._verify.bind(this),\n      enroll: this._enroll.bind(this),\n      unenroll: this._unenroll.bind(this),\n      challenge: this._challenge.bind(this),\n      listFactors: this._listFactors.bind(this),\n      challengeAndVerify: this._challengeAndVerify.bind(this),\n      getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this)\n    };\n    if (this.persistSession) {\n      if (settings.storage) {\n        this.storage = settings.storage;\n      } else {\n        if (supportsLocalStorage()) {\n          this.storage = localStorageAdapter;\n        } else {\n          this.memoryStorage = {};\n          this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n      }\n    } else {\n      this.memoryStorage = {};\n      this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n    }\n    if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n      try {\n        this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n      } catch (e) {\n        console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n      }\n      (_a = this.broadcastChannel) === null || _a === void 0 ? void 0 : _a.addEventListener('message', /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (event) {\n          _this._debug('received broadcast notification from other tab or client', event);\n          yield _this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n        });\n        return function (_x4) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    }\n    this.initialize();\n  }\n  _debug(...args) {\n    if (this.logDebugMessages) {\n      this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n    }\n    return this;\n  }\n  /**\n   * Initializes the client session either from the url or from storage.\n   * This method is automatically called when instantiating the client, but should also be called\n   * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n   */\n  initialize() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.initializePromise) {\n        return yield _this2.initializePromise;\n      }\n      _this2.initializePromise = _asyncToGenerator(function* () {\n        return yield _this2._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this2._initialize();\n        }));\n      })();\n      return yield _this2.initializePromise;\n    })();\n  }\n  /**\n   * IMPORTANT:\n   * 1. Never throw in this method, as it is called from the constructor\n   * 2. Never return a session from this method as it would be cached over\n   *    the whole lifetime of the client\n   */\n  _initialize() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const isPKCEFlow = isBrowser() ? yield _this3._isPKCEFlow() : false;\n        _this3._debug('#_initialize()', 'begin', 'is PKCE flow', isPKCEFlow);\n        if (isPKCEFlow || _this3.detectSessionInUrl && _this3._isImplicitGrantFlow()) {\n          const {\n            data,\n            error\n          } = yield _this3._getSessionFromURL(isPKCEFlow);\n          if (error) {\n            _this3._debug('#_initialize()', 'error detecting session from URL', error);\n            // hacky workaround to keep the existing session if there's an error returned from identity linking\n            // TODO: once error codes are ready, we should match against it instead of the message\n            if ((error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked' || (error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked to another user') {\n              return {\n                error\n              };\n            }\n            // failed login attempt via url,\n            // remove old session as in verifyOtp, signUp and signInWith*\n            yield _this3._removeSession();\n            return {\n              error\n            };\n          }\n          const {\n            session,\n            redirectType\n          } = data;\n          _this3._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n          yield _this3._saveSession(session);\n          setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n            if (redirectType === 'recovery') {\n              yield _this3._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n            } else {\n              yield _this3._notifyAllSubscribers('SIGNED_IN', session);\n            }\n          }), 0);\n          return {\n            error: null\n          };\n        }\n        // no login attempt via callback url try to recover session from storage\n        yield _this3._recoverAndRefresh();\n        return {\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            error\n          };\n        }\n        return {\n          error: new AuthUnknownError('Unexpected error during initialization', error)\n        };\n      } finally {\n        yield _this3._handleVisibilityChange();\n        _this3._debug('#_initialize()', 'end');\n      }\n    })();\n  }\n  /**\n   * Creates a new user.\n   *\n   * Be aware that if a user account exists in the system you may get back an\n   * error message that attempts to hide this information from the user.\n   * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n   *\n   * @returns A logged-in session if the server has \"autoconfirm\" ON\n   * @returns A user if the server has \"autoconfirm\" OFF\n   */\n  signUp(credentials) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      try {\n        yield _this4._removeSession();\n        let res;\n        if ('email' in credentials) {\n          const {\n            email,\n            password,\n            options\n          } = credentials;\n          let codeChallenge = null;\n          let codeChallengeMethod = null;\n          if (_this4.flowType === 'pkce') {\n            const codeVerifier = generatePKCEVerifier();\n            yield setItemAsync(_this4.storage, `${_this4.storageKey}-code-verifier`, codeVerifier);\n            codeChallenge = yield generatePKCEChallenge(codeVerifier);\n            codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n          }\n          res = yield _request(_this4.fetch, 'POST', `${_this4.url}/signup`, {\n            headers: _this4.headers,\n            redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n            body: {\n              email,\n              password,\n              data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              },\n              code_challenge: codeChallenge,\n              code_challenge_method: codeChallengeMethod\n            },\n            xform: _sessionResponse\n          });\n        } else if ('phone' in credentials) {\n          const {\n            phone,\n            password,\n            options\n          } = credentials;\n          res = yield _request(_this4.fetch, 'POST', `${_this4.url}/signup`, {\n            headers: _this4.headers,\n            body: {\n              phone,\n              password,\n              data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n              channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            },\n            xform: _sessionResponse\n          });\n        } else {\n          throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n        }\n        const {\n          data,\n          error\n        } = res;\n        if (error || !data) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: error\n          };\n        }\n        const session = data.session;\n        const user = data.user;\n        if (data.session) {\n          yield _this4._saveSession(data.session);\n          yield _this4._notifyAllSubscribers('SIGNED_IN', session);\n        }\n        return {\n          data: {\n            user,\n            session\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Log in an existing user with an email and password or phone and password.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or that the\n   * email/phone and password combination is wrong or that the account can only\n   * be accessed via social login.\n   */\n  signInWithPassword(credentials) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this5._removeSession();\n        let res;\n        if ('email' in credentials) {\n          const {\n            email,\n            password,\n            options\n          } = credentials;\n          res = yield _request(_this5.fetch, 'POST', `${_this5.url}/token?grant_type=password`, {\n            headers: _this5.headers,\n            body: {\n              email,\n              password,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            },\n            xform: _sessionResponsePassword\n          });\n        } else if ('phone' in credentials) {\n          const {\n            phone,\n            password,\n            options\n          } = credentials;\n          res = yield _request(_this5.fetch, 'POST', `${_this5.url}/token?grant_type=password`, {\n            headers: _this5.headers,\n            body: {\n              phone,\n              password,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            },\n            xform: _sessionResponsePassword\n          });\n        } else {\n          throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n        }\n        const {\n          data,\n          error\n        } = res;\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        } else if (!data || !data.session || !data.user) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: new AuthInvalidTokenResponseError()\n          };\n        }\n        if (data.session) {\n          yield _this5._saveSession(data.session);\n          yield _this5._notifyAllSubscribers('SIGNED_IN', data.session);\n        }\n        return {\n          data: Object.assign({\n            user: data.user,\n            session: data.session\n          }, data.weak_password ? {\n            weakPassword: data.weak_password\n          } : null),\n          error\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Log in an existing user via a third-party provider.\n   * This method supports the PKCE flow.\n   */\n  signInWithOAuth(credentials) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d;\n      yield _this6._removeSession();\n      return yield _this6._handleProviderSignIn(credentials.provider, {\n        redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n        scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n        queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n        skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect\n      });\n    })();\n  }\n  /**\n   * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n   */\n  exchangeCodeForSession(authCode) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      yield _this7.initializePromise;\n      return _this7._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this7._exchangeCodeForSession(authCode);\n      }));\n    })();\n  }\n  _exchangeCodeForSession(authCode) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const storageItem = yield getItemAsync(_this8.storage, `${_this8.storageKey}-code-verifier`);\n      const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n      const {\n        data,\n        error\n      } = yield _request(_this8.fetch, 'POST', `${_this8.url}/token?grant_type=pkce`, {\n        headers: _this8.headers,\n        body: {\n          auth_code: authCode,\n          code_verifier: codeVerifier\n        },\n        xform: _sessionResponse\n      });\n      yield removeItemAsync(_this8.storage, `${_this8.storageKey}-code-verifier`);\n      if (error) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error\n        };\n      } else if (!data || !data.session || !data.user) {\n        return {\n          data: {\n            user: null,\n            session: null,\n            redirectType: null\n          },\n          error: new AuthInvalidTokenResponseError()\n        };\n      }\n      if (data.session) {\n        yield _this8._saveSession(data.session);\n        yield _this8._notifyAllSubscribers('SIGNED_IN', data.session);\n      }\n      return {\n        data: Object.assign(Object.assign({}, data), {\n          redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null\n        }),\n        error\n      };\n    })();\n  }\n  /**\n   * Allows signing in with an OIDC ID token. The authentication provider used\n   * should be enabled and configured.\n   */\n  signInWithIdToken(credentials) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      yield _this9._removeSession();\n      try {\n        const {\n          options,\n          provider,\n          token,\n          access_token,\n          nonce\n        } = credentials;\n        const res = yield _request(_this9.fetch, 'POST', `${_this9.url}/token?grant_type=id_token`, {\n          headers: _this9.headers,\n          body: {\n            provider,\n            id_token: token,\n            access_token,\n            nonce,\n            gotrue_meta_security: {\n              captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n            }\n          },\n          xform: _sessionResponse\n        });\n        const {\n          data,\n          error\n        } = res;\n        if (error) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        } else if (!data || !data.session || !data.user) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error: new AuthInvalidTokenResponseError()\n          };\n        }\n        if (data.session) {\n          yield _this9._saveSession(data.session);\n          yield _this9._notifyAllSubscribers('SIGNED_IN', data.session);\n        }\n        return {\n          data,\n          error\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Log in a user using magiclink or a one-time password (OTP).\n   *\n   * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n   * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n   * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n   *\n   * Be aware that you may get back an error message that will not distinguish\n   * between the cases where the account does not exist or, that the account\n   * can only be accessed via social login.\n   *\n   * Do note that you will need to configure a Whatsapp sender on Twilio\n   * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n   * channel is not supported on other providers\n   * at this time.\n   * This method supports PKCE when an email is passed.\n   */\n  signInWithOtp(credentials) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d, _e;\n      try {\n        yield _this0._removeSession();\n        if ('email' in credentials) {\n          const {\n            email,\n            options\n          } = credentials;\n          let codeChallenge = null;\n          let codeChallengeMethod = null;\n          if (_this0.flowType === 'pkce') {\n            const codeVerifier = generatePKCEVerifier();\n            yield setItemAsync(_this0.storage, `${_this0.storageKey}-code-verifier`, codeVerifier);\n            codeChallenge = yield generatePKCEChallenge(codeVerifier);\n            codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n          }\n          const {\n            error\n          } = yield _request(_this0.fetch, 'POST', `${_this0.url}/otp`, {\n            headers: _this0.headers,\n            body: {\n              email,\n              data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n              create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              },\n              code_challenge: codeChallenge,\n              code_challenge_method: codeChallengeMethod\n            },\n            redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n          });\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        if ('phone' in credentials) {\n          const {\n            phone,\n            options\n          } = credentials;\n          const {\n            data,\n            error\n          } = yield _request(_this0.fetch, 'POST', `${_this0.url}/otp`, {\n            headers: _this0.headers,\n            body: {\n              phone,\n              data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n              create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              },\n              channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms'\n            }\n          });\n          return {\n            data: {\n              user: null,\n              session: null,\n              messageId: data === null || data === void 0 ? void 0 : data.message_id\n            },\n            error\n          };\n        }\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n   */\n  verifyOtp(params) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      try {\n        if (params.type !== 'email_change' && params.type !== 'phone_change') {\n          // we don't want to remove the authenticated session if the user is performing an email_change or phone_change verification\n          yield _this1._removeSession();\n        }\n        let redirectTo = undefined;\n        let captchaToken = undefined;\n        if ('options' in params) {\n          redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n          captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n        }\n        const {\n          data,\n          error\n        } = yield _request(_this1.fetch, 'POST', `${_this1.url}/verify`, {\n          headers: _this1.headers,\n          body: Object.assign(Object.assign({}, params), {\n            gotrue_meta_security: {\n              captcha_token: captchaToken\n            }\n          }),\n          redirectTo,\n          xform: _sessionResponse\n        });\n        if (error) {\n          throw error;\n        }\n        if (!data) {\n          throw new Error('An error occurred on token verification.');\n        }\n        const session = data.session;\n        const user = data.user;\n        if (session === null || session === void 0 ? void 0 : session.access_token) {\n          yield _this1._saveSession(session);\n          yield _this1._notifyAllSubscribers('SIGNED_IN', session);\n        }\n        return {\n          data: {\n            user,\n            session\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Attempts a single-sign on using an enterprise Identity Provider. A\n   * successful SSO attempt will redirect the current page to the identity\n   * provider authorization page. The redirect URL is implementation and SSO\n   * protocol specific.\n   *\n   * You can use it by providing a SSO domain. Typically you can extract this\n   * domain by asking users for their email address. If this domain is\n   * registered on the Auth instance the redirect will use that organization's\n   * currently active SSO Identity Provider for the login.\n   *\n   * If you have built an organization-specific login page, you can use the\n   * organization's SSO Identity Provider UUID directly instead.\n   */\n  signInWithSSO(params) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      try {\n        yield _this10._removeSession();\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (_this10.flowType === 'pkce') {\n          const codeVerifier = generatePKCEVerifier();\n          yield setItemAsync(_this10.storage, `${_this10.storageKey}-code-verifier`, codeVerifier);\n          codeChallenge = yield generatePKCEChallenge(codeVerifier);\n          codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        }\n        return yield _request(_this10.fetch, 'POST', `${_this10.url}/sso`, {\n          body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, 'providerId' in params ? {\n            provider_id: params.providerId\n          } : null), 'domain' in params ? {\n            domain: params.domain\n          } : null), {\n            redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined\n          }), ((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken) ? {\n            gotrue_meta_security: {\n              captcha_token: params.options.captchaToken\n            }\n          } : null), {\n            skip_http_redirect: true,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod\n          }),\n          headers: _this10.headers,\n          xform: _ssoResponse\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Sends a reauthentication OTP to the user's email or phone number.\n   * Requires the user to be signed-in.\n   */\n  reauthenticate() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      yield _this11.initializePromise;\n      return yield _this11._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this11._reauthenticate();\n      }));\n    })();\n  }\n  _reauthenticate() {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this12._useSession(/*#__PURE__*/function () {\n          var _ref7 = _asyncToGenerator(function* (result) {\n            const {\n              data: {\n                session\n              },\n              error: sessionError\n            } = result;\n            if (sessionError) throw sessionError;\n            if (!session) throw new AuthSessionMissingError();\n            const {\n              error\n            } = yield _request(_this12.fetch, 'GET', `${_this12.url}/reauthenticate`, {\n              headers: _this12.headers,\n              jwt: session.access_token\n            });\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error\n            };\n          });\n          return function (_x5) {\n            return _ref7.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n   */\n  resend(credentials) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (credentials.type != 'email_change' && credentials.type != 'phone_change') {\n          yield _this13._removeSession();\n        }\n        const endpoint = `${_this13.url}/resend`;\n        if ('email' in credentials) {\n          const {\n            email,\n            type,\n            options\n          } = credentials;\n          const {\n            error\n          } = yield _request(_this13.fetch, 'POST', endpoint, {\n            headers: _this13.headers,\n            body: {\n              email,\n              type,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            },\n            redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo\n          });\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        } else if ('phone' in credentials) {\n          const {\n            phone,\n            type,\n            options\n          } = credentials;\n          const {\n            data,\n            error\n          } = yield _request(_this13.fetch, 'POST', endpoint, {\n            headers: _this13.headers,\n            body: {\n              phone,\n              type,\n              gotrue_meta_security: {\n                captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken\n              }\n            }\n          });\n          return {\n            data: {\n              user: null,\n              session: null,\n              messageId: data === null || data === void 0 ? void 0 : data.message_id\n            },\n            error\n          };\n        }\n        throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Returns the session, refreshing it if necessary.\n   * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n   */\n  getSession() {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      yield _this14.initializePromise;\n      return _this14._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return _this14._useSession(/*#__PURE__*/function () {\n          var _ref9 = _asyncToGenerator(function* (result) {\n            return result;\n          });\n          return function (_x6) {\n            return _ref9.apply(this, arguments);\n          };\n        }());\n      }));\n    })();\n  }\n  /**\n   * Acquires a global lock based on the storage key.\n   */\n  _acquireLock(acquireTimeout, fn) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      _this15._debug('#_acquireLock', 'begin', acquireTimeout);\n      try {\n        if (_this15.lockAcquired) {\n          const last = _this15.pendingInLock.length ? _this15.pendingInLock[_this15.pendingInLock.length - 1] : Promise.resolve();\n          const result = _asyncToGenerator(function* () {\n            yield last;\n            return yield fn();\n          })();\n          _this15.pendingInLock.push(_asyncToGenerator(function* () {\n            try {\n              yield result;\n            } catch (e) {\n              // we just care if it finished\n            }\n          })());\n          return result;\n        }\n        return yield _this15.lock(`lock:${_this15.storageKey}`, acquireTimeout, /*#__PURE__*/_asyncToGenerator(function* () {\n          _this15._debug('#_acquireLock', 'lock acquired for storage key', _this15.storageKey);\n          try {\n            _this15.lockAcquired = true;\n            const result = fn();\n            _this15.pendingInLock.push(_asyncToGenerator(function* () {\n              try {\n                yield result;\n              } catch (e) {\n                // we just care if it finished\n              }\n            })());\n            yield result;\n            // keep draining the queue until there's nothing to wait on\n            while (_this15.pendingInLock.length) {\n              const waitOn = [..._this15.pendingInLock];\n              yield Promise.all(waitOn);\n              _this15.pendingInLock.splice(0, waitOn.length);\n            }\n            return yield result;\n          } finally {\n            _this15._debug('#_acquireLock', 'lock released for storage key', _this15.storageKey);\n            _this15.lockAcquired = false;\n          }\n        }));\n      } finally {\n        _this15._debug('#_acquireLock', 'end');\n      }\n    })();\n  }\n  /**\n   * Use instead of {@link #getSession} inside the library. It is\n   * semantically usually what you want, as getting a session involves some\n   * processing afterwards that requires only one client operating on the\n   * session at once across multiple tabs or processes.\n   */\n  _useSession(fn) {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      _this16._debug('#_useSession', 'begin');\n      try {\n        // the use of __loadSession here is the only correct use of the function!\n        const result = yield _this16.__loadSession();\n        return yield fn(result);\n      } finally {\n        _this16._debug('#_useSession', 'end');\n      }\n    })();\n  }\n  /**\n   * NEVER USE DIRECTLY!\n   *\n   * Always use {@link #_useSession}.\n   */\n  __loadSession() {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      _this17._debug('#__loadSession()', 'begin');\n      if (!_this17.lockAcquired) {\n        _this17._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n      }\n      try {\n        let currentSession = null;\n        const maybeSession = yield getItemAsync(_this17.storage, _this17.storageKey);\n        _this17._debug('#getSession()', 'session from storage', maybeSession);\n        if (maybeSession !== null) {\n          if (_this17._isValidSession(maybeSession)) {\n            currentSession = maybeSession;\n          } else {\n            _this17._debug('#getSession()', 'session from storage is not valid');\n            yield _this17._removeSession();\n          }\n        }\n        if (!currentSession) {\n          return {\n            data: {\n              session: null\n            },\n            error: null\n          };\n        }\n        const hasExpired = currentSession.expires_at ? currentSession.expires_at <= Date.now() / 1000 : false;\n        _this17._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n        if (!hasExpired) {\n          return {\n            data: {\n              session: currentSession\n            },\n            error: null\n          };\n        }\n        const {\n          session,\n          error\n        } = yield _this17._callRefreshToken(currentSession.refresh_token);\n        if (error) {\n          return {\n            data: {\n              session: null\n            },\n            error\n          };\n        }\n        return {\n          data: {\n            session\n          },\n          error: null\n        };\n      } finally {\n        _this17._debug('#__loadSession()', 'end');\n      }\n    })();\n  }\n  /**\n   * Gets the current user details if there is an existing session.\n   * @param jwt Takes in an optional access token jwt. If no jwt is provided, getUser() will attempt to get the jwt from the current session.\n   */\n  getUser(jwt) {\n    var _this18 = this;\n    return _asyncToGenerator(function* () {\n      if (jwt) {\n        return yield _this18._getUser(jwt);\n      }\n      yield _this18.initializePromise;\n      return _this18._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this18._getUser();\n      }));\n    })();\n  }\n  _getUser(jwt) {\n    var _this19 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (jwt) {\n          return yield _request(_this19.fetch, 'GET', `${_this19.url}/user`, {\n            headers: _this19.headers,\n            jwt: jwt,\n            xform: _userResponse\n          });\n        }\n        return yield _this19._useSession(/*#__PURE__*/function () {\n          var _ref13 = _asyncToGenerator(function* (result) {\n            var _a, _b;\n            const {\n              data,\n              error\n            } = result;\n            if (error) {\n              throw error;\n            }\n            return yield _request(_this19.fetch, 'GET', `${_this19.url}/user`, {\n              headers: _this19.headers,\n              jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n              xform: _userResponse\n            });\n          });\n          return function (_x7) {\n            return _ref13.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Updates user data for a logged in user.\n   */\n  updateUser(_x8) {\n    var _this20 = this;\n    return _asyncToGenerator(function* (attributes, options = {}) {\n      yield _this20.initializePromise;\n      return yield _this20._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this20._updateUser(attributes, options);\n      }));\n    }).apply(this, arguments);\n  }\n  _updateUser(_x9) {\n    var _this21 = this;\n    return _asyncToGenerator(function* (attributes, options = {}) {\n      try {\n        return yield _this21._useSession(/*#__PURE__*/function () {\n          var _ref15 = _asyncToGenerator(function* (result) {\n            const {\n              data: sessionData,\n              error: sessionError\n            } = result;\n            if (sessionError) {\n              throw sessionError;\n            }\n            if (!sessionData.session) {\n              throw new AuthSessionMissingError();\n            }\n            const session = sessionData.session;\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (_this21.flowType === 'pkce' && attributes.email != null) {\n              const codeVerifier = generatePKCEVerifier();\n              yield setItemAsync(_this21.storage, `${_this21.storageKey}-code-verifier`, codeVerifier);\n              codeChallenge = yield generatePKCEChallenge(codeVerifier);\n              codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n            }\n            const {\n              data,\n              error: userError\n            } = yield _request(_this21.fetch, 'PUT', `${_this21.url}/user`, {\n              headers: _this21.headers,\n              redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n              body: Object.assign(Object.assign({}, attributes), {\n                code_challenge: codeChallenge,\n                code_challenge_method: codeChallengeMethod\n              }),\n              jwt: session.access_token,\n              xform: _userResponse\n            });\n            if (userError) throw userError;\n            session.user = data.user;\n            yield _this21._saveSession(session);\n            yield _this21._notifyAllSubscribers('USER_UPDATED', session);\n            return {\n              data: {\n                user: session.user\n              },\n              error: null\n            };\n          });\n          return function (_x0) {\n            return _ref15.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Decodes a JWT (without performing any validation).\n   */\n  _decodeJWT(jwt) {\n    return decodeJWTPayload(jwt);\n  }\n  /**\n   * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n   * If the refresh token or access token in the current session is invalid, an error will be thrown.\n   * @param currentSession The current session that minimally contains an access token and refresh token.\n   */\n  setSession(currentSession) {\n    var _this22 = this;\n    return _asyncToGenerator(function* () {\n      yield _this22.initializePromise;\n      return yield _this22._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this22._setSession(currentSession);\n      }));\n    })();\n  }\n  _setSession(currentSession) {\n    var _this23 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!currentSession.access_token || !currentSession.refresh_token) {\n          throw new AuthSessionMissingError();\n        }\n        const timeNow = Date.now() / 1000;\n        let expiresAt = timeNow;\n        let hasExpired = true;\n        let session = null;\n        const payload = decodeJWTPayload(currentSession.access_token);\n        if (payload.exp) {\n          expiresAt = payload.exp;\n          hasExpired = expiresAt <= timeNow;\n        }\n        if (hasExpired) {\n          const {\n            session: refreshedSession,\n            error\n          } = yield _this23._callRefreshToken(currentSession.refresh_token);\n          if (error) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: error\n            };\n          }\n          if (!refreshedSession) {\n            return {\n              data: {\n                user: null,\n                session: null\n              },\n              error: null\n            };\n          }\n          session = refreshedSession;\n        } else {\n          const {\n            data,\n            error\n          } = yield _this23._getUser(currentSession.access_token);\n          if (error) {\n            throw error;\n          }\n          session = {\n            access_token: currentSession.access_token,\n            refresh_token: currentSession.refresh_token,\n            user: data.user,\n            token_type: 'bearer',\n            expires_in: expiresAt - timeNow,\n            expires_at: expiresAt\n          };\n          yield _this23._saveSession(session);\n          yield _this23._notifyAllSubscribers('SIGNED_IN', session);\n        }\n        return {\n          data: {\n            user: session.user,\n            session\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              session: null,\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Returns a new session, regardless of expiry status.\n   * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n   * If the current session's refresh token is invalid, an error will be thrown.\n   * @param currentSession The current session. If passed in, it must contain a refresh token.\n   */\n  refreshSession(currentSession) {\n    var _this24 = this;\n    return _asyncToGenerator(function* () {\n      yield _this24.initializePromise;\n      return yield _this24._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this24._refreshSession(currentSession);\n      }));\n    })();\n  }\n  _refreshSession(currentSession) {\n    var _this25 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this25._useSession(/*#__PURE__*/function () {\n          var _ref18 = _asyncToGenerator(function* (result) {\n            var _a;\n            if (!currentSession) {\n              const {\n                data,\n                error\n              } = result;\n              if (error) {\n                throw error;\n              }\n              currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n            }\n            if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n              throw new AuthSessionMissingError();\n            }\n            const {\n              session,\n              error\n            } = yield _this25._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n              return {\n                data: {\n                  user: null,\n                  session: null\n                },\n                error: error\n              };\n            }\n            if (!session) {\n              return {\n                data: {\n                  user: null,\n                  session: null\n                },\n                error: null\n              };\n            }\n            return {\n              data: {\n                user: session.user,\n                session\n              },\n              error: null\n            };\n          });\n          return function (_x1) {\n            return _ref18.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              user: null,\n              session: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Gets the session data from a URL string\n   */\n  _getSessionFromURL(isPKCEFlow) {\n    var _this26 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!isBrowser()) throw new AuthImplicitGrantRedirectError('No browser detected.');\n        if (_this26.flowType === 'implicit' && !_this26._isImplicitGrantFlow()) {\n          throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n        } else if (_this26.flowType == 'pkce' && !isPKCEFlow) {\n          throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n        }\n        const params = parseParametersFromURL(window.location.href);\n        if (isPKCEFlow) {\n          if (!params.code) throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n          const {\n            data,\n            error\n          } = yield _this26._exchangeCodeForSession(params.code);\n          if (error) throw error;\n          const url = new URL(window.location.href);\n          url.searchParams.delete('code');\n          window.history.replaceState(window.history.state, '', url.toString());\n          return {\n            data: {\n              session: data.session,\n              redirectType: null\n            },\n            error: null\n          };\n        }\n        if (params.error || params.error_description || params.error_code) {\n          throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n            error: params.error || 'unspecified_error',\n            code: params.error_code || 'unspecified_code'\n          });\n        }\n        const {\n          provider_token,\n          provider_refresh_token,\n          access_token,\n          refresh_token,\n          expires_in,\n          expires_at,\n          token_type\n        } = params;\n        if (!access_token || !expires_in || !refresh_token || !token_type) {\n          throw new AuthImplicitGrantRedirectError('No session defined in URL');\n        }\n        const timeNow = Math.round(Date.now() / 1000);\n        const expiresIn = parseInt(expires_in);\n        let expiresAt = timeNow + expiresIn;\n        if (expires_at) {\n          expiresAt = parseInt(expires_at);\n        }\n        const actuallyExpiresIn = expiresAt - timeNow;\n        if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION) {\n          console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n        }\n        const issuedAt = expiresAt - expiresIn;\n        if (timeNow - issuedAt >= 120) {\n          console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n        } else if (timeNow - issuedAt < 0) {\n          console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clok for skew', issuedAt, expiresAt, timeNow);\n        }\n        const {\n          data,\n          error\n        } = yield _this26._getUser(access_token);\n        if (error) throw error;\n        const session = {\n          provider_token,\n          provider_refresh_token,\n          access_token,\n          expires_in: expiresIn,\n          expires_at: expiresAt,\n          refresh_token,\n          token_type,\n          user: data.user\n        };\n        // Remove tokens from URL\n        window.location.hash = '';\n        _this26._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n        return {\n          data: {\n            session,\n            redirectType: params.type\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              session: null,\n              redirectType: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n   */\n  _isImplicitGrantFlow() {\n    const params = parseParametersFromURL(window.location.href);\n    return !!(isBrowser() && (params.access_token || params.error_description));\n  }\n  /**\n   * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n   */\n  _isPKCEFlow() {\n    var _this27 = this;\n    return _asyncToGenerator(function* () {\n      const params = parseParametersFromURL(window.location.href);\n      const currentStorageContent = yield getItemAsync(_this27.storage, `${_this27.storageKey}-code-verifier`);\n      return !!(params.code && currentStorageContent);\n    })();\n  }\n  /**\n   * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n   *\n   * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n   * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n   *\n   * If using `others` scope, no `SIGNED_OUT` event is fired!\n   */\n  signOut() {\n    var _this28 = this;\n    return _asyncToGenerator(function* (options = {\n      scope: 'global'\n    }) {\n      yield _this28.initializePromise;\n      return yield _this28._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this28._signOut(options);\n      }));\n    }).apply(this, arguments);\n  }\n  _signOut() {\n    var _this29 = this;\n    return _asyncToGenerator(function* ({\n      scope\n    } = {\n      scope: 'global'\n    }) {\n      return yield _this29._useSession(/*#__PURE__*/function () {\n        var _ref20 = _asyncToGenerator(function* (result) {\n          var _a;\n          const {\n            data,\n            error: sessionError\n          } = result;\n          if (sessionError) {\n            return {\n              error: sessionError\n            };\n          }\n          const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n          if (accessToken) {\n            const {\n              error\n            } = yield _this29.admin.signOut(accessToken, scope);\n            if (error) {\n              // ignore 404s since user might not exist anymore\n              // ignore 401s since an invalid or expired JWT should sign out the current session\n              if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401))) {\n                return {\n                  error\n                };\n              }\n            }\n          }\n          if (scope !== 'others') {\n            yield _this29._removeSession();\n            yield removeItemAsync(_this29.storage, `${_this29.storageKey}-code-verifier`);\n            yield _this29._notifyAllSubscribers('SIGNED_OUT', null);\n          }\n          return {\n            error: null\n          };\n        });\n        return function (_x10) {\n          return _ref20.apply(this, arguments);\n        };\n      }());\n    }).apply(this, arguments);\n  }\n  /**\n   * Receive a notification every time an auth event happens.\n   * @param callback A callback function to be invoked when an auth event happens.\n   */\n  onAuthStateChange(callback) {\n    var _this30 = this;\n    const id = uuid();\n    const subscription = {\n      id,\n      callback,\n      unsubscribe: () => {\n        this._debug('#unsubscribe()', 'state change callback with id removed', id);\n        this.stateChangeEmitters.delete(id);\n      }\n    };\n    this._debug('#onAuthStateChange()', 'registered callback with id', id);\n    this.stateChangeEmitters.set(id, subscription);\n    _asyncToGenerator(function* () {\n      yield _this30.initializePromise;\n      yield _this30._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        _this30._emitInitialSession(id);\n      }));\n    })();\n    return {\n      data: {\n        subscription\n      }\n    };\n  }\n  _emitInitialSession(id) {\n    var _this31 = this;\n    return _asyncToGenerator(function* () {\n      return yield _this31._useSession(/*#__PURE__*/function () {\n        var _ref23 = _asyncToGenerator(function* (result) {\n          var _a, _b;\n          try {\n            const {\n              data: {\n                session\n              },\n              error\n            } = result;\n            if (error) throw error;\n            yield (_a = _this31.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session);\n            _this31._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n          } catch (err) {\n            yield (_b = _this31.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null);\n            _this31._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n            console.error(err);\n          }\n        });\n        return function (_x11) {\n          return _ref23.apply(this, arguments);\n        };\n      }());\n    })();\n  }\n  /**\n   * Sends a password reset request to an email address. This method supports the PKCE flow.\n   *\n   * @param email The email address of the user.\n   * @param options.redirectTo The URL to send the user to after they click the password reset link.\n   * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n   */\n  resetPasswordForEmail(_x12) {\n    var _this32 = this;\n    return _asyncToGenerator(function* (email, options = {}) {\n      let codeChallenge = null;\n      let codeChallengeMethod = null;\n      if (_this32.flowType === 'pkce') {\n        const codeVerifier = generatePKCEVerifier();\n        yield setItemAsync(_this32.storage, `${_this32.storageKey}-code-verifier`, `${codeVerifier}/PASSWORD_RECOVERY`);\n        codeChallenge = yield generatePKCEChallenge(codeVerifier);\n        codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n      }\n      try {\n        return yield _request(_this32.fetch, 'POST', `${_this32.url}/recover`, {\n          body: {\n            email,\n            code_challenge: codeChallenge,\n            code_challenge_method: codeChallengeMethod,\n            gotrue_meta_security: {\n              captcha_token: options.captchaToken\n            }\n          },\n          headers: _this32.headers,\n          redirectTo: options.redirectTo\n        });\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * Gets all the identities linked to a user.\n   */\n  getUserIdentities() {\n    var _this33 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      try {\n        const {\n          data,\n          error\n        } = yield _this33.getUser();\n        if (error) throw error;\n        return {\n          data: {\n            identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : []\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Links an oauth identity to an existing user.\n   * This method supports the PKCE flow.\n   */\n  linkIdentity(credentials) {\n    var _this34 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      try {\n        const {\n          data,\n          error\n        } = yield _this34._useSession(/*#__PURE__*/function () {\n          var _ref24 = _asyncToGenerator(function* (result) {\n            var _a, _b, _c, _d, _e;\n            const {\n              data,\n              error\n            } = result;\n            if (error) throw error;\n            const url = yield _this34._getUrlForProvider(`${_this34.url}/user/identities/authorize`, credentials.provider, {\n              redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n              scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n              queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n              skipBrowserRedirect: true\n            });\n            return yield _request(_this34.fetch, 'GET', url, {\n              headers: _this34.headers,\n              jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined\n            });\n          });\n          return function (_x13) {\n            return _ref24.apply(this, arguments);\n          };\n        }());\n        if (error) throw error;\n        if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n          window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n        }\n        return {\n          data: {\n            provider: credentials.provider,\n            url: data === null || data === void 0 ? void 0 : data.url\n          },\n          error: null\n        };\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: {\n              provider: credentials.provider,\n              url: null\n            },\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n   */\n  unlinkIdentity(identity) {\n    var _this35 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this35._useSession(/*#__PURE__*/function () {\n          var _ref25 = _asyncToGenerator(function* (result) {\n            var _a, _b;\n            const {\n              data,\n              error\n            } = result;\n            if (error) {\n              throw error;\n            }\n            return yield _request(_this35.fetch, 'DELETE', `${_this35.url}/user/identities/${identity.identity_id}`, {\n              headers: _this35.headers,\n              jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined\n            });\n          });\n          return function (_x14) {\n            return _ref25.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Generates a new JWT.\n   * @param refreshToken A valid refresh token that was returned on login.\n   */\n  _refreshAccessToken(refreshToken) {\n    var _this36 = this;\n    return _asyncToGenerator(function* () {\n      const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n      _this36._debug(debugName, 'begin');\n      try {\n        const startedAt = Date.now();\n        // will attempt to refresh the token with exponential backoff\n        return yield retryable(/*#__PURE__*/function () {\n          var _ref26 = _asyncToGenerator(function* (attempt) {\n            yield sleep(attempt * 200); // 0, 200, 400, 800, ...\n            _this36._debug(debugName, 'refreshing attempt', attempt);\n            return yield _request(_this36.fetch, 'POST', `${_this36.url}/token?grant_type=refresh_token`, {\n              body: {\n                refresh_token: refreshToken\n              },\n              headers: _this36.headers,\n              xform: _sessionResponse\n            });\n          });\n          return function (_x15) {\n            return _ref26.apply(this, arguments);\n          };\n        }(), (attempt, _, result) => result && result.error && isAuthRetryableFetchError(result.error) &&\n        // retryable only if the request can be sent before the backoff overflows the tick duration\n        Date.now() + (attempt + 1) * 200 - startedAt < AUTO_REFRESH_TICK_DURATION);\n      } catch (error) {\n        _this36._debug(debugName, 'error', error);\n        if (isAuthError(error)) {\n          return {\n            data: {\n              session: null,\n              user: null\n            },\n            error\n          };\n        }\n        throw error;\n      } finally {\n        _this36._debug(debugName, 'end');\n      }\n    })();\n  }\n  _isValidSession(maybeSession) {\n    const isValidSession = typeof maybeSession === 'object' && maybeSession !== null && 'access_token' in maybeSession && 'refresh_token' in maybeSession && 'expires_at' in maybeSession;\n    return isValidSession;\n  }\n  _handleProviderSignIn(provider, options) {\n    var _this37 = this;\n    return _asyncToGenerator(function* () {\n      const url = yield _this37._getUrlForProvider(`${_this37.url}/authorize`, provider, {\n        redirectTo: options.redirectTo,\n        scopes: options.scopes,\n        queryParams: options.queryParams\n      });\n      _this37._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n      // try to open on the browser\n      if (isBrowser() && !options.skipBrowserRedirect) {\n        window.location.assign(url);\n      }\n      return {\n        data: {\n          provider,\n          url\n        },\n        error: null\n      };\n    })();\n  }\n  /**\n   * Recovers the session from LocalStorage and refreshes\n   * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n   */\n  _recoverAndRefresh() {\n    var _this38 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const debugName = '#_recoverAndRefresh()';\n      _this38._debug(debugName, 'begin');\n      try {\n        const currentSession = yield getItemAsync(_this38.storage, _this38.storageKey);\n        _this38._debug(debugName, 'session from storage', currentSession);\n        if (!_this38._isValidSession(currentSession)) {\n          _this38._debug(debugName, 'session is not valid');\n          if (currentSession !== null) {\n            yield _this38._removeSession();\n          }\n          return;\n        }\n        const timeNow = Math.round(Date.now() / 1000);\n        const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) < timeNow + EXPIRY_MARGIN;\n        _this38._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN}s`);\n        if (expiresWithMargin) {\n          if (_this38.autoRefreshToken && currentSession.refresh_token) {\n            const {\n              error\n            } = yield _this38._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n              console.error(error);\n              if (!isAuthRetryableFetchError(error)) {\n                _this38._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                yield _this38._removeSession();\n              }\n            }\n          }\n        } else {\n          // no need to persist currentSession again, as we just loaded it from\n          // local storage; persisting it again may overwrite a value saved by\n          // another client with access to the same local storage\n          yield _this38._notifyAllSubscribers('SIGNED_IN', currentSession);\n        }\n      } catch (err) {\n        _this38._debug(debugName, 'error', err);\n        console.error(err);\n        return;\n      } finally {\n        _this38._debug(debugName, 'end');\n      }\n    })();\n  }\n  _callRefreshToken(refreshToken) {\n    var _this39 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      if (!refreshToken) {\n        throw new AuthSessionMissingError();\n      }\n      // refreshing is already in progress\n      if (_this39.refreshingDeferred) {\n        return _this39.refreshingDeferred.promise;\n      }\n      const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n      _this39._debug(debugName, 'begin');\n      try {\n        _this39.refreshingDeferred = new Deferred();\n        const {\n          data,\n          error\n        } = yield _this39._refreshAccessToken(refreshToken);\n        if (error) throw error;\n        if (!data.session) throw new AuthSessionMissingError();\n        yield _this39._saveSession(data.session);\n        yield _this39._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n        const result = {\n          session: data.session,\n          error: null\n        };\n        _this39.refreshingDeferred.resolve(result);\n        return result;\n      } catch (error) {\n        _this39._debug(debugName, 'error', error);\n        if (isAuthError(error)) {\n          const result = {\n            session: null,\n            error\n          };\n          if (!isAuthRetryableFetchError(error)) {\n            yield _this39._removeSession();\n            yield _this39._notifyAllSubscribers('SIGNED_OUT', null);\n          }\n          (_a = _this39.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n          return result;\n        }\n        (_b = _this39.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n        throw error;\n      } finally {\n        _this39.refreshingDeferred = null;\n        _this39._debug(debugName, 'end');\n      }\n    })();\n  }\n  _notifyAllSubscribers(_x16, _x17) {\n    var _this40 = this;\n    return _asyncToGenerator(function* (event, session, broadcast = true) {\n      const debugName = `#_notifyAllSubscribers(${event})`;\n      _this40._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n      try {\n        if (_this40.broadcastChannel && broadcast) {\n          _this40.broadcastChannel.postMessage({\n            event,\n            session\n          });\n        }\n        const errors = [];\n        const promises = Array.from(_this40.stateChangeEmitters.values()).map(/*#__PURE__*/function () {\n          var _ref27 = _asyncToGenerator(function* (x) {\n            try {\n              yield x.callback(event, session);\n            } catch (e) {\n              errors.push(e);\n            }\n          });\n          return function (_x18) {\n            return _ref27.apply(this, arguments);\n          };\n        }());\n        yield Promise.all(promises);\n        if (errors.length > 0) {\n          for (let i = 0; i < errors.length; i += 1) {\n            console.error(errors[i]);\n          }\n          throw errors[0];\n        }\n      } finally {\n        _this40._debug(debugName, 'end');\n      }\n    }).apply(this, arguments);\n  }\n  /**\n   * set currentSession and currentUser\n   * process to _startAutoRefreshToken if possible\n   */\n  _saveSession(session) {\n    var _this41 = this;\n    return _asyncToGenerator(function* () {\n      _this41._debug('#_saveSession()', session);\n      yield setItemAsync(_this41.storage, _this41.storageKey, session);\n    })();\n  }\n  _removeSession() {\n    var _this42 = this;\n    return _asyncToGenerator(function* () {\n      _this42._debug('#_removeSession()');\n      yield removeItemAsync(_this42.storage, _this42.storageKey);\n    })();\n  }\n  /**\n   * Removes any registered visibilitychange callback.\n   *\n   * {@see #startAutoRefresh}\n   * {@see #stopAutoRefresh}\n   */\n  _removeVisibilityChangedCallback() {\n    this._debug('#_removeVisibilityChangedCallback()');\n    const callback = this.visibilityChangedCallback;\n    this.visibilityChangedCallback = null;\n    try {\n      if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n        window.removeEventListener('visibilitychange', callback);\n      }\n    } catch (e) {\n      console.error('removing visibilitychange callback failed', e);\n    }\n  }\n  /**\n   * This is the private implementation of {@link #startAutoRefresh}. Use this\n   * within the library.\n   */\n  _startAutoRefresh() {\n    var _this43 = this;\n    return _asyncToGenerator(function* () {\n      yield _this43._stopAutoRefresh();\n      _this43._debug('#_startAutoRefresh()');\n      const ticker = setInterval(() => _this43._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION);\n      _this43.autoRefreshTicker = ticker;\n      if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n        // ticker is a NodeJS Timeout object that has an `unref` method\n        // https://nodejs.org/api/timers.html#timeoutunref\n        // When auto refresh is used in NodeJS (like for testing) the\n        // `setInterval` is preventing the process from being marked as\n        // finished and tests run endlessly. This can be prevented by calling\n        // `unref()` on the returned object.\n        ticker.unref();\n        // @ts-ignore\n      } else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n        // similar like for NodeJS, but with the Deno API\n        // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n        // @ts-ignore\n        Deno.unrefTimer(ticker);\n      }\n      // run the tick immediately, but in the next pass of the event loop so that\n      // #_initialize can be allowed to complete without recursively waiting on\n      // itself\n      setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n        yield _this43.initializePromise;\n        yield _this43._autoRefreshTokenTick();\n      }), 0);\n    })();\n  }\n  /**\n   * This is the private implementation of {@link #stopAutoRefresh}. Use this\n   * within the library.\n   */\n  _stopAutoRefresh() {\n    var _this44 = this;\n    return _asyncToGenerator(function* () {\n      _this44._debug('#_stopAutoRefresh()');\n      const ticker = _this44.autoRefreshTicker;\n      _this44.autoRefreshTicker = null;\n      if (ticker) {\n        clearInterval(ticker);\n      }\n    })();\n  }\n  /**\n   * Starts an auto-refresh process in the background. The session is checked\n   * every few seconds. Close to the time of expiration a process is started to\n   * refresh the session. If refreshing fails it will be retried for as long as\n   * necessary.\n   *\n   * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n   * to call this function, it will be called for you.\n   *\n   * On browsers the refresh process works only when the tab/window is in the\n   * foreground to conserve resources as well as prevent race conditions and\n   * flooding auth with requests. If you call this method any managed\n   * visibility change callback will be removed and you must manage visibility\n   * changes on your own.\n   *\n   * On non-browser platforms the refresh process works *continuously* in the\n   * background, which may not be desirable. You should hook into your\n   * platform's foreground indication mechanism and call these methods\n   * appropriately to conserve resources.\n   *\n   * {@see #stopAutoRefresh}\n   */\n  startAutoRefresh() {\n    var _this45 = this;\n    return _asyncToGenerator(function* () {\n      _this45._removeVisibilityChangedCallback();\n      yield _this45._startAutoRefresh();\n    })();\n  }\n  /**\n   * Stops an active auto refresh process running in the background (if any).\n   *\n   * If you call this method any managed visibility change callback will be\n   * removed and you must manage visibility changes on your own.\n   *\n   * See {@link #startAutoRefresh} for more details.\n   */\n  stopAutoRefresh() {\n    var _this46 = this;\n    return _asyncToGenerator(function* () {\n      _this46._removeVisibilityChangedCallback();\n      yield _this46._stopAutoRefresh();\n    })();\n  }\n  /**\n   * Runs the auto refresh token tick.\n   */\n  _autoRefreshTokenTick() {\n    var _this47 = this;\n    return _asyncToGenerator(function* () {\n      _this47._debug('#_autoRefreshTokenTick()', 'begin');\n      try {\n        yield _this47._acquireLock(0, /*#__PURE__*/_asyncToGenerator(function* () {\n          try {\n            const now = Date.now();\n            try {\n              return yield _this47._useSession(/*#__PURE__*/function () {\n                var _ref30 = _asyncToGenerator(function* (result) {\n                  const {\n                    data: {\n                      session\n                    }\n                  } = result;\n                  if (!session || !session.refresh_token || !session.expires_at) {\n                    _this47._debug('#_autoRefreshTokenTick()', 'no session');\n                    return;\n                  }\n                  // session will expire in this many ticks (or has already expired if <= 0)\n                  const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION);\n                  _this47._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                  if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                    yield _this47._callRefreshToken(session.refresh_token);\n                  }\n                });\n                return function (_x19) {\n                  return _ref30.apply(this, arguments);\n                };\n              }());\n            } catch (e) {\n              console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n            }\n          } finally {\n            _this47._debug('#_autoRefreshTokenTick()', 'end');\n          }\n        }));\n      } catch (e) {\n        if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n          _this47._debug('auto refresh token tick lock not available');\n        } else {\n          throw e;\n        }\n      }\n    })();\n  }\n  /**\n   * Registers callbacks on the browser / platform, which in-turn run\n   * algorithms when the browser window/tab are in foreground. On non-browser\n   * platforms it assumes always foreground.\n   */\n  _handleVisibilityChange() {\n    var _this48 = this;\n    return _asyncToGenerator(function* () {\n      _this48._debug('#_handleVisibilityChange()');\n      if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n        if (_this48.autoRefreshToken) {\n          // in non-browser environments the refresh token ticker runs always\n          _this48.startAutoRefresh();\n        }\n        return false;\n      }\n      try {\n        _this48.visibilityChangedCallback = /*#__PURE__*/_asyncToGenerator(function* () {\n          return yield _this48._onVisibilityChanged(false);\n        });\n        window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', _this48.visibilityChangedCallback);\n        // now immediately call the visbility changed callback to setup with the\n        // current visbility state\n        yield _this48._onVisibilityChanged(true); // initial call\n      } catch (error) {\n        console.error('_handleVisibilityChange', error);\n      }\n    })();\n  }\n  /**\n   * Callback registered with `window.addEventListener('visibilitychange')`.\n   */\n  _onVisibilityChanged(calledFromInitialize) {\n    var _this49 = this;\n    return _asyncToGenerator(function* () {\n      const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n      _this49._debug(methodName, 'visibilityState', document.visibilityState);\n      if (document.visibilityState === 'visible') {\n        if (_this49.autoRefreshToken) {\n          // in browser environments the refresh token ticker runs only on focused tabs\n          // which prevents race conditions\n          _this49._startAutoRefresh();\n        }\n        if (!calledFromInitialize) {\n          // called when the visibility has changed, i.e. the browser\n          // transitioned from hidden -> visible so we need to see if the session\n          // should be recovered immediately... but to do that we need to acquire\n          // the lock first asynchronously\n          yield _this49.initializePromise;\n          yield _this49._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n            if (document.visibilityState !== 'visible') {\n              _this49._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n              // visibility has changed while waiting for the lock, abort\n              return;\n            }\n            // recover the session\n            yield _this49._recoverAndRefresh();\n          }));\n        }\n      } else if (document.visibilityState === 'hidden') {\n        if (_this49.autoRefreshToken) {\n          _this49._stopAutoRefresh();\n        }\n      }\n    })();\n  }\n  /**\n   * Generates the relevant login URL for a third-party provider.\n   * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n   * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n   * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n   */\n  _getUrlForProvider(url, provider, options) {\n    var _this50 = this;\n    return _asyncToGenerator(function* () {\n      const urlParams = [`provider=${encodeURIComponent(provider)}`];\n      if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n      }\n      if (options === null || options === void 0 ? void 0 : options.scopes) {\n        urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n      }\n      if (_this50.flowType === 'pkce') {\n        const codeVerifier = generatePKCEVerifier();\n        yield setItemAsync(_this50.storage, `${_this50.storageKey}-code-verifier`, codeVerifier);\n        const codeChallenge = yield generatePKCEChallenge(codeVerifier);\n        const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        _this50._debug('PKCE', 'code verifier', `${codeVerifier.substring(0, 5)}...`, 'code challenge', codeChallenge, 'method', codeChallengeMethod);\n        const flowParams = new URLSearchParams({\n          code_challenge: `${encodeURIComponent(codeChallenge)}`,\n          code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`\n        });\n        urlParams.push(flowParams.toString());\n      }\n      if (options === null || options === void 0 ? void 0 : options.queryParams) {\n        const query = new URLSearchParams(options.queryParams);\n        urlParams.push(query.toString());\n      }\n      if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n        urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n      }\n      return `${url}?${urlParams.join('&')}`;\n    })();\n  }\n  _unenroll(params) {\n    var _this51 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this51._useSession(/*#__PURE__*/function () {\n          var _ref33 = _asyncToGenerator(function* (result) {\n            var _a;\n            const {\n              data: sessionData,\n              error: sessionError\n            } = result;\n            if (sessionError) {\n              return {\n                data: null,\n                error: sessionError\n              };\n            }\n            return yield _request(_this51.fetch, 'DELETE', `${_this51.url}/factors/${params.factorId}`, {\n              headers: _this51.headers,\n              jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n            });\n          });\n          return function (_x20) {\n            return _ref33.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#enroll}\n   */\n  _enroll(params) {\n    var _this52 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this52._useSession(/*#__PURE__*/function () {\n          var _ref34 = _asyncToGenerator(function* (result) {\n            var _a, _b;\n            const {\n              data: sessionData,\n              error: sessionError\n            } = result;\n            if (sessionError) {\n              return {\n                data: null,\n                error: sessionError\n              };\n            }\n            const {\n              data,\n              error\n            } = yield _request(_this52.fetch, 'POST', `${_this52.url}/factors`, {\n              body: {\n                friendly_name: params.friendlyName,\n                factor_type: params.factorType,\n                issuer: params.issuer\n              },\n              headers: _this52.headers,\n              jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n            });\n            if (error) {\n              return {\n                data: null,\n                error\n              };\n            }\n            if ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code) {\n              data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n            }\n            return {\n              data,\n              error: null\n            };\n          });\n          return function (_x21) {\n            return _ref34.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        if (isAuthError(error)) {\n          return {\n            data: null,\n            error\n          };\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#verify}\n   */\n  _verify(params) {\n    var _this53 = this;\n    return _asyncToGenerator(function* () {\n      return _this53._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          return yield _this53._useSession(/*#__PURE__*/function () {\n            var _ref36 = _asyncToGenerator(function* (result) {\n              var _a;\n              const {\n                data: sessionData,\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                return {\n                  data: null,\n                  error: sessionError\n                };\n              }\n              const {\n                data,\n                error\n              } = yield _request(_this53.fetch, 'POST', `${_this53.url}/factors/${params.factorId}/verify`, {\n                body: {\n                  code: params.code,\n                  challenge_id: params.challengeId\n                },\n                headers: _this53.headers,\n                jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n              });\n              if (error) {\n                return {\n                  data: null,\n                  error\n                };\n              }\n              yield _this53._saveSession(Object.assign({\n                expires_at: Math.round(Date.now() / 1000) + data.expires_in\n              }, data));\n              yield _this53._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n              return {\n                data,\n                error\n              };\n            });\n            return function (_x22) {\n              return _ref36.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      }));\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#challenge}\n   */\n  _challenge(params) {\n    var _this54 = this;\n    return _asyncToGenerator(function* () {\n      return _this54._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          return yield _this54._useSession(/*#__PURE__*/function () {\n            var _ref38 = _asyncToGenerator(function* (result) {\n              var _a;\n              const {\n                data: sessionData,\n                error: sessionError\n              } = result;\n              if (sessionError) {\n                return {\n                  data: null,\n                  error: sessionError\n                };\n              }\n              return yield _request(_this54.fetch, 'POST', `${_this54.url}/factors/${params.factorId}/challenge`, {\n                headers: _this54.headers,\n                jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token\n              });\n            });\n            return function (_x23) {\n              return _ref38.apply(this, arguments);\n            };\n          }());\n        } catch (error) {\n          if (isAuthError(error)) {\n            return {\n              data: null,\n              error\n            };\n          }\n          throw error;\n        }\n      }));\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#challengeAndVerify}\n   */\n  _challengeAndVerify(params) {\n    var _this55 = this;\n    return _asyncToGenerator(function* () {\n      // both _challenge and _verify independently acquire the lock, so no need\n      // to acquire it here\n      const {\n        data: challengeData,\n        error: challengeError\n      } = yield _this55._challenge({\n        factorId: params.factorId\n      });\n      if (challengeError) {\n        return {\n          data: null,\n          error: challengeError\n        };\n      }\n      return yield _this55._verify({\n        factorId: params.factorId,\n        challengeId: challengeData.id,\n        code: params.code\n      });\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#listFactors}\n   */\n  _listFactors() {\n    var _this56 = this;\n    return _asyncToGenerator(function* () {\n      // use #getUser instead of #_getUser as the former acquires a lock\n      const {\n        data: {\n          user\n        },\n        error: userError\n      } = yield _this56.getUser();\n      if (userError) {\n        return {\n          data: null,\n          error: userError\n        };\n      }\n      const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n      const totp = factors.filter(factor => factor.factor_type === 'totp' && factor.status === 'verified');\n      return {\n        data: {\n          all: factors,\n          totp\n        },\n        error: null\n      };\n    })();\n  }\n  /**\n   * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n   */\n  _getAuthenticatorAssuranceLevel() {\n    var _this57 = this;\n    return _asyncToGenerator(function* () {\n      return _this57._acquireLock(-1, /*#__PURE__*/_asyncToGenerator(function* () {\n        return yield _this57._useSession(/*#__PURE__*/function () {\n          var _ref40 = _asyncToGenerator(function* (result) {\n            var _a, _b;\n            const {\n              data: {\n                session\n              },\n              error: sessionError\n            } = result;\n            if (sessionError) {\n              return {\n                data: null,\n                error: sessionError\n              };\n            }\n            if (!session) {\n              return {\n                data: {\n                  currentLevel: null,\n                  nextLevel: null,\n                  currentAuthenticationMethods: []\n                },\n                error: null\n              };\n            }\n            const payload = _this57._decodeJWT(session.access_token);\n            let currentLevel = null;\n            if (payload.aal) {\n              currentLevel = payload.aal;\n            }\n            let nextLevel = currentLevel;\n            const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter(factor => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n            if (verifiedFactors.length > 0) {\n              nextLevel = 'aal2';\n            }\n            const currentAuthenticationMethods = payload.amr || [];\n            return {\n              data: {\n                currentLevel,\n                nextLevel,\n                currentAuthenticationMethods\n              },\n              error: null\n            };\n          });\n          return function (_x24) {\n            return _ref40.apply(this, arguments);\n          };\n        }());\n      }));\n    })();\n  }\n}\nGoTrueClient.nextInstanceID = 0;", "map": {"version": 3, "names": ["GoTrueAdminApi", "DEFAULT_HEADERS", "EXPIRY_MARGIN", "GOTRUE_URL", "STORAGE_KEY", "AuthImplicitGrantRedirectError", "AuthPKCEGrantCodeExchangeError", "AuthInvalidCredentialsError", "AuthSessionMissingError", "AuthInvalidTokenResponseError", "AuthUnknownError", "isAuthApiError", "isAuthError", "isAuthRetryableFetchError", "_request", "_sessionResponse", "_sessionResponsePassword", "_userResponse", "_ssoResponse", "decodeJWTPayload", "Deferred", "getItemAsync", "<PERSON><PERSON><PERSON><PERSON>", "removeItemAsync", "resolveFetch", "setItemAsync", "uuid", "retryable", "sleep", "generatePKCEVerifier", "generatePKCEChallenge", "supportsLocalStorage", "parseParametersFromURL", "localStorageAdapter", "memoryLocalStorageAdapter", "polyfillGlobalThis", "version", "LockAcquireTimeoutError", "DEFAULT_OPTIONS", "url", "storageKey", "autoRefreshToken", "persistSession", "detectSessionInUrl", "headers", "flowType", "debug", "AUTO_REFRESH_TICK_DURATION", "AUTO_REFRESH_TICK_THRESHOLD", "lockNoOp", "_x", "_x2", "_x3", "_lockNoOp", "apply", "arguments", "_asyncToGenerator", "name", "acquireTimeout", "fn", "GoTrueClient", "constructor", "options", "_this", "_a", "memoryStorage", "stateChangeEmitters", "Map", "autoRefreshTicker", "visibilityChangedCallback", "refreshing<PERSON><PERSON>erred", "initializePromise", "lockAcquired", "pendingInLock", "broadcastChannel", "logger", "console", "log", "instanceID", "nextInstanceID", "warn", "settings", "Object", "assign", "logDebugMessages", "admin", "fetch", "lock", "mfa", "verify", "_verify", "bind", "enroll", "_enroll", "unenroll", "_unenroll", "challenge", "_challenge", "listFactors", "_listFactors", "challengeAndVerify", "_challengeAndVerify", "getAuthenticatorAssuranceLevel", "_getAuthenticatorAssuranceLevel", "storage", "globalThis", "BroadcastChannel", "e", "error", "addEventListener", "_ref", "event", "_debug", "_notifyAllSubscribers", "data", "session", "_x4", "initialize", "args", "Date", "toISOString", "_this2", "_acquireLock", "_initialize", "_this3", "isPKCEFlow", "_isPKCEFlow", "_isImplicitGrantFlow", "_getSessionFromURL", "message", "_removeSession", "redirectType", "_saveSession", "setTimeout", "_recoverAndRefresh", "_handleVisibilityChange", "signUp", "credentials", "_this4", "_b", "_c", "res", "email", "password", "codeChallenge", "codeChallengeMethod", "codeVerifier", "redirectTo", "emailRedirectTo", "body", "gotrue_meta_security", "captcha_token", "captchaToken", "code_challenge", "code_challenge_method", "xform", "phone", "channel", "user", "signInWithPassword", "_this5", "weak_password", "weakPassword", "signInWithOAuth", "_this6", "_d", "_handleProviderSignIn", "provider", "scopes", "queryParams", "skipBrowserRedirect", "exchangeCodeForSession", "authCode", "_this7", "_exchangeCodeForSession", "_this8", "storageItem", "split", "auth_code", "code_verifier", "signInWithIdToken", "_this9", "token", "access_token", "nonce", "id_token", "signInWithOtp", "_this0", "_e", "create_user", "shouldCreateUser", "messageId", "message_id", "verifyOtp", "params", "_this1", "type", "undefined", "Error", "signInWithSSO", "_this10", "provider_id", "providerId", "domain", "redirect_to", "skip_http_redirect", "reauthenticate", "_this11", "_reauthenticate", "_this12", "_useSession", "_ref7", "result", "sessionError", "jwt", "_x5", "resend", "_this13", "endpoint", "getSession", "_this14", "_ref9", "_x6", "_this15", "last", "length", "Promise", "resolve", "push", "waitOn", "all", "splice", "_this16", "__loadSession", "_this17", "stack", "currentSession", "maybeSession", "_isValidSession", "hasExpired", "expires_at", "now", "_callRefreshToken", "refresh_token", "getUser", "_this18", "_getUser", "_this19", "_ref13", "_x7", "updateUser", "_x8", "_this20", "attributes", "_updateUser", "_x9", "_this21", "_ref15", "sessionData", "userError", "_x0", "_decodeJWT", "setSession", "_this22", "_setSession", "_this23", "timeNow", "expiresAt", "payload", "exp", "refreshedSession", "token_type", "expires_in", "refreshSession", "_this24", "_refreshSession", "_this25", "_ref18", "_x1", "_this26", "window", "location", "href", "code", "URL", "searchParams", "delete", "history", "replaceState", "state", "toString", "error_description", "error_code", "provider_token", "provider_refresh_token", "Math", "round", "expiresIn", "parseInt", "actuallyExpiresIn", "issuedAt", "hash", "_this27", "currentStorageContent", "signOut", "_this28", "scope", "_signOut", "_this29", "_ref20", "accessToken", "status", "_x10", "onAuthStateChange", "callback", "_this30", "id", "subscription", "unsubscribe", "set", "_emitInitialSession", "_this31", "_ref23", "get", "err", "_x11", "resetPasswordForEmail", "_x12", "_this32", "getUserIdentities", "_this33", "identities", "linkIdentity", "_this34", "_ref24", "_getUrl<PERSON><PERSON><PERSON><PERSON><PERSON>", "_x13", "unlinkIdentity", "identity", "_this35", "_ref25", "identity_id", "_x14", "_refreshAccessToken", "refreshToken", "_this36", "debugName", "substring", "startedAt", "_ref26", "attempt", "_x15", "_", "isValidSession", "_this37", "_this38", "expiresWithMargin", "Infinity", "_this39", "promise", "reject", "_x16", "_x17", "_this40", "broadcast", "postMessage", "errors", "promises", "Array", "from", "values", "map", "_ref27", "x", "_x18", "i", "_this41", "_this42", "_removeVisibilityChangedCallback", "removeEventListener", "_startAutoRefresh", "_this43", "_stopAutoRefresh", "ticker", "setInterval", "_autoRefreshTokenTick", "unref", "<PERSON><PERSON>", "unrefTimer", "_this44", "clearInterval", "startAutoRefresh", "_this45", "stopAutoRefresh", "_this46", "_this47", "_ref30", "expiresInTicks", "floor", "_x19", "isAcquireTimeout", "_this48", "_onVisibilityChanged", "calledFromInitialize", "_this49", "methodName", "document", "visibilityState", "_this50", "urlParams", "encodeURIComponent", "flowParams", "URLSearchParams", "query", "join", "_this51", "_ref33", "factorId", "_x20", "_this52", "_ref34", "friendly_name", "friendlyName", "factor_type", "factorType", "issuer", "totp", "qr_code", "_x21", "_this53", "_ref36", "challenge_id", "challengeId", "_x22", "_this54", "_ref38", "_x23", "_this55", "challengeData", "challengeError", "_this56", "factors", "filter", "factor", "_this57", "_ref40", "currentLevel", "nextLevel", "currentAuthenticationMethods", "aal", "verifiedFactors", "amr", "_x24"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js"], "sourcesContent": ["import GoTrueAdmin<PERSON>pi from './GoTrueAdminApi';\nimport { DEFAULT_HEADERS, EXPIRY_MARGIN, GOTRUE_URL, STORAGE_KEY } from './lib/constants';\nimport { AuthImplicitGrantRedirectError, AuthPKCEGrantCodeExchangeError, AuthInvalidCredentialsError, AuthSessionMissingError, AuthInvalidTokenResponseError, AuthUnknownError, isAuthApiError, isAuthError, isAuthRetryableFetchError, } from './lib/errors';\nimport { _request, _sessionResponse, _sessionResponsePassword, _userResponse, _ssoResponse, } from './lib/fetch';\nimport { decodeJWTPayload, Deferred, getItemAsync, isBrowser, removeItemAsync, resolveFetch, setItemAsync, uuid, retryable, sleep, generatePKCEVerifier, generatePKCEChallenge, supportsLocalStorage, parseParametersFromURL, } from './lib/helpers';\nimport { localStorageAdapter, memoryLocalStorageAdapter } from './lib/local-storage';\nimport { polyfillGlobalThis } from './lib/polyfills';\nimport { version } from './lib/version';\nimport { LockAcquireTimeoutError } from './lib/locks';\npolyfillGlobalThis(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: GOTRUE_URL,\n    storageKey: STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n};\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\nexport default class GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && isBrowser()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new GoTrueAdminApi({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = resolveFetch(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if (supportsLocalStorage()) {\n                    this.storage = localStorageAdapter;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n                }\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = memoryLocalStorageAdapter(this.memoryStorage);\n        }\n        if (isBrowser() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_a = this.broadcastChannel) === null || _a === void 0 ? void 0 : _a.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        try {\n            const isPKCEFlow = isBrowser() ? await this._isPKCEFlow() : false;\n            this._debug('#_initialize()', 'begin', 'is PKCE flow', isPKCEFlow);\n            if (isPKCEFlow || (this.detectSessionInUrl && this._isImplicitGrantFlow())) {\n                const { data, error } = await this._getSessionFromURL(isPKCEFlow);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    // hacky workaround to keep the existing session if there's an error returned from identity linking\n                    // TODO: once error codes are ready, we should match against it instead of the message\n                    if ((error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked' ||\n                        (error === null || error === void 0 ? void 0 : error.message) === 'Identity is already linked to another user') {\n                        return { error };\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { error };\n            }\n            return {\n                error: new AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            await this._removeSession();\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    const codeVerifier = generatePKCEVerifier();\n                    await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n                    codeChallenge = await generatePKCEChallenge(codeVerifier);\n                    codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n                }\n                res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponse,\n                });\n            }\n            else {\n                throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            await this._removeSession();\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _sessionResponsePassword,\n                });\n            }\n            else {\n                throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        await this._removeSession();\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        const { data, error } = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n            headers: this.headers,\n            body: {\n                auth_code: authCode,\n                code_verifier: codeVerifier,\n            },\n            xform: _sessionResponse,\n        });\n        await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        if (error) {\n            return { data: { user: null, session: null, redirectType: null }, error };\n        }\n        else if (!data || !data.session || !data.user) {\n            return {\n                data: { user: null, session: null, redirectType: null },\n                error: new AuthInvalidTokenResponseError(),\n            };\n        }\n        if (data.session) {\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('SIGNED_IN', data.session);\n        }\n        return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        await this._removeSession();\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await _request(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            await this._removeSession();\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    const codeVerifier = generatePKCEVerifier();\n                    await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n                    codeChallenge = await generatePKCEChallenge(codeVerifier);\n                    codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n                }\n                const { error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await _request(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            if (params.type !== 'email_change' && params.type !== 'phone_change') {\n                // we don't want to remove the authenticated session if the user is performing an email_change or phone_change verification\n                await this._removeSession();\n            }\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await _request(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            await this._removeSession();\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                const codeVerifier = generatePKCEVerifier();\n                await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n                codeChallenge = await generatePKCEChallenge(codeVerifier);\n                codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n            }\n            return await _request(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _ssoResponse,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new AuthSessionMissingError();\n                const { error } = await _request(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            if (credentials.type != 'email_change' && credentials.type != 'phone_change') {\n                await this._removeSession();\n            }\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await _request(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await _request(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     */\n    async getSession() {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await getItemAsync(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at <= Date.now() / 1000\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session.\n     * @param jwt Takes in an optional access token jwt. If no jwt is provided, getUser() will attempt to get the jwt from the current session.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await _request(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await _request(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                    xform: _userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    const codeVerifier = generatePKCEVerifier();\n                    await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n                    codeChallenge = await generatePKCEChallenge(codeVerifier);\n                    codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n                }\n                const { data, error: userError } = await _request(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Decodes a JWT (without performing any validation).\n     */\n    _decodeJWT(jwt) {\n        return decodeJWTPayload(jwt);\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const payload = decodeJWTPayload(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(isPKCEFlow) {\n        try {\n            if (!isBrowser())\n                throw new AuthImplicitGrantRedirectError('No browser detected.');\n            if (this.flowType === 'implicit' && !this._isImplicitGrantFlow()) {\n                throw new AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n            }\n            else if (this.flowType == 'pkce' && !isPKCEFlow) {\n                throw new AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n            }\n            const params = parseParametersFromURL(window.location.href);\n            if (isPKCEFlow) {\n                if (!params.code)\n                    throw new AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            if (params.error || params.error_description || params.error_code) {\n                throw new AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= AUTO_REFRESH_TICK_DURATION) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clok for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantFlow() {\n        const params = parseParametersFromURL(window.location.href);\n        return !!(isBrowser() && (params.access_token || params.error_description));\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCEFlow() {\n        const params = parseParametersFromURL(window.location.href);\n        const currentStorageContent = await getItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!(isAuthApiError(error) && (error.status === 404 || error.status === 401))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await removeItemAsync(this.storage, `${this.storageKey}-code-verifier`);\n                await this._notifyAllSubscribers('SIGNED_OUT', null);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = uuid();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            const codeVerifier = generatePKCEVerifier();\n            await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, `${codeVerifier}/PASSWORD_RECOVERY`);\n            codeChallenge = await generatePKCEChallenge(codeVerifier);\n            codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n        }\n        try {\n            return await _request(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await _request(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if (isBrowser() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await _request(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await retryable(async (attempt) => {\n                await sleep(attempt * 200); // 0, 200, 400, 800, ...\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await _request(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _sessionResponse,\n                });\n            }, (attempt, _, result) => result &&\n                result.error &&\n                isAuthRetryableFetchError(result.error) &&\n                // retryable only if the request can be sent before the backoff overflows the tick duration\n                Date.now() + (attempt + 1) * 200 - startedAt < AUTO_REFRESH_TICK_DURATION);\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if (isAuthError(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if (isBrowser() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = await getItemAsync(this.storage, this.storageKey);\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) < timeNow + EXPIRY_MARGIN;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${EXPIRY_MARGIN}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!isAuthRetryableFetchError(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if (isAuthError(error)) {\n                const result = { session: null, error };\n                if (!isAuthRetryableFetchError(error)) {\n                    await this._removeSession();\n                    await this._notifyAllSubscribers('SIGNED_OUT', null);\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        await setItemAsync(this.storage, this.storageKey, session);\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await removeItemAsync(this.storage, this.storageKey);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && isBrowser() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), AUTO_REFRESH_TICK_DURATION);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-ignore\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-ignore\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / AUTO_REFRESH_TICK_DURATION);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${AUTO_REFRESH_TICK_DURATION}ms, refresh threshold is ${AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!isBrowser() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const codeVerifier = generatePKCEVerifier();\n            await setItemAsync(this.storage, `${this.storageKey}-code-verifier`, codeVerifier);\n            const codeChallenge = await generatePKCEChallenge(codeVerifier);\n            const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n            this._debug('PKCE', 'code verifier', `${codeVerifier.substring(0, 5)}...`, 'code challenge', codeChallenge, 'method', codeChallengeMethod);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await _request(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#enroll}\n     */\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors`, {\n                    body: {\n                        friendly_name: params.friendlyName,\n                        factor_type: params.factorType,\n                        issuer: params.issuer,\n                    },\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if (isAuthError(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if (isAuthError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await _request(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if (isAuthError(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const payload = this._decodeJWT(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n"], "mappings": ";AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,SAASC,eAAe,EAAEC,aAAa,EAAEC,UAAU,EAAEC,WAAW,QAAQ,iBAAiB;AACzF,SAASC,8BAA8B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,uBAAuB,EAAEC,6BAA6B,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,yBAAyB,QAAS,cAAc;AAC7P,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,aAAa,EAAEC,YAAY,QAAS,aAAa;AAChH,SAASC,gBAAgB,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,YAAY,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAS,eAAe;AACpP,SAASC,mBAAmB,EAAEC,yBAAyB,QAAQ,qBAAqB;AACpF,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,uBAAuB,QAAQ,aAAa;AACrDF,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACtB,MAAMG,eAAe,GAAG;EACpBC,GAAG,EAAEpC,UAAU;EACfqC,UAAU,EAAEpC,WAAW;EACvBqC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAE3C,eAAe;EACxB4C,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,0BAA0B,GAAG,EAAE,GAAG,IAAI;AAC5C;AACA;AACA,MAAMC,2BAA2B,GAAG,CAAC;AAAC,SACvBC,QAAQA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,SAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,UAAA;EAAAA,SAAA,GAAAG,iBAAA,CAAvB,WAAwBC,IAAI,EAAEC,cAAc,EAAEC,EAAE,EAAE;IAC9C,aAAaA,EAAE,CAAC,CAAC;EACrB,CAAC;EAAA,OAAAN,SAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,eAAe,MAAMK,YAAY,CAAC;EAC9B;AACJ;AACA;EACIC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB,IAAIC,EAAE;IACN,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC5B,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAAC6B,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;AACR;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,MAAM,GAAGC,OAAO,CAACC,GAAG;IACzB,IAAI,CAACC,UAAU,GAAGlB,YAAY,CAACmB,cAAc;IAC7CnB,YAAY,CAACmB,cAAc,IAAI,CAAC;IAChC,IAAI,IAAI,CAACD,UAAU,GAAG,CAAC,IAAIxD,SAAS,CAAC,CAAC,EAAE;MACpCsD,OAAO,CAACI,IAAI,CAAC,8MAA8M,CAAC;IAChO;IACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7C,eAAe,CAAC,EAAEwB,OAAO,CAAC;IAC3E,IAAI,CAACsB,gBAAgB,GAAG,CAAC,CAACH,QAAQ,CAACnC,KAAK;IACxC,IAAI,OAAOmC,QAAQ,CAACnC,KAAK,KAAK,UAAU,EAAE;MACtC,IAAI,CAAC6B,MAAM,GAAGM,QAAQ,CAACnC,KAAK;IAChC;IACA,IAAI,CAACJ,cAAc,GAAGuC,QAAQ,CAACvC,cAAc;IAC7C,IAAI,CAACF,UAAU,GAAGyC,QAAQ,CAACzC,UAAU;IACrC,IAAI,CAACC,gBAAgB,GAAGwC,QAAQ,CAACxC,gBAAgB;IACjD,IAAI,CAAC4C,KAAK,GAAG,IAAIrF,cAAc,CAAC;MAC5BuC,GAAG,EAAE0C,QAAQ,CAAC1C,GAAG;MACjBK,OAAO,EAAEqC,QAAQ,CAACrC,OAAO;MACzB0C,KAAK,EAAEL,QAAQ,CAACK;IACpB,CAAC,CAAC;IACF,IAAI,CAAC/C,GAAG,GAAG0C,QAAQ,CAAC1C,GAAG;IACvB,IAAI,CAACK,OAAO,GAAGqC,QAAQ,CAACrC,OAAO;IAC/B,IAAI,CAAC0C,KAAK,GAAG9D,YAAY,CAACyD,QAAQ,CAACK,KAAK,CAAC;IACzC,IAAI,CAACC,IAAI,GAAGN,QAAQ,CAACM,IAAI,IAAItC,QAAQ;IACrC,IAAI,CAACN,kBAAkB,GAAGsC,QAAQ,CAACtC,kBAAkB;IACrD,IAAI,CAACE,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ;IACjC,IAAI,CAAC2C,GAAG,GAAG;MACPC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;MAC/BC,MAAM,EAAE,IAAI,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC;MAC/BG,QAAQ,EAAE,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,IAAI,CAAC;MACnCK,SAAS,EAAE,IAAI,CAACC,UAAU,CAACN,IAAI,CAAC,IAAI,CAAC;MACrCO,WAAW,EAAE,IAAI,CAACC,YAAY,CAACR,IAAI,CAAC,IAAI,CAAC;MACzCS,kBAAkB,EAAE,IAAI,CAACC,mBAAmB,CAACV,IAAI,CAAC,IAAI,CAAC;MACvDW,8BAA8B,EAAE,IAAI,CAACC,+BAA+B,CAACZ,IAAI,CAAC,IAAI;IAClF,CAAC;IACD,IAAI,IAAI,CAACjD,cAAc,EAAE;MACrB,IAAIuC,QAAQ,CAACuB,OAAO,EAAE;QAClB,IAAI,CAACA,OAAO,GAAGvB,QAAQ,CAACuB,OAAO;MACnC,CAAC,MACI;QACD,IAAIzE,oBAAoB,CAAC,CAAC,EAAE;UACxB,IAAI,CAACyE,OAAO,GAAGvE,mBAAmB;QACtC,CAAC,MACI;UACD,IAAI,CAACgC,aAAa,GAAG,CAAC,CAAC;UACvB,IAAI,CAACuC,OAAO,GAAGtE,yBAAyB,CAAC,IAAI,CAAC+B,aAAa,CAAC;QAChE;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACA,aAAa,GAAG,CAAC,CAAC;MACvB,IAAI,CAACuC,OAAO,GAAGtE,yBAAyB,CAAC,IAAI,CAAC+B,aAAa,CAAC;IAChE;IACA,IAAI3C,SAAS,CAAC,CAAC,IAAImF,UAAU,CAACC,gBAAgB,IAAI,IAAI,CAAChE,cAAc,IAAI,IAAI,CAACF,UAAU,EAAE;MACtF,IAAI;QACA,IAAI,CAACkC,gBAAgB,GAAG,IAAI+B,UAAU,CAACC,gBAAgB,CAAC,IAAI,CAAClE,UAAU,CAAC;MAC5E,CAAC,CACD,OAAOmE,CAAC,EAAE;QACN/B,OAAO,CAACgC,KAAK,CAAC,wFAAwF,EAAED,CAAC,CAAC;MAC9G;MACA,CAAC3C,EAAE,GAAG,IAAI,CAACU,gBAAgB,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C,gBAAgB,CAAC,SAAS;QAAA,IAAAC,IAAA,GAAAtD,iBAAA,CAAE,WAAOuD,KAAK,EAAK;UAC9GhD,KAAI,CAACiD,MAAM,CAAC,0DAA0D,EAAED,KAAK,CAAC;UAC9E,MAAMhD,KAAI,CAACkD,qBAAqB,CAACF,KAAK,CAACG,IAAI,CAACH,KAAK,EAAEA,KAAK,CAACG,IAAI,CAACC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QACnF,CAAC;QAAA,iBAAAC,GAAA;UAAA,OAAAN,IAAA,CAAAxD,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IACN;IACA,IAAI,CAAC8D,UAAU,CAAC,CAAC;EACrB;EACAL,MAAMA,CAAC,GAAGM,IAAI,EAAE;IACZ,IAAI,IAAI,CAAClC,gBAAgB,EAAE;MACvB,IAAI,CAACT,MAAM,CAAC,gBAAgB,IAAI,CAACG,UAAU,KAAK1C,OAAO,KAAK,IAAImF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE,EAAE,GAAGF,IAAI,CAAC;IACpG;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACUD,UAAUA,CAAA,EAAG;IAAA,IAAAI,MAAA;IAAA,OAAAjE,iBAAA;MACf,IAAIiE,MAAI,CAAClD,iBAAiB,EAAE;QACxB,aAAakD,MAAI,CAAClD,iBAAiB;MACvC;MACAkD,MAAI,CAAClD,iBAAiB,GAAGf,iBAAA,CAAC,aAAY;QAClC,aAAaiE,MAAI,CAACC,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;UAC3C,aAAaiE,MAAI,CAACE,WAAW,CAAC,CAAC;QACnC,CAAC,EAAC;MACN,CAAC,EAAE,CAAC;MACJ,aAAaF,MAAI,CAAClD,iBAAiB;IAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACUoD,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApE,iBAAA;MAChB,IAAI;QACA,MAAMqE,UAAU,GAAGvG,SAAS,CAAC,CAAC,SAASsG,MAAI,CAACE,WAAW,CAAC,CAAC,GAAG,KAAK;QACjEF,MAAI,CAACZ,MAAM,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAEa,UAAU,CAAC;QAClE,IAAIA,UAAU,IAAKD,MAAI,CAACjF,kBAAkB,IAAIiF,MAAI,CAACG,oBAAoB,CAAC,CAAE,EAAE;UACxE,MAAM;YAAEb,IAAI;YAAEN;UAAM,CAAC,SAASgB,MAAI,CAACI,kBAAkB,CAACH,UAAU,CAAC;UACjE,IAAIjB,KAAK,EAAE;YACPgB,MAAI,CAACZ,MAAM,CAAC,gBAAgB,EAAE,kCAAkC,EAAEJ,KAAK,CAAC;YACxE;YACA;YACA,IAAI,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACqB,OAAO,MAAM,4BAA4B,IAC9F,CAACrB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACqB,OAAO,MAAM,4CAA4C,EAAE;cAChH,OAAO;gBAAErB;cAAM,CAAC;YACpB;YACA;YACA;YACA,MAAMgB,MAAI,CAACM,cAAc,CAAC,CAAC;YAC3B,OAAO;cAAEtB;YAAM,CAAC;UACpB;UACA,MAAM;YAAEO,OAAO;YAAEgB;UAAa,CAAC,GAAGjB,IAAI;UACtCU,MAAI,CAACZ,MAAM,CAAC,gBAAgB,EAAE,yBAAyB,EAAEG,OAAO,EAAE,eAAe,EAAEgB,YAAY,CAAC;UAChG,MAAMP,MAAI,CAACQ,YAAY,CAACjB,OAAO,CAAC;UAChCkB,UAAU,cAAA7E,iBAAA,CAAC,aAAY;YACnB,IAAI2E,YAAY,KAAK,UAAU,EAAE;cAC7B,MAAMP,MAAI,CAACX,qBAAqB,CAAC,mBAAmB,EAAEE,OAAO,CAAC;YAClE,CAAC,MACI;cACD,MAAMS,MAAI,CAACX,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;YAC1D;UACJ,CAAC,GAAE,CAAC,CAAC;UACL,OAAO;YAAEP,KAAK,EAAE;UAAK,CAAC;QAC1B;QACA;QACA,MAAMgB,MAAI,CAACU,kBAAkB,CAAC,CAAC;QAC/B,OAAO;UAAE1B,KAAK,EAAE;QAAK,CAAC;MAC1B,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEA;UAAM,CAAC;QACpB;QACA,OAAO;UACHA,KAAK,EAAE,IAAIlG,gBAAgB,CAAC,wCAAwC,EAAEkG,KAAK;QAC/E,CAAC;MACL,CAAC,SACO;QACJ,MAAMgB,MAAI,CAACW,uBAAuB,CAAC,CAAC;QACpCX,MAAI,CAACZ,MAAM,CAAC,gBAAgB,EAAE,KAAK,CAAC;MACxC;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUwB,MAAMA,CAACC,WAAW,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAlF,iBAAA;MACtB,IAAIQ,EAAE,EAAE2E,EAAE,EAAEC,EAAE;MACd,IAAI;QACA,MAAMF,MAAI,CAACR,cAAc,CAAC,CAAC;QAC3B,IAAIW,GAAG;QACP,IAAI,OAAO,IAAIJ,WAAW,EAAE;UACxB,MAAM;YAAEK,KAAK;YAAEC,QAAQ;YAAEjF;UAAQ,CAAC,GAAG2E,WAAW;UAChD,IAAIO,aAAa,GAAG,IAAI;UACxB,IAAIC,mBAAmB,GAAG,IAAI;UAC9B,IAAIP,MAAI,CAAC7F,QAAQ,KAAK,MAAM,EAAE;YAC1B,MAAMqG,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;YAC3C,MAAMJ,YAAY,CAACiH,MAAI,CAAClC,OAAO,EAAE,GAAGkC,MAAI,CAAClG,UAAU,gBAAgB,EAAE0G,YAAY,CAAC;YAClFF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;YACzDD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;UAC3E;UACAH,GAAG,SAAS/H,QAAQ,CAAC4H,MAAI,CAACpD,KAAK,EAAE,MAAM,EAAE,GAAGoD,MAAI,CAACnG,GAAG,SAAS,EAAE;YAC3DK,OAAO,EAAE8F,MAAI,CAAC9F,OAAO;YACrBuG,UAAU,EAAErF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsF,eAAe;YACrFC,IAAI,EAAE;cACFP,KAAK;cACLC,QAAQ;cACR7B,IAAI,EAAE,CAAClD,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoD,IAAI,MAAM,IAAI,IAAIlD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;cAC/GsF,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa,CAAC;cAC/GC,cAAc,EAAET,aAAa;cAC7BU,qBAAqB,EAAET;YAC3B,CAAC;YACDU,KAAK,EAAE5I;UACX,CAAC,CAAC;QACN,CAAC,MACI,IAAI,OAAO,IAAI0H,WAAW,EAAE;UAC7B,MAAM;YAAEmB,KAAK;YAAEb,QAAQ;YAAEjF;UAAQ,CAAC,GAAG2E,WAAW;UAChDI,GAAG,SAAS/H,QAAQ,CAAC4H,MAAI,CAACpD,KAAK,EAAE,MAAM,EAAE,GAAGoD,MAAI,CAACnG,GAAG,SAAS,EAAE;YAC3DK,OAAO,EAAE8F,MAAI,CAAC9F,OAAO;YACrByG,IAAI,EAAE;cACFO,KAAK;cACLb,QAAQ;cACR7B,IAAI,EAAE,CAACyB,EAAE,GAAG7E,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoD,IAAI,MAAM,IAAI,IAAIyB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;cAC/GkB,OAAO,EAAE,CAACjB,EAAE,GAAG9E,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC+F,OAAO,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;cACxHU,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa;YAClH,CAAC;YACDG,KAAK,EAAE5I;UACX,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAM,IAAIR,2BAA2B,CAAC,iEAAiE,CAAC;QAC5G;QACA,MAAM;UAAE2G,IAAI;UAAEN;QAAM,CAAC,GAAGiC,GAAG;QAC3B,IAAIjC,KAAK,IAAI,CAACM,IAAI,EAAE;UAChB,OAAO;YAAEA,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP,KAAK,EAAEA;UAAM,CAAC;QAChE;QACA,MAAMO,OAAO,GAAGD,IAAI,CAACC,OAAO;QAC5B,MAAM2C,IAAI,GAAG5C,IAAI,CAAC4C,IAAI;QACtB,IAAI5C,IAAI,CAACC,OAAO,EAAE;UACd,MAAMuB,MAAI,CAACN,YAAY,CAAClB,IAAI,CAACC,OAAO,CAAC;UACrC,MAAMuB,MAAI,CAACzB,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;QAC1D;QACA,OAAO;UAAED,IAAI,EAAE;YAAE4C,IAAI;YAAE3C;UAAQ,CAAC;UAAEP,KAAK,EAAE;QAAK,CAAC;MACnD,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUmD,kBAAkBA,CAACtB,WAAW,EAAE;IAAA,IAAAuB,MAAA;IAAA,OAAAxG,iBAAA;MAClC,IAAI;QACA,MAAMwG,MAAI,CAAC9B,cAAc,CAAC,CAAC;QAC3B,IAAIW,GAAG;QACP,IAAI,OAAO,IAAIJ,WAAW,EAAE;UACxB,MAAM;YAAEK,KAAK;YAAEC,QAAQ;YAAEjF;UAAQ,CAAC,GAAG2E,WAAW;UAChDI,GAAG,SAAS/H,QAAQ,CAACkJ,MAAI,CAAC1E,KAAK,EAAE,MAAM,EAAE,GAAG0E,MAAI,CAACzH,GAAG,4BAA4B,EAAE;YAC9EK,OAAO,EAAEoH,MAAI,CAACpH,OAAO;YACrByG,IAAI,EAAE;cACFP,KAAK;cACLC,QAAQ;cACRO,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa;YAClH,CAAC;YACDG,KAAK,EAAE3I;UACX,CAAC,CAAC;QACN,CAAC,MACI,IAAI,OAAO,IAAIyH,WAAW,EAAE;UAC7B,MAAM;YAAEmB,KAAK;YAAEb,QAAQ;YAAEjF;UAAQ,CAAC,GAAG2E,WAAW;UAChDI,GAAG,SAAS/H,QAAQ,CAACkJ,MAAI,CAAC1E,KAAK,EAAE,MAAM,EAAE,GAAG0E,MAAI,CAACzH,GAAG,4BAA4B,EAAE;YAC9EK,OAAO,EAAEoH,MAAI,CAACpH,OAAO;YACrByG,IAAI,EAAE;cACFO,KAAK;cACLb,QAAQ;cACRO,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa;YAClH,CAAC;YACDG,KAAK,EAAE3I;UACX,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAM,IAAIT,2BAA2B,CAAC,iEAAiE,CAAC;QAC5G;QACA,MAAM;UAAE2G,IAAI;UAAEN;QAAM,CAAC,GAAGiC,GAAG;QAC3B,IAAIjC,KAAK,EAAE;UACP,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD,CAAC,MACI,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAAC4C,IAAI,EAAE;UAC3C,OAAO;YAAE5C,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP,KAAK,EAAE,IAAInG,6BAA6B,CAAC;UAAE,CAAC;QAC9F;QACA,IAAIyG,IAAI,CAACC,OAAO,EAAE;UACd,MAAM6C,MAAI,CAAC5B,YAAY,CAAClB,IAAI,CAACC,OAAO,CAAC;UACrC,MAAM6C,MAAI,CAAC/C,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;QAC/D;QACA,OAAO;UACHD,IAAI,EAAEhC,MAAM,CAACC,MAAM,CAAC;YAAE2E,IAAI,EAAE5C,IAAI,CAAC4C,IAAI;YAAE3C,OAAO,EAAED,IAAI,CAACC;UAAQ,CAAC,EAAGD,IAAI,CAAC+C,aAAa,GAAG;YAAEC,YAAY,EAAEhD,IAAI,CAAC+C;UAAc,CAAC,GAAG,IAAK,CAAC;UACnIrD;QACJ,CAAC;MACL,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;EACUuD,eAAeA,CAAC1B,WAAW,EAAE;IAAA,IAAA2B,MAAA;IAAA,OAAA5G,iBAAA;MAC/B,IAAIQ,EAAE,EAAE2E,EAAE,EAAEC,EAAE,EAAEyB,EAAE;MAClB,MAAMD,MAAI,CAAClC,cAAc,CAAC,CAAC;MAC3B,aAAakC,MAAI,CAACE,qBAAqB,CAAC7B,WAAW,CAAC8B,QAAQ,EAAE;QAC1DpB,UAAU,EAAE,CAACnF,EAAE,GAAGyE,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,UAAU;QACzFqB,MAAM,EAAE,CAAC7B,EAAE,GAAGF,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAI6E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,MAAM;QACjFC,WAAW,EAAE,CAAC7B,EAAE,GAAGH,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,WAAW;QAC3FC,mBAAmB,EAAE,CAACL,EAAE,GAAG5B,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAIuG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK;MAC5F,CAAC,CAAC;IAAC;EACP;EACA;AACJ;AACA;EACUC,sBAAsBA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAArH,iBAAA;MACnC,MAAMqH,MAAI,CAACtG,iBAAiB;MAC5B,OAAOsG,MAAI,CAACnD,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,OAAOqH,MAAI,CAACC,uBAAuB,CAACF,QAAQ,CAAC;MACjD,CAAC,EAAC;IAAC;EACP;EACME,uBAAuBA,CAACF,QAAQ,EAAE;IAAA,IAAAG,MAAA;IAAA,OAAAvH,iBAAA;MACpC,MAAMwH,WAAW,SAAS3J,YAAY,CAAC0J,MAAI,CAACvE,OAAO,EAAE,GAAGuE,MAAI,CAACvI,UAAU,gBAAgB,CAAC;MACxF,MAAM,CAAC0G,YAAY,EAAEf,YAAY,CAAC,GAAG,CAAC6C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,EAAE,EAAEC,KAAK,CAAC,GAAG,CAAC;MACnH,MAAM;QAAE/D,IAAI;QAAEN;MAAM,CAAC,SAAS9F,QAAQ,CAACiK,MAAI,CAACzF,KAAK,EAAE,MAAM,EAAE,GAAGyF,MAAI,CAACxI,GAAG,wBAAwB,EAAE;QAC5FK,OAAO,EAAEmI,MAAI,CAACnI,OAAO;QACrByG,IAAI,EAAE;UACF6B,SAAS,EAAEN,QAAQ;UACnBO,aAAa,EAAEjC;QACnB,CAAC;QACDS,KAAK,EAAE5I;MACX,CAAC,CAAC;MACF,MAAMQ,eAAe,CAACwJ,MAAI,CAACvE,OAAO,EAAE,GAAGuE,MAAI,CAACvI,UAAU,gBAAgB,CAAC;MACvE,IAAIoE,KAAK,EAAE;QACP,OAAO;UAAEM,IAAI,EAAE;YAAE4C,IAAI,EAAE,IAAI;YAAE3C,OAAO,EAAE,IAAI;YAAEgB,YAAY,EAAE;UAAK,CAAC;UAAEvB;QAAM,CAAC;MAC7E,CAAC,MACI,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAAC4C,IAAI,EAAE;QAC3C,OAAO;UACH5C,IAAI,EAAE;YAAE4C,IAAI,EAAE,IAAI;YAAE3C,OAAO,EAAE,IAAI;YAAEgB,YAAY,EAAE;UAAK,CAAC;UACvDvB,KAAK,EAAE,IAAInG,6BAA6B,CAAC;QAC7C,CAAC;MACL;MACA,IAAIyG,IAAI,CAACC,OAAO,EAAE;QACd,MAAM4D,MAAI,CAAC3C,YAAY,CAAClB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAM4D,MAAI,CAAC9D,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;MAC/D;MACA,OAAO;QAAED,IAAI,EAAEhC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+B,IAAI,CAAC,EAAE;UAAEiB,YAAY,EAAEA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG;QAAK,CAAC,CAAC;QAAEvB;MAAM,CAAC;IAAC;EAC7J;EACA;AACJ;AACA;AACA;EACUwE,iBAAiBA,CAAC3C,WAAW,EAAE;IAAA,IAAA4C,MAAA;IAAA,OAAA7H,iBAAA;MACjC,MAAM6H,MAAI,CAACnD,cAAc,CAAC,CAAC;MAC3B,IAAI;QACA,MAAM;UAAEpE,OAAO;UAAEyG,QAAQ;UAAEe,KAAK;UAAEC,YAAY;UAAEC;QAAM,CAAC,GAAG/C,WAAW;QACrE,MAAMI,GAAG,SAAS/H,QAAQ,CAACuK,MAAI,CAAC/F,KAAK,EAAE,MAAM,EAAE,GAAG+F,MAAI,CAAC9I,GAAG,4BAA4B,EAAE;UACpFK,OAAO,EAAEyI,MAAI,CAACzI,OAAO;UACrByG,IAAI,EAAE;YACFkB,QAAQ;YACRkB,QAAQ,EAAEH,KAAK;YACfC,YAAY;YACZC,KAAK;YACLlC,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;YAAa;UAClH,CAAC;UACDG,KAAK,EAAE5I;QACX,CAAC,CAAC;QACF,MAAM;UAAEmG,IAAI;UAAEN;QAAM,CAAC,GAAGiC,GAAG;QAC3B,IAAIjC,KAAK,EAAE;UACP,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD,CAAC,MACI,IAAI,CAACM,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAAC4C,IAAI,EAAE;UAC3C,OAAO;YACH5C,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YACnCP,KAAK,EAAE,IAAInG,6BAA6B,CAAC;UAC7C,CAAC;QACL;QACA,IAAIyG,IAAI,CAACC,OAAO,EAAE;UACd,MAAMkE,MAAI,CAACjD,YAAY,CAAClB,IAAI,CAACC,OAAO,CAAC;UACrC,MAAMkE,MAAI,CAACpE,qBAAqB,CAAC,WAAW,EAAEC,IAAI,CAACC,OAAO,CAAC;QAC/D;QACA,OAAO;UAAED,IAAI;UAAEN;QAAM,CAAC;MAC1B,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU8E,aAAaA,CAACjD,WAAW,EAAE;IAAA,IAAAkD,MAAA;IAAA,OAAAnI,iBAAA;MAC7B,IAAIQ,EAAE,EAAE2E,EAAE,EAAEC,EAAE,EAAEyB,EAAE,EAAEuB,EAAE;MACtB,IAAI;QACA,MAAMD,MAAI,CAACzD,cAAc,CAAC,CAAC;QAC3B,IAAI,OAAO,IAAIO,WAAW,EAAE;UACxB,MAAM;YAAEK,KAAK;YAAEhF;UAAQ,CAAC,GAAG2E,WAAW;UACtC,IAAIO,aAAa,GAAG,IAAI;UACxB,IAAIC,mBAAmB,GAAG,IAAI;UAC9B,IAAI0C,MAAI,CAAC9I,QAAQ,KAAK,MAAM,EAAE;YAC1B,MAAMqG,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;YAC3C,MAAMJ,YAAY,CAACkK,MAAI,CAACnF,OAAO,EAAE,GAAGmF,MAAI,CAACnJ,UAAU,gBAAgB,EAAE0G,YAAY,CAAC;YAClFF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;YACzDD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;UAC3E;UACA,MAAM;YAAEpC;UAAM,CAAC,SAAS9F,QAAQ,CAAC6K,MAAI,CAACrG,KAAK,EAAE,MAAM,EAAE,GAAGqG,MAAI,CAACpJ,GAAG,MAAM,EAAE;YACpEK,OAAO,EAAE+I,MAAI,CAAC/I,OAAO;YACrByG,IAAI,EAAE;cACFP,KAAK;cACL5B,IAAI,EAAE,CAAClD,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoD,IAAI,MAAM,IAAI,IAAIlD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;cAC/G6H,WAAW,EAAE,CAAClD,EAAE,GAAG7E,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgI,gBAAgB,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;cACpIW,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa,CAAC;cAC/GC,cAAc,EAAET,aAAa;cAC7BU,qBAAqB,EAAET;YAC3B,CAAC;YACDE,UAAU,EAAErF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsF;UAC1E,CAAC,CAAC;UACF,OAAO;YAAElC,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,IAAI,OAAO,IAAI6B,WAAW,EAAE;UACxB,MAAM;YAAEmB,KAAK;YAAE9F;UAAQ,CAAC,GAAG2E,WAAW;UACtC,MAAM;YAAEvB,IAAI;YAAEN;UAAM,CAAC,SAAS9F,QAAQ,CAAC6K,MAAI,CAACrG,KAAK,EAAE,MAAM,EAAE,GAAGqG,MAAI,CAACpJ,GAAG,MAAM,EAAE;YAC1EK,OAAO,EAAE+I,MAAI,CAAC/I,OAAO;YACrByG,IAAI,EAAE;cACFO,KAAK;cACL1C,IAAI,EAAE,CAAC0B,EAAE,GAAG9E,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoD,IAAI,MAAM,IAAI,IAAI0B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;cAC/GiD,WAAW,EAAE,CAACxB,EAAE,GAAGvG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgI,gBAAgB,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;cACpIf,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa,CAAC;cAC/GK,OAAO,EAAE,CAAC+B,EAAE,GAAG9H,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC+F,OAAO,MAAM,IAAI,IAAI+B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YACvH;UACJ,CAAC,CAAC;UACF,OAAO;YAAE1E,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE,IAAI;cAAE4E,SAAS,EAAE7E,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC8E;YAAW,CAAC;YAAEpF;UAAM,CAAC;QACjI;QACA,MAAM,IAAIrG,2BAA2B,CAAC,mDAAmD,CAAC;MAC9F,CAAC,CACD,OAAOqG,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACUqF,SAASA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3I,iBAAA;MACpB,IAAIQ,EAAE,EAAE2E,EAAE;MACV,IAAI;QACA,IAAIuD,MAAM,CAACE,IAAI,KAAK,cAAc,IAAIF,MAAM,CAACE,IAAI,KAAK,cAAc,EAAE;UAClE;UACA,MAAMD,MAAI,CAACjE,cAAc,CAAC,CAAC;QAC/B;QACA,IAAIiB,UAAU,GAAGkD,SAAS;QAC1B,IAAI7C,YAAY,GAAG6C,SAAS;QAC5B,IAAI,SAAS,IAAIH,MAAM,EAAE;UACrB/C,UAAU,GAAG,CAACnF,EAAE,GAAGkI,MAAM,CAACpI,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,UAAU;UACrFK,YAAY,GAAG,CAACb,EAAE,GAAGuD,MAAM,CAACpI,OAAO,MAAM,IAAI,IAAI6E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,YAAY;QAC7F;QACA,MAAM;UAAEtC,IAAI;UAAEN;QAAM,CAAC,SAAS9F,QAAQ,CAACqL,MAAI,CAAC7G,KAAK,EAAE,MAAM,EAAE,GAAG6G,MAAI,CAAC5J,GAAG,SAAS,EAAE;UAC7EK,OAAO,EAAEuJ,MAAI,CAACvJ,OAAO;UACrByG,IAAI,EAAEnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE+G,MAAM,CAAC,EAAE;YAAE5C,oBAAoB,EAAE;cAAEC,aAAa,EAAEC;YAAa;UAAE,CAAC,CAAC;UACzGL,UAAU;UACVQ,KAAK,EAAE5I;QACX,CAAC,CAAC;QACF,IAAI6F,KAAK,EAAE;UACP,MAAMA,KAAK;QACf;QACA,IAAI,CAACM,IAAI,EAAE;UACP,MAAM,IAAIoF,KAAK,CAAC,0CAA0C,CAAC;QAC/D;QACA,MAAMnF,OAAO,GAAGD,IAAI,CAACC,OAAO;QAC5B,MAAM2C,IAAI,GAAG5C,IAAI,CAAC4C,IAAI;QACtB,IAAI3C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoE,YAAY,EAAE;UACxE,MAAMY,MAAI,CAAC/D,YAAY,CAACjB,OAAO,CAAC;UAChC,MAAMgF,MAAI,CAAClF,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;QAC1D;QACA,OAAO;UAAED,IAAI,EAAE;YAAE4C,IAAI;YAAE3C;UAAQ,CAAC;UAAEP,KAAK,EAAE;QAAK,CAAC;MACnD,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU2F,aAAaA,CAACL,MAAM,EAAE;IAAA,IAAAM,OAAA;IAAA,OAAAhJ,iBAAA;MACxB,IAAIQ,EAAE,EAAE2E,EAAE,EAAEC,EAAE;MACd,IAAI;QACA,MAAM4D,OAAI,CAACtE,cAAc,CAAC,CAAC;QAC3B,IAAIc,aAAa,GAAG,IAAI;QACxB,IAAIC,mBAAmB,GAAG,IAAI;QAC9B,IAAIuD,OAAI,CAAC3J,QAAQ,KAAK,MAAM,EAAE;UAC1B,MAAMqG,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;UAC3C,MAAMJ,YAAY,CAAC+K,OAAI,CAAChG,OAAO,EAAE,GAAGgG,OAAI,CAAChK,UAAU,gBAAgB,EAAE0G,YAAY,CAAC;UAClFF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;UACzDD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;QAC3E;QACA,aAAalI,QAAQ,CAAC0L,OAAI,CAAClH,KAAK,EAAE,MAAM,EAAE,GAAGkH,OAAI,CAACjK,GAAG,MAAM,EAAE;UACzD8G,IAAI,EAAEnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAG,YAAY,IAAI+G,MAAM,GAAG;YAAEO,WAAW,EAAEP,MAAM,CAACQ;UAAW,CAAC,GAAG,IAAK,CAAC,EAAG,QAAQ,IAAIR,MAAM,GAAG;YAAES,MAAM,EAAET,MAAM,CAACS;UAAO,CAAC,GAAG,IAAK,CAAC,EAAE;YAAEC,WAAW,EAAE,CAACjE,EAAE,GAAG,CAAC3E,EAAE,GAAGkI,MAAM,CAACpI,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,UAAU,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0D;UAAU,CAAC,CAAC,EAAG,CAAC,CAACzD,EAAE,GAAGsD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACpI,OAAO,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,YAAY,IACrd;YAAEF,oBAAoB,EAAE;cAAEC,aAAa,EAAE2C,MAAM,CAACpI,OAAO,CAAC0F;YAAa;UAAE,CAAC,GACxE,IAAK,CAAC,EAAE;YAAEqD,kBAAkB,EAAE,IAAI;YAAEpD,cAAc,EAAET,aAAa;YAAEU,qBAAqB,EAAET;UAAoB,CAAC,CAAC;UACtHrG,OAAO,EAAE4J,OAAI,CAAC5J,OAAO;UACrB+G,KAAK,EAAEzI;QACX,CAAC,CAAC;MACN,CAAC,CACD,OAAO0F,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;EACUkG,cAAcA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAvJ,iBAAA;MACnB,MAAMuJ,OAAI,CAACxI,iBAAiB;MAC5B,aAAawI,OAAI,CAACrF,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QAC3C,aAAauJ,OAAI,CAACC,eAAe,CAAC,CAAC;MACvC,CAAC,EAAC;IAAC;EACP;EACMA,eAAeA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAzJ,iBAAA;MACpB,IAAI;QACA,aAAayJ,OAAI,CAACC,WAAW;UAAA,IAAAC,KAAA,GAAA3J,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,MAAM;cAAElG,IAAI,EAAE;gBAAEC;cAAQ,CAAC;cAAEP,KAAK,EAAEyG;YAAc,CAAC,GAAGD,MAAM;YAC1D,IAAIC,YAAY,EACZ,MAAMA,YAAY;YACtB,IAAI,CAAClG,OAAO,EACR,MAAM,IAAI3G,uBAAuB,CAAC,CAAC;YACvC,MAAM;cAAEoG;YAAM,CAAC,SAAS9F,QAAQ,CAACmM,OAAI,CAAC3H,KAAK,EAAE,KAAK,EAAE,GAAG2H,OAAI,CAAC1K,GAAG,iBAAiB,EAAE;cAC9EK,OAAO,EAAEqK,OAAI,CAACrK,OAAO;cACrB0K,GAAG,EAAEnG,OAAO,CAACoE;YACjB,CAAC,CAAC;YACF,OAAO;cAAErE,IAAI,EAAE;gBAAE4C,IAAI,EAAE,IAAI;gBAAE3C,OAAO,EAAE;cAAK,CAAC;cAAEP;YAAM,CAAC;UACzD,CAAC;UAAA,iBAAA2G,GAAA;YAAA,OAAAJ,KAAA,CAAA7J,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACU4G,MAAMA,CAAC/E,WAAW,EAAE;IAAA,IAAAgF,OAAA;IAAA,OAAAjK,iBAAA;MACtB,IAAI;QACA,IAAIiF,WAAW,CAAC2D,IAAI,IAAI,cAAc,IAAI3D,WAAW,CAAC2D,IAAI,IAAI,cAAc,EAAE;UAC1E,MAAMqB,OAAI,CAACvF,cAAc,CAAC,CAAC;QAC/B;QACA,MAAMwF,QAAQ,GAAG,GAAGD,OAAI,CAAClL,GAAG,SAAS;QACrC,IAAI,OAAO,IAAIkG,WAAW,EAAE;UACxB,MAAM;YAAEK,KAAK;YAAEsD,IAAI;YAAEtI;UAAQ,CAAC,GAAG2E,WAAW;UAC5C,MAAM;YAAE7B;UAAM,CAAC,SAAS9F,QAAQ,CAAC2M,OAAI,CAACnI,KAAK,EAAE,MAAM,EAAEoI,QAAQ,EAAE;YAC3D9K,OAAO,EAAE6K,OAAI,CAAC7K,OAAO;YACrByG,IAAI,EAAE;cACFP,KAAK;cACLsD,IAAI;cACJ9C,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa;YAClH,CAAC;YACDL,UAAU,EAAErF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsF;UAC1E,CAAC,CAAC;UACF,OAAO;YAAElC,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD,CAAC,MACI,IAAI,OAAO,IAAI6B,WAAW,EAAE;UAC7B,MAAM;YAAEmB,KAAK;YAAEwC,IAAI;YAAEtI;UAAQ,CAAC,GAAG2E,WAAW;UAC5C,MAAM;YAAEvB,IAAI;YAAEN;UAAM,CAAC,SAAS9F,QAAQ,CAAC2M,OAAI,CAACnI,KAAK,EAAE,MAAM,EAAEoI,QAAQ,EAAE;YACjE9K,OAAO,EAAE6K,OAAI,CAAC7K,OAAO;YACrByG,IAAI,EAAE;cACFO,KAAK;cACLwC,IAAI;cACJ9C,oBAAoB,EAAE;gBAAEC,aAAa,EAAEzF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0F;cAAa;YAClH;UACJ,CAAC,CAAC;UACF,OAAO;YAAEtC,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE,IAAI;cAAE4E,SAAS,EAAE7E,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC8E;YAAW,CAAC;YAAEpF;UAAM,CAAC;QACjI;QACA,MAAM,IAAIrG,2BAA2B,CAAC,6DAA6D,CAAC;MACxG,CAAC,CACD,OAAOqG,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;EACU+G,UAAUA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAApK,iBAAA;MACf,MAAMoK,OAAI,CAACrJ,iBAAiB;MAC5B,OAAOqJ,OAAI,CAAClG,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,OAAOoK,OAAI,CAACV,WAAW;UAAA,IAAAW,KAAA,GAAArK,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YACtC,OAAOA,MAAM;UACjB,CAAC;UAAA,iBAAAU,GAAA;YAAA,OAAAD,KAAA,CAAAvK,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,EAAC;IAAC;EACP;EACA;AACJ;AACA;EACUmE,YAAYA,CAAChE,cAAc,EAAEC,EAAE,EAAE;IAAA,IAAAoK,OAAA;IAAA,OAAAvK,iBAAA;MACnCuK,OAAI,CAAC/G,MAAM,CAAC,eAAe,EAAE,OAAO,EAAEtD,cAAc,CAAC;MACrD,IAAI;QACA,IAAIqK,OAAI,CAACvJ,YAAY,EAAE;UACnB,MAAMwJ,IAAI,GAAGD,OAAI,CAACtJ,aAAa,CAACwJ,MAAM,GAChCF,OAAI,CAACtJ,aAAa,CAACsJ,OAAI,CAACtJ,aAAa,CAACwJ,MAAM,GAAG,CAAC,CAAC,GACjDC,OAAO,CAACC,OAAO,CAAC,CAAC;UACvB,MAAMf,MAAM,GAAG5J,iBAAA,CAAC,aAAY;YACxB,MAAMwK,IAAI;YACV,aAAarK,EAAE,CAAC,CAAC;UACrB,CAAC,EAAE,CAAC;UACJoK,OAAI,CAACtJ,aAAa,CAAC2J,IAAI,CAAC5K,iBAAA,CAAC,aAAY;YACjC,IAAI;cACA,MAAM4J,MAAM;YAChB,CAAC,CACD,OAAOzG,CAAC,EAAE;cACN;YAAA;UAER,CAAC,EAAE,CAAC,CAAC;UACL,OAAOyG,MAAM;QACjB;QACA,aAAaW,OAAI,CAACxI,IAAI,CAAC,QAAQwI,OAAI,CAACvL,UAAU,EAAE,EAAEkB,cAAc,eAAAF,iBAAA,CAAE,aAAY;UAC1EuK,OAAI,CAAC/G,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE+G,OAAI,CAACvL,UAAU,CAAC;UAC9E,IAAI;YACAuL,OAAI,CAACvJ,YAAY,GAAG,IAAI;YACxB,MAAM4I,MAAM,GAAGzJ,EAAE,CAAC,CAAC;YACnBoK,OAAI,CAACtJ,aAAa,CAAC2J,IAAI,CAAC5K,iBAAA,CAAC,aAAY;cACjC,IAAI;gBACA,MAAM4J,MAAM;cAChB,CAAC,CACD,OAAOzG,CAAC,EAAE;gBACN;cAAA;YAER,CAAC,EAAE,CAAC,CAAC;YACL,MAAMyG,MAAM;YACZ;YACA,OAAOW,OAAI,CAACtJ,aAAa,CAACwJ,MAAM,EAAE;cAC9B,MAAMI,MAAM,GAAG,CAAC,GAAGN,OAAI,CAACtJ,aAAa,CAAC;cACtC,MAAMyJ,OAAO,CAACI,GAAG,CAACD,MAAM,CAAC;cACzBN,OAAI,CAACtJ,aAAa,CAAC8J,MAAM,CAAC,CAAC,EAAEF,MAAM,CAACJ,MAAM,CAAC;YAC/C;YACA,aAAab,MAAM;UACvB,CAAC,SACO;YACJW,OAAI,CAAC/G,MAAM,CAAC,eAAe,EAAE,+BAA+B,EAAE+G,OAAI,CAACvL,UAAU,CAAC;YAC9EuL,OAAI,CAACvJ,YAAY,GAAG,KAAK;UAC7B;QACJ,CAAC,EAAC;MACN,CAAC,SACO;QACJuJ,OAAI,CAAC/G,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;MACvC;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACUkG,WAAWA,CAACvJ,EAAE,EAAE;IAAA,IAAA6K,OAAA;IAAA,OAAAhL,iBAAA;MAClBgL,OAAI,CAACxH,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC;MACpC,IAAI;QACA;QACA,MAAMoG,MAAM,SAASoB,OAAI,CAACC,aAAa,CAAC,CAAC;QACzC,aAAa9K,EAAE,CAACyJ,MAAM,CAAC;MAC3B,CAAC,SACO;QACJoB,OAAI,CAACxH,MAAM,CAAC,cAAc,EAAE,KAAK,CAAC;MACtC;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACUyH,aAAaA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAlL,iBAAA;MAClBkL,OAAI,CAAC1H,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;MACxC,IAAI,CAAC0H,OAAI,CAAClK,YAAY,EAAE;QACpBkK,OAAI,CAAC1H,MAAM,CAAC,kBAAkB,EAAE,mCAAmC,EAAE,IAAIsF,KAAK,CAAC,CAAC,CAACqC,KAAK,CAAC;MAC3F;MACA,IAAI;QACA,IAAIC,cAAc,GAAG,IAAI;QACzB,MAAMC,YAAY,SAASxN,YAAY,CAACqN,OAAI,CAAClI,OAAO,EAAEkI,OAAI,CAAClM,UAAU,CAAC;QACtEkM,OAAI,CAAC1H,MAAM,CAAC,eAAe,EAAE,sBAAsB,EAAE6H,YAAY,CAAC;QAClE,IAAIA,YAAY,KAAK,IAAI,EAAE;UACvB,IAAIH,OAAI,CAACI,eAAe,CAACD,YAAY,CAAC,EAAE;YACpCD,cAAc,GAAGC,YAAY;UACjC,CAAC,MACI;YACDH,OAAI,CAAC1H,MAAM,CAAC,eAAe,EAAE,mCAAmC,CAAC;YACjE,MAAM0H,OAAI,CAACxG,cAAc,CAAC,CAAC;UAC/B;QACJ;QACA,IAAI,CAAC0G,cAAc,EAAE;UACjB,OAAO;YAAE1H,IAAI,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAC;YAAEP,KAAK,EAAE;UAAK,CAAC;QACnD;QACA,MAAMmI,UAAU,GAAGH,cAAc,CAACI,UAAU,GACtCJ,cAAc,CAACI,UAAU,IAAIzH,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,IAAI,GAC9C,KAAK;QACXP,OAAI,CAAC1H,MAAM,CAAC,kBAAkB,EAAE,cAAc+H,UAAU,GAAG,EAAE,GAAG,MAAM,UAAU,EAAE,YAAY,EAAEH,cAAc,CAACI,UAAU,CAAC;QAC1H,IAAI,CAACD,UAAU,EAAE;UACb,OAAO;YAAE7H,IAAI,EAAE;cAAEC,OAAO,EAAEyH;YAAe,CAAC;YAAEhI,KAAK,EAAE;UAAK,CAAC;QAC7D;QACA,MAAM;UAAEO,OAAO;UAAEP;QAAM,CAAC,SAAS8H,OAAI,CAACQ,iBAAiB,CAACN,cAAc,CAACO,aAAa,CAAC;QACrF,IAAIvI,KAAK,EAAE;UACP,OAAO;YAAEM,IAAI,EAAE;cAAEC,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QAC7C;QACA,OAAO;UAAEM,IAAI,EAAE;YAAEC;UAAQ,CAAC;UAAEP,KAAK,EAAE;QAAK,CAAC;MAC7C,CAAC,SACO;QACJ8H,OAAI,CAAC1H,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;MAC1C;IAAC;EACL;EACA;AACJ;AACA;AACA;EACUoI,OAAOA,CAAC9B,GAAG,EAAE;IAAA,IAAA+B,OAAA;IAAA,OAAA7L,iBAAA;MACf,IAAI8J,GAAG,EAAE;QACL,aAAa+B,OAAI,CAACC,QAAQ,CAAChC,GAAG,CAAC;MACnC;MACA,MAAM+B,OAAI,CAAC9K,iBAAiB;MAC5B,OAAO8K,OAAI,CAAC3H,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,aAAa6L,OAAI,CAACC,QAAQ,CAAC,CAAC;MAChC,CAAC,EAAC;IAAC;EACP;EACMA,QAAQA,CAAChC,GAAG,EAAE;IAAA,IAAAiC,OAAA;IAAA,OAAA/L,iBAAA;MAChB,IAAI;QACA,IAAI8J,GAAG,EAAE;UACL,aAAaxM,QAAQ,CAACyO,OAAI,CAACjK,KAAK,EAAE,KAAK,EAAE,GAAGiK,OAAI,CAAChN,GAAG,OAAO,EAAE;YACzDK,OAAO,EAAE2M,OAAI,CAAC3M,OAAO;YACrB0K,GAAG,EAAEA,GAAG;YACR3D,KAAK,EAAE1I;UACX,CAAC,CAAC;QACN;QACA,aAAasO,OAAI,CAACrC,WAAW;UAAA,IAAAsC,MAAA,GAAAhM,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE,EAAE2E,EAAE;YACV,MAAM;cAAEzB,IAAI;cAAEN;YAAM,CAAC,GAAGwG,MAAM;YAC9B,IAAIxG,KAAK,EAAE;cACP,MAAMA,KAAK;YACf;YACA,aAAa9F,QAAQ,CAACyO,OAAI,CAACjK,KAAK,EAAE,KAAK,EAAE,GAAGiK,OAAI,CAAChN,GAAG,OAAO,EAAE;cACzDK,OAAO,EAAE2M,OAAI,CAAC3M,OAAO;cACrB0K,GAAG,EAAE,CAAC3E,EAAE,GAAG,CAAC3E,EAAE,GAAGkD,IAAI,CAACC,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH,YAAY,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0D,SAAS;cAC/H1C,KAAK,EAAE1I;YACX,CAAC,CAAC;UACN,CAAC;UAAA,iBAAAwO,GAAA;YAAA,OAAAD,MAAA,CAAAlM,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE;YAAK,CAAC;YAAElD;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACU8I,UAAUA,CAAAC,GAAA,EAA2B;IAAA,IAAAC,OAAA;IAAA,OAAApM,iBAAA,YAA1BqM,UAAU,EAAE/L,OAAO,GAAG,CAAC,CAAC;MACrC,MAAM8L,OAAI,CAACrL,iBAAiB;MAC5B,aAAaqL,OAAI,CAAClI,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QAC3C,aAAaoM,OAAI,CAACE,WAAW,CAACD,UAAU,EAAE/L,OAAO,CAAC;MACtD,CAAC,EAAC;IAAC,GAAAR,KAAA,OAAAC,SAAA;EACP;EACMuM,WAAWA,CAAAC,GAAA,EAA2B;IAAA,IAAAC,OAAA;IAAA,OAAAxM,iBAAA,YAA1BqM,UAAU,EAAE/L,OAAO,GAAG,CAAC,CAAC;MACtC,IAAI;QACA,aAAakM,OAAI,CAAC9C,WAAW;UAAA,IAAA+C,MAAA,GAAAzM,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,MAAM;cAAElG,IAAI,EAAEgJ,WAAW;cAAEtJ,KAAK,EAAEyG;YAAa,CAAC,GAAGD,MAAM;YACzD,IAAIC,YAAY,EAAE;cACd,MAAMA,YAAY;YACtB;YACA,IAAI,CAAC6C,WAAW,CAAC/I,OAAO,EAAE;cACtB,MAAM,IAAI3G,uBAAuB,CAAC,CAAC;YACvC;YACA,MAAM2G,OAAO,GAAG+I,WAAW,CAAC/I,OAAO;YACnC,IAAI6B,aAAa,GAAG,IAAI;YACxB,IAAIC,mBAAmB,GAAG,IAAI;YAC9B,IAAI+G,OAAI,CAACnN,QAAQ,KAAK,MAAM,IAAIgN,UAAU,CAAC/G,KAAK,IAAI,IAAI,EAAE;cACtD,MAAMI,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;cAC3C,MAAMJ,YAAY,CAACuO,OAAI,CAACxJ,OAAO,EAAE,GAAGwJ,OAAI,CAACxN,UAAU,gBAAgB,EAAE0G,YAAY,CAAC;cAClFF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;cACzDD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;YAC3E;YACA,MAAM;cAAE9B,IAAI;cAAEN,KAAK,EAAEuJ;YAAU,CAAC,SAASrP,QAAQ,CAACkP,OAAI,CAAC1K,KAAK,EAAE,KAAK,EAAE,GAAG0K,OAAI,CAACzN,GAAG,OAAO,EAAE;cACrFK,OAAO,EAAEoN,OAAI,CAACpN,OAAO;cACrBuG,UAAU,EAAErF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACsF,eAAe;cACrFC,IAAI,EAAEnE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE0K,UAAU,CAAC,EAAE;gBAAEpG,cAAc,EAAET,aAAa;gBAAEU,qBAAqB,EAAET;cAAoB,CAAC,CAAC;cACjIqE,GAAG,EAAEnG,OAAO,CAACoE,YAAY;cACzB5B,KAAK,EAAE1I;YACX,CAAC,CAAC;YACF,IAAIkP,SAAS,EACT,MAAMA,SAAS;YACnBhJ,OAAO,CAAC2C,IAAI,GAAG5C,IAAI,CAAC4C,IAAI;YACxB,MAAMkG,OAAI,CAAC5H,YAAY,CAACjB,OAAO,CAAC;YAChC,MAAM6I,OAAI,CAAC/I,qBAAqB,CAAC,cAAc,EAAEE,OAAO,CAAC;YACzD,OAAO;cAAED,IAAI,EAAE;gBAAE4C,IAAI,EAAE3C,OAAO,CAAC2C;cAAK,CAAC;cAAElD,KAAK,EAAE;YAAK,CAAC;UACxD,CAAC;UAAA,iBAAAwJ,GAAA;YAAA,OAAAH,MAAA,CAAA3M,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE;YAAK,CAAC;YAAElD;UAAM,CAAC;QAC1C;QACA,MAAMA,KAAK;MACf;IAAC,GAAAtD,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;EACI8M,UAAUA,CAAC/C,GAAG,EAAE;IACZ,OAAOnM,gBAAgB,CAACmM,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;EACUgD,UAAUA,CAAC1B,cAAc,EAAE;IAAA,IAAA2B,OAAA;IAAA,OAAA/M,iBAAA;MAC7B,MAAM+M,OAAI,CAAChM,iBAAiB;MAC5B,aAAagM,OAAI,CAAC7I,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QAC3C,aAAa+M,OAAI,CAACC,WAAW,CAAC5B,cAAc,CAAC;MACjD,CAAC,EAAC;IAAC;EACP;EACM4B,WAAWA,CAAC5B,cAAc,EAAE;IAAA,IAAA6B,OAAA;IAAA,OAAAjN,iBAAA;MAC9B,IAAI;QACA,IAAI,CAACoL,cAAc,CAACrD,YAAY,IAAI,CAACqD,cAAc,CAACO,aAAa,EAAE;UAC/D,MAAM,IAAI3O,uBAAuB,CAAC,CAAC;QACvC;QACA,MAAMkQ,OAAO,GAAGnJ,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,IAAI;QACjC,IAAI0B,SAAS,GAAGD,OAAO;QACvB,IAAI3B,UAAU,GAAG,IAAI;QACrB,IAAI5H,OAAO,GAAG,IAAI;QAClB,MAAMyJ,OAAO,GAAGzP,gBAAgB,CAACyN,cAAc,CAACrD,YAAY,CAAC;QAC7D,IAAIqF,OAAO,CAACC,GAAG,EAAE;UACbF,SAAS,GAAGC,OAAO,CAACC,GAAG;UACvB9B,UAAU,GAAG4B,SAAS,IAAID,OAAO;QACrC;QACA,IAAI3B,UAAU,EAAE;UACZ,MAAM;YAAE5H,OAAO,EAAE2J,gBAAgB;YAAElK;UAAM,CAAC,SAAS6J,OAAI,CAACvB,iBAAiB,CAACN,cAAc,CAACO,aAAa,CAAC;UACvG,IAAIvI,KAAK,EAAE;YACP,OAAO;cAAEM,IAAI,EAAE;gBAAE4C,IAAI,EAAE,IAAI;gBAAE3C,OAAO,EAAE;cAAK,CAAC;cAAEP,KAAK,EAAEA;YAAM,CAAC;UAChE;UACA,IAAI,CAACkK,gBAAgB,EAAE;YACnB,OAAO;cAAE5J,IAAI,EAAE;gBAAE4C,IAAI,EAAE,IAAI;gBAAE3C,OAAO,EAAE;cAAK,CAAC;cAAEP,KAAK,EAAE;YAAK,CAAC;UAC/D;UACAO,OAAO,GAAG2J,gBAAgB;QAC9B,CAAC,MACI;UACD,MAAM;YAAE5J,IAAI;YAAEN;UAAM,CAAC,SAAS6J,OAAI,CAACnB,QAAQ,CAACV,cAAc,CAACrD,YAAY,CAAC;UACxE,IAAI3E,KAAK,EAAE;YACP,MAAMA,KAAK;UACf;UACAO,OAAO,GAAG;YACNoE,YAAY,EAAEqD,cAAc,CAACrD,YAAY;YACzC4D,aAAa,EAAEP,cAAc,CAACO,aAAa;YAC3CrF,IAAI,EAAE5C,IAAI,CAAC4C,IAAI;YACfiH,UAAU,EAAE,QAAQ;YACpBC,UAAU,EAAEL,SAAS,GAAGD,OAAO;YAC/B1B,UAAU,EAAE2B;UAChB,CAAC;UACD,MAAMF,OAAI,CAACrI,YAAY,CAACjB,OAAO,CAAC;UAChC,MAAMsJ,OAAI,CAACxJ,qBAAqB,CAAC,WAAW,EAAEE,OAAO,CAAC;QAC1D;QACA,OAAO;UAAED,IAAI,EAAE;YAAE4C,IAAI,EAAE3C,OAAO,CAAC2C,IAAI;YAAE3C;UAAQ,CAAC;UAAEP,KAAK,EAAE;QAAK,CAAC;MACjE,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAEC,OAAO,EAAE,IAAI;cAAE2C,IAAI,EAAE;YAAK,CAAC;YAAElD;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACUqK,cAAcA,CAACrC,cAAc,EAAE;IAAA,IAAAsC,OAAA;IAAA,OAAA1N,iBAAA;MACjC,MAAM0N,OAAI,CAAC3M,iBAAiB;MAC5B,aAAa2M,OAAI,CAACxJ,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QAC3C,aAAa0N,OAAI,CAACC,eAAe,CAACvC,cAAc,CAAC;MACrD,CAAC,EAAC;IAAC;EACP;EACMuC,eAAeA,CAACvC,cAAc,EAAE;IAAA,IAAAwC,OAAA;IAAA,OAAA5N,iBAAA;MAClC,IAAI;QACA,aAAa4N,OAAI,CAAClE,WAAW;UAAA,IAAAmE,MAAA,GAAA7N,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE;YACN,IAAI,CAAC4K,cAAc,EAAE;cACjB,MAAM;gBAAE1H,IAAI;gBAAEN;cAAM,CAAC,GAAGwG,MAAM;cAC9B,IAAIxG,KAAK,EAAE;gBACP,MAAMA,KAAK;cACf;cACAgI,cAAc,GAAG,CAAC5K,EAAE,GAAGkD,IAAI,CAACC,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGqI,SAAS;YACnF;YACA,IAAI,EAAEuC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACO,aAAa,CAAC,EAAE;cACjG,MAAM,IAAI3O,uBAAuB,CAAC,CAAC;YACvC;YACA,MAAM;cAAE2G,OAAO;cAAEP;YAAM,CAAC,SAASwK,OAAI,CAAClC,iBAAiB,CAACN,cAAc,CAACO,aAAa,CAAC;YACrF,IAAIvI,KAAK,EAAE;cACP,OAAO;gBAAEM,IAAI,EAAE;kBAAE4C,IAAI,EAAE,IAAI;kBAAE3C,OAAO,EAAE;gBAAK,CAAC;gBAAEP,KAAK,EAAEA;cAAM,CAAC;YAChE;YACA,IAAI,CAACO,OAAO,EAAE;cACV,OAAO;gBAAED,IAAI,EAAE;kBAAE4C,IAAI,EAAE,IAAI;kBAAE3C,OAAO,EAAE;gBAAK,CAAC;gBAAEP,KAAK,EAAE;cAAK,CAAC;YAC/D;YACA,OAAO;cAAEM,IAAI,EAAE;gBAAE4C,IAAI,EAAE3C,OAAO,CAAC2C,IAAI;gBAAE3C;cAAQ,CAAC;cAAEP,KAAK,EAAE;YAAK,CAAC;UACjE,CAAC;UAAA,iBAAA0K,GAAA;YAAA,OAAAD,MAAA,CAAA/N,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAE4C,IAAI,EAAE,IAAI;cAAE3C,OAAO,EAAE;YAAK,CAAC;YAAEP;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACUoB,kBAAkBA,CAACH,UAAU,EAAE;IAAA,IAAA0J,OAAA;IAAA,OAAA/N,iBAAA;MACjC,IAAI;QACA,IAAI,CAAClC,SAAS,CAAC,CAAC,EACZ,MAAM,IAAIjB,8BAA8B,CAAC,sBAAsB,CAAC;QACpE,IAAIkR,OAAI,CAAC1O,QAAQ,KAAK,UAAU,IAAI,CAAC0O,OAAI,CAACxJ,oBAAoB,CAAC,CAAC,EAAE;UAC9D,MAAM,IAAI1H,8BAA8B,CAAC,sCAAsC,CAAC;QACpF,CAAC,MACI,IAAIkR,OAAI,CAAC1O,QAAQ,IAAI,MAAM,IAAI,CAACgF,UAAU,EAAE;UAC7C,MAAM,IAAIvH,8BAA8B,CAAC,4BAA4B,CAAC;QAC1E;QACA,MAAM4L,MAAM,GAAGlK,sBAAsB,CAACwP,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;QAC3D,IAAI7J,UAAU,EAAE;UACZ,IAAI,CAACqE,MAAM,CAACyF,IAAI,EACZ,MAAM,IAAIrR,8BAA8B,CAAC,mBAAmB,CAAC;UACjE,MAAM;YAAE4G,IAAI;YAAEN;UAAM,CAAC,SAAS2K,OAAI,CAACzG,uBAAuB,CAACoB,MAAM,CAACyF,IAAI,CAAC;UACvE,IAAI/K,KAAK,EACL,MAAMA,KAAK;UACf,MAAMrE,GAAG,GAAG,IAAIqP,GAAG,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;UACzCnP,GAAG,CAACsP,YAAY,CAACC,MAAM,CAAC,MAAM,CAAC;UAC/BN,MAAM,CAACO,OAAO,CAACC,YAAY,CAACR,MAAM,CAACO,OAAO,CAACE,KAAK,EAAE,EAAE,EAAE1P,GAAG,CAAC2P,QAAQ,CAAC,CAAC,CAAC;UACrE,OAAO;YAAEhL,IAAI,EAAE;cAAEC,OAAO,EAAED,IAAI,CAACC,OAAO;cAAEgB,YAAY,EAAE;YAAK,CAAC;YAAEvB,KAAK,EAAE;UAAK,CAAC;QAC/E;QACA,IAAIsF,MAAM,CAACtF,KAAK,IAAIsF,MAAM,CAACiG,iBAAiB,IAAIjG,MAAM,CAACkG,UAAU,EAAE;UAC/D,MAAM,IAAI/R,8BAA8B,CAAC6L,MAAM,CAACiG,iBAAiB,IAAI,iDAAiD,EAAE;YACpHvL,KAAK,EAAEsF,MAAM,CAACtF,KAAK,IAAI,mBAAmB;YAC1C+K,IAAI,EAAEzF,MAAM,CAACkG,UAAU,IAAI;UAC/B,CAAC,CAAC;QACN;QACA,MAAM;UAAEC,cAAc;UAAEC,sBAAsB;UAAE/G,YAAY;UAAE4D,aAAa;UAAE6B,UAAU;UAAEhC,UAAU;UAAE+B;QAAY,CAAC,GAAG7E,MAAM;QAC3H,IAAI,CAACX,YAAY,IAAI,CAACyF,UAAU,IAAI,CAAC7B,aAAa,IAAI,CAAC4B,UAAU,EAAE;UAC/D,MAAM,IAAI1Q,8BAA8B,CAAC,2BAA2B,CAAC;QACzE;QACA,MAAMqQ,OAAO,GAAG6B,IAAI,CAACC,KAAK,CAACjL,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAC7C,MAAMwD,SAAS,GAAGC,QAAQ,CAAC1B,UAAU,CAAC;QACtC,IAAIL,SAAS,GAAGD,OAAO,GAAG+B,SAAS;QACnC,IAAIzD,UAAU,EAAE;UACZ2B,SAAS,GAAG+B,QAAQ,CAAC1D,UAAU,CAAC;QACpC;QACA,MAAM2D,iBAAiB,GAAGhC,SAAS,GAAGD,OAAO;QAC7C,IAAIiC,iBAAiB,GAAG,IAAI,IAAI5P,0BAA0B,EAAE;UACxD6B,OAAO,CAACI,IAAI,CAAC,iEAAiE2N,iBAAiB,iCAAiCF,SAAS,GAAG,CAAC;QACjJ;QACA,MAAMG,QAAQ,GAAGjC,SAAS,GAAG8B,SAAS;QACtC,IAAI/B,OAAO,GAAGkC,QAAQ,IAAI,GAAG,EAAE;UAC3BhO,OAAO,CAACI,IAAI,CAAC,iGAAiG,EAAE4N,QAAQ,EAAEjC,SAAS,EAAED,OAAO,CAAC;QACjJ,CAAC,MACI,IAAIA,OAAO,GAAGkC,QAAQ,GAAG,CAAC,EAAE;UAC7BhO,OAAO,CAACI,IAAI,CAAC,6GAA6G,EAAE4N,QAAQ,EAAEjC,SAAS,EAAED,OAAO,CAAC;QAC7J;QACA,MAAM;UAAExJ,IAAI;UAAEN;QAAM,CAAC,SAAS2K,OAAI,CAACjC,QAAQ,CAAC/D,YAAY,CAAC;QACzD,IAAI3E,KAAK,EACL,MAAMA,KAAK;QACf,MAAMO,OAAO,GAAG;UACZkL,cAAc;UACdC,sBAAsB;UACtB/G,YAAY;UACZyF,UAAU,EAAEyB,SAAS;UACrBzD,UAAU,EAAE2B,SAAS;UACrBxB,aAAa;UACb4B,UAAU;UACVjH,IAAI,EAAE5C,IAAI,CAAC4C;QACf,CAAC;QACD;QACA0H,MAAM,CAACC,QAAQ,CAACoB,IAAI,GAAG,EAAE;QACzBtB,OAAI,CAACvK,MAAM,CAAC,uBAAuB,EAAE,+BAA+B,CAAC;QACrE,OAAO;UAAEE,IAAI,EAAE;YAAEC,OAAO;YAAEgB,YAAY,EAAE+D,MAAM,CAACE;UAAK,CAAC;UAAExF,KAAK,EAAE;QAAK,CAAC;MACxE,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAEC,OAAO,EAAE,IAAI;cAAEgB,YAAY,EAAE;YAAK,CAAC;YAAEvB;UAAM,CAAC;QACjE;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACImB,oBAAoBA,CAAA,EAAG;IACnB,MAAMmE,MAAM,GAAGlK,sBAAsB,CAACwP,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC3D,OAAO,CAAC,EAAEpQ,SAAS,CAAC,CAAC,KAAK4K,MAAM,CAACX,YAAY,IAAIW,MAAM,CAACiG,iBAAiB,CAAC,CAAC;EAC/E;EACA;AACJ;AACA;EACUrK,WAAWA,CAAA,EAAG;IAAA,IAAAgL,OAAA;IAAA,OAAAtP,iBAAA;MAChB,MAAM0I,MAAM,GAAGlK,sBAAsB,CAACwP,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MAC3D,MAAMqB,qBAAqB,SAAS1R,YAAY,CAACyR,OAAI,CAACtM,OAAO,EAAE,GAAGsM,OAAI,CAACtQ,UAAU,gBAAgB,CAAC;MAClG,OAAO,CAAC,EAAE0J,MAAM,CAACyF,IAAI,IAAIoB,qBAAqB,CAAC;IAAC;EACpD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,OAAOA,CAAA,EAAgC;IAAA,IAAAC,OAAA;IAAA,OAAAzP,iBAAA,YAA/BM,OAAO,GAAG;MAAEoP,KAAK,EAAE;IAAS,CAAC;MACvC,MAAMD,OAAI,CAAC1O,iBAAiB;MAC5B,aAAa0O,OAAI,CAACvL,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QAC3C,aAAayP,OAAI,CAACE,QAAQ,CAACrP,OAAO,CAAC;MACvC,CAAC,EAAC;IAAC,GAAAR,KAAA,OAAAC,SAAA;EACP;EACM4P,QAAQA,CAAA,EAAkC;IAAA,IAAAC,OAAA;IAAA,OAAA5P,iBAAA,YAAjC;MAAE0P;IAAM,CAAC,GAAG;MAAEA,KAAK,EAAE;IAAS,CAAC;MAC1C,aAAaE,OAAI,CAAClG,WAAW;QAAA,IAAAmG,MAAA,GAAA7P,iBAAA,CAAC,WAAO4J,MAAM,EAAK;UAC5C,IAAIpJ,EAAE;UACN,MAAM;YAAEkD,IAAI;YAAEN,KAAK,EAAEyG;UAAa,CAAC,GAAGD,MAAM;UAC5C,IAAIC,YAAY,EAAE;YACd,OAAO;cAAEzG,KAAK,EAAEyG;YAAa,CAAC;UAClC;UACA,MAAMiG,WAAW,GAAG,CAACtP,EAAE,GAAGkD,IAAI,CAACC,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH,YAAY;UAC5F,IAAI+H,WAAW,EAAE;YACb,MAAM;cAAE1M;YAAM,CAAC,SAASwM,OAAI,CAAC/N,KAAK,CAAC2N,OAAO,CAACM,WAAW,EAAEJ,KAAK,CAAC;YAC9D,IAAItM,KAAK,EAAE;cACP;cACA;cACA,IAAI,EAAEjG,cAAc,CAACiG,KAAK,CAAC,KAAKA,KAAK,CAAC2M,MAAM,KAAK,GAAG,IAAI3M,KAAK,CAAC2M,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE;gBAC5E,OAAO;kBAAE3M;gBAAM,CAAC;cACpB;YACJ;UACJ;UACA,IAAIsM,KAAK,KAAK,QAAQ,EAAE;YACpB,MAAME,OAAI,CAAClL,cAAc,CAAC,CAAC;YAC3B,MAAM3G,eAAe,CAAC6R,OAAI,CAAC5M,OAAO,EAAE,GAAG4M,OAAI,CAAC5Q,UAAU,gBAAgB,CAAC;YACvE,MAAM4Q,OAAI,CAACnM,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;UACxD;UACA,OAAO;YAAEL,KAAK,EAAE;UAAK,CAAC;QAC1B,CAAC;QAAA,iBAAA4M,IAAA;UAAA,OAAAH,MAAA,CAAA/P,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC,GAAAD,KAAA,OAAAC,SAAA;EACP;EACA;AACJ;AACA;AACA;EACIkQ,iBAAiBA,CAACC,QAAQ,EAAE;IAAA,IAAAC,OAAA;IACxB,MAAMC,EAAE,GAAGlS,IAAI,CAAC,CAAC;IACjB,MAAMmS,YAAY,GAAG;MACjBD,EAAE;MACFF,QAAQ;MACRI,WAAW,EAAEA,CAAA,KAAM;QACf,IAAI,CAAC9M,MAAM,CAAC,gBAAgB,EAAE,uCAAuC,EAAE4M,EAAE,CAAC;QAC1E,IAAI,CAAC1P,mBAAmB,CAAC4N,MAAM,CAAC8B,EAAE,CAAC;MACvC;IACJ,CAAC;IACD,IAAI,CAAC5M,MAAM,CAAC,sBAAsB,EAAE,6BAA6B,EAAE4M,EAAE,CAAC;IACtE,IAAI,CAAC1P,mBAAmB,CAAC6P,GAAG,CAACH,EAAE,EAAEC,YAAY,CAAC;IAC9CrQ,iBAAA,CAAC,aAAY;MACT,MAAMmQ,OAAI,CAACpP,iBAAiB;MAC5B,MAAMoP,OAAI,CAACjM,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACpCmQ,OAAI,CAACK,mBAAmB,CAACJ,EAAE,CAAC;MAChC,CAAC,EAAC;IACN,CAAC,EAAE,CAAC;IACJ,OAAO;MAAE1M,IAAI,EAAE;QAAE2M;MAAa;IAAE,CAAC;EACrC;EACMG,mBAAmBA,CAACJ,EAAE,EAAE;IAAA,IAAAK,OAAA;IAAA,OAAAzQ,iBAAA;MAC1B,aAAayQ,OAAI,CAAC/G,WAAW;QAAA,IAAAgH,MAAA,GAAA1Q,iBAAA,CAAC,WAAO4J,MAAM,EAAK;UAC5C,IAAIpJ,EAAE,EAAE2E,EAAE;UACV,IAAI;YACA,MAAM;cAAEzB,IAAI,EAAE;gBAAEC;cAAQ,CAAC;cAAEP;YAAO,CAAC,GAAGwG,MAAM;YAC5C,IAAIxG,KAAK,EACL,MAAMA,KAAK;YACf,MAAO,CAAC5C,EAAE,GAAGiQ,OAAI,CAAC/P,mBAAmB,CAACiQ,GAAG,CAACP,EAAE,CAAC,MAAM,IAAI,IAAI5P,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0P,QAAQ,CAAC,iBAAiB,EAAEvM,OAAO,CAAC;YAC3H8M,OAAI,CAACjN,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAE4M,EAAE,EAAE,SAAS,EAAEzM,OAAO,CAAC;UACzE,CAAC,CACD,OAAOiN,GAAG,EAAE;YACR,MAAO,CAACzL,EAAE,GAAGsL,OAAI,CAAC/P,mBAAmB,CAACiQ,GAAG,CAACP,EAAE,CAAC,MAAM,IAAI,IAAIjL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+K,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC;YACxHO,OAAI,CAACjN,MAAM,CAAC,iBAAiB,EAAE,aAAa,EAAE4M,EAAE,EAAE,OAAO,EAAEQ,GAAG,CAAC;YAC/DxP,OAAO,CAACgC,KAAK,CAACwN,GAAG,CAAC;UACtB;QACJ,CAAC;QAAA,iBAAAC,IAAA;UAAA,OAAAH,MAAA,CAAA5Q,KAAA,OAAAC,SAAA;QAAA;MAAA,IAAC;IAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU+Q,qBAAqBA,CAAAC,IAAA,EAAsB;IAAA,IAAAC,OAAA;IAAA,OAAAhR,iBAAA,YAArBsF,KAAK,EAAEhF,OAAO,GAAG,CAAC,CAAC;MAC3C,IAAIkF,aAAa,GAAG,IAAI;MACxB,IAAIC,mBAAmB,GAAG,IAAI;MAC9B,IAAIuL,OAAI,CAAC3R,QAAQ,KAAK,MAAM,EAAE;QAC1B,MAAMqG,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;QAC3C,MAAMJ,YAAY,CAAC+S,OAAI,CAAChO,OAAO,EAAE,GAAGgO,OAAI,CAAChS,UAAU,gBAAgB,EAAE,GAAG0G,YAAY,oBAAoB,CAAC;QACzGF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;QACzDD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;MAC3E;MACA,IAAI;QACA,aAAalI,QAAQ,CAAC0T,OAAI,CAAClP,KAAK,EAAE,MAAM,EAAE,GAAGkP,OAAI,CAACjS,GAAG,UAAU,EAAE;UAC7D8G,IAAI,EAAE;YACFP,KAAK;YACLW,cAAc,EAAET,aAAa;YAC7BU,qBAAqB,EAAET,mBAAmB;YAC1CK,oBAAoB,EAAE;cAAEC,aAAa,EAAEzF,OAAO,CAAC0F;YAAa;UAChE,CAAC;UACD5G,OAAO,EAAE4R,OAAI,CAAC5R,OAAO;UACrBuG,UAAU,EAAErF,OAAO,CAACqF;QACxB,CAAC,CAAC;MACN,CAAC,CACD,OAAOvC,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC,GAAAtD,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;EACUkR,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAlR,iBAAA;MACtB,IAAIQ,EAAE;MACN,IAAI;QACA,MAAM;UAAEkD,IAAI;UAAEN;QAAM,CAAC,SAAS8N,OAAI,CAACtF,OAAO,CAAC,CAAC;QAC5C,IAAIxI,KAAK,EACL,MAAMA,KAAK;QACf,OAAO;UAAEM,IAAI,EAAE;YAAEyN,UAAU,EAAE,CAAC3Q,EAAE,GAAGkD,IAAI,CAAC4C,IAAI,CAAC6K,UAAU,MAAM,IAAI,IAAI3Q,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;UAAG,CAAC;UAAE4C,KAAK,EAAE;QAAK,CAAC;MACjH,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;EACUgO,YAAYA,CAACnM,WAAW,EAAE;IAAA,IAAAoM,OAAA;IAAA,OAAArR,iBAAA;MAC5B,IAAIQ,EAAE;MACN,IAAI;QACA,MAAM;UAAEkD,IAAI;UAAEN;QAAM,CAAC,SAASiO,OAAI,CAAC3H,WAAW;UAAA,IAAA4H,MAAA,GAAAtR,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC7D,IAAIpJ,EAAE,EAAE2E,EAAE,EAAEC,EAAE,EAAEyB,EAAE,EAAEuB,EAAE;YACtB,MAAM;cAAE1E,IAAI;cAAEN;YAAM,CAAC,GAAGwG,MAAM;YAC9B,IAAIxG,KAAK,EACL,MAAMA,KAAK;YACf,MAAMrE,GAAG,SAASsS,OAAI,CAACE,kBAAkB,CAAC,GAAGF,OAAI,CAACtS,GAAG,4BAA4B,EAAEkG,WAAW,CAAC8B,QAAQ,EAAE;cACrGpB,UAAU,EAAE,CAACnF,EAAE,GAAGyE,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmF,UAAU;cACzFqB,MAAM,EAAE,CAAC7B,EAAE,GAAGF,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAI6E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,MAAM;cACjFC,WAAW,EAAE,CAAC7B,EAAE,GAAGH,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAI8E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,WAAW;cAC3FC,mBAAmB,EAAE;YACzB,CAAC,CAAC;YACF,aAAa5J,QAAQ,CAAC+T,OAAI,CAACvP,KAAK,EAAE,KAAK,EAAE/C,GAAG,EAAE;cAC1CK,OAAO,EAAEiS,OAAI,CAACjS,OAAO;cACrB0K,GAAG,EAAE,CAAC1B,EAAE,GAAG,CAACvB,EAAE,GAAGnD,IAAI,CAACC,OAAO,MAAM,IAAI,IAAIkD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,YAAY,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGS;YAC1H,CAAC,CAAC;UACN,CAAC;UAAA,iBAAA2I,IAAA;YAAA,OAAAF,MAAA,CAAAxR,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;QACF,IAAIqD,KAAK,EACL,MAAMA,KAAK;QACf,IAAItF,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC0C,EAAE,GAAGyE,WAAW,CAAC3E,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0G,mBAAmB,CAAC,EAAE;UAC1G8G,MAAM,CAACC,QAAQ,CAACtM,MAAM,CAAC+B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC3E,GAAG,CAAC;QAChF;QACA,OAAO;UAAE2E,IAAI,EAAE;YAAEqD,QAAQ,EAAE9B,WAAW,CAAC8B,QAAQ;YAAEhI,GAAG,EAAE2E,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC3E;UAAI,CAAC;UAAEqE,KAAK,EAAE;QAAK,CAAC;MAC/H,CAAC,CACD,OAAOA,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAEqD,QAAQ,EAAE9B,WAAW,CAAC8B,QAAQ;cAAEhI,GAAG,EAAE;YAAK,CAAC;YAAEqE;UAAM,CAAC;QACzE;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACUqO,cAAcA,CAACC,QAAQ,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAA3R,iBAAA;MAC3B,IAAI;QACA,aAAa2R,OAAI,CAACjI,WAAW;UAAA,IAAAkI,MAAA,GAAA5R,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE,EAAE2E,EAAE;YACV,MAAM;cAAEzB,IAAI;cAAEN;YAAM,CAAC,GAAGwG,MAAM;YAC9B,IAAIxG,KAAK,EAAE;cACP,MAAMA,KAAK;YACf;YACA,aAAa9F,QAAQ,CAACqU,OAAI,CAAC7P,KAAK,EAAE,QAAQ,EAAE,GAAG6P,OAAI,CAAC5S,GAAG,oBAAoB2S,QAAQ,CAACG,WAAW,EAAE,EAAE;cAC/FzS,OAAO,EAAEuS,OAAI,CAACvS,OAAO;cACrB0K,GAAG,EAAE,CAAC3E,EAAE,GAAG,CAAC3E,EAAE,GAAGkD,IAAI,CAACC,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH,YAAY,MAAM,IAAI,IAAI5C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG0D;YAC1H,CAAC,CAAC;UACN,CAAC;UAAA,iBAAAiJ,IAAA;YAAA,OAAAF,MAAA,CAAA9R,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;EACU2O,mBAAmBA,CAACC,YAAY,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAjS,iBAAA;MACpC,MAAMkS,SAAS,GAAG,wBAAwBF,YAAY,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;MAC5EF,OAAI,CAACzO,MAAM,CAAC0O,SAAS,EAAE,OAAO,CAAC;MAC/B,IAAI;QACA,MAAME,SAAS,GAAGrO,IAAI,CAAC0H,GAAG,CAAC,CAAC;QAC5B;QACA,aAAatN,SAAS;UAAA,IAAAkU,MAAA,GAAArS,iBAAA,CAAC,WAAOsS,OAAO,EAAK;YACtC,MAAMlU,KAAK,CAACkU,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;YAC5BL,OAAI,CAACzO,MAAM,CAAC0O,SAAS,EAAE,oBAAoB,EAAEI,OAAO,CAAC;YACrD,aAAahV,QAAQ,CAAC2U,OAAI,CAACnQ,KAAK,EAAE,MAAM,EAAE,GAAGmQ,OAAI,CAAClT,GAAG,iCAAiC,EAAE;cACpF8G,IAAI,EAAE;gBAAE8F,aAAa,EAAEqG;cAAa,CAAC;cACrC5S,OAAO,EAAE6S,OAAI,CAAC7S,OAAO;cACrB+G,KAAK,EAAE5I;YACX,CAAC,CAAC;UACN,CAAC;UAAA,iBAAAgV,IAAA;YAAA,OAAAF,MAAA,CAAAvS,KAAA,OAAAC,SAAA;UAAA;QAAA,KAAE,CAACuS,OAAO,EAAEE,CAAC,EAAE5I,MAAM,KAAKA,MAAM,IAC7BA,MAAM,CAACxG,KAAK,IACZ/F,yBAAyB,CAACuM,MAAM,CAACxG,KAAK,CAAC;QACvC;QACAW,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,CAAC6G,OAAO,GAAG,CAAC,IAAI,GAAG,GAAGF,SAAS,GAAG7S,0BAA0B,CAAC;MAClF,CAAC,CACD,OAAO6D,KAAK,EAAE;QACV6O,OAAI,CAACzO,MAAM,CAAC0O,SAAS,EAAE,OAAO,EAAE9O,KAAK,CAAC;QACtC,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE;cAAEC,OAAO,EAAE,IAAI;cAAE2C,IAAI,EAAE;YAAK,CAAC;YAAElD;UAAM,CAAC;QACzD;QACA,MAAMA,KAAK;MACf,CAAC,SACO;QACJ6O,OAAI,CAACzO,MAAM,CAAC0O,SAAS,EAAE,KAAK,CAAC;MACjC;IAAC;EACL;EACA5G,eAAeA,CAACD,YAAY,EAAE;IAC1B,MAAMoH,cAAc,GAAG,OAAOpH,YAAY,KAAK,QAAQ,IACnDA,YAAY,KAAK,IAAI,IACrB,cAAc,IAAIA,YAAY,IAC9B,eAAe,IAAIA,YAAY,IAC/B,YAAY,IAAIA,YAAY;IAChC,OAAOoH,cAAc;EACzB;EACM3L,qBAAqBA,CAACC,QAAQ,EAAEzG,OAAO,EAAE;IAAA,IAAAoS,OAAA;IAAA,OAAA1S,iBAAA;MAC3C,MAAMjB,GAAG,SAAS2T,OAAI,CAACnB,kBAAkB,CAAC,GAAGmB,OAAI,CAAC3T,GAAG,YAAY,EAAEgI,QAAQ,EAAE;QACzEpB,UAAU,EAAErF,OAAO,CAACqF,UAAU;QAC9BqB,MAAM,EAAE1G,OAAO,CAAC0G,MAAM;QACtBC,WAAW,EAAE3G,OAAO,CAAC2G;MACzB,CAAC,CAAC;MACFyL,OAAI,CAAClP,MAAM,CAAC,0BAA0B,EAAE,UAAU,EAAEuD,QAAQ,EAAE,SAAS,EAAEzG,OAAO,EAAE,KAAK,EAAEvB,GAAG,CAAC;MAC7F;MACA,IAAIjB,SAAS,CAAC,CAAC,IAAI,CAACwC,OAAO,CAAC4G,mBAAmB,EAAE;QAC7C8G,MAAM,CAACC,QAAQ,CAACtM,MAAM,CAAC5C,GAAG,CAAC;MAC/B;MACA,OAAO;QAAE2E,IAAI,EAAE;UAAEqD,QAAQ;UAAEhI;QAAI,CAAC;QAAEqE,KAAK,EAAE;MAAK,CAAC;IAAC;EACpD;EACA;AACJ;AACA;AACA;EACU0B,kBAAkBA,CAAA,EAAG;IAAA,IAAA6N,OAAA;IAAA,OAAA3S,iBAAA;MACvB,IAAIQ,EAAE;MACN,MAAM0R,SAAS,GAAG,uBAAuB;MACzCS,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,OAAO,CAAC;MAC/B,IAAI;QACA,MAAM9G,cAAc,SAASvN,YAAY,CAAC8U,OAAI,CAAC3P,OAAO,EAAE2P,OAAI,CAAC3T,UAAU,CAAC;QACxE2T,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,sBAAsB,EAAE9G,cAAc,CAAC;QAC9D,IAAI,CAACuH,OAAI,CAACrH,eAAe,CAACF,cAAc,CAAC,EAAE;UACvCuH,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,sBAAsB,CAAC;UAC9C,IAAI9G,cAAc,KAAK,IAAI,EAAE;YACzB,MAAMuH,OAAI,CAACjO,cAAc,CAAC,CAAC;UAC/B;UACA;QACJ;QACA,MAAMwI,OAAO,GAAG6B,IAAI,CAACC,KAAK,CAACjL,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAC7C,MAAMmH,iBAAiB,GAAG,CAAC,CAACpS,EAAE,GAAG4K,cAAc,CAACI,UAAU,MAAM,IAAI,IAAIhL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGqS,QAAQ,IAAI3F,OAAO,GAAGxQ,aAAa;QAChIiW,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,cAAcU,iBAAiB,GAAG,EAAE,GAAG,MAAM,2BAA2BlW,aAAa,GAAG,CAAC;QAChH,IAAIkW,iBAAiB,EAAE;UACnB,IAAID,OAAI,CAAC1T,gBAAgB,IAAImM,cAAc,CAACO,aAAa,EAAE;YACvD,MAAM;cAAEvI;YAAM,CAAC,SAASuP,OAAI,CAACjH,iBAAiB,CAACN,cAAc,CAACO,aAAa,CAAC;YAC5E,IAAIvI,KAAK,EAAE;cACPhC,OAAO,CAACgC,KAAK,CAACA,KAAK,CAAC;cACpB,IAAI,CAAC/F,yBAAyB,CAAC+F,KAAK,CAAC,EAAE;gBACnCuP,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,iEAAiE,EAAE9O,KAAK,CAAC;gBAChG,MAAMuP,OAAI,CAACjO,cAAc,CAAC,CAAC;cAC/B;YACJ;UACJ;QACJ,CAAC,MACI;UACD;UACA;UACA;UACA,MAAMiO,OAAI,CAAClP,qBAAqB,CAAC,WAAW,EAAE2H,cAAc,CAAC;QACjE;MACJ,CAAC,CACD,OAAOwF,GAAG,EAAE;QACR+B,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,OAAO,EAAEtB,GAAG,CAAC;QACpCxP,OAAO,CAACgC,KAAK,CAACwN,GAAG,CAAC;QAClB;MACJ,CAAC,SACO;QACJ+B,OAAI,CAACnP,MAAM,CAAC0O,SAAS,EAAE,KAAK,CAAC;MACjC;IAAC;EACL;EACMxG,iBAAiBA,CAACsG,YAAY,EAAE;IAAA,IAAAc,OAAA;IAAA,OAAA9S,iBAAA;MAClC,IAAIQ,EAAE,EAAE2E,EAAE;MACV,IAAI,CAAC6M,YAAY,EAAE;QACf,MAAM,IAAIhV,uBAAuB,CAAC,CAAC;MACvC;MACA;MACA,IAAI8V,OAAI,CAAChS,kBAAkB,EAAE;QACzB,OAAOgS,OAAI,CAAChS,kBAAkB,CAACiS,OAAO;MAC1C;MACA,MAAMb,SAAS,GAAG,sBAAsBF,YAAY,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;MAC1EW,OAAI,CAACtP,MAAM,CAAC0O,SAAS,EAAE,OAAO,CAAC;MAC/B,IAAI;QACAY,OAAI,CAAChS,kBAAkB,GAAG,IAAIlD,QAAQ,CAAC,CAAC;QACxC,MAAM;UAAE8F,IAAI;UAAEN;QAAM,CAAC,SAAS0P,OAAI,CAACf,mBAAmB,CAACC,YAAY,CAAC;QACpE,IAAI5O,KAAK,EACL,MAAMA,KAAK;QACf,IAAI,CAACM,IAAI,CAACC,OAAO,EACb,MAAM,IAAI3G,uBAAuB,CAAC,CAAC;QACvC,MAAM8V,OAAI,CAAClO,YAAY,CAAClB,IAAI,CAACC,OAAO,CAAC;QACrC,MAAMmP,OAAI,CAACrP,qBAAqB,CAAC,iBAAiB,EAAEC,IAAI,CAACC,OAAO,CAAC;QACjE,MAAMiG,MAAM,GAAG;UAAEjG,OAAO,EAAED,IAAI,CAACC,OAAO;UAAEP,KAAK,EAAE;QAAK,CAAC;QACrD0P,OAAI,CAAChS,kBAAkB,CAAC6J,OAAO,CAACf,MAAM,CAAC;QACvC,OAAOA,MAAM;MACjB,CAAC,CACD,OAAOxG,KAAK,EAAE;QACV0P,OAAI,CAACtP,MAAM,CAAC0O,SAAS,EAAE,OAAO,EAAE9O,KAAK,CAAC;QACtC,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,MAAMwG,MAAM,GAAG;YAAEjG,OAAO,EAAE,IAAI;YAAEP;UAAM,CAAC;UACvC,IAAI,CAAC/F,yBAAyB,CAAC+F,KAAK,CAAC,EAAE;YACnC,MAAM0P,OAAI,CAACpO,cAAc,CAAC,CAAC;YAC3B,MAAMoO,OAAI,CAACrP,qBAAqB,CAAC,YAAY,EAAE,IAAI,CAAC;UACxD;UACA,CAACjD,EAAE,GAAGsS,OAAI,CAAChS,kBAAkB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmK,OAAO,CAACf,MAAM,CAAC;UACtF,OAAOA,MAAM;QACjB;QACA,CAACzE,EAAE,GAAG2N,OAAI,CAAChS,kBAAkB,MAAM,IAAI,IAAIqE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6N,MAAM,CAAC5P,KAAK,CAAC;QACpF,MAAMA,KAAK;MACf,CAAC,SACO;QACJ0P,OAAI,CAAChS,kBAAkB,GAAG,IAAI;QAC9BgS,OAAI,CAACtP,MAAM,CAAC0O,SAAS,EAAE,KAAK,CAAC;MACjC;IAAC;EACL;EACMzO,qBAAqBA,CAAAwP,IAAA,EAAAC,IAAA,EAAmC;IAAA,IAAAC,OAAA;IAAA,OAAAnT,iBAAA,YAAlCuD,KAAK,EAAEI,OAAO,EAAEyP,SAAS,GAAG,IAAI;MACxD,MAAMlB,SAAS,GAAG,0BAA0B3O,KAAK,GAAG;MACpD4P,OAAI,CAAC3P,MAAM,CAAC0O,SAAS,EAAE,OAAO,EAAEvO,OAAO,EAAE,eAAeyP,SAAS,EAAE,CAAC;MACpE,IAAI;QACA,IAAID,OAAI,CAACjS,gBAAgB,IAAIkS,SAAS,EAAE;UACpCD,OAAI,CAACjS,gBAAgB,CAACmS,WAAW,CAAC;YAAE9P,KAAK;YAAEI;UAAQ,CAAC,CAAC;QACzD;QACA,MAAM2P,MAAM,GAAG,EAAE;QACjB,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACN,OAAI,CAACzS,mBAAmB,CAACgT,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG;UAAA,IAAAC,MAAA,GAAA5T,iBAAA,CAAC,WAAO6T,CAAC,EAAK;YAC5E,IAAI;cACA,MAAMA,CAAC,CAAC3D,QAAQ,CAAC3M,KAAK,EAAEI,OAAO,CAAC;YACpC,CAAC,CACD,OAAOR,CAAC,EAAE;cACNmQ,MAAM,CAAC1I,IAAI,CAACzH,CAAC,CAAC;YAClB;UACJ,CAAC;UAAA,iBAAA2Q,IAAA;YAAA,OAAAF,MAAA,CAAA9T,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;QACF,MAAM2K,OAAO,CAACI,GAAG,CAACyI,QAAQ,CAAC;QAC3B,IAAID,MAAM,CAAC7I,MAAM,GAAG,CAAC,EAAE;UACnB,KAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAAC7I,MAAM,EAAEsJ,CAAC,IAAI,CAAC,EAAE;YACvC3S,OAAO,CAACgC,KAAK,CAACkQ,MAAM,CAACS,CAAC,CAAC,CAAC;UAC5B;UACA,MAAMT,MAAM,CAAC,CAAC,CAAC;QACnB;MACJ,CAAC,SACO;QACJH,OAAI,CAAC3P,MAAM,CAAC0O,SAAS,EAAE,KAAK,CAAC;MACjC;IAAC,GAAApS,KAAA,OAAAC,SAAA;EACL;EACA;AACJ;AACA;AACA;EACU6E,YAAYA,CAACjB,OAAO,EAAE;IAAA,IAAAqQ,OAAA;IAAA,OAAAhU,iBAAA;MACxBgU,OAAI,CAACxQ,MAAM,CAAC,iBAAiB,EAAEG,OAAO,CAAC;MACvC,MAAM1F,YAAY,CAAC+V,OAAI,CAAChR,OAAO,EAAEgR,OAAI,CAAChV,UAAU,EAAE2E,OAAO,CAAC;IAAC;EAC/D;EACMe,cAAcA,CAAA,EAAG;IAAA,IAAAuP,OAAA;IAAA,OAAAjU,iBAAA;MACnBiU,OAAI,CAACzQ,MAAM,CAAC,mBAAmB,CAAC;MAChC,MAAMzF,eAAe,CAACkW,OAAI,CAACjR,OAAO,EAAEiR,OAAI,CAACjV,UAAU,CAAC;IAAC;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkV,gCAAgCA,CAAA,EAAG;IAC/B,IAAI,CAAC1Q,MAAM,CAAC,qCAAqC,CAAC;IAClD,MAAM0M,QAAQ,GAAG,IAAI,CAACrP,yBAAyB;IAC/C,IAAI,CAACA,yBAAyB,GAAG,IAAI;IACrC,IAAI;MACA,IAAIqP,QAAQ,IAAIpS,SAAS,CAAC,CAAC,KAAKkQ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACmG,mBAAmB,CAAC,EAAE;QACzGnG,MAAM,CAACmG,mBAAmB,CAAC,kBAAkB,EAAEjE,QAAQ,CAAC;MAC5D;IACJ,CAAC,CACD,OAAO/M,CAAC,EAAE;MACN/B,OAAO,CAACgC,KAAK,CAAC,2CAA2C,EAAED,CAAC,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;EACUiR,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAArU,iBAAA;MACtB,MAAMqU,OAAI,CAACC,gBAAgB,CAAC,CAAC;MAC7BD,OAAI,CAAC7Q,MAAM,CAAC,sBAAsB,CAAC;MACnC,MAAM+Q,MAAM,GAAGC,WAAW,CAAC,MAAMH,OAAI,CAACI,qBAAqB,CAAC,CAAC,EAAElV,0BAA0B,CAAC;MAC1F8U,OAAI,CAACzT,iBAAiB,GAAG2T,MAAM;MAC/B,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACG,KAAK,KAAK,UAAU,EAAE;QAC5E;QACA;QACA;QACA;QACA;QACA;QACAH,MAAM,CAACG,KAAK,CAAC,CAAC;QACd;MACJ,CAAC,MACI,IAAI,OAAOC,IAAI,KAAK,WAAW,IAAI,OAAOA,IAAI,CAACC,UAAU,KAAK,UAAU,EAAE;QAC3E;QACA;QACA;QACAD,IAAI,CAACC,UAAU,CAACL,MAAM,CAAC;MAC3B;MACA;MACA;MACA;MACA1P,UAAU,cAAA7E,iBAAA,CAAC,aAAY;QACnB,MAAMqU,OAAI,CAACtT,iBAAiB;QAC5B,MAAMsT,OAAI,CAACI,qBAAqB,CAAC,CAAC;MACtC,CAAC,GAAE,CAAC,CAAC;IAAC;EACV;EACA;AACJ;AACA;AACA;EACUH,gBAAgBA,CAAA,EAAG;IAAA,IAAAO,OAAA;IAAA,OAAA7U,iBAAA;MACrB6U,OAAI,CAACrR,MAAM,CAAC,qBAAqB,CAAC;MAClC,MAAM+Q,MAAM,GAAGM,OAAI,CAACjU,iBAAiB;MACrCiU,OAAI,CAACjU,iBAAiB,GAAG,IAAI;MAC7B,IAAI2T,MAAM,EAAE;QACRO,aAAa,CAACP,MAAM,CAAC;MACzB;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUQ,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAhV,iBAAA;MACrBgV,OAAI,CAACd,gCAAgC,CAAC,CAAC;MACvC,MAAMc,OAAI,CAACZ,iBAAiB,CAAC,CAAC;IAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUa,eAAeA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAlV,iBAAA;MACpBkV,OAAI,CAAChB,gCAAgC,CAAC,CAAC;MACvC,MAAMgB,OAAI,CAACZ,gBAAgB,CAAC,CAAC;IAAC;EAClC;EACA;AACJ;AACA;EACUG,qBAAqBA,CAAA,EAAG;IAAA,IAAAU,OAAA;IAAA,OAAAnV,iBAAA;MAC1BmV,OAAI,CAAC3R,MAAM,CAAC,0BAA0B,EAAE,OAAO,CAAC;MAChD,IAAI;QACA,MAAM2R,OAAI,CAACjR,YAAY,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;UACnC,IAAI;YACA,MAAMyL,GAAG,GAAG1H,IAAI,CAAC0H,GAAG,CAAC,CAAC;YACtB,IAAI;cACA,aAAa0J,OAAI,CAACzL,WAAW;gBAAA,IAAA0L,MAAA,GAAApV,iBAAA,CAAC,WAAO4J,MAAM,EAAK;kBAC5C,MAAM;oBAAElG,IAAI,EAAE;sBAAEC;oBAAQ;kBAAG,CAAC,GAAGiG,MAAM;kBACrC,IAAI,CAACjG,OAAO,IAAI,CAACA,OAAO,CAACgI,aAAa,IAAI,CAAChI,OAAO,CAAC6H,UAAU,EAAE;oBAC3D2J,OAAI,CAAC3R,MAAM,CAAC,0BAA0B,EAAE,YAAY,CAAC;oBACrD;kBACJ;kBACA;kBACA,MAAM6R,cAAc,GAAGtG,IAAI,CAACuG,KAAK,CAAC,CAAC3R,OAAO,CAAC6H,UAAU,GAAG,IAAI,GAAGC,GAAG,IAAIlM,0BAA0B,CAAC;kBACjG4V,OAAI,CAAC3R,MAAM,CAAC,0BAA0B,EAAE,2BAA2B6R,cAAc,wBAAwB9V,0BAA0B,4BAA4BC,2BAA2B,QAAQ,CAAC;kBACnM,IAAI6V,cAAc,IAAI7V,2BAA2B,EAAE;oBAC/C,MAAM2V,OAAI,CAACzJ,iBAAiB,CAAC/H,OAAO,CAACgI,aAAa,CAAC;kBACvD;gBACJ,CAAC;gBAAA,iBAAA4J,IAAA;kBAAA,OAAAH,MAAA,CAAAtV,KAAA,OAAAC,SAAA;gBAAA;cAAA,IAAC;YACN,CAAC,CACD,OAAOoD,CAAC,EAAE;cACN/B,OAAO,CAACgC,KAAK,CAAC,wEAAwE,EAAED,CAAC,CAAC;YAC9F;UACJ,CAAC,SACO;YACJgS,OAAI,CAAC3R,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC;UAClD;QACJ,CAAC,EAAC;MACN,CAAC,CACD,OAAOL,CAAC,EAAE;QACN,IAAIA,CAAC,CAACqS,gBAAgB,IAAIrS,CAAC,YAAYtE,uBAAuB,EAAE;UAC5DsW,OAAI,CAAC3R,MAAM,CAAC,4CAA4C,CAAC;QAC7D,CAAC,MACI;UACD,MAAML,CAAC;QACX;MACJ;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACU4B,uBAAuBA,CAAA,EAAG;IAAA,IAAA0Q,OAAA;IAAA,OAAAzV,iBAAA;MAC5ByV,OAAI,CAACjS,MAAM,CAAC,4BAA4B,CAAC;MACzC,IAAI,CAAC1F,SAAS,CAAC,CAAC,IAAI,EAAEkQ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3K,gBAAgB,CAAC,EAAE;QAC5F,IAAIoS,OAAI,CAACxW,gBAAgB,EAAE;UACvB;UACAwW,OAAI,CAACV,gBAAgB,CAAC,CAAC;QAC3B;QACA,OAAO,KAAK;MAChB;MACA,IAAI;QACAU,OAAI,CAAC5U,yBAAyB,gBAAAb,iBAAA,CAAG;UAAA,aAAkByV,OAAI,CAACC,oBAAoB,CAAC,KAAK,CAAC;QAAA;QACnF1H,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3K,gBAAgB,CAAC,kBAAkB,EAAEoS,OAAI,CAAC5U,yBAAyB,CAAC;QAC3H;QACA;QACA,MAAM4U,OAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3C,CAAC,CACD,OAAOtS,KAAK,EAAE;QACVhC,OAAO,CAACgC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD;IAAC;EACL;EACA;AACJ;AACA;EACUsS,oBAAoBA,CAACC,oBAAoB,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAA5V,iBAAA;MAC7C,MAAM6V,UAAU,GAAG,yBAAyBF,oBAAoB,GAAG;MACnEC,OAAI,CAACpS,MAAM,CAACqS,UAAU,EAAE,iBAAiB,EAAEC,QAAQ,CAACC,eAAe,CAAC;MACpE,IAAID,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;QACxC,IAAIH,OAAI,CAAC3W,gBAAgB,EAAE;UACvB;UACA;UACA2W,OAAI,CAACxB,iBAAiB,CAAC,CAAC;QAC5B;QACA,IAAI,CAACuB,oBAAoB,EAAE;UACvB;UACA;UACA;UACA;UACA,MAAMC,OAAI,CAAC7U,iBAAiB;UAC5B,MAAM6U,OAAI,CAAC1R,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;YACpC,IAAI8V,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;cACxCH,OAAI,CAACpS,MAAM,CAACqS,UAAU,EAAE,0GAA0G,CAAC;cACnI;cACA;YACJ;YACA;YACA,MAAMD,OAAI,CAAC9Q,kBAAkB,CAAC,CAAC;UACnC,CAAC,EAAC;QACN;MACJ,CAAC,MACI,IAAIgR,QAAQ,CAACC,eAAe,KAAK,QAAQ,EAAE;QAC5C,IAAIH,OAAI,CAAC3W,gBAAgB,EAAE;UACvB2W,OAAI,CAACtB,gBAAgB,CAAC,CAAC;QAC3B;MACJ;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACU/C,kBAAkBA,CAACxS,GAAG,EAAEgI,QAAQ,EAAEzG,OAAO,EAAE;IAAA,IAAA0V,OAAA;IAAA,OAAAhW,iBAAA;MAC7C,MAAMiW,SAAS,GAAG,CAAC,YAAYC,kBAAkB,CAACnP,QAAQ,CAAC,EAAE,CAAC;MAC9D,IAAIzG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACqF,UAAU,EAAE;QACtEsQ,SAAS,CAACrL,IAAI,CAAC,eAAesL,kBAAkB,CAAC5V,OAAO,CAACqF,UAAU,CAAC,EAAE,CAAC;MAC3E;MACA,IAAIrF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0G,MAAM,EAAE;QAClEiP,SAAS,CAACrL,IAAI,CAAC,UAAUsL,kBAAkB,CAAC5V,OAAO,CAAC0G,MAAM,CAAC,EAAE,CAAC;MAClE;MACA,IAAIgP,OAAI,CAAC3W,QAAQ,KAAK,MAAM,EAAE;QAC1B,MAAMqG,YAAY,GAAGrH,oBAAoB,CAAC,CAAC;QAC3C,MAAMJ,YAAY,CAAC+X,OAAI,CAAChT,OAAO,EAAE,GAAGgT,OAAI,CAAChX,UAAU,gBAAgB,EAAE0G,YAAY,CAAC;QAClF,MAAMF,aAAa,SAASlH,qBAAqB,CAACoH,YAAY,CAAC;QAC/D,MAAMD,mBAAmB,GAAGC,YAAY,KAAKF,aAAa,GAAG,OAAO,GAAG,MAAM;QAC7EwQ,OAAI,CAACxS,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,GAAGkC,YAAY,CAACyM,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE3M,aAAa,EAAE,QAAQ,EAAEC,mBAAmB,CAAC;QAC1I,MAAM0Q,UAAU,GAAG,IAAIC,eAAe,CAAC;UACnCnQ,cAAc,EAAE,GAAGiQ,kBAAkB,CAAC1Q,aAAa,CAAC,EAAE;UACtDU,qBAAqB,EAAE,GAAGgQ,kBAAkB,CAACzQ,mBAAmB,CAAC;QACrE,CAAC,CAAC;QACFwQ,SAAS,CAACrL,IAAI,CAACuL,UAAU,CAACzH,QAAQ,CAAC,CAAC,CAAC;MACzC;MACA,IAAIpO,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2G,WAAW,EAAE;QACvE,MAAMoP,KAAK,GAAG,IAAID,eAAe,CAAC9V,OAAO,CAAC2G,WAAW,CAAC;QACtDgP,SAAS,CAACrL,IAAI,CAACyL,KAAK,CAAC3H,QAAQ,CAAC,CAAC,CAAC;MACpC;MACA,IAAIpO,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC4G,mBAAmB,EAAE;QAC/E+O,SAAS,CAACrL,IAAI,CAAC,sBAAsBtK,OAAO,CAAC4G,mBAAmB,EAAE,CAAC;MACvE;MACA,OAAO,GAAGnI,GAAG,IAAIkX,SAAS,CAACK,IAAI,CAAC,GAAG,CAAC,EAAE;IAAC;EAC3C;EACM/T,SAASA,CAACmG,MAAM,EAAE;IAAA,IAAA6N,OAAA;IAAA,OAAAvW,iBAAA;MACpB,IAAI;QACA,aAAauW,OAAI,CAAC7M,WAAW;UAAA,IAAA8M,MAAA,GAAAxW,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE;YACN,MAAM;cAAEkD,IAAI,EAAEgJ,WAAW;cAAEtJ,KAAK,EAAEyG;YAAa,CAAC,GAAGD,MAAM;YACzD,IAAIC,YAAY,EAAE;cACd,OAAO;gBAAEnG,IAAI,EAAE,IAAI;gBAAEN,KAAK,EAAEyG;cAAa,CAAC;YAC9C;YACA,aAAavM,QAAQ,CAACiZ,OAAI,CAACzU,KAAK,EAAE,QAAQ,EAAE,GAAGyU,OAAI,CAACxX,GAAG,YAAY2J,MAAM,CAAC+N,QAAQ,EAAE,EAAE;cAClFrX,OAAO,EAAEmX,OAAI,CAACnX,OAAO;cACrB0K,GAAG,EAAE,CAACtJ,EAAE,GAAGkM,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/I,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH;YACtI,CAAC,CAAC;UACN,CAAC;UAAA,iBAAA2O,IAAA;YAAA,OAAAF,MAAA,CAAA1W,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACUf,OAAOA,CAACqG,MAAM,EAAE;IAAA,IAAAiO,OAAA;IAAA,OAAA3W,iBAAA;MAClB,IAAI;QACA,aAAa2W,OAAI,CAACjN,WAAW;UAAA,IAAAkN,MAAA,GAAA5W,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE,EAAE2E,EAAE;YACV,MAAM;cAAEzB,IAAI,EAAEgJ,WAAW;cAAEtJ,KAAK,EAAEyG;YAAa,CAAC,GAAGD,MAAM;YACzD,IAAIC,YAAY,EAAE;cACd,OAAO;gBAAEnG,IAAI,EAAE,IAAI;gBAAEN,KAAK,EAAEyG;cAAa,CAAC;YAC9C;YACA,MAAM;cAAEnG,IAAI;cAAEN;YAAM,CAAC,SAAS9F,QAAQ,CAACqZ,OAAI,CAAC7U,KAAK,EAAE,MAAM,EAAE,GAAG6U,OAAI,CAAC5X,GAAG,UAAU,EAAE;cAC9E8G,IAAI,EAAE;gBACFgR,aAAa,EAAEnO,MAAM,CAACoO,YAAY;gBAClCC,WAAW,EAAErO,MAAM,CAACsO,UAAU;gBAC9BC,MAAM,EAAEvO,MAAM,CAACuO;cACnB,CAAC;cACD7X,OAAO,EAAEuX,OAAI,CAACvX,OAAO;cACrB0K,GAAG,EAAE,CAACtJ,EAAE,GAAGkM,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/I,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH;YACtI,CAAC,CAAC;YACF,IAAI3E,KAAK,EAAE;cACP,OAAO;gBAAEM,IAAI,EAAE,IAAI;gBAAEN;cAAM,CAAC;YAChC;YACA,IAAI,CAAC+B,EAAE,GAAGzB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACwT,IAAI,MAAM,IAAI,IAAI/R,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgS,OAAO,EAAE;cAC9GzT,IAAI,CAACwT,IAAI,CAACC,OAAO,GAAG,4BAA4BzT,IAAI,CAACwT,IAAI,CAACC,OAAO,EAAE;YACvE;YACA,OAAO;cAAEzT,IAAI;cAAEN,KAAK,EAAE;YAAK,CAAC;UAChC,CAAC;UAAA,iBAAAgU,IAAA;YAAA,OAAAR,MAAA,CAAA9W,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,CACD,OAAOqD,KAAK,EAAE;QACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;UACpB,OAAO;YAAEM,IAAI,EAAE,IAAI;YAAEN;UAAM,CAAC;QAChC;QACA,MAAMA,KAAK;MACf;IAAC;EACL;EACA;AACJ;AACA;EACUlB,OAAOA,CAACwG,MAAM,EAAE;IAAA,IAAA2O,OAAA;IAAA,OAAArX,iBAAA;MAClB,OAAOqX,OAAI,CAACnT,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,IAAI;UACA,aAAaqX,OAAI,CAAC3N,WAAW;YAAA,IAAA4N,MAAA,GAAAtX,iBAAA,CAAC,WAAO4J,MAAM,EAAK;cAC5C,IAAIpJ,EAAE;cACN,MAAM;gBAAEkD,IAAI,EAAEgJ,WAAW;gBAAEtJ,KAAK,EAAEyG;cAAa,CAAC,GAAGD,MAAM;cACzD,IAAIC,YAAY,EAAE;gBACd,OAAO;kBAAEnG,IAAI,EAAE,IAAI;kBAAEN,KAAK,EAAEyG;gBAAa,CAAC;cAC9C;cACA,MAAM;gBAAEnG,IAAI;gBAAEN;cAAM,CAAC,SAAS9F,QAAQ,CAAC+Z,OAAI,CAACvV,KAAK,EAAE,MAAM,EAAE,GAAGuV,OAAI,CAACtY,GAAG,YAAY2J,MAAM,CAAC+N,QAAQ,SAAS,EAAE;gBACxG5Q,IAAI,EAAE;kBAAEsI,IAAI,EAAEzF,MAAM,CAACyF,IAAI;kBAAEoJ,YAAY,EAAE7O,MAAM,CAAC8O;gBAAY,CAAC;gBAC7DpY,OAAO,EAAEiY,OAAI,CAACjY,OAAO;gBACrB0K,GAAG,EAAE,CAACtJ,EAAE,GAAGkM,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/I,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH;cACtI,CAAC,CAAC;cACF,IAAI3E,KAAK,EAAE;gBACP,OAAO;kBAAEM,IAAI,EAAE,IAAI;kBAAEN;gBAAM,CAAC;cAChC;cACA,MAAMiU,OAAI,CAACzS,YAAY,CAAClD,MAAM,CAACC,MAAM,CAAC;gBAAE6J,UAAU,EAAEuD,IAAI,CAACC,KAAK,CAACjL,IAAI,CAAC0H,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG/H,IAAI,CAAC8J;cAAW,CAAC,EAAE9J,IAAI,CAAC,CAAC;cAC7G,MAAM2T,OAAI,CAAC5T,qBAAqB,CAAC,wBAAwB,EAAEC,IAAI,CAAC;cAChE,OAAO;gBAAEA,IAAI;gBAAEN;cAAM,CAAC;YAC1B,CAAC;YAAA,iBAAAqU,IAAA;cAAA,OAAAH,MAAA,CAAAxX,KAAA,OAAAC,SAAA;YAAA;UAAA,IAAC;QACN,CAAC,CACD,OAAOqD,KAAK,EAAE;UACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;YACpB,OAAO;cAAEM,IAAI,EAAE,IAAI;cAAEN;YAAM,CAAC;UAChC;UACA,MAAMA,KAAK;QACf;MACJ,CAAC,EAAC;IAAC;EACP;EACA;AACJ;AACA;EACUX,UAAUA,CAACiG,MAAM,EAAE;IAAA,IAAAgP,OAAA;IAAA,OAAA1X,iBAAA;MACrB,OAAO0X,OAAI,CAACxT,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,IAAI;UACA,aAAa0X,OAAI,CAAChO,WAAW;YAAA,IAAAiO,MAAA,GAAA3X,iBAAA,CAAC,WAAO4J,MAAM,EAAK;cAC5C,IAAIpJ,EAAE;cACN,MAAM;gBAAEkD,IAAI,EAAEgJ,WAAW;gBAAEtJ,KAAK,EAAEyG;cAAa,CAAC,GAAGD,MAAM;cACzD,IAAIC,YAAY,EAAE;gBACd,OAAO;kBAAEnG,IAAI,EAAE,IAAI;kBAAEN,KAAK,EAAEyG;gBAAa,CAAC;cAC9C;cACA,aAAavM,QAAQ,CAACoa,OAAI,CAAC5V,KAAK,EAAE,MAAM,EAAE,GAAG4V,OAAI,CAAC3Y,GAAG,YAAY2J,MAAM,CAAC+N,QAAQ,YAAY,EAAE;gBAC1FrX,OAAO,EAAEsY,OAAI,CAACtY,OAAO;gBACrB0K,GAAG,EAAE,CAACtJ,EAAE,GAAGkM,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/I,OAAO,MAAM,IAAI,IAAInD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuH;cACtI,CAAC,CAAC;YACN,CAAC;YAAA,iBAAA6P,IAAA;cAAA,OAAAD,MAAA,CAAA7X,KAAA,OAAAC,SAAA;YAAA;UAAA,IAAC;QACN,CAAC,CACD,OAAOqD,KAAK,EAAE;UACV,IAAIhG,WAAW,CAACgG,KAAK,CAAC,EAAE;YACpB,OAAO;cAAEM,IAAI,EAAE,IAAI;cAAEN;YAAM,CAAC;UAChC;UACA,MAAMA,KAAK;QACf;MACJ,CAAC,EAAC;IAAC;EACP;EACA;AACJ;AACA;EACUP,mBAAmBA,CAAC6F,MAAM,EAAE;IAAA,IAAAmP,OAAA;IAAA,OAAA7X,iBAAA;MAC9B;MACA;MACA,MAAM;QAAE0D,IAAI,EAAEoU,aAAa;QAAE1U,KAAK,EAAE2U;MAAe,CAAC,SAASF,OAAI,CAACpV,UAAU,CAAC;QACzEgU,QAAQ,EAAE/N,MAAM,CAAC+N;MACrB,CAAC,CAAC;MACF,IAAIsB,cAAc,EAAE;QAChB,OAAO;UAAErU,IAAI,EAAE,IAAI;UAAEN,KAAK,EAAE2U;QAAe,CAAC;MAChD;MACA,aAAaF,OAAI,CAAC3V,OAAO,CAAC;QACtBuU,QAAQ,EAAE/N,MAAM,CAAC+N,QAAQ;QACzBe,WAAW,EAAEM,aAAa,CAAC1H,EAAE;QAC7BjC,IAAI,EAAEzF,MAAM,CAACyF;MACjB,CAAC,CAAC;IAAC;EACP;EACA;AACJ;AACA;EACUxL,YAAYA,CAAA,EAAG;IAAA,IAAAqV,OAAA;IAAA,OAAAhY,iBAAA;MACjB;MACA,MAAM;QAAE0D,IAAI,EAAE;UAAE4C;QAAK,CAAC;QAAElD,KAAK,EAAEuJ;MAAW,CAAC,SAASqL,OAAI,CAACpM,OAAO,CAAC,CAAC;MAClE,IAAIe,SAAS,EAAE;QACX,OAAO;UAAEjJ,IAAI,EAAE,IAAI;UAAEN,KAAK,EAAEuJ;QAAU,CAAC;MAC3C;MACA,MAAMsL,OAAO,GAAG,CAAC3R,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC2R,OAAO,KAAK,EAAE;MAChF,MAAMf,IAAI,GAAGe,OAAO,CAACC,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACpB,WAAW,KAAK,MAAM,IAAIoB,MAAM,CAACpI,MAAM,KAAK,UAAU,CAAC;MACtG,OAAO;QACHrM,IAAI,EAAE;UACFoH,GAAG,EAAEmN,OAAO;UACZf;QACJ,CAAC;QACD9T,KAAK,EAAE;MACX,CAAC;IAAC;EACN;EACA;AACJ;AACA;EACUL,+BAA+BA,CAAA,EAAG;IAAA,IAAAqV,OAAA;IAAA,OAAApY,iBAAA;MACpC,OAAOoY,OAAI,CAAClU,YAAY,CAAC,CAAC,CAAC,eAAAlE,iBAAA,CAAE,aAAY;QACrC,aAAaoY,OAAI,CAAC1O,WAAW;UAAA,IAAA2O,MAAA,GAAArY,iBAAA,CAAC,WAAO4J,MAAM,EAAK;YAC5C,IAAIpJ,EAAE,EAAE2E,EAAE;YACV,MAAM;cAAEzB,IAAI,EAAE;gBAAEC;cAAQ,CAAC;cAAEP,KAAK,EAAEyG;YAAc,CAAC,GAAGD,MAAM;YAC1D,IAAIC,YAAY,EAAE;cACd,OAAO;gBAAEnG,IAAI,EAAE,IAAI;gBAAEN,KAAK,EAAEyG;cAAa,CAAC;YAC9C;YACA,IAAI,CAAClG,OAAO,EAAE;cACV,OAAO;gBACHD,IAAI,EAAE;kBAAE4U,YAAY,EAAE,IAAI;kBAAEC,SAAS,EAAE,IAAI;kBAAEC,4BAA4B,EAAE;gBAAG,CAAC;gBAC/EpV,KAAK,EAAE;cACX,CAAC;YACL;YACA,MAAMgK,OAAO,GAAGgL,OAAI,CAACvL,UAAU,CAAClJ,OAAO,CAACoE,YAAY,CAAC;YACrD,IAAIuQ,YAAY,GAAG,IAAI;YACvB,IAAIlL,OAAO,CAACqL,GAAG,EAAE;cACbH,YAAY,GAAGlL,OAAO,CAACqL,GAAG;YAC9B;YACA,IAAIF,SAAS,GAAGD,YAAY;YAC5B,MAAMI,eAAe,GAAG,CAACvT,EAAE,GAAG,CAAC3E,EAAE,GAAGmD,OAAO,CAAC2C,IAAI,CAAC2R,OAAO,MAAM,IAAI,IAAIzX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0X,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAACpI,MAAM,KAAK,UAAU,CAAC,MAAM,IAAI,IAAI5K,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;YACvL,IAAIuT,eAAe,CAACjO,MAAM,GAAG,CAAC,EAAE;cAC5B8N,SAAS,GAAG,MAAM;YACtB;YACA,MAAMC,4BAA4B,GAAGpL,OAAO,CAACuL,GAAG,IAAI,EAAE;YACtD,OAAO;cAAEjV,IAAI,EAAE;gBAAE4U,YAAY;gBAAEC,SAAS;gBAAEC;cAA6B,CAAC;cAAEpV,KAAK,EAAE;YAAK,CAAC;UAC3F,CAAC;UAAA,iBAAAwV,IAAA;YAAA,OAAAP,MAAA,CAAAvY,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACN,CAAC,EAAC;IAAC;EACP;AACJ;AACAK,YAAY,CAACmB,cAAc,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}