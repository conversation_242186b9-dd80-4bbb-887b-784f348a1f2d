import { User } from './user.model';

export type RideStatus = 'requested' | 'assigned' | 'in-progress' | 'completed' | 'canceled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded' | 'completed';

export interface Ride {
  id: string;
  rider_id: string;
  driver_id?: string;
  pickup_location: string;
  dropoff_location: string;
  pickup_time: string;
  status: RideStatus;
  fare?: number;
  passengers?: number;
  notes?: string;
  payment_status?: PaymentStatus;
  payment_id?: string;
  amount?: number;
  // Location coordinates
  pickup_latitude?: number;
  pickup_longitude?: number;
  dropoff_latitude?: number;
  dropoff_longitude?: number;
  // Route information
  distance_miles?: number;
  duration_minutes?: number;
  created_at: string;
  updated_at: string;
}

export interface RideFilter {
  status?: RideStatus;
  riderId?: string;
  driverId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}
