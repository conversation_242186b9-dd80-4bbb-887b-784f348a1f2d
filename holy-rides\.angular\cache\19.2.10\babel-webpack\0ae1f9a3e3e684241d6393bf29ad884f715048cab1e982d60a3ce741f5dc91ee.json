{"ast": null, "code": "export class AuthError extends Error {\n  constructor(message, status) {\n    super(message);\n    this.__isAuthError = true;\n    this.name = 'AuthError';\n    this.status = status;\n  }\n}\nexport function isAuthError(error) {\n  return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n  constructor(message, status) {\n    super(message, status);\n    this.name = 'AuthApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport function isAuthApiError(error) {\n  return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'AuthUnknownError';\n    this.originalError = originalError;\n  }\n}\nexport class CustomAuthError extends AuthError {\n  constructor(message, name, status) {\n    super(message);\n    this.name = name;\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n  constructor() {\n    super('Auth session missing!', 'AuthSessionMissingError', 400);\n  }\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n  constructor() {\n    super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500);\n  }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n  constructor(message) {\n    super(message, 'AuthInvalidCredentialsError', 400);\n  }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthImplicitGrantRedirectError', 500);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n  constructor(message, details = null) {\n    super(message, 'AuthPKCEGrantCodeExchangeError', 500);\n    this.details = null;\n    this.details = details;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status,\n      details: this.details\n    };\n  }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n  constructor(message, status) {\n    super(message, 'AuthRetryableFetchError', status);\n  }\n}\nexport function isAuthRetryableFetchError(error) {\n  return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n  constructor(message, status, reasons) {\n    super(message, 'AuthWeakPasswordError', status);\n    this.reasons = reasons;\n  }\n}\nexport function isAuthWeakPasswordError(error) {\n  return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "Error", "constructor", "message", "status", "__is<PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "isAuthError", "error", "AuthApiError", "toJSON", "isAuthApiError", "AuthUnknownError", "originalError", "CustomAuthError", "AuthSessionMissingError", "AuthInvalidTokenResponseError", "AuthInvalidCredentialsError", "AuthImplicitGrantRedirectError", "details", "AuthPKCEGrantCodeExchangeError", "AuthRetryableFetchError", "isAuthRetryableFetchError", "AuthWeakPasswordError", "reasons", "isAuthWeakPasswordError"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/errors.js"], "sourcesContent": ["export class AuthError extends Error {\n    constructor(message, status) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n    }\n}\nexport function isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nexport class AuthApiError extends AuthError {\n    constructor(message, status) {\n        super(message, status);\n        this.name = 'AuthApiError';\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n        };\n    }\n}\nexport function isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nexport class AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nexport class CustomAuthError extends AuthError {\n    constructor(message, name, status) {\n        super(message);\n        this.name = name;\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n        };\n    }\n}\nexport class AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400);\n    }\n}\nexport class AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500);\n    }\n}\nexport class AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400);\n    }\n}\nexport class AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nexport class AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nexport class AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status);\n    }\n}\nexport function isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nexport class AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status);\n        this.reasons = reasons;\n    }\n}\nexport function isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\n"], "mappings": "AAAA,OAAO,MAAMA,SAAS,SAASC,KAAK,CAAC;EACjCC,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,KAAK,CAACD,OAAO,CAAC;IACd,IAAI,CAACE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA,OAAO,SAASG,WAAWA,CAACC,KAAK,EAAE;EAC/B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,eAAe,IAAIA,KAAK;AAClF;AACA,OAAO,MAAMC,YAAY,SAAST,SAAS,CAAC;EACxCE,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC;IACtB,IAAI,CAACE,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACAM,MAAMA,CAAA,EAAG;IACL,OAAO;MACHJ,IAAI,EAAE,IAAI,CAACA,IAAI;MACfH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AACA,OAAO,SAASO,cAAcA,CAACH,KAAK,EAAE;EAClC,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,cAAc;AAC9D;AACA,OAAO,MAAMM,gBAAgB,SAASZ,SAAS,CAAC;EAC5CE,WAAWA,CAACC,OAAO,EAAEU,aAAa,EAAE;IAChC,KAAK,CAACV,OAAO,CAAC;IACd,IAAI,CAACG,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACO,aAAa,GAAGA,aAAa;EACtC;AACJ;AACA,OAAO,MAAMC,eAAe,SAASd,SAAS,CAAC;EAC3CE,WAAWA,CAACC,OAAO,EAAEG,IAAI,EAAEF,MAAM,EAAE;IAC/B,KAAK,CAACD,OAAO,CAAC;IACd,IAAI,CAACG,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACF,MAAM,GAAGA,MAAM;EACxB;EACAM,MAAMA,CAAA,EAAG;IACL,OAAO;MACHJ,IAAI,EAAE,IAAI,CAACA,IAAI;MACfH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AACA,OAAO,MAAMW,uBAAuB,SAASD,eAAe,CAAC;EACzDZ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,uBAAuB,EAAE,yBAAyB,EAAE,GAAG,CAAC;EAClE;AACJ;AACA,OAAO,MAAMc,6BAA6B,SAASF,eAAe,CAAC;EAC/DZ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,8BAA8B,EAAE,+BAA+B,EAAE,GAAG,CAAC;EAC/E;AACJ;AACA,OAAO,MAAMe,2BAA2B,SAASH,eAAe,CAAC;EAC7DZ,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,EAAE,6BAA6B,EAAE,GAAG,CAAC;EACtD;AACJ;AACA,OAAO,MAAMe,8BAA8B,SAASJ,eAAe,CAAC;EAChEZ,WAAWA,CAACC,OAAO,EAAEgB,OAAO,GAAG,IAAI,EAAE;IACjC,KAAK,CAAChB,OAAO,EAAE,gCAAgC,EAAE,GAAG,CAAC;IACrD,IAAI,CAACgB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAT,MAAMA,CAAA,EAAG;IACL,OAAO;MACHJ,IAAI,EAAE,IAAI,CAACA,IAAI;MACfH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBe,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC;EACL;AACJ;AACA,OAAO,MAAMC,8BAA8B,SAASN,eAAe,CAAC;EAChEZ,WAAWA,CAACC,OAAO,EAAEgB,OAAO,GAAG,IAAI,EAAE;IACjC,KAAK,CAAChB,OAAO,EAAE,gCAAgC,EAAE,GAAG,CAAC;IACrD,IAAI,CAACgB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAT,MAAMA,CAAA,EAAG;IACL,OAAO;MACHJ,IAAI,EAAE,IAAI,CAACA,IAAI;MACfH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBe,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC;EACL;AACJ;AACA,OAAO,MAAME,uBAAuB,SAASP,eAAe,CAAC;EACzDZ,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;IACzB,KAAK,CAACD,OAAO,EAAE,yBAAyB,EAAEC,MAAM,CAAC;EACrD;AACJ;AACA,OAAO,SAASkB,yBAAyBA,CAACd,KAAK,EAAE;EAC7C,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,yBAAyB;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMiB,qBAAqB,SAAST,eAAe,CAAC;EACvDZ,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAEoB,OAAO,EAAE;IAClC,KAAK,CAACrB,OAAO,EAAE,uBAAuB,EAAEC,MAAM,CAAC;IAC/C,IAAI,CAACoB,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA,OAAO,SAASC,uBAAuBA,CAACjB,KAAK,EAAE;EAC3C,OAAOD,WAAW,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACF,IAAI,KAAK,uBAAuB;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}