import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root'
})
export class UpdateService {
  private registration: ServiceWorkerRegistration | null = null;

  constructor(
    private snackBar: MatSnackBar
  ) {
    this.init();
  }

  /**
   * Initialize the update service
   */
  private async init(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        // Get the service worker registration
        const registration = await navigator.serviceWorker.getRegistration();

        // Explicitly handle the undefined case
        this.registration = registration || null;

        if (this.registration) {
          // Listen for controlling service worker changes
          navigator.serviceWorker.addEventListener('controllerchange', () => {
            // When the service worker controlling the page changes, show update notification
            this.promptUpdate();
          });
        }
      } catch (error) {
        console.error('Error getting service worker registration', error);
      }
    }
  }

  /**
   * Check for service worker updates
   */
  checkForUpdates(): void {
    if (this.registration) {
      this.registration.update()
        .then(() => console.log('Service worker update check completed'))
        .catch(error => console.error('Service worker update check failed', error));
    } else if ('serviceWorker' in navigator) {
      // Try to get the registration again if it's not available
      navigator.serviceWorker.getRegistration()
        .then((reg): Promise<void> => {
          // Explicitly handle the undefined case
          this.registration = reg || null;

          if (reg) {
            return reg.update();
          }
          return Promise.resolve();
        })
        .then(() => {
          if (this.registration) {
            console.log('Service worker update check completed after registration');
          }
        })
        .catch(error => console.error('Service worker registration or update failed', error));
    }
  }

  /**
   * Prompt the user to update the application
   */
  private promptUpdate(): void {
    const snackBarRef = this.snackBar.open(
      'A new version of the app is available',
      'Update now',
      { duration: 0 }
    );

    snackBarRef.onAction().subscribe(() => {
      document.location.reload();
    });
  }
}
