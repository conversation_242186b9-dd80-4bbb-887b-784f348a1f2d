-- Create ratings table
CREATE TABLE IF NOT EXISTS ratings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ride_id UUID REFERENCES rides(id) NOT NULL,
  rater_id UUID REFERENCES profiles(id) NOT NULL,
  rated_id UUID REFERENCES profiles(id) NOT NULL,
  rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
  feedback TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  -- Ensure a user can only rate once per ride
  UNIQUE(ride_id, rater_id, rated_id)
);

-- Set up Row Level Security (RLS)
ALTER TABLE ratings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view ratings they've given or received."
  ON ratings FOR SELECT
  USING (auth.uid() = rater_id OR auth.uid() = rated_id);

CREATE POLICY "Users can create ratings for rides they participated in."
  ON ratings FOR INSERT
  WITH CHECK (
    auth.uid() = rater_id AND
    (
      -- Rater is either the rider or driver of the ride
      auth.uid() IN (
        SELECT rider_id FROM rides WHERE id = ride_id
        UNION
        SELECT driver_id FROM rides WHERE id = ride_id
      ) AND
      -- Rated user is either the rider or driver of the ride (but not the rater)
      rated_id IN (
        SELECT rider_id FROM rides WHERE id = ride_id
        UNION
        SELECT driver_id FROM rides WHERE id = ride_id
      ) AND
      rated_id != auth.uid() AND
      -- Ride must be completed
      (SELECT status FROM rides WHERE id = ride_id) = 'completed'
    )
  );

-- Create an updated_at trigger
CREATE OR REPLACE FUNCTION update_ratings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER ratings_updated_at
  BEFORE UPDATE ON ratings
  FOR EACH ROW
  EXECUTE PROCEDURE update_ratings_updated_at();

-- Create function to get average rating for a user
CREATE OR REPLACE FUNCTION get_user_average_rating(user_id UUID)
RETURNS DECIMAL AS $$
DECLARE
  avg_rating DECIMAL;
BEGIN
  SELECT AVG(rating) INTO avg_rating
  FROM ratings
  WHERE rated_id = user_id;
  
  RETURN avg_rating;
END;
$$ LANGUAGE plpgsql;
