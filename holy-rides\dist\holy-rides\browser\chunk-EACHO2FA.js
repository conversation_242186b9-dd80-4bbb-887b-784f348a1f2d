import{J as h}from"./chunk-AG3SD6JT.js";import{Z as d,ca as g,g as o}from"./chunk-ST4QC4E3.js";import{i as a}from"./chunk-ODN5LVDJ.js";var u=class n{constructor(r){this.authService=r;this.supabase=r.supabase}supabase;messagesSubject=new o([]);messages$=this.messagesSubject.asObservable();threadsSubject=new o([]);threads$=this.threadsSubject.asObservable();getOrCreateThreadForRide(r){return a(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("message_threads").select("*").eq("ride_id",r).limit(1);if(t)throw t;if(e&&e.length>0)return e[0];let{data:s,error:i}=yield this.supabase.from("message_threads").insert([{ride_id:r}]).select().single();if(i)throw i;return s}catch(e){throw console.error("Error getting or creating thread:",e),e}})}getThreadMessages(r){return a(this,null,function*(){try{let{data:e,error:t}=yield this.supabase.from("messages").select("*").eq("thread_id",r).order("created_at",{ascending:!0});if(t)throw t;return this.messagesSubject.next(e||[]),e||[]}catch(e){return console.error("Error fetching thread messages:",e),[]}})}getUserThreads(){return a(this,null,function*(){try{let r=yield this.authService.getCurrentUser();if(!r)throw new Error("User not authenticated");let{data:e,error:t}=yield this.supabase.from("message_threads").select(`
          *,
          rides!inner(rider_id, driver_id)
        `).or(`rides.rider_id.eq.${r.id},rides.driver_id.eq.${r.id}`).order("updated_at",{ascending:!1});if(t)throw t;return this.threadsSubject.next(e||[]),e||[]}catch(r){return console.error("Error fetching user threads:",r),[]}})}sendMessage(r,e,t){return a(this,null,function*(){try{let s=yield this.authService.getCurrentUser();if(!s)throw new Error("User not authenticated");let{data:i,error:c}=yield this.supabase.from("messages").insert([{thread_id:r,sender_id:s.id,receiver_id:e,content:t,is_read:!1}]).select().single();if(c)throw c;let m=this.messagesSubject.value;return this.messagesSubject.next([...m,i]),i}catch(s){throw console.error("Error sending message:",s),s}})}markMessagesAsRead(r){return a(this,null,function*(){try{let e=yield this.authService.getCurrentUser();if(!e)throw new Error("User not authenticated");let{error:t}=yield this.supabase.from("messages").update({is_read:!0}).eq("thread_id",r).eq("receiver_id",e.id).eq("is_read",!1);if(t)throw t;yield this.getThreadMessages(r)}catch(e){console.error("Error marking messages as read:",e)}})}getUnreadMessageCount(){return a(this,null,function*(){try{let r=yield this.authService.getCurrentUser();if(!r)return 0;let{data:e,error:t}=yield this.supabase.from("messages").select("id",{count:"exact"}).eq("receiver_id",r.id).eq("is_read",!1);if(t)throw t;return e?.length||0}catch(r){return console.error("Error getting unread message count:",r),0}})}static \u0275fac=function(e){return new(e||n)(g(h))};static \u0275prov=d({token:n,factory:n.\u0275fac,providedIn:"root"})};export{u as a};
