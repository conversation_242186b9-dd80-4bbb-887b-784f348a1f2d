{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { createClient } from '@supabase/supabase-js';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nlet AuthService = class AuthService {\n  _supabase;\n  userSubject = new BehaviorSubject(null);\n  user$ = this.userSubject.asObservable();\n  MAX_RETRIES = 1; // Increase from 3\n  RETRY_DELAY = 10000; // Increase from 1000\n  // Expose the Supabase client as a getter to be used by other services\n  get supabase() {\n    return this._supabase;\n  }\n  constructor() {\n    // Create a custom lock function with a longer timeout\n    // const customLock = (name: string, acquireTimeout: number, fn: () => Promise<any>) => {\n    //   // Increase the timeout to 5000ms (5 seconds) to give more time for lock acquisition\n    //   const adjustedTimeout = Math.max(acquireTimeout, 10000);\n    //   console.log(`Attempting to acquire lock \"${name}\" with timeout ${adjustedTimeout}ms`);\n    //   return navigatorLock(name, adjustedTimeout, fn);\n    // };\n    this._supabase = createClient(environment.supabaseUrl, environment.supabaseKey, {\n      auth: {\n        storageKey: 'holy-rides-auth',\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true,\n        flowType: 'pkce',\n        debug: false // Set to false to reduce console noise\n        //   lock: customLock\n      }\n    });\n    // Initialize auth with a longer delay to avoid lock conflicts\n    this.initializeAuth();\n    setTimeout(() => {}, 2500);\n  }\n  initializeAuth() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield new Promise(resolve => setTimeout(resolve, 1000));\n        // Get initial session\n        const {\n          data: {\n            session\n          }\n        } = yield _this.supabase.auth.getSession();\n        if (session?.user) {\n          try {\n            // Convert Supabase user to our app user model\n            const appUser = yield _this.convertToAppUser(session.user);\n            // Set the user in the subject\n            _this.userSubject.next(appUser);\n            // Then check the role in the background\n            const role = yield _this.getUserRole();\n            if (!role) {\n              console.warn('User has no role, setting auth state to null');\n              _this.userSubject.next(null);\n            }\n          } catch (error) {\n            console.error('Error during user conversion in init:', error);\n            _this.userSubject.next(null);\n          }\n        } else {\n          _this.userSubject.next(null);\n        }\n        // Listen for auth changes\n        _this.supabase.auth.onAuthStateChange(/*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (_event, session) {\n            try {\n              if (session?.user) {\n                try {\n                  // Convert Supabase user to our app user model\n                  const appUser = yield _this.convertToAppUser(session.user);\n                  // Set the user in the subject\n                  _this.userSubject.next(appUser);\n                  // Then check the role in the background\n                  const role = yield _this.getUserRole();\n                  if (!role) {\n                    console.warn('User has no role on auth change, setting auth state to null');\n                    _this.userSubject.next(null);\n                  }\n                } catch (conversionError) {\n                  console.error('Error during user conversion in auth change:', conversionError);\n                  _this.userSubject.next(null);\n                }\n              } else {\n                _this.userSubject.next(null);\n              }\n            } catch (error) {\n              console.error('Auth state change error:', error);\n              _this.userSubject.next(null);\n            }\n          });\n          return function (_x, _x2) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        _this.userSubject.next(null);\n      }\n    })();\n  }\n  getSessionWithRetry() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let attempts = 0;\n      const maxRetries = _this2.MAX_RETRIES;\n      // Increase max backoff to 10 seconds\n      const backoffDelay = attempt => Math.min(_this2.RETRY_DELAY * Math.pow(2, attempt), 20000);\n      while (attempts < maxRetries) {\n        try {\n          // Add a small random delay before each attempt to reduce contention\n          const jitter = Math.random() * 500;\n          yield new Promise(resolve => setTimeout(resolve, jitter));\n          return yield _this2.supabase.auth.getSession();\n        } catch (error) {\n          attempts++;\n          console.warn(`Auth session attempt ${attempts} failed:`, error);\n          if (attempts < maxRetries) {\n            const delay = backoffDelay(attempts);\n            console.log(`Retrying in ${delay}ms... (attempt ${attempts}/${maxRetries})`);\n            yield new Promise(resolve => setTimeout(resolve, delay));\n            continue;\n          }\n          // If it's not a lock error and we've exhausted retries, throw\n          if (!_this2.isLockError(error)) {\n            throw error;\n          }\n        }\n      }\n      // If we've exhausted retries and it was likely due to lock errors,\n      // try one more time with a clean session\n      console.log('Final attempt with clean session...');\n      try {\n        // Clear local session first\n        yield _this2.supabase.auth.signOut({\n          scope: 'local'\n        });\n        // Wait a bit longer before the final attempt\n        yield new Promise(resolve => setTimeout(resolve, 2000));\n        return yield _this2.supabase.auth.getSession();\n      } catch (finalError) {\n        console.error('Final session attempt failed:', finalError);\n        throw new Error('Failed to acquire auth token after multiple attempts');\n      }\n    })();\n  }\n  isLockError(error) {\n    if (!(error instanceof Error)) {\n      return false;\n    }\n    // Check for specific lock error types\n    if (error.name === 'NavigatorLockAcquireTimeoutError' || error.name === 'LockAcquireTimeoutError') {\n      return true;\n    }\n    // Check for lock-related error messages (case insensitive)\n    const errorMsg = error.message?.toLowerCase() || '';\n    return errorMsg.includes('lock') || errorMsg.includes('timeout') || errorMsg.includes('navigator') || errorMsg.includes('acquire') || errorMsg.includes('concurrent');\n  }\n  /**\n   * Converts a Supabase User to our application's User model\n   * This ensures type compatibility throughout the application\n   */\n  convertToAppUser(supabaseUser) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Get profile data from the profiles table\n        const {\n          data: profile,\n          error\n        } = yield _this3.supabase.from('profiles').select('*').eq('id', supabaseUser.id).single();\n        if (error || !profile) {\n          console.error('Error fetching profile for user conversion:', error);\n          return null;\n        }\n        // Return a properly formatted User object\n        return {\n          id: supabaseUser.id,\n          email: supabaseUser.email || '',\n          // Ensure email is never undefined\n          full_name: profile.full_name,\n          phone: profile.phone,\n          avatar_url: profile.avatar_url,\n          role: profile.role,\n          created_at: profile.created_at,\n          updated_at: profile.updated_at,\n          is_approved: profile.is_approved\n          //         is_active: profile.is_active\n        };\n      } catch (error) {\n        console.error('Error converting Supabase user to app user:', error);\n        return null;\n      }\n    })();\n  }\n  login(email, password) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const result = yield _this4.supabase.auth.signInWithPassword({\n          email,\n          password\n        });\n        // Remove or comment out this line during testing if it's causing issues\n        // let r = await this.supabase.functions.invoke('hello', { body: { name: 'Functions' } });\n        return {\n          data: result.data,\n          error: result.error\n        };\n      } catch (error) {\n        if (_this4.isLockError(error)) {\n          console.log('Lock error during login, retrying...');\n          return _this4.retryOperation(() => _this4.supabase.auth.signInWithPassword({\n            email,\n            password\n          }));\n        }\n        throw error;\n      }\n    })();\n  }\n  register(email, password, role, phone, full_name) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: authData,\n          error: authError\n        } = yield _this5.supabase.auth.signUp({\n          email,\n          password,\n          options: {\n            data: {\n              role,\n              phone,\n              full_name\n            }\n          }\n        });\n        if (authError) {\n          return {\n            data: null,\n            error: authError\n          };\n        }\n        // Set initial status based on role\n        // Admins start as approved, others need approval\n        const isApproved = role === 'admin';\n        const {\n          error: profileError\n        } = yield _this5.supabase.from('profiles').insert([{\n          id: authData.user?.id,\n          email: email,\n          phone: phone,\n          full_name: full_name,\n          role: role,\n          created_at: new Date().toISOString(),\n          //is_active: isActive,\n          is_approved: isApproved\n        }]);\n        return {\n          data: authData,\n          error: profileError\n        };\n      } catch (error) {\n        if (_this5.isLockError(error)) {\n          return _this5.retryOperation(() => _this5.register(email, password, role, phone, full_name));\n        }\n        throw error;\n      }\n    })();\n  }\n  retryOperation(operation) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      console.log(\"Starting retry operation\");\n      let attempts = 0;\n      const maxRetries = _this6.MAX_RETRIES;\n      const maxBackoff = 15000; // 15 seconds max backoff\n      while (attempts < maxRetries) {\n        try {\n          // Add a small random delay before each attempt to reduce contention\n          if (attempts > 0) {\n            const jitter = Math.random() * 500;\n            yield new Promise(resolve => setTimeout(resolve, jitter));\n          }\n          return yield operation();\n        } catch (error) {\n          attempts++;\n          // Always log the error\n          console.warn(`Operation attempt ${attempts} failed:`, error);\n          // If it's a lock error or we have more retries, try again\n          if (_this6.isLockError(error) || attempts < maxRetries) {\n            // Use exponential backoff with a maximum limit\n            const delay = Math.min(_this6.RETRY_DELAY * Math.pow(2, attempts - 1), maxBackoff);\n            console.log(`Retry attempt ${attempts}/${maxRetries}, waiting ${delay}ms...`);\n            yield new Promise(resolve => setTimeout(resolve, delay));\n            continue;\n          }\n          // If it's not a lock error and we've exhausted retries, throw\n          throw error;\n        }\n      }\n      throw new Error(`Operation failed after ${maxRetries} retry attempts`);\n    })();\n  }\n  logout() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      console.log(\"LOGOUT\");\n      try {\n        console.log(\"LOGOUT 2\");\n        return yield _this7.supabase.auth.signOut();\n        console.log(\"LOGOUT 3\");\n      } catch (error) {\n        if (_this7.isLockError(error)) {\n          console.log('Lock error during logout, retrying...', error);\n          return _this7.retryOperation(() => _this7.supabase.auth.signOut());\n        }\n        throw error;\n      }\n    })();\n  }\n  resetPassword(email) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      return _this8.retryOperation(() => _this8.supabase.auth.resetPasswordForEmail(email));\n    })();\n  }\n  updatePassword(newPassword) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const result = yield _this9.retryOperation(() => _this9.supabase.auth.updateUser({\n          password: newPassword\n        }));\n        if (result.error || !result.data.user) {\n          return {\n            data: {\n              user: null\n            },\n            error: result.error\n          };\n        }\n        // Convert Supabase user to our app user model\n        const appUser = yield _this9.convertToAppUser(result.data.user);\n        return {\n          data: {\n            user: appUser\n          },\n          error: null\n        };\n      } catch (error) {\n        console.error('Error updating password:', error);\n        return {\n          data: {\n            user: null\n          },\n          error: error\n        };\n      }\n    })();\n  }\n  // Removed deprecated _getAuthUser method - use getCurrentUser() instead\n  getUserRole() {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: {\n            session\n          }\n        } = yield _this0.supabase.auth.getSession();\n        if (!session?.user) return null;\n        const {\n          data: profile,\n          error\n        } = yield _this0.supabase.from('profiles').select('role').eq('id', session.user.id).single();\n        if (error || !profile) {\n          console.error('Error fetching user role:', error);\n          return null;\n        }\n        return profile.role;\n      } catch (error) {\n        console.error('Error in getUserRole:', error);\n        return null;\n      }\n    })();\n  }\n  getDashboardRouteForRole(role) {\n    return `/dashboard/${role}`;\n  }\n  updateProfile(data) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const user = yield _this1.getCurrentUser();\n        if (!user) {\n          throw new Error('No user logged in');\n        }\n        const {\n          error\n        } = yield _this1.supabase.from('profiles').update(data).eq('id', user.id);\n        if (error) {\n          throw error;\n        }\n        return true;\n      } catch (error) {\n        console.error('Error updating profile:', error);\n        return false;\n      }\n    })();\n  }\n  getCurrentUser() {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this10.supabase.auth.getUser();\n        if (error || !data.user) {\n          return null;\n        }\n        // Use our helper method to convert Supabase user to our app user model\n        return yield _this10.convertToAppUser(data.user);\n      } catch (error) {\n        console.error('Error in getCurrentUser:', error);\n        return null;\n      }\n    })();\n  }\n  static ctorParameters = () => [];\n};\nAuthService = __decorate([Injectable({\n  providedIn: 'root'\n})], AuthService);\nexport { AuthService };", "map": {"version": 3, "names": ["Injectable", "createClient", "BehaviorSubject", "environment", "AuthService", "_supabase", "userSubject", "user$", "asObservable", "MAX_RETRIES", "RETRY_DELAY", "supabase", "constructor", "supabaseUrl", "supabase<PERSON>ey", "auth", "storageKey", "persistSession", "autoRefreshToken", "detectSessionInUrl", "flowType", "debug", "initializeAuth", "setTimeout", "_this", "_asyncToGenerator", "Promise", "resolve", "data", "session", "getSession", "user", "appUser", "convertToAppUser", "next", "role", "getUserRole", "console", "warn", "error", "onAuthStateChange", "_ref", "_event", "conversionError", "_x", "_x2", "apply", "arguments", "getSessionWithRetry", "_this2", "attempts", "maxRetries", "backoffDelay", "attempt", "Math", "min", "pow", "jitter", "random", "delay", "log", "isLockError", "signOut", "scope", "finalError", "Error", "name", "errorMsg", "message", "toLowerCase", "includes", "supabaseUser", "_this3", "profile", "from", "select", "eq", "id", "single", "email", "full_name", "phone", "avatar_url", "created_at", "updated_at", "is_approved", "login", "password", "_this4", "result", "signInWithPassword", "retryOperation", "register", "_this5", "authData", "authError", "signUp", "options", "isApproved", "profileError", "insert", "Date", "toISOString", "operation", "_this6", "max<PERSON><PERSON><PERSON>", "logout", "_this7", "resetPassword", "_this8", "resetPasswordForEmail", "updatePassword", "newPassword", "_this9", "updateUser", "_this0", "getDashboardRouteForRole", "updateProfile", "_this1", "getCurrentUser", "update", "_this10", "getUser", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { createClient, SupabaseClient, AuthError, PostgrestError, AuthResponse, User as SupabaseUser, navigatorLock } from '@supabase/supabase-js';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { environment } from '../../../environments/environment';\r\nimport { User } from '../models/user.model';\r\n\r\nexport type UserRole = 'rider' | 'driver' | 'admin';\r\n\r\ninterface AuthResult {\r\n  data: AuthResponse['data'] | null;\r\n  error: AuthError | PostgrestError | null;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private _supabase: SupabaseClient;\r\n  private userSubject = new BehaviorSubject<User | null>(null);\r\n  user$ = this.userSubject.asObservable();\r\nprivate readonly MAX_RETRIES = 1; // Increase from 3\r\nprivate readonly RETRY_DELAY = 10000; // Increase from 1000\r\n\r\n  // Expose the Supabase client as a getter to be used by other services\r\n  get supabase(): SupabaseClient {\r\n    return this._supabase;\r\n  }\r\n\r\n  constructor() {\r\n    // Create a custom lock function with a longer timeout\r\n    // const customLock = (name: string, acquireTimeout: number, fn: () => Promise<any>) => {\r\n    //   // Increase the timeout to 5000ms (5 seconds) to give more time for lock acquisition\r\n    //   const adjustedTimeout = Math.max(acquireTimeout, 10000);\r\n    //   console.log(`Attempting to acquire lock \"${name}\" with timeout ${adjustedTimeout}ms`);\r\n    //   return navigatorLock(name, adjustedTimeout, fn);\r\n    // };\r\n\r\n    this._supabase = createClient(environment.supabaseUrl, environment.supabaseKey,\r\n       {\r\n      auth: {\r\n        storageKey: 'holy-rides-auth',\r\n        persistSession: true,\r\n        autoRefreshToken: true,\r\n        detectSessionInUrl: true,\r\n        flowType: 'pkce',\r\n        debug: false, // Set to false to reduce console noise\r\n     //   lock: customLock\r\n      }\r\n    });\r\n\r\n    // Initialize auth with a longer delay to avoid lock conflicts\r\n      this.initializeAuth();\r\n    setTimeout(() => {\r\n    \r\n    }, 2500);\r\n  }\r\n\r\n  private async initializeAuth() {\r\n    try {\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n      // Get initial session\r\n      const { data: { session } } =await this.supabase.auth.getSession()\r\n\r\n      if (session?.user) {\r\n        try {\r\n          // Convert Supabase user to our app user model\r\n          const appUser = await this.convertToAppUser(session.user);\r\n          // Set the user in the subject\r\n          this.userSubject.next(appUser);\r\n\r\n          // Then check the role in the background\r\n          const role = await this.getUserRole();\r\n          if (!role) {\r\n            console.warn('User has no role, setting auth state to null');\r\n            this.userSubject.next(null);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error during user conversion in init:', error);\r\n          this.userSubject.next(null);\r\n        }\r\n      } else {\r\n        this.userSubject.next(null);\r\n      }\r\n\r\n      // Listen for auth changes\r\n      this.supabase.auth.onAuthStateChange(async (_event, session) => {\r\n        try {\r\n          if (session?.user) {\r\n            try {\r\n              // Convert Supabase user to our app user model\r\n              const appUser = await this.convertToAppUser(session.user);\r\n              // Set the user in the subject\r\n              this.userSubject.next(appUser);\r\n\r\n              // Then check the role in the background\r\n              const role = await this.getUserRole();\r\n              if (!role) {\r\n                console.warn('User has no role on auth change, setting auth state to null');\r\n                this.userSubject.next(null);\r\n              }\r\n            } catch (conversionError) {\r\n              console.error('Error during user conversion in auth change:', conversionError);\r\n              this.userSubject.next(null);\r\n            }\r\n          } else {\r\n            this.userSubject.next(null);\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth state change error:', error);\r\n          this.userSubject.next(null);\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error('Auth initialization error:', error);\r\n      this.userSubject.next(null);\r\n    }\r\n  }\r\n\r\n  private async getSessionWithRetry() {\r\n    let attempts = 0;\r\n    const maxRetries = this.MAX_RETRIES;\r\n    // Increase max backoff to 10 seconds\r\n    const backoffDelay = (attempt: number) => Math.min(this.RETRY_DELAY * Math.pow(2, attempt), 20000);\r\n\r\n    while (attempts < maxRetries) {\r\n      try {\r\n        // Add a small random delay before each attempt to reduce contention\r\n        const jitter = Math.random() * 500;\r\n        await new Promise(resolve => setTimeout(resolve, jitter));\r\n\r\n        return await this.supabase.auth.getSession();\r\n      } catch (error) {\r\n        attempts++;\r\n        console.warn(`Auth session attempt ${attempts} failed:`, error);\r\n\r\n        if (attempts < maxRetries) {\r\n          const delay = backoffDelay(attempts);\r\n          console.log(`Retrying in ${delay}ms... (attempt ${attempts}/${maxRetries})`);\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          continue;\r\n        }\r\n\r\n        // If it's not a lock error and we've exhausted retries, throw\r\n        if (!this.isLockError(error)) {\r\n          throw error;\r\n        }\r\n      }\r\n    }\r\n\r\n    // If we've exhausted retries and it was likely due to lock errors,\r\n    // try one more time with a clean session\r\n    console.log('Final attempt with clean session...');\r\n    try {\r\n      // Clear local session first\r\n      await this.supabase.auth.signOut({ scope: 'local' });\r\n      // Wait a bit longer before the final attempt\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      return await this.supabase.auth.getSession();\r\n    } catch (finalError) {\r\n      console.error('Final session attempt failed:', finalError);\r\n      throw new Error('Failed to acquire auth token after multiple attempts');\r\n    }\r\n  }\r\n\r\n  private isLockError(error: unknown): boolean {\r\n    if (!(error instanceof Error)) {\r\n      return false;\r\n    }\r\n\r\n    // Check for specific lock error types\r\n    if (error.name === 'NavigatorLockAcquireTimeoutError' ||\r\n        error.name === 'LockAcquireTimeoutError') {\r\n      return true;\r\n    }\r\n\r\n    // Check for lock-related error messages (case insensitive)\r\n    const errorMsg = error.message?.toLowerCase() || '';\r\n    return errorMsg.includes('lock') ||\r\n           errorMsg.includes('timeout') ||\r\n           errorMsg.includes('navigator') ||\r\n           errorMsg.includes('acquire') ||\r\n           errorMsg.includes('concurrent');\r\n  }\r\n\r\n  /**\r\n   * Converts a Supabase User to our application's User model\r\n   * This ensures type compatibility throughout the application\r\n   */\r\n  private async convertToAppUser(supabaseUser: SupabaseUser): Promise<User | null> {\r\n    try {\r\n      // Get profile data from the profiles table\r\n      const { data: profile, error } = await this.supabase\r\n        .from('profiles')\r\n        .select('*')\r\n        .eq('id', supabaseUser.id)\r\n        .single();\r\n\r\n      if (error || !profile) {\r\n        console.error('Error fetching profile for user conversion:', error);\r\n        return null;\r\n      }\r\n\r\n      // Return a properly formatted User object\r\n      return {\r\n        id: supabaseUser.id,\r\n        email: supabaseUser.email || '', // Ensure email is never undefined\r\n        full_name: profile.full_name,\r\n        phone: profile.phone,\r\n        avatar_url: profile.avatar_url,\r\n        role: profile.role,\r\n        created_at: profile.created_at,\r\n        updated_at: profile.updated_at,\r\n        is_approved: profile.is_approved,\r\n//         is_active: profile.is_active\r\n      };\r\n    } catch (error) {\r\n      console.error('Error converting Supabase user to app user:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  async login(email: string, password: string): Promise<AuthResult> {\r\n    try {\r\n      const result = await this.supabase.auth.signInWithPassword({ email, password });\r\n      // Remove or comment out this line during testing if it's causing issues\r\n      // let r = await this.supabase.functions.invoke('hello', { body: { name: 'Functions' } });\r\n\r\n      return { data: result.data, error: result.error };\r\n    } catch (error) {\r\n      if (this.isLockError(error)) {\r\n        console.log('Lock error during login, retrying...');\r\n        return this.retryOperation(() =>\r\n          this.supabase.auth.signInWithPassword({ email, password })\r\n        );\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async register(email: string, password: string, role: UserRole, phone?: string, full_name?: string): Promise<AuthResult> {\r\n    try {\r\n      const { data: authData, error: authError } = await this.supabase.auth.signUp({\r\n        email,\r\n        password,\r\n        options: {\r\n          data: {\r\n            role,\r\n            phone,\r\n            full_name\r\n          }\r\n        }\r\n      });\r\n\r\n      if (authError) {\r\n        return { data: null, error: authError };\r\n      }\r\n\r\n      // Set initial status based on role\r\n      // Admins start as approved, others need approval\r\n      const isApproved = role === 'admin';\r\n\r\n      const { error: profileError } = await this.supabase\r\n        .from('profiles')\r\n        .insert([\r\n          {\r\n            id: authData.user?.id,\r\n            email: email,\r\n            phone: phone,\r\n            full_name: full_name,\r\n            role: role,\r\n            created_at: new Date().toISOString(),\r\n            //is_active: isActive,\r\n            is_approved: isApproved\r\n          }\r\n        ]);\r\n\r\n      return { data: authData, error: profileError };\r\n    } catch (error) {\r\n      if (this.isLockError(error)) {\r\n        return this.retryOperation(() => this.register(email, password, role, phone, full_name));\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\nprivate async retryOperation<T>(operation: () => Promise<T>): Promise<T> {\r\n  console.log(\"Starting retry operation\");\r\n  let attempts = 0;\r\n  const maxRetries = this.MAX_RETRIES;\r\n  const maxBackoff = 15000; // 15 seconds max backoff\r\n\r\n  while (attempts < maxRetries) {\r\n    try {\r\n      // Add a small random delay before each attempt to reduce contention\r\n      if (attempts > 0) {\r\n        const jitter = Math.random() * 500;\r\n        await new Promise(resolve => setTimeout(resolve, jitter));\r\n      }\r\n\r\n      return await operation();\r\n    } catch (error) {\r\n      attempts++;\r\n\r\n      // Always log the error\r\n      console.warn(`Operation attempt ${attempts} failed:`, error);\r\n\r\n      // If it's a lock error or we have more retries, try again\r\n      if ((this.isLockError(error) || attempts < maxRetries)) {\r\n        // Use exponential backoff with a maximum limit\r\n        const delay = Math.min(this.RETRY_DELAY * Math.pow(2, attempts - 1), maxBackoff);\r\n        console.log(`Retry attempt ${attempts}/${maxRetries}, waiting ${delay}ms...`);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n        continue;\r\n      }\r\n\r\n      // If it's not a lock error and we've exhausted retries, throw\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  throw new Error(`Operation failed after ${maxRetries} retry attempts`);\r\n}\r\n\r\n  async logout(): Promise<{ error: AuthError | null }> {\r\n    console.log(\"LOGOUT\")\r\n    try {\r\n      console.log(\"LOGOUT 2\")\r\n      return await this.supabase.auth.signOut();\r\n      console.log(\"LOGOUT 3\")\r\n    } catch (error) {\r\n      if (this.isLockError(error)) {\r\n        console.log('Lock error during logout, retrying...', error);\r\n        return this.retryOperation(() => this.supabase.auth.signOut());\r\n      }\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async resetPassword(email: string): Promise<{ error: AuthError | null }> {\r\n    return this.retryOperation(() =>\r\n      this.supabase.auth.resetPasswordForEmail(email)\r\n    );\r\n  }\r\n\r\n  async updatePassword(newPassword: string): Promise<{ data: { user: User | null }; error: AuthError | null }> {\r\n    try {\r\n      const result = await this.retryOperation(() =>\r\n        this.supabase.auth.updateUser({ password: newPassword })\r\n      );\r\n\r\n      if (result.error || !result.data.user) {\r\n        return { data: { user: null }, error: result.error };\r\n      }\r\n\r\n      // Convert Supabase user to our app user model\r\n      const appUser = await this.convertToAppUser(result.data.user);\r\n      return { data: { user: appUser }, error: null };\r\n    } catch (error) {\r\n      console.error('Error updating password:', error);\r\n      return { data: { user: null }, error: error as AuthError };\r\n    }\r\n  }\r\n\r\n  // Removed deprecated _getAuthUser method - use getCurrentUser() instead\r\n\r\n  async getUserRole(): Promise<UserRole | null> {\r\n    try {\r\n      const { data: { session } } = await this.supabase.auth.getSession();\r\n      if (!session?.user) return null;\r\n\r\n      const { data: profile, error } = await this.supabase\r\n        .from('profiles')\r\n        .select('role')\r\n        .eq('id', session.user.id)\r\n        .single();\r\n\r\n      if (error || !profile) {\r\n        console.error('Error fetching user role:', error);\r\n        return null;\r\n      }\r\n\r\n      return profile.role as UserRole;\r\n    } catch (error) {\r\n      console.error('Error in getUserRole:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  getDashboardRouteForRole(role: UserRole): string {\r\n    return `/dashboard/${role}`;\r\n  }\r\n\r\n  async updateProfile(data: Partial<{\r\n    full_name: string;\r\n    phone: string;\r\n    avatar_url: string;\r\n  }>): Promise<boolean> {\r\n    try {\r\n      const user = await this.getCurrentUser();\r\n      if (!user) {\r\n        throw new Error('No user logged in');\r\n      }\r\n\r\n      const { error } = await this.supabase\r\n        .from('profiles')\r\n        .update(data)\r\n        .eq('id', user.id);\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Error updating profile:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  async getCurrentUser(): Promise<User | null> {\r\n    try {\r\n      const { data, error } = await this.supabase.auth.getUser();\r\n      if (error || !data.user) {\r\n        return null;\r\n      }\r\n\r\n      // Use our helper method to convert Supabase user to our app user model\r\n      return await this.convertToAppUser(data.user);\r\n    } catch (error) {\r\n      console.error('Error in getCurrentUser:', error);\r\n      return null;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,YAAY,QAAsG,uBAAuB;AAClJ,SAASC,eAAe,QAAQ,MAAM;AACtC,SAASC,WAAW,QAAQ,mCAAmC;AAaxD,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EACdC,SAAS;EACTC,WAAW,GAAG,IAAIJ,eAAe,CAAc,IAAI,CAAC;EAC5DK,KAAK,GAAG,IAAI,CAACD,WAAW,CAACE,YAAY,EAAE;EACxBC,WAAW,GAAG,CAAC,CAAC,CAAC;EACjBC,WAAW,GAAG,KAAK,CAAC,CAAC;EAEpC;EACA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACN,SAAS;EACvB;EAEAO,YAAA;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAI,CAACP,SAAS,GAAGJ,YAAY,CAACE,WAAW,CAACU,WAAW,EAAEV,WAAW,CAACW,WAAW,EAC3E;MACDC,IAAI,EAAE;QACJC,UAAU,EAAE,iBAAiB;QAC7BC,cAAc,EAAE,IAAI;QACpBC,gBAAgB,EAAE,IAAI;QACtBC,kBAAkB,EAAE,IAAI;QACxBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,KAAK,CAAE;QACjB;;KAEA,CAAC;IAEF;IACE,IAAI,CAACC,cAAc,EAAE;IACvBC,UAAU,CAAC,MAAK,CAEhB,CAAC,EAAE,IAAI,CAAC;EACV;EAEcD,cAAcA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAC1B,IAAI;QACF,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD;QACA,MAAM;UAAEC,IAAI,EAAE;YAAEC;UAAO;QAAE,CAAE,SAAQL,KAAI,CAACb,QAAQ,CAACI,IAAI,CAACe,UAAU,EAAE;QAElE,IAAID,OAAO,EAAEE,IAAI,EAAE;UACjB,IAAI;YACF;YACA,MAAMC,OAAO,SAASR,KAAI,CAACS,gBAAgB,CAACJ,OAAO,CAACE,IAAI,CAAC;YACzD;YACAP,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAACF,OAAO,CAAC;YAE9B;YACA,MAAMG,IAAI,SAASX,KAAI,CAACY,WAAW,EAAE;YACrC,IAAI,CAACD,IAAI,EAAE;cACTE,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;cAC5Dd,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;YAC7B;UACF,CAAC,CAAC,OAAOK,KAAK,EAAE;YACdF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;YAC7Df,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;UAC7B;QACF,CAAC,MAAM;UACLV,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;QAC7B;QAEA;QACAV,KAAI,CAACb,QAAQ,CAACI,IAAI,CAACyB,iBAAiB;UAAA,IAAAC,IAAA,GAAAhB,iBAAA,CAAC,WAAOiB,MAAM,EAAEb,OAAO,EAAI;YAC7D,IAAI;cACF,IAAIA,OAAO,EAAEE,IAAI,EAAE;gBACjB,IAAI;kBACF;kBACA,MAAMC,OAAO,SAASR,KAAI,CAACS,gBAAgB,CAACJ,OAAO,CAACE,IAAI,CAAC;kBACzD;kBACAP,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAACF,OAAO,CAAC;kBAE9B;kBACA,MAAMG,IAAI,SAASX,KAAI,CAACY,WAAW,EAAE;kBACrC,IAAI,CAACD,IAAI,EAAE;oBACTE,OAAO,CAACC,IAAI,CAAC,6DAA6D,CAAC;oBAC3Ed,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;kBAC7B;gBACF,CAAC,CAAC,OAAOS,eAAe,EAAE;kBACxBN,OAAO,CAACE,KAAK,CAAC,8CAA8C,EAAEI,eAAe,CAAC;kBAC9EnB,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;gBAC7B;cACF,CAAC,MAAM;gBACLV,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;cAC7B;YACF,CAAC,CAAC,OAAOK,KAAK,EAAE;cACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;cAChDf,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;YAC7B;UACF,CAAC;UAAA,iBAAAU,EAAA,EAAAC,GAAA;YAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACJ,CAAC,CAAC,OAAOR,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDf,KAAI,CAAClB,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;MAC7B;IAAC;EACH;EAEcc,mBAAmBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAxB,iBAAA;MAC/B,IAAIyB,QAAQ,GAAG,CAAC;MAChB,MAAMC,UAAU,GAAGF,MAAI,CAACxC,WAAW;MACnC;MACA,MAAM2C,YAAY,GAAIC,OAAe,IAAKC,IAAI,CAACC,GAAG,CAACN,MAAI,CAACvC,WAAW,GAAG4C,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,OAAO,CAAC,EAAE,KAAK,CAAC;MAElG,OAAOH,QAAQ,GAAGC,UAAU,EAAE;QAC5B,IAAI;UACF;UACA,MAAMM,MAAM,GAAGH,IAAI,CAACI,MAAM,EAAE,GAAG,GAAG;UAClC,MAAM,IAAIhC,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAE8B,MAAM,CAAC,CAAC;UAEzD,aAAaR,MAAI,CAACtC,QAAQ,CAACI,IAAI,CAACe,UAAU,EAAE;QAC9C,CAAC,CAAC,OAAOS,KAAK,EAAE;UACdW,QAAQ,EAAE;UACVb,OAAO,CAACC,IAAI,CAAC,wBAAwBY,QAAQ,UAAU,EAAEX,KAAK,CAAC;UAE/D,IAAIW,QAAQ,GAAGC,UAAU,EAAE;YACzB,MAAMQ,KAAK,GAAGP,YAAY,CAACF,QAAQ,CAAC;YACpCb,OAAO,CAACuB,GAAG,CAAC,eAAeD,KAAK,kBAAkBT,QAAQ,IAAIC,UAAU,GAAG,CAAC;YAC5E,MAAM,IAAIzB,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAEgC,KAAK,CAAC,CAAC;YACxD;UACF;UAEA;UACA,IAAI,CAACV,MAAI,CAACY,WAAW,CAACtB,KAAK,CAAC,EAAE;YAC5B,MAAMA,KAAK;UACb;QACF;MACF;MAEA;MACA;MACAF,OAAO,CAACuB,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI;QACF;QACA,MAAMX,MAAI,CAACtC,QAAQ,CAACI,IAAI,CAAC+C,OAAO,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE,CAAC;QACpD;QACA,MAAM,IAAIrC,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,aAAasB,MAAI,CAACtC,QAAQ,CAACI,IAAI,CAACe,UAAU,EAAE;MAC9C,CAAC,CAAC,OAAOkC,UAAU,EAAE;QACnB3B,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEyB,UAAU,CAAC;QAC1D,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;MACzE;IAAC;EACH;EAEQJ,WAAWA,CAACtB,KAAc;IAChC,IAAI,EAAEA,KAAK,YAAY0B,KAAK,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA;IACA,IAAI1B,KAAK,CAAC2B,IAAI,KAAK,kCAAkC,IACjD3B,KAAK,CAAC2B,IAAI,KAAK,yBAAyB,EAAE;MAC5C,OAAO,IAAI;IACb;IAEA;IACA,MAAMC,QAAQ,GAAG5B,KAAK,CAAC6B,OAAO,EAAEC,WAAW,EAAE,IAAI,EAAE;IACnD,OAAOF,QAAQ,CAACG,QAAQ,CAAC,MAAM,CAAC,IACzBH,QAAQ,CAACG,QAAQ,CAAC,SAAS,CAAC,IAC5BH,QAAQ,CAACG,QAAQ,CAAC,WAAW,CAAC,IAC9BH,QAAQ,CAACG,QAAQ,CAAC,SAAS,CAAC,IAC5BH,QAAQ,CAACG,QAAQ,CAAC,YAAY,CAAC;EACxC;EAEA;;;;EAIcrC,gBAAgBA,CAACsC,YAA0B;IAAA,IAAAC,MAAA;IAAA,OAAA/C,iBAAA;MACvD,IAAI;QACF;QACA,MAAM;UAAEG,IAAI,EAAE6C,OAAO;UAAElC;QAAK,CAAE,SAASiC,MAAI,CAAC7D,QAAQ,CACjD+D,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,IAAI,EAAEL,YAAY,CAACM,EAAE,CAAC,CACzBC,MAAM,EAAE;QAEX,IAAIvC,KAAK,IAAI,CAACkC,OAAO,EAAE;UACrBpC,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,OAAO,IAAI;QACb;QAEA;QACA,OAAO;UACLsC,EAAE,EAAEN,YAAY,CAACM,EAAE;UACnBE,KAAK,EAAER,YAAY,CAACQ,KAAK,IAAI,EAAE;UAAE;UACjCC,SAAS,EAAEP,OAAO,CAACO,SAAS;UAC5BC,KAAK,EAAER,OAAO,CAACQ,KAAK;UACpBC,UAAU,EAAET,OAAO,CAACS,UAAU;UAC9B/C,IAAI,EAAEsC,OAAO,CAACtC,IAAI;UAClBgD,UAAU,EAAEV,OAAO,CAACU,UAAU;UAC9BC,UAAU,EAAEX,OAAO,CAACW,UAAU;UAC9BC,WAAW,EAAEZ,OAAO,CAACY;UAC7B;SACO;MACH,CAAC,CAAC,OAAO9C,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,OAAO,IAAI;MACb;IAAC;EACH;EAEM+C,KAAKA,CAACP,KAAa,EAAEQ,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAA/D,iBAAA;MACzC,IAAI;QACF,MAAMgE,MAAM,SAASD,MAAI,CAAC7E,QAAQ,CAACI,IAAI,CAAC2E,kBAAkB,CAAC;UAAEX,KAAK;UAAEQ;QAAQ,CAAE,CAAC;QAC/E;QACA;QAEA,OAAO;UAAE3D,IAAI,EAAE6D,MAAM,CAAC7D,IAAI;UAAEW,KAAK,EAAEkD,MAAM,CAAClD;QAAK,CAAE;MACnD,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd,IAAIiD,MAAI,CAAC3B,WAAW,CAACtB,KAAK,CAAC,EAAE;UAC3BF,OAAO,CAACuB,GAAG,CAAC,sCAAsC,CAAC;UACnD,OAAO4B,MAAI,CAACG,cAAc,CAAC,MACzBH,MAAI,CAAC7E,QAAQ,CAACI,IAAI,CAAC2E,kBAAkB,CAAC;YAAEX,KAAK;YAAEQ;UAAQ,CAAE,CAAC,CAC3D;QACH;QACA,MAAMhD,KAAK;MACb;IAAC;EACH;EAEMqD,QAAQA,CAACb,KAAa,EAAEQ,QAAgB,EAAEpD,IAAc,EAAE8C,KAAc,EAAED,SAAkB;IAAA,IAAAa,MAAA;IAAA,OAAApE,iBAAA;MAChG,IAAI;QACF,MAAM;UAAEG,IAAI,EAAEkE,QAAQ;UAAEvD,KAAK,EAAEwD;QAAS,CAAE,SAASF,MAAI,CAAClF,QAAQ,CAACI,IAAI,CAACiF,MAAM,CAAC;UAC3EjB,KAAK;UACLQ,QAAQ;UACRU,OAAO,EAAE;YACPrE,IAAI,EAAE;cACJO,IAAI;cACJ8C,KAAK;cACLD;;;SAGL,CAAC;QAEF,IAAIe,SAAS,EAAE;UACb,OAAO;YAAEnE,IAAI,EAAE,IAAI;YAAEW,KAAK,EAAEwD;UAAS,CAAE;QACzC;QAEA;QACA;QACA,MAAMG,UAAU,GAAG/D,IAAI,KAAK,OAAO;QAEnC,MAAM;UAAEI,KAAK,EAAE4D;QAAY,CAAE,SAASN,MAAI,CAAClF,QAAQ,CAChD+D,IAAI,CAAC,UAAU,CAAC,CAChB0B,MAAM,CAAC,CACN;UACEvB,EAAE,EAAEiB,QAAQ,CAAC/D,IAAI,EAAE8C,EAAE;UACrBE,KAAK,EAAEA,KAAK;UACZE,KAAK,EAAEA,KAAK;UACZD,SAAS,EAAEA,SAAS;UACpB7C,IAAI,EAAEA,IAAI;UACVgD,UAAU,EAAE,IAAIkB,IAAI,EAAE,CAACC,WAAW,EAAE;UACpC;UACAjB,WAAW,EAAEa;SACd,CACF,CAAC;QAEJ,OAAO;UAAEtE,IAAI,EAAEkE,QAAQ;UAAEvD,KAAK,EAAE4D;QAAY,CAAE;MAChD,CAAC,CAAC,OAAO5D,KAAK,EAAE;QACd,IAAIsD,MAAI,CAAChC,WAAW,CAACtB,KAAK,CAAC,EAAE;UAC3B,OAAOsD,MAAI,CAACF,cAAc,CAAC,MAAME,MAAI,CAACD,QAAQ,CAACb,KAAK,EAAEQ,QAAQ,EAAEpD,IAAI,EAAE8C,KAAK,EAAED,SAAS,CAAC,CAAC;QAC1F;QACA,MAAMzC,KAAK;MACb;IAAC;EACH;EACYoD,cAAcA,CAAIY,SAA2B;IAAA,IAAAC,MAAA;IAAA,OAAA/E,iBAAA;MACzDY,OAAO,CAACuB,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAIV,QAAQ,GAAG,CAAC;MAChB,MAAMC,UAAU,GAAGqD,MAAI,CAAC/F,WAAW;MACnC,MAAMgG,UAAU,GAAG,KAAK,CAAC,CAAC;MAE1B,OAAOvD,QAAQ,GAAGC,UAAU,EAAE;QAC5B,IAAI;UACF;UACA,IAAID,QAAQ,GAAG,CAAC,EAAE;YAChB,MAAMO,MAAM,GAAGH,IAAI,CAACI,MAAM,EAAE,GAAG,GAAG;YAClC,MAAM,IAAIhC,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAE8B,MAAM,CAAC,CAAC;UAC3D;UAEA,aAAa8C,SAAS,EAAE;QAC1B,CAAC,CAAC,OAAOhE,KAAK,EAAE;UACdW,QAAQ,EAAE;UAEV;UACAb,OAAO,CAACC,IAAI,CAAC,qBAAqBY,QAAQ,UAAU,EAAEX,KAAK,CAAC;UAE5D;UACA,IAAKiE,MAAI,CAAC3C,WAAW,CAACtB,KAAK,CAAC,IAAIW,QAAQ,GAAGC,UAAU,EAAG;YACtD;YACA,MAAMQ,KAAK,GAAGL,IAAI,CAACC,GAAG,CAACiD,MAAI,CAAC9F,WAAW,GAAG4C,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,QAAQ,GAAG,CAAC,CAAC,EAAEuD,UAAU,CAAC;YAChFpE,OAAO,CAACuB,GAAG,CAAC,iBAAiBV,QAAQ,IAAIC,UAAU,aAAaQ,KAAK,OAAO,CAAC;YAC7E,MAAM,IAAIjC,OAAO,CAACC,OAAO,IAAIJ,UAAU,CAACI,OAAO,EAAEgC,KAAK,CAAC,CAAC;YACxD;UACF;UAEA;UACA,MAAMpB,KAAK;QACb;MACF;MAEA,MAAM,IAAI0B,KAAK,CAAC,0BAA0Bd,UAAU,iBAAiB,CAAC;IAAC;EACzE;EAEQuD,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAlF,iBAAA;MACVY,OAAO,CAACuB,GAAG,CAAC,QAAQ,CAAC;MACrB,IAAI;QACFvB,OAAO,CAACuB,GAAG,CAAC,UAAU,CAAC;QACvB,aAAa+C,MAAI,CAAChG,QAAQ,CAACI,IAAI,CAAC+C,OAAO,EAAE;QACzCzB,OAAO,CAACuB,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACd,IAAIoE,MAAI,CAAC9C,WAAW,CAACtB,KAAK,CAAC,EAAE;UAC3BF,OAAO,CAACuB,GAAG,CAAC,uCAAuC,EAAErB,KAAK,CAAC;UAC3D,OAAOoE,MAAI,CAAChB,cAAc,CAAC,MAAMgB,MAAI,CAAChG,QAAQ,CAACI,IAAI,CAAC+C,OAAO,EAAE,CAAC;QAChE;QACA,MAAMvB,KAAK;MACb;IAAC;EACH;EAEMqE,aAAaA,CAAC7B,KAAa;IAAA,IAAA8B,MAAA;IAAA,OAAApF,iBAAA;MAC/B,OAAOoF,MAAI,CAAClB,cAAc,CAAC,MACzBkB,MAAI,CAAClG,QAAQ,CAACI,IAAI,CAAC+F,qBAAqB,CAAC/B,KAAK,CAAC,CAChD;IAAC;EACJ;EAEMgC,cAAcA,CAACC,WAAmB;IAAA,IAAAC,MAAA;IAAA,OAAAxF,iBAAA;MACtC,IAAI;QACF,MAAMgE,MAAM,SAASwB,MAAI,CAACtB,cAAc,CAAC,MACvCsB,MAAI,CAACtG,QAAQ,CAACI,IAAI,CAACmG,UAAU,CAAC;UAAE3B,QAAQ,EAAEyB;QAAW,CAAE,CAAC,CACzD;QAED,IAAIvB,MAAM,CAAClD,KAAK,IAAI,CAACkD,MAAM,CAAC7D,IAAI,CAACG,IAAI,EAAE;UACrC,OAAO;YAAEH,IAAI,EAAE;cAAEG,IAAI,EAAE;YAAI,CAAE;YAAEQ,KAAK,EAAEkD,MAAM,CAAClD;UAAK,CAAE;QACtD;QAEA;QACA,MAAMP,OAAO,SAASiF,MAAI,CAAChF,gBAAgB,CAACwD,MAAM,CAAC7D,IAAI,CAACG,IAAI,CAAC;QAC7D,OAAO;UAAEH,IAAI,EAAE;YAAEG,IAAI,EAAEC;UAAO,CAAE;UAAEO,KAAK,EAAE;QAAI,CAAE;MACjD,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO;UAAEX,IAAI,EAAE;YAAEG,IAAI,EAAE;UAAI,CAAE;UAAEQ,KAAK,EAAEA;QAAkB,CAAE;MAC5D;IAAC;EACH;EAEA;EAEMH,WAAWA,CAAA;IAAA,IAAA+E,MAAA;IAAA,OAAA1F,iBAAA;MACf,IAAI;QACF,MAAM;UAAEG,IAAI,EAAE;YAAEC;UAAO;QAAE,CAAE,SAASsF,MAAI,CAACxG,QAAQ,CAACI,IAAI,CAACe,UAAU,EAAE;QACnE,IAAI,CAACD,OAAO,EAAEE,IAAI,EAAE,OAAO,IAAI;QAE/B,MAAM;UAAEH,IAAI,EAAE6C,OAAO;UAAElC;QAAK,CAAE,SAAS4E,MAAI,CAACxG,QAAQ,CACjD+D,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,MAAM,CAAC,CACdC,EAAE,CAAC,IAAI,EAAE/C,OAAO,CAACE,IAAI,CAAC8C,EAAE,CAAC,CACzBC,MAAM,EAAE;QAEX,IAAIvC,KAAK,IAAI,CAACkC,OAAO,EAAE;UACrBpC,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACjD,OAAO,IAAI;QACb;QAEA,OAAOkC,OAAO,CAACtC,IAAgB;MACjC,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,OAAO,IAAI;MACb;IAAC;EACH;EAEA6E,wBAAwBA,CAACjF,IAAc;IACrC,OAAO,cAAcA,IAAI,EAAE;EAC7B;EAEMkF,aAAaA,CAACzF,IAIlB;IAAA,IAAA0F,MAAA;IAAA,OAAA7F,iBAAA;MACA,IAAI;QACF,MAAMM,IAAI,SAASuF,MAAI,CAACC,cAAc,EAAE;QACxC,IAAI,CAACxF,IAAI,EAAE;UACT,MAAM,IAAIkC,KAAK,CAAC,mBAAmB,CAAC;QACtC;QAEA,MAAM;UAAE1B;QAAK,CAAE,SAAS+E,MAAI,CAAC3G,QAAQ,CAClC+D,IAAI,CAAC,UAAU,CAAC,CAChB8C,MAAM,CAAC5F,IAAI,CAAC,CACZgD,EAAE,CAAC,IAAI,EAAE7C,IAAI,CAAC8C,EAAE,CAAC;QAEpB,IAAItC,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,OAAO,KAAK;MACd;IAAC;EACH;EAEMgF,cAAcA,CAAA;IAAA,IAAAE,OAAA;IAAA,OAAAhG,iBAAA;MAClB,IAAI;QACF,MAAM;UAAEG,IAAI;UAAEW;QAAK,CAAE,SAASkF,OAAI,CAAC9G,QAAQ,CAACI,IAAI,CAAC2G,OAAO,EAAE;QAC1D,IAAInF,KAAK,IAAI,CAACX,IAAI,CAACG,IAAI,EAAE;UACvB,OAAO,IAAI;QACb;QAEA;QACA,aAAa0F,OAAI,CAACxF,gBAAgB,CAACL,IAAI,CAACG,IAAI,CAAC;MAC/C,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,OAAO,IAAI;MACb;IAAC;EACH;;;AA/ZWnC,WAAW,GAAAuH,UAAA,EAHvB3H,UAAU,CAAC;EACV4H,UAAU,EAAE;CACb,CAAC,C,EACWxH,WAAW,CAgavB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}