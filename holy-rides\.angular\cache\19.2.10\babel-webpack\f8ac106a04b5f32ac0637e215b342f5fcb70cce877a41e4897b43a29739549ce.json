{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { UserService } from './user.service';\nimport { AuthService } from './auth.service';\nlet SmsService = class SmsService {\n  userService;\n  authService;\n  supabase;\n  constructor(userService, authService) {\n    this.userService = userService;\n    this.authService = authService;\n    this.supabase = authService.supabase;\n  }\n  /**\n   * Send an SMS notification to a user via Supabase Twilio lambda function\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @returns Promise resolving to the message SID if successful\n   */\n  sendSms(to, body) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Format phone number if needed (ensure it has the + prefix)\n        const formattedPhone = _this.formatPhoneNumber(to);\n        // Call the Supabase lambda function to send the SMS\n        const {\n          data,\n          error\n        } = yield _this.supabase.functions.invoke('twilio', {\n          body: {\n            to: formattedPhone,\n            message: body,\n            from: \"+17272025413\"\n          }\n        });\n        if (error) {\n          console.error('Error calling Twilio lambda function:', error);\n          throw error;\n        }\n        if (!data || !data.sid) {\n          throw new Error('No message SID returned from Twilio lambda function');\n        }\n        console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);\n        return data.sid;\n      } catch (error) {\n        console.error('Error sending SMS:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Send ride assignment notifications to both rider and driver\n   * @param ride The ride that was assigned\n   * @param driverId The ID of the driver assigned to the ride\n   */\n  sendRideAssignmentNotifications(ride, driverId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Get rider and driver information\n        const [rider, driver] = yield Promise.all([_this2.userService.getUserById(ride.rider_id), _this2.userService.getUserById(driverId)]);\n        if (!rider || !driver) {\n          throw new Error('Could not find rider or driver information');\n        }\n        // Check if phone numbers are available\n        if (!rider.phone || !driver.phone) {\n          console.warn('Phone number missing for rider or driver. SMS notification skipped.');\n          return;\n        }\n        // Format pickup time for better readability\n        const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n        // Send notification to rider with more detailed information\n        const riderMessage = `Your ride has been assigned to driver ${driver.full_name || 'a driver'}. They will pick you up at ${ride.pickup_location} at ${pickupTime}. Driver phone: ${driver.phone}`;\n        // Send notification to driver with more detailed information\n        const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || 'your rider'} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;\n        // Send both messages in parallel for efficiency\n        const results = yield Promise.allSettled([_this2.sendSmsWithRetry(rider.phone, riderMessage), _this2.sendSmsWithRetry(driver.phone, driverMessage)]);\n        // Check results and log any failures\n        const [riderResult, driverResult] = results;\n        if (riderResult.status === 'rejected') {\n          console.error('Failed to send SMS to rider:', riderResult.reason);\n        }\n        if (driverResult.status === 'rejected') {\n          console.error('Failed to send SMS to driver:', driverResult.reason);\n        }\n        if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {\n          console.log('Ride assignment notifications sent successfully to both rider and driver');\n        } else if (riderResult.status === 'fulfilled' || driverResult.status === 'fulfilled') {\n          console.log('Ride assignment notifications sent partially (not to all recipients)');\n        } else {\n          console.error('Failed to send ride assignment notifications to any recipient');\n        }\n      } catch (error) {\n        console.error('Error sending ride assignment notifications:', error);\n        // Don't throw the error - we don't want to break the ride assignment process\n        // if SMS sending fails\n      }\n    })();\n  }\n  /**\n   * Send SMS with retry logic for better reliability\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @param maxRetries Maximum number of retry attempts\n   * @returns Promise resolving to the message SID if successful\n   */\n  sendSmsWithRetry(_x, _x2) {\n    var _this3 = this;\n    return _asyncToGenerator(function* (to, body, maxRetries = 2) {\n      let lastError;\n      for (let attempt = 0; attempt <= maxRetries; attempt++) {\n        try {\n          // Wait a bit before retrying (exponential backoff)\n          if (attempt > 0) {\n            yield new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));\n          }\n          return yield _this3.sendSms(\"+1\" + to, body);\n        } catch (error) {\n          lastError = error;\n          console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);\n        }\n      }\n      // If we get here, all attempts failed\n      throw lastError || new Error('Failed to send SMS after multiple attempts');\n    }).apply(this, arguments);\n  }\n  /**\n   * Send ride status update notifications to both rider and driver\n   * @param ride The ride that had its status updated\n   * @param newStatus The new status of the ride\n   */\n  sendRideStatusUpdateNotifications(ride, newStatus) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Skip if the ride doesn't have both rider and driver assigned\n        if (!ride.rider_id || !ride.driver_id) {\n          console.warn('Ride is missing rider or driver ID. Status update notification skipped.');\n          return;\n        }\n        // Get rider and driver information\n        const [rider, driver] = yield Promise.all([_this4.userService.getUserById(ride.rider_id), _this4.userService.getUserById(ride.driver_id)]);\n        if (!rider || !driver) {\n          throw new Error('Could not find rider or driver information');\n        }\n        // Check if phone numbers are available\n        if (!rider.phone || !driver.phone) {\n          console.warn('Phone number missing for rider or driver. Status update notification skipped.');\n          return;\n        }\n        // Create appropriate messages based on the new status\n        let riderMessage = '';\n        let driverMessage = '';\n        switch (newStatus) {\n          case 'in-progress':\n            riderMessage = `Your ride has started. Your driver ${driver.full_name || 'is'} on the way to ${ride.dropoff_location}.`;\n            driverMessage = `You have started the ride with ${rider.full_name || 'your rider'}. Destination: ${ride.dropoff_location}.`;\n            break;\n          case 'completed':\n            riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;\n            driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;\n            break;\n          case 'canceled':\n            riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;\n            driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;\n            break;\n          default:\n            // Don't send notifications for other status changes\n            return;\n        }\n        // Send both messages in parallel\n        const results = yield Promise.allSettled([_this4.sendSmsWithRetry(rider.phone, riderMessage), _this4.sendSmsWithRetry(driver.phone, driverMessage)]);\n        // Check results and log any failures\n        const [riderResult, driverResult] = results;\n        if (riderResult.status === 'rejected') {\n          console.error('Failed to send status update SMS to rider:', riderResult.reason);\n        }\n        if (driverResult.status === 'rejected') {\n          console.error('Failed to send status update SMS to driver:', driverResult.reason);\n        }\n        if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {\n          console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);\n        }\n      } catch (error) {\n        console.error('Error sending ride status update notifications:', error);\n        // Don't throw the error - we don't want to break the status update process\n        // if SMS sending fails\n      }\n    })();\n  }\n  /**\n   * Format phone number to ensure it has the international format with + prefix\n   * @param phoneNumber The phone number to format\n   * @returns Formatted phone number\n   */\n  formatPhoneNumber(phoneNumber) {\n    // If the phone number already starts with +, return it as is\n    if (phoneNumber.startsWith('+')) {\n      return phoneNumber;\n    }\n    // If it starts with a country code without +, add the +\n    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {\n      return `+${phoneNumber}`;\n    }\n    // Otherwise, assume it's a US number without country code\n    // and add +1 (you can modify this based on your target country)\n    return `+1${phoneNumber.replace(/\\D/g, '')}`;\n  }\n  static ctorParameters = () => [{\n    type: UserService\n  }, {\n    type: AuthService\n  }];\n};\nSmsService = __decorate([Injectable({\n  providedIn: 'root'\n})], SmsService);\nexport { SmsService };", "map": {"version": 3, "names": ["Injectable", "UserService", "AuthService", "SmsService", "userService", "authService", "supabase", "constructor", "sendSms", "to", "body", "_this", "_asyncToGenerator", "formattedPhone", "formatPhoneNumber", "data", "error", "functions", "invoke", "message", "from", "console", "sid", "Error", "log", "sendRideAssignmentNotifications", "ride", "driverId", "_this2", "rider", "driver", "Promise", "all", "getUserById", "rider_id", "phone", "warn", "pickupTime", "Date", "pickup_time", "toLocaleTimeString", "hour", "minute", "riderMessage", "full_name", "pickup_location", "driverMessage", "dropoff_location", "results", "allSettled", "sendSmsWithRetry", "rider<PERSON><PERSON>ult", "driver<PERSON><PERSON><PERSON>", "status", "reason", "_x", "_x2", "_this3", "maxRetries", "lastError", "attempt", "resolve", "setTimeout", "Math", "pow", "apply", "arguments", "sendRideStatusUpdateNotifications", "newStatus", "_this4", "driver_id", "phoneNumber", "startsWith", "match", "replace", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\core\\services\\sms.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { SupabaseClient } from '@supabase/supabase-js';\nimport { UserService } from './user.service';\nimport { Ride } from '../models/ride.model';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SmsService {\n  private supabase: SupabaseClient;\n\n  constructor(\n    private userService: UserService,\n    private authService: AuthService\n  ) {\n    this.supabase = authService.supabase;\n  }\n\n  /**\n   * Send an SMS notification to a user via Supabase Twilio lambda function\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @returns Promise resolving to the message SID if successful\n   */\n  async sendSms(to: string, body: string): Promise<string> {\n    try {\n      // Format phone number if needed (ensure it has the + prefix)\n\n      const formattedPhone = this.formatPhoneNumber(to);\n\n      // Call the Supabase lambda function to send the SMS\n      const { data, error } = await this.supabase.functions.invoke('twilio', {\n        body: {\n          to: formattedPhone,\n          message: body,\n          from: \"+17272025413\"\n        }\n      });\n\n      if (error) {\n        console.error('Error calling Twilio lambda function:', error);\n        throw error;\n      }\n\n      if (!data || !data.sid) {\n        throw new Error('No message SID returned from Twilio lambda function');\n      }\n\n      console.log(`SMS sent successfully to ${to}, SID: ${data.sid}`);\n      return data.sid;\n    } catch (error) {\n      console.error('Error sending SMS:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send ride assignment notifications to both rider and driver\n   * @param ride The ride that was assigned\n   * @param driverId The ID of the driver assigned to the ride\n   */\n  async sendRideAssignmentNotifications(ride: Ride, driverId: string): Promise<void> {\n    try {\n      // Get rider and driver information\n      const [rider, driver] = await Promise.all([\n        this.userService.getUserById(ride.rider_id),\n        this.userService.getUserById(driverId)\n      ]);\n\n      if (!rider || !driver) {\n        throw new Error('Could not find rider or driver information');\n      }\n\n      // Check if phone numbers are available\n      if (!rider.phone || !driver.phone) {\n        console.warn('Phone number missing for rider or driver. SMS notification skipped.');\n        return;\n      }\n\n      // Format pickup time for better readability\n      const pickupTime = new Date(ride.pickup_time).toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n\n      // Send notification to rider with more detailed information\n      const riderMessage = `Your ride has been assigned to driver ${driver.full_name || 'a driver'}. They will pick you up at ${ride.pickup_location} at ${pickupTime}. Driver phone: ${driver.phone}`;\n\n      // Send notification to driver with more detailed information\n      const driverMessage = `You have been assigned a new ride. Pick up ${rider.full_name || 'your rider'} at ${ride.pickup_location} at ${pickupTime} and drop off at ${ride.dropoff_location}. Rider phone: ${rider.phone}`;\n\n      // Send both messages in parallel for efficiency\n      const results = await Promise.allSettled([\n        this.sendSmsWithRetry(rider.phone, riderMessage),\n        this.sendSmsWithRetry(driver.phone, driverMessage)\n      ]);\n\n      // Check results and log any failures\n      const [riderResult, driverResult] = results;\n\n      if (riderResult.status === 'rejected') {\n        console.error('Failed to send SMS to rider:', riderResult.reason);\n      }\n\n      if (driverResult.status === 'rejected') {\n        console.error('Failed to send SMS to driver:', driverResult.reason);\n      }\n\n      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {\n        console.log('Ride assignment notifications sent successfully to both rider and driver');\n      } else if (riderResult.status === 'fulfilled' || driverResult.status === 'fulfilled') {\n        console.log('Ride assignment notifications sent partially (not to all recipients)');\n      } else {\n        console.error('Failed to send ride assignment notifications to any recipient');\n      }\n    } catch (error) {\n      console.error('Error sending ride assignment notifications:', error);\n      // Don't throw the error - we don't want to break the ride assignment process\n      // if SMS sending fails\n    }\n  }\n\n  /**\n   * Send SMS with retry logic for better reliability\n   * @param to Phone number to send the SMS to\n   * @param body Message body\n   * @param maxRetries Maximum number of retry attempts\n   * @returns Promise resolving to the message SID if successful\n   */\n  private async sendSmsWithRetry(to: string, body: string, maxRetries = 2): Promise<string> {\n    let lastError: any;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        // Wait a bit before retrying (exponential backoff)\n        if (attempt > 0) {\n          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));\n        }\n\n        return await this.sendSms(\"+1\"+to, body);\n      } catch (error) {\n        lastError = error;\n        console.warn(`SMS sending attempt ${attempt + 1}/${maxRetries + 1} failed:`, error);\n      }\n    }\n\n    // If we get here, all attempts failed\n    throw lastError || new Error('Failed to send SMS after multiple attempts');\n  }\n\n  /**\n   * Send ride status update notifications to both rider and driver\n   * @param ride The ride that had its status updated\n   * @param newStatus The new status of the ride\n   */\n  async sendRideStatusUpdateNotifications(ride: Ride, newStatus: string): Promise<void> {\n    try {\n      // Skip if the ride doesn't have both rider and driver assigned\n      if (!ride.rider_id || !ride.driver_id) {\n        console.warn('Ride is missing rider or driver ID. Status update notification skipped.');\n        return;\n      }\n\n      // Get rider and driver information\n      const [rider, driver] = await Promise.all([\n        this.userService.getUserById(ride.rider_id),\n        this.userService.getUserById(ride.driver_id)\n      ]);\n\n      if (!rider || !driver) {\n        throw new Error('Could not find rider or driver information');\n      }\n\n      // Check if phone numbers are available\n      if (!rider.phone || !driver.phone) {\n        console.warn('Phone number missing for rider or driver. Status update notification skipped.');\n        return;\n      }\n\n      // Create appropriate messages based on the new status\n      let riderMessage = '';\n      let driverMessage = '';\n\n      switch (newStatus) {\n        case 'in-progress':\n          riderMessage = `Your ride has started. Your driver ${driver.full_name || 'is'} on the way to ${ride.dropoff_location}.`;\n          driverMessage = `You have started the ride with ${rider.full_name || 'your rider'}. Destination: ${ride.dropoff_location}.`;\n          break;\n        case 'completed':\n          riderMessage = `Your ride to ${ride.dropoff_location} has been completed. Thank you for using Holy Rides!`;\n          driverMessage = `You have completed the ride to ${ride.dropoff_location}. Thank you for your service!`;\n          break;\n        case 'canceled':\n          riderMessage = `Your ride has been canceled. Please contact support if you did not request this cancellation.`;\n          driverMessage = `The ride to ${ride.dropoff_location} has been canceled. Please check your dashboard for new ride opportunities.`;\n          break;\n        default:\n          // Don't send notifications for other status changes\n          return;\n      }\n\n      // Send both messages in parallel\n      const results = await Promise.allSettled([\n        this.sendSmsWithRetry(rider.phone, riderMessage),\n        this.sendSmsWithRetry(driver.phone, driverMessage)\n      ]);\n\n      // Check results and log any failures\n      const [riderResult, driverResult] = results;\n\n      if (riderResult.status === 'rejected') {\n        console.error('Failed to send status update SMS to rider:', riderResult.reason);\n      }\n\n      if (driverResult.status === 'rejected') {\n        console.error('Failed to send status update SMS to driver:', driverResult.reason);\n      }\n\n      if (riderResult.status === 'fulfilled' && driverResult.status === 'fulfilled') {\n        console.log(`Ride status update (${newStatus}) notifications sent successfully to both rider and driver`);\n      }\n    } catch (error) {\n      console.error('Error sending ride status update notifications:', error);\n      // Don't throw the error - we don't want to break the status update process\n      // if SMS sending fails\n    }\n  }\n\n  /**\n   * Format phone number to ensure it has the international format with + prefix\n   * @param phoneNumber The phone number to format\n   * @returns Formatted phone number\n   */\n  private formatPhoneNumber(phoneNumber: string): string {\n    // If the phone number already starts with +, return it as is\n    if (phoneNumber.startsWith('+')) {\n      return phoneNumber;\n    }\n\n    // If it starts with a country code without +, add the +\n    if (phoneNumber.match(/^[1-9][0-9]{1,3}[0-9]{5,12}$/)) {\n      return `+${phoneNumber}`;\n    }\n\n    // Otherwise, assume it's a US number without country code\n    // and add +1 (you can modify this based on your target country)\n    return `+1${phoneNumber.replace(/\\D/g, '')}`;\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,gBAAgB;AAKrC,IAAMC,UAAU,GAAhB,MAAMA,UAAU;EAIXC,WAAA;EACAC,WAAA;EAJFC,QAAQ;EAEhBC,YACUH,WAAwB,EACxBC,WAAwB;IADxB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAEnB,IAAI,CAACC,QAAQ,GAAGD,WAAW,CAACC,QAAQ;EACtC;EAEA;;;;;;EAMME,OAAOA,CAACC,EAAU,EAAEC,IAAY;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACpC,IAAI;QACF;QAEA,MAAMC,cAAc,GAAGF,KAAI,CAACG,iBAAiB,CAACL,EAAE,CAAC;QAEjD;QACA,MAAM;UAAEM,IAAI;UAAEC;QAAK,CAAE,SAASL,KAAI,CAACL,QAAQ,CAACW,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;UACrER,IAAI,EAAE;YACJD,EAAE,EAAEI,cAAc;YAClBM,OAAO,EAAET,IAAI;YACbU,IAAI,EAAE;;SAET,CAAC;QAEF,IAAIJ,KAAK,EAAE;UACTK,OAAO,CAACL,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,MAAMA,KAAK;QACb;QAEA,IAAI,CAACD,IAAI,IAAI,CAACA,IAAI,CAACO,GAAG,EAAE;UACtB,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;QACxE;QAEAF,OAAO,CAACG,GAAG,CAAC,4BAA4Bf,EAAE,UAAUM,IAAI,CAACO,GAAG,EAAE,CAAC;QAC/D,OAAOP,IAAI,CAACO,GAAG;MACjB,CAAC,CAAC,OAAON,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,MAAMA,KAAK;MACb;IAAC;EACH;EAEA;;;;;EAKMS,+BAA+BA,CAACC,IAAU,EAAEC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MAChE,IAAI;QACF;QACA,MAAM,CAACiB,KAAK,EAAEC,MAAM,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACxCJ,MAAI,CAACxB,WAAW,CAAC6B,WAAW,CAACP,IAAI,CAACQ,QAAQ,CAAC,EAC3CN,MAAI,CAACxB,WAAW,CAAC6B,WAAW,CAACN,QAAQ,CAAC,CACvC,CAAC;QAEF,IAAI,CAACE,KAAK,IAAI,CAACC,MAAM,EAAE;UACrB,MAAM,IAAIP,KAAK,CAAC,4CAA4C,CAAC;QAC/D;QAEA;QACA,IAAI,CAACM,KAAK,CAACM,KAAK,IAAI,CAACL,MAAM,CAACK,KAAK,EAAE;UACjCd,OAAO,CAACe,IAAI,CAAC,qEAAqE,CAAC;UACnF;QACF;QAEA;QACA,MAAMC,UAAU,GAAG,IAAIC,IAAI,CAACZ,IAAI,CAACa,WAAW,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;UACnEC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;QAEF;QACA,MAAMC,YAAY,GAAG,yCAAyCb,MAAM,CAACc,SAAS,IAAI,UAAU,8BAA8BlB,IAAI,CAACmB,eAAe,OAAOR,UAAU,mBAAmBP,MAAM,CAACK,KAAK,EAAE;QAEhM;QACA,MAAMW,aAAa,GAAG,8CAA8CjB,KAAK,CAACe,SAAS,IAAI,YAAY,OAAOlB,IAAI,CAACmB,eAAe,OAAOR,UAAU,oBAAoBX,IAAI,CAACqB,gBAAgB,kBAAkBlB,KAAK,CAACM,KAAK,EAAE;QAEvN;QACA,MAAMa,OAAO,SAASjB,OAAO,CAACkB,UAAU,CAAC,CACvCrB,MAAI,CAACsB,gBAAgB,CAACrB,KAAK,CAACM,KAAK,EAAEQ,YAAY,CAAC,EAChDf,MAAI,CAACsB,gBAAgB,CAACpB,MAAM,CAACK,KAAK,EAAEW,aAAa,CAAC,CACnD,CAAC;QAEF;QACA,MAAM,CAACK,WAAW,EAAEC,YAAY,CAAC,GAAGJ,OAAO;QAE3C,IAAIG,WAAW,CAACE,MAAM,KAAK,UAAU,EAAE;UACrChC,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEmC,WAAW,CAACG,MAAM,CAAC;QACnE;QAEA,IAAIF,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;UACtChC,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEoC,YAAY,CAACE,MAAM,CAAC;QACrE;QAEA,IAAIH,WAAW,CAACE,MAAM,KAAK,WAAW,IAAID,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;UAC7EhC,OAAO,CAACG,GAAG,CAAC,0EAA0E,CAAC;QACzF,CAAC,MAAM,IAAI2B,WAAW,CAACE,MAAM,KAAK,WAAW,IAAID,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;UACpFhC,OAAO,CAACG,GAAG,CAAC,sEAAsE,CAAC;QACrF,CAAC,MAAM;UACLH,OAAO,CAACL,KAAK,CAAC,+DAA+D,CAAC;QAChF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;QACA;MACF;IAAC;EACH;EAEA;;;;;;;EAOckC,gBAAgBA,CAAAK,EAAA,EAAAC,GAAA,EAAyC;IAAA,IAAAC,MAAA;IAAA,OAAA7C,iBAAA,YAAxCH,EAAU,EAAEC,IAAY,EAAEgD,UAAU,GAAG,CAAC;MACrE,IAAIC,SAAc;MAElB,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIF,UAAU,EAAEE,OAAO,EAAE,EAAE;QACtD,IAAI;UACF;UACA,IAAIA,OAAO,GAAG,CAAC,EAAE;YACf,MAAM,IAAI7B,OAAO,CAAC8B,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEJ,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;UACpF;UAEA,aAAaH,MAAI,CAACjD,OAAO,CAAC,IAAI,GAACC,EAAE,EAAEC,IAAI,CAAC;QAC1C,CAAC,CAAC,OAAOM,KAAK,EAAE;UACd2C,SAAS,GAAG3C,KAAK;UACjBK,OAAO,CAACe,IAAI,CAAC,uBAAuBwB,OAAO,GAAG,CAAC,IAAIF,UAAU,GAAG,CAAC,UAAU,EAAE1C,KAAK,CAAC;QACrF;MACF;MAEA;MACA,MAAM2C,SAAS,IAAI,IAAIpC,KAAK,CAAC,4CAA4C,CAAC;IAAC,GAAA0C,KAAA,OAAAC,SAAA;EAC7E;EAEA;;;;;EAKMC,iCAAiCA,CAACzC,IAAU,EAAE0C,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAAzD,iBAAA;MACnE,IAAI;QACF;QACA,IAAI,CAACc,IAAI,CAACQ,QAAQ,IAAI,CAACR,IAAI,CAAC4C,SAAS,EAAE;UACrCjD,OAAO,CAACe,IAAI,CAAC,yEAAyE,CAAC;UACvF;QACF;QAEA;QACA,MAAM,CAACP,KAAK,EAAEC,MAAM,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACxCqC,MAAI,CAACjE,WAAW,CAAC6B,WAAW,CAACP,IAAI,CAACQ,QAAQ,CAAC,EAC3CmC,MAAI,CAACjE,WAAW,CAAC6B,WAAW,CAACP,IAAI,CAAC4C,SAAS,CAAC,CAC7C,CAAC;QAEF,IAAI,CAACzC,KAAK,IAAI,CAACC,MAAM,EAAE;UACrB,MAAM,IAAIP,KAAK,CAAC,4CAA4C,CAAC;QAC/D;QAEA;QACA,IAAI,CAACM,KAAK,CAACM,KAAK,IAAI,CAACL,MAAM,CAACK,KAAK,EAAE;UACjCd,OAAO,CAACe,IAAI,CAAC,+EAA+E,CAAC;UAC7F;QACF;QAEA;QACA,IAAIO,YAAY,GAAG,EAAE;QACrB,IAAIG,aAAa,GAAG,EAAE;QAEtB,QAAQsB,SAAS;UACf,KAAK,aAAa;YAChBzB,YAAY,GAAG,sCAAsCb,MAAM,CAACc,SAAS,IAAI,IAAI,kBAAkBlB,IAAI,CAACqB,gBAAgB,GAAG;YACvHD,aAAa,GAAG,kCAAkCjB,KAAK,CAACe,SAAS,IAAI,YAAY,kBAAkBlB,IAAI,CAACqB,gBAAgB,GAAG;YAC3H;UACF,KAAK,WAAW;YACdJ,YAAY,GAAG,gBAAgBjB,IAAI,CAACqB,gBAAgB,sDAAsD;YAC1GD,aAAa,GAAG,kCAAkCpB,IAAI,CAACqB,gBAAgB,+BAA+B;YACtG;UACF,KAAK,UAAU;YACbJ,YAAY,GAAG,+FAA+F;YAC9GG,aAAa,GAAG,eAAepB,IAAI,CAACqB,gBAAgB,6EAA6E;YACjI;UACF;YACE;YACA;QACJ;QAEA;QACA,MAAMC,OAAO,SAASjB,OAAO,CAACkB,UAAU,CAAC,CACvCoB,MAAI,CAACnB,gBAAgB,CAACrB,KAAK,CAACM,KAAK,EAAEQ,YAAY,CAAC,EAChD0B,MAAI,CAACnB,gBAAgB,CAACpB,MAAM,CAACK,KAAK,EAAEW,aAAa,CAAC,CACnD,CAAC;QAEF;QACA,MAAM,CAACK,WAAW,EAAEC,YAAY,CAAC,GAAGJ,OAAO;QAE3C,IAAIG,WAAW,CAACE,MAAM,KAAK,UAAU,EAAE;UACrChC,OAAO,CAACL,KAAK,CAAC,4CAA4C,EAAEmC,WAAW,CAACG,MAAM,CAAC;QACjF;QAEA,IAAIF,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;UACtChC,OAAO,CAACL,KAAK,CAAC,6CAA6C,EAAEoC,YAAY,CAACE,MAAM,CAAC;QACnF;QAEA,IAAIH,WAAW,CAACE,MAAM,KAAK,WAAW,IAAID,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;UAC7EhC,OAAO,CAACG,GAAG,CAAC,uBAAuB4C,SAAS,4DAA4D,CAAC;QAC3G;MACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE;QACA;MACF;IAAC;EACH;EAEA;;;;;EAKQF,iBAAiBA,CAACyD,WAAmB;IAC3C;IACA,IAAIA,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MAC/B,OAAOD,WAAW;IACpB;IAEA;IACA,IAAIA,WAAW,CAACE,KAAK,CAAC,8BAA8B,CAAC,EAAE;MACrD,OAAO,IAAIF,WAAW,EAAE;IAC1B;IAEA;IACA;IACA,OAAO,KAAKA,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;EAC9C;;;;;;;AA/OWvE,UAAU,GAAAwE,UAAA,EAHtB3E,UAAU,CAAC;EACV4E,UAAU,EAAE;CACb,CAAC,C,EACWzE,UAAU,CAgPtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}