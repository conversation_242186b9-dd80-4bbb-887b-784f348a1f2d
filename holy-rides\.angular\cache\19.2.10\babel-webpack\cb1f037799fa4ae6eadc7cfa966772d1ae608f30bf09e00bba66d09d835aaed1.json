{"ast": null, "code": "import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n  return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};", "map": {"version": 3, "names": ["SupabaseClient", "PostgrestError", "FunctionsHttpError", "FunctionsFetchError", "FunctionsRelayError", "FunctionsError", "FunctionRegion", "default", "createClient", "supabaseUrl", "supabase<PERSON>ey", "options"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/index.js"], "sourcesContent": ["import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError, } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion, } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n    return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,cAAc,mBAAmB;AACjC,SAASC,cAAc,QAAS,wBAAwB;AACxD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,QAAS,wBAAwB;AACtI,cAAc,uBAAuB;AACrC,SAASC,OAAO,IAAIP,cAAc,QAAQ,kBAAkB;AAC5D;AACA;AACA;AACA,OAAO,MAAMQ,YAAY,GAAGA,CAACC,WAAW,EAAEC,WAAW,EAAEC,OAAO,KAAK;EAC/D,OAAO,IAAIX,cAAc,CAACS,WAAW,EAAEC,WAAW,EAAEC,OAAO,CAAC;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}