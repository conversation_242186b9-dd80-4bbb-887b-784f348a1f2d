import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject, Observable, from, map } from 'rxjs';
import { User, UserFilter, UserRole } from '../models/user.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private supabase: SupabaseClient;
  private usersSubject = new BehaviorSubject<User[]>([]);
  users$ = this.usersSubject.asObservable();

  constructor(private authService: AuthService) {
    this.supabase = authService.supabase;
  }

  async getAllUsers(): Promise<User[]> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      this.usersSubject.next(data);
      return data;
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  }

  async getUsersByRole(role: UserRole): Promise<User[]> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('role', role)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error(`Error fetching ${role}s:`, error);
      return [];
    }
  }

  async approveDriver(userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('profiles')
        .update({
          is_approved: "TRUE",
        //  is_active: true // Automatically activate when approved
        })
        .eq('id', userId)
        ;

      if (error) {
        throw error;
      }

      // Update the local users list
      const currentUsers = this.usersSubject.value;
      const updatedUsers = currentUsers.map(user =>
        user.id === userId ? { ...user, is_approved: true, is_active: true } : user
      );
      this.usersSubject.next(updatedUsers);

      return true;
    } catch (error) {
      console.error('Error approving driver:', error);
      return false;
    }
  }

  async updateUserStatus(userId: string, isActive: boolean): Promise<boolean> {
    try {
      // First get the user's current data
      const { data: user, error: userError } = await this.supabase
        .from('profiles')
        .select('role, is_approved')
        .eq('id', userId)
        .single();

      if (userError) throw userError;

      // If trying to activate a rider/driver who isn't approved, prevent it
      if (isActive && user.role !== 'admin' && !user.is_approved) {
        throw new Error('Cannot activate unapproved user');
      }

      const { error } = await this.supabase
        .from('profiles')
        .update({
          is_approved: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      // Update the local users list
      const currentUsers = this.usersSubject.value;
      const updatedUsers = currentUsers.map(u =>
        u.id === userId ? { ...u, is_approved: isActive } : u
      );
      this.usersSubject.next(updatedUsers);

      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }

  async getUserById(userId: string): Promise<User | null> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      return null;
    }
  }

  filterUsers(users: User[], filter: UserFilter): User[] {
    return users.filter(user => {
      // Filter by role if specified
      if (filter.role && user.role !== filter.role) {
        return false;
      }

      // Filter by approval status if specified and user is a driver
      if (filter.isApproved !== undefined && user.role === 'driver') {
        if (user.is_approved !== filter.isApproved) {
          return false;
        }
      }

      // Filter by search term if specified
      if (filter.searchTerm) {
        const searchTerm = filter.searchTerm.toLowerCase();
        const fullName = user.full_name?.toLowerCase() || '';
        const email = user.email.toLowerCase();
        const phone = user.phone?.toLowerCase() || '';

        if (!fullName.includes(searchTerm) &&
            !email.includes(searchTerm) &&
            !phone.includes(searchTerm)) {
          return false;
        }
      }

      return true;
    });
  }
}
