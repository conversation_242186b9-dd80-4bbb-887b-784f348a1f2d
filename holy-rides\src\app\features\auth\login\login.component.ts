import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterLink } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { AuthService } from '../../../core/services/auth.service';
import { firstValueFrom } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    RouterLink
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent {
  loginForm: FormGroup;
  error: string = '';
  loading: boolean = false;
  private readonly MAX_ROLE_CHECK_ATTEMPTS = 3;
  private readonly RETRY_DELAY = 1000;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  async onSubmit() {
    if (this.loginForm.invalid) {
      return;
    }

    this.loading = true;
    this.error = '';

    try {
      const { error } = await this.authService.login(
        this.loginForm.value.email,
        this.loginForm.value.password
      );

      if (error) {
        this.error = error.message;
        return;
      }

      // Wait for auth state to be updated and get the user
      const user = await firstValueFrom(
        this.authService.user$.pipe(
          filter(user => user !== null)
        )
      );

      // Check if user is active
      if (user && !user.is_approved) {
        const message = user.role === 'admin' ? 
          'Your account has been deactivated. Please contact support.' :
          'Your account is pending approval. Please wait for administrator approval.';
        
        this.error = message;
        await this.authService.logout();
        return;
      }

      // Try getting the role multiple times if needed
      let role = null;
      let attempts = 0;
      while (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS) {
        console.log('Attempting to get user role...');
        role = await this.authService.getUserRole();
        if (!role && attempts < this.MAX_ROLE_CHECK_ATTEMPTS - 1) {
          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        }
        attempts++;
      }

      if (role) {
        const dashboardRoute = this.authService.getDashboardRouteForRole(role);
        await this.router.navigate([dashboardRoute]);
      } else {
        this.error = 'Unable to determine user role. Please try logging in again.';
        await this.authService.logout();
      }
    } catch (error) {
      console.error('Login error:', error);
      this.error = 'An error occurred during login. Please try again.';
    } finally {
      this.loading = false;
    }
  }
}
