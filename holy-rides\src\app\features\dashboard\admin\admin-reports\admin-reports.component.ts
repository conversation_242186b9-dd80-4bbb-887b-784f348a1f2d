import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { ChartConfiguration, ChartType } from 'chart.js';

import { StatisticsService } from '../../../../core/services/statistics.service';
import { ChartDisplayComponent } from '../../../../shared/components/chart-display/chart-display.component';
import {
  DateRange,
  RideReportData,
  RevenueReportData,
  UserActivityReportData,
  ReportOptions
} from '../../../../core/models/statistics.model';

@Component({
  selector: 'app-admin-reports',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTabsModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatSnackBarModule,
    MatTableModule,
    ChartDisplayComponent
  ],
  templateUrl: './admin-reports.component.html',
  styleUrl: './admin-reports.component.scss'
})
export class AdminReportsComponent implements OnInit {
  // Report options
  reportType: 'rides' | 'revenue' | 'users' = 'rides';
  startDate: Date = new Date();
  endDate: Date = new Date();
  userRole: string = '';
  rideStatus: string = '';

  // Report data
  rideReport: RideReportData | null = null;
  revenueReport: RevenueReportData | null = null;
  userReport: UserActivityReportData | null = null;

  // Loading state
  loading = false;

  // Chart configurations
  rideStatusChartData: any[] = [];
  rideStatusChartLabels: string[] = [];

  ridesByDayChartData: any[] = [];
  ridesByDayChartLabels: string[] = [];

  revenueByDayChartData: any[] = [];
  revenueByDayChartLabels: string[] = [];

  revenueByStatusChartData: any[] = [];
  revenueByStatusChartLabels: string[] = [];

  usersByDayChartData: any[] = [];
  usersByDayChartLabels: string[] = [];

  // Table data
  topDriversColumns: string[] = ['driverName', 'revenue', 'rideCount'];
  topRidersColumns: string[] = ['riderName', 'rideCount', 'totalSpent'];

  constructor(
    private statisticsService: StatisticsService,
    private snackBar: MatSnackBar
  ) {
    // Set default date range to last 30 days
    this.startDate = new Date();
    this.startDate.setDate(this.startDate.getDate() - 30);
    this.endDate = new Date();
  }

  ngOnInit(): void {
    this.generateReport();
  }

  async generateReport(): Promise<void> {
    if (!this.validateDateRange()) {
      this.snackBar.open('Invalid date range. End date must be after start date.', 'Close', { duration: 3000 });
      return;
    }

    this.loading = true;

    try {
      const options: ReportOptions = {
        dateRange: {
          startDate: this.startDate,
          endDate: this.endDate
        },
        reportType: this.reportType,
        userRole: this.userRole ? this.userRole as any : undefined,
        rideStatus: this.rideStatus || undefined
      };

      switch (this.reportType) {
        case 'rides':
          this.rideReport = await this.statisticsService.generateRideReport(options);
          this.revenueReport = null;
          this.userReport = null;
          this.updateRideCharts();
          break;
        case 'revenue':
          this.revenueReport = await this.statisticsService.generateRevenueReport(options);
          this.rideReport = null;
          this.userReport = null;
          this.updateRevenueCharts();
          break;
        case 'users':
          this.userReport = await this.statisticsService.generateUserActivityReport(options);
          this.rideReport = null;
          this.revenueReport = null;
          this.updateUserCharts();
          break;
      }
    } catch (error) {
      console.error('Error generating report:', error);
      this.snackBar.open('Failed to generate report', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  validateDateRange(): boolean {
    return this.startDate <= this.endDate;
  }

  updateRideCharts(): void {
    if (!this.rideReport) return;

    // Ride status chart
    this.rideStatusChartLabels = ['Requested', 'Assigned', 'In Progress', 'Completed', 'Canceled'];
    this.rideStatusChartData = [
      {
        data: [
          this.rideReport.ridesByStatus.requested,
          this.rideReport.ridesByStatus.assigned,
          this.rideReport.ridesByStatus.inProgress,
          this.rideReport.ridesByStatus.completed,
          this.rideReport.ridesByStatus.canceled
        ],
        label: 'Rides by Status',
        backgroundColor: [
          'rgba(255, 193, 7, 0.6)',  // Amber for requested
          'rgba(33, 150, 243, 0.6)',  // Blue for assigned
          'rgba(156, 39, 176, 0.6)',  // Purple for in-progress
          'rgba(76, 175, 80, 0.6)',   // Green for completed
          'rgba(244, 67, 54, 0.6)'    // Red for canceled
        ]
      }
    ];

    // Rides by day chart
    this.ridesByDayChartLabels = this.rideReport.ridesByDay.map(day => day.date);
    this.ridesByDayChartData = [
      {
        data: this.rideReport.ridesByDay.map(day => day.count),
        label: 'Rides per Day',
        backgroundColor: 'rgba(33, 150, 243, 0.6)',
        borderColor: 'rgba(33, 150, 243, 1)',
        fill: false
      }
    ];
  }

  updateRevenueCharts(): void {
    if (!this.revenueReport) return;

    // Revenue by day chart
    this.revenueByDayChartLabels = this.revenueReport.revenueByDay.map(day => day.date);
    this.revenueByDayChartData = [
      {
        data: this.revenueReport.revenueByDay.map(day => day.amount),
        label: 'Revenue per Day',
        backgroundColor: 'rgba(76, 175, 80, 0.6)',
        borderColor: 'rgba(76, 175, 80, 1)',
        fill: false
      }
    ];

    // Revenue by status chart
    this.revenueByStatusChartLabels = ['Pending', 'Paid', 'Failed', 'Refunded'];
    this.revenueByStatusChartData = [
      {
        data: [
          this.revenueReport.revenueByStatus.pending,
          this.revenueReport.revenueByStatus.paid,
          this.revenueReport.revenueByStatus.failed,
          this.revenueReport.revenueByStatus.refunded
        ],
        label: 'Revenue by Payment Status',
        backgroundColor: [
          'rgba(255, 193, 7, 0.6)',  // Amber for pending
          'rgba(76, 175, 80, 0.6)',   // Green for paid
          'rgba(244, 67, 54, 0.6)',   // Red for failed
          'rgba(158, 158, 158, 0.6)'  // Grey for refunded
        ]
      }
    ];
  }

  updateUserCharts(): void {
    if (!this.userReport) return;

    // Users by day chart
    this.usersByDayChartLabels = this.userReport.usersByDay.map(day => day.date);
    this.usersByDayChartData = [
      {
        data: this.userReport.usersByDay.map(day => day.newUsers),
        label: 'New Users',
        backgroundColor: 'rgba(33, 150, 243, 0.6)',
        borderColor: 'rgba(33, 150, 243, 1)',
        fill: false
      },
      {
        data: this.userReport.usersByDay.map(day => day.activeUsers),
        label: 'Active Users',
        backgroundColor: 'rgba(156, 39, 176, 0.6)',
        borderColor: 'rgba(156, 39, 176, 1)',
        fill: false
      }
    ];
  }

  exportReport(): void {
    let reportData: any;
    let fileName: string;

    switch (this.reportType) {
      case 'rides':
        reportData = this.rideReport;
        fileName = 'ride-report.csv';
        break;
      case 'revenue':
        reportData = this.revenueReport;
        fileName = 'revenue-report.csv';
        break;
      case 'users':
        reportData = this.userReport;
        fileName = 'user-activity-report.csv';
        break;
      default:
        this.snackBar.open('No report data to export', 'Close', { duration: 3000 });
        return;
    }

    if (!reportData) {
      this.snackBar.open('No report data to export', 'Close', { duration: 3000 });
      return;
    }

    const csvContent = this.statisticsService.exportReportToCsv(this.reportType, reportData);
    this.downloadCsv(csvContent, fileName);
  }

  downloadCsv(csvContent: string, fileName: string): void {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  printReport(): void {
    window.print();
  }
}
