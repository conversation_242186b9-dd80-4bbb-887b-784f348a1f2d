.spacer {
  flex: 1 1 auto;
}

mat-toolbar {
  margin-bottom: 20px;
}

.logo-container {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  cursor: pointer;
}

.logo {
  height: 40px;
  margin-right: 10px;
  filter: brightness(0) invert(1); /* This will make the logo white */
}

.notification-prompt {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  padding: 16px;
}

.notification-prompt mat-card {
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.notification-prompt h3 {
  margin: 0 0 8px;
  font-size: 18px;
}

.notification-prompt p {
  margin: 0 0 16px;
  color: rgba(0, 0, 0, 0.6);
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
}