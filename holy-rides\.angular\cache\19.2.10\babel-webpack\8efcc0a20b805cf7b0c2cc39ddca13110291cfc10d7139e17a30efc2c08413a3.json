{"ast": null, "code": "import { AuthClient } from '@supabase/auth-js';\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options) {\n    super(options);\n  }\n}", "map": {"version": 3, "names": ["AuthClient", "SupabaseAuthClient", "constructor", "options"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js"], "sourcesContent": ["import { AuthClient } from '@supabase/auth-js';\nexport class SupabaseAuthClient extends AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,MAAMC,kBAAkB,SAASD,UAAU,CAAC;EAC/CE,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;EAClB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}