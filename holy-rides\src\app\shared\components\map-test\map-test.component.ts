import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MapDisplayComponent } from '../map-display/map-display.component';

@Component({
  selector: 'app-map-test',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MapDisplayComponent
  ],
  template: `
    <mat-card>
      <mat-card-header>
        <mat-card-title>Google Maps Test</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <app-map-display
          [origin]="'New York, NY'"
          [destination]="'Boston, MA'">
        </app-map-display>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    mat-card {
      margin: 16px;
    }
  `]
})
export class MapTestComponent {
  // This is a simple test component to verify that our Google Maps implementation works
}
