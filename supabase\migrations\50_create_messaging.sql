-- Create message_threads table
CREATE TABLE IF NOT EXISTS message_threads (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ride_id UUID REFERENCES rides(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  thread_id UUID REFERENCES message_threads(id) NOT NULL,
  sender_id UUID REFERENCES profiles(id) NOT NULL,
  receiver_id UUID REFERENCES profiles(id) NOT NULL,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Set up Row Level Security (RLS)
ALTER TABLE message_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create policies for message_threads
CREATE POLICY "Users can view message threads they are part of."
  ON message_threads FOR SELECT
  USING (
    auth.uid() IN (
      SELECT rider_id FROM rides WHERE id = ride_id
      UNION
      SELECT driver_id FROM rides WHERE id = ride_id
    )
  );

CREATE POLICY "Users can create message threads for their rides."
  ON message_threads FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT rider_id FROM rides WHERE id = ride_id
      UNION
      SELECT driver_id FROM rides WHERE id = ride_id
    )
  );

-- Create policies for messages
CREATE POLICY "Users can view messages they sent or received."
  ON messages FOR SELECT
  USING (
    auth.uid() = sender_id OR auth.uid() = receiver_id
  );

CREATE POLICY "Users can insert messages they are sending."
  ON messages FOR INSERT
  WITH CHECK (
    auth.uid() = sender_id
  );

CREATE POLICY "Users can update read status of messages they received."
  ON messages FOR UPDATE
  USING (
    auth.uid() = receiver_id
  )
  WITH CHECK (
    auth.uid() = receiver_id AND
    (OLD.is_read IS DISTINCT FROM NEW.is_read)
  );

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_message_threads_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER message_threads_updated_at
  BEFORE UPDATE ON message_threads
  FOR EACH ROW
  EXECUTE PROCEDURE update_message_threads_updated_at();

CREATE OR REPLACE FUNCTION update_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER messages_updated_at
  BEFORE UPDATE ON messages
  FOR EACH ROW
  EXECUTE PROCEDURE update_messages_updated_at();

-- Create a function to update the thread's updated_at when a new message is added
CREATE OR REPLACE FUNCTION update_thread_timestamp_on_message()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE message_threads
  SET updated_at = now()
  WHERE id = NEW.thread_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_thread_timestamp
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE PROCEDURE update_thread_timestamp_on_message();
