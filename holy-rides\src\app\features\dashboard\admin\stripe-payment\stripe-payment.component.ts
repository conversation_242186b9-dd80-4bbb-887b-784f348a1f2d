import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { environment } from '../../../../../environments/environment';
import { PaymentService } from '../../../../core/services/payment.service';
import { loadStripe, StripeConstructor } from '@stripe/stripe-js';
import { RideService } from '../../../../core/services/ride.service';
import { Ride } from '../../../../core/models/ride.model';
import { AuthService } from '../../../../core/services/auth.service';

declare global {
  interface Window {
    Stripe?: StripeConstructor;
  }
}

@Component({
  selector: 'app-stripe-payment',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatIconModule,
    MatTableModule
  ],
  template: `
    <div class="container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Stripe Payment Processing</mat-card-title>
          <mat-card-subtitle>Process payments securely with Stripe</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="sdk-status" *ngIf="!sdkLoaded">
            <p>Loading Stripe SDK...</p>
            <mat-spinner diameter="30"></mat-spinner>
          </div>

          <div class="payment-tabs" *ngIf="sdkLoaded">
            <div class="payment-section">
              <h3>Process a Payment</h3>
              <p>Use this form to process a one-time payment</p>

              <form [formGroup]="paymentForm" (ngSubmit)="processPayment()">
                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Amount (USD)</mat-label>
                    <input matInput type="number" formControlName="amount" min="1" step="0.01">
                    <mat-error *ngIf="paymentForm.get('amount')?.hasError('required')">
                      Amount is required
                    </mat-error>
                    <mat-error *ngIf="paymentForm.get('amount')?.hasError('min')">
                      Amount must be at least $1
                    </mat-error>
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <mat-form-field appearance="outline">
                    <mat-label>Description</mat-label>
                    <input matInput formControlName="description" placeholder="Payment description">
                  </mat-form-field>
                </div>

                <div class="form-row">
                  <div #cardElement class="card-element"></div>
                  <div class="card-errors" *ngIf="cardError">{{ cardError }}</div>
                </div>

                <div class="form-actions">
                  <button mat-raised-button color="primary" type="submit" [disabled]="paymentForm.invalid || processing">
                    <mat-icon>payment</mat-icon>
                    {{ processing ? 'Processing...' : 'Process Payment' }}
                  </button>
                </div>
              </form>
            </div>
<!-- 
            <mat-divider class="divider"></mat-divider>

            <div class="recent-transactions">
              <h3>Recent Transactions</h3>
              <p *ngIf="!recentTransactions || recentTransactions.length === 0">No recent transactions to display</p>

              <table mat-table [dataSource]="recentTransactions" *ngIf="recentTransactions && recentTransactions.length > 0">
                <ng-container matColumnDef="date">
                  <th mat-header-cell *matHeaderCellDef>Date</th>
                  <td mat-cell *matCellDef="let transaction">{{transaction.date | date:'short'}}</td>
                </ng-container>

                <ng-container matColumnDef="amount">
                  <th mat-header-cell *matHeaderCellDef>Amount</th>
                  <td mat-cell *matCellDef="let transaction">  {{ '$'+transaction.amount.toFixed(2)}}</td>
                </ng-container>

                <ng-container matColumnDef="description">
                  <th mat-header-cell *matHeaderCellDef>Description</th>
                  <td mat-cell *matCellDef="let transaction">{{transaction.description}}</td>
                </ng-container>

                <ng-container matColumnDef="status">
                  <th mat-header-cell *matHeaderCellDef>Status</th>
                  <td mat-cell *matCellDef="let transaction">
                    <span [ngClass]="'status-' + transaction.status.toLowerCase()">
                      {{transaction.status}}
                    </span>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="['date', 'amount', 'description', 'status']"></tr>
                <tr mat-row *matRowDef="let row; columns: ['date', 'amount', 'description', 'status'];"></tr>
              </table>
            </div> -->
          </div>

          <!-- <mat-divider *ngIf="paymentResult" class="divider"></mat-divider>

          <div class="payment-result" *ngIf="paymentResult">
            <h3>Payment Result</h3>
            <div class="result-card" [ngClass]="{'success': paymentResult.success, 'error': !paymentResult.success}">
              <mat-icon>{{paymentResult.success ? 'check_circle' : 'error'}}</mat-icon>
              <div class="result-message">
                <h4>{{paymentResult.success ? 'Payment Successful' : 'Payment Failed'}}</h4>
                <p>{{paymentResult.success ? 'Transaction completed successfully' : paymentResult.error?.message}}</p>
              </div>
            </div>
          </div> -->
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .container {
      padding: 20px;
    }

    .form-row {
      margin-bottom: 20px;
    }

    mat-form-field {
      width: 100%;
    }

    .card-element {
      border: 1px solid #ccc;
      padding: 10px;
      border-radius: 4px;
      height: 40px;
      background-color: white;
    }

    .card-errors {
      color: #f44336;
      margin-top: 8px;
      font-size: 14px;
    }

    .form-actions {
      margin-top: 20px;
      margin-bottom: 20px;
    }

    .divider {
      margin: 30px 0;
    }

    .payment-result {
      margin-top: 20px;
    }

    .payment-result pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }

    .payment-section {
      margin-bottom: 30px;
    }

    .payment-tabs {
      margin-top: 20px;
    }

    .recent-transactions {
      margin-top: 30px;
    }

    table {
      width: 100%;
    }

    .status-succeeded, .status-completed {
      color: #4caf50;
      font-weight: 500;
    }

    .status-pending {
      color: #ff9800;
      font-weight: 500;
    }

    .status-failed, .status-refunded {
      color: #f44336;
      font-weight: 500;
    }

    .result-card {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 4px;
      margin-top: 10px;
    }

    .result-card.success {
      background-color: rgba(76, 175, 80, 0.1);
    }

    .result-card.error {
      background-color: rgba(244, 67, 54, 0.1);
    }

    .result-card mat-icon {
      margin-right: 15px;
      font-size: 24px;
      height: 24px;
      width: 24px;
    }

    .result-card.success mat-icon {
      color: #4caf50;
    }

    .result-card.error mat-icon {
      color: #f44336;
    }

    .result-message h4 {
      margin: 0 0 5px 0;
    }

    .result-message p {
      margin: 0;
    }

    .sdk-status {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 40px;
    }
  `]
})
export class StripePaymentComponent implements OnInit, AfterViewInit {
  @ViewChild('cardElement') cardElement!: ElementRef;

  paymentForm: FormGroup;
  stripe: any;
  card: any;
  sdkLoaded = false;
  processing = false;
  cardError = '';
  paymentResult: any = null;

  // Transaction history
  recentTransactions: any[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar,
    private paymentService: PaymentService,
    private rideService: RideService,
    private authService: AuthService
  ) {
    this.paymentForm = this.formBuilder.group({
      amount: [10.00, [Validators.required, Validators.min(1)]],
      description: ['', Validators.required]
    });
  }

  async ngOnInit() {
    // this.loadStripeScript();
    await this.loadStripe();

    // Load recent transactions
   // await this.loadRecentTransactions();
  }

  ngAfterViewInit(): void {
    // Card element will be initialized after Stripe script is loaded
    if (this.sdkLoaded) {
      this.initializeCard();
    }
  }

  async loadStripe(): Promise<void> {
    try {
      const stripe = await loadStripe(environment.stripePublishableKey);
      this.stripe = stripe;
      this.sdkLoaded = true;

      // Initialize card element after a short delay to ensure DOM is ready
      setTimeout(() => this.initializeCard(), 100);
    } catch (error) {
      console.error('Error loading Stripe:', error);
      this.snackBar.open('Error loading Stripe. Please check your API keys.', 'Close', { duration: 5000 });
    }
  }

  loadStripeScript(): void {
    if (window.Stripe) {
      this.initializeStripe();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    script.onload = () => {
      this.initializeStripe();
    };
    document.body.appendChild(script);
  }

  initializeStripe(): void {
    if (!window.Stripe) {
      this.snackBar.open('Stripe SDK not available', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Initialize Stripe with the publishable key from environment
      this.stripe = window.Stripe(environment.stripePublishableKey);

      // Initialize card element after a short delay to ensure DOM is ready
      setTimeout(() => this.initializeCard(), 100);
    } catch (error) {
      console.error('Error initializing Stripe:', error);
      this.snackBar.open('Error initializing Stripe payments. Check your credentials.', 'Close', { duration: 5000 });
    }
  }

  initializeCard(): void {
    if (!this.cardElement || !this.cardElement.nativeElement || !this.stripe) {
      setTimeout(() => this.initializeCard(), 100);
      return;
    }

    try {
      const elements = this.stripe.elements();

      // Create card element
      this.card = elements.create('card', {
        style: {
          base: {
            iconColor: '#666EE8',
            color: '#31325F',
            fontWeight: 400,
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSize: '16px',
            '::placeholder': {
              color: '#CFD7E0'
            }
          }
        }
      });

      // Mount the card element
      this.card.mount(this.cardElement.nativeElement);

      // Handle card element errors
      this.card.on('change', (event: any) => {
        this.cardError = event.error ? event.error.message : '';
      });

      this.sdkLoaded = true;
    } catch (error) {
      console.error('Error initializing Stripe card:', error);
      this.snackBar.open('Error initializing Stripe card form', 'Close', { duration: 5000 });
    }
  }

  /**
   * Load recent transactions from the database
   */
  async loadRecentTransactions(): Promise<void> {
    try {
      // Call the Supabase Stripe edge function to get recent transactions
      const { data, error } = await this.authService.supabase.functions.invoke('stripe', {
        body: {
          action: 'listPaymentIntents',
          limit: 10 // Get the 10 most recent transactions
        }
      });

      if (error) {
        console.error('Error loading recent transactions:', error);
        this.snackBar.open('Error loading recent transactions', 'Close', { duration: 3000 });
        return;
      }

      if (data && data.paymentIntents) {
        // Format the transactions for display
        this.recentTransactions = data.paymentIntents.map((pi: any) => ({
          id: pi.id,
          date: new Date(pi.created * 1000), // Convert from Unix timestamp
          amount: pi.amount / 100, // Convert from cents to dollars
          description: pi.description || 'No description',
          status: this.formatPaymentStatus(pi.status)
        }));
      }
    } catch (error) {
      console.error('Error loading recent transactions:', error);
      this.snackBar.open('Error loading recent transactions', 'Close', { duration: 3000 });
    }
  }

  /**
   * Format payment status for display
   */
  private formatPaymentStatus(status: string): string {
    // Capitalize the first letter
    return status.charAt(0).toUpperCase() + status.slice(1);
  }
  /**
   * Process a payment using Stripe
   */
  async processPayment(): Promise<void> {
    if (this.paymentForm.invalid || !this.card || !this.stripe) {
      return;
    }

    this.processing = true;
    this.paymentResult = null;
    const amount = this.paymentForm.get('amount')?.value;
    const description = this.paymentForm.get('description')?.value;

    try {
      // Step 1: Create a payment method with the card details
      const { paymentMethod, error: paymentMethodError } = await this.stripe.createPaymentMethod({
        type: 'card',
        card: this.card,
      });

      if (paymentMethodError) {
        throw paymentMethodError;
      }
        let payment = {
        amount: amount * 100, // Stripe uses cents
        currency: 'usd',
        description: description,
        payment_method: paymentMethod.id
      };
        console.log(payment)
      // Step 2: Create a payment intent using Supabase Stripe edge function
      const { data, error } = await this.authService.supabase.functions.invoke('stripe', {
        body:payment });

      if (error) {
        console.error('Error creating payment intent:', error);
        throw new Error(`Failed to create payment intent: ${error.message}`);
      }
        console.log('Payment intent created:', data);

      if (!data || !data.client_secret) {
        throw new Error('No client secret returned from payment intent creation');
      }

      const clientSecret = data.client_secret;

      // Step 3: Confirm the payment with the client secret
      const { error: confirmError, paymentIntent } = await this.stripe.confirmCardPayment(clientSecret, {
        payment_method: paymentMethod.id
      });

      if (confirmError) {
        throw confirmError;
      }

      // Payment succeeded
      this.paymentResult = {
        success: true,
        paymentIntent: paymentIntent
      };

      // Reload the transactions to include the new one
      //await this.loadRecentTransactions();

      this.snackBar.open('Payment processed successfully!', 'Close', { duration: 3000 });

      // Reset the form
      this.paymentForm.reset({
        amount: 10.00
      });

      // Clear the card field
      this.card.clear();

    } catch (error: any) {
      console.error('Error processing payment:', error);
      this.paymentResult = {
        success: false,
        error: {
          message: error.message
        }
      };
      this.snackBar.open(`Payment error: ${error.message}`, 'Close', { duration: 5000 });
    } finally {
      this.processing = false;
    }
  }
}