{"version": 3, "sources": ["../../../../../../node_modules/@angular/animations/fesm2022/util-D9FfmVnv.mjs", "../../../../../../node_modules/@angular/animations/fesm2022/browser.mjs", "../../../../../../node_modules/@angular/platform-browser/fesm2022/animations.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { AnimationGroupPlayer, NoopAnimationPlayer, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationMetadataType, sequence } from './private_export-faY_wCkZ.mjs';\nimport { ɵRuntimeError as _RuntimeError } from '@angular/core';\nconst LINE_START = '\\n - ';\nfunction invalidTimingValue(exp) {\n  return new _RuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value \"${exp}\" is invalid.`);\n}\nfunction negativeStepValue() {\n  return new _RuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');\n}\nfunction negativeDelayValue() {\n  return new _RuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');\n}\nfunction invalidStyleParams(varName) {\n  return new _RuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode && `Unable to resolve the local animation param ${varName} in the given list of values`);\n}\nfunction invalidParamValue(varName) {\n  return new _RuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);\n}\nfunction invalidNodeType(nodeType) {\n  return new _RuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);\n}\nfunction invalidCssUnitValue(userProvidedProperty, value) {\n  return new _RuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);\n}\nfunction invalidTrigger() {\n  return new _RuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode && \"animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))\");\n}\nfunction invalidDefinition() {\n  return new _RuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');\n}\nfunction invalidState(metadataName, missingSubs) {\n  return new _RuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode && `state(\"${metadataName}\", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);\n}\nfunction invalidStyleValue(value) {\n  return new _RuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);\n}\nfunction invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {\n  return new _RuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode && `The CSS property \"${prop}\" that exists between the times of \"${firstStart}ms\" and \"${firstEnd}ms\" is also being animated in a parallel animation between the times of \"${secondStart}ms\" and \"${secondEnd}ms\"`);\n}\nfunction invalidKeyframes() {\n  return new _RuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);\n}\nfunction invalidOffset() {\n  return new _RuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);\n}\nfunction keyframeOffsetsOutOfOrder() {\n  return new _RuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);\n}\nfunction keyframesMissingOffsets() {\n  return new _RuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);\n}\nfunction invalidStagger() {\n  return new _RuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);\n}\nfunction invalidQuery(selector) {\n  return new _RuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode && `\\`query(\"${selector}\")\\` returned zero elements. (Use \\`query(\"${selector}\", { optional: true })\\` if you wish to allow this.)`);\n}\nfunction invalidExpression(expr) {\n  return new _RuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression \"${expr}\" is not supported`);\n}\nfunction invalidTransitionAlias(alias) {\n  return new _RuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value \"${alias}\" is not supported`);\n}\nfunction validationFailed(errors) {\n  return new _RuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction buildingFailed(errors) {\n  return new _RuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\\n${errors.map(err => err.message).join('\\n')}`);\n}\nfunction triggerBuildFailed(name, errors) {\n  return new _RuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode && `The animation trigger \"${name}\" has failed to build due to the following errors:\\n - ${errors.map(err => err.message).join('\\n - ')}`);\n}\nfunction animationFailed(errors) {\n  return new _RuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode && `Unable to animate due to the following errors:${LINE_START}${errors.map(err => err.message).join(LINE_START)}`);\n}\nfunction registerFailed(errors) {\n  return new _RuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode && `Unable to build the animation due to the following errors: ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingOrDestroyedAnimation() {\n  return new _RuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && \"The requested animation doesn't exist or has already been destroyed\");\n}\nfunction createAnimationFailed(errors) {\n  return new _RuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode && `Unable to create the animation due to the following errors:${errors.map(err => err.message).join('\\n')}`);\n}\nfunction missingPlayer(id) {\n  return new _RuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);\n}\nfunction missingTrigger(phase, name) {\n  return new _RuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode && `Unable to listen on the animation trigger event \"${phase}\" because the animation trigger \"${name}\" doesn\\'t exist!`);\n}\nfunction missingEvent(name) {\n  return new _RuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode && `Unable to listen on the animation trigger \"${name}\" because the provided event is undefined!`);\n}\nfunction unsupportedTriggerEvent(phase, name) {\n  return new _RuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode && `The provided animation trigger event \"${phase}\" for the animation trigger \"${name}\" is not supported!`);\n}\nfunction unregisteredTrigger(name) {\n  return new _RuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger \"${name}\" has not been registered!`);\n}\nfunction triggerTransitionsFailed(errors) {\n  return new _RuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode && `Unable to process animations due to the following failed trigger transitions\\n ${errors.map(err => err.message).join('\\n')}`);\n}\nfunction transitionFailed(name, errors) {\n  return new _RuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\\n ${errors.map(err => err.message).join('\\n- ')}`);\n}\n\n/**\n * Set of all animatable CSS properties\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties\n */\nconst ANIMATABLE_PROP_SET = new Set(['-moz-outline-radius', '-moz-outline-radius-bottomleft', '-moz-outline-radius-bottomright', '-moz-outline-radius-topleft', '-moz-outline-radius-topright', '-ms-grid-columns', '-ms-grid-rows', '-webkit-line-clamp', '-webkit-text-fill-color', '-webkit-text-stroke', '-webkit-text-stroke-color', 'accent-color', 'all', 'backdrop-filter', 'background', 'background-color', 'background-position', 'background-size', 'block-size', 'border', 'border-block-end', 'border-block-end-color', 'border-block-end-width', 'border-block-start', 'border-block-start-color', 'border-block-start-width', 'border-bottom', 'border-bottom-color', 'border-bottom-left-radius', 'border-bottom-right-radius', 'border-bottom-width', 'border-color', 'border-end-end-radius', 'border-end-start-radius', 'border-image-outset', 'border-image-slice', 'border-image-width', 'border-inline-end', 'border-inline-end-color', 'border-inline-end-width', 'border-inline-start', 'border-inline-start-color', 'border-inline-start-width', 'border-left', 'border-left-color', 'border-left-width', 'border-radius', 'border-right', 'border-right-color', 'border-right-width', 'border-start-end-radius', 'border-start-start-radius', 'border-top', 'border-top-color', 'border-top-left-radius', 'border-top-right-radius', 'border-top-width', 'border-width', 'bottom', 'box-shadow', 'caret-color', 'clip', 'clip-path', 'color', 'column-count', 'column-gap', 'column-rule', 'column-rule-color', 'column-rule-width', 'column-width', 'columns', 'filter', 'flex', 'flex-basis', 'flex-grow', 'flex-shrink', 'font', 'font-size', 'font-size-adjust', 'font-stretch', 'font-variation-settings', 'font-weight', 'gap', 'grid-column-gap', 'grid-gap', 'grid-row-gap', 'grid-template-columns', 'grid-template-rows', 'height', 'inline-size', 'input-security', 'inset', 'inset-block', 'inset-block-end', 'inset-block-start', 'inset-inline', 'inset-inline-end', 'inset-inline-start', 'left', 'letter-spacing', 'line-clamp', 'line-height', 'margin', 'margin-block-end', 'margin-block-start', 'margin-bottom', 'margin-inline-end', 'margin-inline-start', 'margin-left', 'margin-right', 'margin-top', 'mask', 'mask-border', 'mask-position', 'mask-size', 'max-block-size', 'max-height', 'max-inline-size', 'max-lines', 'max-width', 'min-block-size', 'min-height', 'min-inline-size', 'min-width', 'object-position', 'offset', 'offset-anchor', 'offset-distance', 'offset-path', 'offset-position', 'offset-rotate', 'opacity', 'order', 'outline', 'outline-color', 'outline-offset', 'outline-width', 'padding', 'padding-block-end', 'padding-block-start', 'padding-bottom', 'padding-inline-end', 'padding-inline-start', 'padding-left', 'padding-right', 'padding-top', 'perspective', 'perspective-origin', 'right', 'rotate', 'row-gap', 'scale', 'scroll-margin', 'scroll-margin-block', 'scroll-margin-block-end', 'scroll-margin-block-start', 'scroll-margin-bottom', 'scroll-margin-inline', 'scroll-margin-inline-end', 'scroll-margin-inline-start', 'scroll-margin-left', 'scroll-margin-right', 'scroll-margin-top', 'scroll-padding', 'scroll-padding-block', 'scroll-padding-block-end', 'scroll-padding-block-start', 'scroll-padding-bottom', 'scroll-padding-inline', 'scroll-padding-inline-end', 'scroll-padding-inline-start', 'scroll-padding-left', 'scroll-padding-right', 'scroll-padding-top', 'scroll-snap-coordinate', 'scroll-snap-destination', 'scrollbar-color', 'shape-image-threshold', 'shape-margin', 'shape-outside', 'tab-size', 'text-decoration', 'text-decoration-color', 'text-decoration-thickness', 'text-emphasis', 'text-emphasis-color', 'text-indent', 'text-shadow', 'text-underline-offset', 'top', 'transform', 'transform-origin', 'translate', 'vertical-align', 'visibility', 'width', 'word-spacing', 'z-index', 'zoom']);\nfunction optimizeGroupPlayer(players) {\n  switch (players.length) {\n    case 0:\n      return new NoopAnimationPlayer();\n    case 1:\n      return players[0];\n    default:\n      return new AnimationGroupPlayer(players);\n  }\n}\nfunction normalizeKeyframes$1(normalizer, keyframes, preStyles = new Map(), postStyles = new Map()) {\n  const errors = [];\n  const normalizedKeyframes = [];\n  let previousOffset = -1;\n  let previousKeyframe = null;\n  keyframes.forEach(kf => {\n    const offset = kf.get('offset');\n    const isSameOffset = offset == previousOffset;\n    const normalizedKeyframe = isSameOffset && previousKeyframe || new Map();\n    kf.forEach((val, prop) => {\n      let normalizedProp = prop;\n      let normalizedValue = val;\n      if (prop !== 'offset') {\n        normalizedProp = normalizer.normalizePropertyName(normalizedProp, errors);\n        switch (normalizedValue) {\n          case _PRE_STYLE:\n            normalizedValue = preStyles.get(prop);\n            break;\n          case AUTO_STYLE:\n            normalizedValue = postStyles.get(prop);\n            break;\n          default:\n            normalizedValue = normalizer.normalizeStyleValue(prop, normalizedProp, normalizedValue, errors);\n            break;\n        }\n      }\n      normalizedKeyframe.set(normalizedProp, normalizedValue);\n    });\n    if (!isSameOffset) {\n      normalizedKeyframes.push(normalizedKeyframe);\n    }\n    previousKeyframe = normalizedKeyframe;\n    previousOffset = offset;\n  });\n  if (errors.length) {\n    throw animationFailed(errors);\n  }\n  return normalizedKeyframes;\n}\nfunction listenOnPlayer(player, eventName, event, callback) {\n  switch (eventName) {\n    case 'start':\n      player.onStart(() => callback(event && copyAnimationEvent(event, 'start', player)));\n      break;\n    case 'done':\n      player.onDone(() => callback(event && copyAnimationEvent(event, 'done', player)));\n      break;\n    case 'destroy':\n      player.onDestroy(() => callback(event && copyAnimationEvent(event, 'destroy', player)));\n      break;\n  }\n}\nfunction copyAnimationEvent(e, phaseName, player) {\n  const totalTime = player.totalTime;\n  const disabled = player.disabled ? true : false;\n  const event = makeAnimationEvent(e.element, e.triggerName, e.fromState, e.toState, phaseName || e.phaseName, totalTime == undefined ? e.totalTime : totalTime, disabled);\n  const data = e['_data'];\n  if (data != null) {\n    event['_data'] = data;\n  }\n  return event;\n}\nfunction makeAnimationEvent(element, triggerName, fromState, toState, phaseName = '', totalTime = 0, disabled) {\n  return {\n    element,\n    triggerName,\n    fromState,\n    toState,\n    phaseName,\n    totalTime,\n    disabled: !!disabled\n  };\n}\nfunction getOrSetDefaultValue(map, key, defaultValue) {\n  let value = map.get(key);\n  if (!value) {\n    map.set(key, value = defaultValue);\n  }\n  return value;\n}\nfunction parseTimelineCommand(command) {\n  const separatorPos = command.indexOf(':');\n  const id = command.substring(1, separatorPos);\n  const action = command.slice(separatorPos + 1);\n  return [id, action];\n}\nconst documentElement = /* @__PURE__ */(() => typeof document === 'undefined' ? null : document.documentElement)();\nfunction getParentElement(element) {\n  const parent = element.parentNode || element.host || null; // consider host to support shadow DOM\n  if (parent === documentElement) {\n    return null;\n  }\n  return parent;\n}\nfunction containsVendorPrefix(prop) {\n  // Webkit is the only real popular vendor prefix nowadays\n  // cc: http://shouldiprefix.com/\n  return prop.substring(1, 6) == 'ebkit'; // webkit or Webkit\n}\nlet _CACHED_BODY = null;\nlet _IS_WEBKIT = false;\nfunction validateStyleProperty(prop) {\n  if (!_CACHED_BODY) {\n    _CACHED_BODY = getBodyNode() || {};\n    _IS_WEBKIT = _CACHED_BODY.style ? 'WebkitAppearance' in _CACHED_BODY.style : false;\n  }\n  let result = true;\n  if (_CACHED_BODY.style && !containsVendorPrefix(prop)) {\n    result = prop in _CACHED_BODY.style;\n    if (!result && _IS_WEBKIT) {\n      const camelProp = 'Webkit' + prop.charAt(0).toUpperCase() + prop.slice(1);\n      result = camelProp in _CACHED_BODY.style;\n    }\n  }\n  return result;\n}\nfunction validateWebAnimatableStyleProperty(prop) {\n  return ANIMATABLE_PROP_SET.has(prop);\n}\nfunction getBodyNode() {\n  if (typeof document != 'undefined') {\n    return document.body;\n  }\n  return null;\n}\nfunction containsElement(elm1, elm2) {\n  while (elm2) {\n    if (elm2 === elm1) {\n      return true;\n    }\n    elm2 = getParentElement(elm2);\n  }\n  return false;\n}\nfunction invokeQuery(element, selector, multi) {\n  if (multi) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n  const elem = element.querySelector(selector);\n  return elem ? [elem] : [];\n}\nconst ONE_SECOND = 1000;\nconst SUBSTITUTION_EXPR_START = '{{';\nconst SUBSTITUTION_EXPR_END = '}}';\nconst ENTER_CLASSNAME = 'ng-enter';\nconst LEAVE_CLASSNAME = 'ng-leave';\nconst NG_TRIGGER_CLASSNAME = 'ng-trigger';\nconst NG_TRIGGER_SELECTOR = '.ng-trigger';\nconst NG_ANIMATING_CLASSNAME = 'ng-animating';\nconst NG_ANIMATING_SELECTOR = '.ng-animating';\nfunction resolveTimingValue(value) {\n  if (typeof value == 'number') return value;\n  const matches = value.match(/^(-?[\\.\\d]+)(m?s)/);\n  if (!matches || matches.length < 2) return 0;\n  return _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n}\nfunction _convertTimeValueToMS(value, unit) {\n  switch (unit) {\n    case 's':\n      return value * ONE_SECOND;\n    default:\n      // ms or something else\n      return value;\n  }\n}\nfunction resolveTiming(timings, errors, allowNegativeValues) {\n  return timings.hasOwnProperty('duration') ? timings : parseTimeExpression(timings, errors, allowNegativeValues);\n}\nfunction parseTimeExpression(exp, errors, allowNegativeValues) {\n  const regex = /^(-?[\\.\\d]+)(m?s)(?:\\s+(-?[\\.\\d]+)(m?s))?(?:\\s+([-a-z]+(?:\\(.+?\\))?))?$/i;\n  let duration;\n  let delay = 0;\n  let easing = '';\n  if (typeof exp === 'string') {\n    const matches = exp.match(regex);\n    if (matches === null) {\n      errors.push(invalidTimingValue(exp));\n      return {\n        duration: 0,\n        delay: 0,\n        easing: ''\n      };\n    }\n    duration = _convertTimeValueToMS(parseFloat(matches[1]), matches[2]);\n    const delayMatch = matches[3];\n    if (delayMatch != null) {\n      delay = _convertTimeValueToMS(parseFloat(delayMatch), matches[4]);\n    }\n    const easingVal = matches[5];\n    if (easingVal) {\n      easing = easingVal;\n    }\n  } else {\n    duration = exp;\n  }\n  if (!allowNegativeValues) {\n    let containsErrors = false;\n    let startIndex = errors.length;\n    if (duration < 0) {\n      errors.push(negativeStepValue());\n      containsErrors = true;\n    }\n    if (delay < 0) {\n      errors.push(negativeDelayValue());\n      containsErrors = true;\n    }\n    if (containsErrors) {\n      errors.splice(startIndex, 0, invalidTimingValue(exp));\n    }\n  }\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction normalizeKeyframes(keyframes) {\n  if (!keyframes.length) {\n    return [];\n  }\n  if (keyframes[0] instanceof Map) {\n    return keyframes;\n  }\n  return keyframes.map(kf => new Map(Object.entries(kf)));\n}\nfunction normalizeStyles(styles) {\n  return Array.isArray(styles) ? new Map(...styles) : new Map(styles);\n}\nfunction setStyles(element, styles, formerStyles) {\n  styles.forEach((val, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    if (formerStyles && !formerStyles.has(prop)) {\n      formerStyles.set(prop, element.style[camelProp]);\n    }\n    element.style[camelProp] = val;\n  });\n}\nfunction eraseStyles(element, styles) {\n  styles.forEach((_, prop) => {\n    const camelProp = dashCaseToCamelCase(prop);\n    element.style[camelProp] = '';\n  });\n}\nfunction normalizeAnimationEntry(steps) {\n  if (Array.isArray(steps)) {\n    if (steps.length == 1) return steps[0];\n    return sequence(steps);\n  }\n  return steps;\n}\nfunction validateStyleParams(value, options, errors) {\n  const params = options.params || {};\n  const matches = extractStyleParams(value);\n  if (matches.length) {\n    matches.forEach(varName => {\n      if (!params.hasOwnProperty(varName)) {\n        errors.push(invalidStyleParams(varName));\n      }\n    });\n  }\n}\nconst PARAM_REGEX = /* @__PURE__ */new RegExp(`${SUBSTITUTION_EXPR_START}\\\\s*(.+?)\\\\s*${SUBSTITUTION_EXPR_END}`, 'g');\nfunction extractStyleParams(value) {\n  let params = [];\n  if (typeof value === 'string') {\n    let match;\n    while (match = PARAM_REGEX.exec(value)) {\n      params.push(match[1]);\n    }\n    PARAM_REGEX.lastIndex = 0;\n  }\n  return params;\n}\nfunction interpolateParams(value, params, errors) {\n  const original = `${value}`;\n  const str = original.replace(PARAM_REGEX, (_, varName) => {\n    let localVal = params[varName];\n    // this means that the value was never overridden by the data passed in by the user\n    if (localVal == null) {\n      errors.push(invalidParamValue(varName));\n      localVal = '';\n    }\n    return localVal.toString();\n  });\n  // we do this to assert that numeric values stay as they are\n  return str == original ? value : str;\n}\nconst DASH_CASE_REGEXP = /-+([a-z0-9])/g;\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\nfunction camelCaseToDashCase(input) {\n  return input.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\nfunction allowPreviousPlayerStylesMerge(duration, delay) {\n  return duration === 0 || delay === 0;\n}\nfunction balancePreviousStylesIntoKeyframes(element, keyframes, previousStyles) {\n  if (previousStyles.size && keyframes.length) {\n    let startingKeyframe = keyframes[0];\n    let missingStyleProps = [];\n    previousStyles.forEach((val, prop) => {\n      if (!startingKeyframe.has(prop)) {\n        missingStyleProps.push(prop);\n      }\n      startingKeyframe.set(prop, val);\n    });\n    if (missingStyleProps.length) {\n      for (let i = 1; i < keyframes.length; i++) {\n        let kf = keyframes[i];\n        missingStyleProps.forEach(prop => kf.set(prop, computeStyle(element, prop)));\n      }\n    }\n  }\n  return keyframes;\n}\nfunction visitDslNode(visitor, node, context) {\n  switch (node.type) {\n    case AnimationMetadataType.Trigger:\n      return visitor.visitTrigger(node, context);\n    case AnimationMetadataType.State:\n      return visitor.visitState(node, context);\n    case AnimationMetadataType.Transition:\n      return visitor.visitTransition(node, context);\n    case AnimationMetadataType.Sequence:\n      return visitor.visitSequence(node, context);\n    case AnimationMetadataType.Group:\n      return visitor.visitGroup(node, context);\n    case AnimationMetadataType.Animate:\n      return visitor.visitAnimate(node, context);\n    case AnimationMetadataType.Keyframes:\n      return visitor.visitKeyframes(node, context);\n    case AnimationMetadataType.Style:\n      return visitor.visitStyle(node, context);\n    case AnimationMetadataType.Reference:\n      return visitor.visitReference(node, context);\n    case AnimationMetadataType.AnimateChild:\n      return visitor.visitAnimateChild(node, context);\n    case AnimationMetadataType.AnimateRef:\n      return visitor.visitAnimateRef(node, context);\n    case AnimationMetadataType.Query:\n      return visitor.visitQuery(node, context);\n    case AnimationMetadataType.Stagger:\n      return visitor.visitStagger(node, context);\n    default:\n      throw invalidNodeType(node.type);\n  }\n}\nfunction computeStyle(element, prop) {\n  return window.getComputedStyle(element)[prop];\n}\nexport { ENTER_CLASSNAME, LEAVE_CLASSNAME, NG_ANIMATING_CLASSNAME, NG_ANIMATING_SELECTOR, NG_TRIGGER_CLASSNAME, NG_TRIGGER_SELECTOR, SUBSTITUTION_EXPR_START, allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, buildingFailed, camelCaseToDashCase, computeStyle, containsElement, createAnimationFailed, dashCaseToCamelCase, eraseStyles, extractStyleParams, getOrSetDefaultValue, getParentElement, interpolateParams, invalidCssUnitValue, invalidDefinition, invalidExpression, invalidKeyframes, invalidOffset, invalidParallelAnimation, invalidQuery, invalidStagger, invalidState, invalidStyleValue, invalidTransitionAlias, invalidTrigger, invokeQuery, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, listenOnPlayer, makeAnimationEvent, missingEvent, missingOrDestroyedAnimation, missingPlayer, missingTrigger, normalizeAnimationEntry, normalizeKeyframes$1 as normalizeKeyframes, normalizeKeyframes as normalizeKeyframes$1, normalizeStyles, optimizeGroupPlayer, parseTimelineCommand, registerFailed, resolveTiming, resolveTimingValue, setStyles, transitionFailed, triggerBuildFailed, triggerTransitionsFailed, unregisteredTrigger, unsupportedTriggerEvent, validateStyleParams, validateStyleProperty, validateWebAnimatableStyleProperty, validationFailed, visitDslNode };\n", "/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\nimport { validateStyleProperty, containsElement, getParentElement, invokeQuery, dashCaseToCamelCase, invalidCssUnitValue, invalidExpression, invalidTransitionAlias, visitDslNode, invalidTrigger, invalidDefinition, extractStyleParams, invalidState, invalidStyleValue, SUBSTITUTION_EXPR_START, invalidParallelAnimation, validateStyleParams, invalidKeyframes, invalidOffset, keyframeOffsetsOutOfOrder, keyframesMissingOffsets, getOrSetDefaultValue, invalidStagger, resolveTiming, normalizeAnimationEntry, NG_TRIGGER_SELECTOR, NG_ANIMATING_SELECTOR, resolveTimingValue, interpolateParams, invalidQuery, registerFailed, normalizeKeyframes, LEAVE_CLASSNAME, ENTER_CLASSNAME, missingOrDestroyedAnimation, createAnimationFailed, optimizeGroupPlayer, missingPlayer, listenOnPlayer, makeAnimationEvent, triggerTransitionsFailed, eraseStyles, setStyles, transitionFailed, missingTrigger, missingEvent, unsupportedTriggerEvent, unregisteredTrigger, NG_TRIGGER_CLASSNAME, NG_ANIMATING_CLASSNAME, triggerBuildFailed, parseTimelineCommand, computeStyle, camelCaseToDashCase, validateWebAnimatableStyleProperty, allowPreviousPlayerStylesMerge, normalizeKeyframes$1, balancePreviousStylesIntoKeyframes, validationFailed, normalizeStyles, buildingFailed } from './util-D9FfmVnv.mjs';\nimport { NoopAnimationPlayer, AnimationMetadataType, style, AUTO_STYLE, ɵPRE_STYLE as _PRE_STYLE, AnimationGroupPlayer } from './private_export-faY_wCkZ.mjs';\n\n/**\n * @publicApi\n *\n * `AnimationDriver` implentation for Noop animations\n */\nclass NoopAnimationDriver {\n  /**\n   * @returns Whether `prop` is a valid CSS property\n   */\n  validateStyleProperty(prop) {\n    return validateStyleProperty(prop);\n  }\n  /**\n   *\n   * @returns Whether elm1 contains elm2.\n   */\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  /**\n   * @returns Rhe parent of the given element or `null` if the element is the `document`\n   */\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  /**\n   * @returns The result of the query selector on the element. The array will contain up to 1 item\n   *     if `multi` is  `false`.\n   */\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  /**\n   * @returns The `defaultValue` or empty string\n   */\n  computeStyle(element, prop, defaultValue) {\n    return defaultValue || '';\n  }\n  /**\n   * @returns An `NoopAnimationPlayer`\n   */\n  animate(element, keyframes, duration, delay, easing, previousPlayers = [], scrubberAccessRequested) {\n    return new NoopAnimationPlayer(duration, delay);\n  }\n  static ɵfac = function NoopAnimationDriver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoopAnimationDriver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NoopAnimationDriver,\n    factory: NoopAnimationDriver.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationDriver, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @publicApi\n */\nclass AnimationDriver {\n  /**\n   * @deprecated Use the NoopAnimationDriver class.\n   */\n  static NOOP = new NoopAnimationDriver();\n}\nclass AnimationStyleNormalizer {}\nclass NoopAnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return propertyName;\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    return value;\n  }\n}\nconst DIMENSIONAL_PROP_SET = new Set(['width', 'height', 'minWidth', 'minHeight', 'maxWidth', 'maxHeight', 'left', 'top', 'bottom', 'right', 'fontSize', 'outlineWidth', 'outlineOffset', 'paddingTop', 'paddingLeft', 'paddingBottom', 'paddingRight', 'marginTop', 'marginLeft', 'marginBottom', 'marginRight', 'borderRadius', 'borderWidth', 'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth', 'textIndent', 'perspective']);\nclass WebAnimationsStyleNormalizer extends AnimationStyleNormalizer {\n  normalizePropertyName(propertyName, errors) {\n    return dashCaseToCamelCase(propertyName);\n  }\n  normalizeStyleValue(userProvidedProperty, normalizedProperty, value, errors) {\n    let unit = '';\n    const strVal = value.toString().trim();\n    if (DIMENSIONAL_PROP_SET.has(normalizedProperty) && value !== 0 && value !== '0') {\n      if (typeof value === 'number') {\n        unit = 'px';\n      } else {\n        const valAndSuffixMatch = value.match(/^[+-]?[\\d\\.]+([a-z]*)$/);\n        if (valAndSuffixMatch && valAndSuffixMatch[1].length == 0) {\n          errors.push(invalidCssUnitValue(userProvidedProperty, value));\n        }\n      }\n    }\n    return strVal + unit;\n  }\n}\nfunction createListOfWarnings(warnings) {\n  const LINE_START = '\\n - ';\n  return `${LINE_START}${warnings.filter(Boolean).map(warning => warning).join(LINE_START)}`;\n}\nfunction warnValidation(warnings) {\n  console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnTriggerBuild(name, warnings) {\n  console.warn(`The animation trigger \"${name}\" has built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction warnRegister(warnings) {\n  console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);\n}\nfunction pushUnrecognizedPropertiesWarning(warnings, props) {\n  if (props.length) {\n    warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);\n  }\n}\nconst ANY_STATE = '*';\nfunction parseTransitionExpr(transitionValue, errors) {\n  const expressions = [];\n  if (typeof transitionValue == 'string') {\n    transitionValue.split(/\\s*,\\s*/).forEach(str => parseInnerTransitionStr(str, expressions, errors));\n  } else {\n    expressions.push(transitionValue);\n  }\n  return expressions;\n}\nfunction parseInnerTransitionStr(eventStr, expressions, errors) {\n  if (eventStr[0] == ':') {\n    const result = parseAnimationAlias(eventStr, errors);\n    if (typeof result == 'function') {\n      expressions.push(result);\n      return;\n    }\n    eventStr = result;\n  }\n  const match = eventStr.match(/^(\\*|[-\\w]+)\\s*(<?[=-]>)\\s*(\\*|[-\\w]+)$/);\n  if (match == null || match.length < 4) {\n    errors.push(invalidExpression(eventStr));\n    return expressions;\n  }\n  const fromState = match[1];\n  const separator = match[2];\n  const toState = match[3];\n  expressions.push(makeLambdaFromStates(fromState, toState));\n  const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;\n  if (separator[0] == '<' && !isFullAnyStateExpr) {\n    expressions.push(makeLambdaFromStates(toState, fromState));\n  }\n  return;\n}\nfunction parseAnimationAlias(alias, errors) {\n  switch (alias) {\n    case ':enter':\n      return 'void => *';\n    case ':leave':\n      return '* => void';\n    case ':increment':\n      return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);\n    case ':decrement':\n      return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);\n    default:\n      errors.push(invalidTransitionAlias(alias));\n      return '* => *';\n  }\n}\n// DO NOT REFACTOR ... keep the follow set instantiations\n// with the values intact (closure compiler for some reason\n// removes follow-up lines that add the values outside of\n// the constructor...\nconst TRUE_BOOLEAN_VALUES = new Set(['true', '1']);\nconst FALSE_BOOLEAN_VALUES = new Set(['false', '0']);\nfunction makeLambdaFromStates(lhs, rhs) {\n  const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);\n  const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);\n  return (fromState, toState) => {\n    let lhsMatch = lhs == ANY_STATE || lhs == fromState;\n    let rhsMatch = rhs == ANY_STATE || rhs == toState;\n    if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {\n      lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);\n    }\n    if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {\n      rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);\n    }\n    return lhsMatch && rhsMatch;\n  };\n}\nconst SELF_TOKEN = ':self';\nconst SELF_TOKEN_REGEX = /* @__PURE__ */new RegExp(`s*${SELF_TOKEN}s*,?`, 'g');\n/*\n * [Validation]\n * The visitor code below will traverse the animation AST generated by the animation verb functions\n * (the output is a tree of objects) and attempt to perform a series of validations on the data. The\n * following corner-cases will be validated:\n *\n * 1. Overlap of animations\n * Given that a CSS property cannot be animated in more than one place at the same time, it's\n * important that this behavior is detected and validated. The way in which this occurs is that\n * each time a style property is examined, a string-map containing the property will be updated with\n * the start and end times for when the property is used within an animation step.\n *\n * If there are two or more parallel animations that are currently running (these are invoked by the\n * group()) on the same element then the validator will throw an error. Since the start/end timing\n * values are collected for each property then if the current animation step is animating the same\n * property and its timing values fall anywhere into the window of time that the property is\n * currently being animated within then this is what causes an error.\n *\n * 2. Timing values\n * The validator will validate to see if a timing value of `duration delay easing` or\n * `durationNumber` is valid or not.\n *\n * (note that upon validation the code below will replace the timing data with an object containing\n * {duration,delay,easing}.\n *\n * 3. Offset Validation\n * Each of the style() calls are allowed to have an offset value when placed inside of keyframes().\n * Offsets within keyframes() are considered valid when:\n *\n *   - No offsets are used at all\n *   - Each style() entry contains an offset value\n *   - Each offset is between 0 and 1\n *   - Each offset is greater to or equal than the previous one\n *\n * Otherwise an error will be thrown.\n */\nfunction buildAnimationAst(driver, metadata, errors, warnings) {\n  return new AnimationAstBuilderVisitor(driver).build(metadata, errors, warnings);\n}\nconst ROOT_SELECTOR = '';\nclass AnimationAstBuilderVisitor {\n  _driver;\n  constructor(_driver) {\n    this._driver = _driver;\n  }\n  build(metadata, errors, warnings) {\n    const context = new AnimationAstBuilderContext(errors);\n    this._resetContextStyleTimingState(context);\n    const ast = visitDslNode(this, normalizeAnimationEntry(metadata), context);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (context.unsupportedCSSPropertiesFound.size) {\n        pushUnrecognizedPropertiesWarning(warnings, [...context.unsupportedCSSPropertiesFound.keys()]);\n      }\n    }\n    return ast;\n  }\n  _resetContextStyleTimingState(context) {\n    context.currentQuerySelector = ROOT_SELECTOR;\n    context.collectedStyles = new Map();\n    context.collectedStyles.set(ROOT_SELECTOR, new Map());\n    context.currentTime = 0;\n  }\n  visitTrigger(metadata, context) {\n    let queryCount = context.queryCount = 0;\n    let depCount = context.depCount = 0;\n    const states = [];\n    const transitions = [];\n    if (metadata.name.charAt(0) == '@') {\n      context.errors.push(invalidTrigger());\n    }\n    metadata.definitions.forEach(def => {\n      this._resetContextStyleTimingState(context);\n      if (def.type == AnimationMetadataType.State) {\n        const stateDef = def;\n        const name = stateDef.name;\n        name.toString().split(/\\s*,\\s*/).forEach(n => {\n          stateDef.name = n;\n          states.push(this.visitState(stateDef, context));\n        });\n        stateDef.name = name;\n      } else if (def.type == AnimationMetadataType.Transition) {\n        const transition = this.visitTransition(def, context);\n        queryCount += transition.queryCount;\n        depCount += transition.depCount;\n        transitions.push(transition);\n      } else {\n        context.errors.push(invalidDefinition());\n      }\n    });\n    return {\n      type: AnimationMetadataType.Trigger,\n      name: metadata.name,\n      states,\n      transitions,\n      queryCount,\n      depCount,\n      options: null\n    };\n  }\n  visitState(metadata, context) {\n    const styleAst = this.visitStyle(metadata.styles, context);\n    const astParams = metadata.options && metadata.options.params || null;\n    if (styleAst.containsDynamicStyles) {\n      const missingSubs = new Set();\n      const params = astParams || {};\n      styleAst.styles.forEach(style => {\n        if (style instanceof Map) {\n          style.forEach(value => {\n            extractStyleParams(value).forEach(sub => {\n              if (!params.hasOwnProperty(sub)) {\n                missingSubs.add(sub);\n              }\n            });\n          });\n        }\n      });\n      if (missingSubs.size) {\n        context.errors.push(invalidState(metadata.name, [...missingSubs.values()]));\n      }\n    }\n    return {\n      type: AnimationMetadataType.State,\n      name: metadata.name,\n      style: styleAst,\n      options: astParams ? {\n        params: astParams\n      } : null\n    };\n  }\n  visitTransition(metadata, context) {\n    context.queryCount = 0;\n    context.depCount = 0;\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    const matchers = parseTransitionExpr(metadata.expr, context.errors);\n    return {\n      type: AnimationMetadataType.Transition,\n      matchers,\n      animation,\n      queryCount: context.queryCount,\n      depCount: context.depCount,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitSequence(metadata, context) {\n    return {\n      type: AnimationMetadataType.Sequence,\n      steps: metadata.steps.map(s => visitDslNode(this, s, context)),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitGroup(metadata, context) {\n    const currentTime = context.currentTime;\n    let furthestTime = 0;\n    const steps = metadata.steps.map(step => {\n      context.currentTime = currentTime;\n      const innerAst = visitDslNode(this, step, context);\n      furthestTime = Math.max(furthestTime, context.currentTime);\n      return innerAst;\n    });\n    context.currentTime = furthestTime;\n    return {\n      type: AnimationMetadataType.Group,\n      steps,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimate(metadata, context) {\n    const timingAst = constructTimingAst(metadata.timings, context.errors);\n    context.currentAnimateTimings = timingAst;\n    let styleAst;\n    let styleMetadata = metadata.styles ? metadata.styles : style({});\n    if (styleMetadata.type == AnimationMetadataType.Keyframes) {\n      styleAst = this.visitKeyframes(styleMetadata, context);\n    } else {\n      let styleMetadata = metadata.styles;\n      let isEmpty = false;\n      if (!styleMetadata) {\n        isEmpty = true;\n        const newStyleData = {};\n        if (timingAst.easing) {\n          newStyleData['easing'] = timingAst.easing;\n        }\n        styleMetadata = style(newStyleData);\n      }\n      context.currentTime += timingAst.duration + timingAst.delay;\n      const _styleAst = this.visitStyle(styleMetadata, context);\n      _styleAst.isEmptyStep = isEmpty;\n      styleAst = _styleAst;\n    }\n    context.currentAnimateTimings = null;\n    return {\n      type: AnimationMetadataType.Animate,\n      timings: timingAst,\n      style: styleAst,\n      options: null\n    };\n  }\n  visitStyle(metadata, context) {\n    const ast = this._makeStyleAst(metadata, context);\n    this._validateStyleAst(ast, context);\n    return ast;\n  }\n  _makeStyleAst(metadata, context) {\n    const styles = [];\n    const metadataStyles = Array.isArray(metadata.styles) ? metadata.styles : [metadata.styles];\n    for (let styleTuple of metadataStyles) {\n      if (typeof styleTuple === 'string') {\n        if (styleTuple === AUTO_STYLE) {\n          styles.push(styleTuple);\n        } else {\n          context.errors.push(invalidStyleValue(styleTuple));\n        }\n      } else {\n        styles.push(new Map(Object.entries(styleTuple)));\n      }\n    }\n    let containsDynamicStyles = false;\n    let collectedEasing = null;\n    styles.forEach(styleData => {\n      if (styleData instanceof Map) {\n        if (styleData.has('easing')) {\n          collectedEasing = styleData.get('easing');\n          styleData.delete('easing');\n        }\n        if (!containsDynamicStyles) {\n          for (let value of styleData.values()) {\n            if (value.toString().indexOf(SUBSTITUTION_EXPR_START) >= 0) {\n              containsDynamicStyles = true;\n              break;\n            }\n          }\n        }\n      }\n    });\n    return {\n      type: AnimationMetadataType.Style,\n      styles,\n      easing: collectedEasing,\n      offset: metadata.offset,\n      containsDynamicStyles,\n      options: null\n    };\n  }\n  _validateStyleAst(ast, context) {\n    const timings = context.currentAnimateTimings;\n    let endTime = context.currentTime;\n    let startTime = context.currentTime;\n    if (timings && startTime > 0) {\n      startTime -= timings.duration + timings.delay;\n    }\n    ast.styles.forEach(tuple => {\n      if (typeof tuple === 'string') return;\n      tuple.forEach((value, prop) => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          if (!this._driver.validateStyleProperty(prop)) {\n            tuple.delete(prop);\n            context.unsupportedCSSPropertiesFound.add(prop);\n            return;\n          }\n        }\n        // This is guaranteed to have a defined Map at this querySelector location making it\n        // safe to add the assertion here. It is set as a default empty map in prior methods.\n        const collectedStyles = context.collectedStyles.get(context.currentQuerySelector);\n        const collectedEntry = collectedStyles.get(prop);\n        let updateCollectedStyle = true;\n        if (collectedEntry) {\n          if (startTime != endTime && startTime >= collectedEntry.startTime && endTime <= collectedEntry.endTime) {\n            context.errors.push(invalidParallelAnimation(prop, collectedEntry.startTime, collectedEntry.endTime, startTime, endTime));\n            updateCollectedStyle = false;\n          }\n          // we always choose the smaller start time value since we\n          // want to have a record of the entire animation window where\n          // the style property is being animated in between\n          startTime = collectedEntry.startTime;\n        }\n        if (updateCollectedStyle) {\n          collectedStyles.set(prop, {\n            startTime,\n            endTime\n          });\n        }\n        if (context.options) {\n          validateStyleParams(value, context.options, context.errors);\n        }\n      });\n    });\n  }\n  visitKeyframes(metadata, context) {\n    const ast = {\n      type: AnimationMetadataType.Keyframes,\n      styles: [],\n      options: null\n    };\n    if (!context.currentAnimateTimings) {\n      context.errors.push(invalidKeyframes());\n      return ast;\n    }\n    const MAX_KEYFRAME_OFFSET = 1;\n    let totalKeyframesWithOffsets = 0;\n    const offsets = [];\n    let offsetsOutOfOrder = false;\n    let keyframesOutOfRange = false;\n    let previousOffset = 0;\n    const keyframes = metadata.steps.map(styles => {\n      const style = this._makeStyleAst(styles, context);\n      let offsetVal = style.offset != null ? style.offset : consumeOffset(style.styles);\n      let offset = 0;\n      if (offsetVal != null) {\n        totalKeyframesWithOffsets++;\n        offset = style.offset = offsetVal;\n      }\n      keyframesOutOfRange = keyframesOutOfRange || offset < 0 || offset > 1;\n      offsetsOutOfOrder = offsetsOutOfOrder || offset < previousOffset;\n      previousOffset = offset;\n      offsets.push(offset);\n      return style;\n    });\n    if (keyframesOutOfRange) {\n      context.errors.push(invalidOffset());\n    }\n    if (offsetsOutOfOrder) {\n      context.errors.push(keyframeOffsetsOutOfOrder());\n    }\n    const length = metadata.steps.length;\n    let generatedOffset = 0;\n    if (totalKeyframesWithOffsets > 0 && totalKeyframesWithOffsets < length) {\n      context.errors.push(keyframesMissingOffsets());\n    } else if (totalKeyframesWithOffsets == 0) {\n      generatedOffset = MAX_KEYFRAME_OFFSET / (length - 1);\n    }\n    const limit = length - 1;\n    const currentTime = context.currentTime;\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const animateDuration = currentAnimateTimings.duration;\n    keyframes.forEach((kf, i) => {\n      const offset = generatedOffset > 0 ? i == limit ? 1 : generatedOffset * i : offsets[i];\n      const durationUpToThisFrame = offset * animateDuration;\n      context.currentTime = currentTime + currentAnimateTimings.delay + durationUpToThisFrame;\n      currentAnimateTimings.duration = durationUpToThisFrame;\n      this._validateStyleAst(kf, context);\n      kf.offset = offset;\n      ast.styles.push(kf);\n    });\n    return ast;\n  }\n  visitReference(metadata, context) {\n    return {\n      type: AnimationMetadataType.Reference,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateChild(metadata, context) {\n    context.depCount++;\n    return {\n      type: AnimationMetadataType.AnimateChild,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitAnimateRef(metadata, context) {\n    return {\n      type: AnimationMetadataType.AnimateRef,\n      animation: this.visitReference(metadata.animation, context),\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitQuery(metadata, context) {\n    const parentSelector = context.currentQuerySelector;\n    const options = metadata.options || {};\n    context.queryCount++;\n    context.currentQuery = metadata;\n    const [selector, includeSelf] = normalizeSelector(metadata.selector);\n    context.currentQuerySelector = parentSelector.length ? parentSelector + ' ' + selector : selector;\n    getOrSetDefaultValue(context.collectedStyles, context.currentQuerySelector, new Map());\n    const animation = visitDslNode(this, normalizeAnimationEntry(metadata.animation), context);\n    context.currentQuery = null;\n    context.currentQuerySelector = parentSelector;\n    return {\n      type: AnimationMetadataType.Query,\n      selector,\n      limit: options.limit || 0,\n      optional: !!options.optional,\n      includeSelf,\n      animation,\n      originalSelector: metadata.selector,\n      options: normalizeAnimationOptions(metadata.options)\n    };\n  }\n  visitStagger(metadata, context) {\n    if (!context.currentQuery) {\n      context.errors.push(invalidStagger());\n    }\n    const timings = metadata.timings === 'full' ? {\n      duration: 0,\n      delay: 0,\n      easing: 'full'\n    } : resolveTiming(metadata.timings, context.errors, true);\n    return {\n      type: AnimationMetadataType.Stagger,\n      animation: visitDslNode(this, normalizeAnimationEntry(metadata.animation), context),\n      timings,\n      options: null\n    };\n  }\n}\nfunction normalizeSelector(selector) {\n  const hasAmpersand = selector.split(/\\s*,\\s*/).find(token => token == SELF_TOKEN) ? true : false;\n  if (hasAmpersand) {\n    selector = selector.replace(SELF_TOKEN_REGEX, '');\n  }\n  // Note: the :enter and :leave aren't normalized here since those\n  // selectors are filled in at runtime during timeline building\n  selector = selector.replace(/@\\*/g, NG_TRIGGER_SELECTOR).replace(/@\\w+/g, match => NG_TRIGGER_SELECTOR + '-' + match.slice(1)).replace(/:animating/g, NG_ANIMATING_SELECTOR);\n  return [selector, hasAmpersand];\n}\nfunction normalizeParams(obj) {\n  return obj ? {\n    ...obj\n  } : null;\n}\nclass AnimationAstBuilderContext {\n  errors;\n  queryCount = 0;\n  depCount = 0;\n  currentTransition = null;\n  currentQuery = null;\n  currentQuerySelector = null;\n  currentAnimateTimings = null;\n  currentTime = 0;\n  collectedStyles = new Map();\n  options = null;\n  unsupportedCSSPropertiesFound = new Set();\n  constructor(errors) {\n    this.errors = errors;\n  }\n}\nfunction consumeOffset(styles) {\n  if (typeof styles == 'string') return null;\n  let offset = null;\n  if (Array.isArray(styles)) {\n    styles.forEach(styleTuple => {\n      if (styleTuple instanceof Map && styleTuple.has('offset')) {\n        const obj = styleTuple;\n        offset = parseFloat(obj.get('offset'));\n        obj.delete('offset');\n      }\n    });\n  } else if (styles instanceof Map && styles.has('offset')) {\n    const obj = styles;\n    offset = parseFloat(obj.get('offset'));\n    obj.delete('offset');\n  }\n  return offset;\n}\nfunction constructTimingAst(value, errors) {\n  if (value.hasOwnProperty('duration')) {\n    return value;\n  }\n  if (typeof value == 'number') {\n    const duration = resolveTiming(value, errors).duration;\n    return makeTimingAst(duration, 0, '');\n  }\n  const strValue = value;\n  const isDynamic = strValue.split(/\\s+/).some(v => v.charAt(0) == '{' && v.charAt(1) == '{');\n  if (isDynamic) {\n    const ast = makeTimingAst(0, 0, '');\n    ast.dynamic = true;\n    ast.strValue = strValue;\n    return ast;\n  }\n  const timings = resolveTiming(strValue, errors);\n  return makeTimingAst(timings.duration, timings.delay, timings.easing);\n}\nfunction normalizeAnimationOptions(options) {\n  if (options) {\n    options = {\n      ...options\n    };\n    if (options['params']) {\n      options['params'] = normalizeParams(options['params']);\n    }\n  } else {\n    options = {};\n  }\n  return options;\n}\nfunction makeTimingAst(duration, delay, easing) {\n  return {\n    duration,\n    delay,\n    easing\n  };\n}\nfunction createTimelineInstruction(element, keyframes, preStyleProps, postStyleProps, duration, delay, easing = null, subTimeline = false) {\n  return {\n    type: 1 /* AnimationTransitionInstructionType.TimelineAnimation */,\n    element,\n    keyframes,\n    preStyleProps,\n    postStyleProps,\n    duration,\n    delay,\n    totalTime: duration + delay,\n    easing,\n    subTimeline\n  };\n}\nclass ElementInstructionMap {\n  _map = new Map();\n  get(element) {\n    return this._map.get(element) || [];\n  }\n  append(element, instructions) {\n    let existingInstructions = this._map.get(element);\n    if (!existingInstructions) {\n      this._map.set(element, existingInstructions = []);\n    }\n    existingInstructions.push(...instructions);\n  }\n  has(element) {\n    return this._map.has(element);\n  }\n  clear() {\n    this._map.clear();\n  }\n}\nconst ONE_FRAME_IN_MILLISECONDS = 1;\nconst ENTER_TOKEN = ':enter';\nconst ENTER_TOKEN_REGEX = /* @__PURE__ */new RegExp(ENTER_TOKEN, 'g');\nconst LEAVE_TOKEN = ':leave';\nconst LEAVE_TOKEN_REGEX = /* @__PURE__ */new RegExp(LEAVE_TOKEN, 'g');\n/*\n * The code within this file aims to generate web-animations-compatible keyframes from Angular's\n * animation DSL code.\n *\n * The code below will be converted from:\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(1000, style({ opacity: 0 }))\n * ])\n * ```\n *\n * To:\n * ```ts\n * keyframes = [{ opacity: 0, offset: 0 }, { opacity: 1, offset: 1 }]\n * duration = 1000\n * delay = 0\n * easing = ''\n * ```\n *\n * For this operation to cover the combination of animation verbs (style, animate, group, etc...) a\n * combination of AST traversal and merge-sort-like algorithms are used.\n *\n * [AST Traversal]\n * Each of the animation verbs, when executed, will return an string-map object representing what\n * type of action it is (style, animate, group, etc...) and the data associated with it. This means\n * that when functional composition mix of these functions is evaluated (like in the example above)\n * then it will end up producing a tree of objects representing the animation itself.\n *\n * When this animation object tree is processed by the visitor code below it will visit each of the\n * verb statements within the visitor. And during each visit it will build the context of the\n * animation keyframes by interacting with the `TimelineBuilder`.\n *\n * [TimelineBuilder]\n * This class is responsible for tracking the styles and building a series of keyframe objects for a\n * timeline between a start and end time. The builder starts off with an initial timeline and each\n * time the AST comes across a `group()`, `keyframes()` or a combination of the two within a\n * `sequence()` then it will generate a sub timeline for each step as well as a new one after\n * they are complete.\n *\n * As the AST is traversed, the timing state on each of the timelines will be incremented. If a sub\n * timeline was created (based on one of the cases above) then the parent timeline will attempt to\n * merge the styles used within the sub timelines into itself (only with group() this will happen).\n * This happens with a merge operation (much like how the merge works in mergeSort) and it will only\n * copy the most recently used styles from the sub timelines into the parent timeline. This ensures\n * that if the styles are used later on in another phase of the animation then they will be the most\n * up-to-date values.\n *\n * [How Missing Styles Are Updated]\n * Each timeline has a `backFill` property which is responsible for filling in new styles into\n * already processed keyframes if a new style shows up later within the animation sequence.\n *\n * ```ts\n * sequence([\n *   style({ width: 0 }),\n *   animate(1000, style({ width: 100 })),\n *   animate(1000, style({ width: 200 })),\n *   animate(1000, style({ width: 300 }))\n *   animate(1000, style({ width: 400, height: 400 })) // notice how `height` doesn't exist anywhere\n * else\n * ])\n * ```\n *\n * What is happening here is that the `height` value is added later in the sequence, but is missing\n * from all previous animation steps. Therefore when a keyframe is created it would also be missing\n * from all previous keyframes up until where it is first used. For the timeline keyframe generation\n * to properly fill in the style it will place the previous value (the value from the parent\n * timeline) or a default value of `*` into the backFill map.\n *\n * When a sub-timeline is created it will have its own backFill property. This is done so that\n * styles present within the sub-timeline do not accidentally seep into the previous/future timeline\n * keyframes\n *\n * [Validation]\n * The code in this file is not responsible for validation. That functionality happens with within\n * the `AnimationValidatorVisitor` code.\n */\nfunction buildAnimationTimelines(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles = new Map(), finalStyles = new Map(), options, subInstructions, errors = []) {\n  return new AnimationTimelineBuilderVisitor().buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors);\n}\nclass AnimationTimelineBuilderVisitor {\n  buildKeyframes(driver, rootElement, ast, enterClassName, leaveClassName, startingStyles, finalStyles, options, subInstructions, errors = []) {\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const context = new AnimationTimelineContext(driver, rootElement, subInstructions, enterClassName, leaveClassName, errors, []);\n    context.options = options;\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    context.currentTimeline.delayNextStep(delay);\n    context.currentTimeline.setStyles([startingStyles], null, context.errors, options);\n    visitDslNode(this, ast, context);\n    // this checks to see if an actual animation happened\n    const timelines = context.timelines.filter(timeline => timeline.containsAnimation());\n    // note: we just want to apply the final styles for the rootElement, so we do not\n    //       just apply the styles to the last timeline but the last timeline which\n    //       element is the root one (basically `*`-styles are replaced with the actual\n    //       state style values only for the root element)\n    if (timelines.length && finalStyles.size) {\n      let lastRootTimeline;\n      for (let i = timelines.length - 1; i >= 0; i--) {\n        const timeline = timelines[i];\n        if (timeline.element === rootElement) {\n          lastRootTimeline = timeline;\n          break;\n        }\n      }\n      if (lastRootTimeline && !lastRootTimeline.allowOnlyTimelineStyles()) {\n        lastRootTimeline.setStyles([finalStyles], null, context.errors, options);\n      }\n    }\n    return timelines.length ? timelines.map(timeline => timeline.buildKeyframes()) : [createTimelineInstruction(rootElement, [], [], [], 0, delay, '', false)];\n  }\n  visitTrigger(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitState(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitTransition(ast, context) {\n    // these values are not visited in this AST\n  }\n  visitAnimateChild(ast, context) {\n    const elementInstructions = context.subInstructions.get(context.element);\n    if (elementInstructions) {\n      const innerContext = context.createSubContext(ast.options);\n      const startTime = context.currentTimeline.currentTime;\n      const endTime = this._visitSubInstructions(elementInstructions, innerContext, innerContext.options);\n      if (startTime != endTime) {\n        // we do this on the upper context because we created a sub context for\n        // the sub child animations\n        context.transformIntoNewTimeline(endTime);\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitAnimateRef(ast, context) {\n    const innerContext = context.createSubContext(ast.options);\n    innerContext.transformIntoNewTimeline();\n    this._applyAnimationRefDelays([ast.options, ast.animation.options], context, innerContext);\n    this.visitReference(ast.animation, innerContext);\n    context.transformIntoNewTimeline(innerContext.currentTimeline.currentTime);\n    context.previousNode = ast;\n  }\n  _applyAnimationRefDelays(animationsRefsOptions, context, innerContext) {\n    for (const animationRefOptions of animationsRefsOptions) {\n      const animationDelay = animationRefOptions?.delay;\n      if (animationDelay) {\n        const animationDelayValue = typeof animationDelay === 'number' ? animationDelay : resolveTimingValue(interpolateParams(animationDelay, animationRefOptions?.params ?? {}, context.errors));\n        innerContext.delayNextStep(animationDelayValue);\n      }\n    }\n  }\n  _visitSubInstructions(instructions, context, options) {\n    const startTime = context.currentTimeline.currentTime;\n    let furthestTime = startTime;\n    // this is a special-case for when a user wants to skip a sub\n    // animation from being fired entirely.\n    const duration = options.duration != null ? resolveTimingValue(options.duration) : null;\n    const delay = options.delay != null ? resolveTimingValue(options.delay) : null;\n    if (duration !== 0) {\n      instructions.forEach(instruction => {\n        const instructionTimings = context.appendInstructionToTimeline(instruction, duration, delay);\n        furthestTime = Math.max(furthestTime, instructionTimings.duration + instructionTimings.delay);\n      });\n    }\n    return furthestTime;\n  }\n  visitReference(ast, context) {\n    context.updateOptions(ast.options, true);\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n  }\n  visitSequence(ast, context) {\n    const subContextCount = context.subContextCount;\n    let ctx = context;\n    const options = ast.options;\n    if (options && (options.params || options.delay)) {\n      ctx = context.createSubContext(options);\n      ctx.transformIntoNewTimeline();\n      if (options.delay != null) {\n        if (ctx.previousNode.type == AnimationMetadataType.Style) {\n          ctx.currentTimeline.snapshotCurrentStyles();\n          ctx.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n        }\n        const delay = resolveTimingValue(options.delay);\n        ctx.delayNextStep(delay);\n      }\n    }\n    if (ast.steps.length) {\n      ast.steps.forEach(s => visitDslNode(this, s, ctx));\n      // this is here just in case the inner steps only contain or end with a style() call\n      ctx.currentTimeline.applyStylesToKeyframe();\n      // this means that some animation function within the sequence\n      // ended up creating a sub timeline (which means the current\n      // timeline cannot overlap with the contents of the sequence)\n      if (ctx.subContextCount > subContextCount) {\n        ctx.transformIntoNewTimeline();\n      }\n    }\n    context.previousNode = ast;\n  }\n  visitGroup(ast, context) {\n    const innerTimelines = [];\n    let furthestTime = context.currentTimeline.currentTime;\n    const delay = ast.options && ast.options.delay ? resolveTimingValue(ast.options.delay) : 0;\n    ast.steps.forEach(s => {\n      const innerContext = context.createSubContext(ast.options);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      visitDslNode(this, s, innerContext);\n      furthestTime = Math.max(furthestTime, innerContext.currentTimeline.currentTime);\n      innerTimelines.push(innerContext.currentTimeline);\n    });\n    // this operation is run after the AST loop because otherwise\n    // if the parent timeline's collected styles were updated then\n    // it would pass in invalid data into the new-to-be forked items\n    innerTimelines.forEach(timeline => context.currentTimeline.mergeTimelineCollectedStyles(timeline));\n    context.transformIntoNewTimeline(furthestTime);\n    context.previousNode = ast;\n  }\n  _visitTiming(ast, context) {\n    if (ast.dynamic) {\n      const strValue = ast.strValue;\n      const timingValue = context.params ? interpolateParams(strValue, context.params, context.errors) : strValue;\n      return resolveTiming(timingValue, context.errors);\n    } else {\n      return {\n        duration: ast.duration,\n        delay: ast.delay,\n        easing: ast.easing\n      };\n    }\n  }\n  visitAnimate(ast, context) {\n    const timings = context.currentAnimateTimings = this._visitTiming(ast.timings, context);\n    const timeline = context.currentTimeline;\n    if (timings.delay) {\n      context.incrementTime(timings.delay);\n      timeline.snapshotCurrentStyles();\n    }\n    const style = ast.style;\n    if (style.type == AnimationMetadataType.Keyframes) {\n      this.visitKeyframes(style, context);\n    } else {\n      context.incrementTime(timings.duration);\n      this.visitStyle(style, context);\n      timeline.applyStylesToKeyframe();\n    }\n    context.currentAnimateTimings = null;\n    context.previousNode = ast;\n  }\n  visitStyle(ast, context) {\n    const timeline = context.currentTimeline;\n    const timings = context.currentAnimateTimings;\n    // this is a special case for when a style() call\n    // directly follows  an animate() call (but not inside of an animate() call)\n    if (!timings && timeline.hasCurrentStyleProperties()) {\n      timeline.forwardFrame();\n    }\n    const easing = timings && timings.easing || ast.easing;\n    if (ast.isEmptyStep) {\n      timeline.applyEmptyStep(easing);\n    } else {\n      timeline.setStyles(ast.styles, easing, context.errors, context.options);\n    }\n    context.previousNode = ast;\n  }\n  visitKeyframes(ast, context) {\n    const currentAnimateTimings = context.currentAnimateTimings;\n    const startTime = context.currentTimeline.duration;\n    const duration = currentAnimateTimings.duration;\n    const innerContext = context.createSubContext();\n    const innerTimeline = innerContext.currentTimeline;\n    innerTimeline.easing = currentAnimateTimings.easing;\n    ast.styles.forEach(step => {\n      const offset = step.offset || 0;\n      innerTimeline.forwardTime(offset * duration);\n      innerTimeline.setStyles(step.styles, step.easing, context.errors, context.options);\n      innerTimeline.applyStylesToKeyframe();\n    });\n    // this will ensure that the parent timeline gets all the styles from\n    // the child even if the new timeline below is not used\n    context.currentTimeline.mergeTimelineCollectedStyles(innerTimeline);\n    // we do this because the window between this timeline and the sub timeline\n    // should ensure that the styles within are exactly the same as they were before\n    context.transformIntoNewTimeline(startTime + duration);\n    context.previousNode = ast;\n  }\n  visitQuery(ast, context) {\n    // in the event that the first step before this is a style step we need\n    // to ensure the styles are applied before the children are animated\n    const startTime = context.currentTimeline.currentTime;\n    const options = ast.options || {};\n    const delay = options.delay ? resolveTimingValue(options.delay) : 0;\n    if (delay && (context.previousNode.type === AnimationMetadataType.Style || startTime == 0 && context.currentTimeline.hasCurrentStyleProperties())) {\n      context.currentTimeline.snapshotCurrentStyles();\n      context.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    }\n    let furthestTime = startTime;\n    const elms = context.invokeQuery(ast.selector, ast.originalSelector, ast.limit, ast.includeSelf, options.optional ? true : false, context.errors);\n    context.currentQueryTotal = elms.length;\n    let sameElementTimeline = null;\n    elms.forEach((element, i) => {\n      context.currentQueryIndex = i;\n      const innerContext = context.createSubContext(ast.options, element);\n      if (delay) {\n        innerContext.delayNextStep(delay);\n      }\n      if (element === context.element) {\n        sameElementTimeline = innerContext.currentTimeline;\n      }\n      visitDslNode(this, ast.animation, innerContext);\n      // this is here just incase the inner steps only contain or end\n      // with a style() call (which is here to signal that this is a preparatory\n      // call to style an element before it is animated again)\n      innerContext.currentTimeline.applyStylesToKeyframe();\n      const endTime = innerContext.currentTimeline.currentTime;\n      furthestTime = Math.max(furthestTime, endTime);\n    });\n    context.currentQueryIndex = 0;\n    context.currentQueryTotal = 0;\n    context.transformIntoNewTimeline(furthestTime);\n    if (sameElementTimeline) {\n      context.currentTimeline.mergeTimelineCollectedStyles(sameElementTimeline);\n      context.currentTimeline.snapshotCurrentStyles();\n    }\n    context.previousNode = ast;\n  }\n  visitStagger(ast, context) {\n    const parentContext = context.parentContext;\n    const tl = context.currentTimeline;\n    const timings = ast.timings;\n    const duration = Math.abs(timings.duration);\n    const maxTime = duration * (context.currentQueryTotal - 1);\n    let delay = duration * context.currentQueryIndex;\n    let staggerTransformer = timings.duration < 0 ? 'reverse' : timings.easing;\n    switch (staggerTransformer) {\n      case 'reverse':\n        delay = maxTime - delay;\n        break;\n      case 'full':\n        delay = parentContext.currentStaggerTime;\n        break;\n    }\n    const timeline = context.currentTimeline;\n    if (delay) {\n      timeline.delayNextStep(delay);\n    }\n    const startingTime = timeline.currentTime;\n    visitDslNode(this, ast.animation, context);\n    context.previousNode = ast;\n    // time = duration + delay\n    // the reason why this computation is so complex is because\n    // the inner timeline may either have a delay value or a stretched\n    // keyframe depending on if a subtimeline is not used or is used.\n    parentContext.currentStaggerTime = tl.currentTime - startingTime + (tl.startTime - parentContext.currentTimeline.startTime);\n  }\n}\nconst DEFAULT_NOOP_PREVIOUS_NODE = {};\nclass AnimationTimelineContext {\n  _driver;\n  element;\n  subInstructions;\n  _enterClassName;\n  _leaveClassName;\n  errors;\n  timelines;\n  parentContext = null;\n  currentTimeline;\n  currentAnimateTimings = null;\n  previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n  subContextCount = 0;\n  options = {};\n  currentQueryIndex = 0;\n  currentQueryTotal = 0;\n  currentStaggerTime = 0;\n  constructor(_driver, element, subInstructions, _enterClassName, _leaveClassName, errors, timelines, initialTimeline) {\n    this._driver = _driver;\n    this.element = element;\n    this.subInstructions = subInstructions;\n    this._enterClassName = _enterClassName;\n    this._leaveClassName = _leaveClassName;\n    this.errors = errors;\n    this.timelines = timelines;\n    this.currentTimeline = initialTimeline || new TimelineBuilder(this._driver, element, 0);\n    timelines.push(this.currentTimeline);\n  }\n  get params() {\n    return this.options.params;\n  }\n  updateOptions(options, skipIfExists) {\n    if (!options) return;\n    const newOptions = options;\n    let optionsToUpdate = this.options;\n    // NOTE: this will get patched up when other animation methods support duration overrides\n    if (newOptions.duration != null) {\n      optionsToUpdate.duration = resolveTimingValue(newOptions.duration);\n    }\n    if (newOptions.delay != null) {\n      optionsToUpdate.delay = resolveTimingValue(newOptions.delay);\n    }\n    const newParams = newOptions.params;\n    if (newParams) {\n      let paramsToUpdate = optionsToUpdate.params;\n      if (!paramsToUpdate) {\n        paramsToUpdate = this.options.params = {};\n      }\n      Object.keys(newParams).forEach(name => {\n        if (!skipIfExists || !paramsToUpdate.hasOwnProperty(name)) {\n          paramsToUpdate[name] = interpolateParams(newParams[name], paramsToUpdate, this.errors);\n        }\n      });\n    }\n  }\n  _copyOptions() {\n    const options = {};\n    if (this.options) {\n      const oldParams = this.options.params;\n      if (oldParams) {\n        const params = options['params'] = {};\n        Object.keys(oldParams).forEach(name => {\n          params[name] = oldParams[name];\n        });\n      }\n    }\n    return options;\n  }\n  createSubContext(options = null, element, newTime) {\n    const target = element || this.element;\n    const context = new AnimationTimelineContext(this._driver, target, this.subInstructions, this._enterClassName, this._leaveClassName, this.errors, this.timelines, this.currentTimeline.fork(target, newTime || 0));\n    context.previousNode = this.previousNode;\n    context.currentAnimateTimings = this.currentAnimateTimings;\n    context.options = this._copyOptions();\n    context.updateOptions(options);\n    context.currentQueryIndex = this.currentQueryIndex;\n    context.currentQueryTotal = this.currentQueryTotal;\n    context.parentContext = this;\n    this.subContextCount++;\n    return context;\n  }\n  transformIntoNewTimeline(newTime) {\n    this.previousNode = DEFAULT_NOOP_PREVIOUS_NODE;\n    this.currentTimeline = this.currentTimeline.fork(this.element, newTime);\n    this.timelines.push(this.currentTimeline);\n    return this.currentTimeline;\n  }\n  appendInstructionToTimeline(instruction, duration, delay) {\n    const updatedTimings = {\n      duration: duration != null ? duration : instruction.duration,\n      delay: this.currentTimeline.currentTime + (delay != null ? delay : 0) + instruction.delay,\n      easing: ''\n    };\n    const builder = new SubTimelineBuilder(this._driver, instruction.element, instruction.keyframes, instruction.preStyleProps, instruction.postStyleProps, updatedTimings, instruction.stretchStartingKeyframe);\n    this.timelines.push(builder);\n    return updatedTimings;\n  }\n  incrementTime(time) {\n    this.currentTimeline.forwardTime(this.currentTimeline.duration + time);\n  }\n  delayNextStep(delay) {\n    // negative delays are not yet supported\n    if (delay > 0) {\n      this.currentTimeline.delayNextStep(delay);\n    }\n  }\n  invokeQuery(selector, originalSelector, limit, includeSelf, optional, errors) {\n    let results = [];\n    if (includeSelf) {\n      results.push(this.element);\n    }\n    if (selector.length > 0) {\n      // only if :self is used then the selector can be empty\n      selector = selector.replace(ENTER_TOKEN_REGEX, '.' + this._enterClassName);\n      selector = selector.replace(LEAVE_TOKEN_REGEX, '.' + this._leaveClassName);\n      const multi = limit != 1;\n      let elements = this._driver.query(this.element, selector, multi);\n      if (limit !== 0) {\n        elements = limit < 0 ? elements.slice(elements.length + limit, elements.length) : elements.slice(0, limit);\n      }\n      results.push(...elements);\n    }\n    if (!optional && results.length == 0) {\n      errors.push(invalidQuery(originalSelector));\n    }\n    return results;\n  }\n}\nclass TimelineBuilder {\n  _driver;\n  element;\n  startTime;\n  _elementTimelineStylesLookup;\n  duration = 0;\n  easing = null;\n  _previousKeyframe = new Map();\n  _currentKeyframe = new Map();\n  _keyframes = new Map();\n  _styleSummary = new Map();\n  _localTimelineStyles = new Map();\n  _globalTimelineStyles;\n  _pendingStyles = new Map();\n  _backFill = new Map();\n  _currentEmptyStepKeyframe = null;\n  constructor(_driver, element, startTime, _elementTimelineStylesLookup) {\n    this._driver = _driver;\n    this.element = element;\n    this.startTime = startTime;\n    this._elementTimelineStylesLookup = _elementTimelineStylesLookup;\n    if (!this._elementTimelineStylesLookup) {\n      this._elementTimelineStylesLookup = new Map();\n    }\n    this._globalTimelineStyles = this._elementTimelineStylesLookup.get(element);\n    if (!this._globalTimelineStyles) {\n      this._globalTimelineStyles = this._localTimelineStyles;\n      this._elementTimelineStylesLookup.set(element, this._localTimelineStyles);\n    }\n    this._loadKeyframe();\n  }\n  containsAnimation() {\n    switch (this._keyframes.size) {\n      case 0:\n        return false;\n      case 1:\n        return this.hasCurrentStyleProperties();\n      default:\n        return true;\n    }\n  }\n  hasCurrentStyleProperties() {\n    return this._currentKeyframe.size > 0;\n  }\n  get currentTime() {\n    return this.startTime + this.duration;\n  }\n  delayNextStep(delay) {\n    // in the event that a style() step is placed right before a stagger()\n    // and that style() step is the very first style() value in the animation\n    // then we need to make a copy of the keyframe [0, copy, 1] so that the delay\n    // properly applies the style() values to work with the stagger...\n    const hasPreStyleStep = this._keyframes.size === 1 && this._pendingStyles.size;\n    if (this.duration || hasPreStyleStep) {\n      this.forwardTime(this.currentTime + delay);\n      if (hasPreStyleStep) {\n        this.snapshotCurrentStyles();\n      }\n    } else {\n      this.startTime += delay;\n    }\n  }\n  fork(element, currentTime) {\n    this.applyStylesToKeyframe();\n    return new TimelineBuilder(this._driver, element, currentTime || this.currentTime, this._elementTimelineStylesLookup);\n  }\n  _loadKeyframe() {\n    if (this._currentKeyframe) {\n      this._previousKeyframe = this._currentKeyframe;\n    }\n    this._currentKeyframe = this._keyframes.get(this.duration);\n    if (!this._currentKeyframe) {\n      this._currentKeyframe = new Map();\n      this._keyframes.set(this.duration, this._currentKeyframe);\n    }\n  }\n  forwardFrame() {\n    this.duration += ONE_FRAME_IN_MILLISECONDS;\n    this._loadKeyframe();\n  }\n  forwardTime(time) {\n    this.applyStylesToKeyframe();\n    this.duration = time;\n    this._loadKeyframe();\n  }\n  _updateStyle(prop, value) {\n    this._localTimelineStyles.set(prop, value);\n    this._globalTimelineStyles.set(prop, value);\n    this._styleSummary.set(prop, {\n      time: this.currentTime,\n      value\n    });\n  }\n  allowOnlyTimelineStyles() {\n    return this._currentEmptyStepKeyframe !== this._currentKeyframe;\n  }\n  applyEmptyStep(easing) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    // special case for animate(duration):\n    // all missing styles are filled with a `*` value then\n    // if any destination styles are filled in later on the same\n    // keyframe then they will override the overridden styles\n    // We use `_globalTimelineStyles` here because there may be\n    // styles in previous keyframes that are not present in this timeline\n    for (let [prop, value] of this._globalTimelineStyles) {\n      this._backFill.set(prop, value || AUTO_STYLE);\n      this._currentKeyframe.set(prop, AUTO_STYLE);\n    }\n    this._currentEmptyStepKeyframe = this._currentKeyframe;\n  }\n  setStyles(input, easing, errors, options) {\n    if (easing) {\n      this._previousKeyframe.set('easing', easing);\n    }\n    const params = options && options.params || {};\n    const styles = flattenStyles(input, this._globalTimelineStyles);\n    for (let [prop, value] of styles) {\n      const val = interpolateParams(value, params, errors);\n      this._pendingStyles.set(prop, val);\n      if (!this._localTimelineStyles.has(prop)) {\n        this._backFill.set(prop, this._globalTimelineStyles.get(prop) ?? AUTO_STYLE);\n      }\n      this._updateStyle(prop, val);\n    }\n  }\n  applyStylesToKeyframe() {\n    if (this._pendingStyles.size == 0) return;\n    this._pendingStyles.forEach((val, prop) => {\n      this._currentKeyframe.set(prop, val);\n    });\n    this._pendingStyles.clear();\n    this._localTimelineStyles.forEach((val, prop) => {\n      if (!this._currentKeyframe.has(prop)) {\n        this._currentKeyframe.set(prop, val);\n      }\n    });\n  }\n  snapshotCurrentStyles() {\n    for (let [prop, val] of this._localTimelineStyles) {\n      this._pendingStyles.set(prop, val);\n      this._updateStyle(prop, val);\n    }\n  }\n  getFinalKeyframe() {\n    return this._keyframes.get(this.duration);\n  }\n  get properties() {\n    const properties = [];\n    for (let prop in this._currentKeyframe) {\n      properties.push(prop);\n    }\n    return properties;\n  }\n  mergeTimelineCollectedStyles(timeline) {\n    timeline._styleSummary.forEach((details1, prop) => {\n      const details0 = this._styleSummary.get(prop);\n      if (!details0 || details1.time > details0.time) {\n        this._updateStyle(prop, details1.value);\n      }\n    });\n  }\n  buildKeyframes() {\n    this.applyStylesToKeyframe();\n    const preStyleProps = new Set();\n    const postStyleProps = new Set();\n    const isEmpty = this._keyframes.size === 1 && this.duration === 0;\n    let finalKeyframes = [];\n    this._keyframes.forEach((keyframe, time) => {\n      const finalKeyframe = new Map([...this._backFill, ...keyframe]);\n      finalKeyframe.forEach((value, prop) => {\n        if (value === _PRE_STYLE) {\n          preStyleProps.add(prop);\n        } else if (value === AUTO_STYLE) {\n          postStyleProps.add(prop);\n        }\n      });\n      if (!isEmpty) {\n        finalKeyframe.set('offset', time / this.duration);\n      }\n      finalKeyframes.push(finalKeyframe);\n    });\n    const preProps = [...preStyleProps.values()];\n    const postProps = [...postStyleProps.values()];\n    // special case for a 0-second animation (which is designed just to place styles onscreen)\n    if (isEmpty) {\n      const kf0 = finalKeyframes[0];\n      const kf1 = new Map(kf0);\n      kf0.set('offset', 0);\n      kf1.set('offset', 1);\n      finalKeyframes = [kf0, kf1];\n    }\n    return createTimelineInstruction(this.element, finalKeyframes, preProps, postProps, this.duration, this.startTime, this.easing, false);\n  }\n}\nclass SubTimelineBuilder extends TimelineBuilder {\n  keyframes;\n  preStyleProps;\n  postStyleProps;\n  _stretchStartingKeyframe;\n  timings;\n  constructor(driver, element, keyframes, preStyleProps, postStyleProps, timings, _stretchStartingKeyframe = false) {\n    super(driver, element, timings.delay);\n    this.keyframes = keyframes;\n    this.preStyleProps = preStyleProps;\n    this.postStyleProps = postStyleProps;\n    this._stretchStartingKeyframe = _stretchStartingKeyframe;\n    this.timings = {\n      duration: timings.duration,\n      delay: timings.delay,\n      easing: timings.easing\n    };\n  }\n  containsAnimation() {\n    return this.keyframes.length > 1;\n  }\n  buildKeyframes() {\n    let keyframes = this.keyframes;\n    let {\n      delay,\n      duration,\n      easing\n    } = this.timings;\n    if (this._stretchStartingKeyframe && delay) {\n      const newKeyframes = [];\n      const totalTime = duration + delay;\n      const startingGap = delay / totalTime;\n      // the original starting keyframe now starts once the delay is done\n      const newFirstKeyframe = new Map(keyframes[0]);\n      newFirstKeyframe.set('offset', 0);\n      newKeyframes.push(newFirstKeyframe);\n      const oldFirstKeyframe = new Map(keyframes[0]);\n      oldFirstKeyframe.set('offset', roundOffset(startingGap));\n      newKeyframes.push(oldFirstKeyframe);\n      /*\n        When the keyframe is stretched then it means that the delay before the animation\n        starts is gone. Instead the first keyframe is placed at the start of the animation\n        and it is then copied to where it starts when the original delay is over. This basically\n        means nothing animates during that delay, but the styles are still rendered. For this\n        to work the original offset values that exist in the original keyframes must be \"warped\"\n        so that they can take the new keyframe + delay into account.\n               delay=1000, duration=1000, keyframes = 0 .5 1\n               turns into\n               delay=0, duration=2000, keyframes = 0 .33 .66 1\n       */\n      // offsets between 1 ... n -1 are all warped by the keyframe stretch\n      const limit = keyframes.length - 1;\n      for (let i = 1; i <= limit; i++) {\n        let kf = new Map(keyframes[i]);\n        const oldOffset = kf.get('offset');\n        const timeAtKeyframe = delay + oldOffset * duration;\n        kf.set('offset', roundOffset(timeAtKeyframe / totalTime));\n        newKeyframes.push(kf);\n      }\n      // the new starting keyframe should be added at the start\n      duration = totalTime;\n      delay = 0;\n      easing = '';\n      keyframes = newKeyframes;\n    }\n    return createTimelineInstruction(this.element, keyframes, this.preStyleProps, this.postStyleProps, duration, delay, easing, true);\n  }\n}\nfunction roundOffset(offset, decimalPoints = 3) {\n  const mult = Math.pow(10, decimalPoints - 1);\n  return Math.round(offset * mult) / mult;\n}\nfunction flattenStyles(input, allStyles) {\n  const styles = new Map();\n  let allProperties;\n  input.forEach(token => {\n    if (token === '*') {\n      allProperties ??= allStyles.keys();\n      for (let prop of allProperties) {\n        styles.set(prop, AUTO_STYLE);\n      }\n    } else {\n      for (let [prop, val] of token) {\n        styles.set(prop, val);\n      }\n    }\n  });\n  return styles;\n}\nfunction createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {\n  return {\n    type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,\n    element,\n    triggerName,\n    isRemovalTransition,\n    fromState,\n    fromStyles,\n    toState,\n    toStyles,\n    timelines,\n    queriedElements,\n    preStyleProps,\n    postStyleProps,\n    totalTime,\n    errors\n  };\n}\nconst EMPTY_OBJECT = {};\nclass AnimationTransitionFactory {\n  _triggerName;\n  ast;\n  _stateStyles;\n  constructor(_triggerName, ast, _stateStyles) {\n    this._triggerName = _triggerName;\n    this.ast = ast;\n    this._stateStyles = _stateStyles;\n  }\n  match(currentState, nextState, element, params) {\n    return oneOrMoreTransitionsMatch(this.ast.matchers, currentState, nextState, element, params);\n  }\n  buildStyles(stateName, params, errors) {\n    let styler = this._stateStyles.get('*');\n    if (stateName !== undefined) {\n      styler = this._stateStyles.get(stateName?.toString()) || styler;\n    }\n    return styler ? styler.buildStyles(params, errors) : new Map();\n  }\n  build(driver, element, currentState, nextState, enterClassName, leaveClassName, currentOptions, nextOptions, subInstructions, skipAstBuild) {\n    const errors = [];\n    const transitionAnimationParams = this.ast.options && this.ast.options.params || EMPTY_OBJECT;\n    const currentAnimationParams = currentOptions && currentOptions.params || EMPTY_OBJECT;\n    const currentStateStyles = this.buildStyles(currentState, currentAnimationParams, errors);\n    const nextAnimationParams = nextOptions && nextOptions.params || EMPTY_OBJECT;\n    const nextStateStyles = this.buildStyles(nextState, nextAnimationParams, errors);\n    const queriedElements = new Set();\n    const preStyleMap = new Map();\n    const postStyleMap = new Map();\n    const isRemoval = nextState === 'void';\n    const animationOptions = {\n      params: applyParamDefaults(nextAnimationParams, transitionAnimationParams),\n      delay: this.ast.options?.delay\n    };\n    const timelines = skipAstBuild ? [] : buildAnimationTimelines(driver, element, this.ast.animation, enterClassName, leaveClassName, currentStateStyles, nextStateStyles, animationOptions, subInstructions, errors);\n    let totalTime = 0;\n    timelines.forEach(tl => {\n      totalTime = Math.max(tl.duration + tl.delay, totalTime);\n    });\n    if (errors.length) {\n      return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, [], [], preStyleMap, postStyleMap, totalTime, errors);\n    }\n    timelines.forEach(tl => {\n      const elm = tl.element;\n      const preProps = getOrSetDefaultValue(preStyleMap, elm, new Set());\n      tl.preStyleProps.forEach(prop => preProps.add(prop));\n      const postProps = getOrSetDefaultValue(postStyleMap, elm, new Set());\n      tl.postStyleProps.forEach(prop => postProps.add(prop));\n      if (elm !== element) {\n        queriedElements.add(elm);\n      }\n    });\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkNonAnimatableInTimelines(timelines, this._triggerName, driver);\n    }\n    return createTransitionInstruction(element, this._triggerName, currentState, nextState, isRemoval, currentStateStyles, nextStateStyles, timelines, [...queriedElements.values()], preStyleMap, postStyleMap, totalTime);\n  }\n}\n/**\n * Checks inside a set of timelines if they try to animate a css property which is not considered\n * animatable, in that case it prints a warning on the console.\n * Besides that the function doesn't have any other effect.\n *\n * Note: this check is done here after the timelines are built instead of doing on a lower level so\n * that we can make sure that the warning appears only once per instruction (we can aggregate here\n * all the issues instead of finding them separately).\n *\n * @param timelines The built timelines for the current instruction.\n * @param triggerName The name of the trigger for the current instruction.\n * @param driver Animation driver used to perform the check.\n *\n */\nfunction checkNonAnimatableInTimelines(timelines, triggerName, driver) {\n  if (!driver.validateAnimatableStyleProperty) {\n    return;\n  }\n  const allowedNonAnimatableProps = new Set([\n  // 'easing' is a utility/synthetic prop we use to represent\n  // easing functions, it represents a property of the animation\n  // which is not animatable but different values can be used\n  // in different steps\n  'easing']);\n  const invalidNonAnimatableProps = new Set();\n  timelines.forEach(({\n    keyframes\n  }) => {\n    const nonAnimatablePropsInitialValues = new Map();\n    keyframes.forEach(keyframe => {\n      const entriesToCheck = Array.from(keyframe.entries()).filter(([prop]) => !allowedNonAnimatableProps.has(prop));\n      for (const [prop, value] of entriesToCheck) {\n        if (!driver.validateAnimatableStyleProperty(prop)) {\n          if (nonAnimatablePropsInitialValues.has(prop) && !invalidNonAnimatableProps.has(prop)) {\n            const propInitialValue = nonAnimatablePropsInitialValues.get(prop);\n            if (propInitialValue !== value) {\n              invalidNonAnimatableProps.add(prop);\n            }\n          } else {\n            nonAnimatablePropsInitialValues.set(prop, value);\n          }\n        }\n      }\n    });\n  });\n  if (invalidNonAnimatableProps.size > 0) {\n    console.warn(`Warning: The animation trigger \"${triggerName}\" is attempting to animate the following` + ' not animatable properties: ' + Array.from(invalidNonAnimatableProps).join(', ') + '\\n' + '(to check the list of all animatable properties visit https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties)');\n  }\n}\nfunction oneOrMoreTransitionsMatch(matchFns, currentState, nextState, element, params) {\n  return matchFns.some(fn => fn(currentState, nextState, element, params));\n}\nfunction applyParamDefaults(userParams, defaults) {\n  const result = {\n    ...defaults\n  };\n  Object.entries(userParams).forEach(([key, value]) => {\n    if (value != null) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\nclass AnimationStateStyles {\n  styles;\n  defaultParams;\n  normalizer;\n  constructor(styles, defaultParams, normalizer) {\n    this.styles = styles;\n    this.defaultParams = defaultParams;\n    this.normalizer = normalizer;\n  }\n  buildStyles(params, errors) {\n    const finalStyles = new Map();\n    const combinedParams = applyParamDefaults(params, this.defaultParams);\n    this.styles.styles.forEach(value => {\n      if (typeof value !== 'string') {\n        value.forEach((val, prop) => {\n          if (val) {\n            val = interpolateParams(val, combinedParams, errors);\n          }\n          const normalizedProp = this.normalizer.normalizePropertyName(prop, errors);\n          val = this.normalizer.normalizeStyleValue(prop, normalizedProp, val, errors);\n          finalStyles.set(prop, val);\n        });\n      }\n    });\n    return finalStyles;\n  }\n}\nfunction buildTrigger(name, ast, normalizer) {\n  return new AnimationTrigger(name, ast, normalizer);\n}\nclass AnimationTrigger {\n  name;\n  ast;\n  _normalizer;\n  transitionFactories = [];\n  fallbackTransition;\n  states = new Map();\n  constructor(name, ast, _normalizer) {\n    this.name = name;\n    this.ast = ast;\n    this._normalizer = _normalizer;\n    ast.states.forEach(ast => {\n      const defaultParams = ast.options && ast.options.params || {};\n      this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));\n    });\n    balanceProperties(this.states, 'true', '1');\n    balanceProperties(this.states, 'false', '0');\n    ast.transitions.forEach(ast => {\n      this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));\n    });\n    this.fallbackTransition = createFallbackTransition(name, this.states);\n  }\n  get containsQueries() {\n    return this.ast.queryCount > 0;\n  }\n  matchTransition(currentState, nextState, element, params) {\n    const entry = this.transitionFactories.find(f => f.match(currentState, nextState, element, params));\n    return entry || null;\n  }\n  matchStyles(currentState, params, errors) {\n    return this.fallbackTransition.buildStyles(currentState, params, errors);\n  }\n}\nfunction createFallbackTransition(triggerName, states, normalizer) {\n  const matchers = [(fromState, toState) => true];\n  const animation = {\n    type: AnimationMetadataType.Sequence,\n    steps: [],\n    options: null\n  };\n  const transition = {\n    type: AnimationMetadataType.Transition,\n    animation,\n    matchers,\n    options: null,\n    queryCount: 0,\n    depCount: 0\n  };\n  return new AnimationTransitionFactory(triggerName, transition, states);\n}\nfunction balanceProperties(stateMap, key1, key2) {\n  if (stateMap.has(key1)) {\n    if (!stateMap.has(key2)) {\n      stateMap.set(key2, stateMap.get(key1));\n    }\n  } else if (stateMap.has(key2)) {\n    stateMap.set(key1, stateMap.get(key2));\n  }\n}\nconst EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();\nclass TimelineAnimationEngine {\n  bodyNode;\n  _driver;\n  _normalizer;\n  _animations = new Map();\n  _playersById = new Map();\n  players = [];\n  constructor(bodyNode, _driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n  }\n  register(id, metadata) {\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n    if (errors.length) {\n      throw registerFailed(errors);\n    } else {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnRegister(warnings);\n        }\n      }\n      this._animations.set(id, ast);\n    }\n  }\n  _buildPlayer(i, preStyles, postStyles) {\n    const element = i.element;\n    const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);\n    return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);\n  }\n  create(id, element, options = {}) {\n    const errors = [];\n    const ast = this._animations.get(id);\n    let instructions;\n    const autoStylesMap = new Map();\n    if (ast) {\n      instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);\n      instructions.forEach(inst => {\n        const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());\n        inst.postStyleProps.forEach(prop => styles.set(prop, null));\n      });\n    } else {\n      errors.push(missingOrDestroyedAnimation());\n      instructions = [];\n    }\n    if (errors.length) {\n      throw createAnimationFailed(errors);\n    }\n    autoStylesMap.forEach((styles, element) => {\n      styles.forEach((_, prop) => {\n        styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));\n      });\n    });\n    const players = instructions.map(i => {\n      const styles = autoStylesMap.get(i.element);\n      return this._buildPlayer(i, new Map(), styles);\n    });\n    const player = optimizeGroupPlayer(players);\n    this._playersById.set(id, player);\n    player.onDestroy(() => this.destroy(id));\n    this.players.push(player);\n    return player;\n  }\n  destroy(id) {\n    const player = this._getPlayer(id);\n    player.destroy();\n    this._playersById.delete(id);\n    const index = this.players.indexOf(player);\n    if (index >= 0) {\n      this.players.splice(index, 1);\n    }\n  }\n  _getPlayer(id) {\n    const player = this._playersById.get(id);\n    if (!player) {\n      throw missingPlayer(id);\n    }\n    return player;\n  }\n  listen(id, element, eventName, callback) {\n    // triggerName, fromState, toState are all ignored for timeline animations\n    const baseEvent = makeAnimationEvent(element, '', '', '');\n    listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);\n    return () => {};\n  }\n  command(id, element, command, args) {\n    if (command == 'register') {\n      this.register(id, args[0]);\n      return;\n    }\n    if (command == 'create') {\n      const options = args[0] || {};\n      this.create(id, element, options);\n      return;\n    }\n    const player = this._getPlayer(id);\n    switch (command) {\n      case 'play':\n        player.play();\n        break;\n      case 'pause':\n        player.pause();\n        break;\n      case 'reset':\n        player.reset();\n        break;\n      case 'restart':\n        player.restart();\n        break;\n      case 'finish':\n        player.finish();\n        break;\n      case 'init':\n        player.init();\n        break;\n      case 'setPosition':\n        player.setPosition(parseFloat(args[0]));\n        break;\n      case 'destroy':\n        this.destroy(id);\n        break;\n    }\n  }\n}\nconst QUEUED_CLASSNAME = 'ng-animate-queued';\nconst QUEUED_SELECTOR = '.ng-animate-queued';\nconst DISABLED_CLASSNAME = 'ng-animate-disabled';\nconst DISABLED_SELECTOR = '.ng-animate-disabled';\nconst STAR_CLASSNAME = 'ng-star-inserted';\nconst STAR_SELECTOR = '.ng-star-inserted';\nconst EMPTY_PLAYER_ARRAY = [];\nconst NULL_REMOVAL_STATE = {\n  namespaceId: '',\n  setForRemoval: false,\n  setForMove: false,\n  hasAnimation: false,\n  removedBeforeQueried: false\n};\nconst NULL_REMOVED_QUERIED_STATE = {\n  namespaceId: '',\n  setForMove: false,\n  setForRemoval: false,\n  hasAnimation: false,\n  removedBeforeQueried: true\n};\nconst REMOVAL_FLAG = '__ng_removed';\nclass StateValue {\n  namespaceId;\n  value;\n  options;\n  get params() {\n    return this.options.params;\n  }\n  constructor(input, namespaceId = '') {\n    this.namespaceId = namespaceId;\n    const isObj = input && input.hasOwnProperty('value');\n    const value = isObj ? input['value'] : input;\n    this.value = normalizeTriggerValue(value);\n    if (isObj) {\n      // we drop the value property from options.\n      const {\n        value,\n        ...options\n      } = input;\n      this.options = options;\n    } else {\n      this.options = {};\n    }\n    if (!this.options.params) {\n      this.options.params = {};\n    }\n  }\n  absorbOptions(options) {\n    const newParams = options.params;\n    if (newParams) {\n      const oldParams = this.options.params;\n      Object.keys(newParams).forEach(prop => {\n        if (oldParams[prop] == null) {\n          oldParams[prop] = newParams[prop];\n        }\n      });\n    }\n  }\n}\nconst VOID_VALUE = 'void';\nconst DEFAULT_STATE_VALUE = new StateValue(VOID_VALUE);\nclass AnimationTransitionNamespace {\n  id;\n  hostElement;\n  _engine;\n  players = [];\n  _triggers = new Map();\n  _queue = [];\n  _elementListeners = new Map();\n  _hostClassName;\n  constructor(id, hostElement, _engine) {\n    this.id = id;\n    this.hostElement = hostElement;\n    this._engine = _engine;\n    this._hostClassName = 'ng-tns-' + id;\n    addClass(hostElement, this._hostClassName);\n  }\n  listen(element, name, phase, callback) {\n    if (!this._triggers.has(name)) {\n      throw missingTrigger(phase, name);\n    }\n    if (phase == null || phase.length == 0) {\n      throw missingEvent(name);\n    }\n    if (!isTriggerEventValid(phase)) {\n      throw unsupportedTriggerEvent(phase, name);\n    }\n    const listeners = getOrSetDefaultValue(this._elementListeners, element, []);\n    const data = {\n      name,\n      phase,\n      callback\n    };\n    listeners.push(data);\n    const triggersWithStates = getOrSetDefaultValue(this._engine.statesByElement, element, new Map());\n    if (!triggersWithStates.has(name)) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + name);\n      triggersWithStates.set(name, DEFAULT_STATE_VALUE);\n    }\n    return () => {\n      // the event listener is removed AFTER the flush has occurred such\n      // that leave animations callbacks can fire (otherwise if the node\n      // is removed in between then the listeners would be deregistered)\n      this._engine.afterFlush(() => {\n        const index = listeners.indexOf(data);\n        if (index >= 0) {\n          listeners.splice(index, 1);\n        }\n        if (!this._triggers.has(name)) {\n          triggersWithStates.delete(name);\n        }\n      });\n    };\n  }\n  register(name, ast) {\n    if (this._triggers.has(name)) {\n      // throw\n      return false;\n    } else {\n      this._triggers.set(name, ast);\n      return true;\n    }\n  }\n  _getTrigger(name) {\n    const trigger = this._triggers.get(name);\n    if (!trigger) {\n      throw unregisteredTrigger(name);\n    }\n    return trigger;\n  }\n  trigger(element, triggerName, value, defaultToFallback = true) {\n    const trigger = this._getTrigger(triggerName);\n    const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n    let triggersWithStates = this._engine.statesByElement.get(element);\n    if (!triggersWithStates) {\n      addClass(element, NG_TRIGGER_CLASSNAME);\n      addClass(element, NG_TRIGGER_CLASSNAME + '-' + triggerName);\n      this._engine.statesByElement.set(element, triggersWithStates = new Map());\n    }\n    let fromState = triggersWithStates.get(triggerName);\n    const toState = new StateValue(value, this.id);\n    const isObj = value && value.hasOwnProperty('value');\n    if (!isObj && fromState) {\n      toState.absorbOptions(fromState.options);\n    }\n    triggersWithStates.set(triggerName, toState);\n    if (!fromState) {\n      fromState = DEFAULT_STATE_VALUE;\n    }\n    const isRemoval = toState.value === VOID_VALUE;\n    // normally this isn't reached by here, however, if an object expression\n    // is passed in then it may be a new object each time. Comparing the value\n    // is important since that will stay the same despite there being a new object.\n    // The removal arc here is special cased because the same element is triggered\n    // twice in the event that it contains animations on the outer/inner portions\n    // of the host container\n    if (!isRemoval && fromState.value === toState.value) {\n      // this means that despite the value not changing, some inner params\n      // have changed which means that the animation final styles need to be applied\n      if (!objEquals(fromState.params, toState.params)) {\n        const errors = [];\n        const fromStyles = trigger.matchStyles(fromState.value, fromState.params, errors);\n        const toStyles = trigger.matchStyles(toState.value, toState.params, errors);\n        if (errors.length) {\n          this._engine.reportError(errors);\n        } else {\n          this._engine.afterFlush(() => {\n            eraseStyles(element, fromStyles);\n            setStyles(element, toStyles);\n          });\n        }\n      }\n      return;\n    }\n    const playersOnElement = getOrSetDefaultValue(this._engine.playersByElement, element, []);\n    playersOnElement.forEach(player => {\n      // only remove the player if it is queued on the EXACT same trigger/namespace\n      // we only also deal with queued players here because if the animation has\n      // started then we want to keep the player alive until the flush happens\n      // (which is where the previousPlayers are passed into the new player)\n      if (player.namespaceId == this.id && player.triggerName == triggerName && player.queued) {\n        player.destroy();\n      }\n    });\n    let transition = trigger.matchTransition(fromState.value, toState.value, element, toState.params);\n    let isFallbackTransition = false;\n    if (!transition) {\n      if (!defaultToFallback) return;\n      transition = trigger.fallbackTransition;\n      isFallbackTransition = true;\n    }\n    this._engine.totalQueuedPlayers++;\n    this._queue.push({\n      element,\n      triggerName,\n      transition,\n      fromState,\n      toState,\n      player,\n      isFallbackTransition\n    });\n    if (!isFallbackTransition) {\n      addClass(element, QUEUED_CLASSNAME);\n      player.onStart(() => {\n        removeClass(element, QUEUED_CLASSNAME);\n      });\n    }\n    player.onDone(() => {\n      let index = this.players.indexOf(player);\n      if (index >= 0) {\n        this.players.splice(index, 1);\n      }\n      const players = this._engine.playersByElement.get(element);\n      if (players) {\n        let index = players.indexOf(player);\n        if (index >= 0) {\n          players.splice(index, 1);\n        }\n      }\n    });\n    this.players.push(player);\n    playersOnElement.push(player);\n    return player;\n  }\n  deregister(name) {\n    this._triggers.delete(name);\n    this._engine.statesByElement.forEach(stateMap => stateMap.delete(name));\n    this._elementListeners.forEach((listeners, element) => {\n      this._elementListeners.set(element, listeners.filter(entry => {\n        return entry.name != name;\n      }));\n    });\n  }\n  clearElementCache(element) {\n    this._engine.statesByElement.delete(element);\n    this._elementListeners.delete(element);\n    const elementPlayers = this._engine.playersByElement.get(element);\n    if (elementPlayers) {\n      elementPlayers.forEach(player => player.destroy());\n      this._engine.playersByElement.delete(element);\n    }\n  }\n  _signalRemovalForInnerTriggers(rootElement, context) {\n    const elements = this._engine.driver.query(rootElement, NG_TRIGGER_SELECTOR, true);\n    // emulate a leave animation for all inner nodes within this node.\n    // If there are no animations found for any of the nodes then clear the cache\n    // for the element.\n    elements.forEach(elm => {\n      // this means that an inner remove() operation has already kicked off\n      // the animation on this element...\n      if (elm[REMOVAL_FLAG]) return;\n      const namespaces = this._engine.fetchNamespacesByElement(elm);\n      if (namespaces.size) {\n        namespaces.forEach(ns => ns.triggerLeaveAnimation(elm, context, false, true));\n      } else {\n        this.clearElementCache(elm);\n      }\n    });\n    // If the child elements were removed along with the parent, their animations might not\n    // have completed. Clear all the elements from the cache so we don't end up with a memory leak.\n    this._engine.afterFlushAnimationsDone(() => elements.forEach(elm => this.clearElementCache(elm)));\n  }\n  triggerLeaveAnimation(element, context, destroyAfterComplete, defaultToFallback) {\n    const triggerStates = this._engine.statesByElement.get(element);\n    const previousTriggersValues = new Map();\n    if (triggerStates) {\n      const players = [];\n      triggerStates.forEach((state, triggerName) => {\n        previousTriggersValues.set(triggerName, state.value);\n        // this check is here in the event that an element is removed\n        // twice (both on the host level and the component level)\n        if (this._triggers.has(triggerName)) {\n          const player = this.trigger(element, triggerName, VOID_VALUE, defaultToFallback);\n          if (player) {\n            players.push(player);\n          }\n        }\n      });\n      if (players.length) {\n        this._engine.markElementAsRemoved(this.id, element, true, context, previousTriggersValues);\n        if (destroyAfterComplete) {\n          optimizeGroupPlayer(players).onDone(() => this._engine.processLeaveNode(element));\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n  prepareLeaveAnimationListeners(element) {\n    const listeners = this._elementListeners.get(element);\n    const elementStates = this._engine.statesByElement.get(element);\n    // if this statement fails then it means that the element was picked up\n    // by an earlier flush (or there are no listeners at all to track the leave).\n    if (listeners && elementStates) {\n      const visitedTriggers = new Set();\n      listeners.forEach(listener => {\n        const triggerName = listener.name;\n        if (visitedTriggers.has(triggerName)) return;\n        visitedTriggers.add(triggerName);\n        const trigger = this._triggers.get(triggerName);\n        const transition = trigger.fallbackTransition;\n        const fromState = elementStates.get(triggerName) || DEFAULT_STATE_VALUE;\n        const toState = new StateValue(VOID_VALUE);\n        const player = new TransitionAnimationPlayer(this.id, triggerName, element);\n        this._engine.totalQueuedPlayers++;\n        this._queue.push({\n          element,\n          triggerName,\n          transition,\n          fromState,\n          toState,\n          player,\n          isFallbackTransition: true\n        });\n      });\n    }\n  }\n  removeNode(element, context) {\n    const engine = this._engine;\n    if (element.childElementCount) {\n      this._signalRemovalForInnerTriggers(element, context);\n    }\n    // this means that a * => VOID animation was detected and kicked off\n    if (this.triggerLeaveAnimation(element, context, true)) return;\n    // find the player that is animating and make sure that the\n    // removal is delayed until that player has completed\n    let containsPotentialParentTransition = false;\n    if (engine.totalAnimations) {\n      const currentPlayers = engine.players.length ? engine.playersByQueriedElement.get(element) : [];\n      // when this `if statement` does not continue forward it means that\n      // a previous animation query has selected the current element and\n      // is animating it. In this situation want to continue forwards and\n      // allow the element to be queued up for animation later.\n      if (currentPlayers && currentPlayers.length) {\n        containsPotentialParentTransition = true;\n      } else {\n        let parent = element;\n        while (parent = parent.parentNode) {\n          const triggers = engine.statesByElement.get(parent);\n          if (triggers) {\n            containsPotentialParentTransition = true;\n            break;\n          }\n        }\n      }\n    }\n    // at this stage we know that the element will either get removed\n    // during flush or will be picked up by a parent query. Either way\n    // we need to fire the listeners for this element when it DOES get\n    // removed (once the query parent animation is done or after flush)\n    this.prepareLeaveAnimationListeners(element);\n    // whether or not a parent has an animation we need to delay the deferral of the leave\n    // operation until we have more information (which we do after flush() has been called)\n    if (containsPotentialParentTransition) {\n      engine.markElementAsRemoved(this.id, element, false, context);\n    } else {\n      const removalFlag = element[REMOVAL_FLAG];\n      if (!removalFlag || removalFlag === NULL_REMOVAL_STATE) {\n        // we do this after the flush has occurred such\n        // that the callbacks can be fired\n        engine.afterFlush(() => this.clearElementCache(element));\n        engine.destroyInnerAnimations(element);\n        engine._onRemovalComplete(element, context);\n      }\n    }\n  }\n  insertNode(element, parent) {\n    addClass(element, this._hostClassName);\n  }\n  drainQueuedTransitions(microtaskId) {\n    const instructions = [];\n    this._queue.forEach(entry => {\n      const player = entry.player;\n      if (player.destroyed) return;\n      const element = entry.element;\n      const listeners = this._elementListeners.get(element);\n      if (listeners) {\n        listeners.forEach(listener => {\n          if (listener.name == entry.triggerName) {\n            const baseEvent = makeAnimationEvent(element, entry.triggerName, entry.fromState.value, entry.toState.value);\n            baseEvent['_data'] = microtaskId;\n            listenOnPlayer(entry.player, listener.phase, baseEvent, listener.callback);\n          }\n        });\n      }\n      if (player.markedForDestroy) {\n        this._engine.afterFlush(() => {\n          // now we can destroy the element properly since the event listeners have\n          // been bound to the player\n          player.destroy();\n        });\n      } else {\n        instructions.push(entry);\n      }\n    });\n    this._queue = [];\n    return instructions.sort((a, b) => {\n      // if depCount == 0 them move to front\n      // otherwise if a contains b then move back\n      const d0 = a.transition.ast.depCount;\n      const d1 = b.transition.ast.depCount;\n      if (d0 == 0 || d1 == 0) {\n        return d0 - d1;\n      }\n      return this._engine.driver.containsElement(a.element, b.element) ? 1 : -1;\n    });\n  }\n  destroy(context) {\n    this.players.forEach(p => p.destroy());\n    this._signalRemovalForInnerTriggers(this.hostElement, context);\n  }\n}\nclass TransitionAnimationEngine {\n  bodyNode;\n  driver;\n  _normalizer;\n  players = [];\n  newHostElements = new Map();\n  playersByElement = new Map();\n  playersByQueriedElement = new Map();\n  statesByElement = new Map();\n  disabledNodes = new Set();\n  totalAnimations = 0;\n  totalQueuedPlayers = 0;\n  _namespaceLookup = {};\n  _namespaceList = [];\n  _flushFns = [];\n  _whenQuietFns = [];\n  namespacesByHostElement = new Map();\n  collectedEnterElements = [];\n  collectedLeaveElements = [];\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  /** @internal */\n  _onRemovalComplete(element, context) {\n    this.onRemovalComplete(element, context);\n  }\n  constructor(bodyNode, driver, _normalizer) {\n    this.bodyNode = bodyNode;\n    this.driver = driver;\n    this._normalizer = _normalizer;\n  }\n  get queuedPlayers() {\n    const players = [];\n    this._namespaceList.forEach(ns => {\n      ns.players.forEach(player => {\n        if (player.queued) {\n          players.push(player);\n        }\n      });\n    });\n    return players;\n  }\n  createNamespace(namespaceId, hostElement) {\n    const ns = new AnimationTransitionNamespace(namespaceId, hostElement, this);\n    if (this.bodyNode && this.driver.containsElement(this.bodyNode, hostElement)) {\n      this._balanceNamespaceList(ns, hostElement);\n    } else {\n      // defer this later until flush during when the host element has\n      // been inserted so that we know exactly where to place it in\n      // the namespace list\n      this.newHostElements.set(hostElement, ns);\n      // given that this host element is a part of the animation code, it\n      // may or may not be inserted by a parent node that is of an\n      // animation renderer type. If this happens then we can still have\n      // access to this item when we query for :enter nodes. If the parent\n      // is a renderer then the set data-structure will normalize the entry\n      this.collectEnterElement(hostElement);\n    }\n    return this._namespaceLookup[namespaceId] = ns;\n  }\n  _balanceNamespaceList(ns, hostElement) {\n    const namespaceList = this._namespaceList;\n    const namespacesByHostElement = this.namespacesByHostElement;\n    const limit = namespaceList.length - 1;\n    if (limit >= 0) {\n      let found = false;\n      // Find the closest ancestor with an existing namespace so we can then insert `ns` after it,\n      // establishing a top-down ordering of namespaces in `this._namespaceList`.\n      let ancestor = this.driver.getParentElement(hostElement);\n      while (ancestor) {\n        const ancestorNs = namespacesByHostElement.get(ancestor);\n        if (ancestorNs) {\n          // An animation namespace has been registered for this ancestor, so we insert `ns`\n          // right after it to establish top-down ordering of animation namespaces.\n          const index = namespaceList.indexOf(ancestorNs);\n          namespaceList.splice(index + 1, 0, ns);\n          found = true;\n          break;\n        }\n        ancestor = this.driver.getParentElement(ancestor);\n      }\n      if (!found) {\n        // No namespace exists that is an ancestor of `ns`, so `ns` is inserted at the front to\n        // ensure that any existing descendants are ordered after `ns`, retaining the desired\n        // top-down ordering.\n        namespaceList.unshift(ns);\n      }\n    } else {\n      namespaceList.push(ns);\n    }\n    namespacesByHostElement.set(hostElement, ns);\n    return ns;\n  }\n  register(namespaceId, hostElement) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (!ns) {\n      ns = this.createNamespace(namespaceId, hostElement);\n    }\n    return ns;\n  }\n  registerTrigger(namespaceId, name, trigger) {\n    let ns = this._namespaceLookup[namespaceId];\n    if (ns && ns.register(name, trigger)) {\n      this.totalAnimations++;\n    }\n  }\n  destroy(namespaceId, context) {\n    if (!namespaceId) return;\n    this.afterFlush(() => {});\n    this.afterFlushAnimationsDone(() => {\n      const ns = this._fetchNamespace(namespaceId);\n      this.namespacesByHostElement.delete(ns.hostElement);\n      const index = this._namespaceList.indexOf(ns);\n      if (index >= 0) {\n        this._namespaceList.splice(index, 1);\n      }\n      ns.destroy(context);\n      delete this._namespaceLookup[namespaceId];\n    });\n  }\n  _fetchNamespace(id) {\n    return this._namespaceLookup[id];\n  }\n  fetchNamespacesByElement(element) {\n    // normally there should only be one namespace per element, however\n    // if @triggers are placed on both the component element and then\n    // its host element (within the component code) then there will be\n    // two namespaces returned. We use a set here to simply deduplicate\n    // the namespaces in case (for the reason described above) there are multiple triggers\n    const namespaces = new Set();\n    const elementStates = this.statesByElement.get(element);\n    if (elementStates) {\n      for (let stateValue of elementStates.values()) {\n        if (stateValue.namespaceId) {\n          const ns = this._fetchNamespace(stateValue.namespaceId);\n          if (ns) {\n            namespaces.add(ns);\n          }\n        }\n      }\n    }\n    return namespaces;\n  }\n  trigger(namespaceId, element, name, value) {\n    if (isElementNode(element)) {\n      const ns = this._fetchNamespace(namespaceId);\n      if (ns) {\n        ns.trigger(element, name, value);\n        return true;\n      }\n    }\n    return false;\n  }\n  insertNode(namespaceId, element, parent, insertBefore) {\n    if (!isElementNode(element)) return;\n    // special case for when an element is removed and reinserted (move operation)\n    // when this occurs we do not want to use the element for deletion later\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      details.setForRemoval = false;\n      details.setForMove = true;\n      const index = this.collectedLeaveElements.indexOf(element);\n      if (index >= 0) {\n        this.collectedLeaveElements.splice(index, 1);\n      }\n    }\n    // in the event that the namespaceId is blank then the caller\n    // code does not contain any animation code in it, but it is\n    // just being called so that the node is marked as being inserted\n    if (namespaceId) {\n      const ns = this._fetchNamespace(namespaceId);\n      // This if-statement is a workaround for router issue #21947.\n      // The router sometimes hits a race condition where while a route\n      // is being instantiated a new navigation arrives, triggering leave\n      // animation of DOM that has not been fully initialized, until this\n      // is resolved, we need to handle the scenario when DOM is not in a\n      // consistent state during the animation.\n      if (ns) {\n        ns.insertNode(element, parent);\n      }\n    }\n    // only *directives and host elements are inserted before\n    if (insertBefore) {\n      this.collectEnterElement(element);\n    }\n  }\n  collectEnterElement(element) {\n    this.collectedEnterElements.push(element);\n  }\n  markElementAsDisabled(element, value) {\n    if (value) {\n      if (!this.disabledNodes.has(element)) {\n        this.disabledNodes.add(element);\n        addClass(element, DISABLED_CLASSNAME);\n      }\n    } else if (this.disabledNodes.has(element)) {\n      this.disabledNodes.delete(element);\n      removeClass(element, DISABLED_CLASSNAME);\n    }\n  }\n  removeNode(namespaceId, element, context) {\n    if (isElementNode(element)) {\n      const ns = namespaceId ? this._fetchNamespace(namespaceId) : null;\n      if (ns) {\n        ns.removeNode(element, context);\n      } else {\n        this.markElementAsRemoved(namespaceId, element, false, context);\n      }\n      const hostNS = this.namespacesByHostElement.get(element);\n      if (hostNS && hostNS.id !== namespaceId) {\n        hostNS.removeNode(element, context);\n      }\n    } else {\n      this._onRemovalComplete(element, context);\n    }\n  }\n  markElementAsRemoved(namespaceId, element, hasAnimation, context, previousTriggersValues) {\n    this.collectedLeaveElements.push(element);\n    element[REMOVAL_FLAG] = {\n      namespaceId,\n      setForRemoval: context,\n      hasAnimation,\n      removedBeforeQueried: false,\n      previousTriggersValues\n    };\n  }\n  listen(namespaceId, element, name, phase, callback) {\n    if (isElementNode(element)) {\n      return this._fetchNamespace(namespaceId).listen(element, name, phase, callback);\n    }\n    return () => {};\n  }\n  _buildInstruction(entry, subTimelines, enterClassName, leaveClassName, skipBuildAst) {\n    return entry.transition.build(this.driver, entry.element, entry.fromState.value, entry.toState.value, enterClassName, leaveClassName, entry.fromState.options, entry.toState.options, subTimelines, skipBuildAst);\n  }\n  destroyInnerAnimations(containerElement) {\n    let elements = this.driver.query(containerElement, NG_TRIGGER_SELECTOR, true);\n    elements.forEach(element => this.destroyActiveAnimationsForElement(element));\n    if (this.playersByQueriedElement.size == 0) return;\n    elements = this.driver.query(containerElement, NG_ANIMATING_SELECTOR, true);\n    elements.forEach(element => this.finishActiveQueriedAnimationOnElement(element));\n  }\n  destroyActiveAnimationsForElement(element) {\n    const players = this.playersByElement.get(element);\n    if (players) {\n      players.forEach(player => {\n        // special case for when an element is set for destruction, but hasn't started.\n        // in this situation we want to delay the destruction until the flush occurs\n        // so that any event listeners attached to the player are triggered.\n        if (player.queued) {\n          player.markedForDestroy = true;\n        } else {\n          player.destroy();\n        }\n      });\n    }\n  }\n  finishActiveQueriedAnimationOnElement(element) {\n    const players = this.playersByQueriedElement.get(element);\n    if (players) {\n      players.forEach(player => player.finish());\n    }\n  }\n  whenRenderingDone() {\n    return new Promise(resolve => {\n      if (this.players.length) {\n        return optimizeGroupPlayer(this.players).onDone(() => resolve());\n      } else {\n        resolve();\n      }\n    });\n  }\n  processLeaveNode(element) {\n    const details = element[REMOVAL_FLAG];\n    if (details && details.setForRemoval) {\n      // this will prevent it from removing it twice\n      element[REMOVAL_FLAG] = NULL_REMOVAL_STATE;\n      if (details.namespaceId) {\n        this.destroyInnerAnimations(element);\n        const ns = this._fetchNamespace(details.namespaceId);\n        if (ns) {\n          ns.clearElementCache(element);\n        }\n      }\n      this._onRemovalComplete(element, details.setForRemoval);\n    }\n    if (element.classList?.contains(DISABLED_CLASSNAME)) {\n      this.markElementAsDisabled(element, false);\n    }\n    this.driver.query(element, DISABLED_SELECTOR, true).forEach(node => {\n      this.markElementAsDisabled(node, false);\n    });\n  }\n  flush(microtaskId = -1) {\n    let players = [];\n    if (this.newHostElements.size) {\n      this.newHostElements.forEach((ns, element) => this._balanceNamespaceList(ns, element));\n      this.newHostElements.clear();\n    }\n    if (this.totalAnimations && this.collectedEnterElements.length) {\n      for (let i = 0; i < this.collectedEnterElements.length; i++) {\n        const elm = this.collectedEnterElements[i];\n        addClass(elm, STAR_CLASSNAME);\n      }\n    }\n    if (this._namespaceList.length && (this.totalQueuedPlayers || this.collectedLeaveElements.length)) {\n      const cleanupFns = [];\n      try {\n        players = this._flushAnimations(cleanupFns, microtaskId);\n      } finally {\n        for (let i = 0; i < cleanupFns.length; i++) {\n          cleanupFns[i]();\n        }\n      }\n    } else {\n      for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n        const element = this.collectedLeaveElements[i];\n        this.processLeaveNode(element);\n      }\n    }\n    this.totalQueuedPlayers = 0;\n    this.collectedEnterElements.length = 0;\n    this.collectedLeaveElements.length = 0;\n    this._flushFns.forEach(fn => fn());\n    this._flushFns = [];\n    if (this._whenQuietFns.length) {\n      // we move these over to a variable so that\n      // if any new callbacks are registered in another\n      // flush they do not populate the existing set\n      const quietFns = this._whenQuietFns;\n      this._whenQuietFns = [];\n      if (players.length) {\n        optimizeGroupPlayer(players).onDone(() => {\n          quietFns.forEach(fn => fn());\n        });\n      } else {\n        quietFns.forEach(fn => fn());\n      }\n    }\n  }\n  reportError(errors) {\n    throw triggerTransitionsFailed(errors);\n  }\n  _flushAnimations(cleanupFns, microtaskId) {\n    const subTimelines = new ElementInstructionMap();\n    const skippedPlayers = [];\n    const skippedPlayersMap = new Map();\n    const queuedInstructions = [];\n    const queriedElements = new Map();\n    const allPreStyleElements = new Map();\n    const allPostStyleElements = new Map();\n    const disabledElementsSet = new Set();\n    this.disabledNodes.forEach(node => {\n      disabledElementsSet.add(node);\n      const nodesThatAreDisabled = this.driver.query(node, QUEUED_SELECTOR, true);\n      for (let i = 0; i < nodesThatAreDisabled.length; i++) {\n        disabledElementsSet.add(nodesThatAreDisabled[i]);\n      }\n    });\n    const bodyNode = this.bodyNode;\n    const allTriggerElements = Array.from(this.statesByElement.keys());\n    const enterNodeMap = buildRootMap(allTriggerElements, this.collectedEnterElements);\n    // this must occur before the instructions are built below such that\n    // the :enter queries match the elements (since the timeline queries\n    // are fired during instruction building).\n    const enterNodeMapIds = new Map();\n    let i = 0;\n    enterNodeMap.forEach((nodes, root) => {\n      const className = ENTER_CLASSNAME + i++;\n      enterNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    const allLeaveNodes = [];\n    const mergedLeaveNodes = new Set();\n    const leaveNodesWithoutAnimations = new Set();\n    for (let i = 0; i < this.collectedLeaveElements.length; i++) {\n      const element = this.collectedLeaveElements[i];\n      const details = element[REMOVAL_FLAG];\n      if (details && details.setForRemoval) {\n        allLeaveNodes.push(element);\n        mergedLeaveNodes.add(element);\n        if (details.hasAnimation) {\n          this.driver.query(element, STAR_SELECTOR, true).forEach(elm => mergedLeaveNodes.add(elm));\n        } else {\n          leaveNodesWithoutAnimations.add(element);\n        }\n      }\n    }\n    const leaveNodeMapIds = new Map();\n    const leaveNodeMap = buildRootMap(allTriggerElements, Array.from(mergedLeaveNodes));\n    leaveNodeMap.forEach((nodes, root) => {\n      const className = LEAVE_CLASSNAME + i++;\n      leaveNodeMapIds.set(root, className);\n      nodes.forEach(node => addClass(node, className));\n    });\n    cleanupFns.push(() => {\n      enterNodeMap.forEach((nodes, root) => {\n        const className = enterNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      leaveNodeMap.forEach((nodes, root) => {\n        const className = leaveNodeMapIds.get(root);\n        nodes.forEach(node => removeClass(node, className));\n      });\n      allLeaveNodes.forEach(element => {\n        this.processLeaveNode(element);\n      });\n    });\n    const allPlayers = [];\n    const erroneousTransitions = [];\n    for (let i = this._namespaceList.length - 1; i >= 0; i--) {\n      const ns = this._namespaceList[i];\n      ns.drainQueuedTransitions(microtaskId).forEach(entry => {\n        const player = entry.player;\n        const element = entry.element;\n        allPlayers.push(player);\n        if (this.collectedEnterElements.length) {\n          const details = element[REMOVAL_FLAG];\n          // animations for move operations (elements being removed and reinserted,\n          // e.g. when the order of an *ngFor list changes) are currently not supported\n          if (details && details.setForMove) {\n            if (details.previousTriggersValues && details.previousTriggersValues.has(entry.triggerName)) {\n              const previousValue = details.previousTriggersValues.get(entry.triggerName);\n              // we need to restore the previous trigger value since the element has\n              // only been moved and hasn't actually left the DOM\n              const triggersWithStates = this.statesByElement.get(entry.element);\n              if (triggersWithStates && triggersWithStates.has(entry.triggerName)) {\n                const state = triggersWithStates.get(entry.triggerName);\n                state.value = previousValue;\n                triggersWithStates.set(entry.triggerName, state);\n              }\n            }\n            player.destroy();\n            return;\n          }\n        }\n        const nodeIsOrphaned = !bodyNode || !this.driver.containsElement(bodyNode, element);\n        const leaveClassName = leaveNodeMapIds.get(element);\n        const enterClassName = enterNodeMapIds.get(element);\n        const instruction = this._buildInstruction(entry, subTimelines, enterClassName, leaveClassName, nodeIsOrphaned);\n        if (instruction.errors && instruction.errors.length) {\n          erroneousTransitions.push(instruction);\n          return;\n        }\n        // even though the element may not be in the DOM, it may still\n        // be added at a later point (due to the mechanics of content\n        // projection and/or dynamic component insertion) therefore it's\n        // important to still style the element.\n        if (nodeIsOrphaned) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // if an unmatched transition is queued and ready to go\n        // then it SHOULD NOT render an animation and cancel the\n        // previously running animations.\n        if (entry.isFallbackTransition) {\n          player.onStart(() => eraseStyles(element, instruction.fromStyles));\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          skippedPlayers.push(player);\n          return;\n        }\n        // this means that if a parent animation uses this animation as a sub-trigger\n        // then it will instruct the timeline builder not to add a player delay, but\n        // instead stretch the first keyframe gap until the animation starts. This is\n        // important in order to prevent extra initialization styles from being\n        // required by the user for the animation.\n        const timelines = [];\n        instruction.timelines.forEach(tl => {\n          tl.stretchStartingKeyframe = true;\n          if (!this.disabledNodes.has(tl.element)) {\n            timelines.push(tl);\n          }\n        });\n        instruction.timelines = timelines;\n        subTimelines.append(element, instruction.timelines);\n        const tuple = {\n          instruction,\n          player,\n          element\n        };\n        queuedInstructions.push(tuple);\n        instruction.queriedElements.forEach(element => getOrSetDefaultValue(queriedElements, element, []).push(player));\n        instruction.preStyleProps.forEach((stringMap, element) => {\n          if (stringMap.size) {\n            let setVal = allPreStyleElements.get(element);\n            if (!setVal) {\n              allPreStyleElements.set(element, setVal = new Set());\n            }\n            stringMap.forEach((_, prop) => setVal.add(prop));\n          }\n        });\n        instruction.postStyleProps.forEach((stringMap, element) => {\n          let setVal = allPostStyleElements.get(element);\n          if (!setVal) {\n            allPostStyleElements.set(element, setVal = new Set());\n          }\n          stringMap.forEach((_, prop) => setVal.add(prop));\n        });\n      });\n    }\n    if (erroneousTransitions.length) {\n      const errors = [];\n      erroneousTransitions.forEach(instruction => {\n        errors.push(transitionFailed(instruction.triggerName, instruction.errors));\n      });\n      allPlayers.forEach(player => player.destroy());\n      this.reportError(errors);\n    }\n    const allPreviousPlayersMap = new Map();\n    // this map tells us which element in the DOM tree is contained by\n    // which animation. Further down this map will get populated once\n    // the players are built and in doing so we can use it to efficiently\n    // figure out if a sub player is skipped due to a parent player having priority.\n    const animationElementMap = new Map();\n    queuedInstructions.forEach(entry => {\n      const element = entry.element;\n      if (subTimelines.has(element)) {\n        animationElementMap.set(element, element);\n        this._beforeAnimationBuild(entry.player.namespaceId, entry.instruction, allPreviousPlayersMap);\n      }\n    });\n    skippedPlayers.forEach(player => {\n      const element = player.element;\n      const previousPlayers = this._getPreviousPlayers(element, false, player.namespaceId, player.triggerName, null);\n      previousPlayers.forEach(prevPlayer => {\n        getOrSetDefaultValue(allPreviousPlayersMap, element, []).push(prevPlayer);\n        prevPlayer.destroy();\n      });\n    });\n    // this is a special case for nodes that will be removed either by\n    // having their own leave animations or by being queried in a container\n    // that will be removed once a parent animation is complete. The idea\n    // here is that * styles must be identical to ! styles because of\n    // backwards compatibility (* is also filled in by default in many places).\n    // Otherwise * styles will return an empty value or \"auto\" since the element\n    // passed to getComputedStyle will not be visible (since * === destination)\n    const replaceNodes = allLeaveNodes.filter(node => {\n      return replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements);\n    });\n    // POST STAGE: fill the * styles\n    const postStylesMap = new Map();\n    const allLeaveQueriedNodes = cloakAndComputeStyles(postStylesMap, this.driver, leaveNodesWithoutAnimations, allPostStyleElements, AUTO_STYLE);\n    allLeaveQueriedNodes.forEach(node => {\n      if (replacePostStylesAsPre(node, allPreStyleElements, allPostStyleElements)) {\n        replaceNodes.push(node);\n      }\n    });\n    // PRE STAGE: fill the ! styles\n    const preStylesMap = new Map();\n    enterNodeMap.forEach((nodes, root) => {\n      cloakAndComputeStyles(preStylesMap, this.driver, new Set(nodes), allPreStyleElements, _PRE_STYLE);\n    });\n    replaceNodes.forEach(node => {\n      const post = postStylesMap.get(node);\n      const pre = preStylesMap.get(node);\n      postStylesMap.set(node, new Map([...(post?.entries() ?? []), ...(pre?.entries() ?? [])]));\n    });\n    const rootPlayers = [];\n    const subPlayers = [];\n    const NO_PARENT_ANIMATION_ELEMENT_DETECTED = {};\n    queuedInstructions.forEach(entry => {\n      const {\n        element,\n        player,\n        instruction\n      } = entry;\n      // this means that it was never consumed by a parent animation which\n      // means that it is independent and therefore should be set for animation\n      if (subTimelines.has(element)) {\n        if (disabledElementsSet.has(element)) {\n          player.onDestroy(() => setStyles(element, instruction.toStyles));\n          player.disabled = true;\n          player.overrideTotalTime(instruction.totalTime);\n          skippedPlayers.push(player);\n          return;\n        }\n        // this will flow up the DOM and query the map to figure out\n        // if a parent animation has priority over it. In the situation\n        // that a parent is detected then it will cancel the loop. If\n        // nothing is detected, or it takes a few hops to find a parent,\n        // then it will fill in the missing nodes and signal them as having\n        // a detected parent (or a NO_PARENT value via a special constant).\n        let parentWithAnimation = NO_PARENT_ANIMATION_ELEMENT_DETECTED;\n        if (animationElementMap.size > 1) {\n          let elm = element;\n          const parentsToAdd = [];\n          while (elm = elm.parentNode) {\n            const detectedParent = animationElementMap.get(elm);\n            if (detectedParent) {\n              parentWithAnimation = detectedParent;\n              break;\n            }\n            parentsToAdd.push(elm);\n          }\n          parentsToAdd.forEach(parent => animationElementMap.set(parent, parentWithAnimation));\n        }\n        const innerPlayer = this._buildAnimation(player.namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap);\n        player.setRealPlayer(innerPlayer);\n        if (parentWithAnimation === NO_PARENT_ANIMATION_ELEMENT_DETECTED) {\n          rootPlayers.push(player);\n        } else {\n          const parentPlayers = this.playersByElement.get(parentWithAnimation);\n          if (parentPlayers && parentPlayers.length) {\n            player.parentPlayer = optimizeGroupPlayer(parentPlayers);\n          }\n          skippedPlayers.push(player);\n        }\n      } else {\n        eraseStyles(element, instruction.fromStyles);\n        player.onDestroy(() => setStyles(element, instruction.toStyles));\n        // there still might be a ancestor player animating this\n        // element therefore we will still add it as a sub player\n        // even if its animation may be disabled\n        subPlayers.push(player);\n        if (disabledElementsSet.has(element)) {\n          skippedPlayers.push(player);\n        }\n      }\n    });\n    // find all of the sub players' corresponding inner animation players\n    subPlayers.forEach(player => {\n      // even if no players are found for a sub animation it\n      // will still complete itself after the next tick since it's Noop\n      const playersForElement = skippedPlayersMap.get(player.element);\n      if (playersForElement && playersForElement.length) {\n        const innerPlayer = optimizeGroupPlayer(playersForElement);\n        player.setRealPlayer(innerPlayer);\n      }\n    });\n    // the reason why we don't actually play the animation is\n    // because all that a skipped player is designed to do is to\n    // fire the start/done transition callback events\n    skippedPlayers.forEach(player => {\n      if (player.parentPlayer) {\n        player.syncPlayerEvents(player.parentPlayer);\n      } else {\n        player.destroy();\n      }\n    });\n    // run through all of the queued removals and see if they\n    // were picked up by a query. If not then perform the removal\n    // operation right away unless a parent animation is ongoing.\n    for (let i = 0; i < allLeaveNodes.length; i++) {\n      const element = allLeaveNodes[i];\n      const details = element[REMOVAL_FLAG];\n      removeClass(element, LEAVE_CLASSNAME);\n      // this means the element has a removal animation that is being\n      // taken care of and therefore the inner elements will hang around\n      // until that animation is over (or the parent queried animation)\n      if (details && details.hasAnimation) continue;\n      let players = [];\n      // if this element is queried or if it contains queried children\n      // then we want for the element not to be removed from the page\n      // until the queried animations have finished\n      if (queriedElements.size) {\n        let queriedPlayerResults = queriedElements.get(element);\n        if (queriedPlayerResults && queriedPlayerResults.length) {\n          players.push(...queriedPlayerResults);\n        }\n        let queriedInnerElements = this.driver.query(element, NG_ANIMATING_SELECTOR, true);\n        for (let j = 0; j < queriedInnerElements.length; j++) {\n          let queriedPlayers = queriedElements.get(queriedInnerElements[j]);\n          if (queriedPlayers && queriedPlayers.length) {\n            players.push(...queriedPlayers);\n          }\n        }\n      }\n      const activePlayers = players.filter(p => !p.destroyed);\n      if (activePlayers.length) {\n        removeNodesAfterAnimationDone(this, element, activePlayers);\n      } else {\n        this.processLeaveNode(element);\n      }\n    }\n    // this is required so the cleanup method doesn't remove them\n    allLeaveNodes.length = 0;\n    rootPlayers.forEach(player => {\n      this.players.push(player);\n      player.onDone(() => {\n        player.destroy();\n        const index = this.players.indexOf(player);\n        this.players.splice(index, 1);\n      });\n      player.play();\n    });\n    return rootPlayers;\n  }\n  afterFlush(callback) {\n    this._flushFns.push(callback);\n  }\n  afterFlushAnimationsDone(callback) {\n    this._whenQuietFns.push(callback);\n  }\n  _getPreviousPlayers(element, isQueriedElement, namespaceId, triggerName, toStateValue) {\n    let players = [];\n    if (isQueriedElement) {\n      const queriedElementPlayers = this.playersByQueriedElement.get(element);\n      if (queriedElementPlayers) {\n        players = queriedElementPlayers;\n      }\n    } else {\n      const elementPlayers = this.playersByElement.get(element);\n      if (elementPlayers) {\n        const isRemovalAnimation = !toStateValue || toStateValue == VOID_VALUE;\n        elementPlayers.forEach(player => {\n          if (player.queued) return;\n          if (!isRemovalAnimation && player.triggerName != triggerName) return;\n          players.push(player);\n        });\n      }\n    }\n    if (namespaceId || triggerName) {\n      players = players.filter(player => {\n        if (namespaceId && namespaceId != player.namespaceId) return false;\n        if (triggerName && triggerName != player.triggerName) return false;\n        return true;\n      });\n    }\n    return players;\n  }\n  _beforeAnimationBuild(namespaceId, instruction, allPreviousPlayersMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // when a removal animation occurs, ALL previous players are collected\n    // and destroyed (even if they are outside of the current namespace)\n    const targetNameSpaceId = instruction.isRemovalTransition ? undefined : namespaceId;\n    const targetTriggerName = instruction.isRemovalTransition ? undefined : triggerName;\n    for (const timelineInstruction of instruction.timelines) {\n      const element = timelineInstruction.element;\n      const isQueriedElement = element !== rootElement;\n      const players = getOrSetDefaultValue(allPreviousPlayersMap, element, []);\n      const previousPlayers = this._getPreviousPlayers(element, isQueriedElement, targetNameSpaceId, targetTriggerName, instruction.toState);\n      previousPlayers.forEach(player => {\n        const realPlayer = player.getRealPlayer();\n        if (realPlayer.beforeDestroy) {\n          realPlayer.beforeDestroy();\n        }\n        player.destroy();\n        players.push(player);\n      });\n    }\n    // this needs to be done so that the PRE/POST styles can be\n    // computed properly without interfering with the previous animation\n    eraseStyles(rootElement, instruction.fromStyles);\n  }\n  _buildAnimation(namespaceId, instruction, allPreviousPlayersMap, skippedPlayersMap, preStylesMap, postStylesMap) {\n    const triggerName = instruction.triggerName;\n    const rootElement = instruction.element;\n    // we first run this so that the previous animation player\n    // data can be passed into the successive animation players\n    const allQueriedPlayers = [];\n    const allConsumedElements = new Set();\n    const allSubElements = new Set();\n    const allNewPlayers = instruction.timelines.map(timelineInstruction => {\n      const element = timelineInstruction.element;\n      allConsumedElements.add(element);\n      // FIXME (matsko): make sure to-be-removed animations are removed properly\n      const details = element[REMOVAL_FLAG];\n      if (details && details.removedBeforeQueried) return new NoopAnimationPlayer(timelineInstruction.duration, timelineInstruction.delay);\n      const isQueriedElement = element !== rootElement;\n      const previousPlayers = flattenGroupPlayers((allPreviousPlayersMap.get(element) || EMPTY_PLAYER_ARRAY).map(p => p.getRealPlayer())).filter(p => {\n        // the `element` is not apart of the AnimationPlayer definition, but\n        // Mock/WebAnimations\n        // use the element within their implementation. This will be added in Angular5 to\n        // AnimationPlayer\n        const pp = p;\n        return pp.element ? pp.element === element : false;\n      });\n      const preStyles = preStylesMap.get(element);\n      const postStyles = postStylesMap.get(element);\n      const keyframes = normalizeKeyframes(this._normalizer, timelineInstruction.keyframes, preStyles, postStyles);\n      const player = this._buildPlayer(timelineInstruction, keyframes, previousPlayers);\n      // this means that this particular player belongs to a sub trigger. It is\n      // important that we match this player up with the corresponding (@trigger.listener)\n      if (timelineInstruction.subTimeline && skippedPlayersMap) {\n        allSubElements.add(element);\n      }\n      if (isQueriedElement) {\n        const wrappedPlayer = new TransitionAnimationPlayer(namespaceId, triggerName, element);\n        wrappedPlayer.setRealPlayer(player);\n        allQueriedPlayers.push(wrappedPlayer);\n      }\n      return player;\n    });\n    allQueriedPlayers.forEach(player => {\n      getOrSetDefaultValue(this.playersByQueriedElement, player.element, []).push(player);\n      player.onDone(() => deleteOrUnsetInMap(this.playersByQueriedElement, player.element, player));\n    });\n    allConsumedElements.forEach(element => addClass(element, NG_ANIMATING_CLASSNAME));\n    const player = optimizeGroupPlayer(allNewPlayers);\n    player.onDestroy(() => {\n      allConsumedElements.forEach(element => removeClass(element, NG_ANIMATING_CLASSNAME));\n      setStyles(rootElement, instruction.toStyles);\n    });\n    // this basically makes all of the callbacks for sub element animations\n    // be dependent on the upper players for when they finish\n    allSubElements.forEach(element => {\n      getOrSetDefaultValue(skippedPlayersMap, element, []).push(player);\n    });\n    return player;\n  }\n  _buildPlayer(instruction, keyframes, previousPlayers) {\n    if (keyframes.length > 0) {\n      return this.driver.animate(instruction.element, keyframes, instruction.duration, instruction.delay, instruction.easing, previousPlayers);\n    }\n    // special case for when an empty transition|definition is provided\n    // ... there is no point in rendering an empty animation\n    return new NoopAnimationPlayer(instruction.duration, instruction.delay);\n  }\n}\nclass TransitionAnimationPlayer {\n  namespaceId;\n  triggerName;\n  element;\n  _player = new NoopAnimationPlayer();\n  _containsRealPlayer = false;\n  _queuedCallbacks = new Map();\n  destroyed = false;\n  parentPlayer = null;\n  markedForDestroy = false;\n  disabled = false;\n  queued = true;\n  totalTime = 0;\n  constructor(namespaceId, triggerName, element) {\n    this.namespaceId = namespaceId;\n    this.triggerName = triggerName;\n    this.element = element;\n  }\n  setRealPlayer(player) {\n    if (this._containsRealPlayer) return;\n    this._player = player;\n    this._queuedCallbacks.forEach((callbacks, phase) => {\n      callbacks.forEach(callback => listenOnPlayer(player, phase, undefined, callback));\n    });\n    this._queuedCallbacks.clear();\n    this._containsRealPlayer = true;\n    this.overrideTotalTime(player.totalTime);\n    this.queued = false;\n  }\n  getRealPlayer() {\n    return this._player;\n  }\n  overrideTotalTime(totalTime) {\n    this.totalTime = totalTime;\n  }\n  syncPlayerEvents(player) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      player.onStart(() => p.triggerCallback('start'));\n    }\n    player.onDone(() => this.finish());\n    player.onDestroy(() => this.destroy());\n  }\n  _queueEvent(name, callback) {\n    getOrSetDefaultValue(this._queuedCallbacks, name, []).push(callback);\n  }\n  onDone(fn) {\n    if (this.queued) {\n      this._queueEvent('done', fn);\n    }\n    this._player.onDone(fn);\n  }\n  onStart(fn) {\n    if (this.queued) {\n      this._queueEvent('start', fn);\n    }\n    this._player.onStart(fn);\n  }\n  onDestroy(fn) {\n    if (this.queued) {\n      this._queueEvent('destroy', fn);\n    }\n    this._player.onDestroy(fn);\n  }\n  init() {\n    this._player.init();\n  }\n  hasStarted() {\n    return this.queued ? false : this._player.hasStarted();\n  }\n  play() {\n    !this.queued && this._player.play();\n  }\n  pause() {\n    !this.queued && this._player.pause();\n  }\n  restart() {\n    !this.queued && this._player.restart();\n  }\n  finish() {\n    this._player.finish();\n  }\n  destroy() {\n    this.destroyed = true;\n    this._player.destroy();\n  }\n  reset() {\n    !this.queued && this._player.reset();\n  }\n  setPosition(p) {\n    if (!this.queued) {\n      this._player.setPosition(p);\n    }\n  }\n  getPosition() {\n    return this.queued ? 0 : this._player.getPosition();\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const p = this._player;\n    if (p.triggerCallback) {\n      p.triggerCallback(phaseName);\n    }\n  }\n}\nfunction deleteOrUnsetInMap(map, key, value) {\n  let currentValues = map.get(key);\n  if (currentValues) {\n    if (currentValues.length) {\n      const index = currentValues.indexOf(value);\n      currentValues.splice(index, 1);\n    }\n    if (currentValues.length == 0) {\n      map.delete(key);\n    }\n  }\n  return currentValues;\n}\nfunction normalizeTriggerValue(value) {\n  // we use `!= null` here because it's the most simple\n  // way to test against a \"falsy\" value without mixing\n  // in empty strings or a zero value. DO NOT OPTIMIZE.\n  return value != null ? value : null;\n}\nfunction isElementNode(node) {\n  return node && node['nodeType'] === 1;\n}\nfunction isTriggerEventValid(eventName) {\n  return eventName == 'start' || eventName == 'done';\n}\nfunction cloakElement(element, value) {\n  const oldValue = element.style.display;\n  element.style.display = value != null ? value : 'none';\n  return oldValue;\n}\nfunction cloakAndComputeStyles(valuesMap, driver, elements, elementPropsMap, defaultStyle) {\n  const cloakVals = [];\n  elements.forEach(element => cloakVals.push(cloakElement(element)));\n  const failedElements = [];\n  elementPropsMap.forEach((props, element) => {\n    const styles = new Map();\n    props.forEach(prop => {\n      const value = driver.computeStyle(element, prop, defaultStyle);\n      styles.set(prop, value);\n      // there is no easy way to detect this because a sub element could be removed\n      // by a parent animation element being detached.\n      if (!value || value.length == 0) {\n        element[REMOVAL_FLAG] = NULL_REMOVED_QUERIED_STATE;\n        failedElements.push(element);\n      }\n    });\n    valuesMap.set(element, styles);\n  });\n  // we use a index variable here since Set.forEach(a, i) does not return\n  // an index value for the closure (but instead just the value)\n  let i = 0;\n  elements.forEach(element => cloakElement(element, cloakVals[i++]));\n  return failedElements;\n}\n/*\nSince the Angular renderer code will return a collection of inserted\nnodes in all areas of a DOM tree, it's up to this algorithm to figure\nout which nodes are roots for each animation @trigger.\n\nBy placing each inserted node into a Set and traversing upwards, it\nis possible to find the @trigger elements and well any direct *star\ninsertion nodes, if a @trigger root is found then the enter element\nis placed into the Map[@trigger] spot.\n */\nfunction buildRootMap(roots, nodes) {\n  const rootMap = new Map();\n  roots.forEach(root => rootMap.set(root, []));\n  if (nodes.length == 0) return rootMap;\n  const NULL_NODE = 1;\n  const nodeSet = new Set(nodes);\n  const localRootMap = new Map();\n  function getRoot(node) {\n    if (!node) return NULL_NODE;\n    let root = localRootMap.get(node);\n    if (root) return root;\n    const parent = node.parentNode;\n    if (rootMap.has(parent)) {\n      // ngIf inside @trigger\n      root = parent;\n    } else if (nodeSet.has(parent)) {\n      // ngIf inside ngIf\n      root = NULL_NODE;\n    } else {\n      // recurse upwards\n      root = getRoot(parent);\n    }\n    localRootMap.set(node, root);\n    return root;\n  }\n  nodes.forEach(node => {\n    const root = getRoot(node);\n    if (root !== NULL_NODE) {\n      rootMap.get(root).push(node);\n    }\n  });\n  return rootMap;\n}\nfunction addClass(element, className) {\n  element.classList?.add(className);\n}\nfunction removeClass(element, className) {\n  element.classList?.remove(className);\n}\nfunction removeNodesAfterAnimationDone(engine, element, players) {\n  optimizeGroupPlayer(players).onDone(() => engine.processLeaveNode(element));\n}\nfunction flattenGroupPlayers(players) {\n  const finalPlayers = [];\n  _flattenGroupPlayersRecur(players, finalPlayers);\n  return finalPlayers;\n}\nfunction _flattenGroupPlayersRecur(players, finalPlayers) {\n  for (let i = 0; i < players.length; i++) {\n    const player = players[i];\n    if (player instanceof AnimationGroupPlayer) {\n      _flattenGroupPlayersRecur(player.players, finalPlayers);\n    } else {\n      finalPlayers.push(player);\n    }\n  }\n}\nfunction objEquals(a, b) {\n  const k1 = Object.keys(a);\n  const k2 = Object.keys(b);\n  if (k1.length != k2.length) return false;\n  for (let i = 0; i < k1.length; i++) {\n    const prop = k1[i];\n    if (!b.hasOwnProperty(prop) || a[prop] !== b[prop]) return false;\n  }\n  return true;\n}\nfunction replacePostStylesAsPre(element, allPreStyleElements, allPostStyleElements) {\n  const postEntry = allPostStyleElements.get(element);\n  if (!postEntry) return false;\n  let preEntry = allPreStyleElements.get(element);\n  if (preEntry) {\n    postEntry.forEach(data => preEntry.add(data));\n  } else {\n    allPreStyleElements.set(element, postEntry);\n  }\n  allPostStyleElements.delete(element);\n  return true;\n}\nclass AnimationEngine {\n  _driver;\n  _normalizer;\n  _transitionEngine;\n  _timelineEngine;\n  _triggerCache = {};\n  // this method is designed to be overridden by the code that uses this engine\n  onRemovalComplete = (element, context) => {};\n  constructor(doc, _driver, _normalizer) {\n    this._driver = _driver;\n    this._normalizer = _normalizer;\n    this._transitionEngine = new TransitionAnimationEngine(doc.body, _driver, _normalizer);\n    this._timelineEngine = new TimelineAnimationEngine(doc.body, _driver, _normalizer);\n    this._transitionEngine.onRemovalComplete = (element, context) => this.onRemovalComplete(element, context);\n  }\n  registerTrigger(componentId, namespaceId, hostElement, name, metadata) {\n    const cacheKey = componentId + '-' + name;\n    let trigger = this._triggerCache[cacheKey];\n    if (!trigger) {\n      const errors = [];\n      const warnings = [];\n      const ast = buildAnimationAst(this._driver, metadata, errors, warnings);\n      if (errors.length) {\n        throw triggerBuildFailed(name, errors);\n      }\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (warnings.length) {\n          warnTriggerBuild(name, warnings);\n        }\n      }\n      trigger = buildTrigger(name, ast, this._normalizer);\n      this._triggerCache[cacheKey] = trigger;\n    }\n    this._transitionEngine.registerTrigger(namespaceId, name, trigger);\n  }\n  register(namespaceId, hostElement) {\n    this._transitionEngine.register(namespaceId, hostElement);\n  }\n  destroy(namespaceId, context) {\n    this._transitionEngine.destroy(namespaceId, context);\n  }\n  onInsert(namespaceId, element, parent, insertBefore) {\n    this._transitionEngine.insertNode(namespaceId, element, parent, insertBefore);\n  }\n  onRemove(namespaceId, element, context) {\n    this._transitionEngine.removeNode(namespaceId, element, context);\n  }\n  disableAnimations(element, disable) {\n    this._transitionEngine.markElementAsDisabled(element, disable);\n  }\n  process(namespaceId, element, property, value) {\n    if (property.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(property);\n      const args = value;\n      this._timelineEngine.command(id, element, action, args);\n    } else {\n      this._transitionEngine.trigger(namespaceId, element, property, value);\n    }\n  }\n  listen(namespaceId, element, eventName, eventPhase, callback) {\n    // @@listen\n    if (eventName.charAt(0) == '@') {\n      const [id, action] = parseTimelineCommand(eventName);\n      return this._timelineEngine.listen(id, element, action, callback);\n    }\n    return this._transitionEngine.listen(namespaceId, element, eventName, eventPhase, callback);\n  }\n  flush(microtaskId = -1) {\n    this._transitionEngine.flush(microtaskId);\n  }\n  get players() {\n    return [...this._transitionEngine.players, ...this._timelineEngine.players];\n  }\n  whenRenderingDone() {\n    return this._transitionEngine.whenRenderingDone();\n  }\n  afterFlushAnimationsDone(cb) {\n    this._transitionEngine.afterFlushAnimationsDone(cb);\n  }\n}\n\n/**\n * Returns an instance of `SpecialCasedStyles` if and when any special (non animateable) styles are\n * detected.\n *\n * In CSS there exist properties that cannot be animated within a keyframe animation\n * (whether it be via CSS keyframes or web-animations) and the animation implementation\n * will ignore them. This function is designed to detect those special cased styles and\n * return a container that will be executed at the start and end of the animation.\n *\n * @returns an instance of `SpecialCasedStyles` if any special styles are detected otherwise `null`\n */\nfunction packageNonAnimatableStyles(element, styles) {\n  let startStyles = null;\n  let endStyles = null;\n  if (Array.isArray(styles) && styles.length) {\n    startStyles = filterNonAnimatableStyles(styles[0]);\n    if (styles.length > 1) {\n      endStyles = filterNonAnimatableStyles(styles[styles.length - 1]);\n    }\n  } else if (styles instanceof Map) {\n    startStyles = filterNonAnimatableStyles(styles);\n  }\n  return startStyles || endStyles ? new SpecialCasedStyles(element, startStyles, endStyles) : null;\n}\n/**\n * Designed to be executed during a keyframe-based animation to apply any special-cased styles.\n *\n * When started (when the `start()` method is run) then the provided `startStyles`\n * will be applied. When finished (when the `finish()` method is called) the\n * `endStyles` will be applied as well any any starting styles. Finally when\n * `destroy()` is called then all styles will be removed.\n */\nclass SpecialCasedStyles {\n  _element;\n  _startStyles;\n  _endStyles;\n  static initialStylesByElement = /* @__PURE__ */new WeakMap();\n  _state = 0 /* SpecialCasedStylesState.Pending */;\n  _initialStyles;\n  constructor(_element, _startStyles, _endStyles) {\n    this._element = _element;\n    this._startStyles = _startStyles;\n    this._endStyles = _endStyles;\n    let initialStyles = SpecialCasedStyles.initialStylesByElement.get(_element);\n    if (!initialStyles) {\n      SpecialCasedStyles.initialStylesByElement.set(_element, initialStyles = new Map());\n    }\n    this._initialStyles = initialStyles;\n  }\n  start() {\n    if (this._state < 1 /* SpecialCasedStylesState.Started */) {\n      if (this._startStyles) {\n        setStyles(this._element, this._startStyles, this._initialStyles);\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  finish() {\n    this.start();\n    if (this._state < 2 /* SpecialCasedStylesState.Finished */) {\n      setStyles(this._element, this._initialStyles);\n      if (this._endStyles) {\n        setStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      this._state = 1 /* SpecialCasedStylesState.Started */;\n    }\n  }\n  destroy() {\n    this.finish();\n    if (this._state < 3 /* SpecialCasedStylesState.Destroyed */) {\n      SpecialCasedStyles.initialStylesByElement.delete(this._element);\n      if (this._startStyles) {\n        eraseStyles(this._element, this._startStyles);\n        this._endStyles = null;\n      }\n      if (this._endStyles) {\n        eraseStyles(this._element, this._endStyles);\n        this._endStyles = null;\n      }\n      setStyles(this._element, this._initialStyles);\n      this._state = 3 /* SpecialCasedStylesState.Destroyed */;\n    }\n  }\n}\nfunction filterNonAnimatableStyles(styles) {\n  let result = null;\n  styles.forEach((val, prop) => {\n    if (isNonAnimatableStyle(prop)) {\n      result = result || new Map();\n      result.set(prop, val);\n    }\n  });\n  return result;\n}\nfunction isNonAnimatableStyle(prop) {\n  return prop === 'display' || prop === 'position';\n}\nclass WebAnimationsPlayer {\n  element;\n  keyframes;\n  options;\n  _specialStyles;\n  _onDoneFns = [];\n  _onStartFns = [];\n  _onDestroyFns = [];\n  _duration;\n  _delay;\n  _initialized = false;\n  _finished = false;\n  _started = false;\n  _destroyed = false;\n  _finalKeyframe;\n  // the following original fns are persistent copies of the _onStartFns and _onDoneFns\n  // and are used to reset the fns to their original values upon reset()\n  // (since the _onStartFns and _onDoneFns get deleted after they are called)\n  _originalOnDoneFns = [];\n  _originalOnStartFns = [];\n  // using non-null assertion because it's re(set) by init();\n  domPlayer;\n  time = 0;\n  parentPlayer = null;\n  currentSnapshot = new Map();\n  constructor(element, keyframes, options, _specialStyles) {\n    this.element = element;\n    this.keyframes = keyframes;\n    this.options = options;\n    this._specialStyles = _specialStyles;\n    this._duration = options['duration'];\n    this._delay = options['delay'] || 0;\n    this.time = this._duration + this._delay;\n  }\n  _onFinish() {\n    if (!this._finished) {\n      this._finished = true;\n      this._onDoneFns.forEach(fn => fn());\n      this._onDoneFns = [];\n    }\n  }\n  init() {\n    this._buildPlayer();\n    this._preparePlayerBeforeStart();\n  }\n  _buildPlayer() {\n    if (this._initialized) return;\n    this._initialized = true;\n    const keyframes = this.keyframes;\n    // @ts-expect-error overwriting a readonly property\n    this.domPlayer = this._triggerWebAnimation(this.element, keyframes, this.options);\n    this._finalKeyframe = keyframes.length ? keyframes[keyframes.length - 1] : new Map();\n    const onFinish = () => this._onFinish();\n    this.domPlayer.addEventListener('finish', onFinish);\n    this.onDestroy(() => {\n      // We must remove the `finish` event listener once an animation has completed all its\n      // iterations. This action is necessary to prevent a memory leak since the listener captures\n      // `this`, creating a closure that prevents `this` from being garbage collected.\n      this.domPlayer.removeEventListener('finish', onFinish);\n    });\n  }\n  _preparePlayerBeforeStart() {\n    // this is required so that the player doesn't start to animate right away\n    if (this._delay) {\n      this._resetDomPlayerState();\n    } else {\n      this.domPlayer.pause();\n    }\n  }\n  _convertKeyframesToObject(keyframes) {\n    const kfs = [];\n    keyframes.forEach(frame => {\n      kfs.push(Object.fromEntries(frame));\n    });\n    return kfs;\n  }\n  /** @internal */\n  _triggerWebAnimation(element, keyframes, options) {\n    return element.animate(this._convertKeyframesToObject(keyframes), options);\n  }\n  onStart(fn) {\n    this._originalOnStartFns.push(fn);\n    this._onStartFns.push(fn);\n  }\n  onDone(fn) {\n    this._originalOnDoneFns.push(fn);\n    this._onDoneFns.push(fn);\n  }\n  onDestroy(fn) {\n    this._onDestroyFns.push(fn);\n  }\n  play() {\n    this._buildPlayer();\n    if (!this.hasStarted()) {\n      this._onStartFns.forEach(fn => fn());\n      this._onStartFns = [];\n      this._started = true;\n      if (this._specialStyles) {\n        this._specialStyles.start();\n      }\n    }\n    this.domPlayer.play();\n  }\n  pause() {\n    this.init();\n    this.domPlayer.pause();\n  }\n  finish() {\n    this.init();\n    if (this._specialStyles) {\n      this._specialStyles.finish();\n    }\n    this._onFinish();\n    this.domPlayer.finish();\n  }\n  reset() {\n    this._resetDomPlayerState();\n    this._destroyed = false;\n    this._finished = false;\n    this._started = false;\n    this._onStartFns = this._originalOnStartFns;\n    this._onDoneFns = this._originalOnDoneFns;\n  }\n  _resetDomPlayerState() {\n    if (this.domPlayer) {\n      this.domPlayer.cancel();\n    }\n  }\n  restart() {\n    this.reset();\n    this.play();\n  }\n  hasStarted() {\n    return this._started;\n  }\n  destroy() {\n    if (!this._destroyed) {\n      this._destroyed = true;\n      this._resetDomPlayerState();\n      this._onFinish();\n      if (this._specialStyles) {\n        this._specialStyles.destroy();\n      }\n      this._onDestroyFns.forEach(fn => fn());\n      this._onDestroyFns = [];\n    }\n  }\n  setPosition(p) {\n    if (this.domPlayer === undefined) {\n      this.init();\n    }\n    this.domPlayer.currentTime = p * this.time;\n  }\n  getPosition() {\n    // tsc is complaining with TS2362 without the conversion to number\n    return +(this.domPlayer.currentTime ?? 0) / this.time;\n  }\n  get totalTime() {\n    return this._delay + this._duration;\n  }\n  beforeDestroy() {\n    const styles = new Map();\n    if (this.hasStarted()) {\n      // note: this code is invoked only when the `play` function was called prior to this\n      // (thus `hasStarted` returns true), this implies that the code that initializes\n      // `_finalKeyframe` has also been executed and the non-null assertion can be safely used here\n      const finalKeyframe = this._finalKeyframe;\n      finalKeyframe.forEach((val, prop) => {\n        if (prop !== 'offset') {\n          styles.set(prop, this._finished ? val : computeStyle(this.element, prop));\n        }\n      });\n    }\n    this.currentSnapshot = styles;\n  }\n  /** @internal */\n  triggerCallback(phaseName) {\n    const methods = phaseName === 'start' ? this._onStartFns : this._onDoneFns;\n    methods.forEach(fn => fn());\n    methods.length = 0;\n  }\n}\nclass WebAnimationsDriver {\n  validateStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      return validateStyleProperty(prop);\n    }\n    return true;\n  }\n  validateAnimatableStyleProperty(prop) {\n    // Perform actual validation in dev mode only, in prod mode this check is a noop.\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const cssProp = camelCaseToDashCase(prop);\n      return validateWebAnimatableStyleProperty(cssProp);\n    }\n    return true;\n  }\n  containsElement(elm1, elm2) {\n    return containsElement(elm1, elm2);\n  }\n  getParentElement(element) {\n    return getParentElement(element);\n  }\n  query(element, selector, multi) {\n    return invokeQuery(element, selector, multi);\n  }\n  computeStyle(element, prop, defaultValue) {\n    return computeStyle(element, prop);\n  }\n  animate(element, keyframes, duration, delay, easing, previousPlayers = []) {\n    const fill = delay == 0 ? 'both' : 'forwards';\n    const playerOptions = {\n      duration,\n      delay,\n      fill\n    };\n    // we check for this to avoid having a null|undefined value be present\n    // for the easing (which results in an error for certain browsers #9752)\n    if (easing) {\n      playerOptions['easing'] = easing;\n    }\n    const previousStyles = new Map();\n    const previousWebAnimationPlayers = previousPlayers.filter(player => player instanceof WebAnimationsPlayer);\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousWebAnimationPlayers.forEach(player => {\n        player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));\n      });\n    }\n    let _keyframes = normalizeKeyframes$1(keyframes).map(styles => new Map(styles));\n    _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);\n    const specialStyles = packageNonAnimatableStyles(element, _keyframes);\n    return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);\n  }\n}\nfunction createEngine(type, doc) {\n  // TODO: find a way to make this tree shakable.\n  if (type === 'noop') {\n    return new AnimationEngine(doc, new NoopAnimationDriver(), new NoopAnimationStyleNormalizer());\n  }\n  return new AnimationEngine(doc, new WebAnimationsDriver(), new WebAnimationsStyleNormalizer());\n}\nclass Animation {\n  _driver;\n  _animationAst;\n  constructor(_driver, input) {\n    this._driver = _driver;\n    const errors = [];\n    const warnings = [];\n    const ast = buildAnimationAst(_driver, input, errors, warnings);\n    if (errors.length) {\n      throw validationFailed(errors);\n    }\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (warnings.length) {\n        warnValidation(warnings);\n      }\n    }\n    this._animationAst = ast;\n  }\n  buildTimelines(element, startingStyles, destinationStyles, options, subInstructions) {\n    const start = Array.isArray(startingStyles) ? normalizeStyles(startingStyles) : startingStyles;\n    const dest = Array.isArray(destinationStyles) ? normalizeStyles(destinationStyles) : destinationStyles;\n    const errors = [];\n    subInstructions = subInstructions || new ElementInstructionMap();\n    const result = buildAnimationTimelines(this._driver, element, this._animationAst, ENTER_CLASSNAME, LEAVE_CLASSNAME, start, dest, options, subInstructions, errors);\n    if (errors.length) {\n      throw buildingFailed(errors);\n    }\n    return result;\n  }\n}\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass BaseAnimationRenderer {\n  namespaceId;\n  delegate;\n  engine;\n  _onDestroy;\n  // We need to explicitly type this property because of an api-extractor bug\n  // See https://github.com/microsoft/rushstack/issues/4390\n  ɵtype = 0 /* AnimationRendererType.Regular */;\n  constructor(namespaceId, delegate, engine, _onDestroy) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this._onDestroy = _onDestroy;\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroyNode(node) {\n    this.delegate.destroyNode?.(node);\n  }\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.engine.afterFlushAnimationsDone(() => {\n      // Call the renderer destroy method after the animations has finished as otherwise\n      // styles will be removed too early which will cause an unstyled animation.\n      queueMicrotask(() => {\n        this.delegate.destroy();\n      });\n    });\n    this._onDestroy?.();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild);\n    // If `isMove` true than we should animate this insert.\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    // Prior to the changes in #57203, this method wasn't being called at all by `core` if the child\n    // doesn't have a parent. There appears to be some animation-specific downstream logic that\n    // depends on the null check happening before the animation engine. This check keeps the old\n    // behavior while allowing `core` to not have to check for the parent element anymore.\n    if (this.parentNode(oldChild)) {\n      this.engine.onRemove(this.namespaceId, oldChild, this.delegate);\n    }\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback, options) {\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n  factory;\n  constructor(factory, namespaceId, delegate, engine, onDestroy) {\n    super(namespaceId, delegate, engine, onDestroy);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.slice(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n  listen(target, eventName, callback, options) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.slice(1);\n      let phase = '';\n      // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n}\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n    case 'document':\n      return document;\n    case 'window':\n      return window;\n    default:\n      return target;\n  }\n}\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.slice(dotIndex + 1);\n  return [trigger, phase];\n}\nclass AnimationRendererFactory {\n  delegate;\n  engine;\n  _zone;\n  _currentId = 0;\n  _microtaskId = 1;\n  _animationCallbacksBuffer = [];\n  _rendererCache = new Map();\n  _cdRecurDepth = 0;\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    engine.onRemovalComplete = (element, delegate) => {\n      delegate?.removeChild(null, element);\n    };\n  }\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = '';\n    // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n    const delegate = this.delegate.createRenderer(hostElement, type);\n    if (!hostElement || !type?.data?.['animation']) {\n      const cache = this._rendererCache;\n      let renderer = cache.get(delegate);\n      if (!renderer) {\n        // Ensure that the renderer is removed from the cache on destroy\n        // since it may contain references to detached DOM nodes.\n        const onRendererDestroy = () => cache.delete(delegate);\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);\n        // only cache this result when the base renderer is used\n        cache.set(delegate, renderer);\n      }\n      return renderer;\n    }\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n  begin() {\n    this._cdRecurDepth++;\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n  _scheduleCountTask() {\n    queueMicrotask(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n      return;\n    }\n    const animationCallbacksBuffer = this._animationCallbacksBuffer;\n    if (animationCallbacksBuffer.length == 0) {\n      queueMicrotask(() => {\n        this._zone.run(() => {\n          animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n    animationCallbacksBuffer.push([fn, data]);\n  }\n  end() {\n    this._cdRecurDepth--;\n    // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n        this.engine.flush(this._microtaskId);\n      });\n    }\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this.engine.flush();\n    this.delegate.componentReplaced?.(componentId);\n  }\n}\nexport { AnimationDriver, NoopAnimationDriver, Animation as ɵAnimation, AnimationEngine as ɵAnimationEngine, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, AnimationStyleNormalizer as ɵAnimationStyleNormalizer, BaseAnimationRenderer as ɵBaseAnimationRenderer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, WebAnimationsDriver as ɵWebAnimationsDriver, WebAnimationsPlayer as ɵWebAnimationsPlayer, WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer, allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, containsElement as ɵcontainsElement, createEngine as ɵcreateEngine, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, normalizeKeyframes$1 as ɵnormalizeKeyframes, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty };\n", "/**\n * @license Angular v19.2.9\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ANIMATION_MODULE_TYPE, NgZone, RendererFactory2, Inject, Injectable, ɵperformanceMarkFeature as _performanceMarkFeature, NgModule } from '@angular/core';\nexport { ANIMATION_MODULE_TYPE } from '@angular/core';\nimport * as i1 from '@angular/animations/browser';\nimport { NoopAnimationDriver, AnimationDriver, ɵAnimationStyleNormalizer as _AnimationStyleNormalizer, ɵAnimationEngine as _AnimationEngine, ɵWebAnimationsDriver as _WebAnimationsDriver, ɵWebAnimationsStyleNormalizer as _WebAnimationsStyleNormalizer, ɵAnimationRendererFactory as _AnimationRendererFactory } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\nimport { DomRendererFactory2 } from './dom_renderer-DGKzginR.mjs';\nimport { BrowserModule } from './browser-X3l5Bmdq.mjs';\nclass InjectableAnimationEngine extends _AnimationEngine {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(doc, driver, normalizer) {\n    super(doc, driver, normalizer);\n  }\n  ngOnDestroy() {\n    this.flush();\n  }\n  static ɵfac = function InjectableAnimationEngine_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InjectableAnimationEngine,\n    factory: InjectableAnimationEngine.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.AnimationDriver\n  }, {\n    type: i1.ɵAnimationStyleNormalizer\n  }], null);\n})();\nfunction instantiateDefaultStyleNormalizer() {\n  return new _WebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new _AnimationRendererFactory(renderer, engine, zone);\n}\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: _AnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: _AnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [DomRendererFactory2, _AnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: NoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n// Note: the `ngServerMode` happen inside factories to give the variable time to initialize.\n{\n  provide: AnimationDriver,\n  useFactory: () => typeof ngServerMode !== 'undefined' && ngServerMode ? new NoopAnimationDriver() : new _WebAnimationsDriver()\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useFactory: () => typeof ngServerMode !== 'undefined' && ngServerMode ? 'NoopAnimations' : 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n\n/**\n * Exports `BrowserModule` with additional dependency-injection providers\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see {@link BrowserAnimationsModuleConfig}\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```ts\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n  static ɵfac = function BrowserAnimationsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserAnimationsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserAnimationsModule,\n    exports: [BrowserModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideAnimations() {\n  _performanceMarkFeature('NgEagerAnimations');\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n  static ɵfac = function NoopAnimationsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NoopAnimationsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: NoopAnimationsModule,\n    exports: [BrowserModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n    imports: [BrowserModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * Returns the set of dependency-injection providers\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nfunction provideNoopAnimations() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\nexport { BrowserAnimationsModule, NoopAnimationsModule, provideAnimations, provideNoopAnimations, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,aAAa;AACnB,SAAS,mBAAmB,KAAK;AAC/B,SAAO,IAAI,aAAc,KAAkD,aAAa,8BAA8B,GAAG,eAAe;AAC1I;AACA,SAAS,oBAAoB;AAC3B,SAAO,IAAI,aAAc,MAAiD,aAAa,kEAAkE;AAC3J;AACA,SAAS,qBAAqB;AAC5B,SAAO,IAAI,aAAc,MAAkD,aAAa,+DAA+D;AACzJ;AACA,SAAS,mBAAmB,SAAS;AACnC,SAAO,IAAI,aAAc,MAAkD,aAAa,+CAA+C,OAAO,8BAA8B;AAC9K;AACA,SAAS,kBAAkB,SAAS;AAClC,SAAO,IAAI,aAAc,MAAiD,aAAa,kDAAkD,OAAO,EAAE;AACpJ;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,IAAI,aAAc,MAA+C,aAAa,8CAA8C,QAAQ,EAAE;AAC/I;AACA,SAAS,oBAAoB,sBAAsB,OAAO;AACxD,SAAO,IAAI,aAAc,MAAoD,aAAa,uCAAuC,oBAAoB,IAAI,KAAK,EAAE;AAClK;AACA,SAAS,iBAAiB;AACxB,SAAO,IAAI,aAAc,MAA6C,aAAa,sFAAsF;AAC3K;AACA,SAAS,oBAAoB;AAC3B,SAAO,IAAI,aAAc,MAAgD,aAAa,yEAAyE;AACjK;AACA,SAAS,aAAa,cAAc,aAAa;AAC/C,SAAO,IAAI,aAAc,MAA2C,aAAa,UAAU,YAAY,iFAAiF,YAAY,KAAK,IAAI,CAAC,EAAE;AAClN;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,IAAI,aAAc,MAAiD,aAAa,mCAAmC,KAAK,kBAAkB;AACnJ;AACA,SAAS,yBAAyB,MAAM,YAAY,UAAU,aAAa,WAAW;AACpF,SAAO,IAAI,aAAc,MAAwD,aAAa,qBAAqB,IAAI,uCAAuC,UAAU,YAAY,QAAQ,4EAA4E,WAAW,YAAY,SAAS,KAAK;AAC/S;AACA,SAAS,mBAAmB;AAC1B,SAAO,IAAI,aAAc,MAA+C,aAAa,0DAA0D;AACjJ;AACA,SAAS,gBAAgB;AACvB,SAAO,IAAI,aAAc,MAA4C,aAAa,6DAA6D;AACjJ;AACA,SAAS,4BAA4B;AACnC,SAAO,IAAI,aAAc,MAA2D,aAAa,sDAAsD;AACzJ;AACA,SAAS,0BAA0B;AACjC,SAAO,IAAI,aAAc,MAAuD,aAAa,uEAAuE;AACtK;AACA,SAAS,iBAAiB;AACxB,SAAO,IAAI,aAAc,MAA6C,aAAa,8CAA8C;AACnI;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,IAAI,aAAc,MAA2C,aAAa,YAAY,QAAQ,8CAA8C,QAAQ,sDAAsD;AACnN;AACA,SAAS,kBAAkB,MAAM;AAC/B,SAAO,IAAI,aAAc,MAAgD,aAAa,uCAAuC,IAAI,oBAAoB;AACvJ;AACA,SAAS,uBAAuB,OAAO;AACrC,SAAO,IAAI,aAAc,MAAsD,aAAa,+BAA+B,KAAK,oBAAoB;AACtJ;AAOA,SAAS,mBAAmB,MAAM,QAAQ;AACxC,SAAO,IAAI,aAAc,MAAkD,aAAa,0BAA0B,IAAI;AAAA,KAA0D,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,OAAO,CAAC,EAAE;AAChO;AACA,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,IAAI,aAAc,MAA8C,aAAa,iDAAiD,UAAU,GAAG,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,UAAU,CAAC,EAAE;AACrM;AACA,SAAS,eAAe,QAAQ;AAC9B,SAAO,IAAI,aAAc,MAAiD,aAAa,8DAA8D,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE;AAClM;AACA,SAAS,8BAA8B;AACrC,SAAO,IAAI,aAAc,MAA4D,aAAa,qEAAqE;AACzK;AACA,SAAS,sBAAsB,QAAQ;AACrC,SAAO,IAAI,aAAc,MAAqD,aAAa,8DAA8D,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE;AACtM;AACA,SAAS,cAAc,IAAI;AACzB,SAAO,IAAI,aAAc,MAA4C,aAAa,oDAAoD,EAAE,EAAE;AAC5I;AACA,SAAS,eAAe,OAAO,MAAM;AACnC,SAAO,IAAI,aAAc,MAA6C,aAAa,oDAAoD,KAAK,oCAAoC,IAAI,kBAAmB;AACzM;AACA,SAAS,aAAa,MAAM;AAC1B,SAAO,IAAI,aAAc,MAA2C,aAAa,8CAA8C,IAAI,4CAA4C;AACjL;AACA,SAAS,wBAAwB,OAAO,MAAM;AAC5C,SAAO,IAAI,aAAc,MAAuD,aAAa,yCAAyC,KAAK,gCAAgC,IAAI,qBAAqB;AACtM;AACA,SAAS,oBAAoB,MAAM;AACjC,SAAO,IAAI,aAAc,MAAkD,aAAa,mCAAmC,IAAI,4BAA4B;AAC7J;AACA,SAAS,yBAAyB,QAAQ;AACxC,SAAO,IAAI,aAAc,MAAwD,aAAa;AAAA,GAAkF,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE;AAC7N;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,IAAI,aAAc,MAA+C,aAAa,IAAI,IAAI;AAAA,GAAyB,OAAO,IAAI,SAAO,IAAI,OAAO,EAAE,KAAK,MAAM,CAAC,EAAE;AACrK;AAOA,IAAM,sBAAsB,oBAAI,IAAI,CAAC,uBAAuB,kCAAkC,mCAAmC,+BAA+B,gCAAgC,oBAAoB,iBAAiB,sBAAsB,2BAA2B,uBAAuB,6BAA6B,gBAAgB,OAAO,mBAAmB,cAAc,oBAAoB,uBAAuB,mBAAmB,cAAc,UAAU,oBAAoB,0BAA0B,0BAA0B,sBAAsB,4BAA4B,4BAA4B,iBAAiB,uBAAuB,6BAA6B,8BAA8B,uBAAuB,gBAAgB,yBAAyB,2BAA2B,uBAAuB,sBAAsB,sBAAsB,qBAAqB,2BAA2B,2BAA2B,uBAAuB,6BAA6B,6BAA6B,eAAe,qBAAqB,qBAAqB,iBAAiB,gBAAgB,sBAAsB,sBAAsB,2BAA2B,6BAA6B,cAAc,oBAAoB,0BAA0B,2BAA2B,oBAAoB,gBAAgB,UAAU,cAAc,eAAe,QAAQ,aAAa,SAAS,gBAAgB,cAAc,eAAe,qBAAqB,qBAAqB,gBAAgB,WAAW,UAAU,QAAQ,cAAc,aAAa,eAAe,QAAQ,aAAa,oBAAoB,gBAAgB,2BAA2B,eAAe,OAAO,mBAAmB,YAAY,gBAAgB,yBAAyB,sBAAsB,UAAU,eAAe,kBAAkB,SAAS,eAAe,mBAAmB,qBAAqB,gBAAgB,oBAAoB,sBAAsB,QAAQ,kBAAkB,cAAc,eAAe,UAAU,oBAAoB,sBAAsB,iBAAiB,qBAAqB,uBAAuB,eAAe,gBAAgB,cAAc,QAAQ,eAAe,iBAAiB,aAAa,kBAAkB,cAAc,mBAAmB,aAAa,aAAa,kBAAkB,cAAc,mBAAmB,aAAa,mBAAmB,UAAU,iBAAiB,mBAAmB,eAAe,mBAAmB,iBAAiB,WAAW,SAAS,WAAW,iBAAiB,kBAAkB,iBAAiB,WAAW,qBAAqB,uBAAuB,kBAAkB,sBAAsB,wBAAwB,gBAAgB,iBAAiB,eAAe,eAAe,sBAAsB,SAAS,UAAU,WAAW,SAAS,iBAAiB,uBAAuB,2BAA2B,6BAA6B,wBAAwB,wBAAwB,4BAA4B,8BAA8B,sBAAsB,uBAAuB,qBAAqB,kBAAkB,wBAAwB,4BAA4B,8BAA8B,yBAAyB,yBAAyB,6BAA6B,+BAA+B,uBAAuB,wBAAwB,sBAAsB,0BAA0B,2BAA2B,mBAAmB,yBAAyB,gBAAgB,iBAAiB,YAAY,mBAAmB,yBAAyB,6BAA6B,iBAAiB,uBAAuB,eAAe,eAAe,yBAAyB,OAAO,aAAa,oBAAoB,aAAa,kBAAkB,cAAc,SAAS,gBAAgB,WAAW,MAAM,CAAC;AAC9sH,SAAS,oBAAoB,SAAS;AACpC,UAAQ,QAAQ,QAAQ;AAAA,IACtB,KAAK;AACH,aAAO,IAAI,oBAAoB;AAAA,IACjC,KAAK;AACH,aAAO,QAAQ,CAAC;AAAA,IAClB;AACE,aAAO,IAAI,qBAAqB,OAAO;AAAA,EAC3C;AACF;AACA,SAAS,qBAAqB,YAAY,WAAW,YAAY,oBAAI,IAAI,GAAG,aAAa,oBAAI,IAAI,GAAG;AAClG,QAAM,SAAS,CAAC;AAChB,QAAM,sBAAsB,CAAC;AAC7B,MAAI,iBAAiB;AACrB,MAAI,mBAAmB;AACvB,YAAU,QAAQ,QAAM;AACtB,UAAM,SAAS,GAAG,IAAI,QAAQ;AAC9B,UAAM,eAAe,UAAU;AAC/B,UAAM,qBAAqB,gBAAgB,oBAAoB,oBAAI,IAAI;AACvE,OAAG,QAAQ,CAAC,KAAK,SAAS;AACxB,UAAI,iBAAiB;AACrB,UAAI,kBAAkB;AACtB,UAAI,SAAS,UAAU;AACrB,yBAAiB,WAAW,sBAAsB,gBAAgB,MAAM;AACxE,gBAAQ,iBAAiB;AAAA,UACvB,KAAK;AACH,8BAAkB,UAAU,IAAI,IAAI;AACpC;AAAA,UACF,KAAK;AACH,8BAAkB,WAAW,IAAI,IAAI;AACrC;AAAA,UACF;AACE,8BAAkB,WAAW,oBAAoB,MAAM,gBAAgB,iBAAiB,MAAM;AAC9F;AAAA,QACJ;AAAA,MACF;AACA,yBAAmB,IAAI,gBAAgB,eAAe;AAAA,IACxD,CAAC;AACD,QAAI,CAAC,cAAc;AACjB,0BAAoB,KAAK,kBAAkB;AAAA,IAC7C;AACA,uBAAmB;AACnB,qBAAiB;AAAA,EACnB,CAAC;AACD,MAAI,OAAO,QAAQ;AACjB,UAAM,gBAAgB,MAAM;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ,WAAW,OAAO,UAAU;AAC1D,UAAQ,WAAW;AAAA,IACjB,KAAK;AACH,aAAO,QAAQ,MAAM,SAAS,SAAS,mBAAmB,OAAO,SAAS,MAAM,CAAC,CAAC;AAClF;AAAA,IACF,KAAK;AACH,aAAO,OAAO,MAAM,SAAS,SAAS,mBAAmB,OAAO,QAAQ,MAAM,CAAC,CAAC;AAChF;AAAA,IACF,KAAK;AACH,aAAO,UAAU,MAAM,SAAS,SAAS,mBAAmB,OAAO,WAAW,MAAM,CAAC,CAAC;AACtF;AAAA,EACJ;AACF;AACA,SAAS,mBAAmB,GAAG,WAAW,QAAQ;AAChD,QAAM,YAAY,OAAO;AACzB,QAAM,WAAW,OAAO,WAAW,OAAO;AAC1C,QAAM,QAAQ,mBAAmB,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,aAAa,EAAE,WAAW,aAAa,SAAY,EAAE,YAAY,WAAW,QAAQ;AACvK,QAAM,OAAO,EAAE,OAAO;AACtB,MAAI,QAAQ,MAAM;AAChB,UAAM,OAAO,IAAI;AAAA,EACnB;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,SAAS,aAAa,WAAW,SAAS,YAAY,IAAI,YAAY,GAAG,UAAU;AAC7G,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,CAAC,CAAC;AAAA,EACd;AACF;AACA,SAAS,qBAAqB,KAAK,KAAK,cAAc;AACpD,MAAI,QAAQ,IAAI,IAAI,GAAG;AACvB,MAAI,CAAC,OAAO;AACV,QAAI,IAAI,KAAK,QAAQ,YAAY;AAAA,EACnC;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,SAAS;AACrC,QAAM,eAAe,QAAQ,QAAQ,GAAG;AACxC,QAAM,KAAK,QAAQ,UAAU,GAAG,YAAY;AAC5C,QAAM,SAAS,QAAQ,MAAM,eAAe,CAAC;AAC7C,SAAO,CAAC,IAAI,MAAM;AACpB;AACA,IAAM,mBAAkC,MAAM,OAAO,aAAa,cAAc,OAAO,SAAS,iBAAiB;AACjH,SAAS,iBAAiB,SAAS;AACjC,QAAM,SAAS,QAAQ,cAAc,QAAQ,QAAQ;AACrD,MAAI,WAAW,iBAAiB;AAC9B,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAGlC,SAAO,KAAK,UAAU,GAAG,CAAC,KAAK;AACjC;AACA,IAAI,eAAe;AACnB,IAAI,aAAa;AACjB,SAAS,sBAAsB,MAAM;AACnC,MAAI,CAAC,cAAc;AACjB,mBAAe,YAAY,KAAK,CAAC;AACjC,iBAAa,aAAa,QAAQ,sBAAsB,aAAa,QAAQ;AAAA,EAC/E;AACA,MAAI,SAAS;AACb,MAAI,aAAa,SAAS,CAAC,qBAAqB,IAAI,GAAG;AACrD,aAAS,QAAQ,aAAa;AAC9B,QAAI,CAAC,UAAU,YAAY;AACzB,YAAM,YAAY,WAAW,KAAK,OAAO,CAAC,EAAE,YAAY,IAAI,KAAK,MAAM,CAAC;AACxE,eAAS,aAAa,aAAa;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mCAAmC,MAAM;AAChD,SAAO,oBAAoB,IAAI,IAAI;AACrC;AACA,SAAS,cAAc;AACrB,MAAI,OAAO,YAAY,aAAa;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,MAAM,MAAM;AACnC,SAAO,MAAM;AACX,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,WAAO,iBAAiB,IAAI;AAAA,EAC9B;AACA,SAAO;AACT;AACA,SAAS,YAAY,SAAS,UAAU,OAAO;AAC7C,MAAI,OAAO;AACT,WAAO,MAAM,KAAK,QAAQ,iBAAiB,QAAQ,CAAC;AAAA,EACtD;AACA,QAAM,OAAO,QAAQ,cAAc,QAAQ;AAC3C,SAAO,OAAO,CAAC,IAAI,IAAI,CAAC;AAC1B;AACA,IAAM,aAAa;AACnB,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAC/B,IAAM,wBAAwB;AAC9B,SAAS,mBAAmB,OAAO;AACjC,MAAI,OAAO,SAAS,SAAU,QAAO;AACrC,QAAM,UAAU,MAAM,MAAM,mBAAmB;AAC/C,MAAI,CAAC,WAAW,QAAQ,SAAS,EAAG,QAAO;AAC3C,SAAO,sBAAsB,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjE;AACA,SAAS,sBAAsB,OAAO,MAAM;AAC1C,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,QAAQ;AAAA,IACjB;AAEE,aAAO;AAAA,EACX;AACF;AACA,SAAS,cAAc,SAAS,QAAQ,qBAAqB;AAC3D,SAAO,QAAQ,eAAe,UAAU,IAAI,UAAU,oBAAoB,SAAS,QAAQ,mBAAmB;AAChH;AACA,SAAS,oBAAoB,KAAK,QAAQ,qBAAqB;AAC7D,QAAM,QAAQ;AACd,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,UAAU,IAAI,MAAM,KAAK;AAC/B,QAAI,YAAY,MAAM;AACpB,aAAO,KAAK,mBAAmB,GAAG,CAAC;AACnC,aAAO;AAAA,QACL,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AACA,eAAW,sBAAsB,WAAW,QAAQ,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;AACnE,UAAM,aAAa,QAAQ,CAAC;AAC5B,QAAI,cAAc,MAAM;AACtB,cAAQ,sBAAsB,WAAW,UAAU,GAAG,QAAQ,CAAC,CAAC;AAAA,IAClE;AACA,UAAM,YAAY,QAAQ,CAAC;AAC3B,QAAI,WAAW;AACb,eAAS;AAAA,IACX;AAAA,EACF,OAAO;AACL,eAAW;AAAA,EACb;AACA,MAAI,CAAC,qBAAqB;AACxB,QAAI,iBAAiB;AACrB,QAAI,aAAa,OAAO;AACxB,QAAI,WAAW,GAAG;AAChB,aAAO,KAAK,kBAAkB,CAAC;AAC/B,uBAAiB;AAAA,IACnB;AACA,QAAI,QAAQ,GAAG;AACb,aAAO,KAAK,mBAAmB,CAAC;AAChC,uBAAiB;AAAA,IACnB;AACA,QAAI,gBAAgB;AAClB,aAAO,OAAO,YAAY,GAAG,mBAAmB,GAAG,CAAC;AAAA,IACtD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,WAAW;AACrC,MAAI,CAAC,UAAU,QAAQ;AACrB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,UAAU,CAAC,aAAa,KAAK;AAC/B,WAAO;AAAA,EACT;AACA,SAAO,UAAU,IAAI,QAAM,IAAI,IAAI,OAAO,QAAQ,EAAE,CAAC,CAAC;AACxD;AAIA,SAAS,UAAU,SAAS,QAAQ,cAAc;AAChD,SAAO,QAAQ,CAAC,KAAK,SAAS;AAC5B,UAAM,YAAY,oBAAoB,IAAI;AAC1C,QAAI,gBAAgB,CAAC,aAAa,IAAI,IAAI,GAAG;AAC3C,mBAAa,IAAI,MAAM,QAAQ,MAAM,SAAS,CAAC;AAAA,IACjD;AACA,YAAQ,MAAM,SAAS,IAAI;AAAA,EAC7B,CAAC;AACH;AACA,SAAS,YAAY,SAAS,QAAQ;AACpC,SAAO,QAAQ,CAAC,GAAG,SAAS;AAC1B,UAAM,YAAY,oBAAoB,IAAI;AAC1C,YAAQ,MAAM,SAAS,IAAI;AAAA,EAC7B,CAAC;AACH;AACA,SAAS,wBAAwB,OAAO;AACtC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,QAAI,MAAM,UAAU,EAAG,QAAO,MAAM,CAAC;AACrC,WAAO,SAAS,KAAK;AAAA,EACvB;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,OAAO,SAAS,QAAQ;AACnD,QAAM,SAAS,QAAQ,UAAU,CAAC;AAClC,QAAM,UAAU,mBAAmB,KAAK;AACxC,MAAI,QAAQ,QAAQ;AAClB,YAAQ,QAAQ,aAAW;AACzB,UAAI,CAAC,OAAO,eAAe,OAAO,GAAG;AACnC,eAAO,KAAK,mBAAmB,OAAO,CAAC;AAAA,MACzC;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,cAA6B,IAAI,OAAO,GAAG,uBAAuB,gBAAgB,qBAAqB,IAAI,GAAG;AACpH,SAAS,mBAAmB,OAAO;AACjC,MAAI,SAAS,CAAC;AACd,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI;AACJ,WAAO,QAAQ,YAAY,KAAK,KAAK,GAAG;AACtC,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACtB;AACA,gBAAY,YAAY;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAChD,QAAM,WAAW,GAAG,KAAK;AACzB,QAAM,MAAM,SAAS,QAAQ,aAAa,CAAC,GAAG,YAAY;AACxD,QAAI,WAAW,OAAO,OAAO;AAE7B,QAAI,YAAY,MAAM;AACpB,aAAO,KAAK,kBAAkB,OAAO,CAAC;AACtC,iBAAW;AAAA,IACb;AACA,WAAO,SAAS,SAAS;AAAA,EAC3B,CAAC;AAED,SAAO,OAAO,WAAW,QAAQ;AACnC;AACA,IAAM,mBAAmB;AACzB,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM,QAAQ,kBAAkB,IAAI,MAAM,EAAE,CAAC,EAAE,YAAY,CAAC;AACrE;AACA,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM,QAAQ,mBAAmB,OAAO,EAAE,YAAY;AAC/D;AACA,SAAS,+BAA+B,UAAU,OAAO;AACvD,SAAO,aAAa,KAAK,UAAU;AACrC;AACA,SAAS,mCAAmC,SAAS,WAAW,gBAAgB;AAC9E,MAAI,eAAe,QAAQ,UAAU,QAAQ;AAC3C,QAAI,mBAAmB,UAAU,CAAC;AAClC,QAAI,oBAAoB,CAAC;AACzB,mBAAe,QAAQ,CAAC,KAAK,SAAS;AACpC,UAAI,CAAC,iBAAiB,IAAI,IAAI,GAAG;AAC/B,0BAAkB,KAAK,IAAI;AAAA,MAC7B;AACA,uBAAiB,IAAI,MAAM,GAAG;AAAA,IAChC,CAAC;AACD,QAAI,kBAAkB,QAAQ;AAC5B,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,YAAI,KAAK,UAAU,CAAC;AACpB,0BAAkB,QAAQ,UAAQ,GAAG,IAAI,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC;AAAA,MAC7E;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,SAAS,MAAM,SAAS;AAC5C,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK,sBAAsB;AACzB,aAAO,QAAQ,aAAa,MAAM,OAAO;AAAA,IAC3C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,IACzC,KAAK,sBAAsB;AACzB,aAAO,QAAQ,gBAAgB,MAAM,OAAO;AAAA,IAC9C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,cAAc,MAAM,OAAO;AAAA,IAC5C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,IACzC,KAAK,sBAAsB;AACzB,aAAO,QAAQ,aAAa,MAAM,OAAO;AAAA,IAC3C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,eAAe,MAAM,OAAO;AAAA,IAC7C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,IACzC,KAAK,sBAAsB;AACzB,aAAO,QAAQ,eAAe,MAAM,OAAO;AAAA,IAC7C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,kBAAkB,MAAM,OAAO;AAAA,IAChD,KAAK,sBAAsB;AACzB,aAAO,QAAQ,gBAAgB,MAAM,OAAO;AAAA,IAC9C,KAAK,sBAAsB;AACzB,aAAO,QAAQ,WAAW,MAAM,OAAO;AAAA,IACzC,KAAK,sBAAsB;AACzB,aAAO,QAAQ,aAAa,MAAM,OAAO;AAAA,IAC3C;AACE,YAAM,gBAAgB,KAAK,IAAI;AAAA,EACnC;AACF;AACA,SAAS,aAAa,SAAS,MAAM;AACnC,SAAO,OAAO,iBAAiB,OAAO,EAAE,IAAI;AAC9C;;;AC9cA,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA,EAIxB,sBAAsB,MAAM;AAC1B,WAAO,sBAAsB,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,gBAAgB,MAAM,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,SAAS;AACxB,WAAO,iBAAiB,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,UAAU,OAAO;AAC9B,WAAO,YAAY,SAAS,UAAU,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,SAAS,MAAM,cAAc;AACxC,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,WAAW,UAAU,OAAO,QAAQ,kBAAkB,CAAC,GAAG,yBAAyB;AAClG,WAAO,IAAI,oBAAoB,UAAU,KAAK;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,qBAAoB;AAAA,EAC/B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,OAAO,IAAI,oBAAoB;AACxC;AACA,IAAM,2BAAN,MAA+B;AAAC;AAShC,IAAM,uBAAuB,oBAAI,IAAI,CAAC,SAAS,UAAU,YAAY,aAAa,YAAY,aAAa,QAAQ,OAAO,UAAU,SAAS,YAAY,gBAAgB,iBAAiB,cAAc,eAAe,iBAAiB,gBAAgB,aAAa,cAAc,gBAAgB,eAAe,gBAAgB,eAAe,kBAAkB,mBAAmB,oBAAoB,qBAAqB,cAAc,aAAa,CAAC;AAC3b,IAAM,+BAAN,cAA2C,yBAAyB;AAAA,EAClE,sBAAsB,cAAc,QAAQ;AAC1C,WAAO,oBAAoB,YAAY;AAAA,EACzC;AAAA,EACA,oBAAoB,sBAAsB,oBAAoB,OAAO,QAAQ;AAC3E,QAAI,OAAO;AACX,UAAM,SAAS,MAAM,SAAS,EAAE,KAAK;AACrC,QAAI,qBAAqB,IAAI,kBAAkB,KAAK,UAAU,KAAK,UAAU,KAAK;AAChF,UAAI,OAAO,UAAU,UAAU;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,cAAM,oBAAoB,MAAM,MAAM,wBAAwB;AAC9D,YAAI,qBAAqB,kBAAkB,CAAC,EAAE,UAAU,GAAG;AACzD,iBAAO,KAAK,oBAAoB,sBAAsB,KAAK,CAAC;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AACA,WAAO,SAAS;AAAA,EAClB;AACF;AACA,SAAS,qBAAqB,UAAU;AACtC,QAAMA,cAAa;AACnB,SAAO,GAAGA,WAAU,GAAG,SAAS,OAAO,OAAO,EAAE,IAAI,aAAW,OAAO,EAAE,KAAKA,WAAU,CAAC;AAC1F;AAIA,SAAS,iBAAiB,MAAM,UAAU;AACxC,UAAQ,KAAK,0BAA0B,IAAI,2CAA2C,qBAAqB,QAAQ,CAAC,EAAE;AACxH;AACA,SAAS,aAAa,UAAU;AAC9B,UAAQ,KAAK,+CAA+C,qBAAqB,QAAQ,CAAC,EAAE;AAC9F;AACA,SAAS,kCAAkC,UAAU,OAAO;AAC1D,MAAI,MAAM,QAAQ;AAChB,aAAS,KAAK,yDAAyD,MAAM,KAAK,IAAI,CAAC,EAAE;AAAA,EAC3F;AACF;AACA,IAAM,YAAY;AAClB,SAAS,oBAAoB,iBAAiB,QAAQ;AACpD,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,mBAAmB,UAAU;AACtC,oBAAgB,MAAM,SAAS,EAAE,QAAQ,SAAO,wBAAwB,KAAK,aAAa,MAAM,CAAC;AAAA,EACnG,OAAO;AACL,gBAAY,KAAK,eAAe;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,wBAAwB,UAAU,aAAa,QAAQ;AAC9D,MAAI,SAAS,CAAC,KAAK,KAAK;AACtB,UAAM,SAAS,oBAAoB,UAAU,MAAM;AACnD,QAAI,OAAO,UAAU,YAAY;AAC/B,kBAAY,KAAK,MAAM;AACvB;AAAA,IACF;AACA,eAAW;AAAA,EACb;AACA,QAAM,QAAQ,SAAS,MAAM,yCAAyC;AACtE,MAAI,SAAS,QAAQ,MAAM,SAAS,GAAG;AACrC,WAAO,KAAK,kBAAkB,QAAQ,CAAC;AACvC,WAAO;AAAA,EACT;AACA,QAAM,YAAY,MAAM,CAAC;AACzB,QAAM,YAAY,MAAM,CAAC;AACzB,QAAM,UAAU,MAAM,CAAC;AACvB,cAAY,KAAK,qBAAqB,WAAW,OAAO,CAAC;AACzD,QAAM,qBAAqB,aAAa,aAAa,WAAW;AAChE,MAAI,UAAU,CAAC,KAAK,OAAO,CAAC,oBAAoB;AAC9C,gBAAY,KAAK,qBAAqB,SAAS,SAAS,CAAC;AAAA,EAC3D;AACA;AACF;AACA,SAAS,oBAAoB,OAAO,QAAQ;AAC1C,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO,CAAC,WAAW,YAAY,WAAW,OAAO,IAAI,WAAW,SAAS;AAAA,IAC3E,KAAK;AACH,aAAO,CAAC,WAAW,YAAY,WAAW,OAAO,IAAI,WAAW,SAAS;AAAA,IAC3E;AACE,aAAO,KAAK,uBAAuB,KAAK,CAAC;AACzC,aAAO;AAAA,EACX;AACF;AAKA,IAAM,sBAAsB,oBAAI,IAAI,CAAC,QAAQ,GAAG,CAAC;AACjD,IAAM,uBAAuB,oBAAI,IAAI,CAAC,SAAS,GAAG,CAAC;AACnD,SAAS,qBAAqB,KAAK,KAAK;AACtC,QAAM,oBAAoB,oBAAoB,IAAI,GAAG,KAAK,qBAAqB,IAAI,GAAG;AACtF,QAAM,oBAAoB,oBAAoB,IAAI,GAAG,KAAK,qBAAqB,IAAI,GAAG;AACtF,SAAO,CAAC,WAAW,YAAY;AAC7B,QAAI,WAAW,OAAO,aAAa,OAAO;AAC1C,QAAI,WAAW,OAAO,aAAa,OAAO;AAC1C,QAAI,CAAC,YAAY,qBAAqB,OAAO,cAAc,WAAW;AACpE,iBAAW,YAAY,oBAAoB,IAAI,GAAG,IAAI,qBAAqB,IAAI,GAAG;AAAA,IACpF;AACA,QAAI,CAAC,YAAY,qBAAqB,OAAO,YAAY,WAAW;AAClE,iBAAW,UAAU,oBAAoB,IAAI,GAAG,IAAI,qBAAqB,IAAI,GAAG;AAAA,IAClF;AACA,WAAO,YAAY;AAAA,EACrB;AACF;AACA,IAAM,aAAa;AACnB,IAAM,mBAAkC,IAAI,OAAO,KAAK,UAAU,QAAQ,GAAG;AAqC7E,SAAS,kBAAkB,QAAQ,UAAU,QAAQ,UAAU;AAC7D,SAAO,IAAI,2BAA2B,MAAM,EAAE,MAAM,UAAU,QAAQ,QAAQ;AAChF;AACA,IAAM,gBAAgB;AACtB,IAAM,6BAAN,MAAiC;AAAA,EAC/B;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,MAAM,UAAU,QAAQ,UAAU;AAChC,UAAM,UAAU,IAAI,2BAA2B,MAAM;AACrD,SAAK,8BAA8B,OAAO;AAC1C,UAAM,MAAM,aAAa,MAAM,wBAAwB,QAAQ,GAAG,OAAO;AACzE,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,UAAI,QAAQ,8BAA8B,MAAM;AAC9C,0CAAkC,UAAU,CAAC,GAAG,QAAQ,8BAA8B,KAAK,CAAC,CAAC;AAAA,MAC/F;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,8BAA8B,SAAS;AACrC,YAAQ,uBAAuB;AAC/B,YAAQ,kBAAkB,oBAAI,IAAI;AAClC,YAAQ,gBAAgB,IAAI,eAAe,oBAAI,IAAI,CAAC;AACpD,YAAQ,cAAc;AAAA,EACxB;AAAA,EACA,aAAa,UAAU,SAAS;AAC9B,QAAI,aAAa,QAAQ,aAAa;AACtC,QAAI,WAAW,QAAQ,WAAW;AAClC,UAAM,SAAS,CAAC;AAChB,UAAM,cAAc,CAAC;AACrB,QAAI,SAAS,KAAK,OAAO,CAAC,KAAK,KAAK;AAClC,cAAQ,OAAO,KAAK,eAAe,CAAC;AAAA,IACtC;AACA,aAAS,YAAY,QAAQ,SAAO;AAClC,WAAK,8BAA8B,OAAO;AAC1C,UAAI,IAAI,QAAQ,sBAAsB,OAAO;AAC3C,cAAM,WAAW;AACjB,cAAM,OAAO,SAAS;AACtB,aAAK,SAAS,EAAE,MAAM,SAAS,EAAE,QAAQ,OAAK;AAC5C,mBAAS,OAAO;AAChB,iBAAO,KAAK,KAAK,WAAW,UAAU,OAAO,CAAC;AAAA,QAChD,CAAC;AACD,iBAAS,OAAO;AAAA,MAClB,WAAW,IAAI,QAAQ,sBAAsB,YAAY;AACvD,cAAM,aAAa,KAAK,gBAAgB,KAAK,OAAO;AACpD,sBAAc,WAAW;AACzB,oBAAY,WAAW;AACvB,oBAAY,KAAK,UAAU;AAAA,MAC7B,OAAO;AACL,gBAAQ,OAAO,KAAK,kBAAkB,CAAC;AAAA,MACzC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,MAAM,SAAS;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,WAAW,UAAU,SAAS;AAC5B,UAAM,WAAW,KAAK,WAAW,SAAS,QAAQ,OAAO;AACzD,UAAM,YAAY,SAAS,WAAW,SAAS,QAAQ,UAAU;AACjE,QAAI,SAAS,uBAAuB;AAClC,YAAM,cAAc,oBAAI,IAAI;AAC5B,YAAM,SAAS,aAAa,CAAC;AAC7B,eAAS,OAAO,QAAQ,CAAAC,WAAS;AAC/B,YAAIA,kBAAiB,KAAK;AACxB,UAAAA,OAAM,QAAQ,WAAS;AACrB,+BAAmB,KAAK,EAAE,QAAQ,SAAO;AACvC,kBAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC/B,4BAAY,IAAI,GAAG;AAAA,cACrB;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,UAAI,YAAY,MAAM;AACpB,gBAAQ,OAAO,KAAK,aAAa,SAAS,MAAM,CAAC,GAAG,YAAY,OAAO,CAAC,CAAC,CAAC;AAAA,MAC5E;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,MAAM,SAAS;AAAA,MACf,OAAO;AAAA,MACP,SAAS,YAAY;AAAA,QACnB,QAAQ;AAAA,MACV,IAAI;AAAA,IACN;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAAS;AACjC,YAAQ,aAAa;AACrB,YAAQ,WAAW;AACnB,UAAM,YAAY,aAAa,MAAM,wBAAwB,SAAS,SAAS,GAAG,OAAO;AACzF,UAAM,WAAW,oBAAoB,SAAS,MAAM,QAAQ,MAAM;AAClE,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B;AAAA,MACA;AAAA,MACA,YAAY,QAAQ;AAAA,MACpB,UAAU,QAAQ;AAAA,MAClB,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,cAAc,UAAU,SAAS;AAC/B,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,OAAO,SAAS,MAAM,IAAI,OAAK,aAAa,MAAM,GAAG,OAAO,CAAC;AAAA,MAC7D,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,WAAW,UAAU,SAAS;AAC5B,UAAM,cAAc,QAAQ;AAC5B,QAAI,eAAe;AACnB,UAAM,QAAQ,SAAS,MAAM,IAAI,UAAQ;AACvC,cAAQ,cAAc;AACtB,YAAM,WAAW,aAAa,MAAM,MAAM,OAAO;AACjD,qBAAe,KAAK,IAAI,cAAc,QAAQ,WAAW;AACzD,aAAO;AAAA,IACT,CAAC;AACD,YAAQ,cAAc;AACtB,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B;AAAA,MACA,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,aAAa,UAAU,SAAS;AAC9B,UAAM,YAAY,mBAAmB,SAAS,SAAS,QAAQ,MAAM;AACrE,YAAQ,wBAAwB;AAChC,QAAI;AACJ,QAAI,gBAAgB,SAAS,SAAS,SAAS,SAAS,MAAM,CAAC,CAAC;AAChE,QAAI,cAAc,QAAQ,sBAAsB,WAAW;AACzD,iBAAW,KAAK,eAAe,eAAe,OAAO;AAAA,IACvD,OAAO;AACL,UAAIC,iBAAgB,SAAS;AAC7B,UAAI,UAAU;AACd,UAAI,CAACA,gBAAe;AAClB,kBAAU;AACV,cAAM,eAAe,CAAC;AACtB,YAAI,UAAU,QAAQ;AACpB,uBAAa,QAAQ,IAAI,UAAU;AAAA,QACrC;AACA,QAAAA,iBAAgB,MAAM,YAAY;AAAA,MACpC;AACA,cAAQ,eAAe,UAAU,WAAW,UAAU;AACtD,YAAM,YAAY,KAAK,WAAWA,gBAAe,OAAO;AACxD,gBAAU,cAAc;AACxB,iBAAW;AAAA,IACb;AACA,YAAQ,wBAAwB;AAChC,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,WAAW,UAAU,SAAS;AAC5B,UAAM,MAAM,KAAK,cAAc,UAAU,OAAO;AAChD,SAAK,kBAAkB,KAAK,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,UAAU,SAAS;AAC/B,UAAM,SAAS,CAAC;AAChB,UAAM,iBAAiB,MAAM,QAAQ,SAAS,MAAM,IAAI,SAAS,SAAS,CAAC,SAAS,MAAM;AAC1F,aAAS,cAAc,gBAAgB;AACrC,UAAI,OAAO,eAAe,UAAU;AAClC,YAAI,eAAe,YAAY;AAC7B,iBAAO,KAAK,UAAU;AAAA,QACxB,OAAO;AACL,kBAAQ,OAAO,KAAK,kBAAkB,UAAU,CAAC;AAAA,QACnD;AAAA,MACF,OAAO;AACL,eAAO,KAAK,IAAI,IAAI,OAAO,QAAQ,UAAU,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AACA,QAAI,wBAAwB;AAC5B,QAAI,kBAAkB;AACtB,WAAO,QAAQ,eAAa;AAC1B,UAAI,qBAAqB,KAAK;AAC5B,YAAI,UAAU,IAAI,QAAQ,GAAG;AAC3B,4BAAkB,UAAU,IAAI,QAAQ;AACxC,oBAAU,OAAO,QAAQ;AAAA,QAC3B;AACA,YAAI,CAAC,uBAAuB;AAC1B,mBAAS,SAAS,UAAU,OAAO,GAAG;AACpC,gBAAI,MAAM,SAAS,EAAE,QAAQ,uBAAuB,KAAK,GAAG;AAC1D,sCAAwB;AACxB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ,SAAS;AAAA,MACjB;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,kBAAkB,KAAK,SAAS;AAC9B,UAAM,UAAU,QAAQ;AACxB,QAAI,UAAU,QAAQ;AACtB,QAAI,YAAY,QAAQ;AACxB,QAAI,WAAW,YAAY,GAAG;AAC5B,mBAAa,QAAQ,WAAW,QAAQ;AAAA,IAC1C;AACA,QAAI,OAAO,QAAQ,WAAS;AAC1B,UAAI,OAAO,UAAU,SAAU;AAC/B,YAAM,QAAQ,CAAC,OAAO,SAAS;AAC7B,YAAI,OAAO,cAAc,eAAe,WAAW;AACjD,cAAI,CAAC,KAAK,QAAQ,sBAAsB,IAAI,GAAG;AAC7C,kBAAM,OAAO,IAAI;AACjB,oBAAQ,8BAA8B,IAAI,IAAI;AAC9C;AAAA,UACF;AAAA,QACF;AAGA,cAAM,kBAAkB,QAAQ,gBAAgB,IAAI,QAAQ,oBAAoB;AAChF,cAAM,iBAAiB,gBAAgB,IAAI,IAAI;AAC/C,YAAI,uBAAuB;AAC3B,YAAI,gBAAgB;AAClB,cAAI,aAAa,WAAW,aAAa,eAAe,aAAa,WAAW,eAAe,SAAS;AACtG,oBAAQ,OAAO,KAAK,yBAAyB,MAAM,eAAe,WAAW,eAAe,SAAS,WAAW,OAAO,CAAC;AACxH,mCAAuB;AAAA,UACzB;AAIA,sBAAY,eAAe;AAAA,QAC7B;AACA,YAAI,sBAAsB;AACxB,0BAAgB,IAAI,MAAM;AAAA,YACxB;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AACA,YAAI,QAAQ,SAAS;AACnB,8BAAoB,OAAO,QAAQ,SAAS,QAAQ,MAAM;AAAA,QAC5D;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,eAAe,UAAU,SAAS;AAChC,UAAM,MAAM;AAAA,MACV,MAAM,sBAAsB;AAAA,MAC5B,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,IACX;AACA,QAAI,CAAC,QAAQ,uBAAuB;AAClC,cAAQ,OAAO,KAAK,iBAAiB,CAAC;AACtC,aAAO;AAAA,IACT;AACA,UAAM,sBAAsB;AAC5B,QAAI,4BAA4B;AAChC,UAAM,UAAU,CAAC;AACjB,QAAI,oBAAoB;AACxB,QAAI,sBAAsB;AAC1B,QAAI,iBAAiB;AACrB,UAAM,YAAY,SAAS,MAAM,IAAI,YAAU;AAC7C,YAAMD,SAAQ,KAAK,cAAc,QAAQ,OAAO;AAChD,UAAI,YAAYA,OAAM,UAAU,OAAOA,OAAM,SAAS,cAAcA,OAAM,MAAM;AAChF,UAAI,SAAS;AACb,UAAI,aAAa,MAAM;AACrB;AACA,iBAASA,OAAM,SAAS;AAAA,MAC1B;AACA,4BAAsB,uBAAuB,SAAS,KAAK,SAAS;AACpE,0BAAoB,qBAAqB,SAAS;AAClD,uBAAiB;AACjB,cAAQ,KAAK,MAAM;AACnB,aAAOA;AAAA,IACT,CAAC;AACD,QAAI,qBAAqB;AACvB,cAAQ,OAAO,KAAK,cAAc,CAAC;AAAA,IACrC;AACA,QAAI,mBAAmB;AACrB,cAAQ,OAAO,KAAK,0BAA0B,CAAC;AAAA,IACjD;AACA,UAAM,SAAS,SAAS,MAAM;AAC9B,QAAI,kBAAkB;AACtB,QAAI,4BAA4B,KAAK,4BAA4B,QAAQ;AACvE,cAAQ,OAAO,KAAK,wBAAwB,CAAC;AAAA,IAC/C,WAAW,6BAA6B,GAAG;AACzC,wBAAkB,uBAAuB,SAAS;AAAA,IACpD;AACA,UAAM,QAAQ,SAAS;AACvB,UAAM,cAAc,QAAQ;AAC5B,UAAM,wBAAwB,QAAQ;AACtC,UAAM,kBAAkB,sBAAsB;AAC9C,cAAU,QAAQ,CAAC,IAAI,MAAM;AAC3B,YAAM,SAAS,kBAAkB,IAAI,KAAK,QAAQ,IAAI,kBAAkB,IAAI,QAAQ,CAAC;AACrF,YAAM,wBAAwB,SAAS;AACvC,cAAQ,cAAc,cAAc,sBAAsB,QAAQ;AAClE,4BAAsB,WAAW;AACjC,WAAK,kBAAkB,IAAI,OAAO;AAClC,SAAG,SAAS;AACZ,UAAI,OAAO,KAAK,EAAE;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU,SAAS;AAChC,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,WAAW,aAAa,MAAM,wBAAwB,SAAS,SAAS,GAAG,OAAO;AAAA,MAClF,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,kBAAkB,UAAU,SAAS;AACnC,YAAQ;AACR,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAAS;AACjC,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,WAAW,KAAK,eAAe,SAAS,WAAW,OAAO;AAAA,MAC1D,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,WAAW,UAAU,SAAS;AAC5B,UAAM,iBAAiB,QAAQ;AAC/B,UAAM,UAAU,SAAS,WAAW,CAAC;AACrC,YAAQ;AACR,YAAQ,eAAe;AACvB,UAAM,CAAC,UAAU,WAAW,IAAI,kBAAkB,SAAS,QAAQ;AACnE,YAAQ,uBAAuB,eAAe,SAAS,iBAAiB,MAAM,WAAW;AACzF,yBAAqB,QAAQ,iBAAiB,QAAQ,sBAAsB,oBAAI,IAAI,CAAC;AACrF,UAAM,YAAY,aAAa,MAAM,wBAAwB,SAAS,SAAS,GAAG,OAAO;AACzF,YAAQ,eAAe;AACvB,YAAQ,uBAAuB;AAC/B,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B;AAAA,MACA,OAAO,QAAQ,SAAS;AAAA,MACxB,UAAU,CAAC,CAAC,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,MACA,kBAAkB,SAAS;AAAA,MAC3B,SAAS,0BAA0B,SAAS,OAAO;AAAA,IACrD;AAAA,EACF;AAAA,EACA,aAAa,UAAU,SAAS;AAC9B,QAAI,CAAC,QAAQ,cAAc;AACzB,cAAQ,OAAO,KAAK,eAAe,CAAC;AAAA,IACtC;AACA,UAAM,UAAU,SAAS,YAAY,SAAS;AAAA,MAC5C,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,IACV,IAAI,cAAc,SAAS,SAAS,QAAQ,QAAQ,IAAI;AACxD,WAAO;AAAA,MACL,MAAM,sBAAsB;AAAA,MAC5B,WAAW,aAAa,MAAM,wBAAwB,SAAS,SAAS,GAAG,OAAO;AAAA,MAClF;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,UAAU;AACnC,QAAM,eAAe,SAAS,MAAM,SAAS,EAAE,KAAK,WAAS,SAAS,UAAU,IAAI,OAAO;AAC3F,MAAI,cAAc;AAChB,eAAW,SAAS,QAAQ,kBAAkB,EAAE;AAAA,EAClD;AAGA,aAAW,SAAS,QAAQ,QAAQ,mBAAmB,EAAE,QAAQ,SAAS,WAAS,sBAAsB,MAAM,MAAM,MAAM,CAAC,CAAC,EAAE,QAAQ,eAAe,qBAAqB;AAC3K,SAAO,CAAC,UAAU,YAAY;AAChC;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,MAAM,mBACR,OACD;AACN;AACA,IAAM,6BAAN,MAAiC;AAAA,EAC/B;AAAA,EACA,aAAa;AAAA,EACb,WAAW;AAAA,EACX,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,UAAU;AAAA,EACV,gCAAgC,oBAAI,IAAI;AAAA,EACxC,YAAY,QAAQ;AAClB,SAAK,SAAS;AAAA,EAChB;AACF;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,OAAO,UAAU,SAAU,QAAO;AACtC,MAAI,SAAS;AACb,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,QAAQ,gBAAc;AAC3B,UAAI,sBAAsB,OAAO,WAAW,IAAI,QAAQ,GAAG;AACzD,cAAM,MAAM;AACZ,iBAAS,WAAW,IAAI,IAAI,QAAQ,CAAC;AACrC,YAAI,OAAO,QAAQ;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH,WAAW,kBAAkB,OAAO,OAAO,IAAI,QAAQ,GAAG;AACxD,UAAM,MAAM;AACZ,aAAS,WAAW,IAAI,IAAI,QAAQ,CAAC;AACrC,QAAI,OAAO,QAAQ;AAAA,EACrB;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO,QAAQ;AACzC,MAAI,MAAM,eAAe,UAAU,GAAG;AACpC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,WAAW,cAAc,OAAO,MAAM,EAAE;AAC9C,WAAO,cAAc,UAAU,GAAG,EAAE;AAAA,EACtC;AACA,QAAM,WAAW;AACjB,QAAM,YAAY,SAAS,MAAM,KAAK,EAAE,KAAK,OAAK,EAAE,OAAO,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,KAAK,GAAG;AAC1F,MAAI,WAAW;AACb,UAAM,MAAM,cAAc,GAAG,GAAG,EAAE;AAClC,QAAI,UAAU;AACd,QAAI,WAAW;AACf,WAAO;AAAA,EACT;AACA,QAAM,UAAU,cAAc,UAAU,MAAM;AAC9C,SAAO,cAAc,QAAQ,UAAU,QAAQ,OAAO,QAAQ,MAAM;AACtE;AACA,SAAS,0BAA0B,SAAS;AAC1C,MAAI,SAAS;AACX,cAAU,mBACL;AAEL,QAAI,QAAQ,QAAQ,GAAG;AACrB,cAAQ,QAAQ,IAAI,gBAAgB,QAAQ,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF,OAAO;AACL,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,cAAc,UAAU,OAAO,QAAQ;AAC9C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,SAAS,WAAW,eAAe,gBAAgB,UAAU,OAAO,SAAS,MAAM,cAAc,OAAO;AACzI,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,WAAW;AAAA,IACtB;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,wBAAN,MAA4B;AAAA,EAC1B,OAAO,oBAAI,IAAI;AAAA,EACf,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,IAAI,OAAO,KAAK,CAAC;AAAA,EACpC;AAAA,EACA,OAAO,SAAS,cAAc;AAC5B,QAAI,uBAAuB,KAAK,KAAK,IAAI,OAAO;AAChD,QAAI,CAAC,sBAAsB;AACzB,WAAK,KAAK,IAAI,SAAS,uBAAuB,CAAC,CAAC;AAAA,IAClD;AACA,yBAAqB,KAAK,GAAG,YAAY;AAAA,EAC3C;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,KAAK,IAAI,OAAO;AAAA,EAC9B;AAAA,EACA,QAAQ;AACN,SAAK,KAAK,MAAM;AAAA,EAClB;AACF;AACA,IAAM,4BAA4B;AAClC,IAAM,cAAc;AACpB,IAAM,oBAAmC,IAAI,OAAO,aAAa,GAAG;AACpE,IAAM,cAAc;AACpB,IAAM,oBAAmC,IAAI,OAAO,aAAa,GAAG;AA+EpE,SAAS,wBAAwB,QAAQ,aAAa,KAAK,gBAAgB,gBAAgB,iBAAiB,oBAAI,IAAI,GAAG,cAAc,oBAAI,IAAI,GAAG,SAAS,iBAAiB,SAAS,CAAC,GAAG;AACrL,SAAO,IAAI,gCAAgC,EAAE,eAAe,QAAQ,aAAa,KAAK,gBAAgB,gBAAgB,gBAAgB,aAAa,SAAS,iBAAiB,MAAM;AACrL;AACA,IAAM,kCAAN,MAAsC;AAAA,EACpC,eAAe,QAAQ,aAAa,KAAK,gBAAgB,gBAAgB,gBAAgB,aAAa,SAAS,iBAAiB,SAAS,CAAC,GAAG;AAC3I,sBAAkB,mBAAmB,IAAI,sBAAsB;AAC/D,UAAM,UAAU,IAAI,yBAAyB,QAAQ,aAAa,iBAAiB,gBAAgB,gBAAgB,QAAQ,CAAC,CAAC;AAC7H,YAAQ,UAAU;AAClB,UAAM,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAClE,YAAQ,gBAAgB,cAAc,KAAK;AAC3C,YAAQ,gBAAgB,UAAU,CAAC,cAAc,GAAG,MAAM,QAAQ,QAAQ,OAAO;AACjF,iBAAa,MAAM,KAAK,OAAO;AAE/B,UAAM,YAAY,QAAQ,UAAU,OAAO,cAAY,SAAS,kBAAkB,CAAC;AAKnF,QAAI,UAAU,UAAU,YAAY,MAAM;AACxC,UAAI;AACJ,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,cAAM,WAAW,UAAU,CAAC;AAC5B,YAAI,SAAS,YAAY,aAAa;AACpC,6BAAmB;AACnB;AAAA,QACF;AAAA,MACF;AACA,UAAI,oBAAoB,CAAC,iBAAiB,wBAAwB,GAAG;AACnE,yBAAiB,UAAU,CAAC,WAAW,GAAG,MAAM,QAAQ,QAAQ,OAAO;AAAA,MACzE;AAAA,IACF;AACA,WAAO,UAAU,SAAS,UAAU,IAAI,cAAY,SAAS,eAAe,CAAC,IAAI,CAAC,0BAA0B,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,OAAO,IAAI,KAAK,CAAC;AAAA,EAC3J;AAAA,EACA,aAAa,KAAK,SAAS;AAAA,EAE3B;AAAA,EACA,WAAW,KAAK,SAAS;AAAA,EAEzB;AAAA,EACA,gBAAgB,KAAK,SAAS;AAAA,EAE9B;AAAA,EACA,kBAAkB,KAAK,SAAS;AAC9B,UAAM,sBAAsB,QAAQ,gBAAgB,IAAI,QAAQ,OAAO;AACvE,QAAI,qBAAqB;AACvB,YAAM,eAAe,QAAQ,iBAAiB,IAAI,OAAO;AACzD,YAAM,YAAY,QAAQ,gBAAgB;AAC1C,YAAM,UAAU,KAAK,sBAAsB,qBAAqB,cAAc,aAAa,OAAO;AAClG,UAAI,aAAa,SAAS;AAGxB,gBAAQ,yBAAyB,OAAO;AAAA,MAC1C;AAAA,IACF;AACA,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,gBAAgB,KAAK,SAAS;AAC5B,UAAM,eAAe,QAAQ,iBAAiB,IAAI,OAAO;AACzD,iBAAa,yBAAyB;AACtC,SAAK,yBAAyB,CAAC,IAAI,SAAS,IAAI,UAAU,OAAO,GAAG,SAAS,YAAY;AACzF,SAAK,eAAe,IAAI,WAAW,YAAY;AAC/C,YAAQ,yBAAyB,aAAa,gBAAgB,WAAW;AACzE,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,yBAAyB,uBAAuB,SAAS,cAAc;AACrE,eAAW,uBAAuB,uBAAuB;AACvD,YAAM,iBAAiB,qBAAqB;AAC5C,UAAI,gBAAgB;AAClB,cAAM,sBAAsB,OAAO,mBAAmB,WAAW,iBAAiB,mBAAmB,kBAAkB,gBAAgB,qBAAqB,UAAU,CAAC,GAAG,QAAQ,MAAM,CAAC;AACzL,qBAAa,cAAc,mBAAmB;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAAA,EACA,sBAAsB,cAAc,SAAS,SAAS;AACpD,UAAM,YAAY,QAAQ,gBAAgB;AAC1C,QAAI,eAAe;AAGnB,UAAM,WAAW,QAAQ,YAAY,OAAO,mBAAmB,QAAQ,QAAQ,IAAI;AACnF,UAAM,QAAQ,QAAQ,SAAS,OAAO,mBAAmB,QAAQ,KAAK,IAAI;AAC1E,QAAI,aAAa,GAAG;AAClB,mBAAa,QAAQ,iBAAe;AAClC,cAAM,qBAAqB,QAAQ,4BAA4B,aAAa,UAAU,KAAK;AAC3F,uBAAe,KAAK,IAAI,cAAc,mBAAmB,WAAW,mBAAmB,KAAK;AAAA,MAC9F,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,KAAK,SAAS;AAC3B,YAAQ,cAAc,IAAI,SAAS,IAAI;AACvC,iBAAa,MAAM,IAAI,WAAW,OAAO;AACzC,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,cAAc,KAAK,SAAS;AAC1B,UAAM,kBAAkB,QAAQ;AAChC,QAAI,MAAM;AACV,UAAM,UAAU,IAAI;AACpB,QAAI,YAAY,QAAQ,UAAU,QAAQ,QAAQ;AAChD,YAAM,QAAQ,iBAAiB,OAAO;AACtC,UAAI,yBAAyB;AAC7B,UAAI,QAAQ,SAAS,MAAM;AACzB,YAAI,IAAI,aAAa,QAAQ,sBAAsB,OAAO;AACxD,cAAI,gBAAgB,sBAAsB;AAC1C,cAAI,eAAe;AAAA,QACrB;AACA,cAAM,QAAQ,mBAAmB,QAAQ,KAAK;AAC9C,YAAI,cAAc,KAAK;AAAA,MACzB;AAAA,IACF;AACA,QAAI,IAAI,MAAM,QAAQ;AACpB,UAAI,MAAM,QAAQ,OAAK,aAAa,MAAM,GAAG,GAAG,CAAC;AAEjD,UAAI,gBAAgB,sBAAsB;AAI1C,UAAI,IAAI,kBAAkB,iBAAiB;AACzC,YAAI,yBAAyB;AAAA,MAC/B;AAAA,IACF;AACA,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,WAAW,KAAK,SAAS;AACvB,UAAM,iBAAiB,CAAC;AACxB,QAAI,eAAe,QAAQ,gBAAgB;AAC3C,UAAM,QAAQ,IAAI,WAAW,IAAI,QAAQ,QAAQ,mBAAmB,IAAI,QAAQ,KAAK,IAAI;AACzF,QAAI,MAAM,QAAQ,OAAK;AACrB,YAAM,eAAe,QAAQ,iBAAiB,IAAI,OAAO;AACzD,UAAI,OAAO;AACT,qBAAa,cAAc,KAAK;AAAA,MAClC;AACA,mBAAa,MAAM,GAAG,YAAY;AAClC,qBAAe,KAAK,IAAI,cAAc,aAAa,gBAAgB,WAAW;AAC9E,qBAAe,KAAK,aAAa,eAAe;AAAA,IAClD,CAAC;AAID,mBAAe,QAAQ,cAAY,QAAQ,gBAAgB,6BAA6B,QAAQ,CAAC;AACjG,YAAQ,yBAAyB,YAAY;AAC7C,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,aAAa,KAAK,SAAS;AACzB,QAAI,IAAI,SAAS;AACf,YAAM,WAAW,IAAI;AACrB,YAAM,cAAc,QAAQ,SAAS,kBAAkB,UAAU,QAAQ,QAAQ,QAAQ,MAAM,IAAI;AACnG,aAAO,cAAc,aAAa,QAAQ,MAAM;AAAA,IAClD,OAAO;AACL,aAAO;AAAA,QACL,UAAU,IAAI;AAAA,QACd,OAAO,IAAI;AAAA,QACX,QAAQ,IAAI;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,KAAK,SAAS;AACzB,UAAM,UAAU,QAAQ,wBAAwB,KAAK,aAAa,IAAI,SAAS,OAAO;AACtF,UAAM,WAAW,QAAQ;AACzB,QAAI,QAAQ,OAAO;AACjB,cAAQ,cAAc,QAAQ,KAAK;AACnC,eAAS,sBAAsB;AAAA,IACjC;AACA,UAAMA,SAAQ,IAAI;AAClB,QAAIA,OAAM,QAAQ,sBAAsB,WAAW;AACjD,WAAK,eAAeA,QAAO,OAAO;AAAA,IACpC,OAAO;AACL,cAAQ,cAAc,QAAQ,QAAQ;AACtC,WAAK,WAAWA,QAAO,OAAO;AAC9B,eAAS,sBAAsB;AAAA,IACjC;AACA,YAAQ,wBAAwB;AAChC,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,WAAW,KAAK,SAAS;AACvB,UAAM,WAAW,QAAQ;AACzB,UAAM,UAAU,QAAQ;AAGxB,QAAI,CAAC,WAAW,SAAS,0BAA0B,GAAG;AACpD,eAAS,aAAa;AAAA,IACxB;AACA,UAAM,SAAS,WAAW,QAAQ,UAAU,IAAI;AAChD,QAAI,IAAI,aAAa;AACnB,eAAS,eAAe,MAAM;AAAA,IAChC,OAAO;AACL,eAAS,UAAU,IAAI,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAAA,IACxE;AACA,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,eAAe,KAAK,SAAS;AAC3B,UAAM,wBAAwB,QAAQ;AACtC,UAAM,YAAY,QAAQ,gBAAgB;AAC1C,UAAM,WAAW,sBAAsB;AACvC,UAAM,eAAe,QAAQ,iBAAiB;AAC9C,UAAM,gBAAgB,aAAa;AACnC,kBAAc,SAAS,sBAAsB;AAC7C,QAAI,OAAO,QAAQ,UAAQ;AACzB,YAAM,SAAS,KAAK,UAAU;AAC9B,oBAAc,YAAY,SAAS,QAAQ;AAC3C,oBAAc,UAAU,KAAK,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AACjF,oBAAc,sBAAsB;AAAA,IACtC,CAAC;AAGD,YAAQ,gBAAgB,6BAA6B,aAAa;AAGlE,YAAQ,yBAAyB,YAAY,QAAQ;AACrD,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,WAAW,KAAK,SAAS;AAGvB,UAAM,YAAY,QAAQ,gBAAgB;AAC1C,UAAM,UAAU,IAAI,WAAW,CAAC;AAChC,UAAM,QAAQ,QAAQ,QAAQ,mBAAmB,QAAQ,KAAK,IAAI;AAClE,QAAI,UAAU,QAAQ,aAAa,SAAS,sBAAsB,SAAS,aAAa,KAAK,QAAQ,gBAAgB,0BAA0B,IAAI;AACjJ,cAAQ,gBAAgB,sBAAsB;AAC9C,cAAQ,eAAe;AAAA,IACzB;AACA,QAAI,eAAe;AACnB,UAAM,OAAO,QAAQ,YAAY,IAAI,UAAU,IAAI,kBAAkB,IAAI,OAAO,IAAI,aAAa,QAAQ,WAAW,OAAO,OAAO,QAAQ,MAAM;AAChJ,YAAQ,oBAAoB,KAAK;AACjC,QAAI,sBAAsB;AAC1B,SAAK,QAAQ,CAAC,SAAS,MAAM;AAC3B,cAAQ,oBAAoB;AAC5B,YAAM,eAAe,QAAQ,iBAAiB,IAAI,SAAS,OAAO;AAClE,UAAI,OAAO;AACT,qBAAa,cAAc,KAAK;AAAA,MAClC;AACA,UAAI,YAAY,QAAQ,SAAS;AAC/B,8BAAsB,aAAa;AAAA,MACrC;AACA,mBAAa,MAAM,IAAI,WAAW,YAAY;AAI9C,mBAAa,gBAAgB,sBAAsB;AACnD,YAAM,UAAU,aAAa,gBAAgB;AAC7C,qBAAe,KAAK,IAAI,cAAc,OAAO;AAAA,IAC/C,CAAC;AACD,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,yBAAyB,YAAY;AAC7C,QAAI,qBAAqB;AACvB,cAAQ,gBAAgB,6BAA6B,mBAAmB;AACxE,cAAQ,gBAAgB,sBAAsB;AAAA,IAChD;AACA,YAAQ,eAAe;AAAA,EACzB;AAAA,EACA,aAAa,KAAK,SAAS;AACzB,UAAM,gBAAgB,QAAQ;AAC9B,UAAM,KAAK,QAAQ;AACnB,UAAM,UAAU,IAAI;AACpB,UAAM,WAAW,KAAK,IAAI,QAAQ,QAAQ;AAC1C,UAAM,UAAU,YAAY,QAAQ,oBAAoB;AACxD,QAAI,QAAQ,WAAW,QAAQ;AAC/B,QAAI,qBAAqB,QAAQ,WAAW,IAAI,YAAY,QAAQ;AACpE,YAAQ,oBAAoB;AAAA,MAC1B,KAAK;AACH,gBAAQ,UAAU;AAClB;AAAA,MACF,KAAK;AACH,gBAAQ,cAAc;AACtB;AAAA,IACJ;AACA,UAAM,WAAW,QAAQ;AACzB,QAAI,OAAO;AACT,eAAS,cAAc,KAAK;AAAA,IAC9B;AACA,UAAM,eAAe,SAAS;AAC9B,iBAAa,MAAM,IAAI,WAAW,OAAO;AACzC,YAAQ,eAAe;AAKvB,kBAAc,qBAAqB,GAAG,cAAc,gBAAgB,GAAG,YAAY,cAAc,gBAAgB;AAAA,EACnH;AACF;AACA,IAAM,6BAA6B,CAAC;AACpC,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB;AAAA,EACA,wBAAwB;AAAA,EACxB,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,UAAU,CAAC;AAAA,EACX,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,YAAY,SAAS,SAAS,iBAAiB,iBAAiB,iBAAiB,QAAQ,WAAW,iBAAiB;AACnH,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,kBAAkB,mBAAmB,IAAI,gBAAgB,KAAK,SAAS,SAAS,CAAC;AACtF,cAAU,KAAK,KAAK,eAAe;AAAA,EACrC;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,cAAc,SAAS,cAAc;AACnC,QAAI,CAAC,QAAS;AACd,UAAM,aAAa;AACnB,QAAI,kBAAkB,KAAK;AAE3B,QAAI,WAAW,YAAY,MAAM;AAC/B,sBAAgB,WAAW,mBAAmB,WAAW,QAAQ;AAAA,IACnE;AACA,QAAI,WAAW,SAAS,MAAM;AAC5B,sBAAgB,QAAQ,mBAAmB,WAAW,KAAK;AAAA,IAC7D;AACA,UAAM,YAAY,WAAW;AAC7B,QAAI,WAAW;AACb,UAAI,iBAAiB,gBAAgB;AACrC,UAAI,CAAC,gBAAgB;AACnB,yBAAiB,KAAK,QAAQ,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO,KAAK,SAAS,EAAE,QAAQ,UAAQ;AACrC,YAAI,CAAC,gBAAgB,CAAC,eAAe,eAAe,IAAI,GAAG;AACzD,yBAAe,IAAI,IAAI,kBAAkB,UAAU,IAAI,GAAG,gBAAgB,KAAK,MAAM;AAAA,QACvF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe;AACb,UAAM,UAAU,CAAC;AACjB,QAAI,KAAK,SAAS;AAChB,YAAM,YAAY,KAAK,QAAQ;AAC/B,UAAI,WAAW;AACb,cAAM,SAAS,QAAQ,QAAQ,IAAI,CAAC;AACpC,eAAO,KAAK,SAAS,EAAE,QAAQ,UAAQ;AACrC,iBAAO,IAAI,IAAI,UAAU,IAAI;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,UAAU,MAAM,SAAS,SAAS;AACjD,UAAM,SAAS,WAAW,KAAK;AAC/B,UAAM,UAAU,IAAI,0BAAyB,KAAK,SAAS,QAAQ,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,iBAAiB,KAAK,QAAQ,KAAK,WAAW,KAAK,gBAAgB,KAAK,QAAQ,WAAW,CAAC,CAAC;AACjN,YAAQ,eAAe,KAAK;AAC5B,YAAQ,wBAAwB,KAAK;AACrC,YAAQ,UAAU,KAAK,aAAa;AACpC,YAAQ,cAAc,OAAO;AAC7B,YAAQ,oBAAoB,KAAK;AACjC,YAAQ,oBAAoB,KAAK;AACjC,YAAQ,gBAAgB;AACxB,SAAK;AACL,WAAO;AAAA,EACT;AAAA,EACA,yBAAyB,SAAS;AAChC,SAAK,eAAe;AACpB,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,KAAK,SAAS,OAAO;AACtE,SAAK,UAAU,KAAK,KAAK,eAAe;AACxC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,4BAA4B,aAAa,UAAU,OAAO;AACxD,UAAM,iBAAiB;AAAA,MACrB,UAAU,YAAY,OAAO,WAAW,YAAY;AAAA,MACpD,OAAO,KAAK,gBAAgB,eAAe,SAAS,OAAO,QAAQ,KAAK,YAAY;AAAA,MACpF,QAAQ;AAAA,IACV;AACA,UAAM,UAAU,IAAI,mBAAmB,KAAK,SAAS,YAAY,SAAS,YAAY,WAAW,YAAY,eAAe,YAAY,gBAAgB,gBAAgB,YAAY,uBAAuB;AAC3M,SAAK,UAAU,KAAK,OAAO;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,SAAK,gBAAgB,YAAY,KAAK,gBAAgB,WAAW,IAAI;AAAA,EACvE;AAAA,EACA,cAAc,OAAO;AAEnB,QAAI,QAAQ,GAAG;AACb,WAAK,gBAAgB,cAAc,KAAK;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,YAAY,UAAU,kBAAkB,OAAO,aAAa,UAAU,QAAQ;AAC5E,QAAI,UAAU,CAAC;AACf,QAAI,aAAa;AACf,cAAQ,KAAK,KAAK,OAAO;AAAA,IAC3B;AACA,QAAI,SAAS,SAAS,GAAG;AAEvB,iBAAW,SAAS,QAAQ,mBAAmB,MAAM,KAAK,eAAe;AACzE,iBAAW,SAAS,QAAQ,mBAAmB,MAAM,KAAK,eAAe;AACzE,YAAM,QAAQ,SAAS;AACvB,UAAI,WAAW,KAAK,QAAQ,MAAM,KAAK,SAAS,UAAU,KAAK;AAC/D,UAAI,UAAU,GAAG;AACf,mBAAW,QAAQ,IAAI,SAAS,MAAM,SAAS,SAAS,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,GAAG,KAAK;AAAA,MAC3G;AACA,cAAQ,KAAK,GAAG,QAAQ;AAAA,IAC1B;AACA,QAAI,CAAC,YAAY,QAAQ,UAAU,GAAG;AACpC,aAAO,KAAK,aAAa,gBAAgB,CAAC;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,SAAS;AAAA,EACT,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,mBAAmB,oBAAI,IAAI;AAAA,EAC3B,aAAa,oBAAI,IAAI;AAAA,EACrB,gBAAgB,oBAAI,IAAI;AAAA,EACxB,uBAAuB,oBAAI,IAAI;AAAA,EAC/B;AAAA,EACA,iBAAiB,oBAAI,IAAI;AAAA,EACzB,YAAY,oBAAI,IAAI;AAAA,EACpB,4BAA4B;AAAA,EAC5B,YAAY,SAAS,SAAS,WAAW,8BAA8B;AACrE,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,+BAA+B;AACpC,QAAI,CAAC,KAAK,8BAA8B;AACtC,WAAK,+BAA+B,oBAAI,IAAI;AAAA,IAC9C;AACA,SAAK,wBAAwB,KAAK,6BAA6B,IAAI,OAAO;AAC1E,QAAI,CAAC,KAAK,uBAAuB;AAC/B,WAAK,wBAAwB,KAAK;AAClC,WAAK,6BAA6B,IAAI,SAAS,KAAK,oBAAoB;AAAA,IAC1E;AACA,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,oBAAoB;AAClB,YAAQ,KAAK,WAAW,MAAM;AAAA,MAC5B,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO,KAAK,0BAA0B;AAAA,MACxC;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,4BAA4B;AAC1B,WAAO,KAAK,iBAAiB,OAAO;AAAA,EACtC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,cAAc,OAAO;AAKnB,UAAM,kBAAkB,KAAK,WAAW,SAAS,KAAK,KAAK,eAAe;AAC1E,QAAI,KAAK,YAAY,iBAAiB;AACpC,WAAK,YAAY,KAAK,cAAc,KAAK;AACzC,UAAI,iBAAiB;AACnB,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,KAAK,SAAS,aAAa;AACzB,SAAK,sBAAsB;AAC3B,WAAO,IAAI,iBAAgB,KAAK,SAAS,SAAS,eAAe,KAAK,aAAa,KAAK,4BAA4B;AAAA,EACtH;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,kBAAkB;AACzB,WAAK,oBAAoB,KAAK;AAAA,IAChC;AACA,SAAK,mBAAmB,KAAK,WAAW,IAAI,KAAK,QAAQ;AACzD,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,mBAAmB,oBAAI,IAAI;AAChC,WAAK,WAAW,IAAI,KAAK,UAAU,KAAK,gBAAgB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,sBAAsB;AAC3B,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,aAAa,MAAM,OAAO;AACxB,SAAK,qBAAqB,IAAI,MAAM,KAAK;AACzC,SAAK,sBAAsB,IAAI,MAAM,KAAK;AAC1C,SAAK,cAAc,IAAI,MAAM;AAAA,MAC3B,MAAM,KAAK;AAAA,MACX;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,8BAA8B,KAAK;AAAA,EACjD;AAAA,EACA,eAAe,QAAQ;AACrB,QAAI,QAAQ;AACV,WAAK,kBAAkB,IAAI,UAAU,MAAM;AAAA,IAC7C;AAOA,aAAS,CAAC,MAAM,KAAK,KAAK,KAAK,uBAAuB;AACpD,WAAK,UAAU,IAAI,MAAM,SAAS,UAAU;AAC5C,WAAK,iBAAiB,IAAI,MAAM,UAAU;AAAA,IAC5C;AACA,SAAK,4BAA4B,KAAK;AAAA,EACxC;AAAA,EACA,UAAU,OAAO,QAAQ,QAAQ,SAAS;AACxC,QAAI,QAAQ;AACV,WAAK,kBAAkB,IAAI,UAAU,MAAM;AAAA,IAC7C;AACA,UAAM,SAAS,WAAW,QAAQ,UAAU,CAAC;AAC7C,UAAM,SAAS,cAAc,OAAO,KAAK,qBAAqB;AAC9D,aAAS,CAAC,MAAM,KAAK,KAAK,QAAQ;AAChC,YAAM,MAAM,kBAAkB,OAAO,QAAQ,MAAM;AACnD,WAAK,eAAe,IAAI,MAAM,GAAG;AACjC,UAAI,CAAC,KAAK,qBAAqB,IAAI,IAAI,GAAG;AACxC,aAAK,UAAU,IAAI,MAAM,KAAK,sBAAsB,IAAI,IAAI,KAAK,UAAU;AAAA,MAC7E;AACA,WAAK,aAAa,MAAM,GAAG;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,eAAe,QAAQ,EAAG;AACnC,SAAK,eAAe,QAAQ,CAAC,KAAK,SAAS;AACzC,WAAK,iBAAiB,IAAI,MAAM,GAAG;AAAA,IACrC,CAAC;AACD,SAAK,eAAe,MAAM;AAC1B,SAAK,qBAAqB,QAAQ,CAAC,KAAK,SAAS;AAC/C,UAAI,CAAC,KAAK,iBAAiB,IAAI,IAAI,GAAG;AACpC,aAAK,iBAAiB,IAAI,MAAM,GAAG;AAAA,MACrC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,aAAS,CAAC,MAAM,GAAG,KAAK,KAAK,sBAAsB;AACjD,WAAK,eAAe,IAAI,MAAM,GAAG;AACjC,WAAK,aAAa,MAAM,GAAG;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,WAAW,IAAI,KAAK,QAAQ;AAAA,EAC1C;AAAA,EACA,IAAI,aAAa;AACf,UAAM,aAAa,CAAC;AACpB,aAAS,QAAQ,KAAK,kBAAkB;AACtC,iBAAW,KAAK,IAAI;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AAAA,EACA,6BAA6B,UAAU;AACrC,aAAS,cAAc,QAAQ,CAAC,UAAU,SAAS;AACjD,YAAM,WAAW,KAAK,cAAc,IAAI,IAAI;AAC5C,UAAI,CAAC,YAAY,SAAS,OAAO,SAAS,MAAM;AAC9C,aAAK,aAAa,MAAM,SAAS,KAAK;AAAA,MACxC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,SAAK,sBAAsB;AAC3B,UAAM,gBAAgB,oBAAI,IAAI;AAC9B,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,UAAU,KAAK,WAAW,SAAS,KAAK,KAAK,aAAa;AAChE,QAAI,iBAAiB,CAAC;AACtB,SAAK,WAAW,QAAQ,CAAC,UAAU,SAAS;AAC1C,YAAM,gBAAgB,IAAI,IAAI,CAAC,GAAG,KAAK,WAAW,GAAG,QAAQ,CAAC;AAC9D,oBAAc,QAAQ,CAAC,OAAO,SAAS;AACrC,YAAI,UAAU,YAAY;AACxB,wBAAc,IAAI,IAAI;AAAA,QACxB,WAAW,UAAU,YAAY;AAC/B,yBAAe,IAAI,IAAI;AAAA,QACzB;AAAA,MACF,CAAC;AACD,UAAI,CAAC,SAAS;AACZ,sBAAc,IAAI,UAAU,OAAO,KAAK,QAAQ;AAAA,MAClD;AACA,qBAAe,KAAK,aAAa;AAAA,IACnC,CAAC;AACD,UAAM,WAAW,CAAC,GAAG,cAAc,OAAO,CAAC;AAC3C,UAAM,YAAY,CAAC,GAAG,eAAe,OAAO,CAAC;AAE7C,QAAI,SAAS;AACX,YAAM,MAAM,eAAe,CAAC;AAC5B,YAAM,MAAM,IAAI,IAAI,GAAG;AACvB,UAAI,IAAI,UAAU,CAAC;AACnB,UAAI,IAAI,UAAU,CAAC;AACnB,uBAAiB,CAAC,KAAK,GAAG;AAAA,IAC5B;AACA,WAAO,0BAA0B,KAAK,SAAS,gBAAgB,UAAU,WAAW,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,KAAK;AAAA,EACvI;AACF;AACA,IAAM,qBAAN,cAAiC,gBAAgB;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ,SAAS,WAAW,eAAe,gBAAgB,SAAS,2BAA2B,OAAO;AAChH,UAAM,QAAQ,SAAS,QAAQ,KAAK;AACpC,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AACtB,SAAK,2BAA2B;AAChC,SAAK,UAAU;AAAA,MACb,UAAU,QAAQ;AAAA,MAClB,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAAA,EACA,iBAAiB;AACf,QAAI,YAAY,KAAK;AACrB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,KAAK,4BAA4B,OAAO;AAC1C,YAAM,eAAe,CAAC;AACtB,YAAM,YAAY,WAAW;AAC7B,YAAM,cAAc,QAAQ;AAE5B,YAAM,mBAAmB,IAAI,IAAI,UAAU,CAAC,CAAC;AAC7C,uBAAiB,IAAI,UAAU,CAAC;AAChC,mBAAa,KAAK,gBAAgB;AAClC,YAAM,mBAAmB,IAAI,IAAI,UAAU,CAAC,CAAC;AAC7C,uBAAiB,IAAI,UAAU,YAAY,WAAW,CAAC;AACvD,mBAAa,KAAK,gBAAgB;AAalC,YAAM,QAAQ,UAAU,SAAS;AACjC,eAAS,IAAI,GAAG,KAAK,OAAO,KAAK;AAC/B,YAAI,KAAK,IAAI,IAAI,UAAU,CAAC,CAAC;AAC7B,cAAM,YAAY,GAAG,IAAI,QAAQ;AACjC,cAAM,iBAAiB,QAAQ,YAAY;AAC3C,WAAG,IAAI,UAAU,YAAY,iBAAiB,SAAS,CAAC;AACxD,qBAAa,KAAK,EAAE;AAAA,MACtB;AAEA,iBAAW;AACX,cAAQ;AACR,eAAS;AACT,kBAAY;AAAA,IACd;AACA,WAAO,0BAA0B,KAAK,SAAS,WAAW,KAAK,eAAe,KAAK,gBAAgB,UAAU,OAAO,QAAQ,IAAI;AAAA,EAClI;AACF;AACA,SAAS,YAAY,QAAQ,gBAAgB,GAAG;AAC9C,QAAM,OAAO,KAAK,IAAI,IAAI,gBAAgB,CAAC;AAC3C,SAAO,KAAK,MAAM,SAAS,IAAI,IAAI;AACrC;AACA,SAAS,cAAc,OAAO,WAAW;AACvC,QAAM,SAAS,oBAAI,IAAI;AACvB,MAAI;AACJ,QAAM,QAAQ,WAAS;AACrB,QAAI,UAAU,KAAK;AACjB,wBAAkB,UAAU,KAAK;AACjC,eAAS,QAAQ,eAAe;AAC9B,eAAO,IAAI,MAAM,UAAU;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,eAAS,CAAC,MAAM,GAAG,KAAK,OAAO;AAC7B,eAAO,IAAI,MAAM,GAAG;AAAA,MACtB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,4BAA4B,SAAS,aAAa,WAAW,SAAS,qBAAqB,YAAY,UAAU,WAAW,iBAAiB,eAAe,gBAAgB,WAAW,QAAQ;AACtM,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,eAAe,CAAC;AACtB,IAAM,6BAAN,MAAiC;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,cAAc,KAAK,cAAc;AAC3C,SAAK,eAAe;AACpB,SAAK,MAAM;AACX,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,MAAM,cAAc,WAAW,SAAS,QAAQ;AAC9C,WAAO,0BAA0B,KAAK,IAAI,UAAU,cAAc,WAAW,SAAS,MAAM;AAAA,EAC9F;AAAA,EACA,YAAY,WAAW,QAAQ,QAAQ;AACrC,QAAI,SAAS,KAAK,aAAa,IAAI,GAAG;AACtC,QAAI,cAAc,QAAW;AAC3B,eAAS,KAAK,aAAa,IAAI,WAAW,SAAS,CAAC,KAAK;AAAA,IAC3D;AACA,WAAO,SAAS,OAAO,YAAY,QAAQ,MAAM,IAAI,oBAAI,IAAI;AAAA,EAC/D;AAAA,EACA,MAAM,QAAQ,SAAS,cAAc,WAAW,gBAAgB,gBAAgB,gBAAgB,aAAa,iBAAiB,cAAc;AAC1I,UAAM,SAAS,CAAC;AAChB,UAAM,4BAA4B,KAAK,IAAI,WAAW,KAAK,IAAI,QAAQ,UAAU;AACjF,UAAM,yBAAyB,kBAAkB,eAAe,UAAU;AAC1E,UAAM,qBAAqB,KAAK,YAAY,cAAc,wBAAwB,MAAM;AACxF,UAAM,sBAAsB,eAAe,YAAY,UAAU;AACjE,UAAM,kBAAkB,KAAK,YAAY,WAAW,qBAAqB,MAAM;AAC/E,UAAM,kBAAkB,oBAAI,IAAI;AAChC,UAAM,cAAc,oBAAI,IAAI;AAC5B,UAAM,eAAe,oBAAI,IAAI;AAC7B,UAAM,YAAY,cAAc;AAChC,UAAM,mBAAmB;AAAA,MACvB,QAAQ,mBAAmB,qBAAqB,yBAAyB;AAAA,MACzE,OAAO,KAAK,IAAI,SAAS;AAAA,IAC3B;AACA,UAAM,YAAY,eAAe,CAAC,IAAI,wBAAwB,QAAQ,SAAS,KAAK,IAAI,WAAW,gBAAgB,gBAAgB,oBAAoB,iBAAiB,kBAAkB,iBAAiB,MAAM;AACjN,QAAI,YAAY;AAChB,cAAU,QAAQ,QAAM;AACtB,kBAAY,KAAK,IAAI,GAAG,WAAW,GAAG,OAAO,SAAS;AAAA,IACxD,CAAC;AACD,QAAI,OAAO,QAAQ;AACjB,aAAO,4BAA4B,SAAS,KAAK,cAAc,cAAc,WAAW,WAAW,oBAAoB,iBAAiB,CAAC,GAAG,CAAC,GAAG,aAAa,cAAc,WAAW,MAAM;AAAA,IAC9L;AACA,cAAU,QAAQ,QAAM;AACtB,YAAM,MAAM,GAAG;AACf,YAAM,WAAW,qBAAqB,aAAa,KAAK,oBAAI,IAAI,CAAC;AACjE,SAAG,cAAc,QAAQ,UAAQ,SAAS,IAAI,IAAI,CAAC;AACnD,YAAM,YAAY,qBAAqB,cAAc,KAAK,oBAAI,IAAI,CAAC;AACnE,SAAG,eAAe,QAAQ,UAAQ,UAAU,IAAI,IAAI,CAAC;AACrD,UAAI,QAAQ,SAAS;AACnB,wBAAgB,IAAI,GAAG;AAAA,MACzB;AAAA,IACF,CAAC;AACD,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,oCAA8B,WAAW,KAAK,cAAc,MAAM;AAAA,IACpE;AACA,WAAO,4BAA4B,SAAS,KAAK,cAAc,cAAc,WAAW,WAAW,oBAAoB,iBAAiB,WAAW,CAAC,GAAG,gBAAgB,OAAO,CAAC,GAAG,aAAa,cAAc,SAAS;AAAA,EACxN;AACF;AAeA,SAAS,8BAA8B,WAAW,aAAa,QAAQ;AACrE,MAAI,CAAC,OAAO,iCAAiC;AAC3C;AAAA,EACF;AACA,QAAM,4BAA4B,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,IAK1C;AAAA,EAAQ,CAAC;AACT,QAAM,4BAA4B,oBAAI,IAAI;AAC1C,YAAU,QAAQ,CAAC;AAAA,IACjB;AAAA,EACF,MAAM;AACJ,UAAM,kCAAkC,oBAAI,IAAI;AAChD,cAAU,QAAQ,cAAY;AAC5B,YAAM,iBAAiB,MAAM,KAAK,SAAS,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,0BAA0B,IAAI,IAAI,CAAC;AAC7G,iBAAW,CAAC,MAAM,KAAK,KAAK,gBAAgB;AAC1C,YAAI,CAAC,OAAO,gCAAgC,IAAI,GAAG;AACjD,cAAI,gCAAgC,IAAI,IAAI,KAAK,CAAC,0BAA0B,IAAI,IAAI,GAAG;AACrF,kBAAM,mBAAmB,gCAAgC,IAAI,IAAI;AACjE,gBAAI,qBAAqB,OAAO;AAC9B,wCAA0B,IAAI,IAAI;AAAA,YACpC;AAAA,UACF,OAAO;AACL,4CAAgC,IAAI,MAAM,KAAK;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,0BAA0B,OAAO,GAAG;AACtC,YAAQ,KAAK,mCAAmC,WAAW,yEAA8E,MAAM,KAAK,yBAAyB,EAAE,KAAK,IAAI,IAAI,mIAAwI;AAAA,EACtU;AACF;AACA,SAAS,0BAA0B,UAAU,cAAc,WAAW,SAAS,QAAQ;AACrF,SAAO,SAAS,KAAK,QAAM,GAAG,cAAc,WAAW,SAAS,MAAM,CAAC;AACzE;AACA,SAAS,mBAAmB,YAAY,UAAU;AAChD,QAAM,SAAS,mBACV;AAEL,SAAO,QAAQ,UAAU,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACnD,QAAI,SAAS,MAAM;AACjB,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,IAAM,uBAAN,MAA2B;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ,eAAe,YAAY;AAC7C,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,QAAQ,QAAQ;AAC1B,UAAM,cAAc,oBAAI,IAAI;AAC5B,UAAM,iBAAiB,mBAAmB,QAAQ,KAAK,aAAa;AACpE,SAAK,OAAO,OAAO,QAAQ,WAAS;AAClC,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,QAAQ,CAAC,KAAK,SAAS;AAC3B,cAAI,KAAK;AACP,kBAAM,kBAAkB,KAAK,gBAAgB,MAAM;AAAA,UACrD;AACA,gBAAM,iBAAiB,KAAK,WAAW,sBAAsB,MAAM,MAAM;AACzE,gBAAM,KAAK,WAAW,oBAAoB,MAAM,gBAAgB,KAAK,MAAM;AAC3E,sBAAY,IAAI,MAAM,GAAG;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa,MAAM,KAAK,YAAY;AAC3C,SAAO,IAAI,iBAAiB,MAAM,KAAK,UAAU;AACnD;AACA,IAAM,mBAAN,MAAuB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB,CAAC;AAAA,EACvB;AAAA,EACA,SAAS,oBAAI,IAAI;AAAA,EACjB,YAAY,MAAM,KAAK,aAAa;AAClC,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,cAAc;AACnB,QAAI,OAAO,QAAQ,CAAAE,SAAO;AACxB,YAAM,gBAAgBA,KAAI,WAAWA,KAAI,QAAQ,UAAU,CAAC;AAC5D,WAAK,OAAO,IAAIA,KAAI,MAAM,IAAI,qBAAqBA,KAAI,OAAO,eAAe,WAAW,CAAC;AAAA,IAC3F,CAAC;AACD,sBAAkB,KAAK,QAAQ,QAAQ,GAAG;AAC1C,sBAAkB,KAAK,QAAQ,SAAS,GAAG;AAC3C,QAAI,YAAY,QAAQ,CAAAA,SAAO;AAC7B,WAAK,oBAAoB,KAAK,IAAI,2BAA2B,MAAMA,MAAK,KAAK,MAAM,CAAC;AAAA,IACtF,CAAC;AACD,SAAK,qBAAqB,yBAAyB,MAAM,KAAK,MAAM;AAAA,EACtE;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,IAAI,aAAa;AAAA,EAC/B;AAAA,EACA,gBAAgB,cAAc,WAAW,SAAS,QAAQ;AACxD,UAAM,QAAQ,KAAK,oBAAoB,KAAK,OAAK,EAAE,MAAM,cAAc,WAAW,SAAS,MAAM,CAAC;AAClG,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,YAAY,cAAc,QAAQ,QAAQ;AACxC,WAAO,KAAK,mBAAmB,YAAY,cAAc,QAAQ,MAAM;AAAA,EACzE;AACF;AACA,SAAS,yBAAyB,aAAa,QAAQ,YAAY;AACjE,QAAM,WAAW,CAAC,CAAC,WAAW,YAAY,IAAI;AAC9C,QAAM,YAAY;AAAA,IAChB,MAAM,sBAAsB;AAAA,IAC5B,OAAO,CAAC;AAAA,IACR,SAAS;AAAA,EACX;AACA,QAAM,aAAa;AAAA,IACjB,MAAM,sBAAsB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ;AACA,SAAO,IAAI,2BAA2B,aAAa,YAAY,MAAM;AACvE;AACA,SAAS,kBAAkB,UAAU,MAAM,MAAM;AAC/C,MAAI,SAAS,IAAI,IAAI,GAAG;AACtB,QAAI,CAAC,SAAS,IAAI,IAAI,GAAG;AACvB,eAAS,IAAI,MAAM,SAAS,IAAI,IAAI,CAAC;AAAA,IACvC;AAAA,EACF,WAAW,SAAS,IAAI,IAAI,GAAG;AAC7B,aAAS,IAAI,MAAM,SAAS,IAAI,IAAI,CAAC;AAAA,EACvC;AACF;AACA,IAAM,wBAAwB,IAAI,sBAAsB;AACxD,IAAM,0BAAN,MAA8B;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc,oBAAI,IAAI;AAAA,EACtB,eAAe,oBAAI,IAAI;AAAA,EACvB,UAAU,CAAC;AAAA,EACX,YAAY,UAAU,SAAS,aAAa;AAC1C,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,SAAS,IAAI,UAAU;AACrB,UAAM,SAAS,CAAC;AAChB,UAAM,WAAW,CAAC;AAClB,UAAM,MAAM,kBAAkB,KAAK,SAAS,UAAU,QAAQ,QAAQ;AACtE,QAAI,OAAO,QAAQ;AACjB,YAAM,eAAe,MAAM;AAAA,IAC7B,OAAO;AACL,UAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAI,SAAS,QAAQ;AACnB,uBAAa,QAAQ;AAAA,QACvB;AAAA,MACF;AACA,WAAK,YAAY,IAAI,IAAI,GAAG;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,aAAa,GAAG,WAAW,YAAY;AACrC,UAAM,UAAU,EAAE;AAClB,UAAM,YAAY,qBAAmB,KAAK,aAAa,EAAE,WAAW,WAAW,UAAU;AACzF,WAAO,KAAK,QAAQ,QAAQ,SAAS,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,GAAG,IAAI;AAAA,EACzF;AAAA,EACA,OAAO,IAAI,SAAS,UAAU,CAAC,GAAG;AAChC,UAAM,SAAS,CAAC;AAChB,UAAM,MAAM,KAAK,YAAY,IAAI,EAAE;AACnC,QAAI;AACJ,UAAM,gBAAgB,oBAAI,IAAI;AAC9B,QAAI,KAAK;AACP,qBAAe,wBAAwB,KAAK,SAAS,SAAS,KAAK,iBAAiB,iBAAiB,oBAAI,IAAI,GAAG,oBAAI,IAAI,GAAG,SAAS,uBAAuB,MAAM;AACjK,mBAAa,QAAQ,UAAQ;AAC3B,cAAM,SAAS,qBAAqB,eAAe,KAAK,SAAS,oBAAI,IAAI,CAAC;AAC1E,aAAK,eAAe,QAAQ,UAAQ,OAAO,IAAI,MAAM,IAAI,CAAC;AAAA,MAC5D,CAAC;AAAA,IACH,OAAO;AACL,aAAO,KAAK,4BAA4B,CAAC;AACzC,qBAAe,CAAC;AAAA,IAClB;AACA,QAAI,OAAO,QAAQ;AACjB,YAAM,sBAAsB,MAAM;AAAA,IACpC;AACA,kBAAc,QAAQ,CAAC,QAAQC,aAAY;AACzC,aAAO,QAAQ,CAAC,GAAG,SAAS;AAC1B,eAAO,IAAI,MAAM,KAAK,QAAQ,aAAaA,UAAS,MAAM,UAAU,CAAC;AAAA,MACvE,CAAC;AAAA,IACH,CAAC;AACD,UAAM,UAAU,aAAa,IAAI,OAAK;AACpC,YAAM,SAAS,cAAc,IAAI,EAAE,OAAO;AAC1C,aAAO,KAAK,aAAa,GAAG,oBAAI,IAAI,GAAG,MAAM;AAAA,IAC/C,CAAC;AACD,UAAM,SAAS,oBAAoB,OAAO;AAC1C,SAAK,aAAa,IAAI,IAAI,MAAM;AAChC,WAAO,UAAU,MAAM,KAAK,QAAQ,EAAE,CAAC;AACvC,SAAK,QAAQ,KAAK,MAAM;AACxB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,UAAM,SAAS,KAAK,WAAW,EAAE;AACjC,WAAO,QAAQ;AACf,SAAK,aAAa,OAAO,EAAE;AAC3B,UAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACzC,QAAI,SAAS,GAAG;AACd,WAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,WAAW,IAAI;AACb,UAAM,SAAS,KAAK,aAAa,IAAI,EAAE;AACvC,QAAI,CAAC,QAAQ;AACX,YAAM,cAAc,EAAE;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,IAAI,SAAS,WAAW,UAAU;AAEvC,UAAM,YAAY,mBAAmB,SAAS,IAAI,IAAI,EAAE;AACxD,mBAAe,KAAK,WAAW,EAAE,GAAG,WAAW,WAAW,QAAQ;AAClE,WAAO,MAAM;AAAA,IAAC;AAAA,EAChB;AAAA,EACA,QAAQ,IAAI,SAAS,SAAS,MAAM;AAClC,QAAI,WAAW,YAAY;AACzB,WAAK,SAAS,IAAI,KAAK,CAAC,CAAC;AACzB;AAAA,IACF;AACA,QAAI,WAAW,UAAU;AACvB,YAAM,UAAU,KAAK,CAAC,KAAK,CAAC;AAC5B,WAAK,OAAO,IAAI,SAAS,OAAO;AAChC;AAAA,IACF;AACA,UAAM,SAAS,KAAK,WAAW,EAAE;AACjC,YAAQ,SAAS;AAAA,MACf,KAAK;AACH,eAAO,KAAK;AACZ;AAAA,MACF,KAAK;AACH,eAAO,MAAM;AACb;AAAA,MACF,KAAK;AACH,eAAO,MAAM;AACb;AAAA,MACF,KAAK;AACH,eAAO,QAAQ;AACf;AAAA,MACF,KAAK;AACH,eAAO,OAAO;AACd;AAAA,MACF,KAAK;AACH,eAAO,KAAK;AACZ;AAAA,MACF,KAAK;AACH,eAAO,YAAY,WAAW,KAAK,CAAC,CAAC,CAAC;AACtC;AAAA,MACF,KAAK;AACH,aAAK,QAAQ,EAAE;AACf;AAAA,IACJ;AAAA,EACF;AACF;AACA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,qBAAqB,CAAC;AAC5B,IAAM,qBAAqB;AAAA,EACzB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,sBAAsB;AACxB;AACA,IAAM,6BAA6B;AAAA,EACjC,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,cAAc;AAAA,EACd,sBAAsB;AACxB;AACA,IAAM,eAAe;AACrB,IAAM,aAAN,MAAiB;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,YAAY,OAAO,cAAc,IAAI;AACnC,SAAK,cAAc;AACnB,UAAM,QAAQ,SAAS,MAAM,eAAe,OAAO;AACnD,UAAM,QAAQ,QAAQ,MAAM,OAAO,IAAI;AACvC,SAAK,QAAQ,sBAAsB,KAAK;AACxC,QAAI,OAAO;AAET,YAGI,YAFF;AAAA,eAAAC;AAAA,MAr2DR,IAu2DU,IADC,oBACD,IADC;AAAA,QADH;AAAA;AAGF,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,UAAU,CAAC;AAAA,IAClB;AACA,QAAI,CAAC,KAAK,QAAQ,QAAQ;AACxB,WAAK,QAAQ,SAAS,CAAC;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,YAAY,QAAQ;AAC1B,QAAI,WAAW;AACb,YAAM,YAAY,KAAK,QAAQ;AAC/B,aAAO,KAAK,SAAS,EAAE,QAAQ,UAAQ;AACrC,YAAI,UAAU,IAAI,KAAK,MAAM;AAC3B,oBAAU,IAAI,IAAI,UAAU,IAAI;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAM,aAAa;AACnB,IAAM,sBAAsB,IAAI,WAAW,UAAU;AACrD,IAAM,+BAAN,MAAmC;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,CAAC;AAAA,EACX,YAAY,oBAAI,IAAI;AAAA,EACpB,SAAS,CAAC;AAAA,EACV,oBAAoB,oBAAI,IAAI;AAAA,EAC5B;AAAA,EACA,YAAY,IAAI,aAAa,SAAS;AACpC,SAAK,KAAK;AACV,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,iBAAiB,YAAY;AAClC,aAAS,aAAa,KAAK,cAAc;AAAA,EAC3C;AAAA,EACA,OAAO,SAAS,MAAM,OAAO,UAAU;AACrC,QAAI,CAAC,KAAK,UAAU,IAAI,IAAI,GAAG;AAC7B,YAAM,eAAe,OAAO,IAAI;AAAA,IAClC;AACA,QAAI,SAAS,QAAQ,MAAM,UAAU,GAAG;AACtC,YAAM,aAAa,IAAI;AAAA,IACzB;AACA,QAAI,CAAC,oBAAoB,KAAK,GAAG;AAC/B,YAAM,wBAAwB,OAAO,IAAI;AAAA,IAC3C;AACA,UAAM,YAAY,qBAAqB,KAAK,mBAAmB,SAAS,CAAC,CAAC;AAC1E,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,cAAU,KAAK,IAAI;AACnB,UAAM,qBAAqB,qBAAqB,KAAK,QAAQ,iBAAiB,SAAS,oBAAI,IAAI,CAAC;AAChG,QAAI,CAAC,mBAAmB,IAAI,IAAI,GAAG;AACjC,eAAS,SAAS,oBAAoB;AACtC,eAAS,SAAS,uBAAuB,MAAM,IAAI;AACnD,yBAAmB,IAAI,MAAM,mBAAmB;AAAA,IAClD;AACA,WAAO,MAAM;AAIX,WAAK,QAAQ,WAAW,MAAM;AAC5B,cAAM,QAAQ,UAAU,QAAQ,IAAI;AACpC,YAAI,SAAS,GAAG;AACd,oBAAU,OAAO,OAAO,CAAC;AAAA,QAC3B;AACA,YAAI,CAAC,KAAK,UAAU,IAAI,IAAI,GAAG;AAC7B,6BAAmB,OAAO,IAAI;AAAA,QAChC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,SAAS,MAAM,KAAK;AAClB,QAAI,KAAK,UAAU,IAAI,IAAI,GAAG;AAE5B,aAAO;AAAA,IACT,OAAO;AACL,WAAK,UAAU,IAAI,MAAM,GAAG;AAC5B,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,YAAY,MAAM;AAChB,UAAM,UAAU,KAAK,UAAU,IAAI,IAAI;AACvC,QAAI,CAAC,SAAS;AACZ,YAAM,oBAAoB,IAAI;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,aAAa,OAAO,oBAAoB,MAAM;AAC7D,UAAM,UAAU,KAAK,YAAY,WAAW;AAC5C,UAAM,SAAS,IAAI,0BAA0B,KAAK,IAAI,aAAa,OAAO;AAC1E,QAAI,qBAAqB,KAAK,QAAQ,gBAAgB,IAAI,OAAO;AACjE,QAAI,CAAC,oBAAoB;AACvB,eAAS,SAAS,oBAAoB;AACtC,eAAS,SAAS,uBAAuB,MAAM,WAAW;AAC1D,WAAK,QAAQ,gBAAgB,IAAI,SAAS,qBAAqB,oBAAI,IAAI,CAAC;AAAA,IAC1E;AACA,QAAI,YAAY,mBAAmB,IAAI,WAAW;AAClD,UAAM,UAAU,IAAI,WAAW,OAAO,KAAK,EAAE;AAC7C,UAAM,QAAQ,SAAS,MAAM,eAAe,OAAO;AACnD,QAAI,CAAC,SAAS,WAAW;AACvB,cAAQ,cAAc,UAAU,OAAO;AAAA,IACzC;AACA,uBAAmB,IAAI,aAAa,OAAO;AAC3C,QAAI,CAAC,WAAW;AACd,kBAAY;AAAA,IACd;AACA,UAAM,YAAY,QAAQ,UAAU;AAOpC,QAAI,CAAC,aAAa,UAAU,UAAU,QAAQ,OAAO;AAGnD,UAAI,CAAC,UAAU,UAAU,QAAQ,QAAQ,MAAM,GAAG;AAChD,cAAM,SAAS,CAAC;AAChB,cAAM,aAAa,QAAQ,YAAY,UAAU,OAAO,UAAU,QAAQ,MAAM;AAChF,cAAM,WAAW,QAAQ,YAAY,QAAQ,OAAO,QAAQ,QAAQ,MAAM;AAC1E,YAAI,OAAO,QAAQ;AACjB,eAAK,QAAQ,YAAY,MAAM;AAAA,QACjC,OAAO;AACL,eAAK,QAAQ,WAAW,MAAM;AAC5B,wBAAY,SAAS,UAAU;AAC/B,sBAAU,SAAS,QAAQ;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF;AACA;AAAA,IACF;AACA,UAAM,mBAAmB,qBAAqB,KAAK,QAAQ,kBAAkB,SAAS,CAAC,CAAC;AACxF,qBAAiB,QAAQ,CAAAC,YAAU;AAKjC,UAAIA,QAAO,eAAe,KAAK,MAAMA,QAAO,eAAe,eAAeA,QAAO,QAAQ;AACvF,QAAAA,QAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AACD,QAAI,aAAa,QAAQ,gBAAgB,UAAU,OAAO,QAAQ,OAAO,SAAS,QAAQ,MAAM;AAChG,QAAI,uBAAuB;AAC3B,QAAI,CAAC,YAAY;AACf,UAAI,CAAC,kBAAmB;AACxB,mBAAa,QAAQ;AACrB,6BAAuB;AAAA,IACzB;AACA,SAAK,QAAQ;AACb,SAAK,OAAO,KAAK;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,CAAC,sBAAsB;AACzB,eAAS,SAAS,gBAAgB;AAClC,aAAO,QAAQ,MAAM;AACnB,oBAAY,SAAS,gBAAgB;AAAA,MACvC,CAAC;AAAA,IACH;AACA,WAAO,OAAO,MAAM;AAClB,UAAI,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACvC,UAAI,SAAS,GAAG;AACd,aAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,MAC9B;AACA,YAAM,UAAU,KAAK,QAAQ,iBAAiB,IAAI,OAAO;AACzD,UAAI,SAAS;AACX,YAAIC,SAAQ,QAAQ,QAAQ,MAAM;AAClC,YAAIA,UAAS,GAAG;AACd,kBAAQ,OAAOA,QAAO,CAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,QAAQ,KAAK,MAAM;AACxB,qBAAiB,KAAK,MAAM;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM;AACf,SAAK,UAAU,OAAO,IAAI;AAC1B,SAAK,QAAQ,gBAAgB,QAAQ,cAAY,SAAS,OAAO,IAAI,CAAC;AACtE,SAAK,kBAAkB,QAAQ,CAAC,WAAW,YAAY;AACrD,WAAK,kBAAkB,IAAI,SAAS,UAAU,OAAO,WAAS;AAC5D,eAAO,MAAM,QAAQ;AAAA,MACvB,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,SAAS;AACzB,SAAK,QAAQ,gBAAgB,OAAO,OAAO;AAC3C,SAAK,kBAAkB,OAAO,OAAO;AACrC,UAAM,iBAAiB,KAAK,QAAQ,iBAAiB,IAAI,OAAO;AAChE,QAAI,gBAAgB;AAClB,qBAAe,QAAQ,YAAU,OAAO,QAAQ,CAAC;AACjD,WAAK,QAAQ,iBAAiB,OAAO,OAAO;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,+BAA+B,aAAa,SAAS;AACnD,UAAM,WAAW,KAAK,QAAQ,OAAO,MAAM,aAAa,qBAAqB,IAAI;AAIjF,aAAS,QAAQ,SAAO;AAGtB,UAAI,IAAI,YAAY,EAAG;AACvB,YAAM,aAAa,KAAK,QAAQ,yBAAyB,GAAG;AAC5D,UAAI,WAAW,MAAM;AACnB,mBAAW,QAAQ,QAAM,GAAG,sBAAsB,KAAK,SAAS,OAAO,IAAI,CAAC;AAAA,MAC9E,OAAO;AACL,aAAK,kBAAkB,GAAG;AAAA,MAC5B;AAAA,IACF,CAAC;AAGD,SAAK,QAAQ,yBAAyB,MAAM,SAAS,QAAQ,SAAO,KAAK,kBAAkB,GAAG,CAAC,CAAC;AAAA,EAClG;AAAA,EACA,sBAAsB,SAAS,SAAS,sBAAsB,mBAAmB;AAC/E,UAAM,gBAAgB,KAAK,QAAQ,gBAAgB,IAAI,OAAO;AAC9D,UAAM,yBAAyB,oBAAI,IAAI;AACvC,QAAI,eAAe;AACjB,YAAM,UAAU,CAAC;AACjB,oBAAc,QAAQ,CAAC,OAAO,gBAAgB;AAC5C,+BAAuB,IAAI,aAAa,MAAM,KAAK;AAGnD,YAAI,KAAK,UAAU,IAAI,WAAW,GAAG;AACnC,gBAAM,SAAS,KAAK,QAAQ,SAAS,aAAa,YAAY,iBAAiB;AAC/E,cAAI,QAAQ;AACV,oBAAQ,KAAK,MAAM;AAAA,UACrB;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,QAAQ,QAAQ;AAClB,aAAK,QAAQ,qBAAqB,KAAK,IAAI,SAAS,MAAM,SAAS,sBAAsB;AACzF,YAAI,sBAAsB;AACxB,8BAAoB,OAAO,EAAE,OAAO,MAAM,KAAK,QAAQ,iBAAiB,OAAO,CAAC;AAAA,QAClF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,+BAA+B,SAAS;AACtC,UAAM,YAAY,KAAK,kBAAkB,IAAI,OAAO;AACpD,UAAM,gBAAgB,KAAK,QAAQ,gBAAgB,IAAI,OAAO;AAG9D,QAAI,aAAa,eAAe;AAC9B,YAAM,kBAAkB,oBAAI,IAAI;AAChC,gBAAU,QAAQ,cAAY;AAC5B,cAAM,cAAc,SAAS;AAC7B,YAAI,gBAAgB,IAAI,WAAW,EAAG;AACtC,wBAAgB,IAAI,WAAW;AAC/B,cAAM,UAAU,KAAK,UAAU,IAAI,WAAW;AAC9C,cAAM,aAAa,QAAQ;AAC3B,cAAM,YAAY,cAAc,IAAI,WAAW,KAAK;AACpD,cAAM,UAAU,IAAI,WAAW,UAAU;AACzC,cAAM,SAAS,IAAI,0BAA0B,KAAK,IAAI,aAAa,OAAO;AAC1E,aAAK,QAAQ;AACb,aAAK,OAAO,KAAK;AAAA,UACf;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,sBAAsB;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW,SAAS,SAAS;AAC3B,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ,mBAAmB;AAC7B,WAAK,+BAA+B,SAAS,OAAO;AAAA,IACtD;AAEA,QAAI,KAAK,sBAAsB,SAAS,SAAS,IAAI,EAAG;AAGxD,QAAI,oCAAoC;AACxC,QAAI,OAAO,iBAAiB;AAC1B,YAAM,iBAAiB,OAAO,QAAQ,SAAS,OAAO,wBAAwB,IAAI,OAAO,IAAI,CAAC;AAK9F,UAAI,kBAAkB,eAAe,QAAQ;AAC3C,4CAAoC;AAAA,MACtC,OAAO;AACL,YAAI,SAAS;AACb,eAAO,SAAS,OAAO,YAAY;AACjC,gBAAM,WAAW,OAAO,gBAAgB,IAAI,MAAM;AAClD,cAAI,UAAU;AACZ,gDAAoC;AACpC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAKA,SAAK,+BAA+B,OAAO;AAG3C,QAAI,mCAAmC;AACrC,aAAO,qBAAqB,KAAK,IAAI,SAAS,OAAO,OAAO;AAAA,IAC9D,OAAO;AACL,YAAM,cAAc,QAAQ,YAAY;AACxC,UAAI,CAAC,eAAe,gBAAgB,oBAAoB;AAGtD,eAAO,WAAW,MAAM,KAAK,kBAAkB,OAAO,CAAC;AACvD,eAAO,uBAAuB,OAAO;AACrC,eAAO,mBAAmB,SAAS,OAAO;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,SAAS,QAAQ;AAC1B,aAAS,SAAS,KAAK,cAAc;AAAA,EACvC;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,eAAe,CAAC;AACtB,SAAK,OAAO,QAAQ,WAAS;AAC3B,YAAM,SAAS,MAAM;AACrB,UAAI,OAAO,UAAW;AACtB,YAAM,UAAU,MAAM;AACtB,YAAM,YAAY,KAAK,kBAAkB,IAAI,OAAO;AACpD,UAAI,WAAW;AACb,kBAAU,QAAQ,cAAY;AAC5B,cAAI,SAAS,QAAQ,MAAM,aAAa;AACtC,kBAAM,YAAY,mBAAmB,SAAS,MAAM,aAAa,MAAM,UAAU,OAAO,MAAM,QAAQ,KAAK;AAC3G,sBAAU,OAAO,IAAI;AACrB,2BAAe,MAAM,QAAQ,SAAS,OAAO,WAAW,SAAS,QAAQ;AAAA,UAC3E;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,OAAO,kBAAkB;AAC3B,aAAK,QAAQ,WAAW,MAAM;AAG5B,iBAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,qBAAa,KAAK,KAAK;AAAA,MACzB;AAAA,IACF,CAAC;AACD,SAAK,SAAS,CAAC;AACf,WAAO,aAAa,KAAK,CAAC,GAAG,MAAM;AAGjC,YAAM,KAAK,EAAE,WAAW,IAAI;AAC5B,YAAM,KAAK,EAAE,WAAW,IAAI;AAC5B,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,eAAO,KAAK;AAAA,MACd;AACA,aAAO,KAAK,QAAQ,OAAO,gBAAgB,EAAE,SAAS,EAAE,OAAO,IAAI,IAAI;AAAA,IACzE,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,SAAS;AACf,SAAK,QAAQ,QAAQ,OAAK,EAAE,QAAQ,CAAC;AACrC,SAAK,+BAA+B,KAAK,aAAa,OAAO;AAAA,EAC/D;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,CAAC;AAAA,EACX,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,mBAAmB,oBAAI,IAAI;AAAA,EAC3B,0BAA0B,oBAAI,IAAI;AAAA,EAClC,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,gBAAgB,oBAAI,IAAI;AAAA,EACxB,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,mBAAmB,CAAC;AAAA,EACpB,iBAAiB,CAAC;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,gBAAgB,CAAC;AAAA,EACjB,0BAA0B,oBAAI,IAAI;AAAA,EAClC,yBAAyB,CAAC;AAAA,EAC1B,yBAAyB,CAAC;AAAA;AAAA,EAE1B,oBAAoB,CAAC,SAAS,YAAY;AAAA,EAAC;AAAA;AAAA,EAE3C,mBAAmB,SAAS,SAAS;AACnC,SAAK,kBAAkB,SAAS,OAAO;AAAA,EACzC;AAAA,EACA,YAAY,UAAU,QAAQ,aAAa;AACzC,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,IAAI,gBAAgB;AAClB,UAAM,UAAU,CAAC;AACjB,SAAK,eAAe,QAAQ,QAAM;AAChC,SAAG,QAAQ,QAAQ,YAAU;AAC3B,YAAI,OAAO,QAAQ;AACjB,kBAAQ,KAAK,MAAM;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,aAAa,aAAa;AACxC,UAAM,KAAK,IAAI,6BAA6B,aAAa,aAAa,IAAI;AAC1E,QAAI,KAAK,YAAY,KAAK,OAAO,gBAAgB,KAAK,UAAU,WAAW,GAAG;AAC5E,WAAK,sBAAsB,IAAI,WAAW;AAAA,IAC5C,OAAO;AAIL,WAAK,gBAAgB,IAAI,aAAa,EAAE;AAMxC,WAAK,oBAAoB,WAAW;AAAA,IACtC;AACA,WAAO,KAAK,iBAAiB,WAAW,IAAI;AAAA,EAC9C;AAAA,EACA,sBAAsB,IAAI,aAAa;AACrC,UAAM,gBAAgB,KAAK;AAC3B,UAAM,0BAA0B,KAAK;AACrC,UAAM,QAAQ,cAAc,SAAS;AACrC,QAAI,SAAS,GAAG;AACd,UAAI,QAAQ;AAGZ,UAAI,WAAW,KAAK,OAAO,iBAAiB,WAAW;AACvD,aAAO,UAAU;AACf,cAAM,aAAa,wBAAwB,IAAI,QAAQ;AACvD,YAAI,YAAY;AAGd,gBAAM,QAAQ,cAAc,QAAQ,UAAU;AAC9C,wBAAc,OAAO,QAAQ,GAAG,GAAG,EAAE;AACrC,kBAAQ;AACR;AAAA,QACF;AACA,mBAAW,KAAK,OAAO,iBAAiB,QAAQ;AAAA,MAClD;AACA,UAAI,CAAC,OAAO;AAIV,sBAAc,QAAQ,EAAE;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,oBAAc,KAAK,EAAE;AAAA,IACvB;AACA,4BAAwB,IAAI,aAAa,EAAE;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,SAAS,aAAa,aAAa;AACjC,QAAI,KAAK,KAAK,iBAAiB,WAAW;AAC1C,QAAI,CAAC,IAAI;AACP,WAAK,KAAK,gBAAgB,aAAa,WAAW;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,aAAa,MAAM,SAAS;AAC1C,QAAI,KAAK,KAAK,iBAAiB,WAAW;AAC1C,QAAI,MAAM,GAAG,SAAS,MAAM,OAAO,GAAG;AACpC,WAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,QAAQ,aAAa,SAAS;AAC5B,QAAI,CAAC,YAAa;AAClB,SAAK,WAAW,MAAM;AAAA,IAAC,CAAC;AACxB,SAAK,yBAAyB,MAAM;AAClC,YAAM,KAAK,KAAK,gBAAgB,WAAW;AAC3C,WAAK,wBAAwB,OAAO,GAAG,WAAW;AAClD,YAAM,QAAQ,KAAK,eAAe,QAAQ,EAAE;AAC5C,UAAI,SAAS,GAAG;AACd,aAAK,eAAe,OAAO,OAAO,CAAC;AAAA,MACrC;AACA,SAAG,QAAQ,OAAO;AAClB,aAAO,KAAK,iBAAiB,WAAW;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,IAAI;AAClB,WAAO,KAAK,iBAAiB,EAAE;AAAA,EACjC;AAAA,EACA,yBAAyB,SAAS;AAMhC,UAAM,aAAa,oBAAI,IAAI;AAC3B,UAAM,gBAAgB,KAAK,gBAAgB,IAAI,OAAO;AACtD,QAAI,eAAe;AACjB,eAAS,cAAc,cAAc,OAAO,GAAG;AAC7C,YAAI,WAAW,aAAa;AAC1B,gBAAM,KAAK,KAAK,gBAAgB,WAAW,WAAW;AACtD,cAAI,IAAI;AACN,uBAAW,IAAI,EAAE;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,aAAa,SAAS,MAAM,OAAO;AACzC,QAAI,cAAc,OAAO,GAAG;AAC1B,YAAM,KAAK,KAAK,gBAAgB,WAAW;AAC3C,UAAI,IAAI;AACN,WAAG,QAAQ,SAAS,MAAM,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,aAAa,SAAS,QAAQ,cAAc;AACrD,QAAI,CAAC,cAAc,OAAO,EAAG;AAG7B,UAAM,UAAU,QAAQ,YAAY;AACpC,QAAI,WAAW,QAAQ,eAAe;AACpC,cAAQ,gBAAgB;AACxB,cAAQ,aAAa;AACrB,YAAM,QAAQ,KAAK,uBAAuB,QAAQ,OAAO;AACzD,UAAI,SAAS,GAAG;AACd,aAAK,uBAAuB,OAAO,OAAO,CAAC;AAAA,MAC7C;AAAA,IACF;AAIA,QAAI,aAAa;AACf,YAAM,KAAK,KAAK,gBAAgB,WAAW;AAO3C,UAAI,IAAI;AACN,WAAG,WAAW,SAAS,MAAM;AAAA,MAC/B;AAAA,IACF;AAEA,QAAI,cAAc;AAChB,WAAK,oBAAoB,OAAO;AAAA,IAClC;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,SAAK,uBAAuB,KAAK,OAAO;AAAA,EAC1C;AAAA,EACA,sBAAsB,SAAS,OAAO;AACpC,QAAI,OAAO;AACT,UAAI,CAAC,KAAK,cAAc,IAAI,OAAO,GAAG;AACpC,aAAK,cAAc,IAAI,OAAO;AAC9B,iBAAS,SAAS,kBAAkB;AAAA,MACtC;AAAA,IACF,WAAW,KAAK,cAAc,IAAI,OAAO,GAAG;AAC1C,WAAK,cAAc,OAAO,OAAO;AACjC,kBAAY,SAAS,kBAAkB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,WAAW,aAAa,SAAS,SAAS;AACxC,QAAI,cAAc,OAAO,GAAG;AAC1B,YAAM,KAAK,cAAc,KAAK,gBAAgB,WAAW,IAAI;AAC7D,UAAI,IAAI;AACN,WAAG,WAAW,SAAS,OAAO;AAAA,MAChC,OAAO;AACL,aAAK,qBAAqB,aAAa,SAAS,OAAO,OAAO;AAAA,MAChE;AACA,YAAM,SAAS,KAAK,wBAAwB,IAAI,OAAO;AACvD,UAAI,UAAU,OAAO,OAAO,aAAa;AACvC,eAAO,WAAW,SAAS,OAAO;AAAA,MACpC;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,SAAS,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,qBAAqB,aAAa,SAAS,cAAc,SAAS,wBAAwB;AACxF,SAAK,uBAAuB,KAAK,OAAO;AACxC,YAAQ,YAAY,IAAI;AAAA,MACtB;AAAA,MACA,eAAe;AAAA,MACf;AAAA,MACA,sBAAsB;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,aAAa,SAAS,MAAM,OAAO,UAAU;AAClD,QAAI,cAAc,OAAO,GAAG;AAC1B,aAAO,KAAK,gBAAgB,WAAW,EAAE,OAAO,SAAS,MAAM,OAAO,QAAQ;AAAA,IAChF;AACA,WAAO,MAAM;AAAA,IAAC;AAAA,EAChB;AAAA,EACA,kBAAkB,OAAO,cAAc,gBAAgB,gBAAgB,cAAc;AACnF,WAAO,MAAM,WAAW,MAAM,KAAK,QAAQ,MAAM,SAAS,MAAM,UAAU,OAAO,MAAM,QAAQ,OAAO,gBAAgB,gBAAgB,MAAM,UAAU,SAAS,MAAM,QAAQ,SAAS,cAAc,YAAY;AAAA,EAClN;AAAA,EACA,uBAAuB,kBAAkB;AACvC,QAAI,WAAW,KAAK,OAAO,MAAM,kBAAkB,qBAAqB,IAAI;AAC5E,aAAS,QAAQ,aAAW,KAAK,kCAAkC,OAAO,CAAC;AAC3E,QAAI,KAAK,wBAAwB,QAAQ,EAAG;AAC5C,eAAW,KAAK,OAAO,MAAM,kBAAkB,uBAAuB,IAAI;AAC1E,aAAS,QAAQ,aAAW,KAAK,sCAAsC,OAAO,CAAC;AAAA,EACjF;AAAA,EACA,kCAAkC,SAAS;AACzC,UAAM,UAAU,KAAK,iBAAiB,IAAI,OAAO;AACjD,QAAI,SAAS;AACX,cAAQ,QAAQ,YAAU;AAIxB,YAAI,OAAO,QAAQ;AACjB,iBAAO,mBAAmB;AAAA,QAC5B,OAAO;AACL,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,sCAAsC,SAAS;AAC7C,UAAM,UAAU,KAAK,wBAAwB,IAAI,OAAO;AACxD,QAAI,SAAS;AACX,cAAQ,QAAQ,YAAU,OAAO,OAAO,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,IAAI,QAAQ,aAAW;AAC5B,UAAI,KAAK,QAAQ,QAAQ;AACvB,eAAO,oBAAoB,KAAK,OAAO,EAAE,OAAO,MAAM,QAAQ,CAAC;AAAA,MACjE,OAAO;AACL,gBAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,SAAS;AACxB,UAAM,UAAU,QAAQ,YAAY;AACpC,QAAI,WAAW,QAAQ,eAAe;AAEpC,cAAQ,YAAY,IAAI;AACxB,UAAI,QAAQ,aAAa;AACvB,aAAK,uBAAuB,OAAO;AACnC,cAAM,KAAK,KAAK,gBAAgB,QAAQ,WAAW;AACnD,YAAI,IAAI;AACN,aAAG,kBAAkB,OAAO;AAAA,QAC9B;AAAA,MACF;AACA,WAAK,mBAAmB,SAAS,QAAQ,aAAa;AAAA,IACxD;AACA,QAAI,QAAQ,WAAW,SAAS,kBAAkB,GAAG;AACnD,WAAK,sBAAsB,SAAS,KAAK;AAAA,IAC3C;AACA,SAAK,OAAO,MAAM,SAAS,mBAAmB,IAAI,EAAE,QAAQ,UAAQ;AAClE,WAAK,sBAAsB,MAAM,KAAK;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,MAAM,cAAc,IAAI;AACtB,QAAI,UAAU,CAAC;AACf,QAAI,KAAK,gBAAgB,MAAM;AAC7B,WAAK,gBAAgB,QAAQ,CAAC,IAAI,YAAY,KAAK,sBAAsB,IAAI,OAAO,CAAC;AACrF,WAAK,gBAAgB,MAAM;AAAA,IAC7B;AACA,QAAI,KAAK,mBAAmB,KAAK,uBAAuB,QAAQ;AAC9D,eAAS,IAAI,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AAC3D,cAAM,MAAM,KAAK,uBAAuB,CAAC;AACzC,iBAAS,KAAK,cAAc;AAAA,MAC9B;AAAA,IACF;AACA,QAAI,KAAK,eAAe,WAAW,KAAK,sBAAsB,KAAK,uBAAuB,SAAS;AACjG,YAAM,aAAa,CAAC;AACpB,UAAI;AACF,kBAAU,KAAK,iBAAiB,YAAY,WAAW;AAAA,MACzD,UAAE;AACA,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,qBAAW,CAAC,EAAE;AAAA,QAChB;AAAA,MACF;AAAA,IACF,OAAO;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,uBAAuB,QAAQ,KAAK;AAC3D,cAAM,UAAU,KAAK,uBAAuB,CAAC;AAC7C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AACA,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB,SAAS;AACrC,SAAK,uBAAuB,SAAS;AACrC,SAAK,UAAU,QAAQ,QAAM,GAAG,CAAC;AACjC,SAAK,YAAY,CAAC;AAClB,QAAI,KAAK,cAAc,QAAQ;AAI7B,YAAM,WAAW,KAAK;AACtB,WAAK,gBAAgB,CAAC;AACtB,UAAI,QAAQ,QAAQ;AAClB,4BAAoB,OAAO,EAAE,OAAO,MAAM;AACxC,mBAAS,QAAQ,QAAM,GAAG,CAAC;AAAA,QAC7B,CAAC;AAAA,MACH,OAAO;AACL,iBAAS,QAAQ,QAAM,GAAG,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM,yBAAyB,MAAM;AAAA,EACvC;AAAA,EACA,iBAAiB,YAAY,aAAa;AACxC,UAAM,eAAe,IAAI,sBAAsB;AAC/C,UAAM,iBAAiB,CAAC;AACxB,UAAM,oBAAoB,oBAAI,IAAI;AAClC,UAAM,qBAAqB,CAAC;AAC5B,UAAM,kBAAkB,oBAAI,IAAI;AAChC,UAAM,sBAAsB,oBAAI,IAAI;AACpC,UAAM,uBAAuB,oBAAI,IAAI;AACrC,UAAM,sBAAsB,oBAAI,IAAI;AACpC,SAAK,cAAc,QAAQ,UAAQ;AACjC,0BAAoB,IAAI,IAAI;AAC5B,YAAM,uBAAuB,KAAK,OAAO,MAAM,MAAM,iBAAiB,IAAI;AAC1E,eAASC,KAAI,GAAGA,KAAI,qBAAqB,QAAQA,MAAK;AACpD,4BAAoB,IAAI,qBAAqBA,EAAC,CAAC;AAAA,MACjD;AAAA,IACF,CAAC;AACD,UAAM,WAAW,KAAK;AACtB,UAAM,qBAAqB,MAAM,KAAK,KAAK,gBAAgB,KAAK,CAAC;AACjE,UAAM,eAAe,aAAa,oBAAoB,KAAK,sBAAsB;AAIjF,UAAM,kBAAkB,oBAAI,IAAI;AAChC,QAAI,IAAI;AACR,iBAAa,QAAQ,CAAC,OAAO,SAAS;AACpC,YAAM,YAAY,kBAAkB;AACpC,sBAAgB,IAAI,MAAM,SAAS;AACnC,YAAM,QAAQ,UAAQ,SAAS,MAAM,SAAS,CAAC;AAAA,IACjD,CAAC;AACD,UAAM,gBAAgB,CAAC;AACvB,UAAM,mBAAmB,oBAAI,IAAI;AACjC,UAAM,8BAA8B,oBAAI,IAAI;AAC5C,aAASA,KAAI,GAAGA,KAAI,KAAK,uBAAuB,QAAQA,MAAK;AAC3D,YAAM,UAAU,KAAK,uBAAuBA,EAAC;AAC7C,YAAM,UAAU,QAAQ,YAAY;AACpC,UAAI,WAAW,QAAQ,eAAe;AACpC,sBAAc,KAAK,OAAO;AAC1B,yBAAiB,IAAI,OAAO;AAC5B,YAAI,QAAQ,cAAc;AACxB,eAAK,OAAO,MAAM,SAAS,eAAe,IAAI,EAAE,QAAQ,SAAO,iBAAiB,IAAI,GAAG,CAAC;AAAA,QAC1F,OAAO;AACL,sCAA4B,IAAI,OAAO;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AACA,UAAM,kBAAkB,oBAAI,IAAI;AAChC,UAAM,eAAe,aAAa,oBAAoB,MAAM,KAAK,gBAAgB,CAAC;AAClF,iBAAa,QAAQ,CAAC,OAAO,SAAS;AACpC,YAAM,YAAY,kBAAkB;AACpC,sBAAgB,IAAI,MAAM,SAAS;AACnC,YAAM,QAAQ,UAAQ,SAAS,MAAM,SAAS,CAAC;AAAA,IACjD,CAAC;AACD,eAAW,KAAK,MAAM;AACpB,mBAAa,QAAQ,CAAC,OAAO,SAAS;AACpC,cAAM,YAAY,gBAAgB,IAAI,IAAI;AAC1C,cAAM,QAAQ,UAAQ,YAAY,MAAM,SAAS,CAAC;AAAA,MACpD,CAAC;AACD,mBAAa,QAAQ,CAAC,OAAO,SAAS;AACpC,cAAM,YAAY,gBAAgB,IAAI,IAAI;AAC1C,cAAM,QAAQ,UAAQ,YAAY,MAAM,SAAS,CAAC;AAAA,MACpD,CAAC;AACD,oBAAc,QAAQ,aAAW;AAC/B,aAAK,iBAAiB,OAAO;AAAA,MAC/B,CAAC;AAAA,IACH,CAAC;AACD,UAAM,aAAa,CAAC;AACpB,UAAM,uBAAuB,CAAC;AAC9B,aAASA,KAAI,KAAK,eAAe,SAAS,GAAGA,MAAK,GAAGA,MAAK;AACxD,YAAM,KAAK,KAAK,eAAeA,EAAC;AAChC,SAAG,uBAAuB,WAAW,EAAE,QAAQ,WAAS;AACtD,cAAM,SAAS,MAAM;AACrB,cAAM,UAAU,MAAM;AACtB,mBAAW,KAAK,MAAM;AACtB,YAAI,KAAK,uBAAuB,QAAQ;AACtC,gBAAM,UAAU,QAAQ,YAAY;AAGpC,cAAI,WAAW,QAAQ,YAAY;AACjC,gBAAI,QAAQ,0BAA0B,QAAQ,uBAAuB,IAAI,MAAM,WAAW,GAAG;AAC3F,oBAAM,gBAAgB,QAAQ,uBAAuB,IAAI,MAAM,WAAW;AAG1E,oBAAM,qBAAqB,KAAK,gBAAgB,IAAI,MAAM,OAAO;AACjE,kBAAI,sBAAsB,mBAAmB,IAAI,MAAM,WAAW,GAAG;AACnE,sBAAM,QAAQ,mBAAmB,IAAI,MAAM,WAAW;AACtD,sBAAM,QAAQ;AACd,mCAAmB,IAAI,MAAM,aAAa,KAAK;AAAA,cACjD;AAAA,YACF;AACA,mBAAO,QAAQ;AACf;AAAA,UACF;AAAA,QACF;AACA,cAAM,iBAAiB,CAAC,YAAY,CAAC,KAAK,OAAO,gBAAgB,UAAU,OAAO;AAClF,cAAM,iBAAiB,gBAAgB,IAAI,OAAO;AAClD,cAAM,iBAAiB,gBAAgB,IAAI,OAAO;AAClD,cAAM,cAAc,KAAK,kBAAkB,OAAO,cAAc,gBAAgB,gBAAgB,cAAc;AAC9G,YAAI,YAAY,UAAU,YAAY,OAAO,QAAQ;AACnD,+BAAqB,KAAK,WAAW;AACrC;AAAA,QACF;AAKA,YAAI,gBAAgB;AAClB,iBAAO,QAAQ,MAAM,YAAY,SAAS,YAAY,UAAU,CAAC;AACjE,iBAAO,UAAU,MAAM,UAAU,SAAS,YAAY,QAAQ,CAAC;AAC/D,yBAAe,KAAK,MAAM;AAC1B;AAAA,QACF;AAIA,YAAI,MAAM,sBAAsB;AAC9B,iBAAO,QAAQ,MAAM,YAAY,SAAS,YAAY,UAAU,CAAC;AACjE,iBAAO,UAAU,MAAM,UAAU,SAAS,YAAY,QAAQ,CAAC;AAC/D,yBAAe,KAAK,MAAM;AAC1B;AAAA,QACF;AAMA,cAAM,YAAY,CAAC;AACnB,oBAAY,UAAU,QAAQ,QAAM;AAClC,aAAG,0BAA0B;AAC7B,cAAI,CAAC,KAAK,cAAc,IAAI,GAAG,OAAO,GAAG;AACvC,sBAAU,KAAK,EAAE;AAAA,UACnB;AAAA,QACF,CAAC;AACD,oBAAY,YAAY;AACxB,qBAAa,OAAO,SAAS,YAAY,SAAS;AAClD,cAAM,QAAQ;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,2BAAmB,KAAK,KAAK;AAC7B,oBAAY,gBAAgB,QAAQ,CAAAJ,aAAW,qBAAqB,iBAAiBA,UAAS,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;AAC9G,oBAAY,cAAc,QAAQ,CAAC,WAAWA,aAAY;AACxD,cAAI,UAAU,MAAM;AAClB,gBAAI,SAAS,oBAAoB,IAAIA,QAAO;AAC5C,gBAAI,CAAC,QAAQ;AACX,kCAAoB,IAAIA,UAAS,SAAS,oBAAI,IAAI,CAAC;AAAA,YACrD;AACA,sBAAU,QAAQ,CAAC,GAAG,SAAS,OAAO,IAAI,IAAI,CAAC;AAAA,UACjD;AAAA,QACF,CAAC;AACD,oBAAY,eAAe,QAAQ,CAAC,WAAWA,aAAY;AACzD,cAAI,SAAS,qBAAqB,IAAIA,QAAO;AAC7C,cAAI,CAAC,QAAQ;AACX,iCAAqB,IAAIA,UAAS,SAAS,oBAAI,IAAI,CAAC;AAAA,UACtD;AACA,oBAAU,QAAQ,CAAC,GAAG,SAAS,OAAO,IAAI,IAAI,CAAC;AAAA,QACjD,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,qBAAqB,QAAQ;AAC/B,YAAM,SAAS,CAAC;AAChB,2BAAqB,QAAQ,iBAAe;AAC1C,eAAO,KAAK,iBAAiB,YAAY,aAAa,YAAY,MAAM,CAAC;AAAA,MAC3E,CAAC;AACD,iBAAW,QAAQ,YAAU,OAAO,QAAQ,CAAC;AAC7C,WAAK,YAAY,MAAM;AAAA,IACzB;AACA,UAAM,wBAAwB,oBAAI,IAAI;AAKtC,UAAM,sBAAsB,oBAAI,IAAI;AACpC,uBAAmB,QAAQ,WAAS;AAClC,YAAM,UAAU,MAAM;AACtB,UAAI,aAAa,IAAI,OAAO,GAAG;AAC7B,4BAAoB,IAAI,SAAS,OAAO;AACxC,aAAK,sBAAsB,MAAM,OAAO,aAAa,MAAM,aAAa,qBAAqB;AAAA,MAC/F;AAAA,IACF,CAAC;AACD,mBAAe,QAAQ,YAAU;AAC/B,YAAM,UAAU,OAAO;AACvB,YAAM,kBAAkB,KAAK,oBAAoB,SAAS,OAAO,OAAO,aAAa,OAAO,aAAa,IAAI;AAC7G,sBAAgB,QAAQ,gBAAc;AACpC,6BAAqB,uBAAuB,SAAS,CAAC,CAAC,EAAE,KAAK,UAAU;AACxE,mBAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AAQD,UAAM,eAAe,cAAc,OAAO,UAAQ;AAChD,aAAO,uBAAuB,MAAM,qBAAqB,oBAAoB;AAAA,IAC/E,CAAC;AAED,UAAM,gBAAgB,oBAAI,IAAI;AAC9B,UAAM,uBAAuB,sBAAsB,eAAe,KAAK,QAAQ,6BAA6B,sBAAsB,UAAU;AAC5I,yBAAqB,QAAQ,UAAQ;AACnC,UAAI,uBAAuB,MAAM,qBAAqB,oBAAoB,GAAG;AAC3E,qBAAa,KAAK,IAAI;AAAA,MACxB;AAAA,IACF,CAAC;AAED,UAAM,eAAe,oBAAI,IAAI;AAC7B,iBAAa,QAAQ,CAAC,OAAO,SAAS;AACpC,4BAAsB,cAAc,KAAK,QAAQ,IAAI,IAAI,KAAK,GAAG,qBAAqB,UAAU;AAAA,IAClG,CAAC;AACD,iBAAa,QAAQ,UAAQ;AAC3B,YAAM,OAAO,cAAc,IAAI,IAAI;AACnC,YAAM,MAAM,aAAa,IAAI,IAAI;AACjC,oBAAc,IAAI,MAAM,IAAI,IAAI,CAAC,GAAI,MAAM,QAAQ,KAAK,CAAC,GAAI,GAAI,KAAK,QAAQ,KAAK,CAAC,CAAE,CAAC,CAAC;AAAA,IAC1F,CAAC;AACD,UAAM,cAAc,CAAC;AACrB,UAAM,aAAa,CAAC;AACpB,UAAM,uCAAuC,CAAC;AAC9C,uBAAmB,QAAQ,WAAS;AAClC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AAGJ,UAAI,aAAa,IAAI,OAAO,GAAG;AAC7B,YAAI,oBAAoB,IAAI,OAAO,GAAG;AACpC,iBAAO,UAAU,MAAM,UAAU,SAAS,YAAY,QAAQ,CAAC;AAC/D,iBAAO,WAAW;AAClB,iBAAO,kBAAkB,YAAY,SAAS;AAC9C,yBAAe,KAAK,MAAM;AAC1B;AAAA,QACF;AAOA,YAAI,sBAAsB;AAC1B,YAAI,oBAAoB,OAAO,GAAG;AAChC,cAAI,MAAM;AACV,gBAAM,eAAe,CAAC;AACtB,iBAAO,MAAM,IAAI,YAAY;AAC3B,kBAAM,iBAAiB,oBAAoB,IAAI,GAAG;AAClD,gBAAI,gBAAgB;AAClB,oCAAsB;AACtB;AAAA,YACF;AACA,yBAAa,KAAK,GAAG;AAAA,UACvB;AACA,uBAAa,QAAQ,YAAU,oBAAoB,IAAI,QAAQ,mBAAmB,CAAC;AAAA,QACrF;AACA,cAAM,cAAc,KAAK,gBAAgB,OAAO,aAAa,aAAa,uBAAuB,mBAAmB,cAAc,aAAa;AAC/I,eAAO,cAAc,WAAW;AAChC,YAAI,wBAAwB,sCAAsC;AAChE,sBAAY,KAAK,MAAM;AAAA,QACzB,OAAO;AACL,gBAAM,gBAAgB,KAAK,iBAAiB,IAAI,mBAAmB;AACnE,cAAI,iBAAiB,cAAc,QAAQ;AACzC,mBAAO,eAAe,oBAAoB,aAAa;AAAA,UACzD;AACA,yBAAe,KAAK,MAAM;AAAA,QAC5B;AAAA,MACF,OAAO;AACL,oBAAY,SAAS,YAAY,UAAU;AAC3C,eAAO,UAAU,MAAM,UAAU,SAAS,YAAY,QAAQ,CAAC;AAI/D,mBAAW,KAAK,MAAM;AACtB,YAAI,oBAAoB,IAAI,OAAO,GAAG;AACpC,yBAAe,KAAK,MAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AAED,eAAW,QAAQ,YAAU;AAG3B,YAAM,oBAAoB,kBAAkB,IAAI,OAAO,OAAO;AAC9D,UAAI,qBAAqB,kBAAkB,QAAQ;AACjD,cAAM,cAAc,oBAAoB,iBAAiB;AACzD,eAAO,cAAc,WAAW;AAAA,MAClC;AAAA,IACF,CAAC;AAID,mBAAe,QAAQ,YAAU;AAC/B,UAAI,OAAO,cAAc;AACvB,eAAO,iBAAiB,OAAO,YAAY;AAAA,MAC7C,OAAO;AACL,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAID,aAASI,KAAI,GAAGA,KAAI,cAAc,QAAQA,MAAK;AAC7C,YAAM,UAAU,cAAcA,EAAC;AAC/B,YAAM,UAAU,QAAQ,YAAY;AACpC,kBAAY,SAAS,eAAe;AAIpC,UAAI,WAAW,QAAQ,aAAc;AACrC,UAAI,UAAU,CAAC;AAIf,UAAI,gBAAgB,MAAM;AACxB,YAAI,uBAAuB,gBAAgB,IAAI,OAAO;AACtD,YAAI,wBAAwB,qBAAqB,QAAQ;AACvD,kBAAQ,KAAK,GAAG,oBAAoB;AAAA,QACtC;AACA,YAAI,uBAAuB,KAAK,OAAO,MAAM,SAAS,uBAAuB,IAAI;AACjF,iBAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AACpD,cAAI,iBAAiB,gBAAgB,IAAI,qBAAqB,CAAC,CAAC;AAChE,cAAI,kBAAkB,eAAe,QAAQ;AAC3C,oBAAQ,KAAK,GAAG,cAAc;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AACA,YAAM,gBAAgB,QAAQ,OAAO,OAAK,CAAC,EAAE,SAAS;AACtD,UAAI,cAAc,QAAQ;AACxB,sCAA8B,MAAM,SAAS,aAAa;AAAA,MAC5D,OAAO;AACL,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAEA,kBAAc,SAAS;AACvB,gBAAY,QAAQ,YAAU;AAC5B,WAAK,QAAQ,KAAK,MAAM;AACxB,aAAO,OAAO,MAAM;AAClB,eAAO,QAAQ;AACf,cAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM;AACzC,aAAK,QAAQ,OAAO,OAAO,CAAC;AAAA,MAC9B,CAAC;AACD,aAAO,KAAK;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,WAAW,UAAU;AACnB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC9B;AAAA,EACA,yBAAyB,UAAU;AACjC,SAAK,cAAc,KAAK,QAAQ;AAAA,EAClC;AAAA,EACA,oBAAoB,SAAS,kBAAkB,aAAa,aAAa,cAAc;AACrF,QAAI,UAAU,CAAC;AACf,QAAI,kBAAkB;AACpB,YAAM,wBAAwB,KAAK,wBAAwB,IAAI,OAAO;AACtE,UAAI,uBAAuB;AACzB,kBAAU;AAAA,MACZ;AAAA,IACF,OAAO;AACL,YAAM,iBAAiB,KAAK,iBAAiB,IAAI,OAAO;AACxD,UAAI,gBAAgB;AAClB,cAAM,qBAAqB,CAAC,gBAAgB,gBAAgB;AAC5D,uBAAe,QAAQ,YAAU;AAC/B,cAAI,OAAO,OAAQ;AACnB,cAAI,CAAC,sBAAsB,OAAO,eAAe,YAAa;AAC9D,kBAAQ,KAAK,MAAM;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,eAAe,aAAa;AAC9B,gBAAU,QAAQ,OAAO,YAAU;AACjC,YAAI,eAAe,eAAe,OAAO,YAAa,QAAO;AAC7D,YAAI,eAAe,eAAe,OAAO,YAAa,QAAO;AAC7D,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,aAAa,aAAa,uBAAuB;AACrE,UAAM,cAAc,YAAY;AAChC,UAAM,cAAc,YAAY;AAGhC,UAAM,oBAAoB,YAAY,sBAAsB,SAAY;AACxE,UAAM,oBAAoB,YAAY,sBAAsB,SAAY;AACxE,eAAW,uBAAuB,YAAY,WAAW;AACvD,YAAM,UAAU,oBAAoB;AACpC,YAAM,mBAAmB,YAAY;AACrC,YAAM,UAAU,qBAAqB,uBAAuB,SAAS,CAAC,CAAC;AACvE,YAAM,kBAAkB,KAAK,oBAAoB,SAAS,kBAAkB,mBAAmB,mBAAmB,YAAY,OAAO;AACrI,sBAAgB,QAAQ,YAAU;AAChC,cAAM,aAAa,OAAO,cAAc;AACxC,YAAI,WAAW,eAAe;AAC5B,qBAAW,cAAc;AAAA,QAC3B;AACA,eAAO,QAAQ;AACf,gBAAQ,KAAK,MAAM;AAAA,MACrB,CAAC;AAAA,IACH;AAGA,gBAAY,aAAa,YAAY,UAAU;AAAA,EACjD;AAAA,EACA,gBAAgB,aAAa,aAAa,uBAAuB,mBAAmB,cAAc,eAAe;AAC/G,UAAM,cAAc,YAAY;AAChC,UAAM,cAAc,YAAY;AAGhC,UAAM,oBAAoB,CAAC;AAC3B,UAAM,sBAAsB,oBAAI,IAAI;AACpC,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,gBAAgB,YAAY,UAAU,IAAI,yBAAuB;AACrE,YAAM,UAAU,oBAAoB;AACpC,0BAAoB,IAAI,OAAO;AAE/B,YAAM,UAAU,QAAQ,YAAY;AACpC,UAAI,WAAW,QAAQ,qBAAsB,QAAO,IAAI,oBAAoB,oBAAoB,UAAU,oBAAoB,KAAK;AACnI,YAAM,mBAAmB,YAAY;AACrC,YAAM,kBAAkB,qBAAqB,sBAAsB,IAAI,OAAO,KAAK,oBAAoB,IAAI,OAAK,EAAE,cAAc,CAAC,CAAC,EAAE,OAAO,OAAK;AAK9I,cAAM,KAAK;AACX,eAAO,GAAG,UAAU,GAAG,YAAY,UAAU;AAAA,MAC/C,CAAC;AACD,YAAM,YAAY,aAAa,IAAI,OAAO;AAC1C,YAAM,aAAa,cAAc,IAAI,OAAO;AAC5C,YAAM,YAAY,qBAAmB,KAAK,aAAa,oBAAoB,WAAW,WAAW,UAAU;AAC3G,YAAMF,UAAS,KAAK,aAAa,qBAAqB,WAAW,eAAe;AAGhF,UAAI,oBAAoB,eAAe,mBAAmB;AACxD,uBAAe,IAAI,OAAO;AAAA,MAC5B;AACA,UAAI,kBAAkB;AACpB,cAAM,gBAAgB,IAAI,0BAA0B,aAAa,aAAa,OAAO;AACrF,sBAAc,cAAcA,OAAM;AAClC,0BAAkB,KAAK,aAAa;AAAA,MACtC;AACA,aAAOA;AAAA,IACT,CAAC;AACD,sBAAkB,QAAQ,CAAAA,YAAU;AAClC,2BAAqB,KAAK,yBAAyBA,QAAO,SAAS,CAAC,CAAC,EAAE,KAAKA,OAAM;AAClF,MAAAA,QAAO,OAAO,MAAM,mBAAmB,KAAK,yBAAyBA,QAAO,SAASA,OAAM,CAAC;AAAA,IAC9F,CAAC;AACD,wBAAoB,QAAQ,aAAW,SAAS,SAAS,sBAAsB,CAAC;AAChF,UAAM,SAAS,oBAAoB,aAAa;AAChD,WAAO,UAAU,MAAM;AACrB,0BAAoB,QAAQ,aAAW,YAAY,SAAS,sBAAsB,CAAC;AACnF,gBAAU,aAAa,YAAY,QAAQ;AAAA,IAC7C,CAAC;AAGD,mBAAe,QAAQ,aAAW;AAChC,2BAAqB,mBAAmB,SAAS,CAAC,CAAC,EAAE,KAAK,MAAM;AAAA,IAClE,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,aAAa,aAAa,WAAW,iBAAiB;AACpD,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,KAAK,OAAO,QAAQ,YAAY,SAAS,WAAW,YAAY,UAAU,YAAY,OAAO,YAAY,QAAQ,eAAe;AAAA,IACzI;AAGA,WAAO,IAAI,oBAAoB,YAAY,UAAU,YAAY,KAAK;AAAA,EACxE;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,IAAI,oBAAoB;AAAA,EAClC,sBAAsB;AAAA,EACtB,mBAAmB,oBAAI,IAAI;AAAA,EAC3B,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,YAAY,aAAa,aAAa,SAAS;AAC7C,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,cAAc,QAAQ;AACpB,QAAI,KAAK,oBAAqB;AAC9B,SAAK,UAAU;AACf,SAAK,iBAAiB,QAAQ,CAAC,WAAW,UAAU;AAClD,gBAAU,QAAQ,cAAY,eAAe,QAAQ,OAAO,QAAW,QAAQ,CAAC;AAAA,IAClF,CAAC;AACD,SAAK,iBAAiB,MAAM;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB,OAAO,SAAS;AACvC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,WAAW;AAC3B,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,QAAQ;AACvB,UAAM,IAAI,KAAK;AACf,QAAI,EAAE,iBAAiB;AACrB,aAAO,QAAQ,MAAM,EAAE,gBAAgB,OAAO,CAAC;AAAA,IACjD;AACA,WAAO,OAAO,MAAM,KAAK,OAAO,CAAC;AACjC,WAAO,UAAU,MAAM,KAAK,QAAQ,CAAC;AAAA,EACvC;AAAA,EACA,YAAY,MAAM,UAAU;AAC1B,yBAAqB,KAAK,kBAAkB,MAAM,CAAC,CAAC,EAAE,KAAK,QAAQ;AAAA,EACrE;AAAA,EACA,OAAO,IAAI;AACT,QAAI,KAAK,QAAQ;AACf,WAAK,YAAY,QAAQ,EAAE;AAAA,IAC7B;AACA,SAAK,QAAQ,OAAO,EAAE;AAAA,EACxB;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,KAAK,QAAQ;AACf,WAAK,YAAY,SAAS,EAAE;AAAA,IAC9B;AACA,SAAK,QAAQ,QAAQ,EAAE;AAAA,EACzB;AAAA,EACA,UAAU,IAAI;AACZ,QAAI,KAAK,QAAQ;AACf,WAAK,YAAY,WAAW,EAAE;AAAA,IAChC;AACA,SAAK,QAAQ,UAAU,EAAE;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,QAAQ,KAAK;AAAA,EACpB;AAAA,EACA,aAAa;AACX,WAAO,KAAK,SAAS,QAAQ,KAAK,QAAQ,WAAW;AAAA,EACvD;AAAA,EACA,OAAO;AACL,KAAC,KAAK,UAAU,KAAK,QAAQ,KAAK;AAAA,EACpC;AAAA,EACA,QAAQ;AACN,KAAC,KAAK,UAAU,KAAK,QAAQ,MAAM;AAAA,EACrC;AAAA,EACA,UAAU;AACR,KAAC,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACvC;AAAA,EACA,SAAS;AACP,SAAK,QAAQ,OAAO;AAAA,EACtB;AAAA,EACA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,QAAQ,QAAQ;AAAA,EACvB;AAAA,EACA,QAAQ;AACN,KAAC,KAAK,UAAU,KAAK,QAAQ,MAAM;AAAA,EACrC;AAAA,EACA,YAAY,GAAG;AACb,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,QAAQ,YAAY,CAAC;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,SAAS,IAAI,KAAK,QAAQ,YAAY;AAAA,EACpD;AAAA;AAAA,EAEA,gBAAgB,WAAW;AACzB,UAAM,IAAI,KAAK;AACf,QAAI,EAAE,iBAAiB;AACrB,QAAE,gBAAgB,SAAS;AAAA,IAC7B;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,KAAK,KAAK,OAAO;AAC3C,MAAI,gBAAgB,IAAI,IAAI,GAAG;AAC/B,MAAI,eAAe;AACjB,QAAI,cAAc,QAAQ;AACxB,YAAM,QAAQ,cAAc,QAAQ,KAAK;AACzC,oBAAc,OAAO,OAAO,CAAC;AAAA,IAC/B;AACA,QAAI,cAAc,UAAU,GAAG;AAC7B,UAAI,OAAO,GAAG;AAAA,IAChB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,OAAO;AAIpC,SAAO,SAAS,OAAO,QAAQ;AACjC;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,QAAQ,KAAK,UAAU,MAAM;AACtC;AACA,SAAS,oBAAoB,WAAW;AACtC,SAAO,aAAa,WAAW,aAAa;AAC9C;AACA,SAAS,aAAa,SAAS,OAAO;AACpC,QAAM,WAAW,QAAQ,MAAM;AAC/B,UAAQ,MAAM,UAAU,SAAS,OAAO,QAAQ;AAChD,SAAO;AACT;AACA,SAAS,sBAAsB,WAAW,QAAQ,UAAU,iBAAiB,cAAc;AACzF,QAAM,YAAY,CAAC;AACnB,WAAS,QAAQ,aAAW,UAAU,KAAK,aAAa,OAAO,CAAC,CAAC;AACjE,QAAM,iBAAiB,CAAC;AACxB,kBAAgB,QAAQ,CAAC,OAAO,YAAY;AAC1C,UAAM,SAAS,oBAAI,IAAI;AACvB,UAAM,QAAQ,UAAQ;AACpB,YAAM,QAAQ,OAAO,aAAa,SAAS,MAAM,YAAY;AAC7D,aAAO,IAAI,MAAM,KAAK;AAGtB,UAAI,CAAC,SAAS,MAAM,UAAU,GAAG;AAC/B,gBAAQ,YAAY,IAAI;AACxB,uBAAe,KAAK,OAAO;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,cAAU,IAAI,SAAS,MAAM;AAAA,EAC/B,CAAC;AAGD,MAAI,IAAI;AACR,WAAS,QAAQ,aAAW,aAAa,SAAS,UAAU,GAAG,CAAC,CAAC;AACjE,SAAO;AACT;AAWA,SAAS,aAAa,OAAO,OAAO;AAClC,QAAM,UAAU,oBAAI,IAAI;AACxB,QAAM,QAAQ,UAAQ,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC;AAC3C,MAAI,MAAM,UAAU,EAAG,QAAO;AAC9B,QAAM,YAAY;AAClB,QAAM,UAAU,IAAI,IAAI,KAAK;AAC7B,QAAM,eAAe,oBAAI,IAAI;AAC7B,WAAS,QAAQ,MAAM;AACrB,QAAI,CAAC,KAAM,QAAO;AAClB,QAAI,OAAO,aAAa,IAAI,IAAI;AAChC,QAAI,KAAM,QAAO;AACjB,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ,IAAI,MAAM,GAAG;AAEvB,aAAO;AAAA,IACT,WAAW,QAAQ,IAAI,MAAM,GAAG;AAE9B,aAAO;AAAA,IACT,OAAO;AAEL,aAAO,QAAQ,MAAM;AAAA,IACvB;AACA,iBAAa,IAAI,MAAM,IAAI;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,UAAQ;AACpB,UAAM,OAAO,QAAQ,IAAI;AACzB,QAAI,SAAS,WAAW;AACtB,cAAQ,IAAI,IAAI,EAAE,KAAK,IAAI;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,SAAS,SAAS,WAAW;AACpC,UAAQ,WAAW,IAAI,SAAS;AAClC;AACA,SAAS,YAAY,SAAS,WAAW;AACvC,UAAQ,WAAW,OAAO,SAAS;AACrC;AACA,SAAS,8BAA8B,QAAQ,SAAS,SAAS;AAC/D,sBAAoB,OAAO,EAAE,OAAO,MAAM,OAAO,iBAAiB,OAAO,CAAC;AAC5E;AACA,SAAS,oBAAoB,SAAS;AACpC,QAAM,eAAe,CAAC;AACtB,4BAA0B,SAAS,YAAY;AAC/C,SAAO;AACT;AACA,SAAS,0BAA0B,SAAS,cAAc;AACxD,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,kBAAkB,sBAAsB;AAC1C,gCAA0B,OAAO,SAAS,YAAY;AAAA,IACxD,OAAO;AACL,mBAAa,KAAK,MAAM;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,QAAM,KAAK,OAAO,KAAK,CAAC;AACxB,QAAM,KAAK,OAAO,KAAK,CAAC;AACxB,MAAI,GAAG,UAAU,GAAG,OAAQ,QAAO;AACnC,WAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,UAAM,OAAO,GAAG,CAAC;AACjB,QAAI,CAAC,EAAE,eAAe,IAAI,KAAK,EAAE,IAAI,MAAM,EAAE,IAAI,EAAG,QAAO;AAAA,EAC7D;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,SAAS,qBAAqB,sBAAsB;AAClF,QAAM,YAAY,qBAAqB,IAAI,OAAO;AAClD,MAAI,CAAC,UAAW,QAAO;AACvB,MAAI,WAAW,oBAAoB,IAAI,OAAO;AAC9C,MAAI,UAAU;AACZ,cAAU,QAAQ,UAAQ,SAAS,IAAI,IAAI,CAAC;AAAA,EAC9C,OAAO;AACL,wBAAoB,IAAI,SAAS,SAAS;AAAA,EAC5C;AACA,uBAAqB,OAAO,OAAO;AACnC,SAAO;AACT;AACA,IAAM,kBAAN,MAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB,CAAC;AAAA;AAAA,EAEjB,oBAAoB,CAAC,SAAS,YAAY;AAAA,EAAC;AAAA,EAC3C,YAAY,KAAK,SAAS,aAAa;AACrC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,oBAAoB,IAAI,0BAA0B,IAAI,MAAM,SAAS,WAAW;AACrF,SAAK,kBAAkB,IAAI,wBAAwB,IAAI,MAAM,SAAS,WAAW;AACjF,SAAK,kBAAkB,oBAAoB,CAAC,SAAS,YAAY,KAAK,kBAAkB,SAAS,OAAO;AAAA,EAC1G;AAAA,EACA,gBAAgB,aAAa,aAAa,aAAa,MAAM,UAAU;AACrE,UAAM,WAAW,cAAc,MAAM;AACrC,QAAI,UAAU,KAAK,cAAc,QAAQ;AACzC,QAAI,CAAC,SAAS;AACZ,YAAM,SAAS,CAAC;AAChB,YAAM,WAAW,CAAC;AAClB,YAAM,MAAM,kBAAkB,KAAK,SAAS,UAAU,QAAQ,QAAQ;AACtE,UAAI,OAAO,QAAQ;AACjB,cAAM,mBAAmB,MAAM,MAAM;AAAA,MACvC;AACA,UAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAI,SAAS,QAAQ;AACnB,2BAAiB,MAAM,QAAQ;AAAA,QACjC;AAAA,MACF;AACA,gBAAU,aAAa,MAAM,KAAK,KAAK,WAAW;AAClD,WAAK,cAAc,QAAQ,IAAI;AAAA,IACjC;AACA,SAAK,kBAAkB,gBAAgB,aAAa,MAAM,OAAO;AAAA,EACnE;AAAA,EACA,SAAS,aAAa,aAAa;AACjC,SAAK,kBAAkB,SAAS,aAAa,WAAW;AAAA,EAC1D;AAAA,EACA,QAAQ,aAAa,SAAS;AAC5B,SAAK,kBAAkB,QAAQ,aAAa,OAAO;AAAA,EACrD;AAAA,EACA,SAAS,aAAa,SAAS,QAAQ,cAAc;AACnD,SAAK,kBAAkB,WAAW,aAAa,SAAS,QAAQ,YAAY;AAAA,EAC9E;AAAA,EACA,SAAS,aAAa,SAAS,SAAS;AACtC,SAAK,kBAAkB,WAAW,aAAa,SAAS,OAAO;AAAA,EACjE;AAAA,EACA,kBAAkB,SAAS,SAAS;AAClC,SAAK,kBAAkB,sBAAsB,SAAS,OAAO;AAAA,EAC/D;AAAA,EACA,QAAQ,aAAa,SAAS,UAAU,OAAO;AAC7C,QAAI,SAAS,OAAO,CAAC,KAAK,KAAK;AAC7B,YAAM,CAAC,IAAI,MAAM,IAAI,qBAAqB,QAAQ;AAClD,YAAM,OAAO;AACb,WAAK,gBAAgB,QAAQ,IAAI,SAAS,QAAQ,IAAI;AAAA,IACxD,OAAO;AACL,WAAK,kBAAkB,QAAQ,aAAa,SAAS,UAAU,KAAK;AAAA,IACtE;AAAA,EACF;AAAA,EACA,OAAO,aAAa,SAAS,WAAW,YAAY,UAAU;AAE5D,QAAI,UAAU,OAAO,CAAC,KAAK,KAAK;AAC9B,YAAM,CAAC,IAAI,MAAM,IAAI,qBAAqB,SAAS;AACnD,aAAO,KAAK,gBAAgB,OAAO,IAAI,SAAS,QAAQ,QAAQ;AAAA,IAClE;AACA,WAAO,KAAK,kBAAkB,OAAO,aAAa,SAAS,WAAW,YAAY,QAAQ;AAAA,EAC5F;AAAA,EACA,MAAM,cAAc,IAAI;AACtB,SAAK,kBAAkB,MAAM,WAAW;AAAA,EAC1C;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,CAAC,GAAG,KAAK,kBAAkB,SAAS,GAAG,KAAK,gBAAgB,OAAO;AAAA,EAC5E;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,kBAAkB,kBAAkB;AAAA,EAClD;AAAA,EACA,yBAAyB,IAAI;AAC3B,SAAK,kBAAkB,yBAAyB,EAAE;AAAA,EACpD;AACF;AAaA,SAAS,2BAA2B,SAAS,QAAQ;AACnD,MAAI,cAAc;AAClB,MAAI,YAAY;AAChB,MAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,QAAQ;AAC1C,kBAAc,0BAA0B,OAAO,CAAC,CAAC;AACjD,QAAI,OAAO,SAAS,GAAG;AACrB,kBAAY,0BAA0B,OAAO,OAAO,SAAS,CAAC,CAAC;AAAA,IACjE;AAAA,EACF,WAAW,kBAAkB,KAAK;AAChC,kBAAc,0BAA0B,MAAM;AAAA,EAChD;AACA,SAAO,eAAe,YAAY,IAAI,mBAAmB,SAAS,aAAa,SAAS,IAAI;AAC9F;AASA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO,yBAAwC,oBAAI,QAAQ;AAAA,EAC3D,SAAS;AAAA,EACT;AAAA,EACA,YAAY,UAAU,cAAc,YAAY;AAC9C,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,QAAI,gBAAgB,oBAAmB,uBAAuB,IAAI,QAAQ;AAC1E,QAAI,CAAC,eAAe;AAClB,0BAAmB,uBAAuB,IAAI,UAAU,gBAAgB,oBAAI,IAAI,CAAC;AAAA,IACnF;AACA,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,SAAS,GAAyC;AACzD,UAAI,KAAK,cAAc;AACrB,kBAAU,KAAK,UAAU,KAAK,cAAc,KAAK,cAAc;AAAA,MACjE;AACA,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,SAAS;AACP,SAAK,MAAM;AACX,QAAI,KAAK,SAAS,GAA0C;AAC1D,gBAAU,KAAK,UAAU,KAAK,cAAc;AAC5C,UAAI,KAAK,YAAY;AACnB,kBAAU,KAAK,UAAU,KAAK,UAAU;AACxC,aAAK,aAAa;AAAA,MACpB;AACA,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,OAAO;AACZ,QAAI,KAAK,SAAS,GAA2C;AAC3D,0BAAmB,uBAAuB,OAAO,KAAK,QAAQ;AAC9D,UAAI,KAAK,cAAc;AACrB,oBAAY,KAAK,UAAU,KAAK,YAAY;AAC5C,aAAK,aAAa;AAAA,MACpB;AACA,UAAI,KAAK,YAAY;AACnB,oBAAY,KAAK,UAAU,KAAK,UAAU;AAC1C,aAAK,aAAa;AAAA,MACpB;AACA,gBAAU,KAAK,UAAU,KAAK,cAAc;AAC5C,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,QAAQ;AACzC,MAAI,SAAS;AACb,SAAO,QAAQ,CAAC,KAAK,SAAS;AAC5B,QAAI,qBAAqB,IAAI,GAAG;AAC9B,eAAS,UAAU,oBAAI,IAAI;AAC3B,aAAO,IAAI,MAAM,GAAG;AAAA,IACtB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,qBAAqB,MAAM;AAClC,SAAO,SAAS,aAAa,SAAS;AACxC;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa,CAAC;AAAA,EACd,cAAc,CAAC;AAAA,EACf,gBAAgB,CAAC;AAAA,EACjB;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,CAAC;AAAA,EACtB,sBAAsB,CAAC;AAAA;AAAA,EAEvB;AAAA,EACA,OAAO;AAAA,EACP,eAAe;AAAA,EACf,kBAAkB,oBAAI,IAAI;AAAA,EAC1B,YAAY,SAAS,WAAW,SAAS,gBAAgB;AACvD,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,YAAY,QAAQ,UAAU;AACnC,SAAK,SAAS,QAAQ,OAAO,KAAK;AAClC,SAAK,OAAO,KAAK,YAAY,KAAK;AAAA,EACpC;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,WAAW,QAAQ,QAAM,GAAG,CAAC;AAClC,WAAK,aAAa,CAAC;AAAA,IACrB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,aAAa;AAClB,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACA,eAAe;AACb,QAAI,KAAK,aAAc;AACvB,SAAK,eAAe;AACpB,UAAM,YAAY,KAAK;AAEvB,SAAK,YAAY,KAAK,qBAAqB,KAAK,SAAS,WAAW,KAAK,OAAO;AAChF,SAAK,iBAAiB,UAAU,SAAS,UAAU,UAAU,SAAS,CAAC,IAAI,oBAAI,IAAI;AACnF,UAAM,WAAW,MAAM,KAAK,UAAU;AACtC,SAAK,UAAU,iBAAiB,UAAU,QAAQ;AAClD,SAAK,UAAU,MAAM;AAInB,WAAK,UAAU,oBAAoB,UAAU,QAAQ;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B;AAE1B,QAAI,KAAK,QAAQ;AACf,WAAK,qBAAqB;AAAA,IAC5B,OAAO;AACL,WAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAAA,EACA,0BAA0B,WAAW;AACnC,UAAM,MAAM,CAAC;AACb,cAAU,QAAQ,WAAS;AACzB,UAAI,KAAK,OAAO,YAAY,KAAK,CAAC;AAAA,IACpC,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,qBAAqB,SAAS,WAAW,SAAS;AAChD,WAAO,QAAQ,QAAQ,KAAK,0BAA0B,SAAS,GAAG,OAAO;AAAA,EAC3E;AAAA,EACA,QAAQ,IAAI;AACV,SAAK,oBAAoB,KAAK,EAAE;AAChC,SAAK,YAAY,KAAK,EAAE;AAAA,EAC1B;AAAA,EACA,OAAO,IAAI;AACT,SAAK,mBAAmB,KAAK,EAAE;AAC/B,SAAK,WAAW,KAAK,EAAE;AAAA,EACzB;AAAA,EACA,UAAU,IAAI;AACZ,SAAK,cAAc,KAAK,EAAE;AAAA,EAC5B;AAAA,EACA,OAAO;AACL,SAAK,aAAa;AAClB,QAAI,CAAC,KAAK,WAAW,GAAG;AACtB,WAAK,YAAY,QAAQ,QAAM,GAAG,CAAC;AACnC,WAAK,cAAc,CAAC;AACpB,WAAK,WAAW;AAChB,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,MAAM;AAAA,MAC5B;AAAA,IACF;AACA,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA,EACA,QAAQ;AACN,SAAK,KAAK;AACV,SAAK,UAAU,MAAM;AAAA,EACvB;AAAA,EACA,SAAS;AACP,SAAK,KAAK;AACV,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,OAAO;AAAA,IAC7B;AACA,SAAK,UAAU;AACf,SAAK,UAAU,OAAO;AAAA,EACxB;AAAA,EACA,QAAQ;AACN,SAAK,qBAAqB;AAC1B,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,OAAO;AAAA,IACxB;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,MAAM;AACX,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa;AAClB,WAAK,qBAAqB;AAC1B,WAAK,UAAU;AACf,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,QAAQ;AAAA,MAC9B;AACA,WAAK,cAAc,QAAQ,QAAM,GAAG,CAAC;AACrC,WAAK,gBAAgB,CAAC;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,QAAI,KAAK,cAAc,QAAW;AAChC,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,UAAU,cAAc,IAAI,KAAK;AAAA,EACxC;AAAA,EACA,cAAc;AAEZ,WAAO,EAAE,KAAK,UAAU,eAAe,KAAK,KAAK;AAAA,EACnD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,SAAS,KAAK;AAAA,EAC5B;AAAA,EACA,gBAAgB;AACd,UAAM,SAAS,oBAAI,IAAI;AACvB,QAAI,KAAK,WAAW,GAAG;AAIrB,YAAM,gBAAgB,KAAK;AAC3B,oBAAc,QAAQ,CAAC,KAAK,SAAS;AACnC,YAAI,SAAS,UAAU;AACrB,iBAAO,IAAI,MAAM,KAAK,YAAY,MAAM,aAAa,KAAK,SAAS,IAAI,CAAC;AAAA,QAC1E;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,kBAAkB;AAAA,EACzB;AAAA;AAAA,EAEA,gBAAgB,WAAW;AACzB,UAAM,UAAU,cAAc,UAAU,KAAK,cAAc,KAAK;AAChE,YAAQ,QAAQ,QAAM,GAAG,CAAC;AAC1B,YAAQ,SAAS;AAAA,EACnB;AACF;AACA,IAAM,sBAAN,MAA0B;AAAA,EACxB,sBAAsB,MAAM;AAE1B,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,aAAO,sBAAsB,IAAI;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,MAAM;AAEpC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAM,UAAU,oBAAoB,IAAI;AACxC,aAAO,mCAAmC,OAAO;AAAA,IACnD;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,gBAAgB,MAAM,IAAI;AAAA,EACnC;AAAA,EACA,iBAAiB,SAAS;AACxB,WAAO,iBAAiB,OAAO;AAAA,EACjC;AAAA,EACA,MAAM,SAAS,UAAU,OAAO;AAC9B,WAAO,YAAY,SAAS,UAAU,KAAK;AAAA,EAC7C;AAAA,EACA,aAAa,SAAS,MAAM,cAAc;AACxC,WAAO,aAAa,SAAS,IAAI;AAAA,EACnC;AAAA,EACA,QAAQ,SAAS,WAAW,UAAU,OAAO,QAAQ,kBAAkB,CAAC,GAAG;AACzE,UAAM,OAAO,SAAS,IAAI,SAAS;AACnC,UAAM,gBAAgB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAGA,QAAI,QAAQ;AACV,oBAAc,QAAQ,IAAI;AAAA,IAC5B;AACA,UAAM,iBAAiB,oBAAI,IAAI;AAC/B,UAAM,8BAA8B,gBAAgB,OAAO,YAAU,kBAAkB,mBAAmB;AAC1G,QAAI,+BAA+B,UAAU,KAAK,GAAG;AACnD,kCAA4B,QAAQ,YAAU;AAC5C,eAAO,gBAAgB,QAAQ,CAAC,KAAK,SAAS,eAAe,IAAI,MAAM,GAAG,CAAC;AAAA,MAC7E,CAAC;AAAA,IACH;AACA,QAAI,aAAa,mBAAqB,SAAS,EAAE,IAAI,YAAU,IAAI,IAAI,MAAM,CAAC;AAC9E,iBAAa,mCAAmC,SAAS,YAAY,cAAc;AACnF,UAAM,gBAAgB,2BAA2B,SAAS,UAAU;AACpE,WAAO,IAAI,oBAAoB,SAAS,YAAY,eAAe,aAAa;AAAA,EAClF;AACF;AAsCA,IAAM,mBAAmB;AACzB,IAAM,0BAA0B;AAChC,IAAM,wBAAN,MAA4B;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA,EAGA,QAAQ;AAAA,EACR,YAAY,aAAa,UAAU,QAAQ,YAAY;AACrD,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,SAAS,cAAc,IAAI;AAAA,EAClC;AAAA,EACA,UAAU;AACR,SAAK,OAAO,QAAQ,KAAK,aAAa,KAAK,QAAQ;AACnD,SAAK,OAAO,yBAAyB,MAAM;AAGzC,qBAAe,MAAM;AACnB,aAAK,SAAS,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc,MAAM,WAAW;AAC7B,WAAO,KAAK,SAAS,cAAc,MAAM,SAAS;AAAA,EACpD;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,SAAS,cAAc,KAAK;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,SAAS,WAAW,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,SAAK,SAAS,YAAY,QAAQ,QAAQ;AAC1C,SAAK,OAAO,SAAS,KAAK,aAAa,UAAU,QAAQ,KAAK;AAAA,EAChE;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU,SAAS,MAAM;AACtD,SAAK,SAAS,aAAa,QAAQ,UAAU,QAAQ;AAErD,SAAK,OAAO,SAAS,KAAK,aAAa,UAAU,QAAQ,MAAM;AAAA,EACjE;AAAA,EACA,YAAY,QAAQ,UAAU,eAAe;AAK3C,QAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,WAAK,OAAO,SAAS,KAAK,aAAa,UAAU,KAAK,QAAQ;AAAA,IAChE;AAAA,EACF;AAAA,EACA,kBAAkB,gBAAgB,iBAAiB;AACjD,WAAO,KAAK,SAAS,kBAAkB,gBAAgB,eAAe;AAAA,EACxE;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,SAAS,WAAW,IAAI;AAAA,EACtC;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK,SAAS,YAAY,IAAI;AAAA,EACvC;AAAA,EACA,aAAa,IAAI,MAAM,OAAO,WAAW;AACvC,SAAK,SAAS,aAAa,IAAI,MAAM,OAAO,SAAS;AAAA,EACvD;AAAA,EACA,gBAAgB,IAAI,MAAM,WAAW;AACnC,SAAK,SAAS,gBAAgB,IAAI,MAAM,SAAS;AAAA,EACnD;AAAA,EACA,SAAS,IAAI,MAAM;AACjB,SAAK,SAAS,SAAS,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,YAAY,IAAI,MAAM;AACpB,SAAK,SAAS,YAAY,IAAI,IAAI;AAAA,EACpC;AAAA,EACA,SAAS,IAAIG,QAAO,OAAO,OAAO;AAChC,SAAK,SAAS,SAAS,IAAIA,QAAO,OAAO,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,IAAIA,QAAO,OAAO;AAC5B,SAAK,SAAS,YAAY,IAAIA,QAAO,KAAK;AAAA,EAC5C;AAAA,EACA,YAAY,IAAI,MAAM,OAAO;AAC3B,QAAI,KAAK,OAAO,CAAC,KAAK,oBAAoB,QAAQ,yBAAyB;AACzE,WAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK;AAAA,IACpC,OAAO;AACL,WAAK,SAAS,YAAY,IAAI,MAAM,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,SAAS,MAAM,OAAO;AACpB,SAAK,SAAS,SAAS,MAAM,KAAK;AAAA,EACpC;AAAA,EACA,OAAO,QAAQ,WAAW,UAAU,SAAS;AAC3C,WAAO,KAAK,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAClE;AAAA,EACA,kBAAkB,SAAS,OAAO;AAChC,SAAK,OAAO,kBAAkB,SAAS,KAAK;AAAA,EAC9C;AACF;AACA,IAAM,oBAAN,cAAgC,sBAAsB;AAAA,EACpD;AAAA,EACA,YAAY,SAAS,aAAa,UAAU,QAAQ,WAAW;AAC7D,UAAM,aAAa,UAAU,QAAQ,SAAS;AAC9C,SAAK,UAAU;AACf,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,YAAY,IAAI,MAAM,OAAO;AAC3B,QAAI,KAAK,OAAO,CAAC,KAAK,kBAAkB;AACtC,UAAI,KAAK,OAAO,CAAC,KAAK,OAAO,QAAQ,yBAAyB;AAC5D,gBAAQ,UAAU,SAAY,OAAO,CAAC,CAAC;AACvC,aAAK,kBAAkB,IAAI,KAAK;AAAA,MAClC,OAAO;AACL,aAAK,OAAO,QAAQ,KAAK,aAAa,IAAI,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,MAChE;AAAA,IACF,OAAO;AACL,WAAK,SAAS,YAAY,IAAI,MAAM,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,QAAQ,WAAW,UAAU,SAAS;AAC3C,QAAI,UAAU,OAAO,CAAC,KAAK,kBAAkB;AAC3C,YAAM,UAAU,yBAAyB,MAAM;AAC/C,UAAI,OAAO,UAAU,MAAM,CAAC;AAC5B,UAAI,QAAQ;AAGZ,UAAI,KAAK,OAAO,CAAC,KAAK,kBAAkB;AACtC,SAAC,MAAM,KAAK,IAAI,yBAAyB,IAAI;AAAA,MAC/C;AACA,aAAO,KAAK,OAAO,OAAO,KAAK,aAAa,SAAS,MAAM,OAAO,WAAS;AACzE,cAAM,UAAU,MAAM,OAAO,KAAK;AAClC,aAAK,QAAQ,yBAAyB,SAAS,UAAU,KAAK;AAAA,MAChE,CAAC;AAAA,IACH;AACA,WAAO,KAAK,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAClE;AACF;AACA,SAAS,yBAAyB,QAAQ;AACxC,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,SAAS;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,yBAAyB,aAAa;AAC7C,QAAM,WAAW,YAAY,QAAQ,GAAG;AACxC,QAAM,UAAU,YAAY,UAAU,GAAG,QAAQ;AACjD,QAAM,QAAQ,YAAY,MAAM,WAAW,CAAC;AAC5C,SAAO,CAAC,SAAS,KAAK;AACxB;AACA,IAAM,2BAAN,MAA+B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,eAAe;AAAA,EACf,4BAA4B,CAAC;AAAA,EAC7B,iBAAiB,oBAAI,IAAI;AAAA,EACzB,gBAAgB;AAAA,EAChB,YAAY,UAAU,QAAQ,OAAO;AACnC,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,WAAO,oBAAoB,CAAC,SAASC,cAAa;AAChD,MAAAA,WAAU,YAAY,MAAM,OAAO;AAAA,IACrC;AAAA,EACF;AAAA,EACA,eAAe,aAAa,MAAM;AAChC,UAAM,qBAAqB;AAG3B,UAAM,WAAW,KAAK,SAAS,eAAe,aAAa,IAAI;AAC/D,QAAI,CAAC,eAAe,CAAC,MAAM,OAAO,WAAW,GAAG;AAC9C,YAAM,QAAQ,KAAK;AACnB,UAAI,WAAW,MAAM,IAAI,QAAQ;AACjC,UAAI,CAAC,UAAU;AAGb,cAAM,oBAAoB,MAAM,MAAM,OAAO,QAAQ;AACrD,mBAAW,IAAI,sBAAsB,oBAAoB,UAAU,KAAK,QAAQ,iBAAiB;AAEjG,cAAM,IAAI,UAAU,QAAQ;AAAA,MAC9B;AACA,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK;AACzB,UAAM,cAAc,KAAK,KAAK,MAAM,KAAK;AACzC,SAAK;AACL,SAAK,OAAO,SAAS,aAAa,WAAW;AAC7C,UAAM,kBAAkB,aAAW;AACjC,UAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,gBAAQ,QAAQ,eAAe;AAAA,MACjC,OAAO;AACL,aAAK,OAAO,gBAAgB,aAAa,aAAa,aAAa,QAAQ,MAAM,OAAO;AAAA,MAC1F;AAAA,IACF;AACA,UAAM,oBAAoB,KAAK,KAAK,WAAW;AAC/C,sBAAkB,QAAQ,eAAe;AACzC,WAAO,IAAI,kBAAkB,MAAM,aAAa,UAAU,KAAK,MAAM;AAAA,EACvE;AAAA,EACA,QAAQ;AACN,SAAK;AACL,QAAI,KAAK,SAAS,OAAO;AACvB,WAAK,SAAS,MAAM;AAAA,IACtB;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,mBAAe,MAAM;AACnB,WAAK;AAAA,IACP,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,yBAAyB,OAAO,IAAI,MAAM;AACxC,QAAI,SAAS,KAAK,QAAQ,KAAK,cAAc;AAC3C,WAAK,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC;AAC7B;AAAA,IACF;AACA,UAAM,2BAA2B,KAAK;AACtC,QAAI,yBAAyB,UAAU,GAAG;AACxC,qBAAe,MAAM;AACnB,aAAK,MAAM,IAAI,MAAM;AACnB,mCAAyB,QAAQ,WAAS;AACxC,kBAAM,CAACC,KAAIC,KAAI,IAAI;AACnB,YAAAD,IAAGC,KAAI;AAAA,UACT,CAAC;AACD,eAAK,4BAA4B,CAAC;AAAA,QACpC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,6BAAyB,KAAK,CAAC,IAAI,IAAI,CAAC;AAAA,EAC1C;AAAA,EACA,MAAM;AACJ,SAAK;AAGL,QAAI,KAAK,iBAAiB,GAAG;AAC3B,WAAK,MAAM,kBAAkB,MAAM;AACjC,aAAK,mBAAmB;AACxB,aAAK,OAAO,MAAM,KAAK,YAAY;AAAA,MACrC,CAAC;AAAA,IACH;AACA,QAAI,KAAK,SAAS,KAAK;AACrB,WAAK,SAAS,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,OAAO,kBAAkB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAa;AAE7B,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS,oBAAoB,WAAW;AAAA,EAC/C;AACF;;;ACh8HA,IAAM,4BAAN,MAAM,mCAAkC,gBAAiB;AAAA;AAAA;AAAA;AAAA,EAIvD,YAAY,KAAK,QAAQ,YAAY;AACnC,UAAM,KAAK,QAAQ,UAAU;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,SAAK,MAAM;AAAA,EACb;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA8B,SAAS,QAAQ,GAAM,SAAY,eAAe,GAAM,SAAY,wBAAyB,CAAC;AAAA,EAC/J;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,2BAA0B;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,oCAAoC;AAC3C,SAAO,IAAI,6BAA8B;AAC3C;AACA,SAAS,2BAA2B,UAAU,QAAQ,MAAM;AAC1D,SAAO,IAAI,yBAA0B,UAAU,QAAQ,IAAI;AAC7D;AACA,IAAM,6BAA6B,CAAC;AAAA,EAClC,SAAS;AAAA,EACT,YAAY;AACd,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,qBAAqB,iBAAkB,MAAM;AACtD,CAAC;AAKD,IAAM,oCAAoC,CAAC;AAAA,EACzC,SAAS;AAAA,EACT,UAAU;AACZ,GAAG;AAAA,EACD,SAAS;AAAA,EACT,UAAU;AACZ,GAAG,GAAG,0BAA0B;AAKhC,IAAM,+BAA+B;AAAA;AAAA,EAErC;AAAA,IACE,SAAS;AAAA,IACT,YAAY,MAA6C,QAAe,IAAI,oBAAoB,IAAI,IAAI,oBAAqB;AAAA,EAC/H;AAAA,EAAG;AAAA,IACD,SAAS;AAAA,IACT,YAAY,MAA6C,QAAe,mBAAmB;AAAA,EAC7F;AAAA,EAAG,GAAG;AAA0B;AAOhC,IAAM,0BAAN,MAAM,yBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiB5B,OAAO,WAAW,QAAQ;AACxB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,OAAO,oBAAoB,oCAAoC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW;AAAA,IACX,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,WAAW;AAAA,IACb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAuBH,SAAS,oBAAoB;AAC3B,yBAAwB,mBAAmB;AAG3C,SAAO,CAAC,GAAG,4BAA4B;AACzC;AAKA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW;AAAA,IACX,SAAS,CAAC,aAAa;AAAA,EACzB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa;AAAA,MACvB,WAAW;AAAA,IACb,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAsBH,SAAS,wBAAwB;AAG/B,SAAO,CAAC,GAAG,iCAAiC;AAC9C;", "names": ["LINE_START", "style", "styleMetadata", "ast", "element", "value", "player", "index", "i", "style", "delegate", "fn", "data"]}