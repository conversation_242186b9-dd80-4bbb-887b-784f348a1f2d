import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Ride } from '../../../core/models/ride.model';
import { User } from '../../../core/models/user.model';
import { RatingService } from '../../../core/services/rating.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-rating-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <mat-card *ngIf="ride && userToRate">
      <mat-card-header>
        <mat-card-title>Rate {{ userToRate.full_name || userToRate.email }}</mat-card-title>
        <mat-card-subtitle>
          How was your ride from {{ ride.pickup_location }} to {{ ride.dropoff_location }}?
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <form [formGroup]="ratingForm" (ngSubmit)="onSubmit()">
          <div class="star-rating">
            <button type="button" mat-icon-button *ngFor="let star of stars; let i = index" 
                    [color]="i < selectedRating ? 'accent' : ''"
                    (click)="selectRating(i + 1)">
              <mat-icon>{{ i < selectedRating ? 'star' : 'star_border' }}</mat-icon>
            </button>
          </div>
          
          <mat-form-field appearance="outline" class="feedback-field">
            <mat-label>Feedback (optional)</mat-label>
            <textarea matInput formControlName="feedback" rows="4" placeholder="Share your experience..."></textarea>
          </mat-form-field>
          
          <div class="form-actions">
            <button mat-button type="button" (click)="onCancel()">Cancel</button>
            <button mat-raised-button color="primary" type="submit" [disabled]="ratingForm.invalid || submitting">
              Submit Rating
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .star-rating {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }
    
    .feedback-field {
      width: 100%;
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
    
    .form-actions button {
      margin-left: 10px;
    }
  `]
})
export class RatingFormComponent implements OnInit {
  @Input() ride!: Ride;
  @Input() userToRate!: User;
  @Output() ratingSubmitted = new EventEmitter<boolean>();
  @Output() ratingCancelled = new EventEmitter<void>();
  
  ratingForm!: FormGroup;
  stars = [1, 2, 3, 4, 5];
  selectedRating = 0;
  submitting = false;
  currentUser: User | null = null;
  
  constructor(
    private fb: FormBuilder,
    private ratingService: RatingService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}
  
  ngOnInit(): void {
    this.initForm();
    this.loadCurrentUser();
  }
  
  private async loadCurrentUser(): Promise<void> {
    this.currentUser = await this.authService.getCurrentUser();
  }
  
  private initForm(): void {
    this.ratingForm = this.fb.group({
      rating: [0, [Validators.required, Validators.min(1), Validators.max(5)]],
      feedback: ['']
    });
  }
  
  selectRating(rating: number): void {
    this.selectedRating = rating;
    this.ratingForm.patchValue({ rating });
  }
  
  async onSubmit(): Promise<void> {
    if (this.ratingForm.invalid || !this.currentUser) {
      return;
    }
    
    this.submitting = true;
    
    try {
      // Check if user has already rated this ride
      const hasRated = await this.ratingService.hasUserRated(
        this.ride.id,
        this.currentUser.id,
        this.userToRate.id
      );
      
      if (hasRated) {
        this.snackBar.open('You have already rated this ride', 'Close', { duration: 3000 });
        this.ratingSubmitted.emit(false);
        return;
      }
      
      // Submit the rating
      await this.ratingService.submitRating(
        this.ride.id,
        this.currentUser.id,
        this.userToRate.id,
        this.ratingForm.value.rating,
        this.ratingForm.value.feedback
      );
      
      this.snackBar.open('Rating submitted successfully', 'Close', { duration: 3000 });
      this.ratingSubmitted.emit(true);
    } catch (error) {
      console.error('Error submitting rating:', error);
      this.snackBar.open('Failed to submit rating', 'Close', { duration: 3000 });
      this.ratingSubmitted.emit(false);
    } finally {
      this.submitting = false;
    }
  }
  
  onCancel(): void {
    this.ratingCancelled.emit();
  }
}
