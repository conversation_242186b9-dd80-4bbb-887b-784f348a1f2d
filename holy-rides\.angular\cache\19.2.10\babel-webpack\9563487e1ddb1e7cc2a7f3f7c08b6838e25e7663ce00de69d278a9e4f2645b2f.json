{"ast": null, "code": "export class StorageError extends Error {\n  constructor(message) {\n    super(message);\n    this.__isStorageError = true;\n    this.name = 'StorageError';\n  }\n}\nexport function isStorageError(error) {\n  return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n  constructor(message, status) {\n    super(message);\n    this.name = 'StorageApiError';\n    this.status = status;\n  }\n  toJSON() {\n    return {\n      name: this.name,\n      message: this.message,\n      status: this.status\n    };\n  }\n}\nexport class StorageUnknownError extends StorageError {\n  constructor(message, originalError) {\n    super(message);\n    this.name = 'StorageUnknownError';\n    this.originalError = originalError;\n  }\n}", "map": {"version": 3, "names": ["StorageError", "Error", "constructor", "message", "__isStorageError", "name", "isStorageError", "error", "StorageApiError", "status", "toJSON", "StorageUnknownError", "originalError"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/storage-js/dist/module/lib/errors.js"], "sourcesContent": ["export class StorageError extends Error {\n    constructor(message) {\n        super(message);\n        this.__isStorageError = true;\n        this.name = 'StorageError';\n    }\n}\nexport function isStorageError(error) {\n    return typeof error === 'object' && error !== null && '__isStorageError' in error;\n}\nexport class StorageApiError extends StorageError {\n    constructor(message, status) {\n        super(message);\n        this.name = 'StorageApiError';\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n        };\n    }\n}\nexport class StorageUnknownError extends StorageError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'StorageUnknownError';\n        this.originalError = originalError;\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,YAAY,SAASC,KAAK,CAAC;EACpCC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,IAAI,GAAG,cAAc;EAC9B;AACJ;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EAClC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,kBAAkB,IAAIA,KAAK;AACrF;AACA,OAAO,MAAMC,eAAe,SAASR,YAAY,CAAC;EAC9CE,WAAWA,CAACC,OAAO,EAAEM,MAAM,EAAE;IACzB,KAAK,CAACN,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,iBAAiB;IAC7B,IAAI,CAACI,MAAM,GAAGA,MAAM;EACxB;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO;MACHL,IAAI,EAAE,IAAI,CAACA,IAAI;MACfF,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBM,MAAM,EAAE,IAAI,CAACA;IACjB,CAAC;EACL;AACJ;AACA,OAAO,MAAME,mBAAmB,SAASX,YAAY,CAAC;EAClDE,WAAWA,CAACC,OAAO,EAAES,aAAa,EAAE;IAChC,KAAK,CAACT,OAAO,CAAC;IACd,IAAI,CAACE,IAAI,GAAG,qBAAqB;IACjC,IAAI,CAACO,aAAa,GAAGA,aAAa;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}