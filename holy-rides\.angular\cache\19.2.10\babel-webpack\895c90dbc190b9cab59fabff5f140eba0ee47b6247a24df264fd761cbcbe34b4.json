{"ast": null, "code": "export const version = '2.11.2';", "map": {"version": 3, "names": ["version"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/lib/version.js"], "sourcesContent": ["export const version = '2.11.2';\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}