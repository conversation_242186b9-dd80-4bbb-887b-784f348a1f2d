{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app-shell", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/index.html", "/manifest.webmanifest"]}}, {"name": "app-assets", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(svg|cur|jpg|jpeg|png|webp|gif|otf|ttf|woff|woff2)"]}}], "dataGroups": [{"name": "supabase-auth", "urls": ["https://*.supabase.co/auth/**", "https://*.supabase.co/auth/v1/**", "https://*.supabase.co/rest/v1/auth/**"], "cacheConfig": {"strategy": "network-only", "maxSize": 0, "maxAge": "0u"}}, {"name": "supabase-api", "urls": ["https://*.supabase.co/rest/v1/rides*", "https://*.supabase.co/rest/v1/users*", "https://*.supabase.co/rest/v1/profiles*"], "cacheConfig": {"strategy": "network-only", "maxSize": 0, "maxAge": "0u"}}, {"name": "supabase-static", "urls": ["https://*.supabase.co/rest/v1/static-data*"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "7d"}}], "navigationUrls": ["/**", "!/**/*__*", "!/**/*__*/**"]}