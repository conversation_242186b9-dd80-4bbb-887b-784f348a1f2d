{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  _platform = inject(Platform);\n  _listenerCleanups;\n  /** Emits whenever an input modality is detected. */\n  modalityDetected;\n  /** Emits when the input modality changes. */\n  modalityChanged;\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget = null;\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  _modality = new BehaviorSubject(null);\n  /** Options for this InputModalityDetector. */\n  _options;\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  _lastTouchMs = 0;\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  _onKeydown = event => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onMousedown = event => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onTouchstart = event => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputModalityDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputModalityDetector,\n    factory: InputModalityDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _inputModalityDetector = inject(InputModalityDetector);\n  /** The focus origin that the next focus event is a result of. */\n  _origin = null;\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  _lastFocusOrigin;\n  /** Whether the window has just been focused. */\n  _windowFocused = false;\n  /** The timeout id of the window focus timeout. */\n  _windowFocusTimeoutId;\n  /** The timeout id of the origin clearing timeout. */\n  _originTimeoutId;\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  _originFromTouchInteraction = false;\n  /** Map of elements being monitored to their info. */\n  _elementInfo = new Map();\n  /** The number of elements currently being monitored. */\n  _monitoredElementCount = 0;\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  _rootNodeFocusListenerCount = new Map();\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  _detectionMode;\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n  };\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** Subject for stopping our InputModalityDetector subscription. */\n  _stopInputModalityDetector = new Subject();\n  constructor() {\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _rootNodeFocusAndBlurListener = event => {\n    const target = _getEventTarget(event);\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event, element);\n      } else {\n        this._onBlur(event, element);\n      }\n    }\n  };\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusMonitor,\n    factory: FocusMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _monitorSubscription;\n  _focusOrigin = null;\n  cdkFocusChange = new EventEmitter();\n  constructor() {}\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkMonitorFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkMonitorFocus,\n    selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n    outputs: {\n      cdkFocusChange: \"cdkFocusChange\"\n    },\n    exportAs: [\"cdkMonitorFocus\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "NgZone", "RendererFactory2", "Injectable", "ElementRef", "EventEmitter", "Directive", "Output", "BehaviorSubject", "Subject", "of", "skip", "distinctUntilChanged", "takeUntil", "DOCUMENT", "i", "isFakeMousedownFromScreenReader", "a", "isFakeTouchstartFromScreenReader", "d", "ALT", "C", "CONTROL", "M", "MAC_META", "e", "META", "f", "SHIFT", "_", "_getEventTarget", "_getShadowRoot", "_bindEventWithOptions", "P", "Platform", "n", "normalizePassiveListenerOptions", "coerceElement", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "_platform", "_listenerCleanups", "modalityDetected", "modalityChanged", "mostRecentModality", "_modality", "value", "_mostRecentTarget", "_options", "_lastTouchMs", "_onKeydown", "event", "some", "keyCode", "next", "_onMousedown", "Date", "now", "_onTouchstart", "constructor", "ngZone", "document", "options", "optional", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "runOutsideAngular", "ngOnDestroy", "complete", "for<PERSON>ach", "cleanup", "ɵfac", "InputModalityDetector_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "FocusMonitorDetectionMode", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_ngZone", "_inputModalityDetector", "_origin", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_windowFocused", "_windowFocusTimeoutId", "_originTimeoutId", "_originFromTouchInteraction", "_elementInfo", "Map", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_detectionMode", "_windowFocusListener", "setTimeout", "_document", "_stopInputModalityDetector", "detectionMode", "IMMEDIATE", "_rootNodeFocusAndBlurListener", "target", "element", "parentElement", "_onFocus", "_onBlur", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "nativeElement", "nodeType", "rootNode", "_getDocument", "cachedInfo", "get", "subject", "info", "set", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "delete", "_removeGlobalListeners", "focusVia", "origin", "focusedElement", "activeElement", "_getClosestElementsInfo", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "focus", "_info", "_getWindow", "doc", "defaultView", "window", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_isLastInteractionFromInputLabel", "EVENTUAL", "contains", "classList", "toggle", "isFromInteraction", "clearTimeout", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "observers", "length", "run", "rootNodeFocusListeners", "addEventListener", "subscribe", "modality", "has", "removeEventListener", "results", "push", "mostRecentTarget", "nodeName", "disabled", "labels", "FocusMonitor_Factory", "CdkMonitorFocus", "_elementRef", "_focusMonitor", "_monitorSubscription", "_focus<PERSON><PERSON>in", "cdkFocusChange", "<PERSON><PERSON><PERSON><PERSON>", "ngAfterViewInit", "hasAttribute", "emit", "unsubscribe", "CdkMonitorFocus_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "exportAs", "selector", "F", "I", "b", "c"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/focus-monitor-e2l_RpN3.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-DWOdFTFz.mjs';\nimport { d as ALT, C as CONTROL, M as MAC_META, e as META, f as SHIFT } from './keycodes-CpHkExLC.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-esHZRgIN.mjs';\nimport { a as coerceElement } from './element-x4z00URv.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n    passive: true,\n    capture: true,\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    _platform = inject(Platform);\n    _listenerCleanups;\n    /** Emits whenever an input modality is detected. */\n    modalityDetected;\n    /** Emits when the input modality changes. */\n    modalityChanged;\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n    _mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n    _modality = new BehaviorSubject(null);\n    /** Options for this InputModalityDetector. */\n    _options;\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n    _lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n    _onKeydown = (event) => {\n        // If this is one of the keys we should ignore, then ignore it and don't update the input\n        // modality to keyboard.\n        if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n            return;\n        }\n        this._modality.next('keyboard');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onMousedown = (event) => {\n        // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n        // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n        // after the previous touch event.\n        if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n            return;\n        }\n        // Fake mousedown events are fired by some screen readers when controls are activated by the\n        // screen reader. Attribute them to keyboard input modality.\n        this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n    _onTouchstart = (event) => {\n        // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n        // events are fired. Again, attribute to keyboard input modality.\n        if (isFakeTouchstartFromScreenReader(event)) {\n            this._modality.next('keyboard');\n            return;\n        }\n        // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n        // triggered via mouse vs touch.\n        this._lastTouchMs = Date.now();\n        this._modality.next('touch');\n        this._mostRecentTarget = _getEventTarget(event);\n    };\n    constructor() {\n        const ngZone = inject(NgZone);\n        const document = inject(DOCUMENT);\n        const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, { optional: true });\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (this._platform.isBrowser) {\n            const renderer = inject(RendererFactory2).createRenderer(null, null);\n            this._listenerCleanups = ngZone.runOutsideAngular(() => {\n                return [\n                    _bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions),\n                    _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions),\n                    _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions),\n                ];\n            });\n        }\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        this._listenerCleanups?.forEach(cleanup => cleanup());\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n    /**\n     * Any mousedown, keydown, or touchstart event that happened in the previous\n     * tick or the current tick will be used to assign a focus event's origin (to\n     * either mouse, keyboard, or touch). This is the default option.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n    /**\n     * A focus event's origin is always attributed to the last corresponding\n     * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n     */\n    FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _inputModalityDetector = inject(InputModalityDetector);\n    /** The focus origin that the next focus event is a result of. */\n    _origin = null;\n    /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n    _lastFocusOrigin;\n    /** Whether the window has just been focused. */\n    _windowFocused = false;\n    /** The timeout id of the window focus timeout. */\n    _windowFocusTimeoutId;\n    /** The timeout id of the origin clearing timeout. */\n    _originTimeoutId;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n    _originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n    _elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n    _monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n    _rootNodeFocusListenerCount = new Map();\n    /**\n     * The specified detection mode, used for attributing the origin of a focus\n     * event.\n     */\n    _detectionMode;\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _windowFocusListener = () => {\n        // Make a note of when the window regains focus, so we can\n        // restore the origin info for the focused element.\n        this._windowFocused = true;\n        this._windowFocusTimeoutId = setTimeout(() => (this._windowFocused = false));\n    };\n    /** Used to reference correct document/window */\n    _document = inject(DOCUMENT, { optional: true });\n    /** Subject for stopping our InputModalityDetector subscription. */\n    _stopInputModalityDetector = new Subject();\n    constructor() {\n        const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n            optional: true,\n        });\n        this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n    }\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n    _rootNodeFocusAndBlurListener = (event) => {\n        const target = _getEventTarget(event);\n        // We need to walk up the ancestor chain in order to support `checkChildren`.\n        for (let element = target; element; element = element.parentElement) {\n            if (event.type === 'focus') {\n                this._onFocus(event, element);\n            }\n            else {\n                this._onBlur(event, element);\n            }\n        }\n    };\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            // Note: we don't want the observable to emit at all so we don't pass any parameters.\n            return of();\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        if (this._windowFocused && this._lastFocusOrigin) {\n            return this._lastFocusOrigin;\n        }\n        // If the interaction is coming from an input label, we consider it a mouse interactions.\n        // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n        // our detection, because all our assumptions are for `mousedown`. We need to handle this\n        // special case, because it's very common for checkboxes and radio buttons.\n        if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n            return 'mouse';\n        }\n        return 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === FocusMonitorDetectionMode.EVENTUAL ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo, null);\n    }\n    _emitOrigin(info, origin) {\n        if (info.subject.observers.length) {\n            this._ngZone.run(() => info.subject.next(origin));\n        }\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n    /**\n     * Returns whether an interaction is likely to have come from the user clicking the `label` of\n     * an `input` or `textarea` in order to focus it.\n     * @param focusEventTarget Target currently receiving focus.\n     */\n    _isLastInteractionFromInputLabel(focusEventTarget) {\n        const { _mostRecentTarget: mostRecentTarget, mostRecentModality } = this._inputModalityDetector;\n        // If the last interaction used the mouse on an element contained by one of the labels\n        // of an `input`/`textarea` that is currently focused, it is very likely that the\n        // user redirected focus using the label.\n        if (mostRecentModality !== 'mouse' ||\n            !mostRecentTarget ||\n            mostRecentTarget === focusEventTarget ||\n            (focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA') ||\n            focusEventTarget.disabled) {\n            return false;\n        }\n        const labels = focusEventTarget.labels;\n        if (labels) {\n            for (let i = 0; i < labels.length; i++) {\n                if (labels[i].contains(mostRecentTarget)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    _elementRef = inject(ElementRef);\n    _focusMonitor = inject(FocusMonitor);\n    _monitorSubscription;\n    _focusOrigin = null;\n    cdkFocusChange = new EventEmitter();\n    constructor() { }\n    get focusOrigin() {\n        return this._focusOrigin;\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => {\n            this._focusOrigin = origin;\n            this.cdkFocusChange.emit(origin);\n        });\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkMonitorFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkMonitorFocus, isStandalone: true, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, exportAs: [\"cdkMonitorFocus\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                    exportAs: 'cdkMonitorFocus',\n                }]\n        }], ctorParameters: () => [], propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\nexport { CdkMonitorFocus as C, FocusMonitor as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FocusMonitorDetectionMode as c, FOCUS_MONITOR_DEFAULT_OPTIONS as d };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACzI,SAASC,eAAe,EAAEC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AACnD,SAASC,IAAI,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AACtE,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,CAAC,IAAIC,+BAA+B,EAAEC,CAAC,IAAIC,gCAAgC,QAAQ,qCAAqC;AACjI,SAASC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,KAAK,QAAQ,yBAAyB;AACtG,SAASC,CAAC,IAAIC,eAAe,EAAEb,CAAC,IAAIc,cAAc,QAAQ,2BAA2B;AACrF,SAASF,CAAC,IAAIG,qBAAqB,QAAQ,wCAAwC;AACnF,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,+BAA+B,QAAQ,kCAAkC;AACvF,SAASnB,CAAC,IAAIoB,aAAa,QAAQ,wBAAwB;;AAE3D;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,IAAIvC,cAAc,CAAC,qCAAqC,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwC,uCAAuC,GAAG;EAC5CC,UAAU,EAAE,CAACpB,GAAG,EAAEE,OAAO,EAAEE,QAAQ,EAAEE,IAAI,EAAEE,KAAK;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,eAAe,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAG;EACjCC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBC,SAAS,GAAG9C,MAAM,CAACkC,QAAQ,CAAC;EAC5Ba,iBAAiB;EACjB;EACAC,gBAAgB;EAChB;EACAC,eAAe;EACf;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,SAAS,CAACC,KAAK;EAC/B;EACA;AACJ;AACA;AACA;EACIC,iBAAiB,GAAG,IAAI;EACxB;EACAF,SAAS,GAAG,IAAI3C,eAAe,CAAC,IAAI,CAAC;EACrC;EACA8C,QAAQ;EACR;AACJ;AACA;AACA;EACIC,YAAY,GAAG,CAAC;EAChB;AACJ;AACA;AACA;EACIC,UAAU,GAAIC,KAAK,IAAK;IACpB;IACA;IACA,IAAI,IAAI,CAACH,QAAQ,EAAEd,UAAU,EAAEkB,IAAI,CAACC,OAAO,IAAIA,OAAO,KAAKF,KAAK,CAACE,OAAO,CAAC,EAAE;MACvE;IACJ;IACA,IAAI,CAACR,SAAS,CAACS,IAAI,CAAC,UAAU,CAAC;IAC/B,IAAI,CAACP,iBAAiB,GAAGvB,eAAe,CAAC2B,KAAK,CAAC;EACnD,CAAC;EACD;AACJ;AACA;AACA;EACII,YAAY,GAAIJ,KAAK,IAAK;IACtB;IACA;IACA;IACA,IAAIK,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACR,YAAY,GAAGd,eAAe,EAAE;MAClD;IACJ;IACA;IACA;IACA,IAAI,CAACU,SAAS,CAACS,IAAI,CAAC5C,+BAA+B,CAACyC,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,CAAC;IAClF,IAAI,CAACJ,iBAAiB,GAAGvB,eAAe,CAAC2B,KAAK,CAAC;EACnD,CAAC;EACD;AACJ;AACA;AACA;EACIO,aAAa,GAAIP,KAAK,IAAK;IACvB;IACA;IACA,IAAIvC,gCAAgC,CAACuC,KAAK,CAAC,EAAE;MACzC,IAAI,CAACN,SAAS,CAACS,IAAI,CAAC,UAAU,CAAC;MAC/B;IACJ;IACA;IACA;IACA,IAAI,CAACL,YAAY,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACZ,SAAS,CAACS,IAAI,CAAC,OAAO,CAAC;IAC5B,IAAI,CAACP,iBAAiB,GAAGvB,eAAe,CAAC2B,KAAK,CAAC;EACnD,CAAC;EACDQ,WAAWA,CAAA,EAAG;IACV,MAAMC,MAAM,GAAGlE,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAMkE,QAAQ,GAAGnE,MAAM,CAACc,QAAQ,CAAC;IACjC,MAAMsD,OAAO,GAAGpE,MAAM,CAACsC,+BAA+B,EAAE;MAAE+B,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC3E,IAAI,CAACf,QAAQ,GAAG;MACZ,GAAGf,uCAAuC;MAC1C,GAAG6B;IACP,CAAC;IACD;IACA,IAAI,CAACpB,gBAAgB,GAAG,IAAI,CAACG,SAAS,CAACmB,IAAI,CAAC3D,IAAI,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACsC,eAAe,GAAG,IAAI,CAACD,gBAAgB,CAACsB,IAAI,CAAC1D,oBAAoB,CAAC,CAAC,CAAC;IACzE;IACA;IACA,IAAI,IAAI,CAACkC,SAAS,CAACyB,SAAS,EAAE;MAC1B,MAAMC,QAAQ,GAAGxE,MAAM,CAACE,gBAAgB,CAAC,CAACuE,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;MACpE,IAAI,CAAC1B,iBAAiB,GAAGmB,MAAM,CAACQ,iBAAiB,CAAC,MAAM;QACpD,OAAO,CACH1C,qBAAqB,CAACwC,QAAQ,EAAEL,QAAQ,EAAE,SAAS,EAAE,IAAI,CAACX,UAAU,EAAEd,4BAA4B,CAAC,EACnGV,qBAAqB,CAACwC,QAAQ,EAAEL,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACN,YAAY,EAAEnB,4BAA4B,CAAC,EACvGV,qBAAqB,CAACwC,QAAQ,EAAEL,QAAQ,EAAE,YAAY,EAAE,IAAI,CAACH,aAAa,EAAEtB,4BAA4B,CAAC,CAC5G;MACL,CAAC,CAAC;IACN;EACJ;EACAiC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxB,SAAS,CAACyB,QAAQ,CAAC,CAAC;IACzB,IAAI,CAAC7B,iBAAiB,EAAE8B,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;EACzD;EACA,OAAOC,IAAI,YAAAC,8BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFpC,qBAAqB;EAAA;EACxH,OAAOqC,KAAK,kBAD6EpF,EAAE,CAAAqF,kBAAA;IAAAC,KAAA,EACYvC,qBAAqB;IAAAwC,OAAA,EAArBxC,qBAAqB,CAAAkC,IAAA;IAAAO,UAAA,EAAc;EAAM;AACpJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH6FzF,EAAE,CAAA0F,iBAAA,CAGJ3C,qBAAqB,EAAc,CAAC;IACnH4C,IAAI,EAAEtF,UAAU;IAChBuF,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,IAAIK,yBAAyB;AAC7B,CAAC,UAAUA,yBAAyB,EAAE;EAClC;AACJ;AACA;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACnF;AACJ;AACA;AACA;EACIA,yBAAyB,CAACA,yBAAyB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AACrF,CAAC,EAAEA,yBAAyB,KAAKA,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjE;AACA,MAAMC,6BAA6B,GAAG,IAAI7F,cAAc,CAAC,mCAAmC,CAAC;AAC7F;AACA;AACA;AACA;AACA,MAAM8F,2BAA2B,GAAGzD,+BAA+B,CAAC;EAChEO,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAMkD,YAAY,CAAC;EACfC,OAAO,GAAG/F,MAAM,CAACC,MAAM,CAAC;EACxB6C,SAAS,GAAG9C,MAAM,CAACkC,QAAQ,CAAC;EAC5B8D,sBAAsB,GAAGhG,MAAM,CAAC6C,qBAAqB,CAAC;EACtD;EACAoD,OAAO,GAAG,IAAI;EACd;EACAC,gBAAgB;EAChB;EACAC,cAAc,GAAG,KAAK;EACtB;EACAC,qBAAqB;EACrB;EACAC,gBAAgB;EAChB;AACJ;AACA;AACA;EACIC,2BAA2B,GAAG,KAAK;EACnC;EACAC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EACxB;EACAC,sBAAsB,GAAG,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;EACIC,2BAA2B,GAAG,IAAIF,GAAG,CAAC,CAAC;EACvC;AACJ;AACA;AACA;EACIG,cAAc;EACd;AACJ;AACA;AACA;EACIC,oBAAoB,GAAGA,CAAA,KAAM;IACzB;IACA;IACA,IAAI,CAACT,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,qBAAqB,GAAGS,UAAU,CAAC,MAAO,IAAI,CAACV,cAAc,GAAG,KAAM,CAAC;EAChF,CAAC;EACD;EACAW,SAAS,GAAG9G,MAAM,CAACc,QAAQ,EAAE;IAAEuD,QAAQ,EAAE;EAAK,CAAC,CAAC;EAChD;EACA0C,0BAA0B,GAAG,IAAItG,OAAO,CAAC,CAAC;EAC1CwD,WAAWA,CAAA,EAAG;IACV,MAAMG,OAAO,GAAGpE,MAAM,CAAC4F,6BAA6B,EAAE;MAClDvB,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAI,CAACsC,cAAc,GAAGvC,OAAO,EAAE4C,aAAa,IAAIrB,yBAAyB,CAACsB,SAAS;EACvF;EACA;AACJ;AACA;AACA;EACIC,6BAA6B,GAAIzD,KAAK,IAAK;IACvC,MAAM0D,MAAM,GAAGrF,eAAe,CAAC2B,KAAK,CAAC;IACrC;IACA,KAAK,IAAI2D,OAAO,GAAGD,MAAM,EAAEC,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACC,aAAa,EAAE;MACjE,IAAI5D,KAAK,CAACgC,IAAI,KAAK,OAAO,EAAE;QACxB,IAAI,CAAC6B,QAAQ,CAAC7D,KAAK,EAAE2D,OAAO,CAAC;MACjC,CAAC,MACI;QACD,IAAI,CAACG,OAAO,CAAC9D,KAAK,EAAE2D,OAAO,CAAC;MAChC;IACJ;EACJ,CAAC;EACDI,OAAOA,CAACJ,OAAO,EAAEK,aAAa,GAAG,KAAK,EAAE;IACpC,MAAMC,aAAa,GAAGrF,aAAa,CAAC+E,OAAO,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAACtE,SAAS,CAACyB,SAAS,IAAImD,aAAa,CAACC,QAAQ,KAAK,CAAC,EAAE;MAC3D;MACA,OAAOjH,EAAE,CAAC,CAAC;IACf;IACA;IACA;IACA;IACA,MAAMkH,QAAQ,GAAG7F,cAAc,CAAC2F,aAAa,CAAC,IAAI,IAAI,CAACG,YAAY,CAAC,CAAC;IACrE,MAAMC,UAAU,GAAG,IAAI,CAACvB,YAAY,CAACwB,GAAG,CAACL,aAAa,CAAC;IACvD;IACA,IAAII,UAAU,EAAE;MACZ,IAAIL,aAAa,EAAE;QACf;QACA;QACA;QACAK,UAAU,CAACL,aAAa,GAAG,IAAI;MACnC;MACA,OAAOK,UAAU,CAACE,OAAO;IAC7B;IACA;IACA,MAAMC,IAAI,GAAG;MACTR,aAAa,EAAEA,aAAa;MAC5BO,OAAO,EAAE,IAAIvH,OAAO,CAAC,CAAC;MACtBmH;IACJ,CAAC;IACD,IAAI,CAACrB,YAAY,CAAC2B,GAAG,CAACR,aAAa,EAAEO,IAAI,CAAC;IAC1C,IAAI,CAACE,wBAAwB,CAACF,IAAI,CAAC;IACnC,OAAOA,IAAI,CAACD,OAAO;EACvB;EACAI,cAAcA,CAAChB,OAAO,EAAE;IACpB,MAAMM,aAAa,GAAGrF,aAAa,CAAC+E,OAAO,CAAC;IAC5C,MAAMiB,WAAW,GAAG,IAAI,CAAC9B,YAAY,CAACwB,GAAG,CAACL,aAAa,CAAC;IACxD,IAAIW,WAAW,EAAE;MACbA,WAAW,CAACL,OAAO,CAACpD,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAAC0D,WAAW,CAACZ,aAAa,CAAC;MAC/B,IAAI,CAACnB,YAAY,CAACgC,MAAM,CAACb,aAAa,CAAC;MACvC,IAAI,CAACc,sBAAsB,CAACH,WAAW,CAAC;IAC5C;EACJ;EACAI,QAAQA,CAACrB,OAAO,EAAEsB,MAAM,EAAEtE,OAAO,EAAE;IAC/B,MAAMsD,aAAa,GAAGrF,aAAa,CAAC+E,OAAO,CAAC;IAC5C,MAAMuB,cAAc,GAAG,IAAI,CAACd,YAAY,CAAC,CAAC,CAACe,aAAa;IACxD;IACA;IACA;IACA,IAAIlB,aAAa,KAAKiB,cAAc,EAAE;MAClC,IAAI,CAACE,uBAAuB,CAACnB,aAAa,CAAC,CAAC7C,OAAO,CAAC,CAAC,CAACiE,cAAc,EAAEb,IAAI,CAAC,KAAK,IAAI,CAACc,cAAc,CAACD,cAAc,EAAEJ,MAAM,EAAET,IAAI,CAAC,CAAC;IACtI,CAAC,MACI;MACD,IAAI,CAACe,UAAU,CAACN,MAAM,CAAC;MACvB;MACA,IAAI,OAAOhB,aAAa,CAACuB,KAAK,KAAK,UAAU,EAAE;QAC3CvB,aAAa,CAACuB,KAAK,CAAC7E,OAAO,CAAC;MAChC;IACJ;EACJ;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4B,YAAY,CAAC1B,OAAO,CAAC,CAACqE,KAAK,EAAE9B,OAAO,KAAK,IAAI,CAACgB,cAAc,CAAChB,OAAO,CAAC,CAAC;EAC/E;EACA;EACAS,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACf,SAAS,IAAI3C,QAAQ;EACrC;EACA;EACAgF,UAAUA,CAAA,EAAG;IACT,MAAMC,GAAG,GAAG,IAAI,CAACvB,YAAY,CAAC,CAAC;IAC/B,OAAOuB,GAAG,CAACC,WAAW,IAAIC,MAAM;EACpC;EACAC,eAAeA,CAACC,gBAAgB,EAAE;IAC9B,IAAI,IAAI,CAACvD,OAAO,EAAE;MACd;MACA;MACA,IAAI,IAAI,CAACK,2BAA2B,EAAE;QAClC,OAAO,IAAI,CAACmD,0BAA0B,CAACD,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS;MAClF,CAAC,MACI;QACD,OAAO,IAAI,CAACvD,OAAO;MACvB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACE,cAAc,IAAI,IAAI,CAACD,gBAAgB,EAAE;MAC9C,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIsD,gBAAgB,IAAI,IAAI,CAACE,gCAAgC,CAACF,gBAAgB,CAAC,EAAE;MAC7E,OAAO,OAAO;IAClB;IACA,OAAO,SAAS;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,0BAA0BA,CAACD,gBAAgB,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI,CAAC7C,cAAc,KAAKhB,yBAAyB,CAACgE,QAAQ,IAC9D,CAAC,CAACH,gBAAgB,EAAEI,QAAQ,CAAC,IAAI,CAAC5D,sBAAsB,CAAC3C,iBAAiB,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIiF,WAAWA,CAAClB,OAAO,EAAEsB,MAAM,EAAE;IACzBtB,OAAO,CAACyC,SAAS,CAACC,MAAM,CAAC,aAAa,EAAE,CAAC,CAACpB,MAAM,CAAC;IACjDtB,OAAO,CAACyC,SAAS,CAACC,MAAM,CAAC,mBAAmB,EAAEpB,MAAM,KAAK,OAAO,CAAC;IACjEtB,OAAO,CAACyC,SAAS,CAACC,MAAM,CAAC,sBAAsB,EAAEpB,MAAM,KAAK,UAAU,CAAC;IACvEtB,OAAO,CAACyC,SAAS,CAACC,MAAM,CAAC,mBAAmB,EAAEpB,MAAM,KAAK,OAAO,CAAC;IACjEtB,OAAO,CAACyC,SAAS,CAACC,MAAM,CAAC,qBAAqB,EAAEpB,MAAM,KAAK,SAAS,CAAC;EACzE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,UAAUA,CAACN,MAAM,EAAEqB,iBAAiB,GAAG,KAAK,EAAE;IAC1C,IAAI,CAAChE,OAAO,CAACrB,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACuB,OAAO,GAAGyC,MAAM;MACrB,IAAI,CAACpC,2BAA2B,GAAGoC,MAAM,KAAK,OAAO,IAAIqB,iBAAiB;MAC1E;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACpD,cAAc,KAAKhB,yBAAyB,CAACsB,SAAS,EAAE;QAC7D+C,YAAY,CAAC,IAAI,CAAC3D,gBAAgB,CAAC;QACnC,MAAM4D,EAAE,GAAG,IAAI,CAAC3D,2BAA2B,GAAG7D,eAAe,GAAG,CAAC;QACjE,IAAI,CAAC4D,gBAAgB,GAAGQ,UAAU,CAAC,MAAO,IAAI,CAACZ,OAAO,GAAG,IAAK,EAAEgE,EAAE,CAAC;MACvE;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI3C,QAAQA,CAAC7D,KAAK,EAAE2D,OAAO,EAAE;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAC9B,YAAY,CAACwB,GAAG,CAACX,OAAO,CAAC;IAClD,MAAMoC,gBAAgB,GAAG1H,eAAe,CAAC2B,KAAK,CAAC;IAC/C,IAAI,CAAC4E,WAAW,IAAK,CAACA,WAAW,CAACZ,aAAa,IAAIL,OAAO,KAAKoC,gBAAiB,EAAE;MAC9E;IACJ;IACA,IAAI,CAACT,cAAc,CAAC3B,OAAO,EAAE,IAAI,CAACmC,eAAe,CAACC,gBAAgB,CAAC,EAAEnB,WAAW,CAAC;EACrF;EACA;AACJ;AACA;AACA;AACA;EACId,OAAOA,CAAC9D,KAAK,EAAE2D,OAAO,EAAE;IACpB;IACA;IACA,MAAMiB,WAAW,GAAG,IAAI,CAAC9B,YAAY,CAACwB,GAAG,CAACX,OAAO,CAAC;IAClD,IAAI,CAACiB,WAAW,IACXA,WAAW,CAACZ,aAAa,IACtBhE,KAAK,CAACyG,aAAa,YAAYC,IAAI,IACnC/C,OAAO,CAACwC,QAAQ,CAACnG,KAAK,CAACyG,aAAa,CAAE,EAAE;MAC5C;IACJ;IACA,IAAI,CAAC5B,WAAW,CAAClB,OAAO,CAAC;IACzB,IAAI,CAACgD,WAAW,CAAC/B,WAAW,EAAE,IAAI,CAAC;EACvC;EACA+B,WAAWA,CAACnC,IAAI,EAAES,MAAM,EAAE;IACtB,IAAIT,IAAI,CAACD,OAAO,CAACqC,SAAS,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACvE,OAAO,CAACwE,GAAG,CAAC,MAAMtC,IAAI,CAACD,OAAO,CAACpE,IAAI,CAAC8E,MAAM,CAAC,CAAC;IACrD;EACJ;EACAP,wBAAwBA,CAACE,WAAW,EAAE;IAClC,IAAI,CAAC,IAAI,CAACvF,SAAS,CAACyB,SAAS,EAAE;MAC3B;IACJ;IACA,MAAMqD,QAAQ,GAAGS,WAAW,CAACT,QAAQ;IACrC,MAAM4C,sBAAsB,GAAG,IAAI,CAAC9D,2BAA2B,CAACqB,GAAG,CAACH,QAAQ,CAAC,IAAI,CAAC;IAClF,IAAI,CAAC4C,sBAAsB,EAAE;MACzB,IAAI,CAACzE,OAAO,CAACrB,iBAAiB,CAAC,MAAM;QACjCkD,QAAQ,CAAC6C,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACvD,6BAA6B,EAAErB,2BAA2B,CAAC;QACnG+B,QAAQ,CAAC6C,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACvD,6BAA6B,EAAErB,2BAA2B,CAAC;MACtG,CAAC,CAAC;IACN;IACA,IAAI,CAACa,2BAA2B,CAACwB,GAAG,CAACN,QAAQ,EAAE4C,sBAAsB,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,EAAE,IAAI,CAAC/D,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA;MACA,IAAI,CAACV,OAAO,CAACrB,iBAAiB,CAAC,MAAM;QACjC,MAAM4E,MAAM,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QAChCG,MAAM,CAACmB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC7D,oBAAoB,CAAC;MAC/D,CAAC,CAAC;MACF;MACA,IAAI,CAACZ,sBAAsB,CAAChD,gBAAgB,CACvCsB,IAAI,CAACzD,SAAS,CAAC,IAAI,CAACkG,0BAA0B,CAAC,CAAC,CAChD2D,SAAS,CAACC,QAAQ,IAAI;QACvB,IAAI,CAAC3B,UAAU,CAAC2B,QAAQ,EAAE,IAAI,CAAC,uBAAuB,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACAnC,sBAAsBA,CAACH,WAAW,EAAE;IAChC,MAAMT,QAAQ,GAAGS,WAAW,CAACT,QAAQ;IACrC,IAAI,IAAI,CAAClB,2BAA2B,CAACkE,GAAG,CAAChD,QAAQ,CAAC,EAAE;MAChD,MAAM4C,sBAAsB,GAAG,IAAI,CAAC9D,2BAA2B,CAACqB,GAAG,CAACH,QAAQ,CAAC;MAC7E,IAAI4C,sBAAsB,GAAG,CAAC,EAAE;QAC5B,IAAI,CAAC9D,2BAA2B,CAACwB,GAAG,CAACN,QAAQ,EAAE4C,sBAAsB,GAAG,CAAC,CAAC;MAC9E,CAAC,MACI;QACD5C,QAAQ,CAACiD,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC3D,6BAA6B,EAAErB,2BAA2B,CAAC;QACtG+B,QAAQ,CAACiD,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC3D,6BAA6B,EAAErB,2BAA2B,CAAC;QACrG,IAAI,CAACa,2BAA2B,CAAC6B,MAAM,CAACX,QAAQ,CAAC;MACrD;IACJ;IACA;IACA,IAAI,CAAC,GAAE,IAAI,CAACnB,sBAAsB,EAAE;MAChC,MAAM6C,MAAM,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;MAChCG,MAAM,CAACuB,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACjE,oBAAoB,CAAC;MAC9D;MACA,IAAI,CAACG,0BAA0B,CAACnD,IAAI,CAAC,CAAC;MACtC;MACAoG,YAAY,CAAC,IAAI,CAAC5D,qBAAqB,CAAC;MACxC4D,YAAY,CAAC,IAAI,CAAC3D,gBAAgB,CAAC;IACvC;EACJ;EACA;EACA0C,cAAcA,CAAC3B,OAAO,EAAEsB,MAAM,EAAEL,WAAW,EAAE;IACzC,IAAI,CAACC,WAAW,CAAClB,OAAO,EAAEsB,MAAM,CAAC;IACjC,IAAI,CAAC0B,WAAW,CAAC/B,WAAW,EAAEK,MAAM,CAAC;IACrC,IAAI,CAACxC,gBAAgB,GAAGwC,MAAM;EAClC;EACA;AACJ;AACA;AACA;AACA;EACIG,uBAAuBA,CAACzB,OAAO,EAAE;IAC7B,MAAM0D,OAAO,GAAG,EAAE;IAClB,IAAI,CAACvE,YAAY,CAAC1B,OAAO,CAAC,CAACoD,IAAI,EAAEa,cAAc,KAAK;MAChD,IAAIA,cAAc,KAAK1B,OAAO,IAAKa,IAAI,CAACR,aAAa,IAAIqB,cAAc,CAACc,QAAQ,CAACxC,OAAO,CAAE,EAAE;QACxF0D,OAAO,CAACC,IAAI,CAAC,CAACjC,cAAc,EAAEb,IAAI,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,OAAO6C,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIpB,gCAAgCA,CAACF,gBAAgB,EAAE;IAC/C,MAAM;MAAEnG,iBAAiB,EAAE2H,gBAAgB;MAAE9H;IAAmB,CAAC,GAAG,IAAI,CAAC8C,sBAAsB;IAC/F;IACA;IACA;IACA,IAAI9C,kBAAkB,KAAK,OAAO,IAC9B,CAAC8H,gBAAgB,IACjBA,gBAAgB,KAAKxB,gBAAgB,IACpCA,gBAAgB,CAACyB,QAAQ,KAAK,OAAO,IAAIzB,gBAAgB,CAACyB,QAAQ,KAAK,UAAW,IACnFzB,gBAAgB,CAAC0B,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,MAAMC,MAAM,GAAG3B,gBAAgB,CAAC2B,MAAM;IACtC,IAAIA,MAAM,EAAE;MACR,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,MAAM,CAACb,MAAM,EAAEvJ,CAAC,EAAE,EAAE;QACpC,IAAIoK,MAAM,CAACpK,CAAC,CAAC,CAAC6I,QAAQ,CAACoB,gBAAgB,CAAC,EAAE;UACtC,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB;EACA,OAAOjG,IAAI,YAAAqG,qBAAAnG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFa,YAAY;EAAA;EAC/G,OAAOZ,KAAK,kBAxZ6EpF,EAAE,CAAAqF,kBAAA;IAAAC,KAAA,EAwZYU,YAAY;IAAAT,OAAA,EAAZS,YAAY,CAAAf,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC3I;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1Z6FzF,EAAE,CAAA0F,iBAAA,CA0ZJM,YAAY,EAAc,CAAC;IAC1GL,IAAI,EAAEtF,UAAU;IAChBuF,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+F,eAAe,CAAC;EAClBC,WAAW,GAAGtL,MAAM,CAACI,UAAU,CAAC;EAChCmL,aAAa,GAAGvL,MAAM,CAAC8F,YAAY,CAAC;EACpC0F,oBAAoB;EACpBC,YAAY,GAAG,IAAI;EACnBC,cAAc,GAAG,IAAIrL,YAAY,CAAC,CAAC;EACnC4D,WAAWA,CAAA,EAAG,CAAE;EAChB,IAAI0H,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,YAAY;EAC5B;EACAG,eAAeA,CAAA,EAAG;IACd,MAAMxE,OAAO,GAAG,IAAI,CAACkE,WAAW,CAAC5D,aAAa;IAC9C,IAAI,CAAC8D,oBAAoB,GAAG,IAAI,CAACD,aAAa,CACzC/D,OAAO,CAACJ,OAAO,EAAEA,OAAO,CAACO,QAAQ,KAAK,CAAC,IAAIP,OAAO,CAACyE,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAC1FnB,SAAS,CAAChC,MAAM,IAAI;MACrB,IAAI,CAAC+C,YAAY,GAAG/C,MAAM;MAC1B,IAAI,CAACgD,cAAc,CAACI,IAAI,CAACpD,MAAM,CAAC;IACpC,CAAC,CAAC;EACN;EACA/D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4G,aAAa,CAACnD,cAAc,CAAC,IAAI,CAACkD,WAAW,CAAC;IACnD,IAAI,IAAI,CAACE,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAACO,WAAW,CAAC,CAAC;IAC3C;EACJ;EACA,OAAOhH,IAAI,YAAAiH,wBAAA/G,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoG,eAAe;EAAA;EAClH,OAAOY,IAAI,kBAjc8EnM,EAAE,CAAAoM,iBAAA;IAAAzG,IAAA,EAicJ4F,eAAe;IAAAc,SAAA;IAAAC,OAAA;MAAAV,cAAA;IAAA;IAAAW,QAAA;EAAA;AAC1G;AACA;EAAA,QAAA9G,SAAA,oBAAAA,SAAA,KAnc6FzF,EAAE,CAAA0F,iBAAA,CAmcJ6F,eAAe,EAAc,CAAC;IAC7G5F,IAAI,EAAEnF,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC4G,QAAQ,EAAE,oDAAoD;MAC9DD,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEX,cAAc,EAAE,CAAC;MACzDjG,IAAI,EAAElF;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAS8K,eAAe,IAAIhK,CAAC,EAAEyE,YAAY,IAAIyG,CAAC,EAAE1J,qBAAqB,IAAI2J,CAAC,EAAEjK,uCAAuC,IAAItB,CAAC,EAAEqB,+BAA+B,IAAImK,CAAC,EAAE9G,yBAAyB,IAAI+G,CAAC,EAAE9G,6BAA6B,IAAIzE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}