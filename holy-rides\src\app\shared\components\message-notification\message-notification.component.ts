import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { MessageService } from '../../../core/services/message.service';
import { AuthService } from '../../../core/services/auth.service';
import { Subscription, interval } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-message-notification',
  standalone: true,
  imports: [
    CommonModule,
    MatBadgeModule,
    MatButtonModule,
    MatIconModule
  ],
  template: `
    <button mat-icon-button [matBadge]="unreadCount" 
            [matBadgeHidden]="unreadCount === 0"
            matBadgeColor="warn"
            (click)="navigateToMessages()">
      <mat-icon>chat</mat-icon>
    </button>
  `,
  styles: [`
    :host {
      display: inline-block;
    }
  `]
})
export class MessageNotificationComponent implements OnInit, OnDestroy {
  unreadCount = 0;
  private refreshSubscription: Subscription | null = null;
  
  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private router: Router
  ) {}
  
  ngOnInit() {
    // Check for unread messages initially
    this.checkUnreadMessages();
    
    // Set up polling to check for new messages every 30 seconds
    this.refreshSubscription = interval(30000)
      .pipe(
        switchMap(() => {
          return new Promise<void>(async (resolve) => {
            await this.checkUnreadMessages();
            resolve();
          });
        })
      )
      .subscribe();
  }
  
  ngOnDestroy() {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
    }
  }
  
  async checkUnreadMessages() {
    try {
      this.unreadCount = await this.messageService.getUnreadMessageCount();
    } catch (error) {
      console.error('Error checking unread messages:', error);
    }
  }
  
  async navigateToMessages() {
    try {
      const role = await this.authService.getUserRole();
      if (role) {
        this.router.navigate(['/dashboard', role, 'messages']);
      }
    } catch (error) {
      console.error('Error navigating to messages:', error);
    }
  }
}
