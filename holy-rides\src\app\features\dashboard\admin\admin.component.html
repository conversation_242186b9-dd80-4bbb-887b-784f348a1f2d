<div class="dashboard-container">
  <h1 class="dashboard-title">Admin Dashboard</h1>

  <mat-tab-group animationDuration="300ms">
    <!-- Dashboard Overview Tab -->
    <mat-tab label="Dashboard Overview">
      <div class="tab-content">
        <div class="stats-container" *ngIf="statistics; else loadingStats">
          <mat-grid-list cols="2" rowHeight="250px" gutterSize="16px">
            <!-- User Statistics Card -->
            <mat-grid-tile>
              <mat-card class="stats-card">
                <mat-card-header>
                  <mat-card-title>User Statistics</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="stats-grid">
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.totalUsers.all }}</div>
                      <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.totalUsers.riders }}</div>
                      <div class="stat-label">Riders</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.totalUsers.drivers }}</div>
                      <div class="stat-label">Drivers</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.totalUsers.admins }}</div>
                      <div class="stat-label">Admins</div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>

            <!-- Ride Statistics Card -->
            <mat-grid-tile>
              <mat-card class="stats-card">
                <mat-card-header>
                  <mat-card-title>Ride Statistics</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <div class="stats-grid">
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.rides.total }}</div>
                      <div class="stat-label">Total Rides</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.rides.requested }}</div>
                      <div class="stat-label">Requested</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.rides.inProgress }}</div>
                      <div class="stat-label">In Progress</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-value">{{ statistics.rides.completed }}</div>
                      <div class="stat-label">Completed</div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile>

            <!-- Recent Activity Card -->
            <!-- <mat-grid-tile colspan="2" rowspan="2">
              <mat-card class="stats-card full-width">
                <mat-card-header>
                  <mat-card-title>Recent Activity</mat-card-title>
                </mat-card-header>
                <mat-card-content class="scrollable-content">
                  <mat-list>
                    <mat-list-item *ngFor="let activity of statistics.recentActivity">
                      <mat-icon matListItemIcon>
                        {{ activity.type === 'user_registered' ? 'person_add' :
                           activity.type === 'ride_requested' ? 'directions_car' :
                           activity.type === 'ride_completed' ? 'check_circle' :
                           'verified_user' }}
                      </mat-icon>
                      <div matListItemTitle>
                        {{ activity.type === 'user_registered' ? 'New User Registered' :
                           activity.type === 'ride_requested' ? 'Ride Requested' :
                           activity.type === 'ride_completed' ? 'Ride Completed' :
                           'Driver Approved' }}
                      </div>
                      <div matListItemLine>{{ formatDate(activity.timestamp) }}</div>
                    </mat-list-item>
                  </mat-list>
                </mat-card-content>
              </mat-card>
            </mat-grid-tile> -->
          </mat-grid-list>
        </div>

        <ng-template #loadingStats>
          <div class="loading-container">
            <mat-spinner diameter="50"></mat-spinner>
            <p>Loading statistics...</p>
          </div>
        </ng-template>
      </div>
    </mat-tab>

    <!-- Reports Tab - Hidden
    <mat-tab label="Reports">
      <div class="tab-content">
        <app-admin-reports></app-admin-reports>
      </div>
    </mat-tab> -->

    <!-- User Management Tab -->
    <mat-tab label="User Management">
      <div class="tab-content">
        <mat-card>
          <mat-card-header>
            <mat-card-title>User Management</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <!-- Desktop Filters -->
            <div class="filters-container desktop-filters">
              <mat-form-field appearance="outline">

                <mat-select [(ngModel)]="userRoleFilter" (selectionChange)="applyUserFilters()">
                  <mat-option value="">All Roles</mat-option>
                  <mat-option value="rider">Rider</mat-option>
                  <mat-option value="driver">Driver</mat-option>
                  <mat-option value="admin">Admin</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Search</mat-label>
                <input matInput [(ngModel)]="userSearchTerm" (keyup)="applyUserFilters(); userSearchTermSignal.set(userSearchTerm)" placeholder="Search by name or email">
                <mat-icon matSuffix>search</mat-icon>
              </mat-form-field>
            </div>

            <!-- Mobile Tab Filters -->
            <div class="mobile-filter-container">
              <div class="filter-buttons" aria-label="Filter users by role">
                <button mat-stroked-button
                        [color]="userRoleFilterSignal() === 'all' ? 'primary' : ''"
                        (click)="userRoleFilterSignal.set('all')"
                        class="filter-button">All</button>
                <button mat-stroked-button
                        [color]="userRoleFilterSignal() === 'rider' ? 'primary' : ''"
                        (click)="userRoleFilterSignal.set('rider')"
                        class="filter-button">Riders</button>
                <button mat-stroked-button
                        [color]="userRoleFilterSignal() === 'driver' ? 'primary' : ''"
                        (click)="userRoleFilterSignal.set('driver')"
                        class="filter-button">Drivers</button>
                <button mat-stroked-button
                        [color]="userRoleFilterSignal() === 'admin' ? 'primary' : ''"
                        (click)="userRoleFilterSignal.set('admin')"
                        class="filter-button">Admins</button>
              </div>

              <!-- Mobile Search -->
              <div class="mobile-search">
                <mat-form-field appearance="outline">
                  <mat-label>Search</mat-label>
                  <input matInput [(ngModel)]="userSearchTerm" (keyup)="applyUserFilters(); userSearchTermSignal.set(userSearchTerm)" placeholder="Search by name or email">
                  <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>
              </div>
            </div>

            <!-- Users Table -->
            <div *ngIf="!loadingUsers; else loadingUsersTemplate">
              <!-- Desktop View -->
              <div class="desktop-view">
                <table mat-table [dataSource]="userDataSource" matSort #userSort="matSort" class="mat-elevation-z2 full-width">
                  <!-- Email Column -->
                  <ng-container matColumnDef="email">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                    <td mat-cell *matCellDef="let user">{{ user.email }}</td>
                  </ng-container>

                  <!-- Name Column -->
                  <ng-container matColumnDef="full_name">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Name</th>
                    <td mat-cell *matCellDef="let user">{{ user.full_name || 'N/A' }}</td>
                  </ng-container>

                  <!-- Role Column -->
                  <ng-container matColumnDef="role">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
                    <td mat-cell *matCellDef="let user">
                      <mat-chip [color]="user.role === 'admin' ? 'warn' : 'primary'" selected>
                        {{ getRoleDisplayName(user.role) }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Created At Column -->
                  <ng-container matColumnDef="created_at">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Registered</th>
                    <td mat-cell *matCellDef="let user">{{ formatDate(user.created_at) }}</td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let user">
                      <mat-chip [color]="!user.is_approved ? 'warn' : 'accent'" selected>
                        {{ user.is_approved? 'Active': user.role === 'driver' ? 'Pending Approval' : 'Inactive' }}

                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let user">
                      <button mat-icon-button color="primary" *ngIf="user.role === 'driver' && !user.is_approved"
                              (click)="approveDriver(user.id)" matTooltip="Approve Driver">
                        <mat-icon>check_circle</mat-icon>
                      </button>
                      <button mat-icon-button color="accent" (click)="openUserDetails(user)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="userDisplayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: userDisplayedColumns;"></tr>
                </table>

                <mat-paginator #userPaginator [pageSizeOptions]="[5, 10, 25, 100]" [pageSize]="10" showFirstLastButtons aria-label="Select page of users"></mat-paginator>
              </div>

              <!-- Mobile View -->
              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let user of filteredUsers(); trackBy: trackByUserId">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        {{ user.full_name || user.email }}
                      </mat-panel-title>
                      <mat-panel-description>
                        <div class="user-actions-header">
                          <mat-chip [color]="user.role === 'admin' ? 'warn' : 'primary'" selected class="role-chip">
                            {{ getRoleDisplayName(user.role) }}
                          </mat-chip>
                          <button mat-icon-button color="primary" *ngIf="user.role === 'driver' && !user.is_approved"
                                  (click)="approveDriver(user.id); $event.stopPropagation()" matTooltip="Approve Driver">
                            <mat-icon>check_circle</mat-icon>
                          </button>
                          <button mat-icon-button color="accent" (click)="openUserDetails(user); $event.stopPropagation()" matTooltip="View Details">
                            <mat-icon>visibility</mat-icon>
                          </button>
                        </div>
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="user-details">
                      <p><strong>Email:</strong> {{ user.email }}</p>
                      <p><strong>Name:</strong> {{ user.full_name || 'N/A' }}</p>
                      <p><strong>Status:</strong>
                        <mat-chip [color]="!user.is_approved ? 'warn' : 'accent'" selected>
                          {{ user.is_approved? 'Active': user.role === 'driver' ? 'Pending Approval' : 'Inactive' }}
                        </mat-chip>
                      </p>
                      <p><strong>Registered:</strong> {{ formatDate(user.created_at) }}</p>
                    </div>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </div>

            <ng-template #loadingUsersTemplate>
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Loading users...</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Ride Management Tab -->
    <mat-tab label="Ride Management">
      <div class="tab-content">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Ride Management</mat-card-title>
            <div class="refresh-info">
              <span class="last-refresh">Last refreshed: {{ formatDate(lastRideRefreshTime.toISOString()) }}</span>
              <button mat-icon-button color="primary" (click)="loadRides()" matTooltip="Refresh rides manually">
                <mat-icon>refresh</mat-icon>
              </button>
              <span class="auto-refresh-note">(Real-time updates)</span>
            </div>
            <div class="action-buttons">
              <button mat-raised-button color="primary" (click)="openCreateRideDialog()">
                <mat-icon>add</mat-icon> Create Ride
              </button>
            </div>
          </mat-card-header>
          <mat-card-content>
            <!-- Desktop Filters -->
            <div class="filters-container desktop-filters">
              <mat-form-field appearance="outline">

                <mat-select [(ngModel)]="rideStatusFilter" (selectionChange)="applyRideFilters()">
                  <mat-option value="">All Statuses</mat-option>
                  <mat-option value="requested">Requested</mat-option>
                  <mat-option value="assigned">Assigned</mat-option>
                  <mat-option value="in-progress">In Progress</mat-option>
                  <mat-option value="completed">Completed</mat-option>
                  <mat-option value="canceled">Canceled</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <!-- <mat-label>Search</mat-label> -->
                <input matInput [(ngModel)]="rideSearchTerm" (keyup)="applyRideFilters(); rideSearchTermSignal.set(rideSearchTerm)" placeholder="Search by location or name">
                <mat-icon matSuffix>search</mat-icon>
              </mat-form-field>
            </div>

            <!-- Mobile Tab Filters -->
            <div class="mobile-filter-container">
              <div class="filter-buttons" aria-label="Filter rides by status">
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'all' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('all')"
                        class="filter-button">All</button>
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'requested' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('requested')"
                        class="filter-button">Requested</button>
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'assigned' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('assigned')"
                        class="filter-button">Assigned</button>
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'in-progress' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('in-progress')"
                        class="filter-button">In Progress</button>
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'completed' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('completed')"
                        class="filter-button">Completed</button>
                <button mat-stroked-button
                        [color]="rideStatusFilterSignal() === 'canceled' ? 'primary' : ''"
                        (click)="rideStatusFilterSignal.set('canceled')"
                        class="filter-button">Canceled</button>
              </div>

              <!-- Mobile Search -->
              <div class="mobile-search">
                <mat-form-field appearance="outline">
                  <mat-label>Search</mat-label>
                  <input matInput [(ngModel)]="rideSearchTerm" (keyup)="applyRideFilters(); rideSearchTermSignal.set(rideSearchTerm)" placeholder="Search by location or name">
                  <mat-icon matSuffix>search</mat-icon>
                </mat-form-field>
              </div>
            </div>

            <!-- Rides Table -->
            <div *ngIf="!loadingRides; else loadingRidesTemplate">
              <!-- Desktop View -->
              <div class="desktop-view">
                <table mat-table [dataSource]="rideDataSource" matSort #rideSort="matSort" class="mat-elevation-z2 full-width" [trackBy]="trackByRideId">
                  <!-- Rider Column -->
                  <ng-container matColumnDef="rider_id">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Rider</th>
                    <td mat-cell *matCellDef="let ride">{{ getUserName(ride.rider_id) }}</td>
                  </ng-container>

                  <!-- Driver Column -->
                  <ng-container matColumnDef="driver_id">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Driver</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.driver_id ? getUserName(ride.driver_id) : 'Not Assigned' }}</td>
                  </ng-container>

                  <!-- Pickup Location Column -->
                  <ng-container matColumnDef="pickup_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Pickup</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.pickup_location }}</td>
                  </ng-container>

                  <!-- Dropoff Location Column -->
                  <ng-container matColumnDef="dropoff_location">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Dropoff</th>
                    <td mat-cell *matCellDef="let ride">{{ ride.dropoff_location }}</td>
                  </ng-container>

                  <!-- Price Column -->
                  <ng-container matColumnDef="price">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Price</th>
                    <td mat-cell *matCellDef="let ride">${{ ride.fare || 'N/A' }}</td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
                    <td mat-cell *matCellDef="let ride">
                      <mat-chip [color]="getStatusColor(ride.status)" selected>
                        {{ getStatusDisplayName(ride.status) }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Created At Column -->
                  <ng-container matColumnDef="created_at">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Created</th>
                    <td mat-cell *matCellDef="let ride">{{ formatDate(ride.created_at) }}</td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let ride">
                      <button mat-icon-button color="primary" *ngIf="ride.status === 'requested'"
                              (click)="openDriverSelectionDialog(ride.id)" matTooltip="Assign Driver">
                        <mat-icon>person_add</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" *ngIf="ride.status === 'assigned'"
                              (click)="openDriverSelectionDialog(ride.id)" matTooltip="Reassign Driver">
                        <mat-icon>swap_horiz</mat-icon>
                      </button>
                      <button mat-icon-button color="accent" *ngIf="ride.status === 'requested' || ride.status === 'assigned'"
                              (click)="updateRideStatus(ride.id, 'canceled')" matTooltip="Cancel Ride">
                        <mat-icon>cancel</mat-icon>
                      </button>
                      <button mat-icon-button color="warn" *ngIf="ride.status === 'assigned'"
                              (click)="updateRideStatus(ride.id, 'in-progress')" matTooltip="Start Ride">
                        <mat-icon>play_arrow</mat-icon>
                      </button>
                      <button mat-icon-button color="primary" *ngIf="ride.status === 'in-progress'"
                              (click)="updateRideStatus(ride.id, 'completed')" matTooltip="Complete Ride">
                        <mat-icon>check_circle</mat-icon>
                      </button>
                      <button mat-icon-button color="accent" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="rideDisplayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: rideDisplayedColumns;"></tr>
                </table>

                <mat-paginator [length]="rides.length" #ridePaginator [pageSizeOptions]="[5, 10, 25, 100]" [pageSize]="5" showFirstLastButtons aria-label="Select page of rides"></mat-paginator>
              </div>

              <!-- Mobile View -->
              <div class="mobile-view">
                <mat-accordion multi>
                  <mat-expansion-panel *ngFor="let ride of filteredRides(); trackBy: trackByRideId">
                    <mat-expansion-panel-header>
                      <mat-panel-title>
                        {{ ride.pickup_location }}
                      </mat-panel-title>
                      <mat-panel-description>
                        <div class="ride-actions-header">
                          <mat-chip [color]="getStatusColor(ride.status)" selected class="status-chip">
                            {{ getStatusDisplayName(ride.status) }}
                          </mat-chip>
                          <button mat-icon-button color="primary" *ngIf="ride.status === 'requested'"
                                  (click)="openDriverSelectionDialog(ride.id); $event.stopPropagation()" matTooltip="Assign Driver">
                            <mat-icon>person_add</mat-icon>
                          </button>
                          <button mat-icon-button color="primary" *ngIf="ride.status === 'assigned'"
                                  (click)="openDriverSelectionDialog(ride.id); $event.stopPropagation()" matTooltip="Reassign Driver">
                            <mat-icon>swap_horiz</mat-icon>
                          </button>
                          <button mat-icon-button color="warn" *ngIf="ride.status === 'assigned'"
                                  (click)="updateRideStatus(ride.id, 'in-progress'); $event.stopPropagation()" matTooltip="Start Ride">
                            <mat-icon>play_arrow</mat-icon>
                          </button>
                          <button mat-icon-button color="primary" *ngIf="ride.status === 'in-progress'"
                                  (click)="updateRideStatus(ride.id, 'completed'); $event.stopPropagation()" matTooltip="Complete Ride">
                            <mat-icon>check_circle</mat-icon>
                          </button>
                        </div>
                      </mat-panel-description>
                    </mat-expansion-panel-header>
                    <div class="ride-details">
                      <p><strong>Rider:</strong> {{ getUserName(ride.rider_id) }}</p>
                      <p><strong>Driver:</strong> {{ ride.driver_id ? getUserName(ride.driver_id) : 'Not Assigned' }}</p>
                      <p><strong>To:</strong> {{ ride.dropoff_location }}</p>
                      <p><strong>Fare:</strong> ${{ ride.fare || 'N/A' }}</p>
                      <p><strong>Created:</strong> {{ formatDate(ride.created_at) }}</p>
                    </div>
                    <mat-action-row>
                      <button mat-icon-button color="accent" *ngIf="ride.status === 'requested' || ride.status === 'assigned'"
                              (click)="updateRideStatus(ride.id, 'canceled')" matTooltip="Cancel Ride">
                        <mat-icon>cancel</mat-icon>
                      </button>
                      <button mat-icon-button color="accent" (click)="viewRideDetails(ride.id)" matTooltip="View Details">
                        <mat-icon>visibility</mat-icon>
                      </button>
                    </mat-action-row>
                  </mat-expansion-panel>
                </mat-accordion>
              </div>
            </div>

            <ng-template #loadingRidesTemplate>
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Loading rides...</p>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Driver Payment Tab - Hidden
    <mat-tab label="Driver Payment">
      <div class="tab-content">
        <app-driver-payment></app-driver-payment>
      </div>
    </mat-tab> -->

    <!-- Square Sandbox Tab - Hidden
    <mat-tab label="Square Sandbox">
      <div class="tab-content">
        <app-square-sandbox></app-square-sandbox>
      </div>
    </mat-tab> -->

    <!-- Stripe Payment Processing Tab -->
    <mat-tab label="Stripe Payment Processing">
      <div class="tab-content">
        <app-stripe-payment></app-stripe-payment>
      </div>
    </mat-tab>

    <!-- Ride Pricing Tab -->
    <mat-tab label="Ride Pricing">
      <div class="tab-content">
        <app-ride-pricing></app-ride-pricing>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>

<!-- Ride Detail Dialog -->
<div *ngIf="selectedRideId" class="ride-detail-overlay">
  <app-ride-detail
    [rideId]="selectedRideId"
    [onClose]="closeRideDetails.bind(this)"
    (rideUpdated)="onRideUpdated($event)">
  </app-ride-detail>
</div>
  <div *ngIf="selectedRideId" class="close-overlay"  (click)="closeRideDetails()"><mat-icon>close</mat-icon></div>


