-- Create ride_pricing table to store pricing variables
CREATE TABLE IF NOT EXISTS ride_pricing (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  base_fare DECIMAL(10, 2) NOT NULL,
  distance_rate DECIMAL(10, 2) NOT NULL, -- per mile
  time_rate DECIMAL(10, 2) NOT NULL, -- per minute
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Insert default pricing
INSERT INTO ride_pricing (name, base_fare, distance_rate, time_rate)
VALUES ('Standard', 5.00, 1.50, 0.25);

-- Set up Row Level Security (RLS)
ALTER TABLE ride_pricing ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can view ride pricing."
  ON ride_pricing FOR SELECT
  USING (true);

CREATE POLICY "Only admins can insert ride pricing."
  ON ride_pricing FOR INSERT
  WITH CHECK (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Only admins can update ride pricing."
  ON ride_pricing FOR UPDATE
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

CREATE POLICY "Only admins can delete ride pricing."
  ON ride_pricing FOR DELETE
  USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_ride_pricing_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc'::text, now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_ride_pricing_updated_at
BEFORE UPDATE ON ride_pricing
FOR EACH ROW
EXECUTE FUNCTION update_ride_pricing_updated_at();
