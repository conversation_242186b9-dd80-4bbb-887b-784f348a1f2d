{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable, Inject, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { environment } from '../../../environments/environment';\nlet GoogleMapsLoaderService = class GoogleMapsLoaderService {\n  platformId;\n  isLoaded = false;\n  loadingPromise = null;\n  constructor(platformId) {\n    this.platformId = platformId;\n  }\n  /**\n   * Load the Google Maps API script\n   */\n  loadGoogleMapsApi() {\n    // Return if not in browser environment\n    if (!isPlatformBrowser(this.platformId)) {\n      return Promise.resolve();\n    }\n    // Return existing promise if already loading\n    if (this.loadingPromise) {\n      return this.loadingPromise;\n    }\n    // Return resolved promise if already loaded\n    if (this.isLoaded) {\n      return Promise.resolve();\n    }\n    // Create a new loading promise\n    this.loadingPromise = new Promise((resolve, reject) => {\n      // Check if the API is already loaded\n      if (window.google && window.google.maps) {\n        this.isLoaded = true;\n        resolve();\n        return;\n      }\n      // Create a callback function name\n      const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1000000)}`;\n      // Add the callback to the window object\n      window[callbackName] = () => {\n        this.isLoaded = true;\n        resolve();\n        delete window[callbackName];\n      };\n      // Create the script element\n      const script = document.createElement('script');\n      script.src = `https://maps.googleapis.com/maps/api/js?key=${environment.googleMapsApiKey}&libraries=places&callback=${callbackName}`;\n      script.async = true;\n      script.defer = true;\n      script.onerror = error => {\n        reject(new Error(`Failed to load Google Maps API: ${error}`));\n        delete window[callbackName];\n      };\n      // Append the script to the document\n      document.head.appendChild(script);\n    });\n    return this.loadingPromise;\n  }\n  /**\n   * Check if the Google Maps API is loaded\n   */\n  isGoogleMapsLoaded() {\n    return this.isLoaded;\n  }\n  static ctorParameters = () => [{\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }];\n};\nGoogleMapsLoaderService = __decorate([Injectable({\n  providedIn: 'root'\n})], GoogleMapsLoaderService);\nexport { GoogleMapsLoaderService };", "map": {"version": 3, "names": ["Injectable", "Inject", "PLATFORM_ID", "isPlatformBrowser", "environment", "GoogleMapsLoaderService", "platformId", "isLoaded", "loadingPromise", "constructor", "loadGoogleMapsApi", "Promise", "resolve", "reject", "window", "google", "maps", "callback<PERSON><PERSON>", "Math", "round", "random", "script", "document", "createElement", "src", "googleMapsApiKey", "async", "defer", "onerror", "error", "Error", "head", "append<PERSON><PERSON><PERSON>", "isGoogleMapsLoaded", "args", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\app\\core\\services\\google-maps-loader.service.ts"], "sourcesContent": ["import { Injectable, Inject, PLATFORM_ID } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GoogleMapsLoaderService {\n  private isLoaded = false;\n  private loadingPromise: Promise<void> | null = null;\n\n  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}\n\n  /**\n   * Load the Google Maps API script\n   */\n  loadGoogleMapsApi(): Promise<void> {\n    // Return if not in browser environment\n    if (!isPlatformBrowser(this.platformId)) {\n      return Promise.resolve();\n    }\n\n    // Return existing promise if already loading\n    if (this.loadingPromise) {\n      return this.loadingPromise;\n    }\n\n    // Return resolved promise if already loaded\n    if (this.isLoaded) {\n      return Promise.resolve();\n    }\n\n    // Create a new loading promise\n    this.loadingPromise = new Promise<void>((resolve, reject) => {\n      // Check if the API is already loaded\n      if (window.google && window.google.maps) {\n        this.isLoaded = true;\n        resolve();\n        return;\n      }\n\n      // Create a callback function name\n      const callbackName = `googleMapsApiCallback_${Math.round(Math.random() * 1000000)}`;\n\n      // Add the callback to the window object\n      (window as any)[callbackName] = () => {\n        this.isLoaded = true;\n        resolve();\n        delete (window as any)[callbackName];\n      };\n\n      // Create the script element\n      const script = document.createElement('script');\n      script.src = `https://maps.googleapis.com/maps/api/js?key=${environment.googleMapsApiKey}&libraries=places&callback=${callbackName}`;\n      script.async = true;\n      script.defer = true;\n      script.onerror = (error) => {\n        reject(new Error(`Failed to load Google Maps API: ${error}`));\n        delete (window as any)[callbackName];\n      };\n\n      // Append the script to the document\n      document.head.appendChild(script);\n    });\n\n    return this.loadingPromise;\n  }\n\n  /**\n   * Check if the Google Maps API is loaded\n   */\n  isGoogleMapsLoaded(): boolean {\n    return this.isLoaded;\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,WAAW,QAAQ,eAAe;AAC/D,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,WAAW,QAAQ,mCAAmC;AAKxD,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAIOC,UAAA;EAHjCC,QAAQ,GAAG,KAAK;EAChBC,cAAc,GAAyB,IAAI;EAEnDC,YAAyCH,UAAkB;IAAlB,KAAAA,UAAU,GAAVA,UAAU;EAAW;EAE9D;;;EAGAI,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACP,iBAAiB,CAAC,IAAI,CAACG,UAAU,CAAC,EAAE;MACvC,OAAOK,OAAO,CAACC,OAAO,EAAE;IAC1B;IAEA;IACA,IAAI,IAAI,CAACJ,cAAc,EAAE;MACvB,OAAO,IAAI,CAACA,cAAc;IAC5B;IAEA;IACA,IAAI,IAAI,CAACD,QAAQ,EAAE;MACjB,OAAOI,OAAO,CAACC,OAAO,EAAE;IAC1B;IAEA;IACA,IAAI,CAACJ,cAAc,GAAG,IAAIG,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC1D;MACA,IAAIC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;QACvC,IAAI,CAACT,QAAQ,GAAG,IAAI;QACpBK,OAAO,EAAE;QACT;MACF;MAEA;MACA,MAAMK,YAAY,GAAG,yBAAyBC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE;MAEnF;MACCN,MAAc,CAACG,YAAY,CAAC,GAAG,MAAK;QACnC,IAAI,CAACV,QAAQ,GAAG,IAAI;QACpBK,OAAO,EAAE;QACT,OAAQE,MAAc,CAACG,YAAY,CAAC;MACtC,CAAC;MAED;MACA,MAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,+CAA+CpB,WAAW,CAACqB,gBAAgB,8BAA8BR,YAAY,EAAE;MACpII,MAAM,CAACK,KAAK,GAAG,IAAI;MACnBL,MAAM,CAACM,KAAK,GAAG,IAAI;MACnBN,MAAM,CAACO,OAAO,GAAIC,KAAK,IAAI;QACzBhB,MAAM,CAAC,IAAIiB,KAAK,CAAC,mCAAmCD,KAAK,EAAE,CAAC,CAAC;QAC7D,OAAQf,MAAc,CAACG,YAAY,CAAC;MACtC,CAAC;MAED;MACAK,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,MAAM,CAAC;IACnC,CAAC,CAAC;IAEF,OAAO,IAAI,CAACb,cAAc;EAC5B;EAEA;;;EAGAyB,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC1B,QAAQ;EACtB;;;;YA9DaN,MAAM;MAAAiC,IAAA,GAAChC,WAAW;IAAA;EAAA,E;;AAJpBG,uBAAuB,GAAA8B,UAAA,EAHnCnC,UAAU,CAAC;EACVoC,UAAU,EAAE;CACb,CAAC,C,EACW/B,uBAAuB,CAmEnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}