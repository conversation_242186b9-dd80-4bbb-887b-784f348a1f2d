import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { GoogleMapsLoaderService } from './google-maps-loader.service';

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface RouteInfo {
  distance: number; // in miles
  duration: number; // in minutes
  polyline?: string; // encoded polyline for the route
}

@Injectable({
  providedIn: 'root'
})
export class LocationService {
  private currentLocationSubject = new BehaviorSubject<Coordinates | null>(null);
  currentLocation$ = this.currentLocationSubject.asObservable();

  constructor(private googleMapsLoader: GoogleMapsLoaderService) {}

  /**
   * Get the user's current location using the Geolocation API
   */
  getCurrentLocation(): Promise<Coordinates> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by your browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords: Coordinates = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          };
          this.currentLocationSubject.next(coords);
          resolve(coords);
        },
        (error) => {
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
      );
    });
  }

  /**
   * Convert an address to coordinates (geocoding)
   * Uses the Google Maps Geocoding API
   */
  async geocodeAddress(address: string): Promise<Coordinates> {
    try {
      await this.googleMapsLoader.loadGoogleMapsApi();
      
      return new Promise((resolve, reject) => {
        if (!google || !google.maps || !google.maps.Geocoder) {
          console.warn('Google Maps Geocoder not available, using mock data');
          // Generate random coordinates (this is just for demo)
          const latitude = 40 + (Math.random() * 10 - 5);
          const longitude = -74 + (Math.random() * 10 - 5);

          resolve({ latitude, longitude });
          return;
        }

        const geocoder = new google.maps.Geocoder();

        geocoder.geocode({ address }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
            const location = results[0].geometry.location;
            resolve({
              latitude: location.lat(),
              longitude: location.lng()
            });
          } else {
            console.warn(`Geocoding failed for address: ${address}. Status: ${status}`);
            // Fall back to mock data if geocoding fails
            const latitude = 40 + (Math.random() * 10 - 5);
            const longitude = -74 + (Math.random() * 10 - 5);

            resolve({ latitude, longitude });
          }
        });
      });
    } catch (error) {
      console.error('Failed to load Google Maps API:', error);
      // Fall back to mock data
      return {
        latitude: 40 + (Math.random() * 10 - 5),
        longitude: -74 + (Math.random() * 10 - 5)
      };
    }
  }

  /**
   * Calculate route between two points
   * Uses the Google Maps Directions API
   */
  async calculateRoute(origin: Coordinates | string, destination: Coordinates | string): Promise<RouteInfo> {
    try {
      await this.googleMapsLoader.loadGoogleMapsApi();
      
      return new Promise((resolve, reject) => {
        // If the DirectionsService is not available, fall back to mock data
        if (!google || !google.maps || !google.maps.DirectionsService) {
          console.warn('Google Maps DirectionsService not available, using mock data');
          // Random distance between 2 and 20 miles
          const distance = Math.floor(Math.random() * 18) + 2;
          // Random duration between 5 and 60 minutes
          const duration = Math.floor(Math.random() * 55) + 5;

          resolve({
            distance,
            duration,
            polyline: 'mock_polyline_string'
          });
          return;
        }

        const directionsService = new google.maps.DirectionsService();

        // Convert origin and destination to string format if they are coordinates
        const originStr = typeof origin === 'string' ? origin : `${origin.latitude},${origin.longitude}`;
        const destinationStr = typeof destination === 'string' ? destination : `${destination.latitude},${destination.longitude}`;

        const request: google.maps.DirectionsRequest = {
          origin: originStr,
          destination: destinationStr,
          travelMode: google.maps.TravelMode.DRIVING
        };

        directionsService.route(request, (result, status) => {
          if (status === google.maps.DirectionsStatus.OK && result) {
            const route = result.routes[0];
            if (route && route.legs && route.legs.length > 0) {
              const leg = route.legs[0];

              // Convert distance from meters to miles
              const distanceInMiles = leg.distance ? leg.distance.value / 1609.34 : 0;

              // Convert duration from seconds to minutes
              const durationInMinutes = leg.duration ? Math.ceil(leg.duration.value / 60) : 0;

              // Get encoded polyline
              const polyline = route.overview_polyline ? route.overview_polyline : '';

              resolve({
                distance: parseFloat(distanceInMiles.toFixed(2)),
                duration: durationInMinutes,
                polyline: polyline
              });
            } else {
              console.warn('No route found');
              // Fall back to mock data
              resolve({
                distance: Math.floor(Math.random() * 18) + 2,
                duration: Math.floor(Math.random() * 55) + 5,
                polyline: 'mock_polyline_string'
              });
            }
          } else {
            console.warn(`Directions request failed. Status: ${status}`);
            // Fall back to mock data
            resolve({
              distance: Math.floor(Math.random() * 18) + 2,
              duration: Math.floor(Math.random() * 55) + 5,
              polyline: 'mock_polyline_string'
            });
          }
        });
      });
    } catch (error) {
      console.error('Failed to load Google Maps API:', error);
      // Fall back to mock data
      return {
        distance: Math.floor(Math.random() * 18) + 2,
        duration: Math.floor(Math.random() * 55) + 5,
        polyline: 'mock_polyline_string'
      };
    }
  }

  /**
   * Generate a Google Maps URL for navigation
   */
  getGoogleMapsUrl(address: string): string {
    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
  }

  /**
   * Generate a Google Maps Directions URL
   */
  getGoogleMapsDirectionsUrl(origin: string, destination: string): string {
    return `https://www.google.com/maps/dir/?api=1&origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&travelmode=driving`;
  }
}
