{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n  // TODO: Add back shouldThrowOnError once we figure out the typings\n  /**\n   * Creates a PostgREST client.\n   *\n   * @param url - URL of the PostgREST endpoint\n   * @param options - Named parameters\n   * @param options.headers - Custom headers\n   * @param options.schema - Postgres schema to switch to\n   * @param options.fetch - Custom fetch\n   */\n  constructor(url, {\n    headers = {},\n    schema,\n    fetch\n  } = {}) {\n    this.url = url;\n    this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n    this.schemaName = schema;\n    this.fetch = fetch;\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    const url = new URL(`${this.url}/${relation}`);\n    return new PostgrestQueryBuilder_1.default(url, {\n      headers: Object.assign({}, this.headers),\n      schema: this.schemaName,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return new PostgrestClient(this.url, {\n      headers: this.headers,\n      schema,\n      fetch: this.fetch\n    });\n  }\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, {\n    head = false,\n    get = false,\n    count\n  } = {}) {\n    let method;\n    const url = new URL(`${this.url}/rpc/${fn}`);\n    let body;\n    if (head || get) {\n      method = head ? 'HEAD' : 'GET';\n      Object.entries(args)\n      // params with undefined value needs to be filtered out, otherwise it'll\n      // show up as `?param=undefined`\n      .filter(([_, value]) => value !== undefined)\n      // array values need special syntax\n      .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`]).forEach(([name, value]) => {\n        url.searchParams.append(name, value);\n      });\n    } else {\n      method = 'POST';\n      body = args;\n    }\n    const headers = Object.assign({}, this.headers);\n    if (count) {\n      headers['Prefer'] = `count=${count}`;\n    }\n    return new PostgrestFilterBuilder_1.default({\n      method,\n      url,\n      headers,\n      schema: this.schemaName,\n      body,\n      fetch: this.fetch,\n      allowEmpty: false\n    });\n  }\n}\nexports.default = PostgrestClient;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "PostgrestQueryBuilder_1", "require", "PostgrestFilterBuilder_1", "constants_1", "PostgrestClient", "constructor", "url", "headers", "schema", "fetch", "assign", "DEFAULT_HEADERS", "schemaName", "from", "relation", "URL", "default", "rpc", "fn", "args", "head", "get", "count", "method", "body", "entries", "filter", "_", "undefined", "map", "name", "Array", "isArray", "join", "for<PERSON>ach", "searchParams", "append", "allowEmpty"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestQueryBuilder_1 = __importDefault(require(\"./PostgrestQueryBuilder\"));\nconst PostgrestFilterBuilder_1 = __importDefault(require(\"./PostgrestFilterBuilder\"));\nconst constants_1 = require(\"./constants\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports.default = PostgrestClient;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,uBAAuB,GAAGP,eAAe,CAACQ,OAAO,CAAC,yBAAyB,CAAC,CAAC;AACnF,MAAMC,wBAAwB,GAAGT,eAAe,CAACQ,OAAO,CAAC,0BAA0B,CAAC,CAAC;AACrF,MAAME,WAAW,GAAGF,OAAO,CAAC,aAAa,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,eAAe,CAAC;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,GAAG,EAAE;IAAEC,OAAO,GAAG,CAAC,CAAC;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACpD,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGX,MAAM,CAACc,MAAM,CAACd,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAEP,WAAW,CAACQ,eAAe,CAAC,EAAEJ,OAAO,CAAC;IACrF,IAAI,CAACK,UAAU,GAAGJ,MAAM;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;EACII,IAAIA,CAACC,QAAQ,EAAE;IACX,MAAMR,GAAG,GAAG,IAAIS,GAAG,CAAC,GAAG,IAAI,CAACT,GAAG,IAAIQ,QAAQ,EAAE,CAAC;IAC9C,OAAO,IAAId,uBAAuB,CAACgB,OAAO,CAACV,GAAG,EAAE;MAC5CC,OAAO,EAAEX,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;MACxCC,MAAM,EAAE,IAAI,CAACI,UAAU;MACvBH,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,MAAMA,CAACA,MAAM,EAAE;IACX,OAAO,IAAIJ,eAAe,CAAC,IAAI,CAACE,GAAG,EAAE;MACjCC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,MAAM;MACNC,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACC,EAAE,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IAAEC,IAAI,GAAG,KAAK;IAAEC,GAAG,GAAG,KAAK;IAAEC;EAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IAC3D,IAAIC,MAAM;IACV,MAAMjB,GAAG,GAAG,IAAIS,GAAG,CAAC,GAAG,IAAI,CAACT,GAAG,QAAQY,EAAE,EAAE,CAAC;IAC5C,IAAIM,IAAI;IACR,IAAIJ,IAAI,IAAIC,GAAG,EAAE;MACbE,MAAM,GAAGH,IAAI,GAAG,MAAM,GAAG,KAAK;MAC9BxB,MAAM,CAAC6B,OAAO,CAACN,IAAI;MACf;MACA;MAAA,CACCO,MAAM,CAAC,CAAC,CAACC,CAAC,EAAE5B,KAAK,CAAC,KAAKA,KAAK,KAAK6B,SAAS;MAC3C;MAAA,CACCC,GAAG,CAAC,CAAC,CAACC,IAAI,EAAE/B,KAAK,CAAC,KAAK,CAAC+B,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACjC,KAAK,CAAC,GAAG,IAAIA,KAAK,CAACkC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGlC,KAAK,EAAE,CAAC,CAAC,CAC1FmC,OAAO,CAAC,CAAC,CAACJ,IAAI,EAAE/B,KAAK,CAAC,KAAK;QAC5BO,GAAG,CAAC6B,YAAY,CAACC,MAAM,CAACN,IAAI,EAAE/B,KAAK,CAAC;MACxC,CAAC,CAAC;IACN,CAAC,MACI;MACDwB,MAAM,GAAG,MAAM;MACfC,IAAI,GAAGL,IAAI;IACf;IACA,MAAMZ,OAAO,GAAGX,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACH,OAAO,CAAC;IAC/C,IAAIe,KAAK,EAAE;MACPf,OAAO,CAAC,QAAQ,CAAC,GAAG,SAASe,KAAK,EAAE;IACxC;IACA,OAAO,IAAIpB,wBAAwB,CAACc,OAAO,CAAC;MACxCO,MAAM;MACNjB,GAAG;MACHC,OAAO;MACPC,MAAM,EAAE,IAAI,CAACI,UAAU;MACvBY,IAAI;MACJf,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB4B,UAAU,EAAE;IAChB,CAAC,CAAC;EACN;AACJ;AACAvC,OAAO,CAACkB,OAAO,GAAGZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}