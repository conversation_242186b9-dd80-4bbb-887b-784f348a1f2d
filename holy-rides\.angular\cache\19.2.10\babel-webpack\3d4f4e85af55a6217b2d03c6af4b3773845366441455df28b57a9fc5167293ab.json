{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.version = void 0;\nexports.version = '0.0.0-automated';", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "version"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/postgrest-js/dist/cjs/version.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxBF,OAAO,CAACE,OAAO,GAAG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}