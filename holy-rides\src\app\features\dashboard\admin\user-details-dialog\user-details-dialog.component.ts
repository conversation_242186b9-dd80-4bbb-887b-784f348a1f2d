import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { FormsModule } from '@angular/forms';
import { User } from '../../../../core/models/user.model';
import { UserService } from '../../../../core/services/user.service';

@Component({
  selector: 'app-user-details-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule,
    MatChipsModule,
    MatSlideToggleModule
  ],
  template: `
    <h2 mat-dialog-title>User Details</h2>
    <mat-dialog-content>
      <div class="user-details">
        <div class="detail-row">
          <span class="label">Email:</span>
          <span class="value">{{ data.email }}</span>
        </div>
        
        <div class="detail-row">
          <span class="label">Full Name:</span>
          <span class="value">{{ data.full_name || 'Not provided' }}</span>
        </div>

        <div class="detail-row">
          <span class="label">Phone:</span>
          <span class="value">{{ data.phone || 'Not provided' }}</span>
        </div>

        <div class="detail-row">
          <span class="label">Role:</span>
          <mat-chip color="primary" selected>{{ formatRole(data.role) }}</mat-chip>
        </div>

        <div class="detail-row">
          <span class="label">Status:</span>
          <mat-chip [color]="data.is_approved ? 'primary' : 'warn'" selected>
            {{ data.is_approved ? 'Approved' : 'Pending Approval' }}
          </mat-chip>
          <mat-slide-toggle
            [checked]="data.is_approved"
            (change)="toggleApprovalStatus($event.checked)"
            color="primary">
          </mat-slide-toggle>
        </div>

        <div class="detail-row">
          <span class="label">Registered:</span>
          <span class="value">{{ formatDate(data.created_at) }}</span>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="close()">Close</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .user-details {
      padding: 16px;
    }

    .detail-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .label {
      font-weight: 500;
      min-width: 120px;
      color: rgba(0, 0, 0, 0.54);
    }

    .value {
      flex: 1;
    }

    mat-slide-toggle {
      margin-left: 8px;
    }
  `]
})
export class UserDetailsDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<UserDetailsDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: User,
    private userService: UserService,
    private snackBar: MatSnackBar
  ) {}

  formatRole(role: string): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  async toggleApprovalStatus(isApproved: boolean): Promise<void> {
    try {
      if (isApproved) {
        const success = await this.userService.approveDriver(this.data.id);
        if (success) {
          this.data.is_approved = true;
          this.snackBar.open('User approved successfully', 'Close', { duration: 3000 });
        } else {
          throw new Error('Failed to approve user');
        }
      } else {
        const success = await this.userService.updateUserStatus(this.data.id, false);
        if (success) {
          this.data.is_approved = false;
          this.snackBar.open('User approval revoked successfully', 'Close', { duration: 3000 });
        } else {
          throw new Error('Failed to revoke user approval');
        }
      }
    } catch (error) {
      console.error('Error updating user approval status:', error);
      this.snackBar.open('Failed to update user approval status', 'Close', { duration: 3000 });
      // Revert the toggle if the update failed
      this.data.is_approved = !isApproved;
    }
  }

  close(): void {
    this.dialogRef.close();
  }
}