{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n  /**\n   * Match only rows where `column` is equal to `value`.\n   *\n   * To check if the value of `column` is NULL, you should use `.is()` instead.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  eq(column, value) {\n    this.url.searchParams.append(column, `eq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is not equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  neq(column, value) {\n    this.url.searchParams.append(column, `neq.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gt(column, value) {\n    this.url.searchParams.append(column, `gt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is greater than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  gte(column, value) {\n    this.url.searchParams.append(column, `gte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lt(column, value) {\n    this.url.searchParams.append(column, `lt.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is less than or equal to `value`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  lte(column, value) {\n    this.url.searchParams.append(column, `lte.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  like(column, pattern) {\n    this.url.searchParams.append(column, `like.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-sensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  likeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches `pattern` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param pattern - The pattern to match with\n   */\n  ilike(column, pattern) {\n    this.url.searchParams.append(column, `ilike.${pattern}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches all of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAllOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` matches any of `patterns` case-insensitively.\n   *\n   * @param column - The column to filter on\n   * @param patterns - The patterns to match with\n   */\n  ilikeAnyOf(column, patterns) {\n    this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` IS `value`.\n   *\n   * For non-boolean columns, this is only relevant for checking if the value of\n   * `column` is NULL by setting `value` to `null`.\n   *\n   * For boolean columns, you can also set `value` to `true` or `false` and it\n   * will behave the same way as `.eq()`.\n   *\n   * @param column - The column to filter on\n   * @param value - The value to filter with\n   */\n  is(column, value) {\n    this.url.searchParams.append(column, `is.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows where `column` is included in the `values` array.\n   *\n   * @param column - The column to filter on\n   * @param values - The values array to filter with\n   */\n  in(column, values) {\n    const cleanedValues = Array.from(new Set(values)).map(s => {\n      // handle postgrest reserved characters\n      // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n      if (typeof s === 'string' && new RegExp('[,()]').test(s)) return `\"${s}\"`;else return `${s}`;\n    }).join(',');\n    this.url.searchParams.append(column, `in.(${cleanedValues})`);\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * `column` contains every element appearing in `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  contains(column, value) {\n    if (typeof value === 'string') {\n      // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n      // keep it simple and accept a string\n      this.url.searchParams.append(column, `cs.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for jsonb, array, and range columns. Match only rows where\n   * every element appearing in `column` is contained by `value`.\n   *\n   * @param column - The jsonb, array, or range column to filter on\n   * @param value - The jsonb, array, or range value to filter with\n   */\n  containedBy(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `cd.${value}`);\n    } else if (Array.isArray(value)) {\n      // array\n      this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n    } else {\n      // json\n      this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is greater than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGt(column, range) {\n    this.url.searchParams.append(column, `sr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or greater than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeGte(column, range) {\n    this.url.searchParams.append(column, `nxl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is less than any element in `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLt(column, range) {\n    this.url.searchParams.append(column, `sl.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where every element in\n   * `column` is either contained in `range` or less than any element in\n   * `range`.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeLte(column, range) {\n    this.url.searchParams.append(column, `nxr.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for range columns. Match only rows where `column` is\n   * mutually exclusive to `range` and there can be no element between the two\n   * ranges.\n   *\n   * @param column - The range column to filter on\n   * @param range - The range to filter with\n   */\n  rangeAdjacent(column, range) {\n    this.url.searchParams.append(column, `adj.${range}`);\n    return this;\n  }\n  /**\n   * Only relevant for array and range columns. Match only rows where\n   * `column` and `value` have an element in common.\n   *\n   * @param column - The array or range column to filter on\n   * @param value - The array or range value to filter with\n   */\n  overlaps(column, value) {\n    if (typeof value === 'string') {\n      // range\n      this.url.searchParams.append(column, `ov.${value}`);\n    } else {\n      // array\n      this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n    }\n    return this;\n  }\n  /**\n   * Only relevant for text and tsvector columns. Match only rows where\n   * `column` matches the query string in `query`.\n   *\n   * @param column - The text or tsvector column to filter on\n   * @param query - The query text to match with\n   * @param options - Named parameters\n   * @param options.config - The text search configuration to use\n   * @param options.type - Change how the `query` text is interpreted\n   */\n  textSearch(column, query, {\n    config,\n    type\n  } = {}) {\n    let typePart = '';\n    if (type === 'plain') {\n      typePart = 'pl';\n    } else if (type === 'phrase') {\n      typePart = 'ph';\n    } else if (type === 'websearch') {\n      typePart = 'w';\n    }\n    const configPart = config === undefined ? '' : `(${config})`;\n    this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n    return this;\n  }\n  /**\n   * Match only rows where each column in `query` keys is equal to its\n   * associated value. Shorthand for multiple `.eq()`s.\n   *\n   * @param query - The object to filter with, with column names as keys mapped\n   * to their filter values\n   */\n  match(query) {\n    Object.entries(query).forEach(([column, value]) => {\n      this.url.searchParams.append(column, `eq.${value}`);\n    });\n    return this;\n  }\n  /**\n   * Match only rows which doesn't satisfy the filter.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to be negated to filter with, following\n   * PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  not(column, operator, value) {\n    this.url.searchParams.append(column, `not.${operator}.${value}`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy at least one of the filters.\n   *\n   * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure it's properly sanitized.\n   *\n   * It's currently not possible to do an `.or()` filter across multiple tables.\n   *\n   * @param filters - The filters to use, following PostgREST syntax\n   * @param options - Named parameters\n   * @param options.referencedTable - Set this to filter on referenced tables\n   * instead of the parent table\n   * @param options.foreignTable - Deprecated, use `referencedTable` instead\n   */\n  or(filters, {\n    foreignTable,\n    referencedTable = foreignTable\n  } = {}) {\n    const key = referencedTable ? `${referencedTable}.or` : 'or';\n    this.url.searchParams.append(key, `(${filters})`);\n    return this;\n  }\n  /**\n   * Match only rows which satisfy the filter. This is an escape hatch - you\n   * should use the specific filter methods wherever possible.\n   *\n   * Unlike most filters, `opearator` and `value` are used as-is and need to\n   * follow [PostgREST\n   * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n   * to make sure they are properly sanitized.\n   *\n   * @param column - The column to filter on\n   * @param operator - The operator to filter with, following PostgREST syntax\n   * @param value - The value to filter with, following PostgREST syntax\n   */\n  filter(column, operator, value) {\n    this.url.searchParams.append(column, `${operator}.${value}`);\n    return this;\n  }\n}\nexports.default = PostgrestFilterBuilder;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "PostgrestTransformBuilder_1", "require", "PostgrestFilterBuilder", "default", "eq", "column", "url", "searchParams", "append", "neq", "gt", "gte", "lt", "lte", "like", "pattern", "likeAllOf", "patterns", "join", "likeAnyOf", "ilike", "ilikeAllOf", "ilikeAnyOf", "is", "in", "values", "cleanedV<PERSON>ues", "Array", "from", "Set", "map", "s", "RegExp", "test", "contains", "isArray", "JSON", "stringify", "containedBy", "rangeGt", "range", "rangeGte", "rangeLt", "rangeLte", "rangeAdja<PERSON>", "overlaps", "textSearch", "query", "config", "type", "typePart", "config<PERSON><PERSON>", "undefined", "match", "entries", "for<PERSON>ach", "not", "operator", "or", "filters", "foreignTable", "referencedTable", "key", "filter"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst PostgrestTransformBuilder_1 = __importDefault(require(\"./PostgrestTransformBuilder\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports.default = PostgrestFilterBuilder;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,2BAA2B,GAAGP,eAAe,CAACQ,OAAO,CAAC,6BAA6B,CAAC,CAAC;AAC3F,MAAMC,sBAAsB,SAASF,2BAA2B,CAACG,OAAO,CAAC;EACrE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,EAAEA,CAACC,MAAM,EAAEN,KAAK,EAAE;IACd,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,GAAGA,CAACJ,MAAM,EAAEN,KAAK,EAAE;IACf,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,EAAEA,CAACL,MAAM,EAAEN,KAAK,EAAE;IACd,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,GAAGA,CAACN,MAAM,EAAEN,KAAK,EAAE;IACf,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,EAAEA,CAACP,MAAM,EAAEN,KAAK,EAAE;IACd,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIc,GAAGA,CAACR,MAAM,EAAEN,KAAK,EAAE;IACf,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIe,IAAIA,CAACT,MAAM,EAAEU,OAAO,EAAE;IAClB,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,QAAQU,OAAO,EAAE,CAAC;IACvD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACX,MAAM,EAAEY,QAAQ,EAAE;IACxB,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,cAAcY,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACzE,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACd,MAAM,EAAEY,QAAQ,EAAE;IACxB,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,cAAcY,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACzE,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,KAAKA,CAACf,MAAM,EAAEU,OAAO,EAAE;IACnB,IAAI,CAACT,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,SAASU,OAAO,EAAE,CAAC;IACxD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,UAAUA,CAAChB,MAAM,EAAEY,QAAQ,EAAE;IACzB,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,eAAeY,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1E,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,UAAUA,CAACjB,MAAM,EAAEY,QAAQ,EAAE;IACzB,IAAI,CAACX,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,eAAeY,QAAQ,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IAC1E,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,EAAEA,CAAClB,MAAM,EAAEN,KAAK,EAAE;IACd,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIyB,EAAEA,CAACnB,MAAM,EAAEoB,MAAM,EAAE;IACf,MAAMC,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACJ,MAAM,CAAC,CAAC,CAC5CK,GAAG,CAAEC,CAAC,IAAK;MACZ;MACA;MACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,IAAIC,MAAM,CAAC,OAAO,CAAC,CAACC,IAAI,CAACF,CAAC,CAAC,EACpD,OAAO,IAAIA,CAAC,GAAG,CAAC,KAEhB,OAAO,GAAGA,CAAC,EAAE;IACrB,CAAC,CAAC,CACGb,IAAI,CAAC,GAAG,CAAC;IACd,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAOqB,aAAa,GAAG,CAAC;IAC7D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIQ,QAAQA,CAAC7B,MAAM,EAAEN,KAAK,EAAE;IACpB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B;MACA;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACvD,CAAC,MACI,IAAI4B,KAAK,CAACQ,OAAO,CAACpC,KAAK,CAAC,EAAE;MAC3B;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,CAACmB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACnE,CAAC,MACI;MACD;MACA,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAM+B,IAAI,CAACC,SAAS,CAACtC,KAAK,CAAC,EAAE,CAAC;IACvE;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuC,WAAWA,CAACjC,MAAM,EAAEN,KAAK,EAAE;IACvB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACvD,CAAC,MACI,IAAI4B,KAAK,CAACQ,OAAO,CAACpC,KAAK,CAAC,EAAE;MAC3B;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,CAACmB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACnE,CAAC,MACI;MACD;MACA,IAAI,CAACZ,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAM+B,IAAI,CAACC,SAAS,CAACtC,KAAK,CAAC,EAAE,CAAC;IACvE;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIwC,OAAOA,CAAClC,MAAM,EAAEmC,KAAK,EAAE;IACnB,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMmC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQA,CAACpC,MAAM,EAAEmC,KAAK,EAAE;IACpB,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAOmC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACrC,MAAM,EAAEmC,KAAK,EAAE;IACnB,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMmC,KAAK,EAAE,CAAC;IACnD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACtC,MAAM,EAAEmC,KAAK,EAAE;IACpB,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAOmC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACII,aAAaA,CAACvC,MAAM,EAAEmC,KAAK,EAAE;IACzB,IAAI,CAAClC,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAOmC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,QAAQA,CAACxC,MAAM,EAAEN,KAAK,EAAE;IACpB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACvD,CAAC,MACI;MACD;MACA,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAON,KAAK,CAACmB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;IACnE;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,UAAUA,CAACzC,MAAM,EAAE0C,KAAK,EAAE;IAAEC,MAAM;IAAEC;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7C,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAID,IAAI,KAAK,OAAO,EAAE;MAClBC,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAID,IAAI,KAAK,QAAQ,EAAE;MACxBC,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAID,IAAI,KAAK,WAAW,EAAE;MAC3BC,QAAQ,GAAG,GAAG;IAClB;IACA,MAAMC,UAAU,GAAGH,MAAM,KAAKI,SAAS,GAAG,EAAE,GAAG,IAAIJ,MAAM,GAAG;IAC5D,IAAI,CAAC1C,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,GAAG6C,QAAQ,MAAMC,UAAU,IAAIJ,KAAK,EAAE,CAAC;IAC5E,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIM,KAAKA,CAACN,KAAK,EAAE;IACTnD,MAAM,CAAC0D,OAAO,CAACP,KAAK,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAClD,MAAM,EAAEN,KAAK,CAAC,KAAK;MAC/C,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,MAAMN,KAAK,EAAE,CAAC;IACvD,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyD,GAAGA,CAACnD,MAAM,EAAEoD,QAAQ,EAAE1D,KAAK,EAAE;IACzB,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,OAAOoD,QAAQ,IAAI1D,KAAK,EAAE,CAAC;IAChE,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2D,EAAEA,CAACC,OAAO,EAAE;IAAEC,YAAY;IAAEC,eAAe,GAAGD;EAAc,CAAC,GAAG,CAAC,CAAC,EAAE;IAChE,MAAME,GAAG,GAAGD,eAAe,GAAG,GAAGA,eAAe,KAAK,GAAG,IAAI;IAC5D,IAAI,CAACvD,GAAG,CAACC,YAAY,CAACC,MAAM,CAACsD,GAAG,EAAE,IAAIH,OAAO,GAAG,CAAC;IACjD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,MAAMA,CAAC1D,MAAM,EAAEoD,QAAQ,EAAE1D,KAAK,EAAE;IAC5B,IAAI,CAACO,GAAG,CAACC,YAAY,CAACC,MAAM,CAACH,MAAM,EAAE,GAAGoD,QAAQ,IAAI1D,KAAK,EAAE,CAAC;IAC5D,OAAO,IAAI;EACf;AACJ;AACAD,OAAO,CAACK,OAAO,GAAGD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}