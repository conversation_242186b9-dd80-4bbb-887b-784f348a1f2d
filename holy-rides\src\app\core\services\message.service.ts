import { Injectable } from '@angular/core';
import { SupabaseClient } from '@supabase/supabase-js';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from './auth.service';

interface Message {
  id: string;
  thread_id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
}

interface MessageThread {
  id: string;
  ride_id: string;
  created_at: string;
  updated_at: string;
  rides: {
    rider_id: string;
    driver_id: string;
    pickup_location: string;
    dropoff_location: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class MessageService {
  private supabase: SupabaseClient;
  private messagesSubject = new BehaviorSubject<Message[]>([]);
  messages$ = this.messagesSubject.asObservable();
  private threadsSubject = new BehaviorSubject<MessageThread[]>([]);
  threads$ = this.threadsSubject.asObservable();

  constructor(private authService: AuthService) {
    this.supabase = authService.supabase;
  }

  async getOrCreateThreadForRide(rideId: string): Promise<MessageThread> {
    try {
      // Check if thread exists
      const { data: existingThreads, error: fetchError } = await this.supabase
        .from('message_threads')
        .select('*')
        .eq('ride_id', rideId)
        .limit(1);

      if (fetchError) throw fetchError;

      // If thread exists, return it
      if (existingThreads && existingThreads.length > 0) {
        return existingThreads[0];
      }

      // Create new thread
      const { data: newThread, error: createError } = await this.supabase
        .from('message_threads')
        .insert([{ ride_id: rideId }])
        .select()
        .single();

      if (createError) throw createError;
      return newThread;
    } catch (error) {
      console.error('Error getting or creating thread:', error);
      throw error;
    }
  }

  async getThreadMessages(threadId: string): Promise<Message[]> {
    try {
      const { data, error } = await this.supabase
        .from('messages')
        .select('*')
        .eq('thread_id', threadId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      this.messagesSubject.next(data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching thread messages:', error);
      return [];
    }
  }

  async getUserThreads(): Promise<MessageThread[]> {
    try {
      const user = await this.authService.getCurrentUser();
      if (!user) throw new Error('User not authenticated');

      // Get all threads where the user is either the rider or driver
      const { data, error } = await this.supabase
        .from('message_threads')
        .select(`
          *,
          rides!inner(rider_id, driver_id)
        `)
        .or(`rides.rider_id.eq.${user.id},rides.driver_id.eq.${user.id}`)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      this.threadsSubject.next(data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching user threads:', error);
      return [];
    }
  }

  async sendMessage(threadId: string, receiverId: string, content: string): Promise<Message> {
    try {
      const currentUser = await this.authService.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const { data, error } = await this.supabase
        .from('messages')
        .insert([{
          thread_id: threadId,
          sender_id: currentUser.id,
          receiver_id: receiverId,
          content: content,
          is_read: false
        }])
        .select()
        .single();

      if (error) throw error;

      // Update local messages state
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next([...currentMessages, data]);

      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async markMessagesAsRead(threadId: string): Promise<void> {
    try {
      const currentUser = await this.authService.getCurrentUser();
      if (!currentUser) throw new Error('User not authenticated');

      const { error } = await this.supabase
        .from('messages')
        .update({ is_read: true })
        .eq('thread_id', threadId)
        .eq('receiver_id', currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      // Update local messages state
      await this.getThreadMessages(threadId);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }

  async getUnreadMessageCount(): Promise<number> {
    try {
      const currentUser = await this.authService.getCurrentUser();
      if (!currentUser) return 0;

      const { data, error } = await this.supabase
        .from('messages')
        .select('id', { count: 'exact' })
        .eq('receiver_id', currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      return data?.length || 0;
    } catch (error) {
      console.error('Error getting unread message count:', error);
      return 0;
    }
  }
}
