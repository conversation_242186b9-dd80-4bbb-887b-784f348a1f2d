import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { NgxMatTimepickerModule } from 'ngx-mat-timepicker';

import { RideService } from '../../../../core/services/ride.service';
import { UserService } from '../../../../core/services/user.service';
import { LocationService } from '../../../../core/services/location.service';
import { PaymentService } from '../../../../core/services/payment.service';
import { User } from '../../../../core/models/user.model';
import { MapDisplayComponent } from '../../../../shared/components/map-display/map-display.component';

@Component({
  selector: 'app-admin-ride-create-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatSelectModule,
    MatSnackBarModule,
    NgxMatTimepickerModule,
    MapDisplayComponent
  ],
  template: `
    <h2 mat-dialog-title>Create Ride for User</h2>
    <mat-dialog-content>
      <form [formGroup]="rideForm">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Select Rider</mat-label>
          <mat-select formControlName="rider_id" required>
            <mat-option *ngFor="let rider of riders" [value]="rider.id">
              {{ rider.full_name || rider.email }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="rideForm.get('rider_id')?.errors?.['required']">
            Rider is required
          </mat-error>
        </mat-form-field>

        <div class="location-fields">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Pickup Location</mat-label>
            <input matInput formControlName="pickup_location" placeholder="Enter pickup location">
            <mat-error *ngIf="rideForm.get('pickup_location')?.errors?.['required']">
              Pickup location is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Dropoff Location</mat-label>
            <input matInput formControlName="dropoff_location" placeholder="Enter dropoff location">
            <mat-error *ngIf="rideForm.get('dropoff_location')?.errors?.['required']">
              Dropoff location is required
            </mat-error>
          </mat-form-field>
        </div>

        <div *ngIf="showMap && rideForm.get('pickup_location')?.value && rideForm.get('dropoff_location')?.value">
          <app-map-display
            [origin]="rideForm.get('pickup_location')?.value"
            [destination]="rideForm.get('dropoff_location')?.value">
          </app-map-display>

          <div *ngIf="estimatedFare" class="fare-estimate">
            <p>Estimated fare: <strong>{{estimatedFare ? '$' + estimatedFare.toFixed(2) : ''}}</strong></p>
            <p *ngIf="estimatedDistance">Distance: {{estimatedDistance}} miles</p>
            <p *ngIf="estimatedDuration">Duration: {{estimatedDuration}} minutes</p>
          </div>
        </div>

        <div class="date-time-fields">
          <mat-form-field appearance="outline">
            <mat-label>Pickup Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="pickup_date">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="rideForm.get('pickup_date')?.errors?.['required']">
              Pickup date is required
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Pickup Time</mat-label>
            <input matInput [ngxMatTimepicker]="timepicker" formControlName="pickup_time">
            <ngx-mat-timepicker-toggle matSuffix [for]="timepicker">
              <mat-icon ngxMatTimepickerToggleIcon>keyboard_arrow_down</mat-icon>
            </ngx-mat-timepicker-toggle>
            <ngx-mat-timepicker #timepicker></ngx-mat-timepicker>
            <mat-error *ngIf="rideForm.get('pickup_time')?.errors?.['required']">
              Pickup time is required
            </mat-error>
          </mat-form-field>
        </div>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Special Notes</mat-label>
          <textarea matInput formControlName="notes" placeholder="Any special requirements?"></textarea>
        </mat-form-field>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Cancel</button>
      <button mat-raised-button color="primary" [disabled]="rideForm.invalid || loading" (click)="onSubmit()">
        {{ loading ? 'Creating...' : 'Create Ride' }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .full-width {
      width: 100%;
    }
    
    form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 500px;
    }
    
    .location-fields {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    
    .date-time-fields {
      display: flex;
      gap: 16px;
    }
    
    textarea {
      min-height: 80px;
    }
    
    .fare-estimate {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      margin-top: 16px;
      margin-bottom: 16px;
    }
    
    .fare-estimate p {
      margin: 8px 0;
    }
  `]
})
export class AdminRideCreateDialogComponent implements OnInit {
  rideForm: FormGroup;
  riders: User[] = [];
  loading = false;
  showMap = false;
  estimatedFare: number | null = null;
  estimatedDistance: number | null = null;
  estimatedDuration: number | null = null;

  private locationCoordinates: {
    pickup?: { latitude: number; longitude: number };
    dropoff?: { latitude: number; longitude: number };
  } = {};

  constructor(
    private formBuilder: FormBuilder,
    private dialogRef: MatDialogRef<AdminRideCreateDialogComponent>,
    private rideService: RideService,
    private userService: UserService,
    private locationService: LocationService,
    private paymentService: PaymentService,
    private snackBar: MatSnackBar
  ) {
    this.rideForm = this.formBuilder.group({
      rider_id: ['', Validators.required],
      pickup_location: ['', Validators.required],
      dropoff_location: ['', Validators.required],
      pickup_date: [new Date(), Validators.required],
      pickup_time: ['12:00 PM', Validators.required],
     // notes: ['']
    });
  }

  async ngOnInit() {
    // Load all riders
    try {
      const users = await this.userService.getUsersByRole('rider');
      this.riders = users;
    } catch (error) {
      console.error('Error loading riders:', error);
      this.snackBar.open('Failed to load riders', 'Close', { duration: 3000 });
    }

    // Listen for changes to pickup and dropoff locations
    this.rideForm.get('pickup_location')?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });

    this.rideForm.get('dropoff_location')?.valueChanges.subscribe(() => {
      this.updateRouteEstimates();
    });
  }

  async updateRouteEstimates(): Promise<void> {
    const pickup = this.rideForm.get('pickup_location')?.value;
    const dropoff = this.rideForm.get('dropoff_location')?.value;

    if (pickup && dropoff) {
      this.showMap = true;

      try {
        const { fare, routeInfo } = await this.paymentService.estimateFare(pickup, dropoff);
        this.estimatedFare = fare;
        this.estimatedDistance = routeInfo.distance;
        this.estimatedDuration = routeInfo.duration;
      } catch (error) {
        console.error('Error calculating route:', error);
      }
    } else {
      this.showMap = false;
      this.estimatedFare = null;
      this.estimatedDistance = null;
      this.estimatedDuration = null;
    }
  }

  async onSubmit() {
    if (this.rideForm.invalid) return;

    this.loading = true;

    try {
      // Get coordinates for pickup and dropoff locations
      if (!this.locationCoordinates.pickup) {
        this.locationCoordinates.pickup = await this.locationService.geocodeAddress(this.rideForm.value.pickup_location);
      }

      if (!this.locationCoordinates.dropoff) {
        this.locationCoordinates.dropoff = await this.locationService.geocodeAddress(this.rideForm.value.dropoff_location);
      }

      // Calculate route information
      const routeInfo = await this.locationService.calculateRoute(
        this.locationCoordinates.pickup,
        this.locationCoordinates.dropoff
      );

      // Combine date and time
      const pickupDate = this.rideForm.value.pickup_date;
      const pickupTime = this.rideForm.value.pickup_time;

      // Create a combined date-time object
      const combinedDateTime = new Date(pickupDate);

      // Parse the time string (assuming format like "12:00 PM")
      const timeParts = pickupTime.match(/(\d+):(\d+)\s?(AM|PM)?/i);
      if (timeParts) {
        let hours = parseInt(timeParts[1], 10);
        const minutes = parseInt(timeParts[2], 10);
        const period = timeParts[3] ? timeParts[3].toUpperCase() : null;

        // Convert to 24-hour format if needed
        if (period === 'PM' && hours < 12) {
          hours += 12;
        } else if (period === 'AM' && hours === 12) {
          hours = 0;
        }

        combinedDateTime.setHours(hours, minutes, 0, 0);
      }

      const ride = {
        ...this.rideForm.value,
        status: 'requested',
        pickup_time: combinedDateTime.toISOString(),
        // Add location coordinates
        pickup_latitude: this.locationCoordinates.pickup?.latitude,
        pickup_longitude: this.locationCoordinates.pickup?.longitude,
        dropoff_latitude: this.locationCoordinates.dropoff?.latitude,
        dropoff_longitude: this.locationCoordinates.dropoff?.longitude,
        // Add route information
        distance_miles: routeInfo.distance,
        duration_minutes: routeInfo.duration,
        // Add fare
        fare: this.estimatedFare || (await this.paymentService.estimateFare(
          this.rideForm.value.pickup_location,
          this.rideForm.value.dropoff_location
        )).fare
      };

      const createdRide = await this.rideService.createRide(ride);
      this.snackBar.open('Ride created successfully!', 'Close', { duration: 3000 });
      this.dialogRef.close(createdRide);
    } catch (error: any) {
      console.error('Error creating ride:', error);
      this.snackBar.open(error.message || 'Failed to create ride', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }
}
