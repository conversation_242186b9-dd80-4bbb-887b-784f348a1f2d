{"ast": null, "code": "/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n  if (typeof globalThis === 'object') return;\n  try {\n    Object.defineProperty(Object.prototype, '__magic__', {\n      get: function () {\n        return this;\n      },\n      configurable: true\n    });\n    // @ts-expect-error 'Allow access to magic'\n    __magic__.globalThis = __magic__;\n    // @ts-expect-error 'Allow access to magic'\n    delete Object.prototype.__magic__;\n  } catch (e) {\n    if (typeof self !== 'undefined') {\n      // @ts-expect-error 'Allow access to globals'\n      self.globalThis = self;\n    }\n  }\n}", "map": {"version": 3, "names": ["polyfillGlobalThis", "globalThis", "Object", "defineProperty", "prototype", "get", "configurable", "__magic__", "e", "self"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js"], "sourcesContent": ["/**\n * https://mathiasbynens.be/notes/globalthis\n */\nexport function polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,kBAAkBA,CAAA,EAAG;EACjC,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAC9B;EACJ,IAAI;IACAC,MAAM,CAACC,cAAc,CAACD,MAAM,CAACE,SAAS,EAAE,WAAW,EAAE;MACjDC,GAAG,EAAE,SAAAA,CAAA,EAAY;QACb,OAAO,IAAI;MACf,CAAC;MACDC,YAAY,EAAE;IAClB,CAAC,CAAC;IACF;IACAC,SAAS,CAACN,UAAU,GAAGM,SAAS;IAChC;IACA,OAAOL,MAAM,CAACE,SAAS,CAACG,SAAS;EACrC,CAAC,CACD,OAAOC,CAAC,EAAE;IACN,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;MAC7B;MACAA,IAAI,CAACR,UAAU,GAAGQ,IAAI;IAC1B;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}