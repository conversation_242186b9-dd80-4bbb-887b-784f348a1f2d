{"ast": null, "code": "import { version } from './version';\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `gotrue-js/${version}`\n};\nexport const EXPIRY_MARGIN = 10; // in seconds\nexport const NETWORK_FAILURE = {\n  MAX_RETRIES: 10,\n  RETRY_INTERVAL: 2 // in deciseconds\n};", "map": {"version": 3, "names": ["version", "GOTRUE_URL", "STORAGE_KEY", "AUDIENCE", "DEFAULT_HEADERS", "EXPIRY_MARGIN", "NETWORK_FAILURE", "MAX_RETRIES", "RETRY_INTERVAL"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/node_modules/@supabase/auth-js/dist/module/lib/constants.js"], "sourcesContent": ["import { version } from './version';\nexport const GOTRUE_URL = 'http://localhost:9999';\nexport const STORAGE_KEY = 'supabase.auth.token';\nexport const AUDIENCE = '';\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${version}` };\nexport const EXPIRY_MARGIN = 10; // in seconds\nexport const NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,UAAU,GAAG,uBAAuB;AACjD,OAAO,MAAMC,WAAW,GAAG,qBAAqB;AAChD,OAAO,MAAMC,QAAQ,GAAG,EAAE;AAC1B,OAAO,MAAMC,eAAe,GAAG;EAAE,eAAe,EAAE,aAAaJ,OAAO;AAAG,CAAC;AAC1E,OAAO,MAAMK,aAAa,GAAG,EAAE,CAAC,CAAC;AACjC,OAAO,MAAMC,eAAe,GAAG;EAC3BC,WAAW,EAAE,EAAE;EACfC,cAAc,EAAE,CAAC,CAAE;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}