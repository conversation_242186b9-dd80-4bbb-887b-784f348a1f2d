export interface SystemStatistics {
  totalUsers: {
    all: number;
    riders: number;
    drivers: number;
    admins: number;
  };
  rides: {
    total: number;
    completed: number;
    inProgress: number;
    requested: number;
    canceled: number;
  };
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'user_registered' | 'ride_requested' | 'ride_completed' | 'driver_approved';
  timestamp: string;
  details: any;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface RideReportData {
  dateRange: DateRange;
  ridesByStatus: {
    requested: number;
    assigned: number;
    inProgress: number;
    completed: number;
    canceled: number;
  };
  ridesByDay: {
    date: string;
    count: number;
  }[];
  averageDuration: number;
  averageDistance: number;
}

export interface RevenueReportData {
  dateRange: DateRange;
  totalRevenue: number;
  revenueByDay: {
    date: string;
    amount: number;
  }[];
  revenueByStatus: {
    pending: number;
    paid: number;
    failed: number;
    refunded: number;
  };
  topDriversByRevenue: {
    driverId: string;
    driverName: string;
    revenue: number;
    rideCount: number;
  }[];
}

export interface UserActivityReportData {
  dateRange: DateRange;
  newUsers: {
    total: number;
    riders: number;
    drivers: number;
  };
  activeUsers: {
    total: number;
    riders: number;
    drivers: number;
  };
  usersByDay: {
    date: string;
    newUsers: number;
    activeUsers: number;
  }[];
  topRiders: {
    riderId: string;
    riderName: string;
    rideCount: number;
    totalSpent: number;
  }[];
}

export interface ReportOptions {
  dateRange: DateRange;
  reportType: 'rides' | 'revenue' | 'users';
  userRole?: 'rider' | 'driver' | 'admin';
  rideStatus?: string;
}
