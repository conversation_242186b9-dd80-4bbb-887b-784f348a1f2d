{"ast": null, "code": "import { version } from './version';\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n  JS_ENV = 'deno';\n} else if (typeof document !== 'undefined') {\n  JS_ENV = 'web';\n} else if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n  JS_ENV = 'react-native';\n} else {\n  JS_ENV = 'node';\n}\nexport const DEFAULT_HEADERS = {\n  'X-Client-Info': `supabase-js-${JS_ENV}/${version}`\n};\nexport const DEFAULT_GLOBAL_OPTIONS = {\n  headers: DEFAULT_HEADERS\n};\nexport const DEFAULT_DB_OPTIONS = {\n  schema: 'public'\n};\nexport const DEFAULT_AUTH_OPTIONS = {\n  autoRefreshToken: true,\n  persistSession: true,\n  detectSessionInUrl: true,\n  flowType: 'implicit'\n};\nexport const DEFAULT_REALTIME_OPTIONS = {};", "map": {"version": 3, "names": ["version", "JS_ENV", "<PERSON><PERSON>", "document", "navigator", "product", "DEFAULT_HEADERS", "DEFAULT_GLOBAL_OPTIONS", "headers", "DEFAULT_DB_OPTIONS", "schema", "DEFAULT_AUTH_OPTIONS", "autoRefreshToken", "persistSession", "detectSessionInUrl", "flowType", "DEFAULT_REALTIME_OPTIONS"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/supabase-js/dist/module/lib/constants.js"], "sourcesContent": ["import { version } from './version';\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nexport const DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${version}` };\nexport const DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nexport const DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nexport const DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nexport const DEFAULT_REALTIME_OPTIONS = {};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,IAAIC,MAAM,GAAG,EAAE;AACf;AACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EAC7BD,MAAM,GAAG,MAAM;AACnB,CAAC,MACI,IAAI,OAAOE,QAAQ,KAAK,WAAW,EAAE;EACtCF,MAAM,GAAG,KAAK;AAClB,CAAC,MACI,IAAI,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAK,aAAa,EAAE;EAC9EJ,MAAM,GAAG,cAAc;AAC3B,CAAC,MACI;EACDA,MAAM,GAAG,MAAM;AACnB;AACA,OAAO,MAAMK,eAAe,GAAG;EAAE,eAAe,EAAE,eAAeL,MAAM,IAAID,OAAO;AAAG,CAAC;AACtF,OAAO,MAAMO,sBAAsB,GAAG;EAClCC,OAAO,EAAEF;AACb,CAAC;AACD,OAAO,MAAMG,kBAAkB,GAAG;EAC9BC,MAAM,EAAE;AACZ,CAAC;AACD,OAAO,MAAMC,oBAAoB,GAAG;EAChCC,gBAAgB,EAAE,IAAI;EACtBC,cAAc,EAAE,IAAI;EACpBC,kBAAkB,EAAE,IAAI;EACxBC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,MAAMC,wBAAwB,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}