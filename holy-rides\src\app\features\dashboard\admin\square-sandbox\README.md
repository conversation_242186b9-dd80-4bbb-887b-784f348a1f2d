# Square Sandbox Implementation

This component provides a sandbox environment for testing Square payment processing functionality in the Holy Rides application.

## Features

- Test Square Web Payments SDK integration
- Process test payments in sandbox mode
- Simulate different payment scenarios (approved, declined, etc.)
- View payment results for debugging

## Setup Instructions

1. **Get Square Developer Credentials**:
   - Create a Square Developer account at [https://developer.squareup.com/](https://developer.squareup.com/)
   - Create a new application in the Square Developer Dashboard
   - Switch to Sandbox mode
   - Copy your Sandbox Application ID and Location ID

2. **Update Environment Configuration**:
   - Open `src/environments/environment.ts`
   - Replace the placeholder values with your Square sandbox credentials:
     ```typescript
     squareApplicationId: 'sandbox-sq0idb-YOUR-APP-ID',
     squareLocationId: 'YOUR-LOCATION-ID',
     squareAccessToken: 'YOUR-SANDBOX-ACCESS-TOKEN',
     ```

3. **Test Card Numbers**:
   - Use the following test card numbers in the Square sandbox:
     - Visa: 4111 1111 1111 1111
     - Mastercard: 5555 5555 5555 4444
     - American Express: 3400 0000 0000 009
     - Discover: 6011 0000 0000 0004
   - Any future expiration date and any 3-4 digit CVV can be used
   - Any valid postal code can be used (e.g., 12345)

## Usage

1. Log in as an admin user
2. Navigate to the Admin Dashboard
3. Click on the "Square Sandbox" tab
4. Enter a test amount and select a test scenario
5. Enter test card details in the payment form
6. Click "Process Payment" to test the payment flow

## Test Scenarios

- **Approved Payment**: Simulates a successful payment
- **Declined Payment**: Simulates a payment declined by the issuing bank
- **Card Declined**: Simulates a card that is declined (e.g., insufficient funds)
- **Verification Required**: Simulates a payment that requires additional verification

## Implementation Details

The Square Sandbox component uses the Square Web Payments SDK to create a secure payment form and generate payment tokens. In a production environment, these tokens would be sent to a server to process actual payments using the Square Payments API.

For more information on Square's Web Payments SDK, visit the [official documentation](https://developer.squareup.com/docs/web-payments/overview).
