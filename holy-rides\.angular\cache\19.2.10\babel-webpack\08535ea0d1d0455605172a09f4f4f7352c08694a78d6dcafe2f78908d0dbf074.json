{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, untracked, afterRender, afterNextRender, ElementRef, Injector, ANIMATION_MODULE_TYPE, EnvironmentInjector, ApplicationRef, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT, Location } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ScrollDispatcher, ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { b as DomPortalOutlet, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n  _viewportRuler;\n  _previousHTMLStyles = {\n    top: '',\n    left: ''\n  };\n  _previousScrollPosition;\n  _isEnabled = false;\n  _document;\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n      // Cache the previous inline styles in case the user had set them.\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || '';\n      // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock');\n      // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n    const rootElement = this._document.documentElement;\n    const viewport = this._viewportRuler.getViewportSize();\n    return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n  }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n  _scrollDispatcher;\n  _ngZone;\n  _viewportRuler;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  _initialScrollPosition;\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n    const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n      return !scrollable || !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement);\n    }));\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n  /** Detaches the overlay ref and disables the scroll strategy. */\n  _detach = () => {\n    this.disable();\n    if (this._overlayRef.hasAttached()) {\n      this._ngZone.run(() => this._overlayRef.detach());\n    }\n  };\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n  attach() {}\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n  _scrollDispatcher;\n  _viewportRuler;\n  _ngZone;\n  _config;\n  _scrollSubscription = null;\n  _overlayRef;\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition();\n        // TODO(crisbeto): make `close` on by default once all components can handle it.\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize();\n          // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n      this._scrollSubscription = null;\n    }\n  }\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n  _scrollDispatcher = inject(ScrollDispatcher);\n  _viewportRuler = inject(ViewportRuler);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  constructor() {}\n  /** Do nothing on scroll. */\n  noop = () => new NoopScrollStrategy();\n  /**\n   * Close the overlay as soon as the user scrolls.\n   * @param config Configuration to be used inside the scroll strategy.\n   */\n  close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n  /** Block scrolling. */\n  block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n  /**\n   * Update the overlay's position on scroll.\n   * @param config Configuration to be used inside the scroll strategy.\n   * Allows debouncing the reposition calls.\n   */\n  reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n  static ɵfac = function ScrollStrategyOptions_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ScrollStrategyOptions)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ScrollStrategyOptions,\n    factory: ScrollStrategyOptions.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n  /** Strategy with which to position the overlay. */\n  positionStrategy;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy = new NoopScrollStrategy();\n  /** Custom class to add to the overlay pane. */\n  panelClass = '';\n  /** Whether the overlay has a backdrop. */\n  hasBackdrop = false;\n  /** Custom class to add to the backdrop */\n  backdropClass = 'cdk-overlay-dark-backdrop';\n  /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n  width;\n  /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n  height;\n  /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  minWidth;\n  /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  minHeight;\n  /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxWidth;\n  /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n  maxHeight;\n  /**\n   * Direction of the text in the overlay panel. If a `Directionality` instance\n   * is passed in, the overlay will handle changes to its value automatically.\n   */\n  direction;\n  /**\n   * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  disposeOnNavigation = false;\n  constructor(config) {\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n  offsetX;\n  offsetY;\n  panelClass;\n  /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n  originX;\n  /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n  originY;\n  /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n  overlayX;\n  /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n  overlayY;\n  constructor(origin, overlay, /** Offset along the X axis. */\n  offsetX, /** Offset along the Y axis. */\n  offsetY, /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n  isOriginClipped;\n  isOriginOutsideView;\n  isOverlayClipped;\n  isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n  connectionPair;\n  scrollableViewProperties;\n  constructor(/** The position used as a result of this change. */\n  connectionPair, /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n  /** Currently attached overlays in the order they were attached. */\n  _attachedOverlays = [];\n  _document = inject(DOCUMENT);\n  _isAttached;\n  constructor() {}\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    }\n    // Remove the global listener once there are no more overlays.\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n  static ɵfac = function BaseOverlayDispatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseOverlayDispatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BaseOverlayDispatcher,\n    factory: BaseOverlayDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cleanupKeydown;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Lazily start dispatcher once first overlay is added\n    if (!this._isAttached) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n      });\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanupKeydown?.();\n      this._isAttached = false;\n    }\n  }\n  /** Keyboard event listener that will be attached to the body. */\n  _keydownListener = event => {\n    const overlays = this._attachedOverlays;\n    for (let i = overlays.length - 1; i > -1; i--) {\n      // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n      // We want to target the most recent overlay, rather than trying to match where the event came\n      // from, because some components might open an overlay, but keep focus on a trigger element\n      // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n      // because we don't want overlays that don't handle keyboard events to block the ones below\n      // them that do.\n      if (overlays[i]._keydownEvents.observers.length > 0) {\n        this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n        break;\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayKeyboardDispatcher_BaseFactory;\n    return function OverlayKeyboardDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayKeyboardDispatcher_BaseFactory || (ɵOverlayKeyboardDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayKeyboardDispatcher)))(__ngFactoryType__ || OverlayKeyboardDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayKeyboardDispatcher,\n    factory: OverlayKeyboardDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _cursorOriginalValue;\n  _cursorStyleIsSet = false;\n  _pointerDownEventTarget;\n  _cleanups;\n  /** Add a new overlay to the list of attached overlay refs. */\n  add(overlayRef) {\n    super.add(overlayRef);\n    // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n    if (!this._isAttached) {\n      const body = this._document.body;\n      const eventOptions = {\n        capture: true\n      };\n      this._cleanups = this._ngZone.runOutsideAngular(() => [_bindEventWithOptions(this._renderer, body, 'pointerdown', this._pointerDownListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions), _bindEventWithOptions(this._renderer, body, 'contextmenu', this._clickListener, eventOptions)]);\n      // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n  detach() {\n    if (this._isAttached) {\n      this._cleanups?.forEach(cleanup => cleanup());\n      this._cleanups = undefined;\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        this._document.body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n      this._isAttached = false;\n    }\n  }\n  /** Store pointerdown event target to track origin of click. */\n  _pointerDownListener = event => {\n    this._pointerDownEventTarget = _getEventTarget(event);\n  };\n  /** Click event listener that will be attached to the body propagate phase. */\n  _clickListener = event => {\n    const target = _getEventTarget(event);\n    // In case of a click event, we want to check the origin of the click\n    // (e.g. in case where a user starts a click inside the overlay and\n    // releases the click outside of it).\n    // This is done by using the event target of the preceding pointerdown event.\n    // Every click event caused by a pointer device has a preceding pointerdown\n    // event, unless the click was programmatically triggered (e.g. in a unit test).\n    const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target;\n    // Reset the stored pointerdown event target, to avoid having it interfere\n    // in subsequent events.\n    this._pointerDownEventTarget = null;\n    // We copy the array because the original may be modified asynchronously if the\n    // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n    // the for loop.\n    const overlays = this._attachedOverlays.slice();\n    // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n    // We want to target all overlays for which the click could be considered as outside click.\n    // As soon as we reach an overlay for which the click is not outside click we break off\n    // the loop.\n    for (let i = overlays.length - 1; i > -1; i--) {\n      const overlayRef = overlays[i];\n      if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n        continue;\n      }\n      // If it's a click inside the overlay, just break - we should do nothing\n      // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n      // and proceed with the next overlay\n      if (containsPierceShadowDom(overlayRef.overlayElement, target) || containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n        break;\n      }\n      const outsidePointerEvents = overlayRef._outsidePointerEvents;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.run(() => outsidePointerEvents.next(event));\n      } else {\n        outsidePointerEvents.next(event);\n      }\n    }\n  };\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵOverlayOutsideClickDispatcher_BaseFactory;\n    return function OverlayOutsideClickDispatcher_Factory(__ngFactoryType__) {\n      return (ɵOverlayOutsideClickDispatcher_BaseFactory || (ɵOverlayOutsideClickDispatcher_BaseFactory = i0.ɵɵgetInheritedFactory(OverlayOutsideClickDispatcher)))(__ngFactoryType__ || OverlayOutsideClickDispatcher);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayOutsideClickDispatcher,\n    factory: OverlayOutsideClickDispatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n  const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n  let current = child;\n  while (current) {\n    if (current === parent) {\n      return true;\n    }\n    current = supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n  }\n  return false;\n}\nclass _CdkOverlayStyleLoader {\n  static ɵfac = function _CdkOverlayStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkOverlayStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkOverlayStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-overlay-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkOverlayStyleLoader_Template(rf, ctx) {},\n    styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkOverlayStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-overlay-style-loader': ''\n      },\n      styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"]\n    }]\n  }], null, null);\n})();\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n  _platform = inject(Platform);\n  _containerElement;\n  _document = inject(DOCUMENT);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  constructor() {}\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    this._loadStyles();\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(container);\n    this._containerElement = container;\n  }\n  /** Loads the structural styles necessary for the overlay to work. */\n  _loadStyles() {\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n  }\n  static ɵfac = function OverlayContainer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayContainer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayContainer,\n    factory: OverlayContainer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n  _renderer;\n  _ngZone;\n  element;\n  _cleanupClick;\n  _cleanupTransitionEnd;\n  _fallbackTimeout;\n  constructor(document, _renderer, _ngZone, onClick) {\n    this._renderer = _renderer;\n    this._ngZone = _ngZone;\n    this.element = document.createElement('div');\n    this.element.classList.add('cdk-overlay-backdrop');\n    this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n  }\n  detach() {\n    this._ngZone.runOutsideAngular(() => {\n      const element = this.element;\n      clearTimeout(this._fallbackTimeout);\n      this._cleanupTransitionEnd?.();\n      this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n      this._fallbackTimeout = setTimeout(this.dispose, 500);\n      // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n      // In this case we make it unclickable and we try to remove it after a delay.\n      element.style.pointerEvents = 'none';\n      element.classList.remove('cdk-overlay-backdrop-showing');\n    });\n  }\n  dispose = () => {\n    clearTimeout(this._fallbackTimeout);\n    this._cleanupClick?.();\n    this._cleanupTransitionEnd?.();\n    this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n    this.element.remove();\n  };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n  _portalOutlet;\n  _host;\n  _pane;\n  _config;\n  _ngZone;\n  _keyboardDispatcher;\n  _document;\n  _location;\n  _outsideClickDispatcher;\n  _animationsDisabled;\n  _injector;\n  _renderer;\n  _backdropClick = new Subject();\n  _attachments = new Subject();\n  _detachments = new Subject();\n  _positionStrategy;\n  _scrollStrategy;\n  _locationChanges = Subscription.EMPTY;\n  _backdropRef = null;\n  /**\n   * Reference to the parent of the `_host` at the time it was detached. Used to restore\n   * the `_host` to its original position in the DOM when it gets re-attached.\n   */\n  _previousHostParent;\n  /** Stream of keydown events dispatched to this overlay. */\n  _keydownEvents = new Subject();\n  /** Stream of mouse outside events dispatched to this overlay. */\n  _outsidePointerEvents = new Subject();\n  _renders = new Subject();\n  _afterRenderRef;\n  /** Reference to the currently-running `afterNextRender` call. */\n  _afterNextRenderRef;\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._animationsDisabled = _animationsDisabled;\n    this._injector = _injector;\n    this._renderer = _renderer;\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n      this._scrollStrategy.attach(this);\n    }\n    this._positionStrategy = _config.positionStrategy;\n    // Users could open the overlay from an `effect`, in which case we need to\n    // run the `afterRender` as `untracked`. We don't recommend that users do\n    // this, but we also don't want to break users who are doing it.\n    this._afterRenderRef = untracked(() => afterRender(() => {\n      this._renders.next();\n    }, {\n      injector: this._injector\n    }));\n  }\n  /** The overlay's HTML element */\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n  get backdropElement() {\n    return this._backdropRef?.element || null;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n    const attachResult = this._portalOutlet.attach(portal);\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n    this._updateStackingOrder();\n    this._updateElementSize();\n    this._updateElementDirection();\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    }\n    // We need to clean this up ourselves, because we're passing in an\n    // `EnvironmentInjector` below which won't ever be destroyed.\n    // Otherwise it causes some callbacks to be retained (see #29696).\n    this._afterNextRenderRef?.destroy();\n    // Update the position once the overlay is fully rendered before attempting to position it,\n    // as the position may depend on the size of the rendered content.\n    this._afterNextRenderRef = afterNextRender(() => {\n      // The overlay could've been detached before the callback executed.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }, {\n      injector: this._injector\n    });\n    // Enable pointer events for the overlay pane element.\n    this._togglePointerEvents(true);\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    }\n    // Only emit the `attachments` event once all other setup is done.\n    this._attachments.next();\n    // Track this overlay by the keyboard dispatcher\n    this._keyboardDispatcher.add(this);\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n    this._outsideClickDispatcher.add(this);\n    // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n    // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n    // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n    if (typeof attachResult?.onDestroy === 'function') {\n      // In most cases we control the portal and we know when it is being detached so that\n      // we can finish the disposal process. The exception is if the user passes in a custom\n      // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n      // `detach` here instead of `dispose`, because we don't know if the user intends to\n      // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n      attachResult.onDestroy(() => {\n        if (this.hasAttached()) {\n          // We have to delay the `detach` call, because detaching immediately prevents\n          // other destroy hooks from running. This is likely a framework bug similar to\n          // https://github.com/angular/angular/issues/46119\n          this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n        }\n      });\n    }\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n    this.detachBackdrop();\n    // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n    this._togglePointerEvents(false);\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n    const detachmentResult = this._portalOutlet.detach();\n    // Only emit after everything is detached.\n    this._detachments.next();\n    // Remove this overlay from keyboard dispatcher tracking.\n    this._keyboardDispatcher.remove(this);\n    // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n    this._detachContentWhenEmpty();\n    this._locationChanges.unsubscribe();\n    this._outsideClickDispatcher.remove(this);\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n  dispose() {\n    const isAttached = this.hasAttached();\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._disposeScrollStrategy();\n    this._backdropRef?.dispose();\n    this._locationChanges.unsubscribe();\n    this._keyboardDispatcher.remove(this);\n    this._portalOutlet.dispose();\n    this._attachments.complete();\n    this._backdropClick.complete();\n    this._keydownEvents.complete();\n    this._outsidePointerEvents.complete();\n    this._outsideClickDispatcher.remove(this);\n    this._host?.remove();\n    this._afterNextRenderRef?.destroy();\n    this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n    if (isAttached) {\n      this._detachments.next();\n    }\n    this._detachments.complete();\n    this._afterRenderRef.destroy();\n    this._renders.complete();\n  }\n  /** Whether the overlay has attached content. */\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n    this._positionStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n  updateSize(sizeConfig) {\n    this._config = {\n      ...this._config,\n      ...sizeConfig\n    };\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n  setDirection(dir) {\n    this._config = {\n      ...this._config,\n      direction: dir\n    };\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n  getDirection() {\n    const direction = this._config.direction;\n    if (!direction) {\n      return 'ltr';\n    }\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n    this._disposeScrollStrategy();\n    this._scrollStrategy = strategy;\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropRef?.dispose();\n    this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n      this._backdropClick.next(event);\n    });\n    if (this._animationsDisabled) {\n      this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n    }\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n    }\n    // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n    this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n    // Add class to fade-in the backdrop after one frame.\n    if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n      });\n    } else {\n      this._backdropRef.element.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n  detachBackdrop() {\n    if (this._animationsDisabled) {\n      this._backdropRef?.dispose();\n      this._backdropRef = null;\n    } else {\n      this._backdropRef?.detach();\n    }\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n  _detachContentWhenEmpty() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._renders.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n            this._host.remove();\n          }\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n    scrollStrategy?.disable();\n    scrollStrategy?.detach?.();\n  }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n  _viewportRuler;\n  _document;\n  _platform;\n  _overlayContainer;\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  /** Whether we're performing the very first positioning of the overlay. */\n  _isInitialRender;\n  /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n  _lastBoundingBoxSize = {\n    width: 0,\n    height: 0\n  };\n  /** Whether the overlay was pushed in a previous positioning. */\n  _isPushed = false;\n  /** Whether the overlay can be pushed on-screen on the initial open. */\n  _canPush = true;\n  /** Whether the overlay can grow via flexible width/height after the initial open. */\n  _growAfterOpen = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  _hasFlexibleDimensions = true;\n  /** Whether the overlay position is locked. */\n  _positionLocked = false;\n  /** Cached origin dimensions */\n  _originRect;\n  /** Cached overlay dimensions */\n  _overlayRect;\n  /** Cached viewport dimensions */\n  _viewportRect;\n  /** Cached container dimensions */\n  _containerRect;\n  /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n  _viewportMargin = 0;\n  /** The Scrollable containers used to check scrollable view properties on position change. */\n  _scrollables = [];\n  /** Ordered list of preferred positions, from most to least desirable. */\n  _preferredPositions = [];\n  /** The origin element against which the overlay will be positioned. */\n  _origin;\n  /** The overlay pane element. */\n  _pane;\n  /** Whether the strategy has been disposed of already. */\n  _isDisposed;\n  /**\n   * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n   * within the viewport.\n   */\n  _boundingBox;\n  /** The last position to have been calculated as the best fit position. */\n  _lastPosition;\n  /** The last calculated scroll visibility. Only tracked  */\n  _lastScrollVisibility;\n  /** Subject that emits whenever the position changes. */\n  _positionChanges = new Subject();\n  /** Subscription to viewport size changes. */\n  _resizeSubscription = Subscription.EMPTY;\n  /** Default offset for the overlay along the x axis. */\n  _offsetX = 0;\n  /** Default offset for the overlay along the y axis. */\n  _offsetY = 0;\n  /** Selector to be used when finding the elements on which to set the transform origin. */\n  _transformOriginSelector;\n  /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n  _appliedPanelClasses = [];\n  /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n  _previousPushAmount;\n  /** Observable sequence of position changes. */\n  positionChanges = this._positionChanges;\n  /** Ordered list of preferred positions, from most to least desirable. */\n  get positions() {\n    return this._preferredPositions;\n  }\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    this.setOrigin(connectedTo);\n  }\n  /** Attaches this position strategy to an overlay. */\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n    this._validatePositions();\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n    this._resizeSubscription.unsubscribe();\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n    this._clearPanelClasses();\n    this._resetOverlayElementStyles();\n    this._resetBoundingBoxStyles();\n    // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect;\n    // Positions where the overlay will fit with flexible dimensions.\n    const flexibleFits = [];\n    // Fallback if none of the preferred positions fit within the viewport.\n    let fallback;\n    // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n      // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n      // Calculate how well the overlay would fit into the viewport with this point.\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n      // If the overlay, without any further work, fits into the viewport, use this position.\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n        this._applyPosition(pos, originPoint);\n        return;\n      }\n      // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      }\n      // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    }\n    // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n      this._isPushed = false;\n      this._applyPosition(bestFit.position, bestFit.origin);\n      return;\n    }\n    // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n      this._applyPosition(fallback.position, fallback.originPoint);\n      return;\n    }\n    // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n  detach() {\n    this._clearPanelClasses();\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    }\n    // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n    this.detach();\n    this._positionChanges.complete();\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n    const lastPosition = this._lastPosition;\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n  withPositions(positions) {\n    this._preferredPositions = positions;\n    // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n    this._validatePositions();\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    }\n    // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n    let y;\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    }\n    // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n    let overlayStartY;\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    }\n    // The (x, y) coordinates of the overlay.\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    // Account for the offsets since they could push the overlay out of the viewport.\n    if (offsetX) {\n      x += offsetX;\n    }\n    if (offsetY) {\n      y += offsetY;\n    }\n    // How much the overlay would overflow at this position, on each side.\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height;\n    // Visible parts of the element on each axis.\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    }\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect;\n    // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n    // Amount by which to push the overlay in each axis such that it remains on-screen.\n    let pushX = 0;\n    let pushY = 0;\n    // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n    this._setOverlayElementStyles(originPoint, position);\n    this._setBoundingBoxStyles(originPoint, position);\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    }\n    // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculations can be somewhat expensive.\n    if (this._positionChanges.observers.length) {\n      const scrollVisibility = this._getScrollVisibility();\n      // We're recalculating on scroll, but we only want to emit if anything\n      // changed since downstream code might be hitting the `NgZone`.\n      if (position !== this._lastPosition || !this._lastScrollVisibility || !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n        const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n        this._positionChanges.next(changeEvent);\n      }\n      this._lastScrollVisibility = scrollVisibility;\n    }\n    // Save the last connected position in case the position needs to be re-calculated.\n    this._lastPosition = position;\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n    let xOrigin;\n    let yOrigin = position.overlayY;\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n    const isRtl = this._isRtl();\n    let height, top, bottom;\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    }\n    // The overlay is opening 'right-ward' (the content flows to the right).\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl;\n    // The overlay is opening 'left-ward' (the content flows to the left).\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin * 2;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stretches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n    // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n    const styles = {};\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right);\n      // Push the pane content towards the proper direction.\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n    const hasExactPosition = this._hasExactPosition();\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n    const config = this._overlayRef.getConfig();\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    }\n    // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n    let transformString = '';\n    let offsetX = this._getOffset(position, 'x');\n    let offsetY = this._getOffset(position, 'y');\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n    styles.transform = transformString.trim();\n    // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    }\n    // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n    let horizontalStyleProperty;\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    }\n    // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n    const overlayBounds = this._pane.getBoundingClientRect();\n    // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breaking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      }\n      // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the DOMRect of the current origin. */\n  _getOriginRect() {\n    const origin = this._origin;\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    }\n    // Check for Element so SVG elements are also supported.\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n    const width = origin.width || 0;\n    const height = origin.height || 0;\n    // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n  if (a === b) {\n    return true;\n  }\n  return a.isOriginClipped === b.isOriginClipped && a.isOriginOutsideView === b.isOriginOutsideView && a.isOverlayClipped === b.isOverlayClipped && a.isOverlayOutsideView === b.isOverlayOutsideView;\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [{\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n  /** The overlay to which this strategy is attached. */\n  _overlayRef;\n  _cssPosition = 'static';\n  _topOffset = '';\n  _bottomOffset = '';\n  _alignItems = '';\n  _xPosition = '';\n  _xOffset = '';\n  _width = '';\n  _height = '';\n  _isDisposed = false;\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n  left(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'left';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n  right(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'right';\n    return this;\n  }\n  /**\n   * Sets the overlay to the start of the viewport, depending on the overlay direction.\n   * This will be to the left in LTR layouts and to the right in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  start(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'start';\n    return this;\n  }\n  /**\n   * Sets the overlay to the end of the viewport, depending on the overlay direction.\n   * This will be to the right in LTR layouts and to the left in RTL.\n   * @param offset Offset from the edge of the screen.\n   */\n  end(value = '') {\n    this._xOffset = value;\n    this._xPosition = 'end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._xPosition = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n    const config = this._overlayRef.getConfig();\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    const xPosition = this._xPosition;\n    const xOffset = this._xOffset;\n    const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n    let marginLeft = '';\n    let marginRight = '';\n    let justifyContent = '';\n    if (shouldBeFlushHorizontally) {\n      justifyContent = 'flex-start';\n    } else if (xPosition === 'center') {\n      justifyContent = 'center';\n      if (isRtl) {\n        marginRight = xOffset;\n      } else {\n        marginLeft = xOffset;\n      }\n    } else if (isRtl) {\n      if (xPosition === 'left' || xPosition === 'end') {\n        justifyContent = 'flex-end';\n        marginLeft = xOffset;\n      } else if (xPosition === 'right' || xPosition === 'start') {\n        justifyContent = 'flex-start';\n        marginRight = xOffset;\n      }\n    } else if (xPosition === 'left' || xPosition === 'start') {\n      justifyContent = 'flex-start';\n      marginLeft = xOffset;\n    } else if (xPosition === 'right' || xPosition === 'end') {\n      justifyContent = 'flex-end';\n      marginRight = xOffset;\n    }\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n    parentStyles.justifyContent = justifyContent;\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n  _viewportRuler = inject(ViewportRuler);\n  _document = inject(DOCUMENT);\n  _platform = inject(Platform);\n  _overlayContainer = inject(OverlayContainer);\n  constructor() {}\n  /**\n   * Creates a global position strategy.\n   */\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n  static ɵfac = function OverlayPositionBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayPositionBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: OverlayPositionBuilder,\n    factory: OverlayPositionBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n  scrollStrategies = inject(ScrollStrategyOptions);\n  _overlayContainer = inject(OverlayContainer);\n  _positionBuilder = inject(OverlayPositionBuilder);\n  _keyboardDispatcher = inject(OverlayKeyboardDispatcher);\n  _injector = inject(Injector);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _directionality = inject(Directionality);\n  _location = inject(Location);\n  _outsideClickDispatcher = inject(OverlayOutsideClickDispatcher);\n  _animationsModuleType = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _idGenerator = inject(_IdGenerator);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _appRef;\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  constructor() {}\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n  create(config) {\n    // This is done in the overlay container as well, but we have it here\n    // since it's common to mock out the overlay container in tests.\n    this._styleLoader.load(_CdkOverlayStyleLoader);\n    const host = this._createHostElement();\n    const pane = this._createPaneElement(host);\n    const portalOutlet = this._createPortalOutlet(pane);\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector), this._renderer);\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n    pane.id = this._idGenerator.getId('cdk-overlay-');\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n  _createHostElement() {\n    const host = this._document.createElement('div');\n    this._overlayContainer.getContainerElement().appendChild(host);\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n    return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n  }\n  static ɵfac = function Overlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Overlay)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Overlay,\n    factory: Overlay.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n  elementRef = inject(ElementRef);\n  constructor() {}\n  static ɵfac = function CdkOverlayOrigin_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkOverlayOrigin)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkOverlayOrigin,\n    selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n    exportAs: [\"cdkOverlayOrigin\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n  _overlay = inject(Overlay);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  _overlayRef;\n  _templatePortal;\n  _backdropSubscription = Subscription.EMPTY;\n  _attachSubscription = Subscription.EMPTY;\n  _detachSubscription = Subscription.EMPTY;\n  _positionSubscription = Subscription.EMPTY;\n  _offsetX;\n  _offsetY;\n  _position;\n  _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n  _disposeOnNavigation = false;\n  _ngZone = inject(NgZone);\n  /** Origin for the connected overlay. */\n  origin;\n  /** Registered connected position pairs. */\n  positions;\n  /**\n   * This input overrides the positions input if specified. It lets users pass\n   * in arbitrary positioning strategies.\n   */\n  positionStrategy;\n  /** The offset in pixels for the overlay connection point on the x-axis */\n  get offsetX() {\n    return this._offsetX;\n  }\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n  get offsetY() {\n    return this._offsetY;\n  }\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The width of the overlay panel. */\n  width;\n  /** The height of the overlay panel. */\n  height;\n  /** The min width of the overlay panel. */\n  minWidth;\n  /** The min height of the overlay panel. */\n  minHeight;\n  /** The custom class to be set on the backdrop element. */\n  backdropClass;\n  /** The custom class to add to the overlay pane element. */\n  panelClass;\n  /** Margin between the overlay and the viewport edges. */\n  viewportMargin = 0;\n  /** Strategy to be used when handling scroll events while the overlay is open. */\n  scrollStrategy;\n  /** Whether the overlay is open. */\n  open = false;\n  /** Whether the overlay can be closed by user interaction. */\n  disableClose = false;\n  /** CSS selector which to set the transform origin. */\n  transformOriginSelector;\n  /** Whether or not the overlay should attach a backdrop. */\n  hasBackdrop = false;\n  /** Whether or not the overlay should be locked when scrolling. */\n  lockPosition = false;\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n  flexibleDimensions = false;\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n  growAfterOpen = false;\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n  push = false;\n  /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n  get disposeOnNavigation() {\n    return this._disposeOnNavigation;\n  }\n  set disposeOnNavigation(value) {\n    this._disposeOnNavigation = value;\n  }\n  /** Event emitted when the backdrop is clicked. */\n  backdropClick = new EventEmitter();\n  /** Event emitted when the position has changed. */\n  positionChange = new EventEmitter();\n  /** Event emitted when the overlay has been attached. */\n  attach = new EventEmitter();\n  /** Event emitted when the overlay has been detached. */\n  detach = new EventEmitter();\n  /** Emits when there are keyboard events that are targeted at the overlay. */\n  overlayKeydown = new EventEmitter();\n  /** Emits when there are mouse outside click events that are targeted at the overlay. */\n  overlayOutsideClick = new EventEmitter();\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor() {\n    const templateRef = inject(TemplateRef);\n    const viewContainerRef = inject(ViewContainerRef);\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The associated overlay reference. */\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n    this._detachSubscription.unsubscribe();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this._overlayRef?.dispose();\n  }\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n      this._overlayRef?.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n    if (changes['open']) {\n      this.open ? this.attachOverlay() : this.detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.detachOverlay();\n      }\n    });\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      const origin = this._getOriginElement();\n      const target = _getEventTarget(event);\n      if (!origin || origin !== target && !origin.contains(target)) {\n        this.overlayOutsideClick.next(event);\n      }\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir || 'ltr',\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop,\n      disposeOnNavigation: this.disposeOnNavigation\n    });\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n    this._updatePositionStrategy(strategy);\n    return strategy;\n  }\n  _getOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  _getOriginElement() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef.nativeElement;\n    }\n    if (this.origin instanceof ElementRef) {\n      return this.origin.nativeElement;\n    }\n    if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n      return this.origin;\n    }\n    return null;\n  }\n  /** Attaches the overlay. */\n  attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n    this._positionSubscription.unsubscribe();\n    // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this._ngZone.run(() => this.positionChange.emit(position));\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n    this.open = true;\n  }\n  /** Detaches the overlay. */\n  detachOverlay() {\n    this._overlayRef?.detach();\n    this._backdropSubscription.unsubscribe();\n    this._positionSubscription.unsubscribe();\n    this.open = false;\n  }\n  static ɵfac = function CdkConnectedOverlay_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkConnectedOverlay)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkConnectedOverlay,\n    selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n    inputs: {\n      origin: [0, \"cdkConnectedOverlayOrigin\", \"origin\"],\n      positions: [0, \"cdkConnectedOverlayPositions\", \"positions\"],\n      positionStrategy: [0, \"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n      offsetX: [0, \"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n      offsetY: [0, \"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n      width: [0, \"cdkConnectedOverlayWidth\", \"width\"],\n      height: [0, \"cdkConnectedOverlayHeight\", \"height\"],\n      minWidth: [0, \"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n      minHeight: [0, \"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n      backdropClass: [0, \"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n      panelClass: [0, \"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n      viewportMargin: [0, \"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n      scrollStrategy: [0, \"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n      open: [0, \"cdkConnectedOverlayOpen\", \"open\"],\n      disableClose: [0, \"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n      transformOriginSelector: [0, \"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n      hasBackdrop: [2, \"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute],\n      lockPosition: [2, \"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute],\n      flexibleDimensions: [2, \"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute],\n      growAfterOpen: [2, \"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute],\n      push: [2, \"cdkConnectedOverlayPush\", \"push\", booleanAttribute],\n      disposeOnNavigation: [2, \"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute]\n    },\n    outputs: {\n      backdropClick: \"backdropClick\",\n      positionChange: \"positionChange\",\n      attach: \"attach\",\n      detach: \"detach\",\n      overlayKeydown: \"overlayKeydown\",\n      overlayOutsideClick: \"overlayOutsideClick\"\n    },\n    exportAs: [\"cdkConnectedOverlay\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], () => [], {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayHasBackdrop',\n        transform: booleanAttribute\n      }]\n    }],\n    lockPosition: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayLockPosition',\n        transform: booleanAttribute\n      }]\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayFlexibleDimensions',\n        transform: booleanAttribute\n      }]\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayGrowAfterOpen',\n        transform: booleanAttribute\n      }]\n    }],\n    push: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayPush',\n        transform: booleanAttribute\n      }]\n    }],\n    disposeOnNavigation: [{\n      type: Input,\n      args: [{\n        alias: 'cdkConnectedOverlayDisposeOnNavigation',\n        transform: booleanAttribute\n      }]\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\nclass OverlayModule {\n  static ɵfac = function OverlayModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || OverlayModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayModule,\n    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n    imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, OverlayRef as c, OverlayPositionBuilder as d, STANDARD_DROPDOWN_BELOW_POSITIONS as e, OverlayConfig as f, ConnectionPositionPair as g, ScrollingVisibility as h, ConnectedOverlayPositionChange as i, validateHorizontalPosition as j, ScrollStrategyOptions as k, CloseScrollStrategy as l, OverlayModule as m, OverlayOutsideClickDispatcher as n, OverlayKeyboardDispatcher as o, validateVerticalPosition as v };", "map": {"version": 3, "names": ["i0", "inject", "NgZone", "Injectable", "RendererFactory2", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "untracked", "afterRender", "afterNextRender", "ElementRef", "Injector", "ANIMATION_MODULE_TYPE", "EnvironmentInjector", "ApplicationRef", "InjectionToken", "Directive", "EventEmitter", "TemplateRef", "ViewContainerRef", "booleanAttribute", "Input", "Output", "NgModule", "DOCUMENT", "Location", "P", "Platform", "_", "_bindEventWithOptions", "_getEventTarget", "_isTestEnvironment", "_CdkPrivateStyleLoader", "Subject", "Subscription", "merge", "filter", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "c", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "ScrollingModule", "s", "supportsScrollBehavior", "b", "DomPortalOutlet", "T", "TemplatePortal", "h", "PortalModule", "D", "Directionality", "_IdGenerator", "g", "ESCAPE", "hasModifierKey", "BidiModule", "scrollBehaviorSupported", "BlockScrollStrategy", "_viewportRuler", "_previousHTMLStyles", "top", "left", "_previousScrollPosition", "_isEnabled", "_document", "constructor", "document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "rootElement", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_overlayRef", "_initialScrollPosition", "overlayRef", "ngDevMode", "stream", "scrolled", "pipe", "scrollable", "overlayElement", "getElementRef", "nativeElement", "threshold", "subscribe", "scrollPosition", "Math", "abs", "_detach", "updatePosition", "unsubscribe", "detach", "has<PERSON>tta<PERSON>", "run", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "ScrollStrategyOptions_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "OverlayConfig", "positionStrategy", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "direction", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "undefined", "ConnectionPositionPair", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "origin", "overlay", "ScrollingVisibility", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "_isAttached", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "BaseOverlayDispatcher_Factory", "OverlayKeyboardDispatcher", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "_cleanupKeydown", "runOutsideAngular", "listen", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "next", "ɵOverlayKeyboardDispatcher_BaseFactory", "OverlayKeyboardDispatcher_Factory", "ɵɵgetInheritedFactory", "OverlayOutsideClickDispatcher", "_platform", "_cursorOriginalV<PERSON>ue", "_cursorStyleIsSet", "_pointerDownEventTarget", "_cleanups", "eventOptions", "capture", "_pointerDownListener", "_clickListener", "IOS", "cursor", "for<PERSON>ach", "cleanup", "target", "slice", "_outsidePointerEvents", "containsPierceShadowDom", "outsidePointerEvents", "ɵOverlayOutsideClickDispatcher_BaseFactory", "OverlayOutsideClickDispatcher_Factory", "parent", "child", "supportsShadowRoot", "ShadowRoot", "current", "host", "parentNode", "_CdkOverlayStyleLoader", "_CdkOverlayStyleLoader_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "decls", "vars", "template", "_CdkOverlayStyleLoader_Template", "rf", "ctx", "styles", "encapsulation", "changeDetection", "OnPush", "None", "OverlayContainer", "_containerElement", "_styleLoader", "getContainerElement", "_loadStyles", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "load", "OverlayContainer_Factory", "BackdropRef", "_cleanupClick", "_cleanupTransitionEnd", "_fallbackTimeout", "onClick", "clearTimeout", "dispose", "setTimeout", "pointerEvents", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_animationsDisabled", "_injector", "_backdropClick", "_attachments", "_detachments", "_positionStrategy", "_scrollStrategy", "_locationChanges", "EMPTY", "_backdropRef", "_previousHostParent", "_renders", "_afterRenderRef", "_afterNextRenderRef", "injector", "backdropElement", "hostElement", "portal", "parentElement", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "destroy", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "onDestroy", "Promise", "resolve", "then", "detachBackdrop", "detachmentResult", "_detachContentWhenEmpty", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "keydownEvents", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "enablePointer", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "cssClasses", "isAdd", "subscription", "children", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "_overlayContainer", "_isInitialRender", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_originRect", "_overlayRect", "_viewportRect", "_containerRect", "_viewportMargin", "_scrollables", "_preferredPositions", "_origin", "_isDisposed", "_boundingBox", "_lastPosition", "_lastScrollVisibility", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_transformOriginSelector", "_appliedPanelClasses", "_previousPushAmount", "position<PERSON><PERSON>es", "positions", "connectedTo", "<PERSON><PERSON><PERSON><PERSON>", "_validatePositions", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_getNarrowedViewportRect", "_getOriginRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "scrollVisibility", "_getScrollVisibility", "compareScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "a", "STANDARD_DROPDOWN_BELOW_POSITIONS", "STANDARD_DROPDOWN_ADJACENT_POSITIONS", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_alignItems", "_xPosition", "_xOffset", "_width", "_height", "end", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "xPosition", "xOffset", "marginLeft", "marginRight", "marginTop", "marginBottom", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "OverlayPositionBuilder_Factory", "Overlay", "scrollStrategies", "_positionBuilder", "_directionality", "_animationsModuleType", "optional", "_idGenerator", "_appRef", "create", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "get", "id", "getId", "Overlay_Factory", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "CdkOverlayOrigin_Factory", "ɵdir", "ɵɵdefineDirective", "exportAs", "CdkConnectedOverlay", "_overlay", "_dir", "_templatePortal", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "_position", "_scrollStrategyFactory", "_disposeOnNavigation", "_updatePositionStrategy", "viewportMargin", "open", "disableClose", "transformOriginSelector", "lockPosition", "positionChange", "overlayKeydown", "overlayOutsideClick", "templateRef", "viewContainerRef", "ngOnChanges", "changes", "attachOverlay", "detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_get<PERSON><PERSON>in<PERSON><PERSON>", "_createPositionStrategy", "currentPosition", "_get<PERSON><PERSON>in", "CdkConnectedOverlay_Factory", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "alias", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector", "providers", "B", "C", "F", "G", "N", "O", "R", "S", "d", "e", "f", "j", "k", "l", "m", "n", "o", "v"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@angular/cdk/fesm2022/overlay-module-BUj0D19H.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable, RendererFactory2, Component, ChangeDetectionStrategy, ViewEncapsulation, untracked, afterRender, afterNextRender, ElementRef, Injector, ANIMATION_MODULE_TYPE, EnvironmentInjector, ApplicationRef, InjectionToken, Directive, EventEmitter, TemplateRef, ViewContainerRef, booleanAttribute, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT, Location } from '@angular/common';\nimport { P as Platform } from './platform-DmdVEw_C.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport { _ as _getEventTarget } from './shadow-dom-B0oHn41l.mjs';\nimport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-Cu9AvjH9.mjs';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { filter, takeUntil, takeWhile } from 'rxjs/operators';\nimport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport { c as coerceArray } from './array-I1yfCXUO.mjs';\nimport { ScrollDispatcher, ViewportRuler, ScrollingModule } from './scrolling.mjs';\nimport { s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nimport { b as DomPortalOutlet, T as TemplatePortal, h as PortalModule } from './portal-directives-Bw5woq8I.mjs';\nimport { D as Directionality } from './directionality-CBXD4hga.mjs';\nimport { _ as _IdGenerator } from './id-generator-Dw_9dSDu.mjs';\nimport { g as ESCAPE } from './keycodes-CpHkExLC.mjs';\nimport { hasModifierKey } from './keycodes.mjs';\nimport { BidiModule } from './bidi.mjs';\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    _viewportRuler;\n    _previousHTMLStyles = { top: '', left: '' };\n    _previousScrollPosition;\n    _isEnabled = false;\n    _document;\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const rootElement = this._document.documentElement;\n        const viewport = this._viewportRuler.getViewportSize();\n        return rootElement.scrollHeight > viewport.height || rootElement.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    _scrollDispatcher;\n    _ngZone;\n    _viewportRuler;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    _initialScrollPosition;\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0).pipe(filter(scrollable => {\n            return (!scrollable ||\n                !this._overlayRef.overlayElement.contains(scrollable.getElementRef().nativeElement));\n        }));\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n    /** Detaches the overlay ref and disables the scroll strategy. */\n    _detach = () => {\n        this.disable();\n        if (this._overlayRef.hasAttached()) {\n            this._ngZone.run(() => this._overlayRef.detach());\n        }\n    };\n}\n\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    _scrollDispatcher;\n    _viewportRuler;\n    _ngZone;\n    _config;\n    _scrollSubscription = null;\n    _overlayRef;\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    _scrollDispatcher = inject(ScrollDispatcher);\n    _viewportRuler = inject(ViewportRuler);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    constructor() { }\n    /** Do nothing on scroll. */\n    noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n    close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n    block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n    reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    /** Strategy with which to position the overlay. */\n    positionStrategy;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n    panelClass = '';\n    /** Whether the overlay has a backdrop. */\n    hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n    backdropClass = 'cdk-overlay-dark-backdrop';\n    /** The width of the overlay panel. If a number is provided, pixel units are assumed. */\n    width;\n    /** The height of the overlay panel. If a number is provided, pixel units are assumed. */\n    height;\n    /** The min-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    minWidth;\n    /** The min-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    minHeight;\n    /** The max-width of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxWidth;\n    /** The max-height of the overlay panel. If a number is provided, pixel units are assumed. */\n    maxHeight;\n    /**\n     * Direction of the text in the overlay panel. If a `Directionality` instance\n     * is passed in, the overlay will handle changes to its value automatically.\n     */\n    direction;\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n    disposeOnNavigation = false;\n    constructor(config) {\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the possible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    offsetX;\n    offsetY;\n    panelClass;\n    /** X-axis attachment point for connected overlay origin. Can be 'start', 'end', or 'center'. */\n    originX;\n    /** Y-axis attachment point for connected overlay origin. Can be 'top', 'bottom', or 'center'. */\n    originY;\n    /** X-axis attachment point for connected overlay. Can be 'start', 'end', or 'center'. */\n    overlayX;\n    /** Y-axis attachment point for connected overlay. Can be 'top', 'bottom', or 'center'. */\n    overlayY;\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n    isOriginClipped;\n    isOriginOutsideView;\n    isOverlayClipped;\n    isOverlayOutsideView;\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    connectionPair;\n    scrollableViewProperties;\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    /** Currently attached overlays in the order they were attached. */\n    _attachedOverlays = [];\n    _document = inject(DOCUMENT);\n    _isAttached;\n    constructor() { }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cleanupKeydown;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            this._ngZone.runOutsideAngular(() => {\n                this._cleanupKeydown = this._renderer.listen('body', 'keydown', this._keydownListener);\n            });\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanupKeydown?.();\n            this._isAttached = false;\n        }\n    }\n    /** Keyboard event listener that will be attached to the body. */\n    _keydownListener = (event) => {\n        const overlays = this._attachedOverlays;\n        for (let i = overlays.length - 1; i > -1; i--) {\n            // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n            // We want to target the most recent overlay, rather than trying to match where the event came\n            // from, because some components might open an overlay, but keep focus on a trigger element\n            // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n            // because we don't want overlays that don't handle keyboard events to block the ones below\n            // them that do.\n            if (overlays[i]._keydownEvents.observers.length > 0) {\n                this._ngZone.run(() => overlays[i]._keydownEvents.next(event));\n                break;\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _cursorOriginalValue;\n    _cursorStyleIsSet = false;\n    _pointerDownEventTarget;\n    _cleanups;\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            const eventOptions = { capture: true };\n            this._cleanups = this._ngZone.runOutsideAngular(() => [\n                _bindEventWithOptions(this._renderer, body, 'pointerdown', this._pointerDownListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'click', this._clickListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'auxclick', this._clickListener, eventOptions),\n                _bindEventWithOptions(this._renderer, body, 'contextmenu', this._clickListener, eventOptions),\n            ]);\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._cleanups?.forEach(cleanup => cleanup());\n            this._cleanups = undefined;\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                this._document.body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    /** Store pointerdown event target to track origin of click. */\n    _pointerDownListener = (event) => {\n        this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n    _clickListener = (event) => {\n        const target = _getEventTarget(event);\n        // In case of a click event, we want to check the origin of the click\n        // (e.g. in case where a user starts a click inside the overlay and\n        // releases the click outside of it).\n        // This is done by using the event target of the preceding pointerdown event.\n        // Every click event caused by a pointer device has a preceding pointerdown\n        // event, unless the click was programmatically triggered (e.g. in a unit test).\n        const origin = event.type === 'click' && this._pointerDownEventTarget\n            ? this._pointerDownEventTarget\n            : target;\n        // Reset the stored pointerdown event target, to avoid having it interfere\n        // in subsequent events.\n        this._pointerDownEventTarget = null;\n        // We copy the array because the original may be modified asynchronously if the\n        // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n        // the for loop.\n        const overlays = this._attachedOverlays.slice();\n        // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n        // We want to target all overlays for which the click could be considered as outside click.\n        // As soon as we reach an overlay for which the click is not outside click we break off\n        // the loop.\n        for (let i = overlays.length - 1; i > -1; i--) {\n            const overlayRef = overlays[i];\n            if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                continue;\n            }\n            // If it's a click inside the overlay, just break - we should do nothing\n            // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n            // and proceed with the next overlay\n            if (containsPierceShadowDom(overlayRef.overlayElement, target) ||\n                containsPierceShadowDom(overlayRef.overlayElement, origin)) {\n                break;\n            }\n            const outsidePointerEvents = overlayRef._outsidePointerEvents;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.run(() => outsidePointerEvents.next(event));\n            }\n            else {\n                outsidePointerEvents.next(event);\n            }\n        }\n    };\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** Version of `Element.contains` that transcends shadow DOM boundaries. */\nfunction containsPierceShadowDom(parent, child) {\n    const supportsShadowRoot = typeof ShadowRoot !== 'undefined' && ShadowRoot;\n    let current = child;\n    while (current) {\n        if (current === parent) {\n            return true;\n        }\n        current =\n            supportsShadowRoot && current instanceof ShadowRoot ? current.host : current.parentNode;\n    }\n    return false;\n}\n\nclass _CdkOverlayStyleLoader {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkOverlayStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.6\", type: _CdkOverlayStyleLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-overlay-style-loader\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: _CdkOverlayStyleLoader, decorators: [{\n            type: Component,\n            args: [{ template: '', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: { 'cdk-overlay-style-loader': '' }, styles: [\".cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed}@layer cdk-overlay{.cdk-overlay-container{z-index:1000}}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute}@layer cdk-overlay{.cdk-global-overlay-wrapper{z-index:1000}}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;display:flex;max-width:100%;max-height:100%}@layer cdk-overlay{.cdk-overlay-pane{z-index:1000}}.cdk-overlay-backdrop{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:auto;-webkit-tap-highlight-color:rgba(0,0,0,0);opacity:0;touch-action:manipulation}@layer cdk-overlay{.cdk-overlay-backdrop{z-index:1000;transition:opacity 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}}@media(prefers-reduced-motion){.cdk-overlay-backdrop{transition-duration:1ms}}.cdk-overlay-backdrop-showing{opacity:1}@media(forced-colors: active){.cdk-overlay-backdrop-showing{opacity:.6}}@layer cdk-overlay{.cdk-overlay-dark-backdrop{background:rgba(0,0,0,.32)}}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing,.cdk-high-contrast-active .cdk-overlay-transparent-backdrop{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;display:flex;flex-direction:column;min-width:1px;min-height:1px}@layer cdk-overlay{.cdk-overlay-connected-position-bounding-box{z-index:1000}}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}\\n\"] }]\n        }] });\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    _platform = inject(Platform);\n    _containerElement;\n    _document = inject(DOCUMENT);\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    constructor() { }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        this._loadStyles();\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n    /** Loads the structural styles necessary for the overlay to work. */\n    _loadStyles() {\n        this._styleLoader.load(_CdkOverlayStyleLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Encapsulates the logic for attaching and detaching a backdrop. */\nclass BackdropRef {\n    _renderer;\n    _ngZone;\n    element;\n    _cleanupClick;\n    _cleanupTransitionEnd;\n    _fallbackTimeout;\n    constructor(document, _renderer, _ngZone, onClick) {\n        this._renderer = _renderer;\n        this._ngZone = _ngZone;\n        this.element = document.createElement('div');\n        this.element.classList.add('cdk-overlay-backdrop');\n        this._cleanupClick = _renderer.listen(this.element, 'click', onClick);\n    }\n    detach() {\n        this._ngZone.runOutsideAngular(() => {\n            const element = this.element;\n            clearTimeout(this._fallbackTimeout);\n            this._cleanupTransitionEnd?.();\n            this._cleanupTransitionEnd = this._renderer.listen(element, 'transitionend', this.dispose);\n            this._fallbackTimeout = setTimeout(this.dispose, 500);\n            // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n            // In this case we make it unclickable and we try to remove it after a delay.\n            element.style.pointerEvents = 'none';\n            element.classList.remove('cdk-overlay-backdrop-showing');\n        });\n    }\n    dispose = () => {\n        clearTimeout(this._fallbackTimeout);\n        this._cleanupClick?.();\n        this._cleanupTransitionEnd?.();\n        this._cleanupClick = this._cleanupTransitionEnd = this._fallbackTimeout = undefined;\n        this.element.remove();\n    };\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    _portalOutlet;\n    _host;\n    _pane;\n    _config;\n    _ngZone;\n    _keyboardDispatcher;\n    _document;\n    _location;\n    _outsideClickDispatcher;\n    _animationsDisabled;\n    _injector;\n    _renderer;\n    _backdropClick = new Subject();\n    _attachments = new Subject();\n    _detachments = new Subject();\n    _positionStrategy;\n    _scrollStrategy;\n    _locationChanges = Subscription.EMPTY;\n    _backdropRef = null;\n    /**\n     * Reference to the parent of the `_host` at the time it was detached. Used to restore\n     * the `_host` to its original position in the DOM when it gets re-attached.\n     */\n    _previousHostParent;\n    /** Stream of keydown events dispatched to this overlay. */\n    _keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n    _outsidePointerEvents = new Subject();\n    _renders = new Subject();\n    _afterRenderRef;\n    /** Reference to the currently-running `afterNextRender` call. */\n    _afterNextRenderRef;\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher, _animationsDisabled = false, _injector, _renderer) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._animationsDisabled = _animationsDisabled;\n        this._injector = _injector;\n        this._renderer = _renderer;\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n        // Users could open the overlay from an `effect`, in which case we need to\n        // run the `afterRender` as `untracked`. We don't recommend that users do\n        // this, but we also don't want to break users who are doing it.\n        this._afterRenderRef = untracked(() => afterRender(() => {\n            this._renders.next();\n        }, { injector: this._injector }));\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropRef?.element || null;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // We need to clean this up ourselves, because we're passing in an\n        // `EnvironmentInjector` below which won't ever be destroyed.\n        // Otherwise it causes some callbacks to be retained (see #29696).\n        this._afterNextRenderRef?.destroy();\n        // Update the position once the overlay is fully rendered before attempting to position it,\n        // as the position may depend on the size of the rendered content.\n        this._afterNextRenderRef = afterNextRender(() => {\n            // The overlay could've been detached before the callback executed.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        }, { injector: this._injector });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        // TODO(crisbeto): the null check is here, because the portal outlet returns `any`.\n        // We should be guaranteed for the result to be `ComponentRef | EmbeddedViewRef`, but\n        // `instanceof EmbeddedViewRef` doesn't appear to work at the moment.\n        if (typeof attachResult?.onDestroy === 'function') {\n            // In most cases we control the portal and we know when it is being detached so that\n            // we can finish the disposal process. The exception is if the user passes in a custom\n            // `ViewContainerRef` that isn't destroyed through the overlay API. Note that we use\n            // `detach` here instead of `dispose`, because we don't know if the user intends to\n            // reattach the overlay at a later point. It also has the advantage of waiting for animations.\n            attachResult.onDestroy(() => {\n                if (this.hasAttached()) {\n                    // We have to delay the `detach` call, because detaching immediately prevents\n                    // other destroy hooks from running. This is likely a framework bug similar to\n                    // https://github.com/angular/angular/issues/46119\n                    this._ngZone.runOutsideAngular(() => Promise.resolve().then(() => this.detach()));\n                }\n            });\n        }\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenEmpty();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._backdropRef?.dispose();\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._afterNextRenderRef?.destroy();\n        this._previousHostParent = this._pane = this._host = this._backdropRef = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n        this._afterRenderRef.destroy();\n        this._renders.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropRef?.dispose();\n        this._backdropRef = new BackdropRef(this._document, this._renderer, this._ngZone, event => {\n            this._backdropClick.next(event);\n        });\n        if (this._animationsDisabled) {\n            this._backdropRef.element.classList.add('cdk-overlay-backdrop-noop-animation');\n        }\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropRef.element, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropRef.element, this._host);\n        // Add class to fade-in the backdrop after one frame.\n        if (!this._animationsDisabled && typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => this._backdropRef?.element.classList.add(showingClass));\n            });\n        }\n        else {\n            this._backdropRef.element.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        if (this._animationsDisabled) {\n            this._backdropRef?.dispose();\n            this._backdropRef = null;\n        }\n        else {\n            this._backdropRef?.detach();\n        }\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenEmpty() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._renders\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        scrollStrategy?.disable();\n        scrollStrategy?.detach?.();\n    }\n}\n\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    _viewportRuler;\n    _document;\n    _platform;\n    _overlayContainer;\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    /** Whether we're performing the very first positioning of the overlay. */\n    _isInitialRender;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n    _lastBoundingBoxSize = { width: 0, height: 0 };\n    /** Whether the overlay was pushed in a previous positioning. */\n    _isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n    _canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n    _growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    _hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n    _positionLocked = false;\n    /** Cached origin dimensions */\n    _originRect;\n    /** Cached overlay dimensions */\n    _overlayRect;\n    /** Cached viewport dimensions */\n    _viewportRect;\n    /** Cached container dimensions */\n    _containerRect;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n    _viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n    _scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n    _preferredPositions = [];\n    /** The origin element against which the overlay will be positioned. */\n    _origin;\n    /** The overlay pane element. */\n    _pane;\n    /** Whether the strategy has been disposed of already. */\n    _isDisposed;\n    /**\n     * Parent element for the overlay panel used to constrain the overlay panel's size to fit\n     * within the viewport.\n     */\n    _boundingBox;\n    /** The last position to have been calculated as the best fit position. */\n    _lastPosition;\n    /** The last calculated scroll visibility. Only tracked  */\n    _lastScrollVisibility;\n    /** Subject that emits whenever the position changes. */\n    _positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n    _resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n    _offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n    _offsetY = 0;\n    /** Selector to be used when finding the elements on which to set the transform origin. */\n    _transformOriginSelector;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n    _appliedPanelClasses = [];\n    /** Amount by which the overlay was pushed in each axis during the last time it was positioned. */\n    _previousPushAmount;\n    /** Observable sequence of position changes. */\n    positionChanges = this._positionChanges;\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        this.setOrigin(connectedTo);\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satisfies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occurring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollVisibility = this._getScrollVisibility();\n            // We're recalculating on scroll, but we only want to emit if anything\n            // changed since downstream code might be hitting the `NgZone`.\n            if (position !== this._lastPosition ||\n                !this._lastScrollVisibility ||\n                !compareScrollVisibility(this._lastScrollVisibility, scrollVisibility)) {\n                const changeEvent = new ConnectedOverlayPositionChange(position, scrollVisibility);\n                this._positionChanges.next(changeEvent);\n            }\n            this._lastScrollVisibility = scrollVisibility;\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `DOMRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin * 2;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stretches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breaking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the DOMRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `DOMRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `DOMRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\n/** Returns whether two `ScrollingVisibility` objects are identical. */\nfunction compareScrollVisibility(a, b) {\n    if (a === b) {\n        return true;\n    }\n    return (a.isOriginClipped === b.isOriginClipped &&\n        a.isOriginOutsideView === b.isOriginOutsideView &&\n        a.isOverlayClipped === b.isOverlayClipped &&\n        a.isOverlayOutsideView === b.isOverlayOutsideView);\n}\nconst STANDARD_DROPDOWN_BELOW_POSITIONS = [\n    { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n    { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n    { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },\n];\nconst STANDARD_DROPDOWN_ADJACENT_POSITIONS = [\n    { originX: 'end', originY: 'top', overlayX: 'start', overlayY: 'top' },\n    { originX: 'end', originY: 'bottom', overlayX: 'start', overlayY: 'bottom' },\n    { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'top' },\n    { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'bottom' },\n];\n\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    /** The overlay to which this strategy is attached. */\n    _overlayRef;\n    _cssPosition = 'static';\n    _topOffset = '';\n    _bottomOffset = '';\n    _alignItems = '';\n    _xPosition = '';\n    _xOffset = '';\n    _width = '';\n    _height = '';\n    _isDisposed = false;\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'left';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'right';\n        return this;\n    }\n    /**\n     * Sets the overlay to the start of the viewport, depending on the overlay direction.\n     * This will be to the left in LTR layouts and to the right in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    start(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'start';\n        return this;\n    }\n    /**\n     * Sets the overlay to the end of the viewport, depending on the overlay direction.\n     * This will be to the right in LTR layouts and to the left in RTL.\n     * @param offset Offset from the edge of the screen.\n     */\n    end(value = '') {\n        this._xOffset = value;\n        this._xPosition = 'end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._xPosition = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        const xPosition = this._xPosition;\n        const xOffset = this._xOffset;\n        const isRtl = this._overlayRef.getConfig().direction === 'rtl';\n        let marginLeft = '';\n        let marginRight = '';\n        let justifyContent = '';\n        if (shouldBeFlushHorizontally) {\n            justifyContent = 'flex-start';\n        }\n        else if (xPosition === 'center') {\n            justifyContent = 'center';\n            if (isRtl) {\n                marginRight = xOffset;\n            }\n            else {\n                marginLeft = xOffset;\n            }\n        }\n        else if (isRtl) {\n            if (xPosition === 'left' || xPosition === 'end') {\n                justifyContent = 'flex-end';\n                marginLeft = xOffset;\n            }\n            else if (xPosition === 'right' || xPosition === 'start') {\n                justifyContent = 'flex-start';\n                marginRight = xOffset;\n            }\n        }\n        else if (xPosition === 'left' || xPosition === 'start') {\n            justifyContent = 'flex-start';\n            marginLeft = xOffset;\n        }\n        else if (xPosition === 'right' || xPosition === 'end') {\n            justifyContent = 'flex-end';\n            marginRight = xOffset;\n        }\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : marginLeft;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = shouldBeFlushHorizontally ? '0' : marginRight;\n        parentStyles.justifyContent = justifyContent;\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    _viewportRuler = inject(ViewportRuler);\n    _document = inject(DOCUMENT);\n    _platform = inject(Platform);\n    _overlayContainer = inject(OverlayContainer);\n    constructor() { }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    scrollStrategies = inject(ScrollStrategyOptions);\n    _overlayContainer = inject(OverlayContainer);\n    _positionBuilder = inject(OverlayPositionBuilder);\n    _keyboardDispatcher = inject(OverlayKeyboardDispatcher);\n    _injector = inject(Injector);\n    _ngZone = inject(NgZone);\n    _document = inject(DOCUMENT);\n    _directionality = inject(Directionality);\n    _location = inject(Location);\n    _outsideClickDispatcher = inject(OverlayOutsideClickDispatcher);\n    _animationsModuleType = inject(ANIMATION_MODULE_TYPE, { optional: true });\n    _idGenerator = inject(_IdGenerator);\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _appRef;\n    _styleLoader = inject(_CdkPrivateStyleLoader);\n    constructor() { }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        // This is done in the overlay container as well, but we have it here\n        // since it's common to mock out the overlay container in tests.\n        this._styleLoader.load(_CdkOverlayStyleLoader);\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher, this._animationsModuleType === 'NoopAnimations', this._injector.get(EnvironmentInjector), this._renderer);\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = this._idGenerator.getId('cdk-overlay-');\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, null, this._appRef, this._injector, this._document);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    elementRef = inject(ElementRef);\n    constructor() { }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkOverlayOrigin, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: CdkOverlayOrigin, isStandalone: true, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                }]\n        }], ctorParameters: () => [] });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    _overlay = inject(Overlay);\n    _dir = inject(Directionality, { optional: true });\n    _overlayRef;\n    _templatePortal;\n    _backdropSubscription = Subscription.EMPTY;\n    _attachSubscription = Subscription.EMPTY;\n    _detachSubscription = Subscription.EMPTY;\n    _positionSubscription = Subscription.EMPTY;\n    _offsetX;\n    _offsetY;\n    _position;\n    _scrollStrategyFactory = inject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY);\n    _disposeOnNavigation = false;\n    _ngZone = inject(NgZone);\n    /** Origin for the connected overlay. */\n    origin;\n    /** Registered connected position pairs. */\n    positions;\n    /**\n     * This input overrides the positions input if specified. It lets users pass\n     * in arbitrary positioning strategies.\n     */\n    positionStrategy;\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The width of the overlay panel. */\n    width;\n    /** The height of the overlay panel. */\n    height;\n    /** The min width of the overlay panel. */\n    minWidth;\n    /** The min height of the overlay panel. */\n    minHeight;\n    /** The custom class to be set on the backdrop element. */\n    backdropClass;\n    /** The custom class to add to the overlay pane element. */\n    panelClass;\n    /** Margin between the overlay and the viewport edges. */\n    viewportMargin = 0;\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    scrollStrategy;\n    /** Whether the overlay is open. */\n    open = false;\n    /** Whether the overlay can be closed by user interaction. */\n    disableClose = false;\n    /** CSS selector which to set the transform origin. */\n    transformOriginSelector;\n    /** Whether or not the overlay should attach a backdrop. */\n    hasBackdrop = false;\n    /** Whether or not the overlay should be locked when scrolling. */\n    lockPosition = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    flexibleDimensions = false;\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    growAfterOpen = false;\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    push = false;\n    /** Whether the overlay should be disposed of when the user goes backwards/forwards in history. */\n    get disposeOnNavigation() {\n        return this._disposeOnNavigation;\n    }\n    set disposeOnNavigation(value) {\n        this._disposeOnNavigation = value;\n    }\n    /** Event emitted when the backdrop is clicked. */\n    backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n    positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n    attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n    detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n    overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n    overlayOutsideClick = new EventEmitter();\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor() {\n        const templateRef = inject(TemplateRef);\n        const viewContainerRef = inject(ViewContainerRef);\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this._overlayRef?.dispose();\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef?.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this.attachOverlay() : this.detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            const origin = this._getOriginElement();\n            const target = _getEventTarget(event);\n            if (!origin || (origin !== target && !origin.contains(target))) {\n                this.overlayOutsideClick.next(event);\n            }\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir || 'ltr',\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n            disposeOnNavigation: this.disposeOnNavigation,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay.position().flexibleConnectedTo(this._getOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    _getOriginElement() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef.nativeElement;\n        }\n        if (this.origin instanceof ElementRef) {\n            return this.origin.nativeElement;\n        }\n        if (typeof Element !== 'undefined' && this.origin instanceof Element) {\n            return this.origin;\n        }\n        return null;\n    }\n    /** Attaches the overlay. */\n    attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this._ngZone.run(() => this.positionChange.emit(position));\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n        this.open = true;\n    }\n    /** Detaches the overlay. */\n    detachOverlay() {\n        this._overlayRef?.detach();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        this.open = false;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkConnectedOverlay, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: CdkConnectedOverlay, isStandalone: true, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\", booleanAttribute], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\", booleanAttribute], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\", booleanAttribute], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\", booleanAttribute], push: [\"cdkConnectedOverlayPush\", \"push\", booleanAttribute], disposeOnNavigation: [\"cdkConnectedOverlayDisposeOnNavigation\", \"disposeOnNavigation\", booleanAttribute] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                }]\n        }], ctorParameters: () => [], propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayHasBackdrop', transform: booleanAttribute }]\n            }], lockPosition: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayLockPosition', transform: booleanAttribute }]\n            }], flexibleDimensions: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayFlexibleDimensions', transform: booleanAttribute }]\n            }], growAfterOpen: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayGrowAfterOpen', transform: booleanAttribute }]\n            }], push: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayPush', transform: booleanAttribute }]\n            }], disposeOnNavigation: [{\n                type: Input,\n                args: [{ alias: 'cdkConnectedOverlayDisposeOnNavigation', transform: booleanAttribute }]\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\nclass OverlayModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [BidiModule, PortalModule, ScrollingModule, ScrollingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule, CdkConnectedOverlay, CdkOverlayOrigin],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\nexport { BlockScrollStrategy as B, CdkOverlayOrigin as C, FlexibleConnectedPositionStrategy as F, GlobalPositionStrategy as G, NoopScrollStrategy as N, OverlayContainer as O, RepositionScrollStrategy as R, STANDARD_DROPDOWN_ADJACENT_POSITIONS as S, Overlay as a, CdkConnectedOverlay as b, OverlayRef as c, OverlayPositionBuilder as d, STANDARD_DROPDOWN_BELOW_POSITIONS as e, OverlayConfig as f, ConnectionPositionPair as g, ScrollingVisibility as h, ConnectedOverlayPositionChange as i, validateHorizontalPosition as j, ScrollStrategyOptions as k, CloseScrollStrategy as l, OverlayModule as m, OverlayOutsideClickDispatcher as n, OverlayKeyboardDispatcher as o, validateVerticalPosition as v };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,SAAS,EAAEC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACjX,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,iBAAiB;AACpD,SAASC,CAAC,IAAIC,QAAQ,QAAQ,yBAAyB;AACvD,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,wCAAwC;AACnF,SAASD,CAAC,IAAIE,eAAe,QAAQ,2BAA2B;AAChE,SAASF,CAAC,IAAIG,kBAAkB,QAAQ,iCAAiC;AACzE,SAASH,CAAC,IAAII,sBAAsB,QAAQ,6BAA6B;AACzE,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,gCAAgC;AACzE,SAASD,CAAC,IAAIE,WAAW,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,eAAe,QAAQ,iBAAiB;AAClF,SAASC,CAAC,IAAIC,sBAAsB,QAAQ,0BAA0B;AACtE,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,QAAQ,kCAAkC;AAC/G,SAASC,CAAC,IAAIC,cAAc,QAAQ,+BAA+B;AACnE,SAAS1B,CAAC,IAAI2B,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,MAAM,QAAQ,yBAAyB;AACrD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,UAAU,QAAQ,YAAY;AAEvC,MAAMC,uBAAuB,GAAGd,sBAAsB,CAAC,CAAC;AACxD;AACA;AACA;AACA,MAAMe,mBAAmB,CAAC;EACtBC,cAAc;EACdC,mBAAmB,GAAG;IAAEC,GAAG,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC;EAC3CC,uBAAuB;EACvBC,UAAU,GAAG,KAAK;EAClBC,SAAS;EACTC,WAAWA,CAACP,cAAc,EAAEQ,QAAQ,EAAE;IAClC,IAAI,CAACR,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGE,QAAQ;EAC7B;EACA;EACAC,MAAMA,CAAA,EAAG,CAAE;EACX;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAG,IAAI,CAACN,SAAS,CAACO,eAAe;MAC3C,IAAI,CAACT,uBAAuB,GAAG,IAAI,CAACJ,cAAc,CAACc,yBAAyB,CAAC,CAAC;MAC9E;MACA,IAAI,CAACb,mBAAmB,CAACE,IAAI,GAAGS,IAAI,CAACG,KAAK,CAACZ,IAAI,IAAI,EAAE;MACrD,IAAI,CAACF,mBAAmB,CAACC,GAAG,GAAGU,IAAI,CAACG,KAAK,CAACb,GAAG,IAAI,EAAE;MACnD;MACA;MACAU,IAAI,CAACG,KAAK,CAACZ,IAAI,GAAGzB,mBAAmB,CAAC,CAAC,IAAI,CAAC0B,uBAAuB,CAACD,IAAI,CAAC;MACzES,IAAI,CAACG,KAAK,CAACb,GAAG,GAAGxB,mBAAmB,CAAC,CAAC,IAAI,CAAC0B,uBAAuB,CAACF,GAAG,CAAC;MACvEU,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;MAC5C,IAAI,CAACZ,UAAU,GAAG,IAAI;IAC1B;EACJ;EACA;EACAa,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACb,UAAU,EAAE;MACjB,MAAMc,IAAI,GAAG,IAAI,CAACb,SAAS,CAACO,eAAe;MAC3C,MAAMO,IAAI,GAAG,IAAI,CAACd,SAAS,CAACc,IAAI;MAChC,MAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAK;MAC5B,MAAMO,SAAS,GAAGF,IAAI,CAACL,KAAK;MAC5B,MAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAc,IAAI,EAAE;MACjE,MAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAc,IAAI,EAAE;MACjE,IAAI,CAACnB,UAAU,GAAG,KAAK;MACvBgB,SAAS,CAAClB,IAAI,GAAG,IAAI,CAACF,mBAAmB,CAACE,IAAI;MAC9CkB,SAAS,CAACnB,GAAG,GAAG,IAAI,CAACD,mBAAmB,CAACC,GAAG;MAC5CiB,IAAI,CAACH,SAAS,CAACU,MAAM,CAAC,wBAAwB,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAI5B,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGF,SAAS,CAACE,cAAc,GAAG,MAAM;MAChE;MACAG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACxB,uBAAuB,CAACD,IAAI,EAAE,IAAI,CAACC,uBAAuB,CAACF,GAAG,CAAC;MAClF,IAAIJ,uBAAuB,EAAE;QACzBuB,SAAS,CAACG,cAAc,GAAGD,0BAA0B;QACrDD,SAAS,CAACE,cAAc,GAAGC,0BAA0B;MACzD;IACJ;EACJ;EACAd,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,MAAMQ,IAAI,GAAG,IAAI,CAACb,SAAS,CAACO,eAAe;IAC3C,IAAIM,IAAI,CAACH,SAAS,CAACa,QAAQ,CAAC,wBAAwB,CAAC,IAAI,IAAI,CAACxB,UAAU,EAAE;MACtE,OAAO,KAAK;IAChB;IACA,MAAMyB,WAAW,GAAG,IAAI,CAACxB,SAAS,CAACO,eAAe;IAClD,MAAMkB,QAAQ,GAAG,IAAI,CAAC/B,cAAc,CAACgC,eAAe,CAAC,CAAC;IACtD,OAAOF,WAAW,CAACG,YAAY,GAAGF,QAAQ,CAACG,MAAM,IAAIJ,WAAW,CAACK,WAAW,GAAGJ,QAAQ,CAACK,KAAK;EACjG;AACJ;;AAEA;AACA;AACA;AACA,SAASC,wCAAwCA,CAAA,EAAG;EAChD,OAAOC,KAAK,CAAC,4CAA4C,CAAC;AAC9D;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBC,iBAAiB;EACjBC,OAAO;EACPzC,cAAc;EACd0C,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BC,WAAW;EACXC,sBAAsB;EACtBtC,WAAWA,CAACiC,iBAAiB,EAAEC,OAAO,EAAEzC,cAAc,EAAE0C,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC0C,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAjC,MAAMA,CAACqC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,KAAK,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMV,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACO,WAAW,GAAGE,UAAU;EACjC;EACA;EACApC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACiC,mBAAmB,EAAE;MAC1B;IACJ;IACA,MAAMK,MAAM,GAAG,IAAI,CAACR,iBAAiB,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC5E,MAAM,CAAC6E,UAAU,IAAI;MACxE,OAAQ,CAACA,UAAU,IACf,CAAC,IAAI,CAACP,WAAW,CAACQ,cAAc,CAACvB,QAAQ,CAACsB,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC;IAC3F,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACZ,OAAO,IAAI,IAAI,CAACA,OAAO,CAACa,SAAS,IAAI,IAAI,CAACb,OAAO,CAACa,SAAS,GAAG,CAAC,EAAE;MACtE,IAAI,CAACV,sBAAsB,GAAG,IAAI,CAAC7C,cAAc,CAACc,yBAAyB,CAAC,CAAC,CAACZ,GAAG;MACjF,IAAI,CAACyC,mBAAmB,GAAGK,MAAM,CAACQ,SAAS,CAAC,MAAM;QAC9C,MAAMC,cAAc,GAAG,IAAI,CAACzD,cAAc,CAACc,yBAAyB,CAAC,CAAC,CAACZ,GAAG;QAC1E,IAAIwD,IAAI,CAACC,GAAG,CAACF,cAAc,GAAG,IAAI,CAACZ,sBAAsB,CAAC,GAAG,IAAI,CAACH,OAAO,CAACa,SAAS,EAAE;UACjF,IAAI,CAACK,OAAO,CAAC,CAAC;QAClB,CAAC,MACI;UACD,IAAI,CAAChB,WAAW,CAACiB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAClB,mBAAmB,GAAGK,MAAM,CAACQ,SAAS,CAAC,IAAI,CAACI,OAAO,CAAC;IAC7D;EACJ;EACA;EACA1C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7C,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;EACA;EACAgB,OAAO,GAAGA,CAAA,KAAM;IACZ,IAAI,CAAC1C,OAAO,CAAC,CAAC;IACd,IAAI,IAAI,CAAC0B,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MAChC,IAAI,CAACvB,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACrB,WAAW,CAACmB,MAAM,CAAC,CAAC,CAAC;IACrD;EACJ,CAAC;AACL;;AAEA;AACA,MAAMG,kBAAkB,CAAC;EACrB;EACAxD,MAAMA,CAAA,EAAG,CAAE;EACX;EACAQ,OAAOA,CAAA,EAAG,CAAE;EACZ;EACAT,MAAMA,CAAA,EAAG,CAAE;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0D,4BAA4BA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC7D,OAAOA,gBAAgB,CAACC,IAAI,CAACC,eAAe,IAAI;IAC5C,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,GAAGF,eAAe,CAACrE,GAAG;IACzD,MAAMwE,YAAY,GAAGN,OAAO,CAAClE,GAAG,GAAGqE,eAAe,CAACE,MAAM;IACzD,MAAME,WAAW,GAAGP,OAAO,CAACQ,KAAK,GAAGL,eAAe,CAACpE,IAAI;IACxD,MAAM0E,YAAY,GAAGT,OAAO,CAACjE,IAAI,GAAGoE,eAAe,CAACK,KAAK;IACzD,OAAOJ,YAAY,IAAIE,YAAY,IAAIC,WAAW,IAAIE,YAAY;EACtE,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACV,OAAO,EAAEC,gBAAgB,EAAE;EAC5D,OAAOA,gBAAgB,CAACC,IAAI,CAACS,mBAAmB,IAAI;IAChD,MAAMC,YAAY,GAAGZ,OAAO,CAAClE,GAAG,GAAG6E,mBAAmB,CAAC7E,GAAG;IAC1D,MAAM+E,YAAY,GAAGb,OAAO,CAACK,MAAM,GAAGM,mBAAmB,CAACN,MAAM;IAChE,MAAMS,WAAW,GAAGd,OAAO,CAACjE,IAAI,GAAG4E,mBAAmB,CAAC5E,IAAI;IAC3D,MAAMgF,YAAY,GAAGf,OAAO,CAACQ,KAAK,GAAGG,mBAAmB,CAACH,KAAK;IAC9D,OAAOI,YAAY,IAAIC,YAAY,IAAIC,WAAW,IAAIC,YAAY;EACtE,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3B5C,iBAAiB;EACjBxC,cAAc;EACdyC,OAAO;EACPC,OAAO;EACPC,mBAAmB,GAAG,IAAI;EAC1BC,WAAW;EACXrC,WAAWA,CAACiC,iBAAiB,EAAExC,cAAc,EAAEyC,OAAO,EAAEC,OAAO,EAAE;IAC7D,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACxC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACyC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAjC,MAAMA,CAACqC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,KAAK,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAMV,wCAAwC,CAAC,CAAC;IACpD;IACA,IAAI,CAACO,WAAW,GAAGE,UAAU;EACjC;EACA;EACApC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACiC,mBAAmB,EAAE;MAC3B,MAAM0C,QAAQ,GAAG,IAAI,CAAC3C,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4C,cAAc,GAAG,CAAC;MAC/D,IAAI,CAAC3C,mBAAmB,GAAG,IAAI,CAACH,iBAAiB,CAACS,QAAQ,CAACoC,QAAQ,CAAC,CAAC7B,SAAS,CAAC,MAAM;QACjF,IAAI,CAACZ,WAAW,CAACiB,cAAc,CAAC,CAAC;QACjC;QACA,IAAI,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC6C,SAAS,EAAE;UACxC,MAAMC,WAAW,GAAG,IAAI,CAAC5C,WAAW,CAACQ,cAAc,CAACqC,qBAAqB,CAAC,CAAC;UAC3E,MAAM;YAAErD,KAAK;YAAEF;UAAO,CAAC,GAAG,IAAI,CAAClC,cAAc,CAACgC,eAAe,CAAC,CAAC;UAC/D;UACA;UACA,MAAM0D,WAAW,GAAG,CAAC;YAAEtD,KAAK;YAAEF,MAAM;YAAEuC,MAAM,EAAEvC,MAAM;YAAE0C,KAAK,EAAExC,KAAK;YAAElC,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE;UAAE,CAAC,CAAC;UACtF,IAAIgE,4BAA4B,CAACqB,WAAW,EAAEE,WAAW,CAAC,EAAE;YACxD,IAAI,CAACxE,OAAO,CAAC,CAAC;YACd,IAAI,CAACuB,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACrB,WAAW,CAACmB,MAAM,CAAC,CAAC,CAAC;UACrD;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA7C,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACyB,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACmB,WAAW,CAAC,CAAC;MACtC,IAAI,CAACnB,mBAAmB,GAAG,IAAI;IACnC;EACJ;EACAoB,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7C,OAAO,CAAC,CAAC;IACd,IAAI,CAAC0B,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,qBAAqB,CAAC;EACxBnD,iBAAiB,GAAGtG,MAAM,CAAC0C,gBAAgB,CAAC;EAC5CoB,cAAc,GAAG9D,MAAM,CAAC2C,aAAa,CAAC;EACtC4D,OAAO,GAAGvG,MAAM,CAACC,MAAM,CAAC;EACxBmE,SAAS,GAAGpE,MAAM,CAACwB,QAAQ,CAAC;EAC5B6C,WAAWA,CAAA,EAAG,CAAE;EAChB;EACAqF,IAAI,GAAGA,CAAA,KAAM,IAAI1B,kBAAkB,CAAC,CAAC;EACrC;AACJ;AACA;AACA;EACI2B,KAAK,GAAIC,MAAM,IAAK,IAAIvD,mBAAmB,CAAC,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACzC,cAAc,EAAE8F,MAAM,CAAC;EAC9G;EACAC,KAAK,GAAGA,CAAA,KAAM,IAAIhG,mBAAmB,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAACM,SAAS,CAAC;EAC1E;AACJ;AACA;AACA;AACA;EACI0F,UAAU,GAAIF,MAAM,IAAK,IAAIV,wBAAwB,CAAC,IAAI,CAAC5C,iBAAiB,EAAE,IAAI,CAACxC,cAAc,EAAE,IAAI,CAACyC,OAAO,EAAEqD,MAAM,CAAC;EACxH,OAAOG,IAAI,YAAAC,8BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFR,qBAAqB;EAAA;EACxH,OAAOS,KAAK,kBAD6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EACYX,qBAAqB;IAAAY,OAAA,EAArBZ,qBAAqB,CAAAM,IAAA;IAAAO,UAAA,EAAc;EAAM;AACpJ;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAH6F9G,EAAE,CAAAwK,iBAAA,CAGJd,qBAAqB,EAAc,CAAC;IACnHe,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMI,aAAa,CAAC;EAChB;EACAC,gBAAgB;EAChB;EACAC,cAAc,GAAG,IAAI5C,kBAAkB,CAAC,CAAC;EACzC;EACA6C,UAAU,GAAG,EAAE;EACf;EACAC,WAAW,GAAG,KAAK;EACnB;EACAC,aAAa,GAAG,2BAA2B;EAC3C;EACA7E,KAAK;EACL;EACAF,MAAM;EACN;EACAgF,QAAQ;EACR;EACAC,SAAS;EACT;EACAC,QAAQ;EACR;EACAC,SAAS;EACT;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,mBAAmB,GAAG,KAAK;EAC3BhH,WAAWA,CAACuF,MAAM,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR;MACA;MACA;MACA,MAAM0B,UAAU,GAAGC,MAAM,CAACC,IAAI,CAAC5B,MAAM,CAAC;MACtC,KAAK,MAAM6B,GAAG,IAAIH,UAAU,EAAE;QAC1B,IAAI1B,MAAM,CAAC6B,GAAG,CAAC,KAAKC,SAAS,EAAE;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAACD,GAAG,CAAC,GAAG7B,MAAM,CAAC6B,GAAG,CAAC;QAC3B;MACJ;IACJ;EACJ;AACJ;;AAEA;AACA,MAAME,sBAAsB,CAAC;EACzBC,OAAO;EACPC,OAAO;EACPhB,UAAU;EACV;EACAiB,OAAO;EACP;EACAC,OAAO;EACP;EACAC,QAAQ;EACR;EACAC,QAAQ;EACR5H,WAAWA,CAAC6H,MAAM,EAAEC,OAAO,EAC3B;EACAP,OAAO,EACP;EACAC,OAAO,EACP;EACAhB,UAAU,EAAE;IACR,IAAI,CAACe,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAChB,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACiB,OAAO,GAAGI,MAAM,CAACJ,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAGG,MAAM,CAACH,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAGG,OAAO,CAACH,QAAQ;IAChC,IAAI,CAACC,QAAQ,GAAGE,OAAO,CAACF,QAAQ;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,CAAC;EACtBC,eAAe;EACfC,mBAAmB;EACnBC,gBAAgB;EAChBC,oBAAoB;AACxB;AACA;AACA,MAAMC,8BAA8B,CAAC;EACjCC,cAAc;EACdC,wBAAwB;EACxBtI,WAAWA,CACX;EACAqI,cAAc,EACd;EACAC,wBAAwB,EAAE;IACtB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,wBAAwB,GAAGA,wBAAwB;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EAC/C,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC7D,MAAM1G,KAAK,CAAC,8BAA8ByG,QAAQ,KAAKC,KAAK,KAAK,GAC7D,uCAAuC,CAAC;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACF,QAAQ,EAAEC,KAAK,EAAE;EACjD,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,QAAQ,EAAE;IAC5D,MAAM1G,KAAK,CAAC,8BAA8ByG,QAAQ,KAAKC,KAAK,KAAK,GAC7D,sCAAsC,CAAC;EAC/C;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,qBAAqB,CAAC;EACxB;EACAC,iBAAiB,GAAG,EAAE;EACtB7I,SAAS,GAAGpE,MAAM,CAACwB,QAAQ,CAAC;EAC5B0L,WAAW;EACX7I,WAAWA,CAAA,EAAG,CAAE;EAChB8I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtF,MAAM,CAAC,CAAC;EACjB;EACA;EACA9C,GAAGA,CAAC6B,UAAU,EAAE;IACZ;IACA,IAAI,CAACpB,MAAM,CAACoB,UAAU,CAAC;IACvB,IAAI,CAACqG,iBAAiB,CAACG,IAAI,CAACxG,UAAU,CAAC;EAC3C;EACA;EACApB,MAAMA,CAACoB,UAAU,EAAE;IACf,MAAMyG,KAAK,GAAG,IAAI,CAACJ,iBAAiB,CAACK,OAAO,CAAC1G,UAAU,CAAC;IACxD,IAAIyG,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACJ,iBAAiB,CAACM,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAACJ,iBAAiB,CAACO,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC3F,MAAM,CAAC,CAAC;IACjB;EACJ;EACA,OAAOkC,IAAI,YAAA0D,8BAAAxD,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+C,qBAAqB;EAAA;EACxH,OAAO9C,KAAK,kBAlM6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAkMY4C,qBAAqB;IAAA3C,OAAA,EAArB2C,qBAAqB,CAAAjD,IAAA;IAAAO,UAAA,EAAc;EAAM;AACpJ;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KApM6F9G,EAAE,CAAAwK,iBAAA,CAoMJyC,qBAAqB,EAAc,CAAC;IACnHxC,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA,MAAMoD,yBAAyB,SAASV,qBAAqB,CAAC;EAC1DzG,OAAO,GAAGvG,MAAM,CAACC,MAAM,CAAC;EACxB0N,SAAS,GAAG3N,MAAM,CAACG,gBAAgB,CAAC,CAACyN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DC,eAAe;EACf;EACA9I,GAAGA,CAAC6B,UAAU,EAAE;IACZ,KAAK,CAAC7B,GAAG,CAAC6B,UAAU,CAAC;IACrB;IACA,IAAI,CAAC,IAAI,CAACsG,WAAW,EAAE;MACnB,IAAI,CAAC3G,OAAO,CAACuH,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACD,eAAe,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAAC;MAC1F,CAAC,CAAC;MACF,IAAI,CAACd,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACArF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACqF,WAAW,EAAE;MAClB,IAAI,CAACW,eAAe,GAAG,CAAC;MACxB,IAAI,CAACX,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;EACAc,gBAAgB,GAAIC,KAAK,IAAK;IAC1B,MAAMC,QAAQ,GAAG,IAAI,CAACjB,iBAAiB;IACvC,KAAK,IAAIkB,CAAC,GAAGD,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C;MACA;MACA;MACA;MACA;MACA;MACA,IAAID,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACC,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;QACjD,IAAI,CAACjH,OAAO,CAACwB,GAAG,CAAC,MAAMmG,QAAQ,CAACC,CAAC,CAAC,CAACC,cAAc,CAACE,IAAI,CAACL,KAAK,CAAC,CAAC;QAC9D;MACJ;IACJ;EACJ,CAAC;EACD,OAAOlE,IAAI;IAAA,IAAAwE,sCAAA;IAAA,gBAAAC,kCAAAvE,iBAAA;MAAA,QAAAsE,sCAAA,KAAAA,sCAAA,GApP8ExO,EAAE,CAAA0O,qBAAA,CAoPQf,yBAAyB,IAAAzD,iBAAA,IAAzByD,yBAAyB;IAAA;EAAA;EAC5H,OAAOxD,KAAK,kBArP6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAqPYsD,yBAAyB;IAAArD,OAAA,EAAzBqD,yBAAyB,CAAA3D,IAAA;IAAAO,UAAA,EAAc;EAAM;AACxJ;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAvP6F9G,EAAE,CAAAwK,iBAAA,CAuPJmD,yBAAyB,EAAc,CAAC;IACvHlD,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMoE,6BAA6B,SAAS1B,qBAAqB,CAAC;EAC9D2B,SAAS,GAAG3O,MAAM,CAAC2B,QAAQ,CAAC;EAC5B4E,OAAO,GAAGvG,MAAM,CAACC,MAAM,CAAC;EACxB0N,SAAS,GAAG3N,MAAM,CAACG,gBAAgB,CAAC,CAACyN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/DgB,oBAAoB;EACpBC,iBAAiB,GAAG,KAAK;EACzBC,uBAAuB;EACvBC,SAAS;EACT;EACAhK,GAAGA,CAAC6B,UAAU,EAAE;IACZ,KAAK,CAAC7B,GAAG,CAAC6B,UAAU,CAAC;IACrB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACsG,WAAW,EAAE;MACnB,MAAMhI,IAAI,GAAG,IAAI,CAACd,SAAS,CAACc,IAAI;MAChC,MAAM8J,YAAY,GAAG;QAAEC,OAAO,EAAE;MAAK,CAAC;MACtC,IAAI,CAACF,SAAS,GAAG,IAAI,CAACxI,OAAO,CAACuH,iBAAiB,CAAC,MAAM,CAClDjM,qBAAqB,CAAC,IAAI,CAAC8L,SAAS,EAAEzI,IAAI,EAAE,aAAa,EAAE,IAAI,CAACgK,oBAAoB,EAAEF,YAAY,CAAC,EACnGnN,qBAAqB,CAAC,IAAI,CAAC8L,SAAS,EAAEzI,IAAI,EAAE,OAAO,EAAE,IAAI,CAACiK,cAAc,EAAEH,YAAY,CAAC,EACvFnN,qBAAqB,CAAC,IAAI,CAAC8L,SAAS,EAAEzI,IAAI,EAAE,UAAU,EAAE,IAAI,CAACiK,cAAc,EAAEH,YAAY,CAAC,EAC1FnN,qBAAqB,CAAC,IAAI,CAAC8L,SAAS,EAAEzI,IAAI,EAAE,aAAa,EAAE,IAAI,CAACiK,cAAc,EAAEH,YAAY,CAAC,CAChG,CAAC;MACF;MACA;MACA,IAAI,IAAI,CAACL,SAAS,CAACS,GAAG,IAAI,CAAC,IAAI,CAACP,iBAAiB,EAAE;QAC/C,IAAI,CAACD,oBAAoB,GAAG1J,IAAI,CAACL,KAAK,CAACwK,MAAM;QAC7CnK,IAAI,CAACL,KAAK,CAACwK,MAAM,GAAG,SAAS;QAC7B,IAAI,CAACR,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAI,CAAC3B,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;EACArF,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACqF,WAAW,EAAE;MAClB,IAAI,CAAC6B,SAAS,EAAEO,OAAO,CAACC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACR,SAAS,GAAGrD,SAAS;MAC1B,IAAI,IAAI,CAACiD,SAAS,CAACS,GAAG,IAAI,IAAI,CAACP,iBAAiB,EAAE;QAC9C,IAAI,CAACzK,SAAS,CAACc,IAAI,CAACL,KAAK,CAACwK,MAAM,GAAG,IAAI,CAACT,oBAAoB;QAC5D,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAClC;MACA,IAAI,CAAC3B,WAAW,GAAG,KAAK;IAC5B;EACJ;EACA;EACAgC,oBAAoB,GAAIjB,KAAK,IAAK;IAC9B,IAAI,CAACa,uBAAuB,GAAGhN,eAAe,CAACmM,KAAK,CAAC;EACzD,CAAC;EACD;EACAkB,cAAc,GAAIlB,KAAK,IAAK;IACxB,MAAMuB,MAAM,GAAG1N,eAAe,CAACmM,KAAK,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA,MAAM/B,MAAM,GAAG+B,KAAK,CAACzD,IAAI,KAAK,OAAO,IAAI,IAAI,CAACsE,uBAAuB,GAC/D,IAAI,CAACA,uBAAuB,GAC5BU,MAAM;IACZ;IACA;IACA,IAAI,CAACV,uBAAuB,GAAG,IAAI;IACnC;IACA;IACA;IACA,MAAMZ,QAAQ,GAAG,IAAI,CAACjB,iBAAiB,CAACwC,KAAK,CAAC,CAAC;IAC/C;IACA;IACA;IACA;IACA,KAAK,IAAItB,CAAC,GAAGD,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAEW,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,MAAMvH,UAAU,GAAGsH,QAAQ,CAACC,CAAC,CAAC;MAC9B,IAAIvH,UAAU,CAAC8I,qBAAqB,CAACrB,SAAS,CAACb,MAAM,GAAG,CAAC,IAAI,CAAC5G,UAAU,CAACkB,WAAW,CAAC,CAAC,EAAE;QACpF;MACJ;MACA;MACA;MACA;MACA,IAAI6H,uBAAuB,CAAC/I,UAAU,CAACM,cAAc,EAAEsI,MAAM,CAAC,IAC1DG,uBAAuB,CAAC/I,UAAU,CAACM,cAAc,EAAEgF,MAAM,CAAC,EAAE;QAC5D;MACJ;MACA,MAAM0D,oBAAoB,GAAGhJ,UAAU,CAAC8I,qBAAqB;MAC7D;MACA,IAAI,IAAI,CAACnJ,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAACwB,GAAG,CAAC,MAAM6H,oBAAoB,CAACtB,IAAI,CAACL,KAAK,CAAC,CAAC;MAC5D,CAAC,MACI;QACD2B,oBAAoB,CAACtB,IAAI,CAACL,KAAK,CAAC;MACpC;IACJ;EACJ,CAAC;EACD,OAAOlE,IAAI;IAAA,IAAA8F,0CAAA;IAAA,gBAAAC,sCAAA7F,iBAAA;MAAA,QAAA4F,0CAAA,KAAAA,0CAAA,GAlW8E9P,EAAE,CAAA0O,qBAAA,CAkWQC,6BAA6B,IAAAzE,iBAAA,IAA7ByE,6BAA6B;IAAA;EAAA;EAChI,OAAOxE,KAAK,kBAnW6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAmWYsE,6BAA6B;IAAArE,OAAA,EAA7BqE,6BAA6B,CAAA3E,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC5J;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KArW6F9G,EAAE,CAAAwK,iBAAA,CAqWJmE,6BAA6B,EAAc,CAAC;IAC3HlE,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,SAASqF,uBAAuBA,CAACI,MAAM,EAAEC,KAAK,EAAE;EAC5C,MAAMC,kBAAkB,GAAG,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU;EAC1E,IAAIC,OAAO,GAAGH,KAAK;EACnB,OAAOG,OAAO,EAAE;IACZ,IAAIA,OAAO,KAAKJ,MAAM,EAAE;MACpB,OAAO,IAAI;IACf;IACAI,OAAO,GACHF,kBAAkB,IAAIE,OAAO,YAAYD,UAAU,GAAGC,OAAO,CAACC,IAAI,GAAGD,OAAO,CAACE,UAAU;EAC/F;EACA,OAAO,KAAK;AAChB;AAEA,MAAMC,sBAAsB,CAAC;EACzB,OAAOvG,IAAI,YAAAwG,+BAAAtG,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqG,sBAAsB;EAAA;EACzH,OAAOE,IAAI,kBAzX8EzQ,EAAE,CAAA0Q,iBAAA;IAAAjG,IAAA,EAyXJ8F,sBAAsB;IAAAI,SAAA;IAAAC,SAAA,+BAAkG,EAAE;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACrN;AACA;EAAA,QAAAvK,SAAA,oBAAAA,SAAA,KA3X6F9G,EAAE,CAAAwK,iBAAA,CA2XJ+F,sBAAsB,EAAc,CAAC;IACpH9F,IAAI,EAAEpK,SAAS;IACfqK,IAAI,EAAE,CAAC;MAAEqG,QAAQ,EAAE,EAAE;MAAEM,eAAe,EAAE/Q,uBAAuB,CAACgR,MAAM;MAAEF,aAAa,EAAE7Q,iBAAiB,CAACgR,IAAI;MAAElB,IAAI,EAAE;QAAE,0BAA0B,EAAE;MAAG,CAAC;MAAEc,MAAM,EAAE,CAAC,6oDAA6oD;IAAE,CAAC;EACtzD,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,gBAAgB,CAAC;EACnB5C,SAAS,GAAG3O,MAAM,CAAC2B,QAAQ,CAAC;EAC5B6P,iBAAiB;EACjBpN,SAAS,GAAGpE,MAAM,CAACwB,QAAQ,CAAC;EAC5BiQ,YAAY,GAAGzR,MAAM,CAACgC,sBAAsB,CAAC;EAC7CqC,WAAWA,CAAA,EAAG,CAAE;EAChB8I,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqE,iBAAiB,EAAEhM,MAAM,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkM,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACH,iBAAiB,EAAE;MACzB,IAAI,CAACI,gBAAgB,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACJ,iBAAiB;EACjC;EACA;AACJ;AACA;AACA;EACII,gBAAgBA,CAAA,EAAG;IACf,MAAMC,cAAc,GAAG,uBAAuB;IAC9C;IACA;IACA;IACA,IAAI,IAAI,CAAClD,SAAS,CAACmD,SAAS,IAAI/P,kBAAkB,CAAC,CAAC,EAAE;MAClD,MAAMgQ,0BAA0B,GAAG,IAAI,CAAC3N,SAAS,CAAC4N,gBAAgB,CAAC,IAAIH,cAAc,uBAAuB,GAAG,IAAIA,cAAc,mBAAmB,CAAC;MACrJ;MACA;MACA,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,0BAA0B,CAACvE,MAAM,EAAEW,CAAC,EAAE,EAAE;QACxD4D,0BAA0B,CAAC5D,CAAC,CAAC,CAAC3I,MAAM,CAAC,CAAC;MAC1C;IACJ;IACA,MAAMyM,SAAS,GAAG,IAAI,CAAC7N,SAAS,CAAC8N,aAAa,CAAC,KAAK,CAAC;IACrDD,SAAS,CAACnN,SAAS,CAACC,GAAG,CAAC8M,cAAc,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI9P,kBAAkB,CAAC,CAAC,EAAE;MACtBkQ,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC9C,CAAC,MACI,IAAI,CAAC,IAAI,CAACxD,SAAS,CAACmD,SAAS,EAAE;MAChCG,SAAS,CAACE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;IAChD;IACA,IAAI,CAAC/N,SAAS,CAACc,IAAI,CAACkN,WAAW,CAACH,SAAS,CAAC;IAC1C,IAAI,CAACT,iBAAiB,GAAGS,SAAS;EACtC;EACA;EACAN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,YAAY,CAACY,IAAI,CAAC/B,sBAAsB,CAAC;EAClD;EACA,OAAOvG,IAAI,YAAAuI,yBAAArI,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsH,gBAAgB;EAAA;EACnH,OAAOrH,KAAK,kBAhc6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAgcYmH,gBAAgB;IAAAlH,OAAA,EAAhBkH,gBAAgB,CAAAxH,IAAA;IAAAO,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAlc6F9G,EAAE,CAAAwK,iBAAA,CAkcJgH,gBAAgB,EAAc,CAAC;IAC9G/G,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAMiI,WAAW,CAAC;EACd5E,SAAS;EACTpH,OAAO;EACP2B,OAAO;EACPsK,aAAa;EACbC,qBAAqB;EACrBC,gBAAgB;EAChBrO,WAAWA,CAACC,QAAQ,EAAEqJ,SAAS,EAAEpH,OAAO,EAAEoM,OAAO,EAAE;IAC/C,IAAI,CAAChF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACpH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC2B,OAAO,GAAG5D,QAAQ,CAAC4N,aAAa,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAChK,OAAO,CAACpD,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IAClD,IAAI,CAACyN,aAAa,GAAG7E,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC7F,OAAO,EAAE,OAAO,EAAEyK,OAAO,CAAC;EACzE;EACA9K,MAAMA,CAAA,EAAG;IACL,IAAI,CAACtB,OAAO,CAACuH,iBAAiB,CAAC,MAAM;MACjC,MAAM5F,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B0K,YAAY,CAAC,IAAI,CAACF,gBAAgB,CAAC;MACnC,IAAI,CAACD,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAAC9E,SAAS,CAACI,MAAM,CAAC7F,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC2K,OAAO,CAAC;MAC1F,IAAI,CAACH,gBAAgB,GAAGI,UAAU,CAAC,IAAI,CAACD,OAAO,EAAE,GAAG,CAAC;MACrD;MACA;MACA3K,OAAO,CAACrD,KAAK,CAACkO,aAAa,GAAG,MAAM;MACpC7K,OAAO,CAACpD,SAAS,CAACU,MAAM,CAAC,8BAA8B,CAAC;IAC5D,CAAC,CAAC;EACN;EACAqN,OAAO,GAAGA,CAAA,KAAM;IACZD,YAAY,CAAC,IAAI,CAACF,gBAAgB,CAAC;IACnC,IAAI,CAACF,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,qBAAqB,GAAG,CAAC;IAC9B,IAAI,CAACD,aAAa,GAAG,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACC,gBAAgB,GAAGhH,SAAS;IACnF,IAAI,CAACxD,OAAO,CAAC1C,MAAM,CAAC,CAAC;EACzB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMwN,UAAU,CAAC;EACbC,aAAa;EACbC,KAAK;EACLC,KAAK;EACL3M,OAAO;EACPD,OAAO;EACP6M,mBAAmB;EACnBhP,SAAS;EACTiP,SAAS;EACTC,uBAAuB;EACvBC,mBAAmB;EACnBC,SAAS;EACT7F,SAAS;EACT8F,cAAc,GAAG,IAAIxR,OAAO,CAAC,CAAC;EAC9ByR,YAAY,GAAG,IAAIzR,OAAO,CAAC,CAAC;EAC5B0R,YAAY,GAAG,IAAI1R,OAAO,CAAC,CAAC;EAC5B2R,iBAAiB;EACjBC,eAAe;EACfC,gBAAgB,GAAG5R,YAAY,CAAC6R,KAAK;EACrCC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;EACIC,mBAAmB;EACnB;EACA7F,cAAc,GAAG,IAAInM,OAAO,CAAC,CAAC;EAC9B;EACAyN,qBAAqB,GAAG,IAAIzN,OAAO,CAAC,CAAC;EACrCiS,QAAQ,GAAG,IAAIjS,OAAO,CAAC,CAAC;EACxBkS,eAAe;EACf;EACAC,mBAAmB;EACnB/P,WAAWA,CAAC4O,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAE3M,OAAO,EAAED,OAAO,EAAE6M,mBAAmB,EAAEhP,SAAS,EAAEiP,SAAS,EAAEC,uBAAuB,EAAEC,mBAAmB,GAAG,KAAK,EAAEC,SAAS,EAAE7F,SAAS,EAAE;IAC9K,IAAI,CAACsF,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC3M,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6M,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAChP,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiP,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC7F,SAAS,GAAGA,SAAS;IAC1B,IAAInH,OAAO,CAACoE,cAAc,EAAE;MACxB,IAAI,CAACiJ,eAAe,GAAGrN,OAAO,CAACoE,cAAc;MAC7C,IAAI,CAACiJ,eAAe,CAACtP,MAAM,CAAC,IAAI,CAAC;IACrC;IACA,IAAI,CAACqP,iBAAiB,GAAGpN,OAAO,CAACmE,gBAAgB;IACjD;IACA;IACA;IACA,IAAI,CAACwJ,eAAe,GAAG5T,SAAS,CAAC,MAAMC,WAAW,CAAC,MAAM;MACrD,IAAI,CAAC0T,QAAQ,CAAC5F,IAAI,CAAC,CAAC;IACxB,CAAC,EAAE;MAAE+F,QAAQ,EAAE,IAAI,CAACb;IAAU,CAAC,CAAC,CAAC;EACrC;EACA;EACA,IAAItM,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACiM,KAAK;EACrB;EACA;EACA,IAAImB,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACN,YAAY,EAAE9L,OAAO,IAAI,IAAI;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIqM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrB,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI3O,MAAMA,CAACiQ,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC,IAAI,CAACtB,KAAK,CAACuB,aAAa,IAAI,IAAI,CAACR,mBAAmB,EAAE;MACvD,IAAI,CAACA,mBAAmB,CAAC7B,WAAW,CAAC,IAAI,CAACc,KAAK,CAAC;IACpD;IACA,MAAMwB,YAAY,GAAG,IAAI,CAACzB,aAAa,CAAC1O,MAAM,CAACiQ,MAAM,CAAC;IACtD,IAAI,IAAI,CAACZ,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACrP,MAAM,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACoQ,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,IAAI,CAAChB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACrP,MAAM,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI,CAAC4P,mBAAmB,EAAEU,OAAO,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACV,mBAAmB,GAAG3T,eAAe,CAAC,MAAM;MAC7C;MACA,IAAI,IAAI,CAACqH,WAAW,CAAC,CAAC,EAAE;QACpB,IAAI,CAACH,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE;MAAE0M,QAAQ,EAAE,IAAI,CAACb;IAAU,CAAC,CAAC;IAChC;IACA,IAAI,CAACuB,oBAAoB,CAAC,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACvO,OAAO,CAACsE,WAAW,EAAE;MAC1B,IAAI,CAACkK,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAACxO,OAAO,CAACqE,UAAU,EAAE;MACzB,IAAI,CAACoK,cAAc,CAAC,IAAI,CAAC9B,KAAK,EAAE,IAAI,CAAC3M,OAAO,CAACqE,UAAU,EAAE,IAAI,CAAC;IAClE;IACA;IACA,IAAI,CAAC6I,YAAY,CAACpF,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC8E,mBAAmB,CAACrO,GAAG,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAACyB,OAAO,CAAC6E,mBAAmB,EAAE;MAClC,IAAI,CAACyI,gBAAgB,GAAG,IAAI,CAACT,SAAS,CAAC/L,SAAS,CAAC,MAAM,IAAI,CAACuL,OAAO,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAACS,uBAAuB,CAACvO,GAAG,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA,IAAI,OAAO2P,YAAY,EAAEQ,SAAS,KAAK,UAAU,EAAE;MAC/C;MACA;MACA;MACA;MACA;MACAR,YAAY,CAACQ,SAAS,CAAC,MAAM;QACzB,IAAI,IAAI,CAACpN,WAAW,CAAC,CAAC,EAAE;UACpB;UACA;UACA;UACA,IAAI,CAACvB,OAAO,CAACuH,iBAAiB,CAAC,MAAMqH,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACxN,MAAM,CAAC,CAAC,CAAC,CAAC;QACrF;MACJ,CAAC,CAAC;IACN;IACA,OAAO6M,YAAY;EACvB;EACA;AACJ;AACA;AACA;EACI7M,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;MACrB;IACJ;IACA,IAAI,CAACwN,cAAc,CAAC,CAAC;IACrB;IACA;IACA;IACA,IAAI,CAACP,oBAAoB,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,CAACnB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAAC/L,MAAM,EAAE;MACzD,IAAI,CAAC+L,iBAAiB,CAAC/L,MAAM,CAAC,CAAC;IACnC;IACA,IAAI,IAAI,CAACgM,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC7O,OAAO,CAAC,CAAC;IAClC;IACA,MAAMuQ,gBAAgB,GAAG,IAAI,CAACtC,aAAa,CAACpL,MAAM,CAAC,CAAC;IACpD;IACA,IAAI,CAAC8L,YAAY,CAACrF,IAAI,CAAC,CAAC;IACxB;IACA,IAAI,CAAC8E,mBAAmB,CAAC5N,MAAM,CAAC,IAAI,CAAC;IACrC;IACA;IACA,IAAI,CAACgQ,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC1B,gBAAgB,CAAClM,WAAW,CAAC,CAAC;IACnC,IAAI,CAAC0L,uBAAuB,CAAC9N,MAAM,CAAC,IAAI,CAAC;IACzC,OAAO+P,gBAAgB;EAC3B;EACA;EACA1C,OAAOA,CAAA,EAAG;IACN,MAAM4C,UAAU,GAAG,IAAI,CAAC3N,WAAW,CAAC,CAAC;IACrC,IAAI,IAAI,CAAC8L,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACf,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAAC6C,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC1B,YAAY,EAAEnB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACiB,gBAAgB,CAAClM,WAAW,CAAC,CAAC;IACnC,IAAI,CAACwL,mBAAmB,CAAC5N,MAAM,CAAC,IAAI,CAAC;IACrC,IAAI,CAACyN,aAAa,CAACJ,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACa,YAAY,CAACiC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAClC,cAAc,CAACkC,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACvH,cAAc,CAACuH,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACjG,qBAAqB,CAACiG,QAAQ,CAAC,CAAC;IACrC,IAAI,CAACrC,uBAAuB,CAAC9N,MAAM,CAAC,IAAI,CAAC;IACzC,IAAI,CAAC0N,KAAK,EAAE1N,MAAM,CAAC,CAAC;IACpB,IAAI,CAAC4O,mBAAmB,EAAEU,OAAO,CAAC,CAAC;IACnC,IAAI,CAACb,mBAAmB,GAAG,IAAI,CAACd,KAAK,GAAG,IAAI,CAACD,KAAK,GAAG,IAAI,CAACc,YAAY,GAAG,IAAI;IAC7E,IAAIyB,UAAU,EAAE;MACZ,IAAI,CAAC9B,YAAY,CAACrF,IAAI,CAAC,CAAC;IAC5B;IACA,IAAI,CAACqF,YAAY,CAACgC,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAACxB,eAAe,CAACW,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACZ,QAAQ,CAACyB,QAAQ,CAAC,CAAC;EAC5B;EACA;EACA7N,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmL,aAAa,CAACnL,WAAW,CAAC,CAAC;EAC3C;EACA;EACA8N,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnC,cAAc;EAC9B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACnC,YAAY;EAC5B;EACA;EACAoC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC3H,cAAc;EAC9B;EACA;EACAwB,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,qBAAqB;EACrC;EACA;EACAsG,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxP,OAAO;EACvB;EACA;EACAmB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACiM,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACqC,KAAK,CAAC,CAAC;IAClC;EACJ;EACA;EACAC,sBAAsBA,CAACC,QAAQ,EAAE;IAC7B,IAAIA,QAAQ,KAAK,IAAI,CAACvC,iBAAiB,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAACf,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACe,iBAAiB,GAAGuC,QAAQ;IACjC,IAAI,IAAI,CAACrO,WAAW,CAAC,CAAC,EAAE;MACpBqO,QAAQ,CAAC5R,MAAM,CAAC,IAAI,CAAC;MACrB,IAAI,CAACoD,cAAc,CAAC,CAAC;IACzB;EACJ;EACA;EACAyO,UAAUA,CAACC,UAAU,EAAE;IACnB,IAAI,CAAC7P,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAG6P;IAAW,CAAC;IACjD,IAAI,CAACzB,kBAAkB,CAAC,CAAC;EAC7B;EACA;EACA0B,YAAYA,CAACC,GAAG,EAAE;IACd,IAAI,CAAC/P,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE4E,SAAS,EAAEmL;IAAI,CAAC;IAClD,IAAI,CAAC1B,uBAAuB,CAAC,CAAC;EAClC;EACA;EACA2B,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC8B,cAAc,CAAC,IAAI,CAAC9B,KAAK,EAAEsD,OAAO,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC8B,cAAc,CAAC,IAAI,CAAC9B,KAAK,EAAEsD,OAAO,EAAE,KAAK,CAAC;IACnD;EACJ;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,MAAMvL,SAAS,GAAG,IAAI,CAAC5E,OAAO,CAAC4E,SAAS;IACxC,IAAI,CAACA,SAAS,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,OAAO,OAAOA,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGA,SAAS,CAAC0B,KAAK;EACtE;EACA;EACA8J,oBAAoBA,CAACT,QAAQ,EAAE;IAC3B,IAAIA,QAAQ,KAAK,IAAI,CAACtC,eAAe,EAAE;MACnC;IACJ;IACA,IAAI,CAAC6B,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC7B,eAAe,GAAGsC,QAAQ;IAC/B,IAAI,IAAI,CAACrO,WAAW,CAAC,CAAC,EAAE;MACpBqO,QAAQ,CAAC5R,MAAM,CAAC,IAAI,CAAC;MACrB4R,QAAQ,CAAC3R,MAAM,CAAC,CAAC;IACrB;EACJ;EACA;EACAqQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC3B,KAAK,CAACf,YAAY,CAAC,KAAK,EAAE,IAAI,CAACwE,YAAY,CAAC,CAAC,CAAC;EACvD;EACA;EACA/B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACzB,KAAK,EAAE;MACb;IACJ;IACA,MAAMtO,KAAK,GAAG,IAAI,CAACsO,KAAK,CAACtO,KAAK;IAC9BA,KAAK,CAACqB,KAAK,GAAG1D,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACN,KAAK,CAAC;IACrDrB,KAAK,CAACmB,MAAM,GAAGxD,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACR,MAAM,CAAC;IACvDnB,KAAK,CAACmG,QAAQ,GAAGxI,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACwE,QAAQ,CAAC;IAC3DnG,KAAK,CAACoG,SAAS,GAAGzI,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAACyE,SAAS,CAAC;IAC7DpG,KAAK,CAACqG,QAAQ,GAAG1I,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAAC0E,QAAQ,CAAC;IAC3DrG,KAAK,CAACsG,SAAS,GAAG3I,mBAAmB,CAAC,IAAI,CAACgE,OAAO,CAAC2E,SAAS,CAAC;EACjE;EACA;EACA4J,oBAAoBA,CAAC8B,aAAa,EAAE;IAChC,IAAI,CAAC1D,KAAK,CAACtO,KAAK,CAACkO,aAAa,GAAG8D,aAAa,GAAG,EAAE,GAAG,MAAM;EAChE;EACA;EACA7B,eAAeA,CAAA,EAAG;IACd,MAAM8B,YAAY,GAAG,8BAA8B;IACnD,IAAI,CAAC9C,YAAY,EAAEnB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACmB,YAAY,GAAG,IAAIzB,WAAW,CAAC,IAAI,CAACnO,SAAS,EAAE,IAAI,CAACuJ,SAAS,EAAE,IAAI,CAACpH,OAAO,EAAE0H,KAAK,IAAI;MACvF,IAAI,CAACwF,cAAc,CAACnF,IAAI,CAACL,KAAK,CAAC;IACnC,CAAC,CAAC;IACF,IAAI,IAAI,CAACsF,mBAAmB,EAAE;MAC1B,IAAI,CAACS,YAAY,CAAC9L,OAAO,CAACpD,SAAS,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClF;IACA,IAAI,IAAI,CAACyB,OAAO,CAACuE,aAAa,EAAE;MAC5B,IAAI,CAACkK,cAAc,CAAC,IAAI,CAACjB,YAAY,CAAC9L,OAAO,EAAE,IAAI,CAAC1B,OAAO,CAACuE,aAAa,EAAE,IAAI,CAAC;IACpF;IACA;IACA;IACA,IAAI,CAACmI,KAAK,CAACuB,aAAa,CAACsC,YAAY,CAAC,IAAI,CAAC/C,YAAY,CAAC9L,OAAO,EAAE,IAAI,CAACgL,KAAK,CAAC;IAC5E;IACA,IAAI,CAAC,IAAI,CAACK,mBAAmB,IAAI,OAAOyD,qBAAqB,KAAK,WAAW,EAAE;MAC3E,IAAI,CAACzQ,OAAO,CAACuH,iBAAiB,CAAC,MAAM;QACjCkJ,qBAAqB,CAAC,MAAM,IAAI,CAAChD,YAAY,EAAE9L,OAAO,CAACpD,SAAS,CAACC,GAAG,CAAC+R,YAAY,CAAC,CAAC;MACvF,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC9C,YAAY,CAAC9L,OAAO,CAACpD,SAAS,CAACC,GAAG,CAAC+R,YAAY,CAAC;IACzD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACInC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACzB,KAAK,CAAC+D,WAAW,EAAE;MACxB,IAAI,CAAC/D,KAAK,CAAC7C,UAAU,CAAC+B,WAAW,CAAC,IAAI,CAACc,KAAK,CAAC;IACjD;EACJ;EACA;EACAoC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC/B,mBAAmB,EAAE;MAC1B,IAAI,CAACS,YAAY,EAAEnB,OAAO,CAAC,CAAC;MAC5B,IAAI,CAACmB,YAAY,GAAG,IAAI;IAC5B,CAAC,MACI;MACD,IAAI,CAACA,YAAY,EAAEnM,MAAM,CAAC,CAAC;IAC/B;EACJ;EACA;EACAoN,cAAcA,CAAC/M,OAAO,EAAEgP,UAAU,EAAEC,KAAK,EAAE;IACvC,MAAMV,OAAO,GAAGhU,WAAW,CAACyU,UAAU,IAAI,EAAE,CAAC,CAAC9U,MAAM,CAACG,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC;IAC9D,IAAIkU,OAAO,CAACjJ,MAAM,EAAE;MAChB2J,KAAK,GAAGjP,OAAO,CAACpD,SAAS,CAACC,GAAG,CAAC,GAAG0R,OAAO,CAAC,GAAGvO,OAAO,CAACpD,SAAS,CAACU,MAAM,CAAC,GAAGiR,OAAO,CAAC;IACpF;EACJ;EACA;EACAjB,uBAAuBA,CAAA,EAAG;IACtB;IACA;IACA;IACA,IAAI,CAACjP,OAAO,CAACuH,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA,MAAMsJ,YAAY,GAAG,IAAI,CAAClD,QAAQ,CAC7BlN,IAAI,CAAC3E,SAAS,CAACF,KAAK,CAAC,IAAI,CAACuR,YAAY,EAAE,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAC5DrM,SAAS,CAAC,MAAM;QACjB;QACA;QACA,IAAI,CAAC,IAAI,CAAC6L,KAAK,IAAI,CAAC,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,CAACkE,QAAQ,CAAC7J,MAAM,KAAK,CAAC,EAAE;UAChE,IAAI,IAAI,CAAC2F,KAAK,IAAI,IAAI,CAAC3M,OAAO,CAACqE,UAAU,EAAE;YACvC,IAAI,CAACoK,cAAc,CAAC,IAAI,CAAC9B,KAAK,EAAE,IAAI,CAAC3M,OAAO,CAACqE,UAAU,EAAE,KAAK,CAAC;UACnE;UACA,IAAI,IAAI,CAACqI,KAAK,IAAI,IAAI,CAACA,KAAK,CAACuB,aAAa,EAAE;YACxC,IAAI,CAACR,mBAAmB,GAAG,IAAI,CAACf,KAAK,CAACuB,aAAa;YACnD,IAAI,CAACvB,KAAK,CAAC1N,MAAM,CAAC,CAAC;UACvB;UACA4R,YAAY,CAACxP,WAAW,CAAC,CAAC;QAC9B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA8N,sBAAsBA,CAAA,EAAG;IACrB,MAAM9K,cAAc,GAAG,IAAI,CAACiJ,eAAe;IAC3CjJ,cAAc,EAAE5F,OAAO,CAAC,CAAC;IACzB4F,cAAc,EAAE/C,MAAM,GAAG,CAAC;EAC9B;AACJ;;AAEA;AACA;AACA;AACA,MAAMyP,gBAAgB,GAAG,6CAA6C;AACtE;AACA,MAAMC,cAAc,GAAG,eAAe;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC,CAAC;EACpC1T,cAAc;EACdM,SAAS;EACTuK,SAAS;EACT8I,iBAAiB;EACjB;EACA/Q,WAAW;EACX;EACAgR,gBAAgB;EAChB;EACAC,oBAAoB,GAAG;IAAEzR,KAAK,EAAE,CAAC;IAAEF,MAAM,EAAE;EAAE,CAAC;EAC9C;EACA4R,SAAS,GAAG,KAAK;EACjB;EACAC,QAAQ,GAAG,IAAI;EACf;EACAC,cAAc,GAAG,KAAK;EACtB;EACAC,sBAAsB,GAAG,IAAI;EAC7B;EACAC,eAAe,GAAG,KAAK;EACvB;EACAC,WAAW;EACX;EACAC,YAAY;EACZ;EACAC,aAAa;EACb;EACAC,cAAc;EACd;EACAC,eAAe,GAAG,CAAC;EACnB;EACAC,YAAY,GAAG,EAAE;EACjB;EACAC,mBAAmB,GAAG,EAAE;EACxB;EACAC,OAAO;EACP;EACArF,KAAK;EACL;EACAsF,WAAW;EACX;AACJ;AACA;AACA;EACIC,YAAY;EACZ;EACAC,aAAa;EACb;EACAC,qBAAqB;EACrB;EACAC,gBAAgB,GAAG,IAAI5W,OAAO,CAAC,CAAC;EAChC;EACA6W,mBAAmB,GAAG5W,YAAY,CAAC6R,KAAK;EACxC;EACAgF,QAAQ,GAAG,CAAC;EACZ;EACAC,QAAQ,GAAG,CAAC;EACZ;EACAC,wBAAwB;EACxB;EACAC,oBAAoB,GAAG,EAAE;EACzB;EACAC,mBAAmB;EACnB;EACAC,eAAe,GAAG,IAAI,CAACP,gBAAgB;EACvC;EACA,IAAIQ,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,mBAAmB;EACnC;EACAlU,WAAWA,CAACiV,WAAW,EAAExV,cAAc,EAAEM,SAAS,EAAEuK,SAAS,EAAE8I,iBAAiB,EAAE;IAC9E,IAAI,CAAC3T,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACM,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACuK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC8I,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAAC8B,SAAS,CAACD,WAAW,CAAC;EAC/B;EACA;EACA/U,MAAMA,CAACqC,UAAU,EAAE;IACf,IAAI,IAAI,CAACF,WAAW,IAChBE,UAAU,KAAK,IAAI,CAACF,WAAW,KAC9B,OAAOG,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMT,KAAK,CAAC,0DAA0D,CAAC;IAC3E;IACA,IAAI,CAACoT,kBAAkB,CAAC,CAAC;IACzB5S,UAAU,CAAC2N,WAAW,CAACzP,SAAS,CAACC,GAAG,CAACuS,gBAAgB,CAAC;IACtD,IAAI,CAAC5Q,WAAW,GAAGE,UAAU;IAC7B,IAAI,CAAC8R,YAAY,GAAG9R,UAAU,CAAC2N,WAAW;IAC1C,IAAI,CAACpB,KAAK,GAAGvM,UAAU,CAACM,cAAc;IACtC,IAAI,CAACuR,WAAW,GAAG,KAAK;IACxB,IAAI,CAACf,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACiB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACG,mBAAmB,CAAClR,WAAW,CAAC,CAAC;IACtC,IAAI,CAACkR,mBAAmB,GAAG,IAAI,CAAChV,cAAc,CAAC2V,MAAM,CAAC,CAAC,CAACnS,SAAS,CAAC,MAAM;MACpE;MACA;MACA;MACA,IAAI,CAACoQ,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAACzB,KAAK,CAAC,CAAC;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,IAAI,CAACwC,WAAW,IAAI,CAAC,IAAI,CAAC9J,SAAS,CAACmD,SAAS,EAAE;MAC/C;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC4F,gBAAgB,IAAI,IAAI,CAACM,eAAe,IAAI,IAAI,CAACW,aAAa,EAAE;MACtE,IAAI,CAACe,mBAAmB,CAAC,CAAC;MAC1B;IACJ;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA;IACA,IAAI,CAAC1B,aAAa,GAAG,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;IACpD,IAAI,CAAC7B,WAAW,GAAG,IAAI,CAAC8B,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAAC/E,KAAK,CAAC5J,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAAC6O,cAAc,GAAG,IAAI,CAACX,iBAAiB,CAAC/F,mBAAmB,CAAC,CAAC,CAACnI,qBAAqB,CAAC,CAAC;IAC1F,MAAMyQ,UAAU,GAAG,IAAI,CAAC/B,WAAW;IACnC,MAAM3O,WAAW,GAAG,IAAI,CAAC4O,YAAY;IACrC,MAAM+B,YAAY,GAAG,IAAI,CAAC9B,aAAa;IACvC,MAAM+B,aAAa,GAAG,IAAI,CAAC9B,cAAc;IACzC;IACA,MAAM+B,YAAY,GAAG,EAAE;IACvB;IACA,IAAIC,QAAQ;IACZ;IACA;IACA,KAAK,IAAIC,GAAG,IAAI,IAAI,CAAC9B,mBAAmB,EAAE;MACtC;MACA,IAAI+B,WAAW,GAAG,IAAI,CAACC,eAAe,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,CAAC;MACtE;MACA;MACA;MACA,IAAIG,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAEhR,WAAW,EAAE+Q,GAAG,CAAC;MACvE;MACA,IAAIK,UAAU,GAAG,IAAI,CAACC,cAAc,CAACH,YAAY,EAAElR,WAAW,EAAE2Q,YAAY,EAAEI,GAAG,CAAC;MAClF;MACA,IAAIK,UAAU,CAACE,0BAA0B,EAAE;QACvC,IAAI,CAAChD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiD,cAAc,CAACR,GAAG,EAAEC,WAAW,CAAC;QACrC;MACJ;MACA;MACA;MACA,IAAI,IAAI,CAACQ,6BAA6B,CAACJ,UAAU,EAAEF,YAAY,EAAEP,YAAY,CAAC,EAAE;QAC5E;QACA;QACAE,YAAY,CAAC/M,IAAI,CAAC;UACd2N,QAAQ,EAAEV,GAAG;UACbnO,MAAM,EAAEoO,WAAW;UACnBhR,WAAW;UACX0R,eAAe,EAAE,IAAI,CAACC,yBAAyB,CAACX,WAAW,EAAED,GAAG;QACpE,CAAC,CAAC;QACF;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACD,QAAQ,IAAIA,QAAQ,CAACM,UAAU,CAACQ,WAAW,GAAGR,UAAU,CAACQ,WAAW,EAAE;QACvEd,QAAQ,GAAG;UAAEM,UAAU;UAAEF,YAAY;UAAEF,WAAW;UAAES,QAAQ,EAAEV,GAAG;UAAE/Q;QAAY,CAAC;MACpF;IACJ;IACA;IACA;IACA,IAAI6Q,YAAY,CAAC3M,MAAM,EAAE;MACrB,IAAI2N,OAAO,GAAG,IAAI;MAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;MAClB,KAAK,MAAMC,GAAG,IAAIlB,YAAY,EAAE;QAC5B,MAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAe,CAAC9U,KAAK,GAAGmV,GAAG,CAACL,eAAe,CAAChV,MAAM,IAAIqV,GAAG,CAACN,QAAQ,CAACQ,MAAM,IAAI,CAAC,CAAC;QACjG,IAAID,KAAK,GAAGF,SAAS,EAAE;UACnBA,SAAS,GAAGE,KAAK;UACjBH,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAI,CAACzD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACiD,cAAc,CAACM,OAAO,CAACJ,QAAQ,EAAEI,OAAO,CAACjP,MAAM,CAAC;MACrD;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC2L,QAAQ,EAAE;MACf;MACA,IAAI,CAACD,SAAS,GAAG,IAAI;MACrB,IAAI,CAACiD,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;MAC5D;IACJ;IACA;IACA;IACA,IAAI,CAACO,cAAc,CAACT,QAAQ,CAACW,QAAQ,EAAEX,QAAQ,CAACE,WAAW,CAAC;EAChE;EACAzS,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC8R,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAChB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACQ,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACL,mBAAmB,CAAClR,WAAW,CAAC,CAAC;EAC1C;EACA;EACAiL,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC4F,WAAW,EAAE;MAClB;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB8C,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC7T,KAAK,EAAE;QAClCb,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRyE,KAAK,EAAE,EAAE;QACTH,MAAM,EAAE,EAAE;QACVvC,MAAM,EAAE,EAAE;QACVE,KAAK,EAAE,EAAE;QACTuV,UAAU,EAAE,EAAE;QACdC,cAAc,EAAE;MACpB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACvI,KAAK,EAAE;MACZ,IAAI,CAACyG,0BAA0B,CAAC,CAAC;IACrC;IACA,IAAI,IAAI,CAAClT,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC6N,WAAW,CAACzP,SAAS,CAACU,MAAM,CAAC8R,gBAAgB,CAAC;IACnE;IACA,IAAI,CAACzP,MAAM,CAAC,CAAC;IACb,IAAI,CAACgR,gBAAgB,CAAClD,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACjP,WAAW,GAAG,IAAI,CAACgS,YAAY,GAAG,IAAI;IAC3C,IAAI,CAACD,WAAW,GAAG,IAAI;EAC3B;EACA;AACJ;AACA;AACA;AACA;EACIiB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAACjB,WAAW,IAAI,CAAC,IAAI,CAAC9J,SAAS,CAACmD,SAAS,EAAE;MAC/C;IACJ;IACA,MAAM6J,YAAY,GAAG,IAAI,CAAChD,aAAa;IACvC,IAAIgD,YAAY,EAAE;MACd,IAAI,CAAC1D,WAAW,GAAG,IAAI,CAAC8B,cAAc,CAAC,CAAC;MACxC,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAAC/E,KAAK,CAAC5J,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAAC4O,aAAa,GAAG,IAAI,CAAC2B,wBAAwB,CAAC,CAAC;MACpD,IAAI,CAAC1B,cAAc,GAAG,IAAI,CAACX,iBAAiB,CAAC/F,mBAAmB,CAAC,CAAC,CAACnI,qBAAqB,CAAC,CAAC;MAC1F,MAAM+Q,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACtC,WAAW,EAAE,IAAI,CAACG,cAAc,EAAEuD,YAAY,CAAC;MAC7F,IAAI,CAACd,cAAc,CAACc,YAAY,EAAErB,WAAW,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACrE,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2F,wBAAwBA,CAACC,WAAW,EAAE;IAClC,IAAI,CAACvD,YAAY,GAAGuD,WAAW;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACzC,SAAS,EAAE;IACrB,IAAI,CAACd,mBAAmB,GAAGc,SAAS;IACpC;IACA;IACA,IAAIA,SAAS,CAAC/L,OAAO,CAAC,IAAI,CAACqL,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACA,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACa,kBAAkB,CAAC,CAAC;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIuC,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC3D,eAAe,GAAG2D,MAAM;IAC7B,OAAO,IAAI;EACf;EACA;EACAC,sBAAsBA,CAACC,kBAAkB,GAAG,IAAI,EAAE;IAC9C,IAAI,CAACnE,sBAAsB,GAAGmE,kBAAkB;IAChD,OAAO,IAAI;EACf;EACA;EACAC,iBAAiBA,CAACC,aAAa,GAAG,IAAI,EAAE;IACpC,IAAI,CAACtE,cAAc,GAAGsE,aAAa;IACnC,OAAO,IAAI;EACf;EACA;EACAC,QAAQA,CAACC,OAAO,GAAG,IAAI,EAAE;IACrB,IAAI,CAACzE,QAAQ,GAAGyE,OAAO;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,kBAAkBA,CAACC,QAAQ,GAAG,IAAI,EAAE;IAChC,IAAI,CAACxE,eAAe,GAAGwE,QAAQ;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIjD,SAASA,CAACrN,MAAM,EAAE;IACd,IAAI,CAACsM,OAAO,GAAGtM,MAAM;IACrB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIuQ,kBAAkBA,CAACC,MAAM,EAAE;IACvB,IAAI,CAAC3D,QAAQ,GAAG2D,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAACD,MAAM,EAAE;IACvB,IAAI,CAAC1D,QAAQ,GAAG0D,MAAM;IACtB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,qBAAqBA,CAACC,QAAQ,EAAE;IAC5B,IAAI,CAAC5D,wBAAwB,GAAG4D,QAAQ;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACItC,eAAeA,CAACP,UAAU,EAAEE,aAAa,EAAEG,GAAG,EAAE;IAC5C,IAAIyC,CAAC;IACL,IAAIzC,GAAG,CAACvO,OAAO,IAAI,QAAQ,EAAE;MACzB;MACA;MACAgR,CAAC,GAAG9C,UAAU,CAAC/V,IAAI,GAAG+V,UAAU,CAAC9T,KAAK,GAAG,CAAC;IAC9C,CAAC,MACI;MACD,MAAM6W,MAAM,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGhD,UAAU,CAACtR,KAAK,GAAGsR,UAAU,CAAC/V,IAAI;MACjE,MAAMgZ,IAAI,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC,GAAGhD,UAAU,CAAC/V,IAAI,GAAG+V,UAAU,CAACtR,KAAK;MAC/DoU,CAAC,GAAGzC,GAAG,CAACvO,OAAO,IAAI,OAAO,GAAGiR,MAAM,GAAGE,IAAI;IAC9C;IACA;IACA;IACA,IAAI/C,aAAa,CAACjW,IAAI,GAAG,CAAC,EAAE;MACxB6Y,CAAC,IAAI5C,aAAa,CAACjW,IAAI;IAC3B;IACA,IAAIiZ,CAAC;IACL,IAAI7C,GAAG,CAACtO,OAAO,IAAI,QAAQ,EAAE;MACzBmR,CAAC,GAAGlD,UAAU,CAAChW,GAAG,GAAGgW,UAAU,CAAChU,MAAM,GAAG,CAAC;IAC9C,CAAC,MACI;MACDkX,CAAC,GAAG7C,GAAG,CAACtO,OAAO,IAAI,KAAK,GAAGiO,UAAU,CAAChW,GAAG,GAAGgW,UAAU,CAACzR,MAAM;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAI2R,aAAa,CAAClW,GAAG,GAAG,CAAC,EAAE;MACvBkZ,CAAC,IAAIhD,aAAa,CAAClW,GAAG;IAC1B;IACA,OAAO;MAAE8Y,CAAC;MAAEI;IAAE,CAAC;EACnB;EACA;AACJ;AACA;AACA;EACIzC,gBAAgBA,CAACH,WAAW,EAAEhR,WAAW,EAAE+Q,GAAG,EAAE;IAC5C;IACA;IACA,IAAI8C,aAAa;IACjB,IAAI9C,GAAG,CAACrO,QAAQ,IAAI,QAAQ,EAAE;MAC1BmR,aAAa,GAAG,CAAC7T,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1C,CAAC,MACI,IAAImU,GAAG,CAACrO,QAAQ,KAAK,OAAO,EAAE;MAC/BmR,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC1T,WAAW,CAACpD,KAAK,GAAG,CAAC;IAC1D,CAAC,MACI;MACDiX,aAAa,GAAG,IAAI,CAACH,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC1T,WAAW,CAACpD,KAAK;IAC1D;IACA,IAAIkX,aAAa;IACjB,IAAI/C,GAAG,CAACpO,QAAQ,IAAI,QAAQ,EAAE;MAC1BmR,aAAa,GAAG,CAAC9T,WAAW,CAACtD,MAAM,GAAG,CAAC;IAC3C,CAAC,MACI;MACDoX,aAAa,GAAG/C,GAAG,CAACpO,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC3C,WAAW,CAACtD,MAAM;IACnE;IACA;IACA,OAAO;MACH8W,CAAC,EAAExC,WAAW,CAACwC,CAAC,GAAGK,aAAa;MAChCD,CAAC,EAAE5C,WAAW,CAAC4C,CAAC,GAAGE;IACvB,CAAC;EACL;EACA;EACAzC,cAAcA,CAAC0C,KAAK,EAAEC,cAAc,EAAEzX,QAAQ,EAAEkV,QAAQ,EAAE;IACtD;IACA;IACA,MAAM5O,OAAO,GAAGoR,4BAA4B,CAACD,cAAc,CAAC;IAC5D,IAAI;MAAER,CAAC;MAAEI;IAAE,CAAC,GAAGG,KAAK;IACpB,IAAIzR,OAAO,GAAG,IAAI,CAAC4R,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIlP,OAAO,GAAG,IAAI,CAAC2R,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C;IACA,IAAInP,OAAO,EAAE;MACTkR,CAAC,IAAIlR,OAAO;IAChB;IACA,IAAIC,OAAO,EAAE;MACTqR,CAAC,IAAIrR,OAAO;IAChB;IACA;IACA,IAAI4R,YAAY,GAAG,CAAC,GAAGX,CAAC;IACxB,IAAIY,aAAa,GAAGZ,CAAC,GAAG3Q,OAAO,CAACjG,KAAK,GAAGL,QAAQ,CAACK,KAAK;IACtD,IAAIyX,WAAW,GAAG,CAAC,GAAGT,CAAC;IACvB,IAAIU,cAAc,GAAGV,CAAC,GAAG/Q,OAAO,CAACnG,MAAM,GAAGH,QAAQ,CAACG,MAAM;IACzD;IACA,IAAI6X,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAAC3R,OAAO,CAACjG,KAAK,EAAEuX,YAAY,EAAEC,aAAa,CAAC;IACtF,IAAIK,aAAa,GAAG,IAAI,CAACD,kBAAkB,CAAC3R,OAAO,CAACnG,MAAM,EAAE2X,WAAW,EAAEC,cAAc,CAAC;IACxF,IAAI1C,WAAW,GAAG2C,YAAY,GAAGE,aAAa;IAC9C,OAAO;MACH7C,WAAW;MACXN,0BAA0B,EAAEzO,OAAO,CAACjG,KAAK,GAAGiG,OAAO,CAACnG,MAAM,KAAKkV,WAAW;MAC1E8C,wBAAwB,EAAED,aAAa,KAAK5R,OAAO,CAACnG,MAAM;MAC1DiY,0BAA0B,EAAEJ,YAAY,IAAI1R,OAAO,CAACjG;IACxD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4U,6BAA6BA,CAACO,GAAG,EAAEgC,KAAK,EAAExX,QAAQ,EAAE;IAChD,IAAI,IAAI,CAACkS,sBAAsB,EAAE;MAC7B,MAAMmG,eAAe,GAAGrY,QAAQ,CAAC0C,MAAM,GAAG8U,KAAK,CAACH,CAAC;MACjD,MAAMiB,cAAc,GAAGtY,QAAQ,CAAC6C,KAAK,GAAG2U,KAAK,CAACP,CAAC;MAC/C,MAAM7R,SAAS,GAAGmT,aAAa,CAAC,IAAI,CAAC1X,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAC/K,SAAS,CAAC;MACvE,MAAMD,QAAQ,GAAGoT,aAAa,CAAC,IAAI,CAAC1X,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAChL,QAAQ,CAAC;MACrE,MAAMqT,WAAW,GAAGhD,GAAG,CAAC2C,wBAAwB,IAAK/S,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAIiT,eAAgB;MACvG,MAAMI,aAAa,GAAGjD,GAAG,CAAC4C,0BAA0B,IAAKjT,QAAQ,IAAI,IAAI,IAAIA,QAAQ,IAAImT,cAAe;MACxG,OAAOE,WAAW,IAAIC,aAAa;IACvC;IACA,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,KAAK,EAAElB,cAAc,EAAE/V,cAAc,EAAE;IACxD;IACA;IACA;IACA,IAAI,IAAI,CAAC4R,mBAAmB,IAAI,IAAI,CAACnB,eAAe,EAAE;MAClD,OAAO;QACH8E,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAAC3D,mBAAmB,CAAC2D,CAAC;QACvCI,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC/D,mBAAmB,CAAC+D;MAC1C,CAAC;IACL;IACA;IACA;IACA,MAAM/Q,OAAO,GAAGoR,4BAA4B,CAACD,cAAc,CAAC;IAC5D,MAAMzX,QAAQ,GAAG,IAAI,CAACsS,aAAa;IACnC;IACA;IACA,MAAMsG,aAAa,GAAGjX,IAAI,CAACkX,GAAG,CAACF,KAAK,CAAC1B,CAAC,GAAG3Q,OAAO,CAACjG,KAAK,GAAGL,QAAQ,CAACK,KAAK,EAAE,CAAC,CAAC;IAC3E,MAAMyY,cAAc,GAAGnX,IAAI,CAACkX,GAAG,CAACF,KAAK,CAACtB,CAAC,GAAG/Q,OAAO,CAACnG,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE,CAAC,CAAC;IAC9E,MAAM4Y,WAAW,GAAGpX,IAAI,CAACkX,GAAG,CAAC7Y,QAAQ,CAAC7B,GAAG,GAAGuD,cAAc,CAACvD,GAAG,GAAGwa,KAAK,CAACtB,CAAC,EAAE,CAAC,CAAC;IAC5E,MAAM2B,YAAY,GAAGrX,IAAI,CAACkX,GAAG,CAAC7Y,QAAQ,CAAC5B,IAAI,GAAGsD,cAAc,CAACtD,IAAI,GAAGua,KAAK,CAAC1B,CAAC,EAAE,CAAC,CAAC;IAC/E;IACA,IAAIgC,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb;IACA;IACA;IACA,IAAI5S,OAAO,CAACjG,KAAK,IAAIL,QAAQ,CAACK,KAAK,EAAE;MACjC4Y,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAa;IAC1C,CAAC,MACI;MACDK,KAAK,GAAGN,KAAK,CAAC1B,CAAC,GAAG,IAAI,CAACzE,eAAe,GAAGxS,QAAQ,CAAC5B,IAAI,GAAGsD,cAAc,CAACtD,IAAI,GAAGua,KAAK,CAAC1B,CAAC,GAAG,CAAC;IAC9F;IACA,IAAI3Q,OAAO,CAACnG,MAAM,IAAIH,QAAQ,CAACG,MAAM,EAAE;MACnC+Y,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAc;IAC1C,CAAC,MACI;MACDI,KAAK,GAAGP,KAAK,CAACtB,CAAC,GAAG,IAAI,CAAC7E,eAAe,GAAGxS,QAAQ,CAAC7B,GAAG,GAAGuD,cAAc,CAACvD,GAAG,GAAGwa,KAAK,CAACtB,CAAC,GAAG,CAAC;IAC5F;IACA,IAAI,CAAC/D,mBAAmB,GAAG;MAAE2D,CAAC,EAAEgC,KAAK;MAAE5B,CAAC,EAAE6B;IAAM,CAAC;IACjD,OAAO;MACHjC,CAAC,EAAE0B,KAAK,CAAC1B,CAAC,GAAGgC,KAAK;MAClB5B,CAAC,EAAEsB,KAAK,CAACtB,CAAC,GAAG6B;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACIlE,cAAcA,CAACE,QAAQ,EAAET,WAAW,EAAE;IAClC,IAAI,CAAC0E,mBAAmB,CAACjE,QAAQ,CAAC;IAClC,IAAI,CAACkE,wBAAwB,CAAC3E,WAAW,EAAES,QAAQ,CAAC;IACpD,IAAI,CAACmE,qBAAqB,CAAC5E,WAAW,EAAES,QAAQ,CAAC;IACjD,IAAIA,QAAQ,CAAClQ,UAAU,EAAE;MACrB,IAAI,CAACsU,gBAAgB,CAACpE,QAAQ,CAAClQ,UAAU,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACgO,gBAAgB,CAACxK,SAAS,CAACb,MAAM,EAAE;MACxC,MAAM4R,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;MACpD;MACA;MACA,IAAItE,QAAQ,KAAK,IAAI,CAACpC,aAAa,IAC/B,CAAC,IAAI,CAACC,qBAAqB,IAC3B,CAAC0G,uBAAuB,CAAC,IAAI,CAAC1G,qBAAqB,EAAEwG,gBAAgB,CAAC,EAAE;QACxE,MAAMG,WAAW,GAAG,IAAI9S,8BAA8B,CAACsO,QAAQ,EAAEqE,gBAAgB,CAAC;QAClF,IAAI,CAACvG,gBAAgB,CAACvK,IAAI,CAACiR,WAAW,CAAC;MAC3C;MACA,IAAI,CAAC3G,qBAAqB,GAAGwG,gBAAgB;IACjD;IACA;IACA,IAAI,CAACzG,aAAa,GAAGoC,QAAQ;IAC7B,IAAI,CAACrD,gBAAgB,GAAG,KAAK;EACjC;EACA;EACAsH,mBAAmBA,CAACjE,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAAC9B,wBAAwB,EAAE;MAChC;IACJ;IACA,MAAMuG,QAAQ,GAAG,IAAI,CAAC9G,YAAY,CAAC1G,gBAAgB,CAAC,IAAI,CAACiH,wBAAwB,CAAC;IAClF,IAAIwG,OAAO;IACX,IAAIC,OAAO,GAAG3E,QAAQ,CAAC9O,QAAQ;IAC/B,IAAI8O,QAAQ,CAAC/O,QAAQ,KAAK,QAAQ,EAAE;MAChCyT,OAAO,GAAG,QAAQ;IACtB,CAAC,MACI,IAAI,IAAI,CAACzC,MAAM,CAAC,CAAC,EAAE;MACpByC,OAAO,GAAG1E,QAAQ,CAAC/O,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;IAC9D,CAAC,MACI;MACDyT,OAAO,GAAG1E,QAAQ,CAAC/O,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9D;IACA,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqR,QAAQ,CAAChS,MAAM,EAAEW,CAAC,EAAE,EAAE;MACtCqR,QAAQ,CAACrR,CAAC,CAAC,CAACtJ,KAAK,CAAC8a,eAAe,GAAG,GAAGF,OAAO,IAAIC,OAAO,EAAE;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzE,yBAAyBA,CAAC/O,MAAM,EAAE6O,QAAQ,EAAE;IACxC,MAAMlV,QAAQ,GAAG,IAAI,CAACsS,aAAa;IACnC,MAAMyH,KAAK,GAAG,IAAI,CAAC5C,MAAM,CAAC,CAAC;IAC3B,IAAIhX,MAAM,EAAEhC,GAAG,EAAEuE,MAAM;IACvB,IAAIwS,QAAQ,CAAC9O,QAAQ,KAAK,KAAK,EAAE;MAC7B;MACAjI,GAAG,GAAGkI,MAAM,CAACgR,CAAC;MACdlX,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGhC,GAAG,GAAG,IAAI,CAACqU,eAAe;IACzD,CAAC,MACI,IAAI0C,QAAQ,CAAC9O,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA;MACA;MACA1D,MAAM,GAAG1C,QAAQ,CAACG,MAAM,GAAGkG,MAAM,CAACgR,CAAC,GAAG,IAAI,CAAC7E,eAAe,GAAG,CAAC;MAC9DrS,MAAM,GAAGH,QAAQ,CAACG,MAAM,GAAGuC,MAAM,GAAG,IAAI,CAAC8P,eAAe;IAC5D,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAMwH,8BAA8B,GAAGrY,IAAI,CAACsY,GAAG,CAACja,QAAQ,CAAC0C,MAAM,GAAG2D,MAAM,CAACgR,CAAC,GAAGrX,QAAQ,CAAC7B,GAAG,EAAEkI,MAAM,CAACgR,CAAC,CAAC;MACpG,MAAM6C,cAAc,GAAG,IAAI,CAACpI,oBAAoB,CAAC3R,MAAM;MACvDA,MAAM,GAAG6Z,8BAA8B,GAAG,CAAC;MAC3C7b,GAAG,GAAGkI,MAAM,CAACgR,CAAC,GAAG2C,8BAA8B;MAC/C,IAAI7Z,MAAM,GAAG+Z,cAAc,IAAI,CAAC,IAAI,CAACrI,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;QAC3E9T,GAAG,GAAGkI,MAAM,CAACgR,CAAC,GAAG6C,cAAc,GAAG,CAAC;MACvC;IACJ;IACA;IACA,MAAMC,4BAA4B,GAAIjF,QAAQ,CAAC/O,QAAQ,KAAK,OAAO,IAAI,CAAC4T,KAAK,IAAM7E,QAAQ,CAAC/O,QAAQ,KAAK,KAAK,IAAI4T,KAAM;IACxH;IACA,MAAMK,2BAA2B,GAAIlF,QAAQ,CAAC/O,QAAQ,KAAK,KAAK,IAAI,CAAC4T,KAAK,IAAM7E,QAAQ,CAAC/O,QAAQ,KAAK,OAAO,IAAI4T,KAAM;IACvH,IAAI1Z,KAAK,EAAEjC,IAAI,EAAEyE,KAAK;IACtB,IAAIuX,2BAA2B,EAAE;MAC7BvX,KAAK,GAAG7C,QAAQ,CAACK,KAAK,GAAGgG,MAAM,CAAC4Q,CAAC,GAAG,IAAI,CAACzE,eAAe,GAAG,CAAC;MAC5DnS,KAAK,GAAGgG,MAAM,CAAC4Q,CAAC,GAAG,IAAI,CAACzE,eAAe;IAC3C,CAAC,MACI,IAAI2H,4BAA4B,EAAE;MACnC/b,IAAI,GAAGiI,MAAM,CAAC4Q,CAAC;MACf5W,KAAK,GAAGL,QAAQ,CAAC6C,KAAK,GAAGwD,MAAM,CAAC4Q,CAAC;IACrC,CAAC,MACI;MACD;MACA;MACA;MACA;MACA,MAAM+C,8BAA8B,GAAGrY,IAAI,CAACsY,GAAG,CAACja,QAAQ,CAAC6C,KAAK,GAAGwD,MAAM,CAAC4Q,CAAC,GAAGjX,QAAQ,CAAC5B,IAAI,EAAEiI,MAAM,CAAC4Q,CAAC,CAAC;MACpG,MAAMoD,aAAa,GAAG,IAAI,CAACvI,oBAAoB,CAACzR,KAAK;MACrDA,KAAK,GAAG2Z,8BAA8B,GAAG,CAAC;MAC1C5b,IAAI,GAAGiI,MAAM,CAAC4Q,CAAC,GAAG+C,8BAA8B;MAChD,IAAI3Z,KAAK,GAAGga,aAAa,IAAI,CAAC,IAAI,CAACxI,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;QACzE7T,IAAI,GAAGiI,MAAM,CAAC4Q,CAAC,GAAGoD,aAAa,GAAG,CAAC;MACvC;IACJ;IACA,OAAO;MAAElc,GAAG,EAAEA,GAAG;MAAEC,IAAI,EAAEA,IAAI;MAAEsE,MAAM,EAAEA,MAAM;MAAEG,KAAK,EAAEA,KAAK;MAAExC,KAAK;MAAEF;IAAO,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIkZ,qBAAqBA,CAAChT,MAAM,EAAE6O,QAAQ,EAAE;IACpC,MAAMC,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAAC/O,MAAM,EAAE6O,QAAQ,CAAC;IACxE;IACA;IACA,IAAI,CAAC,IAAI,CAACrD,gBAAgB,IAAI,CAAC,IAAI,CAACI,cAAc,EAAE;MAChDkD,eAAe,CAAChV,MAAM,GAAGwB,IAAI,CAACsY,GAAG,CAAC9E,eAAe,CAAChV,MAAM,EAAE,IAAI,CAAC2R,oBAAoB,CAAC3R,MAAM,CAAC;MAC3FgV,eAAe,CAAC9U,KAAK,GAAGsB,IAAI,CAACsY,GAAG,CAAC9E,eAAe,CAAC9U,KAAK,EAAE,IAAI,CAACyR,oBAAoB,CAACzR,KAAK,CAAC;IAC5F;IACA,MAAMgL,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,IAAI,CAACiP,iBAAiB,CAAC,CAAC,EAAE;MAC1BjP,MAAM,CAAClN,GAAG,GAAGkN,MAAM,CAACjN,IAAI,GAAG,GAAG;MAC9BiN,MAAM,CAAC3I,MAAM,GAAG2I,MAAM,CAACxI,KAAK,GAAGwI,MAAM,CAAC/F,SAAS,GAAG+F,MAAM,CAAChG,QAAQ,GAAG,EAAE;MACtEgG,MAAM,CAAChL,KAAK,GAAGgL,MAAM,CAAClL,MAAM,GAAG,MAAM;IACzC,CAAC,MACI;MACD,MAAMmF,SAAS,GAAG,IAAI,CAACzE,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAC7K,SAAS;MACxD,MAAMD,QAAQ,GAAG,IAAI,CAACxE,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAC9K,QAAQ;MACtDgG,MAAM,CAAClL,MAAM,GAAGxD,mBAAmB,CAACwY,eAAe,CAAChV,MAAM,CAAC;MAC3DkL,MAAM,CAAClN,GAAG,GAAGxB,mBAAmB,CAACwY,eAAe,CAAChX,GAAG,CAAC;MACrDkN,MAAM,CAAC3I,MAAM,GAAG/F,mBAAmB,CAACwY,eAAe,CAACzS,MAAM,CAAC;MAC3D2I,MAAM,CAAChL,KAAK,GAAG1D,mBAAmB,CAACwY,eAAe,CAAC9U,KAAK,CAAC;MACzDgL,MAAM,CAACjN,IAAI,GAAGzB,mBAAmB,CAACwY,eAAe,CAAC/W,IAAI,CAAC;MACvDiN,MAAM,CAACxI,KAAK,GAAGlG,mBAAmB,CAACwY,eAAe,CAACtS,KAAK,CAAC;MACzD;MACA,IAAIqS,QAAQ,CAAC/O,QAAQ,KAAK,QAAQ,EAAE;QAChCkF,MAAM,CAACuK,UAAU,GAAG,QAAQ;MAChC,CAAC,MACI;QACDvK,MAAM,CAACuK,UAAU,GAAGV,QAAQ,CAAC/O,QAAQ,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY;MAC/E;MACA,IAAI+O,QAAQ,CAAC9O,QAAQ,KAAK,QAAQ,EAAE;QAChCiF,MAAM,CAACwK,cAAc,GAAG,QAAQ;MACpC,CAAC,MACI;QACDxK,MAAM,CAACwK,cAAc,GAAGX,QAAQ,CAAC9O,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG,YAAY;MACtF;MACA,IAAId,SAAS,EAAE;QACX+F,MAAM,CAAC/F,SAAS,GAAG3I,mBAAmB,CAAC2I,SAAS,CAAC;MACrD;MACA,IAAID,QAAQ,EAAE;QACVgG,MAAM,CAAChG,QAAQ,GAAG1I,mBAAmB,CAAC0I,QAAQ,CAAC;MACnD;IACJ;IACA,IAAI,CAACyM,oBAAoB,GAAGqD,eAAe;IAC3CQ,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC7T,KAAK,EAAEqM,MAAM,CAAC;EACjD;EACA;EACA2I,uBAAuBA,CAAA,EAAG;IACtB2B,YAAY,CAAC,IAAI,CAAC9C,YAAY,CAAC7T,KAAK,EAAE;MAClCb,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,GAAG;MACTyE,KAAK,EAAE,GAAG;MACVH,MAAM,EAAE,GAAG;MACXvC,MAAM,EAAE,EAAE;MACVE,KAAK,EAAE,EAAE;MACTuV,UAAU,EAAE,EAAE;MACdC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN;EACA;EACA9B,0BAA0BA,CAAA,EAAG;IACzB4B,YAAY,CAAC,IAAI,CAACrI,KAAK,CAACtO,KAAK,EAAE;MAC3Bb,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE,EAAE;MACRsE,MAAM,EAAE,EAAE;MACVG,KAAK,EAAE,EAAE;MACTqS,QAAQ,EAAE,EAAE;MACZqF,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EACA;EACAnB,wBAAwBA,CAAC3E,WAAW,EAAES,QAAQ,EAAE;IAC5C,MAAM7J,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMmP,gBAAgB,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC;IACjD,MAAMG,qBAAqB,GAAG,IAAI,CAACvI,sBAAsB;IACzD,MAAMnO,MAAM,GAAG,IAAI,CAAClD,WAAW,CAACsP,SAAS,CAAC,CAAC;IAC3C,IAAIqK,gBAAgB,EAAE;MAClB,MAAM9Y,cAAc,GAAG,IAAI,CAACzD,cAAc,CAACc,yBAAyB,CAAC,CAAC;MACtE4W,YAAY,CAACtK,MAAM,EAAE,IAAI,CAACqP,iBAAiB,CAACxF,QAAQ,EAAET,WAAW,EAAE/S,cAAc,CAAC,CAAC;MACnFiU,YAAY,CAACtK,MAAM,EAAE,IAAI,CAACsP,iBAAiB,CAACzF,QAAQ,EAAET,WAAW,EAAE/S,cAAc,CAAC,CAAC;IACvF,CAAC,MACI;MACD2J,MAAM,CAAC6J,QAAQ,GAAG,QAAQ;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA,IAAI0F,eAAe,GAAG,EAAE;IACxB,IAAI7U,OAAO,GAAG,IAAI,CAAC4R,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAIlP,OAAO,GAAG,IAAI,CAAC2R,UAAU,CAACzC,QAAQ,EAAE,GAAG,CAAC;IAC5C,IAAInP,OAAO,EAAE;MACT6U,eAAe,IAAI,cAAc7U,OAAO,MAAM;IAClD;IACA,IAAIC,OAAO,EAAE;MACT4U,eAAe,IAAI,cAAc5U,OAAO,KAAK;IACjD;IACAqF,MAAM,CAACkP,SAAS,GAAGK,eAAe,CAACC,IAAI,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA,IAAI9W,MAAM,CAACuB,SAAS,EAAE;MAClB,IAAIkV,gBAAgB,EAAE;QAClBnP,MAAM,CAAC/F,SAAS,GAAG3I,mBAAmB,CAACoH,MAAM,CAACuB,SAAS,CAAC;MAC5D,CAAC,MACI,IAAImV,qBAAqB,EAAE;QAC5BpP,MAAM,CAAC/F,SAAS,GAAG,EAAE;MACzB;IACJ;IACA,IAAIvB,MAAM,CAACsB,QAAQ,EAAE;MACjB,IAAImV,gBAAgB,EAAE;QAClBnP,MAAM,CAAChG,QAAQ,GAAG1I,mBAAmB,CAACoH,MAAM,CAACsB,QAAQ,CAAC;MAC1D,CAAC,MACI,IAAIoV,qBAAqB,EAAE;QAC5BpP,MAAM,CAAChG,QAAQ,GAAG,EAAE;MACxB;IACJ;IACAsQ,YAAY,CAAC,IAAI,CAACrI,KAAK,CAACtO,KAAK,EAAEqM,MAAM,CAAC;EAC1C;EACA;EACAqP,iBAAiBA,CAACxF,QAAQ,EAAET,WAAW,EAAE/S,cAAc,EAAE;IACrD;IACA;IACA,IAAI2J,MAAM,GAAG;MAAElN,GAAG,EAAE,EAAE;MAAEuE,MAAM,EAAE;IAAG,CAAC;IACpC,IAAIiS,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACpC,YAAY,EAAE6C,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACnD,SAAS,EAAE;MAChB4C,YAAY,GAAG,IAAI,CAAC+D,oBAAoB,CAAC/D,YAAY,EAAE,IAAI,CAACtC,YAAY,EAAE3Q,cAAc,CAAC;IAC7F;IACA;IACA;IACA,IAAIwT,QAAQ,CAAC9O,QAAQ,KAAK,QAAQ,EAAE;MAChC;MACA;MACA,MAAM0U,cAAc,GAAG,IAAI,CAACvc,SAAS,CAACO,eAAe,CAACic,YAAY;MAClE1P,MAAM,CAAC3I,MAAM,GAAG,GAAGoY,cAAc,IAAInG,YAAY,CAAC0C,CAAC,GAAG,IAAI,CAAChF,YAAY,CAAClS,MAAM,CAAC,IAAI;IACvF,CAAC,MACI;MACDkL,MAAM,CAAClN,GAAG,GAAGxB,mBAAmB,CAACgY,YAAY,CAAC0C,CAAC,CAAC;IACpD;IACA,OAAOhM,MAAM;EACjB;EACA;EACAsP,iBAAiBA,CAACzF,QAAQ,EAAET,WAAW,EAAE/S,cAAc,EAAE;IACrD;IACA;IACA,IAAI2J,MAAM,GAAG;MAAEjN,IAAI,EAAE,EAAE;MAAEyE,KAAK,EAAE;IAAG,CAAC;IACpC,IAAI8R,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACH,WAAW,EAAE,IAAI,CAACpC,YAAY,EAAE6C,QAAQ,CAAC;IAClF,IAAI,IAAI,CAACnD,SAAS,EAAE;MAChB4C,YAAY,GAAG,IAAI,CAAC+D,oBAAoB,CAAC/D,YAAY,EAAE,IAAI,CAACtC,YAAY,EAAE3Q,cAAc,CAAC;IAC7F;IACA;IACA;IACA;IACA;IACA,IAAIsZ,uBAAuB;IAC3B,IAAI,IAAI,CAAC7D,MAAM,CAAC,CAAC,EAAE;MACf6D,uBAAuB,GAAG9F,QAAQ,CAAC/O,QAAQ,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;IAC5E,CAAC,MACI;MACD6U,uBAAuB,GAAG9F,QAAQ,CAAC/O,QAAQ,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IAC5E;IACA;IACA;IACA,IAAI6U,uBAAuB,KAAK,OAAO,EAAE;MACrC,MAAMC,aAAa,GAAG,IAAI,CAAC1c,SAAS,CAACO,eAAe,CAACoc,WAAW;MAChE7P,MAAM,CAACxI,KAAK,GAAG,GAAGoY,aAAa,IAAItG,YAAY,CAACsC,CAAC,GAAG,IAAI,CAAC5E,YAAY,CAAChS,KAAK,CAAC,IAAI;IACpF,CAAC,MACI;MACDgL,MAAM,CAACjN,IAAI,GAAGzB,mBAAmB,CAACgY,YAAY,CAACsC,CAAC,CAAC;IACrD;IACA,OAAO5L,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACImO,oBAAoBA,CAAA,EAAG;IACnB;IACA,MAAM2B,YAAY,GAAG,IAAI,CAACjH,cAAc,CAAC,CAAC;IAC1C,MAAMkH,aAAa,GAAG,IAAI,CAAC9N,KAAK,CAAC5J,qBAAqB,CAAC,CAAC;IACxD;IACA;IACA;IACA,MAAM2X,qBAAqB,GAAG,IAAI,CAAC5I,YAAY,CAAC6I,GAAG,CAACla,UAAU,IAAI;MAC9D,OAAOA,UAAU,CAACE,aAAa,CAAC,CAAC,CAACC,aAAa,CAACmC,qBAAqB,CAAC,CAAC;IAC3E,CAAC,CAAC;IACF,OAAO;MACH8C,eAAe,EAAEzD,2BAA2B,CAACoY,YAAY,EAAEE,qBAAqB,CAAC;MACjF5U,mBAAmB,EAAErE,4BAA4B,CAAC+Y,YAAY,EAAEE,qBAAqB,CAAC;MACtF3U,gBAAgB,EAAE3D,2BAA2B,CAACqY,aAAa,EAAEC,qBAAqB,CAAC;MACnF1U,oBAAoB,EAAEvE,4BAA4B,CAACgZ,aAAa,EAAEC,qBAAqB;IAC3F,CAAC;EACL;EACA;EACApD,kBAAkBA,CAACtQ,MAAM,EAAE,GAAG4T,SAAS,EAAE;IACrC,OAAOA,SAAS,CAACC,MAAM,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACvD,OAAOD,YAAY,GAAG9Z,IAAI,CAACkX,GAAG,CAAC6C,eAAe,EAAE,CAAC,CAAC;IACtD,CAAC,EAAE/T,MAAM,CAAC;EACd;EACA;EACAsM,wBAAwBA,CAAA,EAAG;IACvB;IACA;IACA;IACA;IACA;IACA,MAAM5T,KAAK,GAAG,IAAI,CAAC9B,SAAS,CAACO,eAAe,CAACoc,WAAW;IACxD,MAAM/a,MAAM,GAAG,IAAI,CAAC5B,SAAS,CAACO,eAAe,CAACic,YAAY;IAC1D,MAAMrZ,cAAc,GAAG,IAAI,CAACzD,cAAc,CAACc,yBAAyB,CAAC,CAAC;IACtE,OAAO;MACHZ,GAAG,EAAEuD,cAAc,CAACvD,GAAG,GAAG,IAAI,CAACqU,eAAe;MAC9CpU,IAAI,EAAEsD,cAAc,CAACtD,IAAI,GAAG,IAAI,CAACoU,eAAe;MAChD3P,KAAK,EAAEnB,cAAc,CAACtD,IAAI,GAAGiC,KAAK,GAAG,IAAI,CAACmS,eAAe;MACzD9P,MAAM,EAAEhB,cAAc,CAACvD,GAAG,GAAGgC,MAAM,GAAG,IAAI,CAACqS,eAAe;MAC1DnS,KAAK,EAAEA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACmS,eAAe;MACvCrS,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqS;IAC9B,CAAC;EACL;EACA;EACA2E,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtW,WAAW,CAACiQ,YAAY,CAAC,CAAC,KAAK,KAAK;EACpD;EACA;EACAwJ,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAACpI,sBAAsB,IAAI,IAAI,CAACH,SAAS;EACzD;EACA;EACA4F,UAAUA,CAACzC,QAAQ,EAAEyG,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAK,GAAG,EAAE;MACd;MACA;MACA,OAAOzG,QAAQ,CAACnP,OAAO,IAAI,IAAI,GAAG,IAAI,CAACmN,QAAQ,GAAGgC,QAAQ,CAACnP,OAAO;IACtE;IACA,OAAOmP,QAAQ,CAAClP,OAAO,IAAI,IAAI,GAAG,IAAI,CAACmN,QAAQ,GAAG+B,QAAQ,CAAClP,OAAO;EACtE;EACA;EACA2N,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAO3S,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAI,CAAC,IAAI,CAAC0R,mBAAmB,CAAC/K,MAAM,EAAE;QAClC,MAAMpH,KAAK,CAAC,uEAAuE,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAACmS,mBAAmB,CAACjJ,OAAO,CAACmS,IAAI,IAAI;QACrC1U,0BAA0B,CAAC,SAAS,EAAE0U,IAAI,CAAC3V,OAAO,CAAC;QACnDc,wBAAwB,CAAC,SAAS,EAAE6U,IAAI,CAAC1V,OAAO,CAAC;QACjDgB,0BAA0B,CAAC,UAAU,EAAE0U,IAAI,CAACzV,QAAQ,CAAC;QACrDY,wBAAwB,CAAC,UAAU,EAAE6U,IAAI,CAACxV,QAAQ,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA;EACAkT,gBAAgBA,CAACjI,UAAU,EAAE;IACzB,IAAI,IAAI,CAAC/D,KAAK,EAAE;MACZ1Q,WAAW,CAACyU,UAAU,CAAC,CAAC5H,OAAO,CAACoS,QAAQ,IAAI;QACxC,IAAIA,QAAQ,KAAK,EAAE,IAAI,IAAI,CAACxI,oBAAoB,CAAC5L,OAAO,CAACoU,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UACvE,IAAI,CAACxI,oBAAoB,CAAC9L,IAAI,CAACsU,QAAQ,CAAC;UACxC,IAAI,CAACvO,KAAK,CAACrO,SAAS,CAACC,GAAG,CAAC2c,QAAQ,CAAC;QACtC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACA/H,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACxG,KAAK,EAAE;MACZ,IAAI,CAAC+F,oBAAoB,CAAC5J,OAAO,CAACoS,QAAQ,IAAI;QAC1C,IAAI,CAACvO,KAAK,CAACrO,SAAS,CAACU,MAAM,CAACkc,QAAQ,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACxI,oBAAoB,GAAG,EAAE;IAClC;EACJ;EACA;EACAa,cAAcA,CAAA,EAAG;IACb,MAAM7N,MAAM,GAAG,IAAI,CAACsM,OAAO;IAC3B,IAAItM,MAAM,YAAYxL,UAAU,EAAE;MAC9B,OAAOwL,MAAM,CAAC9E,aAAa,CAACmC,qBAAqB,CAAC,CAAC;IACvD;IACA;IACA,IAAI2C,MAAM,YAAYyV,OAAO,EAAE;MAC3B,OAAOzV,MAAM,CAAC3C,qBAAqB,CAAC,CAAC;IACzC;IACA,MAAMrD,KAAK,GAAGgG,MAAM,CAAChG,KAAK,IAAI,CAAC;IAC/B,MAAMF,MAAM,GAAGkG,MAAM,CAAClG,MAAM,IAAI,CAAC;IACjC;IACA,OAAO;MACHhC,GAAG,EAAEkI,MAAM,CAACgR,CAAC;MACb3U,MAAM,EAAE2D,MAAM,CAACgR,CAAC,GAAGlX,MAAM;MACzB/B,IAAI,EAAEiI,MAAM,CAAC4Q,CAAC;MACdpU,KAAK,EAAEwD,MAAM,CAAC4Q,CAAC,GAAG5W,KAAK;MACvBF,MAAM;MACNE;IACJ,CAAC;EACL;AACJ;AACA;AACA,SAASsV,YAAYA,CAACoG,WAAW,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIpW,GAAG,IAAIoW,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACC,cAAc,CAACrW,GAAG,CAAC,EAAE;MAC5BmW,WAAW,CAACnW,GAAG,CAAC,GAAGoW,MAAM,CAACpW,GAAG,CAAC;IAClC;EACJ;EACA,OAAOmW,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA,SAASxD,aAAaA,CAAC2D,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,EAAE;IAC5C,MAAM,CAACjV,KAAK,EAAEkV,KAAK,CAAC,GAAGD,KAAK,CAACE,KAAK,CAAC1K,cAAc,CAAC;IAClD,OAAO,CAACyK,KAAK,IAAIA,KAAK,KAAK,IAAI,GAAGE,UAAU,CAACpV,KAAK,CAAC,GAAG,IAAI;EAC9D;EACA,OAAOiV,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxE,4BAA4BA,CAAC4E,UAAU,EAAE;EAC9C,OAAO;IACHne,GAAG,EAAEwD,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAACne,GAAG,CAAC;IAC/B0E,KAAK,EAAElB,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAACzZ,KAAK,CAAC;IACnCH,MAAM,EAAEf,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAAC5Z,MAAM,CAAC;IACrCtE,IAAI,EAAEuD,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAACle,IAAI,CAAC;IACjCiC,KAAK,EAAEsB,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAACjc,KAAK,CAAC;IACnCF,MAAM,EAAEwB,IAAI,CAAC4a,KAAK,CAACD,UAAU,CAACnc,MAAM;EACxC,CAAC;AACL;AACA;AACA,SAASsZ,uBAAuBA,CAAC+C,CAAC,EAAEtf,CAAC,EAAE;EACnC,IAAIsf,CAAC,KAAKtf,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,OAAQsf,CAAC,CAAChW,eAAe,KAAKtJ,CAAC,CAACsJ,eAAe,IAC3CgW,CAAC,CAAC/V,mBAAmB,KAAKvJ,CAAC,CAACuJ,mBAAmB,IAC/C+V,CAAC,CAAC9V,gBAAgB,KAAKxJ,CAAC,CAACwJ,gBAAgB,IACzC8V,CAAC,CAAC7V,oBAAoB,KAAKzJ,CAAC,CAACyJ,oBAAoB;AACzD;AACA,MAAM8V,iCAAiC,GAAG,CACtC;EAAExW,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC3E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC3E;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACvE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC1E;AACD,MAAMsW,oCAAoC,GAAG,CACzC;EAAEzW,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,EAC5E;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,EACtE;EAAEH,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAC/E;;AAED;AACA,MAAMuW,YAAY,GAAG,4BAA4B;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzB;EACA/b,WAAW;EACXgc,YAAY,GAAG,QAAQ;EACvBC,UAAU,GAAG,EAAE;EACfC,aAAa,GAAG,EAAE;EAClBC,WAAW,GAAG,EAAE;EAChBC,UAAU,GAAG,EAAE;EACfC,QAAQ,GAAG,EAAE;EACbC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,EAAE;EACZxK,WAAW,GAAG,KAAK;EACnBlU,MAAMA,CAACqC,UAAU,EAAE;IACf,MAAMgD,MAAM,GAAGhD,UAAU,CAACoP,SAAS,CAAC,CAAC;IACrC,IAAI,CAACtP,WAAW,GAAGE,UAAU;IAC7B,IAAI,IAAI,CAACoc,MAAM,IAAI,CAACpZ,MAAM,CAAC1D,KAAK,EAAE;MAC9BU,UAAU,CAACwP,UAAU,CAAC;QAAElQ,KAAK,EAAE,IAAI,CAAC8c;MAAO,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACC,OAAO,IAAI,CAACrZ,MAAM,CAAC5D,MAAM,EAAE;MAChCY,UAAU,CAACwP,UAAU,CAAC;QAAEpQ,MAAM,EAAE,IAAI,CAACid;MAAQ,CAAC,CAAC;IACnD;IACArc,UAAU,CAAC2N,WAAW,CAACzP,SAAS,CAACC,GAAG,CAACyd,YAAY,CAAC;IAClD,IAAI,CAAC/J,WAAW,GAAG,KAAK;EAC5B;EACA;AACJ;AACA;AACA;EACIzU,GAAGA,CAAC8I,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAAC8V,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,UAAU,GAAG7V,KAAK;IACvB,IAAI,CAAC+V,WAAW,GAAG,YAAY;IAC/B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI5e,IAAIA,CAAC6I,KAAK,GAAG,EAAE,EAAE;IACb,IAAI,CAACiW,QAAQ,GAAGjW,KAAK;IACrB,IAAI,CAACgW,UAAU,GAAG,MAAM;IACxB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIva,MAAMA,CAACuE,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,CAAC6V,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG9V,KAAK;IAC1B,IAAI,CAAC+V,WAAW,GAAG,UAAU;IAC7B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIna,KAAKA,CAACoE,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACiW,QAAQ,GAAGjW,KAAK;IACrB,IAAI,CAACgW,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACItE,KAAKA,CAAC1R,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,CAACiW,QAAQ,GAAGjW,KAAK;IACrB,IAAI,CAACgW,UAAU,GAAG,OAAO;IACzB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACpW,KAAK,GAAG,EAAE,EAAE;IACZ,IAAI,CAACiW,QAAQ,GAAGjW,KAAK;IACrB,IAAI,CAACgW,UAAU,GAAG,KAAK;IACvB,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI5c,KAAKA,CAAC4G,KAAK,GAAG,EAAE,EAAE;IACd,IAAI,IAAI,CAACpG,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC0P,UAAU,CAAC;QAAElQ,KAAK,EAAE4G;MAAM,CAAC,CAAC;IACjD,CAAC,MACI;MACD,IAAI,CAACkW,MAAM,GAAGlW,KAAK;IACvB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9G,MAAMA,CAAC8G,KAAK,GAAG,EAAE,EAAE;IACf,IAAI,IAAI,CAACpG,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC0P,UAAU,CAAC;QAAEpQ,MAAM,EAAE8G;MAAM,CAAC,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAACmW,OAAO,GAAGnW,KAAK;IACxB;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqW,kBAAkBA,CAACzG,MAAM,GAAG,EAAE,EAAE;IAC5B,IAAI,CAACzY,IAAI,CAACyY,MAAM,CAAC;IACjB,IAAI,CAACoG,UAAU,GAAG,QAAQ;IAC1B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIM,gBAAgBA,CAAC1G,MAAM,GAAG,EAAE,EAAE;IAC1B,IAAI,CAAC1Y,GAAG,CAAC0Y,MAAM,CAAC;IAChB,IAAI,CAACmG,WAAW,GAAG,QAAQ;IAC3B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI5M,KAAKA,CAAA,EAAG;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACvP,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MACtD;IACJ;IACA,MAAMoJ,MAAM,GAAG,IAAI,CAACxK,WAAW,CAACQ,cAAc,CAACrC,KAAK;IACpD,MAAMwe,YAAY,GAAG,IAAI,CAAC3c,WAAW,CAAC6N,WAAW,CAAC1P,KAAK;IACvD,MAAM+E,MAAM,GAAG,IAAI,CAAClD,WAAW,CAACsP,SAAS,CAAC,CAAC;IAC3C,MAAM;MAAE9P,KAAK;MAAEF,MAAM;MAAEkF,QAAQ;MAAEC;IAAU,CAAC,GAAGvB,MAAM;IACrD,MAAM0Z,yBAAyB,GAAG,CAACpd,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,MACnE,CAACgF,QAAQ,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,CAAC;IAC9D,MAAMqY,uBAAuB,GAAG,CAACvd,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,MACnE,CAACmF,SAAS,IAAIA,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,CAAC;IACjE,MAAMqY,SAAS,GAAG,IAAI,CAACV,UAAU;IACjC,MAAMW,OAAO,GAAG,IAAI,CAACV,QAAQ;IAC7B,MAAMnD,KAAK,GAAG,IAAI,CAAClZ,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAC5K,SAAS,KAAK,KAAK;IAC9D,IAAIsY,UAAU,GAAG,EAAE;IACnB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIjI,cAAc,GAAG,EAAE;IACvB,IAAI4H,yBAAyB,EAAE;MAC3B5H,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAI8H,SAAS,KAAK,QAAQ,EAAE;MAC7B9H,cAAc,GAAG,QAAQ;MACzB,IAAIkE,KAAK,EAAE;QACP+D,WAAW,GAAGF,OAAO;MACzB,CAAC,MACI;QACDC,UAAU,GAAGD,OAAO;MACxB;IACJ,CAAC,MACI,IAAI7D,KAAK,EAAE;MACZ,IAAI4D,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,KAAK,EAAE;QAC7C9H,cAAc,GAAG,UAAU;QAC3BgI,UAAU,GAAGD,OAAO;MACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,OAAO,EAAE;QACrD9H,cAAc,GAAG,YAAY;QAC7BiI,WAAW,GAAGF,OAAO;MACzB;IACJ,CAAC,MACI,IAAID,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MACpD9H,cAAc,GAAG,YAAY;MAC7BgI,UAAU,GAAGD,OAAO;IACxB,CAAC,MACI,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,KAAK,EAAE;MACnD9H,cAAc,GAAG,UAAU;MAC3BiI,WAAW,GAAGF,OAAO;IACzB;IACAvS,MAAM,CAAC6J,QAAQ,GAAG,IAAI,CAAC2H,YAAY;IACnCxR,MAAM,CAACwS,UAAU,GAAGJ,yBAAyB,GAAG,GAAG,GAAGI,UAAU;IAChExS,MAAM,CAAC0S,SAAS,GAAGL,uBAAuB,GAAG,GAAG,GAAG,IAAI,CAACZ,UAAU;IAClEzR,MAAM,CAAC2S,YAAY,GAAG,IAAI,CAACjB,aAAa;IACxC1R,MAAM,CAACyS,WAAW,GAAGL,yBAAyB,GAAG,GAAG,GAAGK,WAAW;IAClEN,YAAY,CAAC3H,cAAc,GAAGA,cAAc;IAC5C2H,YAAY,CAAC5H,UAAU,GAAG8H,uBAAuB,GAAG,YAAY,GAAG,IAAI,CAACV,WAAW;EACvF;EACA;AACJ;AACA;AACA;EACIhQ,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC4F,WAAW,IAAI,CAAC,IAAI,CAAC/R,WAAW,EAAE;MACvC;IACJ;IACA,MAAMwK,MAAM,GAAG,IAAI,CAACxK,WAAW,CAACQ,cAAc,CAACrC,KAAK;IACpD,MAAMkL,MAAM,GAAG,IAAI,CAACrJ,WAAW,CAAC6N,WAAW;IAC3C,MAAM8O,YAAY,GAAGtT,MAAM,CAAClL,KAAK;IACjCkL,MAAM,CAACjL,SAAS,CAACU,MAAM,CAACgd,YAAY,CAAC;IACrCa,YAAY,CAAC3H,cAAc,GACvB2H,YAAY,CAAC5H,UAAU,GACnBvK,MAAM,CAAC0S,SAAS,GACZ1S,MAAM,CAAC2S,YAAY,GACf3S,MAAM,CAACwS,UAAU,GACbxS,MAAM,CAACyS,WAAW,GACdzS,MAAM,CAAC6J,QAAQ,GACX,EAAE;IAC9B,IAAI,CAACrU,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC+R,WAAW,GAAG,IAAI;EAC3B;AACJ;;AAEA;AACA,MAAMqL,sBAAsB,CAAC;EACzBhgB,cAAc,GAAG9D,MAAM,CAAC2C,aAAa,CAAC;EACtCyB,SAAS,GAAGpE,MAAM,CAACwB,QAAQ,CAAC;EAC5BmN,SAAS,GAAG3O,MAAM,CAAC2B,QAAQ,CAAC;EAC5B8V,iBAAiB,GAAGzX,MAAM,CAACuR,gBAAgB,CAAC;EAC5ClN,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;EACI0f,MAAMA,CAAA,EAAG;IACL,OAAO,IAAItB,sBAAsB,CAAC,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIuB,mBAAmBA,CAAC9X,MAAM,EAAE;IACxB,OAAO,IAAIsL,iCAAiC,CAACtL,MAAM,EAAE,IAAI,CAACpI,cAAc,EAAE,IAAI,CAACM,SAAS,EAAE,IAAI,CAACuK,SAAS,EAAE,IAAI,CAAC8I,iBAAiB,CAAC;EACrI;EACA,OAAO1N,IAAI,YAAAka,+BAAAha,iBAAA;IAAA,YAAAA,iBAAA,IAAwF6Z,sBAAsB;EAAA;EACzH,OAAO5Z,KAAK,kBAjoE6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EAioEY0Z,sBAAsB;IAAAzZ,OAAA,EAAtByZ,sBAAsB,CAAA/Z,IAAA;IAAAO,UAAA,EAAc;EAAM;AACrJ;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KAnoE6F9G,EAAE,CAAAwK,iBAAA,CAmoEJuZ,sBAAsB,EAAc,CAAC;IACpHtZ,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4Z,OAAO,CAAC;EACVC,gBAAgB,GAAGnkB,MAAM,CAACyJ,qBAAqB,CAAC;EAChDgO,iBAAiB,GAAGzX,MAAM,CAACuR,gBAAgB,CAAC;EAC5C6S,gBAAgB,GAAGpkB,MAAM,CAAC8jB,sBAAsB,CAAC;EACjD1Q,mBAAmB,GAAGpT,MAAM,CAAC0N,yBAAyB,CAAC;EACvD8F,SAAS,GAAGxT,MAAM,CAACW,QAAQ,CAAC;EAC5B4F,OAAO,GAAGvG,MAAM,CAACC,MAAM,CAAC;EACxBmE,SAAS,GAAGpE,MAAM,CAACwB,QAAQ,CAAC;EAC5B6iB,eAAe,GAAGrkB,MAAM,CAACsD,cAAc,CAAC;EACxC+P,SAAS,GAAGrT,MAAM,CAACyB,QAAQ,CAAC;EAC5B6R,uBAAuB,GAAGtT,MAAM,CAAC0O,6BAA6B,CAAC;EAC/D4V,qBAAqB,GAAGtkB,MAAM,CAACY,qBAAqB,EAAE;IAAE2jB,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzEC,YAAY,GAAGxkB,MAAM,CAACuD,YAAY,CAAC;EACnCoK,SAAS,GAAG3N,MAAM,CAACG,gBAAgB,CAAC,CAACyN,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC/D6W,OAAO;EACPhT,YAAY,GAAGzR,MAAM,CAACgC,sBAAsB,CAAC;EAC7CqC,WAAWA,CAAA,EAAG,CAAE;EAChB;AACJ;AACA;AACA;AACA;EACIqgB,MAAMA,CAAC9a,MAAM,EAAE;IACX;IACA;IACA,IAAI,CAAC6H,YAAY,CAACY,IAAI,CAAC/B,sBAAsB,CAAC;IAC9C,MAAMF,IAAI,GAAG,IAAI,CAACuU,kBAAkB,CAAC,CAAC;IACtC,MAAMC,IAAI,GAAG,IAAI,CAACC,kBAAkB,CAACzU,IAAI,CAAC;IAC1C,MAAM0U,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACH,IAAI,CAAC;IACnD,MAAMI,aAAa,GAAG,IAAIta,aAAa,CAACd,MAAM,CAAC;IAC/Cob,aAAa,CAAC5Z,SAAS,GAAG4Z,aAAa,CAAC5Z,SAAS,IAAI,IAAI,CAACiZ,eAAe,CAACvX,KAAK;IAC/E,OAAO,IAAIkG,UAAU,CAAC8R,YAAY,EAAE1U,IAAI,EAAEwU,IAAI,EAAEI,aAAa,EAAE,IAAI,CAACze,OAAO,EAAE,IAAI,CAAC6M,mBAAmB,EAAE,IAAI,CAAChP,SAAS,EAAE,IAAI,CAACiP,SAAS,EAAE,IAAI,CAACC,uBAAuB,EAAE,IAAI,CAACgR,qBAAqB,KAAK,gBAAgB,EAAE,IAAI,CAAC9Q,SAAS,CAACyR,GAAG,CAACpkB,mBAAmB,CAAC,EAAE,IAAI,CAAC8M,SAAS,CAAC;EAClR;EACA;AACJ;AACA;AACA;AACA;EACIoN,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACqJ,gBAAgB;EAChC;EACA;AACJ;AACA;AACA;EACIS,kBAAkBA,CAACzU,IAAI,EAAE;IACrB,MAAMwU,IAAI,GAAG,IAAI,CAACxgB,SAAS,CAAC8N,aAAa,CAAC,KAAK,CAAC;IAChD0S,IAAI,CAACM,EAAE,GAAG,IAAI,CAACV,YAAY,CAACW,KAAK,CAAC,cAAc,CAAC;IACjDP,IAAI,CAAC9f,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACtCqL,IAAI,CAACgC,WAAW,CAACwS,IAAI,CAAC;IACtB,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACID,kBAAkBA,CAAA,EAAG;IACjB,MAAMvU,IAAI,GAAG,IAAI,CAAChM,SAAS,CAAC8N,aAAa,CAAC,KAAK,CAAC;IAChD,IAAI,CAACuF,iBAAiB,CAAC/F,mBAAmB,CAAC,CAAC,CAACU,WAAW,CAAChC,IAAI,CAAC;IAC9D,OAAOA,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI2U,mBAAmBA,CAACH,IAAI,EAAE;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACf,IAAI,CAACA,OAAO,GAAG,IAAI,CAACjR,SAAS,CAACyR,GAAG,CAACnkB,cAAc,CAAC;IACrD;IACA,OAAO,IAAIkC,eAAe,CAAC4hB,IAAI,EAAE,IAAI,EAAE,IAAI,CAACH,OAAO,EAAE,IAAI,CAACjR,SAAS,EAAE,IAAI,CAACpP,SAAS,CAAC;EACxF;EACA,OAAO2F,IAAI,YAAAqb,gBAAAnb,iBAAA;IAAA,YAAAA,iBAAA,IAAwFia,OAAO;EAAA;EAC1G,OAAOha,KAAK,kBA5tE6EnK,EAAE,CAAAoK,kBAAA;IAAAC,KAAA,EA4tEY8Z,OAAO;IAAA7Z,OAAA,EAAP6Z,OAAO,CAAAna,IAAA;IAAAO,UAAA,EAAc;EAAM;AACtI;AACA;EAAA,QAAAzD,SAAA,oBAAAA,SAAA,KA9tE6F9G,EAAE,CAAAwK,iBAAA,CA8tEJ2Z,OAAO,EAAc,CAAC;IACrG1Z,IAAI,EAAEtK,UAAU;IAChBuK,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA,MAAM+a,mBAAmB,GAAG,CACxB;EACIvZ,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,OAAO;EAChBC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,EACD;EACIH,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE;AACd,CAAC,CACJ;AACD;AACA,MAAMqZ,qCAAqC,GAAG,IAAIvkB,cAAc,CAAC,uCAAuC,EAAE;EACtGuJ,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM8B,OAAO,GAAGnM,MAAM,CAACkkB,OAAO,CAAC;IAC/B,OAAO,MAAM/X,OAAO,CAACgY,gBAAgB,CAACra,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMyb,gBAAgB,CAAC;EACnBC,UAAU,GAAGxlB,MAAM,CAACU,UAAU,CAAC;EAC/B2D,WAAWA,CAAA,EAAG,CAAE;EAChB,OAAO0F,IAAI,YAAA0b,yBAAAxb,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsb,gBAAgB;EAAA;EACnH,OAAOG,IAAI,kBA9wE8E3lB,EAAE,CAAA4lB,iBAAA;IAAAnb,IAAA,EA8wEJ+a,gBAAgB;IAAA7U,SAAA;IAAAkV,QAAA;EAAA;AAC3G;AACA;EAAA,QAAA/e,SAAA,oBAAAA,SAAA,KAhxE6F9G,EAAE,CAAAwK,iBAAA,CAgxEJgb,gBAAgB,EAAc,CAAC;IAC9G/a,IAAI,EAAExJ,SAAS;IACfyJ,IAAI,EAAE,CAAC;MACCoS,QAAQ,EAAE,4DAA4D;MACtE+I,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBC,QAAQ,GAAG9lB,MAAM,CAACkkB,OAAO,CAAC;EAC1B6B,IAAI,GAAG/lB,MAAM,CAACsD,cAAc,EAAE;IAAEihB,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjD7d,WAAW;EACXsf,eAAe;EACfC,qBAAqB,GAAG/jB,YAAY,CAAC6R,KAAK;EAC1CmS,mBAAmB,GAAGhkB,YAAY,CAAC6R,KAAK;EACxCoS,mBAAmB,GAAGjkB,YAAY,CAAC6R,KAAK;EACxCqS,qBAAqB,GAAGlkB,YAAY,CAAC6R,KAAK;EAC1CgF,QAAQ;EACRC,QAAQ;EACRqN,SAAS;EACTC,sBAAsB,GAAGtmB,MAAM,CAACslB,qCAAqC,CAAC;EACtEiB,oBAAoB,GAAG,KAAK;EAC5BhgB,OAAO,GAAGvG,MAAM,CAACC,MAAM,CAAC;EACxB;EACAiM,MAAM;EACN;EACAmN,SAAS;EACT;AACJ;AACA;AACA;EACI1O,gBAAgB;EAChB;EACA,IAAIiB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmN,QAAQ;EACxB;EACA,IAAInN,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACmN,QAAQ,GAAGnN,OAAO;IACvB,IAAI,IAAI,CAACya,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;IAChD;EACJ;EACA;EACA,IAAIxa,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmN,QAAQ;EACxB;EACA,IAAInN,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAACmN,QAAQ,GAAGnN,OAAO;IACvB,IAAI,IAAI,CAACwa,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;IAChD;EACJ;EACA;EACAngB,KAAK;EACL;EACAF,MAAM;EACN;EACAgF,QAAQ;EACR;EACAC,SAAS;EACT;EACAF,aAAa;EACb;EACAF,UAAU;EACV;EACA4b,cAAc,GAAG,CAAC;EAClB;EACA7b,cAAc;EACd;EACA8b,IAAI,GAAG,KAAK;EACZ;EACAC,YAAY,GAAG,KAAK;EACpB;EACAC,uBAAuB;EACvB;EACA9b,WAAW,GAAG,KAAK;EACnB;EACA+b,YAAY,GAAG,KAAK;EACpB;EACA3K,kBAAkB,GAAG,KAAK;EAC1B;EACAE,aAAa,GAAG,KAAK;EACrB;EACAhP,IAAI,GAAG,KAAK;EACZ;EACA,IAAI/B,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACkb,oBAAoB;EACpC;EACA,IAAIlb,mBAAmBA,CAACyB,KAAK,EAAE;IAC3B,IAAI,CAACyZ,oBAAoB,GAAGzZ,KAAK;EACrC;EACA;EACA8I,aAAa,GAAG,IAAI3U,YAAY,CAAC,CAAC;EAClC;EACA6lB,cAAc,GAAG,IAAI7lB,YAAY,CAAC,CAAC;EACnC;EACAsD,MAAM,GAAG,IAAItD,YAAY,CAAC,CAAC;EAC3B;EACA4G,MAAM,GAAG,IAAI5G,YAAY,CAAC,CAAC;EAC3B;EACA8lB,cAAc,GAAG,IAAI9lB,YAAY,CAAC,CAAC;EACnC;EACA+lB,mBAAmB,GAAG,IAAI/lB,YAAY,CAAC,CAAC;EACxC;EACAoD,WAAWA,CAAA,EAAG;IACV,MAAM4iB,WAAW,GAAGjnB,MAAM,CAACkB,WAAW,CAAC;IACvC,MAAMgmB,gBAAgB,GAAGlnB,MAAM,CAACmB,gBAAgB,CAAC;IACjD,IAAI,CAAC6kB,eAAe,GAAG,IAAI9iB,cAAc,CAAC+jB,WAAW,EAAEC,gBAAgB,CAAC;IACxE,IAAI,CAACtc,cAAc,GAAG,IAAI,CAAC0b,sBAAsB,CAAC,CAAC;EACvD;EACA;EACA,IAAI1f,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACF,WAAW;EAC3B;EACA;EACA,IAAI6P,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACwP,IAAI,GAAG,IAAI,CAACA,IAAI,CAACjZ,KAAK,GAAG,KAAK;EAC9C;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+Y,mBAAmB,CAACte,WAAW,CAAC,CAAC;IACtC,IAAI,CAACue,mBAAmB,CAACve,WAAW,CAAC,CAAC;IACtC,IAAI,CAACqe,qBAAqB,CAACre,WAAW,CAAC,CAAC;IACxC,IAAI,CAACwe,qBAAqB,CAACxe,WAAW,CAAC,CAAC;IACxC,IAAI,CAAClB,WAAW,EAAEmM,OAAO,CAAC,CAAC;EAC/B;EACAsU,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAACf,SAAS,EAAE;MAChB,IAAI,CAACG,uBAAuB,CAAC,IAAI,CAACH,SAAS,CAAC;MAC5C,IAAI,CAAC3f,WAAW,EAAE0P,UAAU,CAAC;QACzBlQ,KAAK,EAAE,IAAI,CAACA,KAAK;QACjB8E,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBhF,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBiF,SAAS,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC;MACF,IAAImc,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAACV,IAAI,EAAE;QAChC,IAAI,CAACL,SAAS,CAACpQ,KAAK,CAAC,CAAC;MAC1B;IACJ;IACA,IAAImR,OAAO,CAAC,MAAM,CAAC,EAAE;MACjB,IAAI,CAACV,IAAI,GAAG,IAAI,CAACW,aAAa,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IAC3D;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAClO,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAAC7L,MAAM,EAAE;MAC3C,IAAI,CAAC6L,SAAS,GAAGgM,mBAAmB;IACxC;IACA,MAAMze,UAAU,GAAI,IAAI,CAACF,WAAW,GAAG,IAAI,CAACof,QAAQ,CAACpB,MAAM,CAAC,IAAI,CAAC8C,YAAY,CAAC,CAAC,CAAE;IACjF,IAAI,CAACtB,mBAAmB,GAAGtf,UAAU,CAACiP,WAAW,CAAC,CAAC,CAACvO,SAAS,CAAC,MAAM,IAAI,CAAC/C,MAAM,CAACkjB,IAAI,CAAC,CAAC,CAAC;IACvF,IAAI,CAACtB,mBAAmB,GAAGvf,UAAU,CAACkP,WAAW,CAAC,CAAC,CAACxO,SAAS,CAAC,MAAM,IAAI,CAACO,MAAM,CAAC4f,IAAI,CAAC,CAAC,CAAC;IACvF7gB,UAAU,CAACmP,aAAa,CAAC,CAAC,CAACzO,SAAS,CAAE2G,KAAK,IAAK;MAC5C,IAAI,CAAC8Y,cAAc,CAACzY,IAAI,CAACL,KAAK,CAAC;MAC/B,IAAIA,KAAK,CAACyZ,OAAO,KAAKjkB,MAAM,IAAI,CAAC,IAAI,CAACkjB,YAAY,IAAI,CAACjjB,cAAc,CAACuK,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAAC0Z,cAAc,CAAC,CAAC;QACtB,IAAI,CAACL,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;IACF,IAAI,CAAC5gB,WAAW,CAACkJ,oBAAoB,CAAC,CAAC,CAACtI,SAAS,CAAE2G,KAAK,IAAK;MACzD,MAAM/B,MAAM,GAAG,IAAI,CAAC0b,iBAAiB,CAAC,CAAC;MACvC,MAAMpY,MAAM,GAAG1N,eAAe,CAACmM,KAAK,CAAC;MACrC,IAAI,CAAC/B,MAAM,IAAKA,MAAM,KAAKsD,MAAM,IAAI,CAACtD,MAAM,CAACvG,QAAQ,CAAC6J,MAAM,CAAE,EAAE;QAC5D,IAAI,CAACwX,mBAAmB,CAAC1Y,IAAI,CAACL,KAAK,CAAC;MACxC;IACJ,CAAC,CAAC;EACN;EACA;EACAuZ,YAAYA,CAAA,EAAG;IACX,MAAM7c,gBAAgB,GAAI,IAAI,CAAC0b,SAAS,GACpC,IAAI,CAAC1b,gBAAgB,IAAI,IAAI,CAACkd,uBAAuB,CAAC,CAAE;IAC5D,MAAM7C,aAAa,GAAG,IAAIta,aAAa,CAAC;MACpCU,SAAS,EAAE,IAAI,CAAC2a,IAAI,IAAI,KAAK;MAC7Bpb,gBAAgB;MAChBC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCE,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BO,mBAAmB,EAAE,IAAI,CAACA;IAC9B,CAAC,CAAC;IACF,IAAI,IAAI,CAACnF,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,EAAE;MAChC8e,aAAa,CAAC9e,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC;IACA,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EAAE;MAClCgf,aAAa,CAAChf,MAAM,GAAG,IAAI,CAACA,MAAM;IACtC;IACA,IAAI,IAAI,CAACgF,QAAQ,IAAI,IAAI,CAACA,QAAQ,KAAK,CAAC,EAAE;MACtCga,aAAa,CAACha,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC1C;IACA,IAAI,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;MACxC+Z,aAAa,CAAC/Z,SAAS,GAAG,IAAI,CAACA,SAAS;IAC5C;IACA,IAAI,IAAI,CAACF,aAAa,EAAE;MACpBia,aAAa,CAACja,aAAa,GAAG,IAAI,CAACA,aAAa;IACpD;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjBma,aAAa,CAACna,UAAU,GAAG,IAAI,CAACA,UAAU;IAC9C;IACA,OAAOma,aAAa;EACxB;EACA;EACAwB,uBAAuBA,CAAC7b,gBAAgB,EAAE;IACtC,MAAM0O,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8H,GAAG,CAAC2G,eAAe,KAAK;MACrDhc,OAAO,EAAEgc,eAAe,CAAChc,OAAO;MAChCC,OAAO,EAAE+b,eAAe,CAAC/b,OAAO;MAChCC,QAAQ,EAAE8b,eAAe,CAAC9b,QAAQ;MAClCC,QAAQ,EAAE6b,eAAe,CAAC7b,QAAQ;MAClCL,OAAO,EAAEkc,eAAe,CAAClc,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDC,OAAO,EAAEic,eAAe,CAACjc,OAAO,IAAI,IAAI,CAACA,OAAO;MAChDhB,UAAU,EAAEid,eAAe,CAACjd,UAAU,IAAIa;IAC9C,CAAC,CAAC,CAAC;IACH,OAAOf,gBAAgB,CAClB4O,SAAS,CAAC,IAAI,CAACwO,UAAU,CAAC,CAAC,CAAC,CAC5BjM,aAAa,CAACzC,SAAS,CAAC,CACxB4C,sBAAsB,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAC/CG,QAAQ,CAAC,IAAI,CAACjP,IAAI,CAAC,CACnB+O,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CACrCL,kBAAkB,CAAC,IAAI,CAAC0K,cAAc,CAAC,CACvClK,kBAAkB,CAAC,IAAI,CAACsK,YAAY,CAAC,CACrCjK,qBAAqB,CAAC,IAAI,CAACgK,uBAAuB,CAAC;EAC5D;EACA;EACAiB,uBAAuBA,CAAA,EAAG;IACtB,MAAM1R,QAAQ,GAAG,IAAI,CAAC2P,QAAQ,CAAC/K,QAAQ,CAAC,CAAC,CAACiJ,mBAAmB,CAAC,IAAI,CAAC+D,UAAU,CAAC,CAAC,CAAC;IAChF,IAAI,CAACvB,uBAAuB,CAACrQ,QAAQ,CAAC;IACtC,OAAOA,QAAQ;EACnB;EACA4R,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAC7b,MAAM,YAAYqZ,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAACrZ,MAAM,CAACsZ,UAAU;IACjC,CAAC,MACI;MACD,OAAO,IAAI,CAACtZ,MAAM;IACtB;EACJ;EACA0b,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAAC1b,MAAM,YAAYqZ,gBAAgB,EAAE;MACzC,OAAO,IAAI,CAACrZ,MAAM,CAACsZ,UAAU,CAACpe,aAAa;IAC/C;IACA,IAAI,IAAI,CAAC8E,MAAM,YAAYxL,UAAU,EAAE;MACnC,OAAO,IAAI,CAACwL,MAAM,CAAC9E,aAAa;IACpC;IACA,IAAI,OAAOua,OAAO,KAAK,WAAW,IAAI,IAAI,CAACzV,MAAM,YAAYyV,OAAO,EAAE;MAClE,OAAO,IAAI,CAACzV,MAAM;IACtB;IACA,OAAO,IAAI;EACf;EACA;EACAmb,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAC3gB,WAAW,EAAE;MACnB,IAAI,CAAC6gB,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACA,IAAI,CAAC7gB,WAAW,CAACsP,SAAS,CAAC,CAAC,CAAClL,WAAW,GAAG,IAAI,CAACA,WAAW;IAC/D;IACA,IAAI,CAAC,IAAI,CAACpE,WAAW,CAACoB,WAAW,CAAC,CAAC,EAAE;MACjC,IAAI,CAACpB,WAAW,CAACnC,MAAM,CAAC,IAAI,CAACyhB,eAAe,CAAC;IACjD;IACA,IAAI,IAAI,CAAClb,WAAW,EAAE;MAClB,IAAI,CAACmb,qBAAqB,GAAG,IAAI,CAACvf,WAAW,CAACkP,aAAa,CAAC,CAAC,CAACtO,SAAS,CAAC2G,KAAK,IAAI;QAC7E,IAAI,CAAC2H,aAAa,CAAC6R,IAAI,CAACxZ,KAAK,CAAC;MAClC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACgY,qBAAqB,CAACre,WAAW,CAAC,CAAC;IAC5C;IACA,IAAI,CAACwe,qBAAqB,CAACxe,WAAW,CAAC,CAAC;IACxC;IACA;IACA,IAAI,IAAI,CAACkf,cAAc,CAACzY,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAAC4Y,qBAAqB,GAAG,IAAI,CAACC,SAAS,CAACjN,eAAe,CACtDpS,IAAI,CAAC1E,SAAS,CAAC,MAAM,IAAI,CAACwkB,cAAc,CAACzY,SAAS,CAACb,MAAM,GAAG,CAAC,CAAC,CAAC,CAC/DlG,SAAS,CAACyT,QAAQ,IAAI;QACvB,IAAI,CAACxU,OAAO,CAACwB,GAAG,CAAC,MAAM,IAAI,CAAC+e,cAAc,CAACW,IAAI,CAAC1M,QAAQ,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC+L,cAAc,CAACzY,SAAS,CAACb,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAAC4Y,qBAAqB,CAACxe,WAAW,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAAC8e,IAAI,GAAG,IAAI;EACpB;EACA;EACAY,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC5gB,WAAW,EAAEmB,MAAM,CAAC,CAAC;IAC1B,IAAI,CAACoe,qBAAqB,CAACre,WAAW,CAAC,CAAC;IACxC,IAAI,CAACwe,qBAAqB,CAACxe,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC8e,IAAI,GAAG,KAAK;EACrB;EACA,OAAO3c,IAAI,YAAAie,4BAAA/d,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4b,mBAAmB;EAAA;EACtH,OAAOH,IAAI,kBAjjF8E3lB,EAAE,CAAA4lB,iBAAA;IAAAnb,IAAA,EAijFJqb,mBAAmB;IAAAnV,SAAA;IAAAuX,MAAA;MAAA/b,MAAA;MAAAmN,SAAA;MAAA1O,gBAAA;MAAAiB,OAAA;MAAAC,OAAA;MAAA3F,KAAA;MAAAF,MAAA;MAAAgF,QAAA;MAAAC,SAAA;MAAAF,aAAA;MAAAF,UAAA;MAAA4b,cAAA;MAAA7b,cAAA;MAAA8b,IAAA;MAAAC,YAAA;MAAAC,uBAAA;MAAA9b,WAAA,uDAAmoC1J,gBAAgB;MAAAylB,YAAA,yDAAqEzlB,gBAAgB;MAAA8a,kBAAA,qEAAuF9a,gBAAgB;MAAAgb,aAAA,2DAAwEhb,gBAAgB;MAAAgM,IAAA,yCAA6ChM,gBAAgB;MAAAiK,mBAAA,uEAA0FjK,gBAAgB;IAAA;IAAA8mB,OAAA;MAAAtS,aAAA;MAAAkR,cAAA;MAAAviB,MAAA;MAAAsD,MAAA;MAAAkf,cAAA;MAAAC,mBAAA;IAAA;IAAApB,QAAA;IAAAuC,QAAA,GAjjF/lDpoB,EAAE,CAAAqoB,oBAAA;EAAA;AAkjF/F;AACA;EAAA,QAAAvhB,SAAA,oBAAAA,SAAA,KAnjF6F9G,EAAE,CAAAwK,iBAAA,CAmjFJsb,mBAAmB,EAAc,CAAC;IACjHrb,IAAI,EAAExJ,SAAS;IACfyJ,IAAI,EAAE,CAAC;MACCoS,QAAQ,EAAE,qEAAqE;MAC/E+I,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE1Z,MAAM,EAAE,CAAC;MACjD1B,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE4O,SAAS,EAAE,CAAC;MACZ7O,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEE,gBAAgB,EAAE,CAAC;MACnBH,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,qCAAqC;IAChD,CAAC,CAAC;IAAEmB,OAAO,EAAE,CAAC;MACVpB,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEoB,OAAO,EAAE,CAAC;MACVrB,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEvE,KAAK,EAAE,CAAC;MACRsE,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEzE,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEO,QAAQ,EAAE,CAAC;MACXR,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,6BAA6B;IACxC,CAAC,CAAC;IAAEQ,SAAS,EAAE,CAAC;MACZT,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,8BAA8B;IACzC,CAAC,CAAC;IAAEM,aAAa,EAAE,CAAC;MAChBP,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,kCAAkC;IAC7C,CAAC,CAAC;IAAEI,UAAU,EAAE,CAAC;MACbL,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEgc,cAAc,EAAE,CAAC;MACjBjc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEG,cAAc,EAAE,CAAC;MACjBJ,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,mCAAmC;IAC9C,CAAC,CAAC;IAAEic,IAAI,EAAE,CAAC;MACPlc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEkc,YAAY,EAAE,CAAC;MACfnc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,iCAAiC;IAC5C,CAAC,CAAC;IAAEmc,uBAAuB,EAAE,CAAC;MAC1Bpc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC,sCAAsC;IACjD,CAAC,CAAC;IAAEK,WAAW,EAAE,CAAC;MACdN,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,gCAAgC;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IACnF,CAAC,CAAC;IAAEylB,YAAY,EAAE,CAAC;MACfrc,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,iCAAiC;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IACpF,CAAC,CAAC;IAAE8a,kBAAkB,EAAE,CAAC;MACrB1R,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,uCAAuC;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IAC1F,CAAC,CAAC;IAAEgb,aAAa,EAAE,CAAC;MAChB5R,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,kCAAkC;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IACrF,CAAC,CAAC;IAAEgM,IAAI,EAAE,CAAC;MACP5C,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,yBAAyB;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IAC5E,CAAC,CAAC;IAAEiK,mBAAmB,EAAE,CAAC;MACtBb,IAAI,EAAEnJ,KAAK;MACXoJ,IAAI,EAAE,CAAC;QAAE4d,KAAK,EAAE,wCAAwC;QAAEjI,SAAS,EAAEhf;MAAiB,CAAC;IAC3F,CAAC,CAAC;IAAEwU,aAAa,EAAE,CAAC;MAChBpL,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwlB,cAAc,EAAE,CAAC;MACjBtc,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEiD,MAAM,EAAE,CAAC;MACTiG,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuG,MAAM,EAAE,CAAC;MACT2C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEylB,cAAc,EAAE,CAAC;MACjBvc,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE0lB,mBAAmB,EAAE,CAAC;MACtBxc,IAAI,EAAElJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA,SAASgnB,sDAAsDA,CAACnc,OAAO,EAAE;EACrE,OAAO,MAAMA,OAAO,CAACgY,gBAAgB,CAACra,UAAU,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMye,8CAA8C,GAAG;EACnDC,OAAO,EAAElD,qCAAqC;EAC9CmD,IAAI,EAAE,CAACvE,OAAO,CAAC;EACfwE,UAAU,EAAEJ;AAChB,CAAC;AAED,MAAMK,aAAa,CAAC;EAChB,OAAO5e,IAAI,YAAA6e,sBAAA3e,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0e,aAAa;EAAA;EAChH,OAAOE,IAAI,kBA7pF8E9oB,EAAE,CAAA+oB,gBAAA;IAAAte,IAAA,EA6pFSme,aAAa;IAAAI,OAAA,GAAYplB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEijB,mBAAmB,EAAEN,gBAAgB;IAAAyD,OAAA,GAAanD,mBAAmB,EAAEN,gBAAgB,EAAE3iB,eAAe;EAAA;EAChR,OAAOqmB,IAAI,kBA9pF8ElpB,EAAE,CAAAmpB,gBAAA;IAAAC,SAAA,EA8pFmC,CAACjF,OAAO,EAAEqE,8CAA8C,CAAC;IAAAQ,OAAA,GAAYplB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEA,eAAe;EAAA;AACjQ;AACA;EAAA,QAAAiE,SAAA,oBAAAA,SAAA,KAhqF6F9G,EAAE,CAAAwK,iBAAA,CAgqFJoe,aAAa,EAAc,CAAC;IAC3Gne,IAAI,EAAEjJ,QAAQ;IACdkJ,IAAI,EAAE,CAAC;MACCse,OAAO,EAAE,CAACplB,UAAU,EAAEP,YAAY,EAAER,eAAe,EAAEijB,mBAAmB,EAAEN,gBAAgB,CAAC;MAC3FyD,OAAO,EAAE,CAACnD,mBAAmB,EAAEN,gBAAgB,EAAE3iB,eAAe,CAAC;MACjEumB,SAAS,EAAE,CAACjF,OAAO,EAAEqE,8CAA8C;IACvE,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAAS1kB,mBAAmB,IAAIulB,CAAC,EAAE7D,gBAAgB,IAAI8D,CAAC,EAAE7R,iCAAiC,IAAI8R,CAAC,EAAE7G,sBAAsB,IAAI8G,CAAC,EAAEvhB,kBAAkB,IAAIwhB,CAAC,EAAEjY,gBAAgB,IAAIkY,CAAC,EAAEvgB,wBAAwB,IAAIwgB,CAAC,EAAEnH,oCAAoC,IAAIoH,CAAC,EAAEzF,OAAO,IAAI7B,CAAC,EAAEwD,mBAAmB,IAAI9iB,CAAC,EAAEiQ,UAAU,IAAIzQ,CAAC,EAAEuhB,sBAAsB,IAAI8F,CAAC,EAAEtH,iCAAiC,IAAIuH,CAAC,EAAEnf,aAAa,IAAIof,CAAC,EAAEne,sBAAsB,IAAInI,CAAC,EAAE4I,mBAAmB,IAAIjJ,CAAC,EAAEsJ,8BAA8B,IAAI0B,CAAC,EAAEpB,0BAA0B,IAAIgd,CAAC,EAAEtgB,qBAAqB,IAAIugB,CAAC,EAAE3jB,mBAAmB,IAAI4jB,CAAC,EAAEtB,aAAa,IAAIuB,CAAC,EAAExb,6BAA6B,IAAIyb,CAAC,EAAEzc,yBAAyB,IAAI0c,CAAC,EAAExd,wBAAwB,IAAIyd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}