#!/bin/bash

# 🔧 CONFIGURATION
PROFILE="walter-holyrides"
BUCKET_NAME="app.bookholyrides.com"
ANGULAR_PROJECT_NAME=""  # or leave empty if default
DISTRIBUTION_ID="E7MSATZY0SAMM"

# Optional: fail on error
#set -e

#echo "🚀 Building Angular project..."
#ng build --configuration production

# Determine correct build output path
#if [[ -z "$ANGULAR_PROJECT_NAME" ]]; then
#  DIST_PATH="dist/"
#else
#  DIST_PATH="dist/$ANGULAR_PROJECT_NAME"
#fi

echo "📤 Syncing build to S3..."
aws s3 sync "holy-rides/dist/holy-rides/browser/" "s3://$BUCKET_NAME/" --delete --profile "$PROFILE"

echo "⚙️ Setting S3 routing rules (optional, but make sure S3 is set up to serve index.html)"
# Ensure index.html is served for all routes
aws s3 website "s3://$BUCKET_NAME/" \
  --index-document index.html \
  --error-document index.html \
  --profile "$PROFILE"

echo "🧹 Invalidating CloudFront cache..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
  --distribution-id "$DISTRIBUTION_ID" \
  --paths "/*" \
  --profile "$PROFILE" \
  --query 'Invalidation.Id' \
  --output text)

echo "✅ CloudFront invalidation started: $INVALIDATION_ID"
echo "🏁 Deployment complete!"
