import{$b as en,Aa as Br,Ab as Ri,<PERSON> as Nt,Ba as Ve,Bb as J,Cd as an,Db as <PERSON>,<PERSON>a as zr,Eb as te,Ed as ze,Fb as F,Ga as Hr,Gb as M,Hb as Kr,Ib as Qr,Jb as Ii,Kb as Yr,Kd as Li,La as T,Lb as Zr,N as jr,Na as qr,Oa as _e,Pa as x,Q as Lr,Qc as nn,Rb as Xr,Rc as $e,S as Nr,Sa as Gr,Sb as S,U as at,Vc as Vi,Wa as lt,X as $t,Xa as X,Y as q,Ya as w,Yc as sn,Z as Y,_ as Z,_a as O,a as Fr,aa as A,ab as R,bd as on,c as Mr,cb as Wr,da as p,dc as Ut,f as H,fc as Ne,g as Or,gb as de,hb as je,hc as ce,hd as Ue,i as Rr,ic as ue,j as Ir,jb as L,jc as tn,ka as ge,la as $r,ma as Ur,mb as I,nc as <PERSON>,pa as Pe,q as Lt,qb as P,ra as Oi,rb as N,sb as Ee,sd as ji,td as Be,va as le,w as Pr,wa as G,wb as Jr,xb as dt,yb as ee,z as Vr,za as W,zb as ve,zc as rn}from"./chunk-ST4QC4E3.js";import{b as ln,c as dn,f as Xs,g as cn}from"./chunk-X5YLR3NI.js";import{a as j,b as z,e as ae,g as Ys,h as Zs,i as u}from"./chunk-ODN5LVDJ.js";var cr=ae(dr=>{"use strict";Object.defineProperty(dr,"__esModule",{value:!0});var lr=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};dr.default=lr});var hr=ae(xt=>{"use strict";var es=xt&&xt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(xt,"__esModule",{value:!0});var Aa=es((cn(),Zs(Xs))),Sa=es(cr()),ur=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=Aa.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,e){return this.headers=Object.assign({},this.headers),this.headers[t]=e,this}then(t,e){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");let i=this.fetch,n=i(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(s=>u(this,null,function*(){var o,a,l;let d=null,c=null,h=null,m=s.status,f=s.statusText;if(s.ok){if(this.method!=="HEAD"){let D=yield s.text();D===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=D:c=JSON.parse(D))}let y=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),v=(a=s.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");y&&v&&v.length>1&&(h=parseInt(v[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(d={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,h=null,m=406,f="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{let y=yield s.text();try{d=JSON.parse(y),Array.isArray(d)&&s.status===404&&(c=[],d=null,m=200,f="OK")}catch{s.status===404&&y===""?(m=204,f="No Content"):d={message:y}}if(d&&this.isMaybeSingle&&(!((l=d?.details)===null||l===void 0)&&l.includes("0 rows"))&&(d=null,m=200,f="OK"),d&&this.shouldThrowOnError)throw new Sa.default(d)}return{error:d,data:c,count:h,status:m,statusText:f}}));return this.shouldThrowOnError||(n=n.catch(s=>{var o,a,l;return{error:{message:`${(o=s?.name)!==null&&o!==void 0?o:"FetchError"}: ${s?.message}`,details:`${(a=s?.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=s?.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),n.then(t,e)}returns(){return this}overrideTypes(){return this}};xt.default=ur});var mr=ae(wt=>{"use strict";var ka=wt&&wt.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(wt,"__esModule",{value:!0});var Da=ka(hr()),fr=class extends Da.default{select(t){let e=!1,i=(t??"*").split("").map(n=>/\s/.test(n)&&!e?"":(n==='"'&&(e=!e),n)).join("");return this.url.searchParams.set("select",i),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:e=!0,nullsFirst:i,foreignTable:n,referencedTable:s=n}={}){let o=s?`${s}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${t}.${e?"asc":"desc"}${i===void 0?"":i?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:e,referencedTable:i=e}={}){let n=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(n,`${t}`),this}range(t,e,{foreignTable:i,referencedTable:n=i}={}){let s=typeof n>"u"?"offset":`${n}.offset`,o=typeof n>"u"?"limit":`${n}.limit`;return this.url.searchParams.set(s,`${t}`),this.url.searchParams.set(o,`${e-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:e=!1,settings:i=!1,buffers:n=!1,wal:s=!1,format:o="text"}={}){var a;let l=[t?"analyze":null,e?"verbose":null,i?"settings":null,n?"buffers":null,s?"wal":null].filter(Boolean).join("|"),d=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${d}"; options=${l};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};wt.default=fr});var ci=ae(Ct=>{"use strict";var Ta=Ct&&Ct.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Ct,"__esModule",{value:!0});var Fa=Ta(mr()),pr=class extends Fa.default{eq(t,e){return this.url.searchParams.append(t,`eq.${e}`),this}neq(t,e){return this.url.searchParams.append(t,`neq.${e}`),this}gt(t,e){return this.url.searchParams.append(t,`gt.${e}`),this}gte(t,e){return this.url.searchParams.append(t,`gte.${e}`),this}lt(t,e){return this.url.searchParams.append(t,`lt.${e}`),this}lte(t,e){return this.url.searchParams.append(t,`lte.${e}`),this}like(t,e){return this.url.searchParams.append(t,`like.${e}`),this}likeAllOf(t,e){return this.url.searchParams.append(t,`like(all).{${e.join(",")}}`),this}likeAnyOf(t,e){return this.url.searchParams.append(t,`like(any).{${e.join(",")}}`),this}ilike(t,e){return this.url.searchParams.append(t,`ilike.${e}`),this}ilikeAllOf(t,e){return this.url.searchParams.append(t,`ilike(all).{${e.join(",")}}`),this}ilikeAnyOf(t,e){return this.url.searchParams.append(t,`ilike(any).{${e.join(",")}}`),this}is(t,e){return this.url.searchParams.append(t,`is.${e}`),this}in(t,e){let i=Array.from(new Set(e)).map(n=>typeof n=="string"&&new RegExp("[,()]").test(n)?`"${n}"`:`${n}`).join(",");return this.url.searchParams.append(t,`in.(${i})`),this}contains(t,e){return typeof e=="string"?this.url.searchParams.append(t,`cs.${e}`):Array.isArray(e)?this.url.searchParams.append(t,`cs.{${e.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(e)}`),this}containedBy(t,e){return typeof e=="string"?this.url.searchParams.append(t,`cd.${e}`):Array.isArray(e)?this.url.searchParams.append(t,`cd.{${e.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(e)}`),this}rangeGt(t,e){return this.url.searchParams.append(t,`sr.${e}`),this}rangeGte(t,e){return this.url.searchParams.append(t,`nxl.${e}`),this}rangeLt(t,e){return this.url.searchParams.append(t,`sl.${e}`),this}rangeLte(t,e){return this.url.searchParams.append(t,`nxr.${e}`),this}rangeAdjacent(t,e){return this.url.searchParams.append(t,`adj.${e}`),this}overlaps(t,e){return typeof e=="string"?this.url.searchParams.append(t,`ov.${e}`):this.url.searchParams.append(t,`ov.{${e.join(",")}}`),this}textSearch(t,e,{config:i,type:n}={}){let s="";n==="plain"?s="pl":n==="phrase"?s="ph":n==="websearch"&&(s="w");let o=i===void 0?"":`(${i})`;return this.url.searchParams.append(t,`${s}fts${o}.${e}`),this}match(t){return Object.entries(t).forEach(([e,i])=>{this.url.searchParams.append(e,`eq.${i}`)}),this}not(t,e,i){return this.url.searchParams.append(t,`not.${e}.${i}`),this}or(t,{foreignTable:e,referencedTable:i=e}={}){let n=i?`${i}.or`:"or";return this.url.searchParams.append(n,`(${t})`),this}filter(t,e,i){return this.url.searchParams.append(t,`${e}.${i}`),this}};Ct.default=pr});var _r=ae(At=>{"use strict";var Ma=At&&At.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(At,"__esModule",{value:!0});var Et=Ma(ci()),gr=class{constructor(t,{headers:e={},schema:i,fetch:n}){this.url=t,this.headers=e,this.schema=i,this.fetch=n}select(t,{head:e=!1,count:i}={}){let n=e?"HEAD":"GET",s=!1,o=(t??"*").split("").map(a=>/\s/.test(a)&&!s?"":(a==='"'&&(s=!s),a)).join("");return this.url.searchParams.set("select",o),i&&(this.headers.Prefer=`count=${i}`),new Et.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:e,defaultToNull:i=!0}={}){let n="POST",s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),e&&s.push(`count=${e}`),i||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){let o=t.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(o.length>0){let a=[...new Set(o)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new Et.default({method:n,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:e,ignoreDuplicates:i=!1,count:n,defaultToNull:s=!0}={}){let o="POST",a=[`resolution=${i?"ignore":"merge"}-duplicates`];if(e!==void 0&&this.url.searchParams.set("on_conflict",e),this.headers.Prefer&&a.push(this.headers.Prefer),n&&a.push(`count=${n}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(t)){let l=t.reduce((d,c)=>d.concat(Object.keys(c)),[]);if(l.length>0){let d=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",d.join(","))}}return new Et.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:e}={}){let i="PATCH",n=[];return this.headers.Prefer&&n.push(this.headers.Prefer),e&&n.push(`count=${e}`),this.headers.Prefer=n.join(","),new Et.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){let e="DELETE",i=[];return t&&i.push(`count=${t}`),this.headers.Prefer&&i.unshift(this.headers.Prefer),this.headers.Prefer=i.join(","),new Et.default({method:e,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};At.default=gr});var ts=ae(ui=>{"use strict";Object.defineProperty(ui,"__esModule",{value:!0});ui.version=void 0;ui.version="0.0.0-automated"});var is=ae(hi=>{"use strict";Object.defineProperty(hi,"__esModule",{value:!0});hi.DEFAULT_HEADERS=void 0;var Oa=ts();hi.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Oa.version}`}});var ns=ae(St=>{"use strict";var rs=St&&St.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(St,"__esModule",{value:!0});var Ra=rs(_r()),Ia=rs(ci()),Pa=is(),vr=class r{constructor(t,{headers:e={},schema:i,fetch:n}={}){this.url=t,this.headers=Object.assign(Object.assign({},Pa.DEFAULT_HEADERS),e),this.schemaName=i,this.fetch=n}from(t){let e=new URL(`${this.url}/${t}`);return new Ra.default(e,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new r(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,e={},{head:i=!1,get:n=!1,count:s}={}){let o,a=new URL(`${this.url}/rpc/${t}`),l;i||n?(o=i?"HEAD":"GET",Object.entries(e).filter(([c,h])=>h!==void 0).map(([c,h])=>[c,Array.isArray(h)?`{${h.join(",")}}`:`${h}`]).forEach(([c,h])=>{a.searchParams.append(c,h)})):(o="POST",l=e);let d=Object.assign({},this.headers);return s&&(d.Prefer=`count=${s}`),new Ia.default({method:o,url:a,headers:d,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};St.default=vr});var us=ae(k=>{"use strict";var Ke=k&&k.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(k,"__esModule",{value:!0});k.PostgrestError=k.PostgrestBuilder=k.PostgrestTransformBuilder=k.PostgrestFilterBuilder=k.PostgrestQueryBuilder=k.PostgrestClient=void 0;var ss=Ke(ns());k.PostgrestClient=ss.default;var os=Ke(_r());k.PostgrestQueryBuilder=os.default;var as=Ke(ci());k.PostgrestFilterBuilder=as.default;var ls=Ke(mr());k.PostgrestTransformBuilder=ls.default;var ds=Ke(hr());k.PostgrestBuilder=ds.default;var cs=Ke(cr());k.PostgrestError=cs.default;k.default={PostgrestClient:ss.default,PostgrestQueryBuilder:os.default,PostgrestFilterBuilder:as.default,PostgrestTransformBuilder:ls.default,PostgrestBuilder:ds.default,PostgrestError:cs.default}});var vn=(()=>{class r{_renderer;_elementRef;onChange=e=>{};onTouched=()=>{};constructor(e,i){this._renderer=e,this._elementRef=i}setProperty(e,i){this._renderer.setProperty(this._elementRef.nativeElement,e,i)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static \u0275fac=function(i){return new(i||r)(x(_e),x(W))};static \u0275dir=w({type:r})}return r})(),yn=(()=>{class r extends vn{static \u0275fac=(()=>{let e;return function(n){return(e||(e=Pe(r)))(n||r)}})();static \u0275dir=w({type:r,features:[O]})}return r})(),_t=new A("");var eo={provide:_t,useExisting:q(()=>bn),multi:!0};function to(){let r=Pi()?Pi().getUserAgent():"";return/android (\d+)/.test(r.toLowerCase())}var io=new A(""),bn=(()=>{class r extends vn{_compositionMode;_composing=!1;constructor(e,i,n){super(e,i),this._compositionMode=n,this._compositionMode==null&&(this._compositionMode=!to())}writeValue(e){let i=e??"";this.setProperty("value",i)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static \u0275fac=function(i){return new(i||r)(x(_e),x(W),x(io,8))};static \u0275dir=w({type:r,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(i,n){i&1&&ee("input",function(o){return n._handleInput(o.target.value)})("blur",function(){return n.onTouched()})("compositionstart",function(){return n._compositionStart()})("compositionend",function(o){return n._compositionEnd(o.target.value)})},standalone:!1,features:[S([eo]),O]})}return r})();function Bi(r){return r==null||zi(r)===0}function zi(r){return r==null?null:Array.isArray(r)||typeof r=="string"?r.length:r instanceof Set?r.size:null}var be=new A(""),vt=new A(""),ro=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,zt=class{static min(t){return xn(t)}static max(t){return no(t)}static required(t){return wn(t)}static requiredTrue(t){return so(t)}static email(t){return oo(t)}static minLength(t){return ao(t)}static maxLength(t){return Cn(t)}static pattern(t){return lo(t)}static nullValidator(t){return Ht()}static compose(t){return Tn(t)}static composeAsync(t){return Fn(t)}};function xn(r){return t=>{if(t.value==null||r==null)return null;let e=parseFloat(t.value);return!isNaN(e)&&e<r?{min:{min:r,actual:t.value}}:null}}function no(r){return t=>{if(t.value==null||r==null)return null;let e=parseFloat(t.value);return!isNaN(e)&&e>r?{max:{max:r,actual:t.value}}:null}}function wn(r){return Bi(r.value)?{required:!0}:null}function so(r){return r.value===!0?null:{required:!0}}function oo(r){return Bi(r.value)||ro.test(r.value)?null:{email:!0}}function ao(r){return t=>{let e=t.value?.length??zi(t.value);return e===null||e===0?null:e<r?{minlength:{requiredLength:r,actualLength:e}}:null}}function Cn(r){return t=>{let e=t.value?.length??zi(t.value);return e!==null&&e>r?{maxlength:{requiredLength:r,actualLength:e}}:null}}function lo(r){if(!r)return Ht;let t,e;return typeof r=="string"?(e="",r.charAt(0)!=="^"&&(e+="^"),e+=r,r.charAt(r.length-1)!=="$"&&(e+="$"),t=new RegExp(e)):(e=r.toString(),t=r),i=>{if(Bi(i.value))return null;let n=i.value;return t.test(n)?null:{pattern:{requiredPattern:e,actualValue:n}}}}function Ht(r){return null}function En(r){return r!=null}function An(r){return Wr(r)?Ir(r):r}function Sn(r){let t={};return r.forEach(e=>{t=e!=null?j(j({},t),e):t}),Object.keys(t).length===0?null:t}function kn(r,t){return t.map(e=>e(r))}function co(r){return!r.validate}function Dn(r){return r.map(t=>co(t)?t:e=>t.validate(e))}function Tn(r){if(!r)return null;let t=r.filter(En);return t.length==0?null:function(e){return Sn(kn(e,t))}}function Hi(r){return r!=null?Tn(Dn(r)):null}function Fn(r){if(!r)return null;let t=r.filter(En);return t.length==0?null:function(e){let i=kn(e,t).map(An);return Pr(i).pipe(Lt(Sn))}}function qi(r){return r!=null?Fn(Dn(r)):null}function un(r,t){return r===null?[t]:Array.isArray(r)?[...r,t]:[r,t]}function Mn(r){return r._rawValidators}function On(r){return r._rawAsyncValidators}function Ni(r){return r?Array.isArray(r)?r:[r]:[]}function qt(r,t){return Array.isArray(r)?r.includes(t):r===t}function hn(r,t){let e=Ni(t);return Ni(r).forEach(n=>{qt(e,n)||e.push(n)}),e}function fn(r,t){return Ni(t).filter(e=>!qt(r,e))}var Gt=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Hi(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=qi(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,e){return this.control?this.control.hasError(t,e):!1}getError(t,e){return this.control?this.control.getError(t,e):null}},he=class extends Gt{name;get formDirective(){return null}get path(){return null}},K=class extends Gt{_parent=null;name=null;valueAccessor=null},Wt=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},uo={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Rl=z(j({},uo),{"[class.ng-submitted]":"isSubmitted"}),Il=(()=>{class r extends Wt{constructor(e){super(e)}static \u0275fac=function(i){return new(i||r)(x(K,2))};static \u0275dir=w({type:r,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(i,n){i&2&&L("ng-untouched",n.isUntouched)("ng-touched",n.isTouched)("ng-pristine",n.isPristine)("ng-dirty",n.isDirty)("ng-valid",n.isValid)("ng-invalid",n.isInvalid)("ng-pending",n.isPending)},standalone:!1,features:[O]})}return r})(),Pl=(()=>{class r extends Wt{constructor(e){super(e)}static \u0275fac=function(i){return new(i||r)(x(he,10))};static \u0275dir=w({type:r,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(i,n){i&2&&L("ng-untouched",n.isUntouched)("ng-touched",n.isTouched)("ng-pristine",n.isPristine)("ng-dirty",n.isDirty)("ng-valid",n.isValid)("ng-invalid",n.isInvalid)("ng-pending",n.isPending)("ng-submitted",n.isSubmitted)},standalone:!1,features:[O]})}return r})();var ct="VALID",Bt="INVALID",He="PENDING",ut="DISABLED",ye=class{},Jt=class extends ye{value;source;constructor(t,e){super(),this.value=t,this.source=e}},ft=class extends ye{pristine;source;constructor(t,e){super(),this.pristine=t,this.source=e}},mt=class extends ye{touched;source;constructor(t,e){super(),this.touched=t,this.source=e}},qe=class extends ye{status;source;constructor(t,e){super(),this.status=t,this.source=e}},Kt=class extends ye{source;constructor(t){super(),this.source=t}},Qt=class extends ye{source;constructor(t){super(),this.source=t}};function Gi(r){return(ei(r)?r.validators:r)||null}function ho(r){return Array.isArray(r)?Hi(r):r||null}function Wi(r,t){return(ei(t)?t.asyncValidators:r)||null}function fo(r){return Array.isArray(r)?qi(r):r||null}function ei(r){return r!=null&&!Array.isArray(r)&&typeof r=="object"}function Rn(r,t,e){let i=r.controls;if(!(t?Object.keys(i):i).length)throw new $t(1e3,"");if(!i[e])throw new $t(1001,"")}function In(r,t,e){r._forEachChild((i,n)=>{if(e[n]===void 0)throw new $t(1002,"")})}var Ge=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,e){this._assignValidators(t),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return ce(this.statusReactive)}set status(t){ce(()=>this.statusReactive.set(t))}_status=ue(()=>this.statusReactive());statusReactive=Ve(void 0);get valid(){return this.status===ct}get invalid(){return this.status===Bt}get pending(){return this.status==He}get disabled(){return this.status===ut}get enabled(){return this.status!==ut}errors;get pristine(){return ce(this.pristineReactive)}set pristine(t){ce(()=>this.pristineReactive.set(t))}_pristine=ue(()=>this.pristineReactive());pristineReactive=Ve(!0);get dirty(){return!this.pristine}get touched(){return ce(this.touchedReactive)}set touched(t){ce(()=>this.touchedReactive.set(t))}_touched=ue(()=>this.touchedReactive());touchedReactive=Ve(!1);get untouched(){return!this.touched}_events=new H;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(hn(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(hn(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(fn(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(fn(t,this._rawAsyncValidators))}hasValidator(t){return qt(this._rawValidators,t)}hasAsyncValidator(t){return qt(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let e=this.touched===!1;this.touched=!0;let i=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(z(j({},t),{sourceControl:i})),e&&t.emitEvent!==!1&&this._events.next(new mt(!0,i))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(e=>e.markAllAsTouched(t))}markAsUntouched(t={}){let e=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let i=t.sourceControl??this;this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:i})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,i),e&&t.emitEvent!==!1&&this._events.next(new mt(!1,i))}markAsDirty(t={}){let e=this.pristine===!0;this.pristine=!1;let i=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(z(j({},t),{sourceControl:i})),e&&t.emitEvent!==!1&&this._events.next(new ft(!1,i))}markAsPristine(t={}){let e=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let i=t.sourceControl??this;this._forEachChild(n=>{n.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,i),e&&t.emitEvent!==!1&&this._events.next(new ft(!0,i))}markAsPending(t={}){this.status=He;let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new qe(this.status,e)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(z(j({},t),{sourceControl:e}))}disable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=ut,this.errors=null,this._forEachChild(n=>{n.disable(z(j({},t),{onlySelf:!0}))}),this._updateValue();let i=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Jt(this.value,i)),this._events.next(new qe(this.status,i)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(z(j({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(n=>n(!0))}enable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=ct,this._forEachChild(i=>{i.enable(z(j({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(z(j({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(i=>i(!1))}_updateAncestors(t,e){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},e),this._parent._updateTouched({},e))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let i=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===ct||this.status===He)&&this._runAsyncValidator(i,t.emitEvent)}let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Jt(this.value,e)),this._events.next(new qe(this.status,e)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(z(j({},t),{sourceControl:e}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ut:ct}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,e){if(this.asyncValidator){this.status=He,this._hasOwnPendingAsyncValidator={emitEvent:e!==!1};let i=An(this.asyncValidator(this));this._asyncValidationSubscription=i.subscribe(n=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(n,{emitEvent:e,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,e={}){this.errors=t,this._updateControlsErrors(e.emitEvent!==!1,this,e.shouldHaveEmitted)}get(t){let e=t;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((i,n)=>i&&i._find(n),this)}getError(t,e){let i=e?this.get(e):this;return i&&i.errors?i.errors[t]:null}hasError(t,e){return!!this.getError(t,e)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,e,i){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||i)&&this._events.next(new qe(this.status,e)),this._parent&&this._parent._updateControlsErrors(t,e,i)}_initObservables(){this.valueChanges=new le,this.statusChanges=new le}_calculateStatus(){return this._allControlsDisabled()?ut:this.errors?Bt:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(He)?He:this._anyControlsHaveStatus(Bt)?Bt:ct}_anyControlsHaveStatus(t){return this._anyControls(e=>e.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,e){let i=!this._anyControlsDirty(),n=this.pristine!==i;this.pristine=i,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,e),n&&this._events.next(new ft(this.pristine,e))}_updateTouched(t={},e){this.touched=this._anyControlsTouched(),this._events.next(new mt(this.touched,e)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,e)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){ei(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let e=this._parent&&this._parent.dirty;return!t&&!!e&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=ho(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=fo(this._rawAsyncValidators)}},We=class extends Ge{constructor(t,e,i){super(Gi(e),Wi(i,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(t,e){return this.controls[t]?this.controls[t]:(this.controls[t]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(t,e,i={}){this.registerControl(t,e),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}removeControl(t,e={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(t,e,i={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],e&&this.registerControl(t,e),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,e={}){In(this,!0,t),Object.keys(t).forEach(i=>{Rn(this,!0,i),this.controls[i].setValue(t[i],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){t!=null&&(Object.keys(t).forEach(i=>{let n=this.controls[i];n&&n.patchValue(t[i],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t={},e={}){this._forEachChild((i,n)=>{i.reset(t?t[n]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(t,e,i)=>(t[i]=e.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(e,i)=>i._syncPendingControls()?!0:e);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(e=>{let i=this.controls[e];i&&t(i,e)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[e,i]of Object.entries(this.controls))if(this.contains(e)&&t(i))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(e,i,n)=>((i.enabled||this.disabled)&&(e[n]=i.value),e))}_reduceChildren(t,e){let i=t;return this._forEachChild((n,s)=>{i=e(i,n,s)}),i}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var $i=class extends We{};var Je=new A("",{providedIn:"root",factory:()=>ti}),ti="always";function Pn(r,t){return[...t.path,r]}function gt(r,t,e=ti){Ji(r,t),t.valueAccessor.writeValue(r.value),(r.disabled||e==="always")&&t.valueAccessor.setDisabledState?.(r.disabled),po(r,t),_o(r,t),go(r,t),mo(r,t)}function Yt(r,t,e=!0){let i=()=>{};t.valueAccessor&&(t.valueAccessor.registerOnChange(i),t.valueAccessor.registerOnTouched(i)),Xt(r,t),r&&(t._invokeOnDestroyCallbacks(),r._registerOnCollectionChange(()=>{}))}function Zt(r,t){r.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(t)})}function mo(r,t){if(t.valueAccessor.setDisabledState){let e=i=>{t.valueAccessor.setDisabledState(i)};r.registerOnDisabledChange(e),t._registerOnDestroy(()=>{r._unregisterOnDisabledChange(e)})}}function Ji(r,t){let e=Mn(r);t.validator!==null?r.setValidators(un(e,t.validator)):typeof e=="function"&&r.setValidators([e]);let i=On(r);t.asyncValidator!==null?r.setAsyncValidators(un(i,t.asyncValidator)):typeof i=="function"&&r.setAsyncValidators([i]);let n=()=>r.updateValueAndValidity();Zt(t._rawValidators,n),Zt(t._rawAsyncValidators,n)}function Xt(r,t){let e=!1;if(r!==null){if(t.validator!==null){let n=Mn(r);if(Array.isArray(n)&&n.length>0){let s=n.filter(o=>o!==t.validator);s.length!==n.length&&(e=!0,r.setValidators(s))}}if(t.asyncValidator!==null){let n=On(r);if(Array.isArray(n)&&n.length>0){let s=n.filter(o=>o!==t.asyncValidator);s.length!==n.length&&(e=!0,r.setAsyncValidators(s))}}}let i=()=>{};return Zt(t._rawValidators,i),Zt(t._rawAsyncValidators,i),e}function po(r,t){t.valueAccessor.registerOnChange(e=>{r._pendingValue=e,r._pendingChange=!0,r._pendingDirty=!0,r.updateOn==="change"&&Vn(r,t)})}function go(r,t){t.valueAccessor.registerOnTouched(()=>{r._pendingTouched=!0,r.updateOn==="blur"&&r._pendingChange&&Vn(r,t),r.updateOn!=="submit"&&r.markAsTouched()})}function Vn(r,t){r._pendingDirty&&r.markAsDirty(),r.setValue(r._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1}function _o(r,t){let e=(i,n)=>{t.valueAccessor.writeValue(i),n&&t.viewToModelUpdate(i)};r.registerOnChange(e),t._registerOnDestroy(()=>{r._unregisterOnChange(e)})}function jn(r,t){r==null,Ji(r,t)}function vo(r,t){return Xt(r,t)}function Ki(r,t){if(!r.hasOwnProperty("model"))return!1;let e=r.model;return e.isFirstChange()?!0:!Object.is(t,e.currentValue)}function yo(r){return Object.getPrototypeOf(r.constructor)===yn}function Ln(r,t){r._syncPendingControls(),t.forEach(e=>{let i=e.control;i.updateOn==="submit"&&i._pendingChange&&(e.viewToModelUpdate(i._pendingValue),i._pendingChange=!1)})}function Qi(r,t){if(!t)return null;Array.isArray(t);let e,i,n;return t.forEach(s=>{s.constructor===bn?e=s:yo(s)?i=s:n=s}),n||i||e||null}function bo(r,t){let e=r.indexOf(t);e>-1&&r.splice(e,1)}var xo={provide:he,useExisting:q(()=>Yi)},ht=Promise.resolve(),Yi=(()=>{class r extends he{callSetDisabledState;get submitted(){return ce(this.submittedReactive)}_submitted=ue(()=>this.submittedReactive());submittedReactive=Ve(!1);_directives=new Set;form;ngSubmit=new le;options;constructor(e,i,n){super(),this.callSetDisabledState=n,this.form=new We({},Hi(e),qi(i))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){ht.then(()=>{let i=this._findContainer(e.path);e.control=i.registerControl(e.name,e.control),gt(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){ht.then(()=>{let i=this._findContainer(e.path);i&&i.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){ht.then(()=>{let i=this._findContainer(e.path),n=new We({});jn(n,e),i.registerControl(e.name,n),n.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){ht.then(()=>{let i=this._findContainer(e.path);i&&i.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,i){ht.then(()=>{this.form.get(e.path).setValue(i)})}setValue(e){this.control.setValue(e)}onSubmit(e){return this.submittedReactive.set(!0),Ln(this.form,this._directives),this.ngSubmit.emit(e),this.form._events.next(new Kt(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submittedReactive.set(!1),this.form._events.next(new Qt(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}static \u0275fac=function(i){return new(i||r)(x(be,10),x(vt,10),x(Je,8))};static \u0275dir=w({type:r,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(i,n){i&1&&ee("submit",function(o){return n.onSubmit(o)})("reset",function(){return n.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[S([xo]),O]})}return r})();function mn(r,t){let e=r.indexOf(t);e>-1&&r.splice(e,1)}function pn(r){return typeof r=="object"&&r!==null&&Object.keys(r).length===2&&"value"in r&&"disabled"in r}var pt=class extends Ge{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,e,i){super(Gi(e),Wi(i,e)),this._applyFormState(t),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),ei(e)&&(e.nonNullable||e.initialValueIsDefault)&&(pn(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,e={}){this.value=this._pendingValue=t,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(i=>i(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(t,e={}){this.setValue(t,e)}reset(t=this.defaultValue,e={}){this._applyFormState(t),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){mn(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){mn(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){pn(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var wo=r=>r instanceof pt;var Co={provide:K,useExisting:q(()=>Eo)},gn=Promise.resolve(),Eo=(()=>{class r extends K{_changeDetectorRef;callSetDisabledState;control=new pt;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new le;constructor(e,i,n,s,o,a){super(),this._changeDetectorRef=o,this.callSetDisabledState=a,this._parent=e,this._setValidators(i),this._setAsyncValidators(n),this.valueAccessor=Qi(this,s)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let i=e.name.previousValue;this.formDirective.removeControl({name:i,path:this._getPath(i)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),Ki(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){gt(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){gn.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){let i=e.isDisabled.currentValue,n=i!==0&&Ne(i);gn.then(()=>{n&&!this.control.disabled?this.control.disable():!n&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?Pn(e,this._parent):[e]}static \u0275fac=function(i){return new(i||r)(x(he,9),x(be,10),x(vt,10),x(_t,10),x(Ut,8),x(Je,8))};static \u0275dir=w({type:r,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[S([Co]),O,ge]})}return r})();var jl=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return r})(),Ao={provide:_t,useExisting:q(()=>So),multi:!0},So=(()=>{class r extends yn{writeValue(e){let i=e??"";this.setProperty("value",i)}registerOnChange(e){this.onChange=i=>{e(i==""?null:parseFloat(i))}}static \u0275fac=(()=>{let e;return function(n){return(e||(e=Pe(r)))(n||r)}})();static \u0275dir=w({type:r,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(i,n){i&1&&ee("input",function(o){return n.onChange(o.target.value)})("blur",function(){return n.onTouched()})},standalone:!1,features:[S([Ao]),O]})}return r})();var Zi=new A(""),ko={provide:K,useExisting:q(()=>Do)},Do=(()=>{class r extends K{_ngModelWarningConfig;callSetDisabledState;viewModel;form;set isDisabled(e){}model;update=new le;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(e,i,n,s,o){super(),this._ngModelWarningConfig=s,this.callSetDisabledState=o,this._setValidators(e),this._setAsyncValidators(i),this.valueAccessor=Qi(this,n)}ngOnChanges(e){if(this._isControlChanged(e)){let i=e.form.previousValue;i&&Yt(i,this,!1),gt(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}Ki(e,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&Yt(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_isControlChanged(e){return e.hasOwnProperty("form")}static \u0275fac=function(i){return new(i||r)(x(be,10),x(vt,10),x(_t,10),x(Zi,8),x(Je,8))};static \u0275dir=w({type:r,selectors:[["","formControl",""]],inputs:{form:[0,"formControl","form"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],standalone:!1,features:[S([ko]),O,ge]})}return r})(),To={provide:he,useExisting:q(()=>Xi)},Xi=(()=>{class r extends he{callSetDisabledState;get submitted(){return ce(this._submittedReactive)}set submitted(e){this._submittedReactive.set(e)}_submitted=ue(()=>this._submittedReactive());_submittedReactive=Ve(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new le;constructor(e,i,n){super(),this.callSetDisabledState=n,this._setValidators(e),this._setAsyncValidators(i)}ngOnChanges(e){e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Xt(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){let i=this.form.get(e.path);return gt(i,e,this.callSetDisabledState),i.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),i}getControl(e){return this.form.get(e.path)}removeControl(e){Yt(e.control||null,e,!1),bo(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,i){this.form.get(e.path).setValue(i)}onSubmit(e){return this._submittedReactive.set(!0),Ln(this.form,this.directives),this.ngSubmit.emit(e),this.form._events.next(new Kt(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this._submittedReactive.set(!1),this.form._events.next(new Qt(this.form))}_updateDomValue(){this.directives.forEach(e=>{let i=e.control,n=this.form.get(e.path);i!==n&&(Yt(i||null,e),wo(n)&&(gt(n,e,this.callSetDisabledState),e.control=n))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){let i=this.form.get(e.path);jn(i,e),i.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){let i=this.form.get(e.path);i&&vo(i,e)&&i.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Ji(this.form,this),this._oldForm&&Xt(this._oldForm,this)}static \u0275fac=function(i){return new(i||r)(x(be,10),x(vt,10),x(Je,8))};static \u0275dir=w({type:r,selectors:[["","formGroup",""]],hostBindings:function(i,n){i&1&&ee("submit",function(o){return n.onSubmit(o)})("reset",function(){return n.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[S([To]),O,ge]})}return r})();var Fo={provide:K,useExisting:q(()=>Mo)},Mo=(()=>{class r extends K{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(e){}model;update=new le;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(e,i,n,s,o){super(),this._ngModelWarningConfig=o,this._parent=e,this._setValidators(i),this._setAsyncValidators(n),this.valueAccessor=Qi(this,s)}ngOnChanges(e){this._added||this._setUpControl(),Ki(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return Pn(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(i){return new(i||r)(x(he,13),x(be,10),x(vt,10),x(_t,10),x(Zi,8))};static \u0275dir=w({type:r,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[S([Fo]),O,ge]})}return r})();function Oo(r){return typeof r=="number"?r:parseInt(r,10)}function Ro(r){return typeof r=="number"?r:parseFloat(r)}var er=(()=>{class r{_validator=Ht;_onChange;_enabled;ngOnChanges(e){if(this.inputName in e){let i=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(i),this._validator=this._enabled?this.createValidator(i):Ht,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,features:[ge]})}return r})();var Io={provide:be,useExisting:q(()=>Po),multi:!0},Po=(()=>{class r extends er{min;inputName="min";normalizeInput=e=>Ro(e);createValidator=e=>xn(e);static \u0275fac=(()=>{let e;return function(n){return(e||(e=Pe(r)))(n||r)}})();static \u0275dir=w({type:r,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(i,n){i&2&&de("min",n._enabled?n.min:null)},inputs:{min:"min"},standalone:!1,features:[S([Io]),O]})}return r})(),Vo={provide:be,useExisting:q(()=>jo),multi:!0};var jo=(()=>{class r extends er{required;inputName="required";normalizeInput=Ne;createValidator=e=>wn;enabled(e){return e}static \u0275fac=(()=>{let e;return function(n){return(e||(e=Pe(r)))(n||r)}})();static \u0275dir=w({type:r,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(i,n){i&2&&de("required",n._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[S([Vo]),O]})}return r})();var Lo={provide:be,useExisting:q(()=>No),multi:!0},No=(()=>{class r extends er{maxlength;inputName="maxlength";normalizeInput=e=>Oo(e);createValidator=e=>Cn(e);static \u0275fac=(()=>{let e;return function(n){return(e||(e=Pe(r)))(n||r)}})();static \u0275dir=w({type:r,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(i,n){i&2&&de("maxlength",n._enabled?n.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[S([Lo]),O]})}return r})();var Nn=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({})}return r})(),Ui=class extends Ge{constructor(t,e,i){super(Gi(e),Wi(i,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(t){return this.controls[this._adjustIndex(t)]}push(t,e={}){this.controls.push(t),this._registerControl(t),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(t,e,i={}){this.controls.splice(t,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:i.emitEvent})}removeAt(t,e={}){let i=this._adjustIndex(t);i<0&&(i=0),this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),this.controls.splice(i,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(t,e,i={}){let n=this._adjustIndex(t);n<0&&(n=0),this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),this.controls.splice(n,1),e&&(this.controls.splice(n,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:i.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(t,e={}){In(this,!1,t),t.forEach((i,n)=>{Rn(this,!1,n),this.at(n).setValue(i,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){t!=null&&(t.forEach((i,n)=>{this.at(n)&&this.at(n).patchValue(i,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t=[],e={}){this._forEachChild((i,n)=>{i.reset(t[n],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(t=>t.getRawValue())}clear(t={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:t.emitEvent}))}_adjustIndex(t){return t<0?t+this.length:t}_syncPendingControls(){let t=this.controls.reduce((e,i)=>i._syncPendingControls()?!0:e,!1);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){this.controls.forEach((e,i)=>{t(e,i)})}_updateValue(){this.value=this.controls.filter(t=>t.enabled||this.disabled).map(t=>t.value)}_anyControls(t){return this.controls.some(e=>e.enabled&&t(e))}_setUpControls(){this._forEachChild(t=>this._registerControl(t))}_allControlsDisabled(){for(let t of this.controls)if(t.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(t){t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)}_find(t){return this.at(t)??null}};function _n(r){return!!r&&(r.asyncValidators!==void 0||r.validators!==void 0||r.updateOn!==void 0)}var Ll=(()=>{class r{useNonNullable=!1;get nonNullable(){let e=new r;return e.useNonNullable=!0,e}group(e,i=null){let n=this._reduceControls(e),s={};return _n(i)?s=i:i!==null&&(s.validators=i.validator,s.asyncValidators=i.asyncValidator),new We(n,s)}record(e,i=null){let n=this._reduceControls(e);return new $i(n,i)}control(e,i,n){let s={};return this.useNonNullable?(_n(i)?s=i:(s.validators=i,s.asyncValidators=n),new pt(e,z(j({},s),{nonNullable:!0}))):new pt(e,i,n)}array(e,i,n){let s=e.map(o=>this._createControl(o));return new Ui(s,i,n)}_reduceControls(e){let i={};return Object.keys(e).forEach(n=>{i[n]=this._createControl(e[n])}),i}_createControl(e){if(e instanceof pt)return e;if(e instanceof Ge)return e;if(Array.isArray(e)){let i=e[0],n=e.length>1?e[1]:null,s=e.length>2?e[2]:null;return this.control(i,n,s)}else return this.control(e)}static \u0275fac=function(i){return new(i||r)};static \u0275prov=Y({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();var Nl=(()=>{class r{static withConfig(e){return{ngModule:r,providers:[{provide:Je,useValue:e.callSetDisabledState??ti}]}}static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({imports:[Nn]})}return r})(),$l=(()=>{class r{static withConfig(e){return{ngModule:r,providers:[{provide:Zi,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:Je,useValue:e.callSetDisabledState??ti}]}}static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({imports:[Nn]})}return r})();var tr=class{_box;_destroyed=new H;_resizeSubject=new H;_resizeObserver;_elementObservables=new Map;constructor(t){this._box=t,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(e=>this._resizeSubject.next(e)))}observe(t){return this._elementObservables.has(t)||this._elementObservables.set(t,new Mr(e=>{let i=this._resizeSubject.subscribe(e);return this._resizeObserver?.observe(t,{box:this._box}),()=>{this._resizeObserver?.unobserve(t),i.unsubscribe(),this._elementObservables.delete(t)}}).pipe(Nt(e=>e.some(i=>i.target===t)),Lr({bufferSize:1,refCount:!0}),at(this._destroyed))),this._elementObservables.get(t)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}},$n=(()=>{class r{_cleanupErrorListener;_observers=new Map;_ngZone=p(G);constructor(){typeof ResizeObserver<"u"}ngOnDestroy(){for(let[,e]of this._observers)e.destroy();this._observers.clear(),this._cleanupErrorListener?.()}observe(e,i){let n=i?.box||"content-box";return this._observers.has(n)||this._observers.set(n,new tr(n)),this._observers.get(n).observe(e)}static \u0275fac=function(i){return new(i||r)};static \u0275prov=Y({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();var $o=["notch"],Uo=["matFormFieldNotchedOutline",""],Bo=["*"],zo=["textField"],Ho=["iconPrefixContainer"],qo=["textPrefixContainer"],Go=["iconSuffixContainer"],Wo=["textSuffixContainer"],Jo=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],Ko=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];function Qo(r,t){r&1&&Ee(0,"span",20)}function Yo(r,t){if(r&1&&(P(0,"label",19),J(1,1),R(2,Qo,1,0,"span",20),N()),r&2){let e=ve(2);je("floating",e._shouldLabelFloat())("monitorResize",e._hasOutline())("id",e._labelId),de("for",e._control.disableAutomaticLabeling?null:e._control.id),T(2),I(!e.hideRequiredMarker&&e._control.required?2:-1)}}function Zo(r,t){if(r&1&&R(0,Yo,3,5,"label",19),r&2){let e=ve();I(e._hasFloatingLabel()?0:-1)}}function Xo(r,t){r&1&&Ee(0,"div",7)}function ea(r,t){}function ta(r,t){if(r&1&&R(0,ea,0,0,"ng-template",13),r&2){ve(2);let e=Ii(1);je("ngTemplateOutlet",e)}}function ia(r,t){if(r&1&&(P(0,"div",9),R(1,ta,1,1,null,13),N()),r&2){let e=ve();je("matFormFieldNotchedOutlineOpen",e._shouldLabelFloat()),T(),I(e._forceDisplayInfixLabel()?-1:1)}}function ra(r,t){r&1&&(P(0,"div",10,2),J(2,2),N())}function na(r,t){r&1&&(P(0,"div",11,3),J(2,3),N())}function sa(r,t){}function oa(r,t){if(r&1&&R(0,sa,0,0,"ng-template",13),r&2){ve();let e=Ii(1);je("ngTemplateOutlet",e)}}function aa(r,t){r&1&&(P(0,"div",14,4),J(2,4),N())}function la(r,t){r&1&&(P(0,"div",15,5),J(2,5),N())}function da(r,t){r&1&&Ee(0,"div",16)}function ca(r,t){r&1&&J(0,6)}function ua(r,t){if(r&1&&(P(0,"mat-hint",21),Yr(1),N()),r&2){let e=ve(2);je("id",e._hintLabelId),T(),Zr(e.hintLabel)}}function ha(r,t){if(r&1&&(R(0,ua,2,2,"mat-hint",21),J(1,7),Ee(2,"div",22),J(3,8)),r&2){let e=ve();I(e.hintLabel?0:-1)}}var ir=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["mat-label"]]})}return r})(),nr=new A("MatError"),fa=(()=>{class r{id=p(Ue).getId("mat-mdc-error-");constructor(){}static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["mat-error"],["","matError",""]],hostAttrs:[1,"mat-mdc-form-field-error","mat-mdc-form-field-bottom-align"],hostVars:1,hostBindings:function(i,n){i&2&&dt("id",n.id)},inputs:{id:"id"},features:[S([{provide:nr,useExisting:r}])]})}return r})(),rr=(()=>{class r{align="start";id=p(Ue).getId("mat-mdc-hint-");static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(i,n){i&2&&(dt("id",n.id),de("align",null),L("mat-mdc-form-field-hint-end",n.align==="end"))},inputs:{align:"align",id:"id"}})}return r})(),sr=new A("MatPrefix"),ma=(()=>{class r{set _isTextSelector(e){this._isText=!0}_isText=!1;static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["","matPrefix",""],["","matIconPrefix",""],["","matTextPrefix",""]],inputs:{_isTextSelector:[0,"matTextPrefix","_isTextSelector"]},features:[S([{provide:sr,useExisting:r}])]})}return r})(),or=new A("MatSuffix"),pa=(()=>{class r{set _isTextSelector(e){this._isText=!0}_isText=!1;static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["","matSuffix",""],["","matIconSuffix",""],["","matTextSuffix",""]],inputs:{_isTextSelector:[0,"matTextSuffix","_isTextSelector"]},features:[S([{provide:or,useExisting:r}])]})}return r})(),Wn=new A("FloatingLabelParent"),Un=(()=>{class r{_elementRef=p(W);get floating(){return this._floating}set floating(e){this._floating=e,this.monitorResize&&this._handleResize()}_floating=!1;get monitorResize(){return this._monitorResize}set monitorResize(e){this._monitorResize=e,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}_monitorResize=!1;_resizeObserver=p($n);_ngZone=p(G);_parent=p(Wn);_resizeSubscription=new Fr;constructor(){}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return ga(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(i,n){i&2&&L("mdc-floating-label--float-above",n.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"}})}return r})();function ga(r){let t=r;if(t.offsetParent!==null)return t.scrollWidth;let e=t.cloneNode(!0);e.style.setProperty("position","absolute"),e.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(e);let i=e.scrollWidth;return e.remove(),i}var Bn="mdc-line-ripple--active",ii="mdc-line-ripple--deactivating",zn=(()=>{class r{_elementRef=p(W);_cleanupTransitionEnd;constructor(){let e=p(G),i=p(_e);e.runOutsideAngular(()=>{this._cleanupTransitionEnd=i.listen(this._elementRef.nativeElement,"transitionend",this._handleTransitionEnd)})}activate(){let e=this._elementRef.nativeElement.classList;e.remove(ii),e.add(Bn)}deactivate(){this._elementRef.nativeElement.classList.add(ii)}_handleTransitionEnd=e=>{let i=this._elementRef.nativeElement.classList,n=i.contains(ii);e.propertyName==="opacity"&&n&&i.remove(Bn,ii)};ngOnDestroy(){this._cleanupTransitionEnd()}static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"]})}return r})(),Hn=(()=>{class r{_elementRef=p(W);_ngZone=p(G);open=!1;_notch;constructor(){}ngAfterViewInit(){let e=this._elementRef.nativeElement.querySelector(".mdc-floating-label");e?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),typeof requestAnimationFrame=="function"&&(e.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>e.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(e){!this.open||!e?this._notch.nativeElement.style.width="":this._notch.nativeElement.style.width=`calc(${e}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=lt({type:r,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(i,n){if(i&1&&te($o,5),i&2){let s;F(s=M())&&(n._notch=s.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(i,n){i&2&&L("mdc-notched-outline--notched",n.open)},inputs:{open:[0,"matFormFieldNotchedOutlineOpen","open"]},attrs:Uo,ngContentSelectors:Bo,decls:5,vars:0,consts:[["notch",""],[1,"mat-mdc-notch-piece","mdc-notched-outline__leading"],[1,"mat-mdc-notch-piece","mdc-notched-outline__notch"],[1,"mat-mdc-notch-piece","mdc-notched-outline__trailing"]],template:function(i,n){i&1&&(Ri(),Ee(0,"div",1),P(1,"div",2,0),J(3),N(),Ee(4,"div",3))},encapsulation:2,changeDetection:0})}return r})(),ri=(()=>{class r{value;stateChanges;id;placeholder;ngControl;focused;empty;shouldLabelFloat;required;disabled;errorState;controlType;autofilled;userAriaDescribedBy;disableAutomaticLabeling;static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r})}return r})();var ni=new A("MatFormField"),Jn=new A("MAT_FORM_FIELD_DEFAULT_OPTIONS"),qn="fill",_a="auto",Gn="fixed",va="translateY(-50%)",ya=(()=>{class r{_elementRef=p(W);_changeDetectorRef=p(Ut);_dir=p(an);_platform=p($e);_idGenerator=p(Ue);_ngZone=p(G);_injector=p(Oi);_defaults=p(Jn,{optional:!0});_textField;_iconPrefixContainer;_textPrefixContainer;_iconSuffixContainer;_textSuffixContainer;_floatingLabel;_notchedOutline;_lineRipple;_formFieldControl;_prefixChildren;_suffixChildren;_errorChildren;_hintChildren;_labelChild=Gr(ir);get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(e){this._hideRequiredMarker=Be(e)}_hideRequiredMarker=!1;color="primary";get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||_a}set floatLabel(e){e!==this._floatLabel&&(this._floatLabel=e,this._changeDetectorRef.markForCheck())}_floatLabel;get appearance(){return this._appearance}set appearance(e){let i=this._appearance,n=e||this._defaults?.appearance||qn;this._appearance=n,this._appearance==="outline"&&this._appearance!==i&&(this._needsOutlineLabelOffsetUpdate=!0)}_appearance=qn;get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||Gn}set subscriptSizing(e){this._subscriptSizing=e||this._defaults?.subscriptSizing||Gn}_subscriptSizing=null;get hintLabel(){return this._hintLabel}set hintLabel(e){this._hintLabel=e,this._processHints()}_hintLabel="";_hasIconPrefix=!1;_hasTextPrefix=!1;_hasIconSuffix=!1;_hasTextSuffix=!1;_labelId=this._idGenerator.getId("mat-mdc-form-field-label-");_hintLabelId=this._idGenerator.getId("mat-mdc-hint-");get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(e){this._explicitFormFieldControl=e}_destroyed=new H;_isFocused=null;_explicitFormFieldControl;_needsOutlineLabelOffsetUpdate=!1;_previousControl=null;_previousControlValidatorFn=null;_stateChanges;_valueChanges;_describedByChanges;_animationsDisabled;constructor(){let e=this._defaults;e&&(e.appearance&&(this.appearance=e.appearance),this._hideRequiredMarker=!!e?.hideRequiredMarker,e.color&&(this.color=e.color)),this._animationsDisabled=p(zr,{optional:!0})==="NoopAnimations"}ngAfterViewInit(){this._updateFocusState(),this._animationsDisabled||this._ngZone.runOutsideAngular(()=>{setTimeout(()=>{this._elementRef.nativeElement.classList.add("mat-form-field-animations-enabled")},300)}),this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl(),this._control!==this._previousControl&&(this._initializeControl(this._previousControl),this._control.ngControl&&this._control.ngControl.control&&(this._previousControlValidatorFn=this._control.ngControl.control.validator),this._previousControl=this._control),this._control.ngControl&&this._control.ngControl.control&&this._control.ngControl.control.validator!==this._previousControlValidatorFn&&this._changeDetectorRef.markForCheck()}ngOnDestroy(){this._stateChanges?.unsubscribe(),this._valueChanges?.unsubscribe(),this._describedByChanges?.unsubscribe(),this._destroyed.next(),this._destroyed.complete()}getLabelId=ue(()=>this._hasFloatingLabel()?this._labelId:null);getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(e){let i=this._control,n="mat-mdc-form-field-type-";e&&this._elementRef.nativeElement.classList.remove(n+e.controlType),i.controlType&&this._elementRef.nativeElement.classList.add(n+i.controlType),this._stateChanges?.unsubscribe(),this._stateChanges=i.stateChanges.subscribe(()=>{this._updateFocusState(),this._changeDetectorRef.markForCheck()}),this._describedByChanges?.unsubscribe(),this._describedByChanges=i.stateChanges.pipe(Nr([void 0,void 0]),Lt(()=>[i.errorState,i.userAriaDescribedBy]),jr(),Nt(([[s,o],[a,l]])=>s!==a||o!==l)).subscribe(()=>this._syncDescribedByIds()),this._valueChanges?.unsubscribe(),i.ngControl&&i.ngControl.valueChanges&&(this._valueChanges=i.ngControl.valueChanges.pipe(at(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck()))}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(e=>!e._isText),this._hasTextPrefix=!!this._prefixChildren.find(e=>e._isText),this._hasIconSuffix=!!this._suffixChildren.find(e=>!e._isText),this._hasTextSuffix=!!this._suffixChildren.find(e=>e._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),Vr(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){this._control}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||this._isFocused===null)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0),Hr(()=>{this._needsOutlineLabelOffsetUpdate&&(this._needsOutlineLabelOffsetUpdate=!1,this._updateOutlineLabelOffset())},{injector:this._injector}),this._dir.change.pipe(at(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdate=!0)}_shouldAlwaysFloat(){return this.floatLabel==="always"}_hasOutline(){return this.appearance==="outline"}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel=ue(()=>!!this._labelChild());_shouldLabelFloat(){return this._hasFloatingLabel()?this._control.shouldLabelFloat||this._shouldAlwaysFloat():!1}_shouldForward(e){let i=this._control?this._control.ngControl:null;return i&&i[e]}_getSubscriptMessageType(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){!this._hasOutline()||!this._floatingLabel||!this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(0):this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth())}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){this._hintChildren}_syncDescribedByIds(){if(this._control){let e=[];if(this._control.userAriaDescribedBy&&typeof this._control.userAriaDescribedBy=="string"&&e.push(...this._control.userAriaDescribedBy.split(" ")),this._getSubscriptMessageType()==="hint"){let i=this._hintChildren?this._hintChildren.find(s=>s.align==="start"):null,n=this._hintChildren?this._hintChildren.find(s=>s.align==="end"):null;i?e.push(i.id):this._hintLabel&&e.push(this._hintLabelId),n&&e.push(n.id)}else this._errorChildren&&e.push(...this._errorChildren.map(i=>i.id));this._control.setDescribedByIds(e)}}_updateOutlineLabelOffset(){if(!this._hasOutline()||!this._floatingLabel)return;let e=this._floatingLabel.element;if(!(this._iconPrefixContainer||this._textPrefixContainer)){e.style.transform="";return}if(!this._isAttachedToDom()){this._needsOutlineLabelOffsetUpdate=!0;return}let i=this._iconPrefixContainer?.nativeElement,n=this._textPrefixContainer?.nativeElement,s=this._iconSuffixContainer?.nativeElement,o=this._textSuffixContainer?.nativeElement,a=i?.getBoundingClientRect().width??0,l=n?.getBoundingClientRect().width??0,d=s?.getBoundingClientRect().width??0,c=o?.getBoundingClientRect().width??0,h=this._dir.value==="rtl"?"-1":"1",m=`${a+l}px`,b=`calc(${h} * (${m} + var(--mat-mdc-form-field-label-offset-x, 0px)))`;e.style.transform=`var(
        --mat-mdc-form-field-label-transform,
        ${va} translateX(${b})
    )`;let y=a+l+d+c;this._elementRef.nativeElement.style.setProperty("--mat-form-field-notch-max-width",`calc(100% - ${y}px)`)}_isAttachedToDom(){let e=this._elementRef.nativeElement;if(e.getRootNode){let i=e.getRootNode();return i&&i!==e}return document.documentElement.contains(e)}static \u0275fac=function(i){return new(i||r)};static \u0275cmp=lt({type:r,selectors:[["mat-form-field"]],contentQueries:function(i,n,s){if(i&1&&(Kr(s,n._labelChild,ir,5),Le(s,ri,5),Le(s,sr,5),Le(s,or,5),Le(s,nr,5),Le(s,rr,5)),i&2){Qr();let o;F(o=M())&&(n._formFieldControl=o.first),F(o=M())&&(n._prefixChildren=o),F(o=M())&&(n._suffixChildren=o),F(o=M())&&(n._errorChildren=o),F(o=M())&&(n._hintChildren=o)}},viewQuery:function(i,n){if(i&1&&(te(zo,5),te(Ho,5),te(qo,5),te(Go,5),te(Wo,5),te(Un,5),te(Hn,5),te(zn,5)),i&2){let s;F(s=M())&&(n._textField=s.first),F(s=M())&&(n._iconPrefixContainer=s.first),F(s=M())&&(n._textPrefixContainer=s.first),F(s=M())&&(n._iconSuffixContainer=s.first),F(s=M())&&(n._textSuffixContainer=s.first),F(s=M())&&(n._floatingLabel=s.first),F(s=M())&&(n._notchedOutline=s.first),F(s=M())&&(n._lineRipple=s.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:40,hostBindings:function(i,n){i&2&&L("mat-mdc-form-field-label-always-float",n._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",n._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",n._hasIconSuffix)("mat-form-field-invalid",n._control.errorState)("mat-form-field-disabled",n._control.disabled)("mat-form-field-autofilled",n._control.autofilled)("mat-form-field-appearance-fill",n.appearance=="fill")("mat-form-field-appearance-outline",n.appearance=="outline")("mat-form-field-hide-placeholder",n._hasFloatingLabel()&&!n._shouldLabelFloat())("mat-focused",n._control.focused)("mat-primary",n.color!=="accent"&&n.color!=="warn")("mat-accent",n.color==="accent")("mat-warn",n.color==="warn")("ng-untouched",n._shouldForward("untouched"))("ng-touched",n._shouldForward("touched"))("ng-pristine",n._shouldForward("pristine"))("ng-dirty",n._shouldForward("dirty"))("ng-valid",n._shouldForward("valid"))("ng-invalid",n._shouldForward("invalid"))("ng-pending",n._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],features:[S([{provide:ni,useExisting:r},{provide:Wn,useExisting:r}])],ngContentSelectors:Ko,decls:20,vars:25,consts:[["labelTemplate",""],["textField",""],["iconPrefixContainer",""],["textPrefixContainer",""],["textSuffixContainer",""],["iconSuffixContainer",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],[1,"mat-mdc-form-field-icon-prefix"],[1,"mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],["aria-atomic","true","aria-live","polite"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(i,n){if(i&1){let s=Jr();Ri(Jo),R(0,Zo,1,1,"ng-template",null,0,en),P(2,"div",6,1),ee("click",function(a){return $r(s),Ur(n._control.onContainerClick(a))}),R(4,Xo,1,0,"div",7),P(5,"div",8),R(6,ia,2,2,"div",9)(7,ra,3,0,"div",10)(8,na,3,0,"div",11),P(9,"div",12),R(10,oa,1,1,null,13),J(11),N(),R(12,aa,3,0,"div",14)(13,la,3,0,"div",15),N(),R(14,da,1,0,"div",16),N(),P(15,"div",17),Xr(16),P(17,"div",18),R(18,ca,1,0)(19,ha,4,1),N()()}if(i&2){let s;T(2),L("mdc-text-field--filled",!n._hasOutline())("mdc-text-field--outlined",n._hasOutline())("mdc-text-field--no-label",!n._hasFloatingLabel())("mdc-text-field--disabled",n._control.disabled)("mdc-text-field--invalid",n._control.errorState),T(2),I(!n._hasOutline()&&!n._control.disabled?4:-1),T(2),I(n._hasOutline()?6:-1),T(),I(n._hasIconPrefix?7:-1),T(),I(n._hasTextPrefix?8:-1),T(2),I(!n._hasOutline()||n._forceDisplayInfixLabel()?10:-1),T(2),I(n._hasTextSuffix?12:-1),T(),I(n._hasIconSuffix?13:-1),T(),I(n._hasOutline()?-1:14),T(),L("mat-mdc-form-field-subscript-dynamic-size",n.subscriptSizing==="dynamic");let o=n._getSubscriptMessageType();T(2),L("mat-mdc-form-field-error-wrapper",o==="error")("mat-mdc-form-field-hint-wrapper",o==="hint"),T(),I((s=o)==="error"?18:s==="hint"?19:-1)}},dependencies:[Un,Hn,rn,zn,rr],styles:[`.mdc-text-field{display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color;border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field__input{width:100%;min-width:0;border:none;border-radius:0;background:none;padding:0;-moz-appearance:none;-webkit-appearance:none;height:28px}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}.mdc-text-field__input::placeholder{opacity:0}.mdc-text-field__input::-moz-placeholder{opacity:0}.mdc-text-field__input::-webkit-input-placeholder{opacity:0}.mdc-text-field__input:-ms-input-placeholder{opacity:0}.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{opacity:1}.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-moz-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive::-webkit-input-placeholder{opacity:0}.mdc-text-field--disabled:not(.mdc-text-field--no-label) .mdc-text-field__input.mat-mdc-input-disabled-interactive:-ms-input-placeholder{opacity:0}.mdc-text-field--outlined .mdc-text-field__input,.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-filled-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color, var(--mat-sys-on-surface));caret-color:var(--mdc-outlined-text-field-caret-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-moz-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::-webkit-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}@media(forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}}.mdc-text-field--filled{height:56px;border-bottom-right-radius:0;border-bottom-left-radius:0;border-top-left-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small));border-top-right-radius:var(--mdc-filled-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color, var(--mat-sys-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 4%, transparent))}.mdc-text-field--outlined{height:56px;overflow:visible;padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)));padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px)}[dir=rtl] .mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)) + 4px);padding-left:max(16px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}.mdc-floating-label{position:absolute;left:0;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label{right:0;left:auto;transform-origin:right top;text-align:right}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:auto}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label{left:auto;right:4px}.mdc-text-field--filled .mdc-floating-label{left:16px;right:auto}[dir=rtl] .mdc-text-field--filled .mdc-floating-label{left:auto;right:16px}.mdc-text-field--disabled .mdc-floating-label{cursor:default}@media(forced-colors: active){.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-filled-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-filled-text-field-hover-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-filled-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-filled-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-filled-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-filled-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-filled-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-filled-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-filled-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label{color:var(--mdc-outlined-text-field-label-text-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-focus-label-text-color, var(--mat-sys-primary))}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-hover-label-text-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label{color:var(--mdc-outlined-text-field-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-floating-label{color:var(--mdc-outlined-text-field-error-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mdc-floating-label{color:var(--mdc-outlined-text-field-error-focus-label-text-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--disabled):hover .mdc-floating-label{color:var(--mdc-outlined-text-field-error-hover-label-text-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font, var(--mat-sys-body-large-font));font-size:var(--mdc-outlined-text-field-label-text-size, var(--mat-sys-body-large-size));font-weight:var(--mdc-outlined-text-field-label-text-weight, var(--mat-sys-body-large-weight));letter-spacing:var(--mdc-outlined-text-field-label-text-tracking, var(--mat-sys-body-large-tracking))}.mdc-floating-label--float-above{cursor:auto;transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1);font-size:.75rem}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline{text-align:right}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mat-mdc-notch-piece{box-sizing:border-box;height:100%;pointer-events:none;border-top:1px solid;border-bottom:1px solid}.mdc-text-field--focused .mat-mdc-notch-piece{border-width:2px}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-outline-color, var(--mat-sys-outline));border-width:var(--mdc-outlined-text-field-outline-width, 1px)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-hover-outline-color, var(--mat-sys-on-surface))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-focus-outline-color, var(--mat-sys-primary))}.mdc-text-field--outlined.mdc-text-field--disabled .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-notched-outline .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-hover-outline-color, var(--mat-sys-on-error-container))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--invalid.mdc-text-field--focused .mat-mdc-notch-piece{border-color:var(--mdc-outlined-text-field-error-focus-outline-color, var(--mat-sys-error))}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mat-mdc-notch-piece{border-width:var(--mdc-outlined-text-field-focus-outline-width, 2px)}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))}[dir=rtl] .mdc-notched-outline__leading{border-left:none;border-right:1px solid;border-bottom-left-radius:0;border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__trailing{flex-grow:1;border-left:none;border-right:1px solid;border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}[dir=rtl] .mdc-notched-outline__trailing{border-left:1px solid;border-right:none;border-top-right-radius:0;border-bottom-right-radius:0;border-top-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small));border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small))}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:min(var(--mat-form-field-notch-max-width, 100%),100% - max(12px,var(--mdc-outlined-text-field-container-shape, var(--mat-sys-corner-extra-small)))*2)}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none;--mat-form-field-notch-max-width: 100%}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1;border-bottom-width:var(--mdc-filled-text-field-active-indicator-height, 1px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color, var(--mat-sys-on-surface-variant))}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color, var(--mat-sys-on-surface))}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color, var(--mat-sys-error))}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--invalid:not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color, var(--mat-sys-on-error-container))}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height, 2px)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color, var(--mat-sys-primary))}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color, var(--mat-sys-error))}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-text-field--disabled{pointer-events:none}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height, 56px);padding-top:var(--mat-form-field-filled-with-label-container-padding-top, 24px);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom, 8px)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding, 16px);padding-bottom:var(--mat-form-field-container-vertical-padding, 16px)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height, 56px)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height, 56px) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}@keyframes _mat-form-field-subscript-animation{from{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px;opacity:1;transform:translateY(0);animation:_mat-form-field-subscript-animation 0ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color, var(--mat-sys-error))}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font, var(--mat-sys-body-small-font));line-height:var(--mat-form-field-subscript-text-line-height, var(--mat-sys-body-small-line-height));font-size:var(--mat-form-field-subscript-text-size, var(--mat-sys-body-small-size));letter-spacing:var(--mat-form-field-subscript-text-tracking, var(--mat-sys-body-small-tracking));font-weight:var(--mat-form-field-subscript-text-weight, var(--mat-sys-body-small-weight))}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color, var(--mat-sys-on-surface))}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity, 0)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color, var(--mat-sys-neutral10))}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color, color-mix(in srgb, var(--mat-sys-neutral10) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color, var(--mat-sys-on-surface-variant))}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color, var(--mat-sys-primary))}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}@media(forced-colors: active){.mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}}@media(forced-colors: active){.mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}}@media(forced-colors: active){.mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font, var(--mat-sys-body-large-font));line-height:var(--mat-form-field-container-text-line-height, var(--mat-sys-body-large-line-height));font-size:var(--mat-form-field-container-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-form-field-container-text-tracking, var(--mat-sys-body-large-tracking));font-weight:var(--mat-form-field-container-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color, var(--mat-sys-error))}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color, var(--mat-sys-on-error-container))}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color, var(--mat-sys-error))}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field-infix:has(textarea[cols]){width:auto}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input{transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-moz-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input::-webkit-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-moz-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-moz-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input::-webkit-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input::-webkit-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mat-mdc-form-field.mat-form-field-animations-enabled.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field.mat-form-field-animations-enabled .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field.mat-form-field-animations-enabled .mat-mdc-form-field-error-wrapper{animation-duration:300ms}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}
`],encapsulation:2,changeDetection:0})}return r})();var si=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({imports:[ze,on,ze]})}return r})();var ba=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275cmp=lt({type:r,selectors:[["ng-component"]],hostAttrs:["cdk-text-field-style-loader",""],decls:0,vars:0,template:function(i,n){},styles:[`textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}
`],encapsulation:2,changeDetection:0})}return r})(),xa={passive:!0},Kn=(()=>{class r{_platform=p($e);_ngZone=p(G);_renderer=p(qr).createRenderer(null,null);_styleLoader=p(sn);_monitoredElements=new Map;constructor(){}monitor(e){if(!this._platform.isBrowser)return Rr;this._styleLoader.load(ba);let i=Vi(e),n=this._monitoredElements.get(i);if(n)return n.subject;let s=new H,o="cdk-text-field-autofilled",a=d=>{d.animationName==="cdk-text-field-autofill-start"&&!i.classList.contains(o)?(i.classList.add(o),this._ngZone.run(()=>s.next({target:d.target,isAutofilled:!0}))):d.animationName==="cdk-text-field-autofill-end"&&i.classList.contains(o)&&(i.classList.remove(o),this._ngZone.run(()=>s.next({target:d.target,isAutofilled:!1})))},l=this._ngZone.runOutsideAngular(()=>(i.classList.add("cdk-text-field-autofill-monitored"),nn(this._renderer,i,"animationstart",a,xa)));return this._monitoredElements.set(i,{subject:s,unlisten:l}),s}stopMonitoring(e){let i=Vi(e),n=this._monitoredElements.get(i);n&&(n.unlisten(),n.subject.complete(),i.classList.remove("cdk-text-field-autofill-monitored"),i.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(i))}ngOnDestroy(){this._monitoredElements.forEach((e,i)=>this.stopMonitoring(i))}static \u0275fac=function(i){return new(i||r)};static \u0275prov=Y({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();var Qn=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({})}return r})();var Yn=new A("MAT_INPUT_VALUE_ACCESSOR");var Zn=(()=>{class r{isErrorState(e,i){return!!(e&&e.invalid&&(e.touched||i&&i.submitted))}static \u0275fac=function(i){return new(i||r)};static \u0275prov=Y({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();var oi=class{_defaultMatcher;ngControl;_parentFormGroup;_parentForm;_stateChanges;errorState=!1;matcher;constructor(t,e,i,n,s){this._defaultMatcher=t,this.ngControl=e,this._parentFormGroup=i,this._parentForm=n,this._stateChanges=s}updateErrorState(){let t=this.errorState,e=this._parentFormGroup||this._parentForm,i=this.matcher||this._defaultMatcher,n=this.ngControl?this.ngControl.control:null,s=i?.isErrorState(n,e)??!1;s!==t&&(this.errorState=s,this._stateChanges.next())}};var wa=["button","checkbox","file","hidden","image","radio","range","reset","submit"],Ca=new A("MAT_INPUT_CONFIG"),nc=(()=>{class r{_elementRef=p(W);_platform=p($e);ngControl=p(K,{optional:!0,self:!0});_autofillMonitor=p(Kn);_ngZone=p(G);_formField=p(ni,{optional:!0});_renderer=p(_e);_uid=p(Ue).getId("mat-input-");_previousNativeValue;_inputValueAccessor;_signalBasedValueAccessor;_previousPlaceholder;_errorStateTracker;_config=p(Ca,{optional:!0});_cleanupIosKeyup;_cleanupWebkitWheel;_formFieldDescribedBy;_isServer;_isNativeSelect;_isTextarea;_isInFormField;focused=!1;stateChanges=new H;controlType="mat-input";autofilled=!1;get disabled(){return this._disabled}set disabled(e){this._disabled=Be(e),this.focused&&(this.focused=!1,this.stateChanges.next())}_disabled=!1;get id(){return this._id}set id(e){this._id=e||this._uid}_id;placeholder;name;get required(){return this._required??this.ngControl?.control?.hasValidator(zt.required)??!1}set required(e){this._required=Be(e)}_required;get type(){return this._type}set type(e){let i=this._type;this._type=e||"text",this._validateType(),!this._isTextarea&&ji().has(this._type)&&(this._elementRef.nativeElement.type=this._type),this._type!==i&&this._ensureWheelDefaultBehavior()}_type="text";get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}userAriaDescribedBy;get value(){return this._signalBasedValueAccessor?this._signalBasedValueAccessor.value():this._inputValueAccessor.value}set value(e){e!==this.value&&(this._signalBasedValueAccessor?this._signalBasedValueAccessor.value.set(e):this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=Be(e)}_readonly=!1;disabledInteractive;get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}_neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(e=>ji().has(e));constructor(){let e=p(Yi,{optional:!0}),i=p(Xi,{optional:!0}),n=p(Zn),s=p(Yn,{optional:!0,self:!0}),o=this._elementRef.nativeElement,a=o.nodeName.toLowerCase();s?Br(s.value)?this._signalBasedValueAccessor=s:this._inputValueAccessor=s:this._inputValueAccessor=o,this._previousNativeValue=this.value,this.id=this.id,this._platform.IOS&&this._ngZone.runOutsideAngular(()=>{this._cleanupIosKeyup=this._renderer.listen(o,"keyup",this._iOSKeyupListener)}),this._errorStateTracker=new oi(n,this.ngControl,i,e,this.stateChanges),this._isServer=!this._platform.isBrowser,this._isNativeSelect=a==="select",this._isTextarea=a==="textarea",this._isInFormField=!!this._formField,this.disabledInteractive=this._config?.disabledInteractive||!1,this._isNativeSelect&&(this.controlType=o.multiple?"mat-native-select-multiple":"mat-native-select"),this._signalBasedValueAccessor&&tn(()=>{this._signalBasedValueAccessor.value(),this.stateChanges.next()})}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._cleanupIosKeyup?.(),this._cleanupWebkitWheel?.()}ngDoCheck(){this.ngControl&&(this.updateErrorState(),this.ngControl.disabled!==null&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}updateErrorState(){this._errorStateTracker.updateErrorState()}_focusChanged(e){if(e!==this.focused){if(!this._isNativeSelect&&e&&this.disabled&&this.disabledInteractive){let i=this._elementRef.nativeElement;i.type==="number"?(i.type="text",i.setSelectionRange(0,0),i.type="number"):i.setSelectionRange(0,0)}this.focused=e,this.stateChanges.next()}}_onInput(){}_dirtyCheckNativeValue(){let e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){let e=this._getPlaceholder();if(e!==this._previousPlaceholder){let i=this._elementRef.nativeElement;this._previousPlaceholder=e,e?i.setAttribute("placeholder",e):i.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){wa.indexOf(this._type)>-1}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!this._isNeverEmpty()&&!this._elementRef.nativeElement.value&&!this._isBadInput()&&!this.autofilled}get shouldLabelFloat(){if(this._isNativeSelect){let e=this._elementRef.nativeElement,i=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&i&&i.label)}else return this.focused&&!this.disabled||!this.empty}setDescribedByIds(e){let i=this._elementRef.nativeElement,n=i.getAttribute("aria-describedby"),s;if(n){let o=this._formFieldDescribedBy||e;s=e.concat(n.split(" ").filter(a=>a&&!o.includes(a)))}else s=e;this._formFieldDescribedBy=e,s.length?i.setAttribute("aria-describedby",s.join(" ")):i.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){let e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}_iOSKeyupListener=e=>{let i=e.target;!i.value&&i.selectionStart===0&&i.selectionEnd===0&&(i.setSelectionRange(1,1),i.setSelectionRange(0,0))};_webkitBlinkWheelListener=()=>{};_ensureWheelDefaultBehavior(){this._cleanupWebkitWheel?.(),this._type==="number"&&(this._platform.BLINK||this._platform.WEBKIT)&&(this._cleanupWebkitWheel=this._renderer.listen(this._elementRef.nativeElement,"wheel",this._webkitBlinkWheelListener))}_getReadonlyAttribute(){return this._isNativeSelect?null:this.readonly||this.disabled&&this.disabledInteractive?"true":null}static \u0275fac=function(i){return new(i||r)};static \u0275dir=w({type:r,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:21,hostBindings:function(i,n){i&1&&ee("focus",function(){return n._focusChanged(!0)})("blur",function(){return n._focusChanged(!1)})("input",function(){return n._onInput()}),i&2&&(dt("id",n.id)("disabled",n.disabled&&!n.disabledInteractive)("required",n.required),de("name",n.name||null)("readonly",n._getReadonlyAttribute())("aria-disabled",n.disabled&&n.disabledInteractive?"true":null)("aria-invalid",n.empty&&n.required?null:n.errorState)("aria-required",n.required)("id",n.id),L("mat-input-server",n._isServer)("mat-mdc-form-field-textarea-control",n._isInFormField&&n._isTextarea)("mat-mdc-form-field-input-control",n._isInFormField)("mat-mdc-input-disabled-interactive",n.disabledInteractive)("mdc-text-field__input",n._isInFormField)("mat-mdc-native-select-inline",n._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:[0,"aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly",disabledInteractive:[2,"disabledInteractive","disabledInteractive",Ne]},exportAs:["matInput"],features:[S([{provide:ri,useExisting:r}]),ge]})}return r})(),sc=(()=>{class r{static \u0275fac=function(i){return new(i||r)};static \u0275mod=X({type:r});static \u0275inj=Z({imports:[ze,si,si,Qn,ze]})}return r})();var Xn=r=>{let t;return r?t=r:typeof fetch>"u"?t=(...e)=>import("./chunk-NPJLHOA5.js").then(({default:i})=>i(...e)):t=fetch,(...e)=>t(...e)};var yt=class extends Error{constructor(t,e="FunctionsError",i){super(t),this.name=e,this.context=i}},ai=class extends yt{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}},li=class extends yt{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}},di=class extends yt{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}},ar=function(r){return r.Any="any",r.ApNortheast1="ap-northeast-1",r.ApNortheast2="ap-northeast-2",r.ApSouth1="ap-south-1",r.ApSoutheast1="ap-southeast-1",r.ApSoutheast2="ap-southeast-2",r.CaCentral1="ca-central-1",r.EuCentral1="eu-central-1",r.EuWest1="eu-west-1",r.EuWest2="eu-west-2",r.EuWest3="eu-west-3",r.SaEast1="sa-east-1",r.UsEast1="us-east-1",r.UsWest1="us-west-1",r.UsWest2="us-west-2",r}(ar||{});var Ea=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},bt=class{constructor(t,{headers:e={},customFetch:i,region:n=ar.Any}={}){this.url=t,this.headers=e,this.region=n,this.fetch=Xn(i)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,e={}){var i;return Ea(this,void 0,void 0,function*(){try{let{headers:n,method:s,body:o}=e,a={},{region:l}=e;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let d;o&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",d=o):typeof o=="string"?(a["Content-Type"]="text/plain",d=o):typeof FormData<"u"&&o instanceof FormData?d=o:(a["Content-Type"]="application/json",d=JSON.stringify(o)));let c=yield this.fetch(`${this.url}/${t}`,{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),n),body:d}).catch(b=>{throw new ai(b)}),h=c.headers.get("x-relay-error");if(h&&h==="true")throw new li(c);if(!c.ok)throw new di(c);let m=((i=c.headers.get("Content-Type"))!==null&&i!==void 0?i:"text/plain").split(";")[0].trim(),f;return m==="application/json"?f=yield c.json():m==="application/octet-stream"?f=yield c.blob():m==="text/event-stream"?f=c:m==="multipart/form-data"?f=yield c.formData():f=yield c.text(),{data:f,error:null}}catch(n){return{data:null,error:n}}})}};var hs=Ys(us(),1),{PostgrestClient:fs,PostgrestQueryBuilder:Ec,PostgrestFilterBuilder:Ac,PostgrestTransformBuilder:Sc,PostgrestBuilder:kc,PostgrestError:Va}=hs.default;var ms="2.11.2";var ps={"X-Client-Info":`realtime-js/${ms}`},gs="1.0.0",fi=1e4,_s=1e3,Qe=function(r){return r[r.connecting=0]="connecting",r[r.open=1]="open",r[r.closing=2]="closing",r[r.closed=3]="closed",r}(Qe||{}),$=function(r){return r.closed="closed",r.errored="errored",r.joined="joined",r.joining="joining",r.leaving="leaving",r}($||{}),U=function(r){return r.close="phx_close",r.error="phx_error",r.join="phx_join",r.reply="phx_reply",r.leave="phx_leave",r.access_token="access_token",r}(U||{}),yr=function(r){return r.websocket="websocket",r}(yr||{}),Se=function(r){return r.Connecting="connecting",r.Open="open",r.Closing="closing",r.Closed="closed",r}(Se||{});var mi=class{constructor(){this.HEADER_LENGTH=1}decode(t,e){return t.constructor===ArrayBuffer?e(this._binaryDecode(t)):e(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){let e=new DataView(t),i=new TextDecoder;return this._decodeBroadcast(t,e,i)}_decodeBroadcast(t,e,i){let n=e.getUint8(1),s=e.getUint8(2),o=this.HEADER_LENGTH+2,a=i.decode(t.slice(o,o+n));o=o+n;let l=i.decode(t.slice(o,o+s));o=o+s;let d=JSON.parse(i.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:d}}};var Ye=class{constructor(t,e){this.callback=t,this.timerCalc=e,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=e}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}};var C=function(r){return r.abstime="abstime",r.bool="bool",r.date="date",r.daterange="daterange",r.float4="float4",r.float8="float8",r.int2="int2",r.int4="int4",r.int4range="int4range",r.int8="int8",r.int8range="int8range",r.json="json",r.jsonb="jsonb",r.money="money",r.numeric="numeric",r.oid="oid",r.reltime="reltime",r.text="text",r.time="time",r.timestamp="timestamp",r.timestamptz="timestamptz",r.timetz="timetz",r.tsrange="tsrange",r.tstzrange="tstzrange",r}(C||{}),xr=(r,t,e={})=>{var i;let n=(i=e.skipTypes)!==null&&i!==void 0?i:[];return Object.keys(t).reduce((s,o)=>(s[o]=ja(o,r,t,n),s),{})},ja=(r,t,e,i)=>{let n=t.find(a=>a.name===r),s=n?.type,o=e[r];return s&&!i.includes(s)?vs(s,o):br(o)},vs=(r,t)=>{if(r.charAt(0)==="_"){let e=r.slice(1,r.length);return Ua(t,e)}switch(r){case C.bool:return La(t);case C.float4:case C.float8:case C.int2:case C.int4:case C.int8:case C.numeric:case C.oid:return Na(t);case C.json:case C.jsonb:return $a(t);case C.timestamp:return Ba(t);case C.abstime:case C.date:case C.daterange:case C.int4range:case C.int8range:case C.money:case C.reltime:case C.text:case C.time:case C.timestamptz:case C.timetz:case C.tsrange:case C.tstzrange:return br(t);default:return br(t)}},br=r=>r,La=r=>{switch(r){case"t":return!0;case"f":return!1;default:return r}},Na=r=>{if(typeof r=="string"){let t=parseFloat(r);if(!Number.isNaN(t))return t}return r},$a=r=>{if(typeof r=="string")try{return JSON.parse(r)}catch(t){return console.log(`JSON parse error: ${t}`),r}return r},Ua=(r,t)=>{if(typeof r!="string")return r;let e=r.length-1,i=r[e];if(r[0]==="{"&&i==="}"){let s,o=r.slice(1,e);try{s=JSON.parse("["+o+"]")}catch{s=o?o.split(","):[]}return s.map(a=>vs(t,a))}return r},Ba=r=>typeof r=="string"?r.replace(" ","T"):r,pi=r=>{let t=r;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};var Ze=class{constructor(t,e,i={},n=fi){this.channel=t,this.event=e,this.payload=i,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,e){var i;return this._hasReceived(t)&&e((i=this.receivedResp)===null||i===void 0?void 0:i.response),this.recHooks.push({status:t,callback:e}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);let t=e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,e){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:e})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:e}){this.recHooks.filter(i=>i.status===t).forEach(i=>i.callback(e))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}};var kt=class r{constructor(t,e){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let i=e?.events||{state:"presence_state",diff:"presence_diff"};this.channel._on(i.state,{},n=>{let{onJoin:s,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=r.syncState(this.state,n,s,o),this.pendingDiffs.forEach(l=>{this.state=r.syncDiff(this.state,l,s,o)}),this.pendingDiffs=[],a()}),this.channel._on(i.diff,{},n=>{let{onJoin:s,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(n):(this.state=r.syncDiff(this.state,n,s,o),a())}),this.onJoin((n,s,o)=>{this.channel._trigger("presence",{event:"join",key:n,currentPresences:s,newPresences:o})}),this.onLeave((n,s,o)=>{this.channel._trigger("presence",{event:"leave",key:n,currentPresences:s,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,e,i,n){let s=this.cloneDeep(t),o=this.transformState(e),a={},l={};return this.map(s,(d,c)=>{o[d]||(l[d]=c)}),this.map(o,(d,c)=>{let h=s[d];if(h){let m=c.map(v=>v.presence_ref),f=h.map(v=>v.presence_ref),b=c.filter(v=>f.indexOf(v.presence_ref)<0),y=h.filter(v=>m.indexOf(v.presence_ref)<0);b.length>0&&(a[d]=b),y.length>0&&(l[d]=y)}else a[d]=c}),this.syncDiff(s,{joins:a,leaves:l},i,n)}static syncDiff(t,e,i,n){let{joins:s,leaves:o}={joins:this.transformState(e.joins),leaves:this.transformState(e.leaves)};return i||(i=()=>{}),n||(n=()=>{}),this.map(s,(a,l)=>{var d;let c=(d=t[a])!==null&&d!==void 0?d:[];if(t[a]=this.cloneDeep(l),c.length>0){let h=t[a].map(f=>f.presence_ref),m=c.filter(f=>h.indexOf(f.presence_ref)<0);t[a].unshift(...m)}i(a,c,l)}),this.map(o,(a,l)=>{let d=t[a];if(!d)return;let c=l.map(h=>h.presence_ref);d=d.filter(h=>c.indexOf(h.presence_ref)<0),t[a]=d,n(a,d,l),d.length===0&&delete t[a]}),t}static map(t,e){return Object.getOwnPropertyNames(t).map(i=>e(i,t[i]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((e,i)=>{let n=t[i];return"metas"in n?e[i]=n.metas.map(s=>(s.presence_ref=s.phx_ref,delete s.phx_ref,delete s.phx_ref_prev,s)):e[i]=n,e},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}};var fe=function(r){return r.SUBSCRIBED="SUBSCRIBED",r.TIMED_OUT="TIMED_OUT",r.CLOSED="CLOSED",r.CHANNEL_ERROR="CHANNEL_ERROR",r}(fe||{});var Dt=class r{constructor(t,e={config:{}},i){this.topic=t,this.params=e,this.socket=i,this.bindings={},this.state=$.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},e.config),this.timeout=this.socket.timeout,this.joinPush=new Ze(this,U.join,this.params,this.timeout),this.rejoinTimer=new Ye(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=$.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(n=>n.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=$.closed,this.socket._remove(this)}),this._onError(n=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,n),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this._on(U.reply,{},(n,s)=>{this._trigger(this._replyEventName(s),n)}),this.presence=new kt(this),this.broadcastEndpointURL=pi(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,e=this.timeout){var i,n;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:s,presence:o,private:a}}=this.params;this._onError(c=>t?.(fe.CHANNEL_ERROR,c)),this._onClose(()=>t?.(fe.CLOSED));let l={},d={broadcast:s,presence:o,postgres_changes:(n=(i=this.bindings.postgres_changes)===null||i===void 0?void 0:i.map(c=>c.filter))!==null&&n!==void 0?n:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:d},l)),this.joinedOnce=!0,this._rejoin(e),this.joinPush.receive("ok",h=>u(this,[h],function*({postgres_changes:c}){var m;if(this.socket.setAuth(),c===void 0){t?.(fe.SUBSCRIBED);return}else{let f=this.bindings.postgres_changes,b=(m=f?.length)!==null&&m!==void 0?m:0,y=[];for(let v=0;v<b;v++){let D=f[v],{filter:{event:ne,schema:se,table:B,filter:oe}}=D,Ie=c&&c[v];if(Ie&&Ie.event===ne&&Ie.schema===se&&Ie.table===B&&Ie.filter===oe)y.push(Object.assign(Object.assign({},D),{id:Ie.id}));else{this.unsubscribe(),t?.(fe.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=y,t&&t(fe.SUBSCRIBED);return}})).receive("error",c=>{t?.(fe.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t?.(fe.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(i){return u(this,arguments,function*(t,e={}){return yield this.send({type:"presence",event:"track",payload:t},e.timeout||this.timeout)})}untrack(){return u(this,arguments,function*(t={}){return yield this.send({type:"presence",event:"untrack"},t)})}on(t,e,i){return this._on(t,e,i)}send(i){return u(this,arguments,function*(t,e={}){var n,s;if(!this._canPush()&&t.type==="broadcast"){let{event:o,payload:a}=t,d={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{let c=yield this._fetchWithTimeout(this.broadcastEndpointURL,d,(n=e.timeout)!==null&&n!==void 0?n:this.timeout);return yield(s=c.body)===null||s===void 0?void 0:s.cancel(),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,l,d;let c=this._push(t.type,t,e.timeout||this.timeout);t.type==="broadcast"&&!(!((d=(l=(a=this.params)===null||a===void 0?void 0:a.config)===null||l===void 0?void 0:l.broadcast)===null||d===void 0)&&d.ack)&&o("ok"),c.receive("ok",()=>o("ok")),c.receive("error",()=>o("error")),c.receive("timeout",()=>o("timed out"))})})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=$.leaving;let e=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(U.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(i=>{let n=new Ze(this,U.leave,{},t);n.receive("ok",()=>{e(),i("ok")}).receive("timeout",()=>{e(),i("timed out")}).receive("error",()=>{i("error")}),n.send(),this._canPush()||n.trigger("ok",{})})}_fetchWithTimeout(t,e,i){return u(this,null,function*(){let n=new AbortController,s=setTimeout(()=>n.abort(),i),o=yield this.socket.fetch(t,Object.assign(Object.assign({},e),{signal:n.signal}));return clearTimeout(s),o})}_push(t,e,i=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new Ze(this,t,e,i);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(t,e,i){return e}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,e,i){var n,s;let o=t.toLocaleLowerCase(),{close:a,error:l,leave:d,join:c}=U;if(i&&[a,l,d,c].indexOf(o)>=0&&i!==this._joinRef())return;let m=this._onMessage(o,e,i);if(e&&!m)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(n=this.bindings.postgres_changes)===null||n===void 0||n.filter(f=>{var b,y,v;return((b=f.filter)===null||b===void 0?void 0:b.event)==="*"||((v=(y=f.filter)===null||y===void 0?void 0:y.event)===null||v===void 0?void 0:v.toLocaleLowerCase())===o}).map(f=>f.callback(m,i)):(s=this.bindings[o])===null||s===void 0||s.filter(f=>{var b,y,v,D,ne,se;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in f){let B=f.id,oe=(b=f.filter)===null||b===void 0?void 0:b.event;return B&&((y=e.ids)===null||y===void 0?void 0:y.includes(B))&&(oe==="*"||oe?.toLocaleLowerCase()===((v=e.data)===null||v===void 0?void 0:v.type.toLocaleLowerCase()))}else{let B=(ne=(D=f?.filter)===null||D===void 0?void 0:D.event)===null||ne===void 0?void 0:ne.toLocaleLowerCase();return B==="*"||B===((se=e?.event)===null||se===void 0?void 0:se.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===o}).map(f=>{if(typeof m=="object"&&"ids"in m){let b=m.data,{schema:y,table:v,commit_timestamp:D,type:ne,errors:se}=b;m=Object.assign(Object.assign({},{schema:y,table:v,commit_timestamp:D,eventType:ne,new:{},old:{},errors:se}),this._getPayloadRecords(b))}f.callback(m,i)})}_isClosed(){return this.state===$.closed}_isJoined(){return this.state===$.joined}_isJoining(){return this.state===$.joining}_isLeaving(){return this.state===$.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,e,i){let n=t.toLocaleLowerCase(),s={type:n,filter:e,callback:i};return this.bindings[n]?this.bindings[n].push(s):this.bindings[n]=[s],this}_off(t,e){let i=t.toLocaleLowerCase();return this.bindings[i]=this.bindings[i].filter(n=>{var s;return!(((s=n.type)===null||s===void 0?void 0:s.toLocaleLowerCase())===i&&r.isEqual(n.filter,e))}),this}static isEqual(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(U.close,{},t)}_onError(t){this._on(U.error,{},e=>t(e))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=$.joining,this.joinPush.resend(t))}_getPayloadRecords(t){let e={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(e.new=xr(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(e.old=xr(t.columns,t.old_record)),e}};var Ha=()=>{},qa=typeof WebSocket<"u",Ga=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`,Tt=class{constructor(t,e){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=ps,this.params={},this.timeout=fi,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Ha,this.conn=null,this.sendBuffer=[],this.serializer=new mi,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=s=>{let o;return s?o=s:typeof fetch>"u"?o=(...a)=>import("./chunk-NPJLHOA5.js").then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${yr.websocket}`,this.httpEndpoint=pi(t),e?.transport?this.transport=e.transport:this.transport=null,e?.params&&(this.params=e.params),e?.headers&&(this.headers=Object.assign(Object.assign({},this.headers),e.headers)),e?.timeout&&(this.timeout=e.timeout),e?.logger&&(this.logger=e.logger),e?.heartbeatIntervalMs&&(this.heartbeatIntervalMs=e.heartbeatIntervalMs);let n=(i=e?.params)===null||i===void 0?void 0:i.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=e?.reconnectAfterMs?e.reconnectAfterMs:s=>[1e3,2e3,5e3,1e4][s-1]||1e4,this.encode=e?.encode?e.encode:(s,o)=>o(JSON.stringify(s)),this.decode=e?.decode?e.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Ye(()=>u(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(e?.fetch),e?.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=e?.worker||!1,this.workerUrl=e?.workerUrl}this.accessToken=e?.accessToken||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(qa){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new wr(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),import("./chunk-GFTZZNGD.js").then(({default:t})=>{this.conn=new t(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:gs}))}disconnect(t,e){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,e??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}removeChannel(t){return u(this,null,function*(){let e=yield t.unsubscribe();return this.channels.length===0&&this.disconnect(),e})}removeAllChannels(){return u(this,null,function*(){let t=yield Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),t})}log(t,e,i){this.logger(t,e,i)}connectionState(){switch(this.conn&&this.conn.readyState){case Qe.connecting:return Se.Connecting;case Qe.open:return Se.Open;case Qe.closing:return Se.Closing;default:return Se.Closed}}isConnected(){return this.connectionState()===Se.Open}channel(t,e={config:{}}){let i=new Dt(`realtime:${t}`,e,this);return this.channels.push(i),i}push(t){let{topic:e,event:i,payload:n,ref:s}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${e} ${i} (${s})`,n),this.isConnected()?o():this.sendBuffer.push(o)}setAuth(t=null){return u(this,null,function*(){let e=t||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;if(e){let i=null;try{i=JSON.parse(atob(e.split(".")[1]))}catch{}if(i&&i.exp&&!(Math.floor(Date.now()/1e3)-i.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${i.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${i.exp}`);this.accessTokenValue=e,this.channels.forEach(n=>{e&&n.updateJoinPayload({access_token:e}),n.joinedOnce&&n._isJoined()&&n._push(U.access_token,{access_token:e})})}})}sendHeartbeat(){return u(this,null,function*(){var t;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(t=this.conn)===null||t===void 0||t.close(_s,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}})}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let e=this.channels.find(i=>i.topic===t&&(i._isJoined()||i._isJoining()));e&&(this.log("transport",`leaving duplicate topic "${t}"`),e.unsubscribe())}_remove(t){this.channels=this.channels.filter(e=>e._joinRef()!==t._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,e=>{let{topic:i,event:n,payload:s,ref:o}=e;o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${i} ${n} ${o&&"("+o+")"||""}`,s),this.channels.filter(a=>a._isMember(i)).forEach(a=>a._trigger(n,s,o)),this.stateChangeCallbacks.message.forEach(a=>a(e))})}_onConnOpen(){return u(this,null,function*(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{e.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(t=>t())})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(e=>e(t))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(e=>e(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(U.error))}_appendParams(t,e){if(Object.keys(e).length===0)return t;let i=t.match(/\?/)?"&":"?",n=new URLSearchParams(e);return`${t}${i}${n}`}_workerObjectUrl(t){let e;if(t)e=t;else{let i=new Blob([Ga],{type:"application/javascript"});e=URL.createObjectURL(i)}return e}};var wr=class{constructor(t,e,i){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=Qe.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=i.close}};var Xe=class extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}};function E(r){return typeof r=="object"&&r!==null&&"__isStorageError"in r}var gi=class extends Xe{constructor(t,e){super(t),this.name="StorageApiError",this.status=e}toJSON(){return{name:this.name,message:this.message,status:this.status}}},ke=class extends Xe{constructor(t,e){super(t),this.name="StorageUnknownError",this.originalError=e}};var Wa=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},vi=r=>{let t;return r?t=r:typeof fetch>"u"?t=(...e)=>import("./chunk-NPJLHOA5.js").then(({default:i})=>i(...e)):t=fetch,(...e)=>t(...e)},ys=()=>Wa(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield import("./chunk-NPJLHOA5.js")).Response:Response}),_i=r=>{if(Array.isArray(r))return r.map(e=>_i(e));if(typeof r=="function"||r!==Object(r))return r;let t={};return Object.entries(r).forEach(([e,i])=>{let n=e.replace(/([-_][a-z])/gi,s=>s.toUpperCase().replace(/[-_]/g,""));t[n]=_i(i)}),t};var De=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},Cr=r=>r.msg||r.message||r.error_description||r.error||JSON.stringify(r),Ja=(r,t,e)=>De(void 0,void 0,void 0,function*(){let i=yield ys();r instanceof i&&!e?.noResolveJson?r.json().then(n=>{t(new gi(Cr(n),r.status||500))}).catch(n=>{t(new ke(Cr(n),n))}):t(new ke(Cr(r),r))}),Ka=(r,t,e,i)=>{let n={method:r,headers:t?.headers||{}};return r==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json"},t?.headers),i&&(n.body=JSON.stringify(i)),Object.assign(Object.assign({},n),e))};function Ft(r,t,e,i,n,s){return De(this,void 0,void 0,function*(){return new Promise((o,a)=>{r(e,Ka(t,i,n,s)).then(l=>{if(!l.ok)throw l;return i?.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>Ja(l,a,i))})})}function et(r,t,e,i){return De(this,void 0,void 0,function*(){return Ft(r,"GET",t,e,i)})}function ie(r,t,e,i,n){return De(this,void 0,void 0,function*(){return Ft(r,"POST",t,i,n,e)})}function bs(r,t,e,i,n){return De(this,void 0,void 0,function*(){return Ft(r,"PUT",t,i,n,e)})}function xs(r,t,e,i){return De(this,void 0,void 0,function*(){return Ft(r,"HEAD",t,Object.assign(Object.assign({},e),{noResolveJson:!0}),i)})}function yi(r,t,e,i,n){return De(this,void 0,void 0,function*(){return Ft(r,"DELETE",t,i,n,e)})}var V=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},Qa={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ws={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1},bi=class{constructor(t,e={},i,n){this.url=t,this.headers=e,this.bucketId=i,this.fetch=vi(n)}uploadOrUpdate(t,e,i,n){return V(this,void 0,void 0,function*(){try{let s,o=Object.assign(Object.assign({},ws),n),a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)}),l=o.metadata;typeof Blob<"u"&&i instanceof Blob?(s=new FormData,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l)),s.append("",i)):typeof FormData<"u"&&i instanceof FormData?(s=i,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l))):(s=i,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),n?.headers&&(a=Object.assign(Object.assign({},a),n.headers));let d=this._removeEmptyFolders(e),c=this._getFinalPath(d),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:s,headers:a},o?.duplex?{duplex:o.duplex}:{})),m=yield h.json();return h.ok?{data:{path:d,id:m.Id,fullPath:m.Key},error:null}:{data:null,error:m}}catch(s){if(E(s))return{data:null,error:s};throw s}})}upload(t,e,i){return V(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,e,i)})}uploadToSignedUrl(t,e,i,n){return V(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(t),o=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",e);try{let l,d=Object.assign({upsert:ws.upsert},n),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(d.upsert)});typeof Blob<"u"&&i instanceof Blob?(l=new FormData,l.append("cacheControl",d.cacheControl),l.append("",i)):typeof FormData<"u"&&i instanceof FormData?(l=i,l.append("cacheControl",d.cacheControl)):(l=i,c["cache-control"]=`max-age=${d.cacheControl}`,c["content-type"]=d.contentType);let h=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),m=yield h.json();return h.ok?{data:{path:s,fullPath:m.Key},error:null}:{data:null,error:m}}catch(l){if(E(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,e){return V(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),n=Object.assign({},this.headers);e?.upsert&&(n["x-upsert"]="true");let s=yield ie(this.fetch,`${this.url}/object/upload/sign/${i}`,{},{headers:n}),o=new URL(this.url+s.url),a=o.searchParams.get("token");if(!a)throw new Xe("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(i){if(E(i))return{data:null,error:i};throw i}})}update(t,e,i){return V(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,e,i)})}move(t,e,i){return V(this,void 0,void 0,function*(){try{return{data:yield ie(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:i?.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(E(n))return{data:null,error:n};throw n}})}copy(t,e,i){return V(this,void 0,void 0,function*(){try{return{data:{path:(yield ie(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:i?.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(E(n))return{data:null,error:n};throw n}})}createSignedUrl(t,e,i){return V(this,void 0,void 0,function*(){try{let n=this._getFinalPath(t),s=yield ie(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:e},i?.transform?{transform:i.transform}:{}),{headers:this.headers}),o=i?.download?`&download=${i.download===!0?"":i.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${o}`)},{data:s,error:null}}catch(n){if(E(n))return{data:null,error:n};throw n}})}createSignedUrls(t,e,i){return V(this,void 0,void 0,function*(){try{let n=yield ie(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:e,paths:t},{headers:this.headers}),s=i?.download?`&download=${i.download===!0?"":i.download}`:"";return{data:n.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${s}`):null})),error:null}}catch(n){if(E(n))return{data:null,error:n};throw n}})}download(t,e){return V(this,void 0,void 0,function*(){let n=typeof e?.transform<"u"?"render/image/authenticated":"object",s=this.transformOptsToQueryString(e?.transform||{}),o=s?`?${s}`:"";try{let a=this._getFinalPath(t);return{data:yield(yield et(this.fetch,`${this.url}/${n}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(E(a))return{data:null,error:a};throw a}})}info(t){return V(this,void 0,void 0,function*(){let e=this._getFinalPath(t);try{let i=yield et(this.fetch,`${this.url}/object/info/${e}`,{headers:this.headers});return{data:_i(i),error:null}}catch(i){if(E(i))return{data:null,error:i};throw i}})}exists(t){return V(this,void 0,void 0,function*(){let e=this._getFinalPath(t);try{return yield xs(this.fetch,`${this.url}/object/${e}`,{headers:this.headers}),{data:!0,error:null}}catch(i){if(E(i)&&i instanceof ke){let n=i.originalError;if([400,404].includes(n?.status))return{data:!1,error:i}}throw i}})}getPublicUrl(t,e){let i=this._getFinalPath(t),n=[],s=e?.download?`download=${e.download===!0?"":e.download}`:"";s!==""&&n.push(s);let a=typeof e?.transform<"u"?"render/image":"object",l=this.transformOptsToQueryString(e?.transform||{});l!==""&&n.push(l);let d=n.join("&");return d!==""&&(d=`?${d}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${i}${d}`)}}}remove(t){return V(this,void 0,void 0,function*(){try{return{data:yield yi(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(e){if(E(e))return{data:null,error:e};throw e}})}list(t,e,i){return V(this,void 0,void 0,function*(){try{let n=Object.assign(Object.assign(Object.assign({},Qa),e),{prefix:t||""});return{data:yield ie(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},i),error:null}}catch(n){if(E(n))return{data:null,error:n};throw n}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){let e=[];return t.width&&e.push(`width=${t.width}`),t.height&&e.push(`height=${t.height}`),t.resize&&e.push(`resize=${t.resize}`),t.format&&e.push(`format=${t.format}`),t.quality&&e.push(`quality=${t.quality}`),e.join("&")}};var Cs="2.7.1";var Es={"X-Client-Info":`storage-js/${Cs}`};var tt=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},xi=class{constructor(t,e={},i){this.url=t,this.headers=Object.assign(Object.assign({},Es),e),this.fetch=vi(i)}listBuckets(){return tt(this,void 0,void 0,function*(){try{return{data:yield et(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(E(t))return{data:null,error:t};throw t}})}getBucket(t){return tt(this,void 0,void 0,function*(){try{return{data:yield et(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(e){if(E(e))return{data:null,error:e};throw e}})}createBucket(t,e={public:!1}){return tt(this,void 0,void 0,function*(){try{return{data:yield ie(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(i){if(E(i))return{data:null,error:i};throw i}})}updateBucket(t,e){return tt(this,void 0,void 0,function*(){try{return{data:yield bs(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(i){if(E(i))return{data:null,error:i};throw i}})}emptyBucket(t){return tt(this,void 0,void 0,function*(){try{return{data:yield ie(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(E(e))return{data:null,error:e};throw e}})}deleteBucket(t){return tt(this,void 0,void 0,function*(){try{return{data:yield yi(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(e){if(E(e))return{data:null,error:e};throw e}})}};var Mt=class extends xi{constructor(t,e={},i){super(t,e,i)}from(t){return new bi(this.url,this.headers,t,this.fetch)}};var As="2.49.4";var Ot="";typeof Deno<"u"?Ot="deno":typeof document<"u"?Ot="web":typeof navigator<"u"&&navigator.product==="ReactNative"?Ot="react-native":Ot="node";var Ya={"X-Client-Info":`supabase-js-${Ot}/${As}`},Ss={headers:Ya},ks={schema:"public"},Ds={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Ts={};cn();var Za=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},Xa=r=>{let t;return r?t=r:typeof fetch>"u"?t=ln:t=fetch,(...e)=>t(...e)},el=()=>typeof Headers>"u"?dn:Headers,Fs=(r,t,e)=>{let i=Xa(e),n=el();return(s,o)=>Za(void 0,void 0,void 0,function*(){var a;let l=(a=yield t())!==null&&a!==void 0?a:r,d=new n(o?.headers);return d.has("apikey")||d.set("apikey",r),d.has("Authorization")||d.set("Authorization",`Bearer ${l}`),i(s,Object.assign(Object.assign({},o),{headers:d}))})};var tl=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})};function Ms(r){return r.replace(/\/$/,"")}function Os(r,t){let{db:e,auth:i,realtime:n,global:s}=r,{db:o,auth:a,realtime:l,global:d}=t,c={db:Object.assign(Object.assign({},o),e),auth:Object.assign(Object.assign({},a),i),realtime:Object.assign(Object.assign({},l),n),global:Object.assign(Object.assign({},d),s),accessToken:()=>tl(this,void 0,void 0,function*(){return""})};return r.accessToken?c.accessToken=r.accessToken:delete c.accessToken,c}function Rs(r){return Math.round(Date.now()/1e3)+r}function Is(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){let t=Math.random()*16|0;return(r=="x"?t:t&3|8).toString(16)})}var Q=()=>typeof document<"u",Te={tested:!1,writable:!1},xe=()=>{if(!Q())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(Te.tested)return Te.writable;let r=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(r,r),globalThis.localStorage.removeItem(r),Te.tested=!0,Te.writable=!0}catch{Te.tested=!0,Te.writable=!1}return Te.writable};function wi(r){let t={},e=new URL(r);if(e.hash&&e.hash[0]==="#")try{new URLSearchParams(e.hash.substring(1)).forEach((n,s)=>{t[s]=n})}catch{}return e.searchParams.forEach((i,n)=>{t[n]=i}),t}var Ci=r=>{let t;return r?t=r:typeof fetch>"u"?t=(...e)=>import("./chunk-NPJLHOA5.js").then(({default:i})=>i(...e)):t=fetch,(...e)=>t(...e)},Ps=r=>typeof r=="object"&&r!==null&&"status"in r&&"ok"in r&&"json"in r&&typeof r.json=="function",we=(r,t,e)=>u(void 0,null,function*(){yield r.setItem(t,JSON.stringify(e))}),It=(r,t)=>u(void 0,null,function*(){let e=yield r.getItem(t);if(!e)return null;try{return JSON.parse(e)}catch{return e}}),Ei=(r,t)=>u(void 0,null,function*(){yield r.removeItem(t)});function il(r){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",e="",i,n,s,o,a,l,d,c=0;for(r=r.replace("-","+").replace("_","/");c<r.length;)o=t.indexOf(r.charAt(c++)),a=t.indexOf(r.charAt(c++)),l=t.indexOf(r.charAt(c++)),d=t.indexOf(r.charAt(c++)),i=o<<2|a>>4,n=(a&15)<<4|l>>2,s=(l&3)<<6|d,e=e+String.fromCharCode(i),l!=64&&n!=0&&(e=e+String.fromCharCode(n)),d!=64&&s!=0&&(e=e+String.fromCharCode(s));return e}var Rt=class r{constructor(){this.promise=new r.promiseConstructor((t,e)=>{this.resolve=t,this.reject=e})}};Rt.promiseConstructor=Promise;function Er(r){let t=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,e=r.split(".");if(e.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!t.test(e[1]))throw new Error("JWT is not valid: payload is not in base64url format");let i=e[1];return JSON.parse(il(i))}function Vs(r){return u(this,null,function*(){return yield new Promise(t=>{setTimeout(()=>t(null),r)})})}function js(r,t){return new Promise((i,n)=>{u(this,null,function*(){for(let s=0;s<1/0;s++)try{let o=yield r(s);if(!t(s,null,o)){i(o);return}}catch(o){if(!t(s,o)){n(o);return}}})})}function rl(r){return("0"+r.toString(16)).substr(-2)}function Fe(){let t=new Uint32Array(56);if(typeof crypto>"u"){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",i=e.length,n="";for(let s=0;s<56;s++)n+=e.charAt(Math.floor(Math.random()*i));return n}return crypto.getRandomValues(t),Array.from(t,rl).join("")}function nl(r){return u(this,null,function*(){let e=new TextEncoder().encode(r),i=yield crypto.subtle.digest("SHA-256",e),n=new Uint8Array(i);return Array.from(n).map(s=>String.fromCharCode(s)).join("")})}function sl(r){return btoa(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function Me(r){return u(this,null,function*(){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),r;let e=yield nl(r);return sl(e)})}var Pt=class extends Error{constructor(t,e){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=e}};function g(r){return typeof r=="object"&&r!==null&&"__isAuthError"in r}var Ai=class extends Pt{constructor(t,e){super(t,e),this.name="AuthApiError",this.status=e}toJSON(){return{name:this.name,message:this.message,status:this.status}}};function Ls(r){return g(r)&&r.name==="AuthApiError"}var it=class extends Pt{constructor(t,e){super(t),this.name="AuthUnknownError",this.originalError=e}},me=class extends Pt{constructor(t,e,i){super(t),this.name=e,this.status=i}toJSON(){return{name:this.name,message:this.message,status:this.status}}},pe=class extends me{constructor(){super("Auth session missing!","AuthSessionMissingError",400)}},rt=class extends me{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500)}},Oe=class extends me{constructor(t){super(t,"AuthInvalidCredentialsError",400)}},Re=class extends me{constructor(t,e=null){super(t,"AuthImplicitGrantRedirectError",500),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}},Vt=class extends me{constructor(t,e=null){super(t,"AuthPKCEGrantCodeExchangeError",500),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}},nt=class extends me{constructor(t,e){super(t,"AuthRetryableFetchError",e)}};function ki(r){return g(r)&&r.name==="AuthRetryableFetchError"}var Si=class extends me{constructor(t,e,i){super(t,"AuthWeakPasswordError",e),this.reasons=i}};var ol=function(r,t){var e={};for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&t.indexOf(i)<0&&(e[i]=r[i]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(r);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(r,i[n])&&(e[i[n]]=r[i[n]]);return e},st=r=>r.msg||r.message||r.error_description||r.error||JSON.stringify(r),al=[502,503,504];function Ns(r){return u(this,null,function*(){if(!Ps(r))throw new nt(st(r),0);if(al.includes(r.status))throw new nt(st(r),r.status);let t;try{t=yield r.json()}catch(e){throw new it(st(e),e)}throw typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((e,i)=>e&&typeof i=="string",!0)?new Si(st(t),r.status,t.weak_password.reasons):new Ai(st(t),r.status||500)})}var ll=(r,t,e,i)=>{let n={method:r,headers:t?.headers||{}};return r==="GET"?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t?.headers),n.body=JSON.stringify(i),Object.assign(Object.assign({},n),e))};function _(r,t,e,i){return u(this,null,function*(){var n;let s=Object.assign({},i?.headers);i?.jwt&&(s.Authorization=`Bearer ${i.jwt}`);let o=(n=i?.query)!==null&&n!==void 0?n:{};i?.redirectTo&&(o.redirect_to=i.redirectTo);let a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=yield dl(r,t,e+a,{headers:s,noResolveJson:i?.noResolveJson},{},i?.body);return i?.xform?i?.xform(l):{data:Object.assign({},l),error:null}})}function dl(r,t,e,i,n,s){return u(this,null,function*(){let o=ll(t,i,n,s),a;try{a=yield r(e,o)}catch(l){throw console.error(l),new nt(st(l),0)}if(a.ok||(yield Ns(a)),i?.noResolveJson)return a;try{return yield a.json()}catch(l){yield Ns(l)}})}function Ce(r){var t;let e=null;cl(r)&&(e=Object.assign({},r),r.expires_at||(e.expires_at=Rs(r.expires_in)));let i=(t=r.user)!==null&&t!==void 0?t:r;return{data:{session:e,user:i},error:null}}function Ar(r){let t=Ce(r);return!t.error&&r.weak_password&&typeof r.weak_password=="object"&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.message&&typeof r.weak_password.message=="string"&&r.weak_password.reasons.reduce((e,i)=>e&&typeof i=="string",!0)&&(t.data.weak_password=r.weak_password),t}function re(r){var t;return{data:{user:(t=r.user)!==null&&t!==void 0?t:r},error:null}}function $s(r){return{data:r,error:null}}function Us(r){let{action_link:t,email_otp:e,hashed_token:i,redirect_to:n,verification_type:s}=r,o=ol(r,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:e,hashed_token:i,redirect_to:n,verification_type:s},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function Bs(r){return r}function cl(r){return r.access_token&&r.refresh_token&&r.expires_in}var ul=function(r,t){var e={};for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&t.indexOf(i)<0&&(e[i]=r[i]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(r);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(r,i[n])&&(e[i[n]]=r[i[n]]);return e},ot=class{constructor({url:t="",headers:e={},fetch:i}){this.url=t,this.headers=e,this.fetch=Ci(i),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(t,e="global"){return u(this,null,function*(){try{return yield _(this.fetch,"POST",`${this.url}/logout?scope=${e}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(i){if(g(i))return{data:null,error:i};throw i}})}inviteUserByEmail(i){return u(this,arguments,function*(t,e={}){try{return yield _(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:e.data},headers:this.headers,redirectTo:e.redirectTo,xform:re})}catch(n){if(g(n))return{data:{user:null},error:n};throw n}})}generateLink(t){return u(this,null,function*(){try{let{options:e}=t,i=ul(t,["options"]),n=Object.assign(Object.assign({},i),e);return"newEmail"in i&&(n.new_email=i?.newEmail,delete n.newEmail),yield _(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:Us,redirectTo:e?.redirectTo})}catch(e){if(g(e))return{data:{properties:null,user:null},error:e};throw e}})}createUser(t){return u(this,null,function*(){try{return yield _(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:re})}catch(e){if(g(e))return{data:{user:null},error:e};throw e}})}listUsers(t){return u(this,null,function*(){var e,i,n,s,o,a,l;try{let d={nextPage:null,lastPage:0,total:0},c=yield _(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(i=(e=t?.page)===null||e===void 0?void 0:e.toString())!==null&&i!==void 0?i:"",per_page:(s=(n=t?.perPage)===null||n===void 0?void 0:n.toString())!==null&&s!==void 0?s:""},xform:Bs});if(c.error)throw c.error;let h=yield c.json(),m=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,f=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(b=>{let y=parseInt(b.split(";")[0].split("=")[1].substring(0,1)),v=JSON.parse(b.split(";")[1].split("=")[1]);d[`${v}Page`]=y}),d.total=parseInt(m)),{data:Object.assign(Object.assign({},h),d),error:null}}catch(d){if(g(d))return{data:{users:[]},error:d};throw d}})}getUserById(t){return u(this,null,function*(){try{return yield _(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:re})}catch(e){if(g(e))return{data:{user:null},error:e};throw e}})}updateUserById(t,e){return u(this,null,function*(){try{return yield _(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:e,headers:this.headers,xform:re})}catch(i){if(g(i))return{data:{user:null},error:i};throw i}})}deleteUser(t,e=!1){return u(this,null,function*(){try{return yield _(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:e},xform:re})}catch(i){if(g(i))return{data:{user:null},error:i};throw i}})}_listFactors(t){return u(this,null,function*(){try{let{data:e,error:i}=yield _(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:n=>({data:{factors:n},error:null})});return{data:e,error:i}}catch(e){if(g(e))return{data:null,error:e};throw e}})}_deleteFactor(t){return u(this,null,function*(){try{return{data:yield _(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(e){if(g(e))return{data:null,error:e};throw e}})}};var Di="2.61.0";var zs="http://localhost:9999",Hs="supabase.auth.token";var qs={"X-Client-Info":`gotrue-js/${Di}`},Sr=10;var Gs={getItem:r=>xe()?globalThis.localStorage.getItem(r):null,setItem:(r,t)=>{xe()&&globalThis.localStorage.setItem(r,t)},removeItem:r=>{xe()&&globalThis.localStorage.removeItem(r)}};function kr(r={}){return{getItem:t=>r[t]||null,setItem:(t,e)=>{r[t]=e},removeItem:t=>{delete r[t]}}}function Ws(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}var hl={debug:!!(globalThis&&xe()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")},Ti=class extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}};Ws();var fl={url:zs,storageKey:Hs,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:qs,flowType:"implicit",debug:!1},jt=30*1e3,Js=3;function ml(r,t,e){return u(this,null,function*(){return yield e()})}var Dr=(()=>{class r{constructor(e){var i;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=r.nextInstanceID,r.nextInstanceID+=1,this.instanceID>0&&Q()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let n=Object.assign(Object.assign({},fl),e);if(this.logDebugMessages=!!n.debug,typeof n.debug=="function"&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new ot({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=Ci(n.fetch),this.lock=n.lock||ml,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:xe()?this.storage=Gs:(this.memoryStorage={},this.storage=kr(this.memoryStorage)):(this.memoryStorage={},this.storage=kr(this.memoryStorage)),Q()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}(i=this.broadcastChannel)===null||i===void 0||i.addEventListener("message",s=>u(this,null,function*(){this._debug("received broadcast notification from other tab or client",s),yield this._notifyAllSubscribers(s.data.event,s.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Di}) ${new Date().toISOString()}`,...e),this}initialize(){return u(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=u(this,null,function*(){return yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return u(this,null,function*(){try{let e=Q()?yield this._isPKCEFlow():!1;if(this._debug("#_initialize()","begin","is PKCE flow",e),e||this.detectSessionInUrl&&this._isImplicitGrantFlow()){let{data:i,error:n}=yield this._getSessionFromURL(e);if(n)return this._debug("#_initialize()","error detecting session from URL",n),n?.message==="Identity is already linked"||n?.message==="Identity is already linked to another user"?{error:n}:(yield this._removeSession(),{error:n});let{session:s,redirectType:o}=i;return this._debug("#_initialize()","detected session in URL",s,"redirect type",o),yield this._saveSession(s),setTimeout(()=>u(this,null,function*(){o==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",s):yield this._notifyAllSubscribers("SIGNED_IN",s)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(e){return g(e)?{error:e}:{error:new it("Unexpected error during initialization",e)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signUp(e){return u(this,null,function*(){var i,n,s;try{yield this._removeSession();let o;if("email"in e){let{email:h,password:m,options:f}=e,b=null,y=null;if(this.flowType==="pkce"){let v=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,v),b=yield Me(v),y=v===b?"plain":"s256"}o=yield _(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f?.emailRedirectTo,body:{email:h,password:m,data:(i=f?.data)!==null&&i!==void 0?i:{},gotrue_meta_security:{captcha_token:f?.captchaToken},code_challenge:b,code_challenge_method:y},xform:Ce})}else if("phone"in e){let{phone:h,password:m,options:f}=e;o=yield _(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:h,password:m,data:(n=f?.data)!==null&&n!==void 0?n:{},channel:(s=f?.channel)!==null&&s!==void 0?s:"sms",gotrue_meta_security:{captcha_token:f?.captchaToken}},xform:Ce})}else throw new Oe("You must provide either an email or phone number and a password");let{data:a,error:l}=o;if(l||!a)return{data:{user:null,session:null},error:l};let d=a.session,c=a.user;return a.session&&(yield this._saveSession(a.session),yield this._notifyAllSubscribers("SIGNED_IN",d)),{data:{user:c,session:d},error:null}}catch(o){if(g(o))return{data:{user:null,session:null},error:o};throw o}})}signInWithPassword(e){return u(this,null,function*(){try{yield this._removeSession();let i;if("email"in e){let{email:o,password:a,options:l}=e;i=yield _(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:o,password:a,gotrue_meta_security:{captcha_token:l?.captchaToken}},xform:Ar})}else if("phone"in e){let{phone:o,password:a,options:l}=e;i=yield _(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:o,password:a,gotrue_meta_security:{captcha_token:l?.captchaToken}},xform:Ar})}else throw new Oe("You must provide either an email or phone number and a password");let{data:n,error:s}=i;return s?{data:{user:null,session:null},error:s}:!n||!n.session||!n.user?{data:{user:null,session:null},error:new rt}:(n.session&&(yield this._saveSession(n.session),yield this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign({user:n.user,session:n.session},n.weak_password?{weakPassword:n.weak_password}:null),error:s})}catch(i){if(g(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithOAuth(e){return u(this,null,function*(){var i,n,s,o;return yield this._removeSession(),yield this._handleProviderSignIn(e.provider,{redirectTo:(i=e.options)===null||i===void 0?void 0:i.redirectTo,scopes:(n=e.options)===null||n===void 0?void 0:n.scopes,queryParams:(s=e.options)===null||s===void 0?void 0:s.queryParams,skipBrowserRedirect:(o=e.options)===null||o===void 0?void 0:o.skipBrowserRedirect})})}exchangeCodeForSession(e){return u(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>u(this,null,function*(){return this._exchangeCodeForSession(e)}))})}_exchangeCodeForSession(e){return u(this,null,function*(){let i=yield It(this.storage,`${this.storageKey}-code-verifier`),[n,s]=(i??"").split("/"),{data:o,error:a}=yield _(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:n},xform:Ce});return yield Ei(this.storage,`${this.storageKey}-code-verifier`),a?{data:{user:null,session:null,redirectType:null},error:a}:!o||!o.session||!o.user?{data:{user:null,session:null,redirectType:null},error:new rt}:(o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:Object.assign(Object.assign({},o),{redirectType:s??null}),error:a})})}signInWithIdToken(e){return u(this,null,function*(){yield this._removeSession();try{let{options:i,provider:n,token:s,access_token:o,nonce:a}=e,l=yield _(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:n,id_token:s,access_token:o,nonce:a,gotrue_meta_security:{captcha_token:i?.captchaToken}},xform:Ce}),{data:d,error:c}=l;return c?{data:{user:null,session:null},error:c}:!d||!d.session||!d.user?{data:{user:null,session:null},error:new rt}:(d.session&&(yield this._saveSession(d.session),yield this._notifyAllSubscribers("SIGNED_IN",d.session)),{data:d,error:c})}catch(i){if(g(i))return{data:{user:null,session:null},error:i};throw i}})}signInWithOtp(e){return u(this,null,function*(){var i,n,s,o,a;try{if(yield this._removeSession(),"email"in e){let{email:l,options:d}=e,c=null,h=null;if(this.flowType==="pkce"){let f=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,f),c=yield Me(f),h=f===c?"plain":"s256"}let{error:m}=yield _(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:l,data:(i=d?.data)!==null&&i!==void 0?i:{},create_user:(n=d?.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:d?.captchaToken},code_challenge:c,code_challenge_method:h},redirectTo:d?.emailRedirectTo});return{data:{user:null,session:null},error:m}}if("phone"in e){let{phone:l,options:d}=e,{data:c,error:h}=yield _(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:l,data:(s=d?.data)!==null&&s!==void 0?s:{},create_user:(o=d?.shouldCreateUser)!==null&&o!==void 0?o:!0,gotrue_meta_security:{captcha_token:d?.captchaToken},channel:(a=d?.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:c?.message_id},error:h}}throw new Oe("You must provide either an email or phone number.")}catch(l){if(g(l))return{data:{user:null,session:null},error:l};throw l}})}verifyOtp(e){return u(this,null,function*(){var i,n;try{e.type!=="email_change"&&e.type!=="phone_change"&&(yield this._removeSession());let s,o;"options"in e&&(s=(i=e.options)===null||i===void 0?void 0:i.redirectTo,o=(n=e.options)===null||n===void 0?void 0:n.captchaToken);let{data:a,error:l}=yield _(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:o}}),redirectTo:s,xform:Ce});if(l)throw l;if(!a)throw new Error("An error occurred on token verification.");let d=a.session,c=a.user;return d?.access_token&&(yield this._saveSession(d),yield this._notifyAllSubscribers("SIGNED_IN",d)),{data:{user:c,session:d},error:null}}catch(s){if(g(s))return{data:{user:null,session:null},error:s};throw s}})}signInWithSSO(e){return u(this,null,function*(){var i,n,s;try{yield this._removeSession();let o=null,a=null;if(this.flowType==="pkce"){let l=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,l),o=yield Me(l),a=l===o?"plain":"s256"}return yield _(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(n=(i=e.options)===null||i===void 0?void 0:i.redirectTo)!==null&&n!==void 0?n:void 0}),!((s=e?.options)===null||s===void 0)&&s.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:o,code_challenge_method:a}),headers:this.headers,xform:$s})}catch(o){if(g(o))return{data:null,error:o};throw o}})}reauthenticate(){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return u(this,null,function*(){try{return yield this._useSession(e=>u(this,null,function*(){let{data:{session:i},error:n}=e;if(n)throw n;if(!i)throw new pe;let{error:s}=yield _(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:i.access_token});return{data:{user:null,session:null},error:s}}))}catch(e){if(g(e))return{data:{user:null,session:null},error:e};throw e}})}resend(e){return u(this,null,function*(){try{e.type!="email_change"&&e.type!="phone_change"&&(yield this._removeSession());let i=`${this.url}/resend`;if("email"in e){let{email:n,type:s,options:o}=e,{error:a}=yield _(this.fetch,"POST",i,{headers:this.headers,body:{email:n,type:s,gotrue_meta_security:{captcha_token:o?.captchaToken}},redirectTo:o?.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in e){let{phone:n,type:s,options:o}=e,{data:a,error:l}=yield _(this.fetch,"POST",i,{headers:this.headers,body:{phone:n,type:s,gotrue_meta_security:{captcha_token:o?.captchaToken}}});return{data:{user:null,session:null,messageId:a?.message_id},error:l}}throw new Oe("You must provide either an email or phone number and a type")}catch(i){if(g(i))return{data:{user:null,session:null},error:i};throw i}})}getSession(){return u(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>u(this,null,function*(){return this._useSession(e=>u(this,null,function*(){return e}))}))})}_acquireLock(e,i){return u(this,null,function*(){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let n=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=u(this,null,function*(){return yield n,yield i()});return this.pendingInLock.push(u(this,null,function*(){try{yield s}catch{}})),s}return yield this.lock(`lock:${this.storageKey}`,e,()=>u(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let n=i();for(this.pendingInLock.push(u(this,null,function*(){try{yield n}catch{}})),yield n;this.pendingInLock.length;){let s=[...this.pendingInLock];yield Promise.all(s),this.pendingInLock.splice(0,s.length)}return yield n}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(e){return u(this,null,function*(){this._debug("#_useSession","begin");try{let i=yield this.__loadSession();return yield e(i)}finally{this._debug("#_useSession","end")}})}__loadSession(){return u(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null,i=yield It(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",i),i!==null&&(this._isValidSession(i)?e=i:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};let n=e.expires_at?e.expires_at<=Date.now()/1e3:!1;if(this._debug("#__loadSession()",`session has${n?"":" not"} expired`,"expires_at",e.expires_at),!n)return{data:{session:e},error:null};let{session:s,error:o}=yield this._callRefreshToken(e.refresh_token);return o?{data:{session:null},error:o}:{data:{session:s},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(e){return u(this,null,function*(){return e?yield this._getUser(e):(yield this.initializePromise,this._acquireLock(-1,()=>u(this,null,function*(){return yield this._getUser()})))})}_getUser(e){return u(this,null,function*(){try{return e?yield _(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:re}):yield this._useSession(i=>u(this,null,function*(){var n,s;let{data:o,error:a}=i;if(a)throw a;return yield _(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(s=(n=o.session)===null||n===void 0?void 0:n.access_token)!==null&&s!==void 0?s:void 0,xform:re})}))}catch(i){if(g(i))return{data:{user:null},error:i};throw i}})}updateUser(n){return u(this,arguments,function*(e,i={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._updateUser(e,i)}))})}_updateUser(n){return u(this,arguments,function*(e,i={}){try{return yield this._useSession(s=>u(this,null,function*(){let{data:o,error:a}=s;if(a)throw a;if(!o.session)throw new pe;let l=o.session,d=null,c=null;if(this.flowType==="pkce"&&e.email!=null){let f=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,f),d=yield Me(f),c=f===d?"plain":"s256"}let{data:h,error:m}=yield _(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:i?.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:d,code_challenge_method:c}),jwt:l.access_token,xform:re});if(m)throw m;return l.user=h.user,yield this._saveSession(l),yield this._notifyAllSubscribers("USER_UPDATED",l),{data:{user:l.user},error:null}}))}catch(s){if(g(s))return{data:{user:null},error:s};throw s}})}_decodeJWT(e){return Er(e)}setSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._setSession(e)}))})}_setSession(e){return u(this,null,function*(){try{if(!e.access_token||!e.refresh_token)throw new pe;let i=Date.now()/1e3,n=i,s=!0,o=null,a=Er(e.access_token);if(a.exp&&(n=a.exp,s=n<=i),s){let{session:l,error:d}=yield this._callRefreshToken(e.refresh_token);if(d)return{data:{user:null,session:null},error:d};if(!l)return{data:{user:null,session:null},error:null};o=l}else{let{data:l,error:d}=yield this._getUser(e.access_token);if(d)throw d;o={access_token:e.access_token,refresh_token:e.refresh_token,user:l.user,token_type:"bearer",expires_in:n-i,expires_at:n},yield this._saveSession(o),yield this._notifyAllSubscribers("SIGNED_IN",o)}return{data:{user:o.user,session:o},error:null}}catch(i){if(g(i))return{data:{session:null,user:null},error:i};throw i}})}refreshSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._refreshSession(e)}))})}_refreshSession(e){return u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n;if(!e){let{data:a,error:l}=i;if(l)throw l;e=(n=a.session)!==null&&n!==void 0?n:void 0}if(!e?.refresh_token)throw new pe;let{session:s,error:o}=yield this._callRefreshToken(e.refresh_token);return o?{data:{user:null,session:null},error:o}:s?{data:{user:s.user,session:s},error:null}:{data:{user:null,session:null},error:null}}))}catch(i){if(g(i))return{data:{user:null,session:null},error:i};throw i}})}_getSessionFromURL(e){return u(this,null,function*(){try{if(!Q())throw new Re("No browser detected.");if(this.flowType==="implicit"&&!this._isImplicitGrantFlow())throw new Re("Not a valid implicit grant flow url.");if(this.flowType=="pkce"&&!e)throw new Vt("Not a valid PKCE flow url.");let i=wi(window.location.href);if(e){if(!i.code)throw new Vt("No code detected.");let{data:se,error:B}=yield this._exchangeCodeForSession(i.code);if(B)throw B;let oe=new URL(window.location.href);return oe.searchParams.delete("code"),window.history.replaceState(window.history.state,"",oe.toString()),{data:{session:se.session,redirectType:null},error:null}}if(i.error||i.error_description||i.error_code)throw new Re(i.error_description||"Error in URL with unspecified error_description",{error:i.error||"unspecified_error",code:i.error_code||"unspecified_code"});let{provider_token:n,provider_refresh_token:s,access_token:o,refresh_token:a,expires_in:l,expires_at:d,token_type:c}=i;if(!o||!l||!a||!c)throw new Re("No session defined in URL");let h=Math.round(Date.now()/1e3),m=parseInt(l),f=h+m;d&&(f=parseInt(d));let b=f-h;b*1e3<=jt&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${b}s, should have been closer to ${m}s`);let y=f-m;h-y>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",y,f,h):h-y<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clok for skew",y,f,h);let{data:v,error:D}=yield this._getUser(o);if(D)throw D;let ne={provider_token:n,provider_refresh_token:s,access_token:o,expires_in:m,expires_at:f,refresh_token:a,token_type:c,user:v.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:ne,redirectType:i.type},error:null}}catch(i){if(g(i))return{data:{session:null,redirectType:null},error:i};throw i}})}_isImplicitGrantFlow(){let e=wi(window.location.href);return!!(Q()&&(e.access_token||e.error_description))}_isPKCEFlow(){return u(this,null,function*(){let e=wi(window.location.href),i=yield It(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&i)})}signOut(){return u(this,arguments,function*(e={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._signOut(e)}))})}_signOut(){return u(this,arguments,function*({scope:e}={scope:"global"}){return yield this._useSession(i=>u(this,null,function*(){var n;let{data:s,error:o}=i;if(o)return{error:o};let a=(n=s.session)===null||n===void 0?void 0:n.access_token;if(a){let{error:l}=yield this.admin.signOut(a,e);if(l&&!(Ls(l)&&(l.status===404||l.status===401)))return{error:l}}return e!=="others"&&(yield this._removeSession(),yield Ei(this.storage,`${this.storageKey}-code-verifier`),yield this._notifyAllSubscribers("SIGNED_OUT",null)),{error:null}}))})}onAuthStateChange(e){let i=Is(),n={id:i,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",i),this.stateChangeEmitters.delete(i)}};return this._debug("#onAuthStateChange()","registered callback with id",i),this.stateChangeEmitters.set(i,n),u(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){this._emitInitialSession(i)}))}),{data:{subscription:n}}}_emitInitialSession(e){return u(this,null,function*(){return yield this._useSession(i=>u(this,null,function*(){var n,s;try{let{data:{session:o},error:a}=i;if(a)throw a;yield(n=this.stateChangeEmitters.get(e))===null||n===void 0?void 0:n.callback("INITIAL_SESSION",o),this._debug("INITIAL_SESSION","callback id",e,"session",o)}catch(o){yield(s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",e,"error",o),console.error(o)}}))})}resetPasswordForEmail(n){return u(this,arguments,function*(e,i={}){let s=null,o=null;if(this.flowType==="pkce"){let a=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,`${a}/PASSWORD_RECOVERY`),s=yield Me(a),o=a===s?"plain":"s256"}try{return yield _(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:o,gotrue_meta_security:{captcha_token:i.captchaToken}},headers:this.headers,redirectTo:i.redirectTo})}catch(a){if(g(a))return{data:null,error:a};throw a}})}getUserIdentities(){return u(this,null,function*(){var e;try{let{data:i,error:n}=yield this.getUser();if(n)throw n;return{data:{identities:(e=i.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(i){if(g(i))return{data:null,error:i};throw i}})}linkIdentity(e){return u(this,null,function*(){var i;try{let{data:n,error:s}=yield this._useSession(o=>u(this,null,function*(){var a,l,d,c,h;let{data:m,error:f}=o;if(f)throw f;let b=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(a=e.options)===null||a===void 0?void 0:a.redirectTo,scopes:(l=e.options)===null||l===void 0?void 0:l.scopes,queryParams:(d=e.options)===null||d===void 0?void 0:d.queryParams,skipBrowserRedirect:!0});return yield _(this.fetch,"GET",b,{headers:this.headers,jwt:(h=(c=m.session)===null||c===void 0?void 0:c.access_token)!==null&&h!==void 0?h:void 0})}));if(s)throw s;return Q()&&!(!((i=e.options)===null||i===void 0)&&i.skipBrowserRedirect)&&window.location.assign(n?.url),{data:{provider:e.provider,url:n?.url},error:null}}catch(n){if(g(n))return{data:{provider:e.provider,url:null},error:n};throw n}})}unlinkIdentity(e){return u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n,s;let{data:o,error:a}=i;if(a)throw a;return yield _(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(s=(n=o.session)===null||n===void 0?void 0:n.access_token)!==null&&s!==void 0?s:void 0})}))}catch(i){if(g(i))return{data:null,error:i};throw i}})}_refreshAccessToken(e){return u(this,null,function*(){let i=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{let n=Date.now();return yield js(s=>u(this,null,function*(){return yield Vs(s*200),this._debug(i,"refreshing attempt",s),yield _(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:Ce})}),(s,o,a)=>a&&a.error&&ki(a.error)&&Date.now()+(s+1)*200-n<jt)}catch(n){if(this._debug(i,"error",n),g(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(i,"end")}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,i){return u(this,null,function*(){let n=yield this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:i.redirectTo,scopes:i.scopes,queryParams:i.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",i,"url",n),Q()&&!i.skipBrowserRedirect&&window.location.assign(n),{data:{provider:e,url:n},error:null}})}_recoverAndRefresh(){return u(this,null,function*(){var e;let i="#_recoverAndRefresh()";this._debug(i,"begin");try{let n=yield It(this.storage,this.storageKey);if(this._debug(i,"session from storage",n),!this._isValidSession(n)){this._debug(i,"session is not valid"),n!==null&&(yield this._removeSession());return}let s=Math.round(Date.now()/1e3),o=((e=n.expires_at)!==null&&e!==void 0?e:1/0)<s+Sr;if(this._debug(i,`session has${o?"":" not"} expired with margin of ${Sr}s`),o){if(this.autoRefreshToken&&n.refresh_token){let{error:a}=yield this._callRefreshToken(n.refresh_token);a&&(console.error(a),ki(a)||(this._debug(i,"refresh failed with a non-retryable error, removing the session",a),yield this._removeSession()))}}else yield this._notifyAllSubscribers("SIGNED_IN",n)}catch(n){this._debug(i,"error",n),console.error(n);return}finally{this._debug(i,"end")}})}_callRefreshToken(e){return u(this,null,function*(){var i,n;if(!e)throw new pe;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let s=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(s,"begin");try{this.refreshingDeferred=new Rt;let{data:o,error:a}=yield this._refreshAccessToken(e);if(a)throw a;if(!o.session)throw new pe;yield this._saveSession(o.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",o.session);let l={session:o.session,error:null};return this.refreshingDeferred.resolve(l),l}catch(o){if(this._debug(s,"error",o),g(o)){let a={session:null,error:o};return ki(o)||(yield this._removeSession(),yield this._notifyAllSubscribers("SIGNED_OUT",null)),(i=this.refreshingDeferred)===null||i===void 0||i.resolve(a),a}throw(n=this.refreshingDeferred)===null||n===void 0||n.reject(o),o}finally{this.refreshingDeferred=null,this._debug(s,"end")}})}_notifyAllSubscribers(e,i,n=!0){return u(this,null,function*(){let s=`#_notifyAllSubscribers(${e})`;this._debug(s,"begin",i,`broadcast = ${n}`);try{this.broadcastChannel&&n&&this.broadcastChannel.postMessage({event:e,session:i});let o=[],a=Array.from(this.stateChangeEmitters.values()).map(l=>u(this,null,function*(){try{yield l.callback(e,i)}catch(d){o.push(d)}}));if(yield Promise.all(a),o.length>0){for(let l=0;l<o.length;l+=1)console.error(o[l]);throw o[0]}}finally{this._debug(s,"end")}})}_saveSession(e){return u(this,null,function*(){this._debug("#_saveSession()",e),yield we(this.storage,this.storageKey,e)})}_removeSession(){return u(this,null,function*(){this._debug("#_removeSession()"),yield Ei(this.storage,this.storageKey)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Q()&&window?.removeEventListener&&window.removeEventListener("visibilitychange",e)}catch(i){console.error("removing visibilitychange callback failed",i)}}_startAutoRefresh(){return u(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),jt);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(()=>u(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return u(this,null,function*(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return u(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>u(this,null,function*(){try{let e=Date.now();try{return yield this._useSession(i=>u(this,null,function*(){let{data:{session:n}}=i;if(!n||!n.refresh_token||!n.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let s=Math.floor((n.expires_at*1e3-e)/jt);this._debug("#_autoRefreshTokenTick()",`access token expires in ${s} ticks, a tick lasts ${jt}ms, refresh threshold is ${Js} ticks`),s<=Js&&(yield this._callRefreshToken(n.refresh_token))}))}catch(i){console.error("Auto refresh tick failed with error. This is likely a transient error.",i)}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(e.isAcquireTimeout||e instanceof Ti)this._debug("auto refresh token tick lock not available");else throw e}})}_handleVisibilityChange(){return u(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!Q()||!window?.addEventListener)return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>u(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window?.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}})}_onVisibilityChanged(e){return u(this,null,function*(){let i=`#_onVisibilityChanged(${e})`;this._debug(i,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(i,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,i,n){return u(this,null,function*(){let s=[`provider=${encodeURIComponent(i)}`];if(n?.redirectTo&&s.push(`redirect_to=${encodeURIComponent(n.redirectTo)}`),n?.scopes&&s.push(`scopes=${encodeURIComponent(n.scopes)}`),this.flowType==="pkce"){let o=Fe();yield we(this.storage,`${this.storageKey}-code-verifier`,o);let a=yield Me(o),l=o===a?"plain":"s256";this._debug("PKCE","code verifier",`${o.substring(0,5)}...`,"code challenge",a,"method",l);let d=new URLSearchParams({code_challenge:`${encodeURIComponent(a)}`,code_challenge_method:`${encodeURIComponent(l)}`});s.push(d.toString())}if(n?.queryParams){let o=new URLSearchParams(n.queryParams);s.push(o.toString())}return n?.skipBrowserRedirect&&s.push(`skip_http_redirect=${n.skipBrowserRedirect}`),`${e}?${s.join("&")}`})}_unenroll(e){return u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n;let{data:s,error:o}=i;return o?{data:null,error:o}:yield _(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(n=s?.session)===null||n===void 0?void 0:n.access_token})}))}catch(i){if(g(i))return{data:null,error:i};throw i}})}_enroll(e){return u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n,s;let{data:o,error:a}=i;if(a)return{data:null,error:a};let{data:l,error:d}=yield _(this.fetch,"POST",`${this.url}/factors`,{body:{friendly_name:e.friendlyName,factor_type:e.factorType,issuer:e.issuer},headers:this.headers,jwt:(n=o?.session)===null||n===void 0?void 0:n.access_token});return d?{data:null,error:d}:(!((s=l?.totp)===null||s===void 0)&&s.qr_code&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})}))}catch(i){if(g(i))return{data:null,error:i};throw i}})}_verify(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n;let{data:s,error:o}=i;if(o)return{data:null,error:o};let{data:a,error:l}=yield _(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(n=s?.session)===null||n===void 0?void 0:n.access_token});return l?{data:null,error:l}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:l})}))}catch(i){if(g(i))return{data:null,error:i};throw i}}))})}_challenge(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(i=>u(this,null,function*(){var n;let{data:s,error:o}=i;return o?{data:null,error:o}:yield _(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{headers:this.headers,jwt:(n=s?.session)===null||n===void 0?void 0:n.access_token})}))}catch(i){if(g(i))return{data:null,error:i};throw i}}))})}_challengeAndVerify(e){return u(this,null,function*(){let{data:i,error:n}=yield this._challenge({factorId:e.factorId});return n?{data:null,error:n}:yield this._verify({factorId:e.factorId,challengeId:i.id,code:e.code})})}_listFactors(){return u(this,null,function*(){let{data:{user:e},error:i}=yield this.getUser();if(i)return{data:null,error:i};let n=e?.factors||[],s=n.filter(o=>o.factor_type==="totp"&&o.status==="verified");return{data:{all:n,totp:s},error:null}})}_getAuthenticatorAssuranceLevel(){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){return yield this._useSession(e=>u(this,null,function*(){var i,n;let{data:{session:s},error:o}=e;if(o)return{data:null,error:o};if(!s)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let a=this._decodeJWT(s.access_token),l=null;a.aal&&(l=a.aal);let d=l;((n=(i=s.user.factors)===null||i===void 0?void 0:i.filter(m=>m.status==="verified"))!==null&&n!==void 0?n:[]).length>0&&(d="aal2");let h=a.amr||[];return{data:{currentLevel:l,nextLevel:d,currentAuthenticationMethods:h},error:null}}))}))})}}return r.nextInstanceID=0,r})();var pl=Dr,Tr=pl;var Fi=class extends Tr{constructor(t){super(t)}};var gl=function(r,t,e,i){function n(s){return s instanceof e?s:new e(function(o){o(s)})}return new(e||(e=Promise))(function(s,o){function a(c){try{d(i.next(c))}catch(h){o(h)}}function l(c){try{d(i.throw(c))}catch(h){o(h)}}function d(c){c.done?s(c.value):n(c.value).then(a,l)}d((i=i.apply(r,t||[])).next())})},Mi=class{constructor(t,e,i){var n,s,o;if(this.supabaseUrl=t,this.supabaseKey=e,!t)throw new Error("supabaseUrl is required.");if(!e)throw new Error("supabaseKey is required.");let a=Ms(t);this.realtimeUrl=`${a}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${a}/auth/v1`,this.storageUrl=`${a}/storage/v1`,this.functionsUrl=`${a}/functions/v1`;let l=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,d={db:ks,realtime:Ts,auth:Object.assign(Object.assign({},Ds),{storageKey:l}),global:Ss},c=Os(i??{},d);this.storageKey=(n=c.auth.storageKey)!==null&&n!==void 0?n:"",this.headers=(s=c.global.headers)!==null&&s!==void 0?s:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:(h,m)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(m)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=c.auth)!==null&&o!==void 0?o:{},this.headers,c.global.fetch),this.fetch=Fs(e,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new fs(`${a}/rest/v1`,{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}get functions(){return new bt(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Mt(this.storageUrl,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,e={},i={}){return this.rest.rpc(t,e,i)}channel(t,e={config:{}}){return this.realtime.channel(t,e)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,e;return gl(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();let{data:i}=yield this.auth.getSession();return(e=(t=i.session)===null||t===void 0?void 0:t.access_token)!==null&&e!==void 0?e:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:e,detectSessionInUrl:i,storage:n,storageKey:s,flowType:o,lock:a,debug:l},d,c){let h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Fi({url:this.authUrl,headers:Object.assign(Object.assign({},h),d),storageKey:s,autoRefreshToken:t,persistSession:e,detectSessionInUrl:i,storage:n,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Tt(this.realtimeUrl,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t?.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,i)=>{this._handleTokenChanged(e,"CLIENT",i?.access_token)})}_handleTokenChanged(t,e,i){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==i?this.changedAccessToken=i:t==="SIGNED_OUT"&&(this.realtime.setAuth(),e=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}};var Ks=(r,t,e)=>new Mi(r,t,e);var Qs=class r{_supabase;userSubject=new Or(null);user$=this.userSubject.asObservable();MAX_RETRIES=1;RETRY_DELAY=1e4;get supabase(){return this._supabase}constructor(){this._supabase=Ks(Li.supabaseUrl,Li.supabaseKey,{auth:{storageKey:"holy-rides-auth",persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,flowType:"pkce",debug:!1}}),this.initializeAuth(),setTimeout(()=>{},2500)}initializeAuth(){return u(this,null,function*(){try{yield new Promise(e=>setTimeout(e,1e3));let{data:{session:t}}=yield this.supabase.auth.getSession();if(t?.user)try{let e=yield this.convertToAppUser(t.user);this.userSubject.next(e),(yield this.getUserRole())||(console.warn("User has no role, setting auth state to null"),this.userSubject.next(null))}catch(e){console.error("Error during user conversion in init:",e),this.userSubject.next(null)}else this.userSubject.next(null);this.supabase.auth.onAuthStateChange((e,i)=>u(this,null,function*(){try{if(i?.user)try{let n=yield this.convertToAppUser(i.user);this.userSubject.next(n),(yield this.getUserRole())||(console.warn("User has no role on auth change, setting auth state to null"),this.userSubject.next(null))}catch(n){console.error("Error during user conversion in auth change:",n),this.userSubject.next(null)}else this.userSubject.next(null)}catch(n){console.error("Auth state change error:",n),this.userSubject.next(null)}}))}catch(t){console.error("Auth initialization error:",t),this.userSubject.next(null)}})}getSessionWithRetry(){return u(this,null,function*(){let t=0,e=this.MAX_RETRIES,i=n=>Math.min(this.RETRY_DELAY*Math.pow(2,n),2e4);for(;t<e;)try{let n=Math.random()*500;return yield new Promise(s=>setTimeout(s,n)),yield this.supabase.auth.getSession()}catch(n){if(t++,console.warn(`Auth session attempt ${t} failed:`,n),t<e){let s=i(t);console.log(`Retrying in ${s}ms... (attempt ${t}/${e})`),yield new Promise(o=>setTimeout(o,s));continue}if(!this.isLockError(n))throw n}console.log("Final attempt with clean session...");try{return yield this.supabase.auth.signOut({scope:"local"}),yield new Promise(n=>setTimeout(n,2e3)),yield this.supabase.auth.getSession()}catch(n){throw console.error("Final session attempt failed:",n),new Error("Failed to acquire auth token after multiple attempts")}})}isLockError(t){if(!(t instanceof Error))return!1;if(t.name==="NavigatorLockAcquireTimeoutError"||t.name==="LockAcquireTimeoutError")return!0;let e=t.message?.toLowerCase()||"";return e.includes("lock")||e.includes("timeout")||e.includes("navigator")||e.includes("acquire")||e.includes("concurrent")}convertToAppUser(t){return u(this,null,function*(){try{let{data:e,error:i}=yield this.supabase.from("profiles").select("*").eq("id",t.id).single();return i||!e?(console.error("Error fetching profile for user conversion:",i),null):{id:t.id,email:t.email||"",full_name:e.full_name,phone:e.phone,avatar_url:e.avatar_url,role:e.role,created_at:e.created_at,updated_at:e.updated_at,is_approved:e.is_approved}}catch(e){return console.error("Error converting Supabase user to app user:",e),null}})}login(t,e){return u(this,null,function*(){try{let i=yield this.supabase.auth.signInWithPassword({email:t,password:e});return{data:i.data,error:i.error}}catch(i){if(this.isLockError(i))return console.log("Lock error during login, retrying..."),this.retryOperation(()=>this.supabase.auth.signInWithPassword({email:t,password:e}));throw i}})}register(t,e,i,n,s){return u(this,null,function*(){try{let{data:o,error:a}=yield this.supabase.auth.signUp({email:t,password:e,options:{data:{role:i,phone:n,full_name:s}}});if(a)return{data:null,error:a};let l=i==="admin"||i==="rider",{error:d}=yield this.supabase.from("profiles").insert([{id:o.user?.id,email:t,phone:n,full_name:s,role:i,created_at:new Date().toISOString(),is_approved:l}]);return{data:o,error:d}}catch(o){if(this.isLockError(o))return this.retryOperation(()=>this.register(t,e,i,n,s));throw o}})}retryOperation(t){return u(this,null,function*(){console.log("Starting retry operation");let e=0,i=this.MAX_RETRIES,n=15e3;for(;e<i;)try{if(e>0){let s=Math.random()*500;yield new Promise(o=>setTimeout(o,s))}return yield t()}catch(s){if(e++,console.warn(`Operation attempt ${e} failed:`,s),this.isLockError(s)||e<i){let o=Math.min(this.RETRY_DELAY*Math.pow(2,e-1),n);console.log(`Retry attempt ${e}/${i}, waiting ${o}ms...`),yield new Promise(a=>setTimeout(a,o));continue}throw s}throw new Error(`Operation failed after ${i} retry attempts`)})}logout(){return u(this,null,function*(){console.log("LOGOUT");try{return console.log("LOGOUT 2"),yield this.supabase.auth.signOut();console.log("LOGOUT 3")}catch(t){if(this.isLockError(t))return console.log("Lock error during logout, retrying...",t),this.retryOperation(()=>this.supabase.auth.signOut());throw t}})}resetPassword(t){return u(this,null,function*(){return this.retryOperation(()=>this.supabase.auth.resetPasswordForEmail(t))})}updatePassword(t){return u(this,null,function*(){try{let e=yield this.retryOperation(()=>this.supabase.auth.updateUser({password:t}));return e.error||!e.data.user?{data:{user:null},error:e.error}:{data:{user:yield this.convertToAppUser(e.data.user)},error:null}}catch(e){return console.error("Error updating password:",e),{data:{user:null},error:e}}})}getUserRole(){return u(this,null,function*(){try{let{data:{session:t}}=yield this.supabase.auth.getSession();if(!t?.user)return null;let{data:e,error:i}=yield this.supabase.from("profiles").select("role").eq("id",t.user.id).single();return i||!e?(console.error("Error fetching user role:",i),null):e.role}catch(t){return console.error("Error in getUserRole:",t),null}})}getDashboardRouteForRole(t){return`/dashboard/${t}`}updateProfile(t){return u(this,null,function*(){try{let e=yield this.getCurrentUser();if(!e)throw new Error("No user logged in");let{error:i}=yield this.supabase.from("profiles").update(t).eq("id",e.id);if(i)throw i;return!0}catch(e){return console.error("Error updating profile:",e),!1}})}getCurrentUser(){return u(this,null,function*(){try{let{data:t,error:e}=yield this.supabase.auth.getUser();return e||!t.user?null:yield this.convertToAppUser(t.user)}catch(t){return console.error("Error in getCurrentUser:",t),null}})}static \u0275fac=function(e){return new(e||r)};static \u0275prov=Y({token:r,factory:r.\u0275fac,providedIn:"root"})};export{_t as a,bn as b,be as c,zt as d,K as e,Il as f,Pl as g,Yi as h,pt as i,Eo as j,jl as k,So as l,Do as m,Xi as n,Mo as o,Po as p,jo as q,No as r,Ll as s,Nl as t,$l as u,$n as v,ir as w,fa as x,ma as y,pa as z,ri as A,ni as B,ya as C,Zn as D,oi as E,si as F,Yn as G,nc as H,sc as I,Qs as J};
