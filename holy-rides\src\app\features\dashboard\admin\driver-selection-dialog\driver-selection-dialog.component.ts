import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { User } from '../../../../core/models/user.model';
import { UserService } from '../../../../core/services/user.service';
import { RatingService } from '../../../../core/services/rating.service';

export interface DriverSelectionDialogData {
  rideId: string;
}

interface DriverUser extends User {
  rating?: number;
}

@Component({
  selector: 'app-driver-selection-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatProgressSpinnerModule
  ],
  template: `
    <h2 mat-dialog-title>Assign Driver</h2>
    <div mat-dialog-content>
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading available drivers...</p>
      </div>
      <div *ngIf="!loading">
        <mat-form-field appearance="outline" style="width: 100%;">
          <mat-label>Select Driver</mat-label>
          <mat-select [(ngModel)]="selectedDriverId" [disabled]="loading">
            <mat-option *ngFor="let driver of availableDrivers" [value]="driver.id">
              {{ driver.full_name || driver.email }}
              <span *ngIf="driver.rating" class="driver-rating">
                ({{ driver.rating.toFixed(1) }} ⭐)
              </span>
            </mat-option>
          </mat-select>
        </mat-form-field>
        <p *ngIf="availableDrivers.length === 0" class="no-drivers-message">
          No approved drivers available at this time.
        </p>
      </div>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()" [disabled]="loading">Cancel</button>
      <button mat-raised-button color="primary" 
              [disabled]="!selectedDriverId || loading" 
              (click)="onAssign()">
        {{ loading ? 'Assigning...' : 'Assign' }}
      </button>
    </div>
  `,
  styles: [`
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    
    .loading-container p {
      margin-top: 10px;
      color: rgba(0, 0, 0, 0.54);
    }
    
    .no-drivers-message {
      color: #f44336;
      font-style: italic;
      text-align: center;
      margin: 16px 0;
    }

    .driver-rating {
      margin-left: 8px;
      color: rgba(0, 0, 0, 0.54);
    }
  `]
})
export class DriverSelectionDialogComponent implements OnInit {
  availableDrivers: DriverUser[] = [];
  selectedDriverId: string = '';
  loading: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<DriverSelectionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DriverSelectionDialogData,
    private userService: UserService,
    private ratingService: RatingService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAvailableDrivers();
  }

  async loadAvailableDrivers() {
    this.loading = true;
    try {
      // Get all users
      const allUsers = await this.userService.getAllUsers();
      
      // Filter for approved drivers
      this.availableDrivers = allUsers.filter(user => 
        user.role === 'driver' && 
        user.is_approved === true
      );

      // Load ratings for each driver
      await Promise.all(this.availableDrivers.map(async driver => {
        try {
          const ratingSummary = await this.ratingService.getUserRatingSummary(driver.id);
          if (ratingSummary) {
            driver.rating = ratingSummary.averageRating;
          }
        } catch (error) {
          console.error(`Error loading rating for driver ${driver.id}:`, error);
        }
      }));

      // Sort drivers by rating (highest first)
      this.availableDrivers.sort((a, b) => (b.rating || 0) - (a.rating || 0));

      if (this.availableDrivers.length === 0) {
        this.snackBar.open('No available drivers found', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error loading drivers:', error);
      this.snackBar.open('Failed to load available drivers', 'Close', { duration: 3000 });
    } finally {
      this.loading = false;
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onAssign(): void {
    if (this.selectedDriverId) {
      if (this.availableDrivers.some(d => d.id === this.selectedDriverId)) {
        this.dialogRef.close(this.selectedDriverId);
      } else {
        this.snackBar.open('Selected driver is no longer available', 'Close', { duration: 3000 });
      }
    } else {
      this.snackBar.open('Please select a driver', 'Close', { duration: 3000 });
    }
  }
}
