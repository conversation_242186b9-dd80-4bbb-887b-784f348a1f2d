{"ast": null, "code": "export class FunctionsError extends Error {\n  constructor(message, name = 'FunctionsError', context) {\n    super(message);\n    this.name = name;\n    this.context = context;\n  }\n}\nexport class FunctionsFetchError extends FunctionsError {\n  constructor(context) {\n    super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n  }\n}\nexport class FunctionsRelayError extends FunctionsError {\n  constructor(context) {\n    super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n  }\n}\nexport class FunctionsHttpError extends FunctionsError {\n  constructor(context) {\n    super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n  }\n}\n// Define the enum for the 'region' property\nexport var FunctionRegion;\n(function (FunctionRegion) {\n  FunctionRegion[\"Any\"] = \"any\";\n  FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n  FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n  FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n  FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n  FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n  FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n  FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n  FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n  FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n  FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n  FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n  FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n  FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n  FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));", "map": {"version": 3, "names": ["FunctionsError", "Error", "constructor", "message", "name", "context", "FunctionsFetchError", "FunctionsRelayError", "FunctionsHttpError", "FunctionRegion"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/functions-js/dist/module/types.js"], "sourcesContent": ["export class FunctionsError extends Error {\n    constructor(message, name = 'FunctionsError', context) {\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nexport class FunctionsFetchError extends FunctionsError {\n    constructor(context) {\n        super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n    }\n}\nexport class FunctionsRelayError extends FunctionsError {\n    constructor(context) {\n        super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n    }\n}\nexport class FunctionsHttpError extends FunctionsError {\n    constructor(context) {\n        super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n    }\n}\n// Define the enum for the 'region' property\nexport var FunctionRegion;\n(function (FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n"], "mappings": "AAAA,OAAO,MAAMA,cAAc,SAASC,KAAK,CAAC;EACtCC,WAAWA,CAACC,OAAO,EAAEC,IAAI,GAAG,gBAAgB,EAAEC,OAAO,EAAE;IACnD,KAAK,CAACF,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA,OAAO,MAAMC,mBAAmB,SAASN,cAAc,CAAC;EACpDE,WAAWA,CAACG,OAAO,EAAE;IACjB,KAAK,CAAC,+CAA+C,EAAE,qBAAqB,EAAEA,OAAO,CAAC;EAC1F;AACJ;AACA,OAAO,MAAME,mBAAmB,SAASP,cAAc,CAAC;EACpDE,WAAWA,CAACG,OAAO,EAAE;IACjB,KAAK,CAAC,wCAAwC,EAAE,qBAAqB,EAAEA,OAAO,CAAC;EACnF;AACJ;AACA,OAAO,MAAMG,kBAAkB,SAASR,cAAc,CAAC;EACnDE,WAAWA,CAACG,OAAO,EAAE;IACjB,KAAK,CAAC,8CAA8C,EAAE,oBAAoB,EAAEA,OAAO,CAAC;EACxF;AACJ;AACA;AACA,OAAO,IAAII,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK;EAC7BA,cAAc,CAAC,cAAc,CAAC,GAAG,gBAAgB;EACjDA,cAAc,CAAC,cAAc,CAAC,GAAG,gBAAgB;EACjDA,cAAc,CAAC,UAAU,CAAC,GAAG,YAAY;EACzCA,cAAc,CAAC,cAAc,CAAC,GAAG,gBAAgB;EACjDA,cAAc,CAAC,cAAc,CAAC,GAAG,gBAAgB;EACjDA,cAAc,CAAC,YAAY,CAAC,GAAG,cAAc;EAC7CA,cAAc,CAAC,YAAY,CAAC,GAAG,cAAc;EAC7CA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;EACvCA,cAAc,CAAC,SAAS,CAAC,GAAG,WAAW;AAC3C,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}