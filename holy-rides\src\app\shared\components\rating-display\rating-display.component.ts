import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { RatingService } from '../../../core/services/rating.service';
import { RatingSummary, Rating } from '../../../core/models/rating.model';
import { UserService } from '../../../core/services/user.service';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-rating-display',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatDividerModule
  ],
  template: `
    <mat-card *ngIf="ratingSummary">
      <mat-card-header>
        <mat-card-title>Ratings & Feedback</mat-card-title>
        <mat-card-subtitle *ngIf="ratingSummary.totalRatings > 0">
          {{ ratingSummary.averageRating | number:'1.1-1' }} stars from {{ ratingSummary.totalRatings }} ratings
        </mat-card-subtitle>
        <mat-card-subtitle *ngIf="ratingSummary.totalRatings === 0">
          No ratings yet
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="rating-stars">
          <mat-icon *ngFor="let star of getStars(ratingSummary.averageRating)" 
                   [ngClass]="star">
            {{ star === 'full' ? 'star' : (star === 'half' ? 'star_half' : 'star_border') }}
          </mat-icon>
        </div>
        
        <mat-divider *ngIf="ratingSummary.recentRatings.length > 0" class="rating-divider"></mat-divider>
        
        <div class="recent-ratings" *ngIf="ratingSummary.recentRatings.length > 0">
          <h3>Recent Feedback</h3>
          <div class="rating-item" *ngFor="let rating of ratingSummary.recentRatings">
            <div class="rating-header">
              <div class="rating-stars small">
                <mat-icon *ngFor="let star of getFullStars(rating.rating)" class="small-icon">
                  star
                </mat-icon>
              </div>
              <div class="rating-info">
                <span class="rating-user">{{ getRaterName(rating.rater_id) }}</span>
                <span class="rating-date">{{ rating.created_at | date }}</span>
              </div>
            </div>
            <p class="rating-feedback" *ngIf="rating.feedback">{{ rating.feedback }}</p>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  `,
  styles: [`
    .rating-stars {
      display: flex;
      margin: 10px 0;
    }
    
    .rating-stars.small {
      margin: 0;
    }
    
    .full {
      color: #ffc107;
    }
    
    .half {
      color: #ffc107;
    }
    
    .empty {
      color: #e0e0e0;
    }
    
    .small-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #ffc107;
    }
    
    .rating-divider {
      margin: 20px 0;
    }
    
    .recent-ratings h3 {
      margin-bottom: 16px;
      font-weight: 500;
    }
    
    .rating-item {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .rating-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .rating-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    
    .rating-user {
      font-weight: 500;
    }
    
    .rating-date {
      font-size: 0.8em;
      color: #757575;
    }
    
    .rating-feedback {
      margin: 0;
      font-style: italic;
      color: #555;
    }
  `]
})
export class RatingDisplayComponent implements OnInit {
  @Input() userId!: string;
  
  ratingSummary: RatingSummary | null = null;
  raterNames: { [key: string]: string } = {};
  
  constructor(
    private ratingService: RatingService,
    private userService: UserService
  ) {}
  
  ngOnInit(): void {
    this.loadRatingSummary();
  }
  
  async loadRatingSummary(): Promise<void> {
    if (!this.userId) return;
    
    try {
      this.ratingSummary = await this.ratingService.getUserRatingSummary(this.userId);
      
      // Load rater names for recent ratings
      if (this.ratingSummary.recentRatings.length > 0) {
        const raterIds = this.ratingSummary.recentRatings.map(rating => rating.rater_id);
        const uniqueRaterIds = [...new Set(raterIds)];
        
        for (const raterId of uniqueRaterIds) {
          const user = await this.userService.getUserById(raterId);
          if (user) {
            this.raterNames[raterId] = user.full_name || user.email;
          }
        }
      }
    } catch (error) {
      console.error('Error loading rating summary:', error);
    }
  }
  
  getStars(rating: number): string[] {
    const stars: string[] = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push('full');
    }
    
    // Add half star if needed
    if (hasHalfStar) {
      stars.push('half');
    }
    
    // Add empty stars
    while (stars.length < 5) {
      stars.push('empty');
    }
    
    return stars;
  }
  
  getFullStars(rating: number): number[] {
    return Array(rating).fill(0);
  }
  
  getRaterName(raterId: string): string {
    return this.raterNames[raterId] || 'Anonymous';
  }
}
