import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';
import { publicGuard } from './core/guards/public.guard';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: '/auth/login'
  },
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent),
        canActivate: [publicGuard]
      },
      {
        path: 'register',
        loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent),
        canActivate: [publicGuard]
      },
      {
        path: 'profile',
        loadComponent: () => import('./features/auth/profile/profile.component').then(m => m.ProfileComponent),
        canActivate: [authGuard]
      }
    ]
  },
  {
    path: 'dashboard',
    canActivate: [authGuard],
    children: [
      {
        path: 'rider',
        loadComponent: () => import('./features/dashboard/rider/rider.component').then(m => m.RiderComponent)
      },
      {
        path: 'driver',
        loadComponent: () => import('./features/dashboard/driver/driver.component').then(m => m.DriverComponent)
      },
      {
        path: 'admin',
        loadComponent: () => import('./features/dashboard/admin/admin.component').then(m => m.AdminComponent)
      },
      {
        path: ':role/messages',
        loadComponent: () => import('./features/dashboard/shared/messages/messages.component').then(m => m.MessagesComponent)
      },
      {
        path: ':role/messages/:threadId',
        loadComponent: () => import('./features/dashboard/shared/messages/messages.component').then(m => m.MessagesComponent)
      },
      {
        path: 'map-test',
        loadComponent: () => import('./shared/components/map-test/map-test.component').then(m => m.MapTestComponent)
      }
    ]
  },
  {
    path: '**',
    redirectTo: '/auth/login'
  }
];
