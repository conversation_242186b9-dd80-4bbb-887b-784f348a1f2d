{"ast": null, "code": "export const environment = {\n  production: false,\n  supabaseUrl: 'https://fiaqzhajyfkjbwushngn.supabase.co',\n  //supabaseUrl: 'http://localhost:8000',\n  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v8UgUwEetoAIwSqMbYSU77pK8ACM7Wryl2BCebW80ZQ',\n  //supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',\n  squareApplicationId: 'sandbox-sq0idb-e6g3DAdm9fjgWfBGyXyk_g',\n  squareLocationId: 'sq0idp-6i0oiAmtrJ-bKkU9PIk9uQ',\n  squareAccessToken: 'EAAAl-JzTB0lwXa7T2FV7NJrF2gK80wgPwlrl3RfH3joiIH3A1HEq57Ak_og5KqY',\n  stripePublishableKey: 'pk_live_51QXPoUKBQR9HiWVf963rrIm0mm1h9v0RanSMS3u4t3OrQh6930W67JsmVn2E7EVR6eBzLhMfejvfJL0umcIbukFW00ELXE0ANo',\n  stripeSecretKey: '',\n  googleMapsApiKey: 'AIzaSyDNfNjbZAkM9p_SmPwc_SZLDyKNMw07bc8',\n  twilioAccountSid: '**********************************',\n  twilioAuthToken: '60bdb78931f35cce4e8f47c3fba3f8ad',\n  twilioPhoneNumber: '+***********' // Replace with your Twilio phone number/ Replace with your Twilio phone number\n};", "map": {"version": 3, "names": ["environment", "production", "supabaseUrl", "supabase<PERSON>ey", "squareApplicationId", "squareLocationId", "squareAccessToken", "stripePublishableKey", "stripeSecretKey", "googleMapsApiKey", "twilioAccountSid", "twilioAuthToken", "twilioPhoneNumber"], "sources": ["C:\\Users\\<USER>\\code\\holy rides\\holy-rides\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\r\n  production: false,\r\n  supabaseUrl:  'https://fiaqzhajyfkjbwushngn.supabase.co',\r\n  //supabaseUrl: 'http://localhost:8000',\r\n  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v8UgUwEetoAIwSqMbYSU77pK8ACM7Wryl2BCebW80ZQ',\r\n  //supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',\r\n  squareApplicationId: 'sandbox-sq0idb-e6g3DAdm9fjgWfBGyXyk_g',\r\n  squareLocationId: 'sq0idp-6i0oiAmtrJ-bKkU9PIk9uQ',\r\n  squareAccessToken: 'EAAAl-JzTB0lwXa7T2FV7NJrF2gK80wgPwlrl3RfH3joiIH3A1HEq57Ak_og5KqY',\r\n  stripePublishableKey: 'pk_live_51QXPoUKBQR9HiWVf963rrIm0mm1h9v0RanSMS3u4t3OrQh6930W67JsmVn2E7EVR6eBzLhMfejvfJL0umcIbukFW00ELXE0ANo',\r\n  stripeSecretKey: '',\r\n  googleMapsApiKey: 'AIzaSyDNfNjbZAkM9p_SmPwc_SZLDyKNMw07bc8',\r\n  \r\n  twilioAccountSid: '**********************************',\r\n  twilioAuthToken: '60bdb78931f35cce4e8f47c3fba3f8ad',\r\n  twilioPhoneNumber: '+***********' // Replace with your Twilio phone number/ Replace with your Twilio phone number\r\n};\r\n\r\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAG,0CAA0C;EACxD;EACAC,WAAW,EAAE,kNAAkN;EAC/N;EACAC,mBAAmB,EAAE,uCAAuC;EAC5DC,gBAAgB,EAAE,+BAA+B;EACjDC,iBAAiB,EAAE,kEAAkE;EACrFC,oBAAoB,EAAE,6GAA6G;EACnIC,eAAe,EAAE,EAAE;EACnBC,gBAAgB,EAAE,yCAAyC;EAE3DC,gBAAgB,EAAE,oCAAoC;EACtDC,eAAe,EAAE,kCAAkC;EACnDC,iBAAiB,EAAE,cAAc,CAAC;CACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}