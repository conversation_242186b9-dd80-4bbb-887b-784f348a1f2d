<div class="login-container">
  <!-- <div class="logo-container">
    <img src="assets/hr.png" alt="Holy Rides Logo" class="logo">

  </div> -->
  <mat-card>
    <mat-card-header>
      <mat-card-title>Login</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" placeholder="Enter your email">
          <mat-error *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</mat-error>
          <mat-error *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Password</mat-label>
          <input matInput type="password" formControlName="password" placeholder="Enter your password">
          <mat-error *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</mat-error>
          <mat-error *ngIf="loginForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</mat-error>
        </mat-form-field>

        <div class="error-message" *ngIf="error">{{ error }}</div>

        <div class="button-container">
          <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || loading">
            {{ loading ? 'Logging in...' : 'Login' }}
          </button>
        </div>

        <div class="links">
          <a routerLink="/auth/register">Don't have an account? Register</a>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
