{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (!predicate.call(thisArg, value, index++, source)) {\n        subscriber.next(false);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "every", "predicate", "thisArg", "source", "subscriber", "index", "subscribe", "value", "call", "next", "complete"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/rxjs/dist/esm/internal/operators/every.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            if (!predicate.call(thisArg, value, index++, source)) {\n                subscriber.next(false);\n                subscriber.complete();\n            }\n        }, () => {\n            subscriber.next(true);\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACtC,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAK;MAC7D,IAAI,CAACN,SAAS,CAACO,IAAI,CAACN,OAAO,EAAEK,KAAK,EAAEF,KAAK,EAAE,EAAEF,MAAM,CAAC,EAAE;QAClDC,UAAU,CAACK,IAAI,CAAC,KAAK,CAAC;QACtBL,UAAU,CAACM,QAAQ,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE,MAAM;MACLN,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC;MACrBL,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}