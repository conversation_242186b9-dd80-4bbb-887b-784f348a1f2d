{"ast": null, "code": "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nexport var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nexport default class RealtimePresence {\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(channel, opts) {\n    this.channel = channel;\n    this.state = {};\n    this.pendingDiffs = [];\n    this.joinRef = null;\n    this.caller = {\n      onJoin: () => {},\n      onLeave: () => {},\n      onSync: () => {}\n    };\n    const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n      state: 'presence_state',\n      diff: 'presence_diff'\n    };\n    this.channel._on(events.state, {}, newState => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      this.joinRef = this.channel._joinRef();\n      this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n      this.pendingDiffs.forEach(diff => {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n      });\n      this.pendingDiffs = [];\n      onSync();\n    });\n    this.channel._on(events.diff, {}, diff => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff);\n      } else {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n        onSync();\n      }\n    });\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences\n      });\n    });\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences\n      });\n    });\n    this.onSync(() => {\n      this.channel._trigger('presence', {\n        event: 'sync'\n      });\n    });\n  }\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  static syncState(currentState, newState, onJoin, onLeave) {\n    const state = this.cloneDeep(currentState);\n    const transformedState = this.transformState(newState);\n    const joins = {};\n    const leaves = {};\n    this.map(state, (key, presences) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences;\n      }\n    });\n    this.map(transformedState, (key, newPresences) => {\n      const currentPresences = state[key];\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(m => m.presence_ref);\n        const curPresenceRefs = currentPresences.map(m => m.presence_ref);\n        const joinedPresences = newPresences.filter(m => curPresenceRefs.indexOf(m.presence_ref) < 0);\n        const leftPresences = currentPresences.filter(m => newPresenceRefs.indexOf(m.presence_ref) < 0);\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences;\n        }\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences;\n        }\n      } else {\n        joins[key] = newPresences;\n      }\n    });\n    return this.syncDiff(state, {\n      joins,\n      leaves\n    }, onJoin, onLeave);\n  }\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  static syncDiff(state, diff, onJoin, onLeave) {\n    const {\n      joins,\n      leaves\n    } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves)\n    };\n    if (!onJoin) {\n      onJoin = () => {};\n    }\n    if (!onLeave) {\n      onLeave = () => {};\n    }\n    this.map(joins, (key, newPresences) => {\n      var _a;\n      const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n      state[key] = this.cloneDeep(newPresences);\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(m => m.presence_ref);\n        const curPresences = currentPresences.filter(m => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n        state[key].unshift(...curPresences);\n      }\n      onJoin(key, currentPresences, newPresences);\n    });\n    this.map(leaves, (key, leftPresences) => {\n      let currentPresences = state[key];\n      if (!currentPresences) return;\n      const presenceRefsToRemove = leftPresences.map(m => m.presence_ref);\n      currentPresences = currentPresences.filter(m => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n      state[key] = currentPresences;\n      onLeave(key, currentPresences, leftPresences);\n      if (currentPresences.length === 0) delete state[key];\n    });\n    return state;\n  }\n  /** @internal */\n  static map(obj, func) {\n    return Object.getOwnPropertyNames(obj).map(key => func(key, obj[key]));\n  }\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  static transformState(state) {\n    state = this.cloneDeep(state);\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key];\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map(presence => {\n          presence['presence_ref'] = presence['phx_ref'];\n          delete presence['phx_ref'];\n          delete presence['phx_ref_prev'];\n          return presence;\n        });\n      } else {\n        newState[key] = presences;\n      }\n      return newState;\n    }, {});\n  }\n  /** @internal */\n  static cloneDeep(obj) {\n    return JSON.parse(JSON.stringify(obj));\n  }\n  /** @internal */\n  onJoin(callback) {\n    this.caller.onJoin = callback;\n  }\n  /** @internal */\n  onLeave(callback) {\n    this.caller.onLeave = callback;\n  }\n  /** @internal */\n  onSync(callback) {\n    this.caller.onSync = callback;\n  }\n  /** @internal */\n  inPendingSyncState() {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef();\n  }\n}", "map": {"version": 3, "names": ["REALTIME_PRESENCE_LISTEN_EVENTS", "RealtimePresence", "constructor", "channel", "opts", "state", "pendingDiffs", "joinRef", "caller", "onJoin", "onLeave", "onSync", "events", "diff", "_on", "newState", "_joinRef", "syncState", "for<PERSON>ach", "syncDiff", "inPendingSyncState", "push", "key", "currentPresences", "newPresences", "_trigger", "event", "leftPresences", "currentState", "cloneDeep", "transformedState", "transformState", "joins", "leaves", "map", "presences", "newPresenceRefs", "m", "presence_ref", "curPresenceRefs", "joinedPresences", "filter", "indexOf", "length", "_a", "joinedPresenceRefs", "curPresences", "unshift", "presenceRefsToRemove", "obj", "func", "Object", "getOwnPropertyNames", "reduce", "metas", "presence", "JSON", "parse", "stringify", "callback"], "sources": ["C:/Users/<USER>/code/holy rides/holy-rides/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js"], "sourcesContent": ["/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nexport var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nexport default class RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,+BAA+B;AAC1C,CAAC,UAAUA,+BAA+B,EAAE;EACxCA,+BAA+B,CAAC,MAAM,CAAC,GAAG,MAAM;EAChDA,+BAA+B,CAAC,MAAM,CAAC,GAAG,MAAM;EAChDA,+BAA+B,CAAC,OAAO,CAAC,GAAG,OAAO;AACtD,CAAC,EAAEA,+BAA+B,KAAKA,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,eAAe,MAAMC,gBAAgB,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;IACvB,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,MAAM,GAAG;MACVC,MAAM,EAAEA,CAAA,KAAM,CAAE,CAAC;MACjBC,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAC;MAClBC,MAAM,EAAEA,CAAA,KAAM,CAAE;IACpB,CAAC;IACD,MAAMC,MAAM,GAAG,CAACR,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,MAAM,KAAK;MACxEP,KAAK,EAAE,gBAAgB;MACvBQ,IAAI,EAAE;IACV,CAAC;IACD,IAAI,CAACV,OAAO,CAACW,GAAG,CAACF,MAAM,CAACP,KAAK,EAAE,CAAC,CAAC,EAAGU,QAAQ,IAAK;MAC7C,MAAM;QAAEN,MAAM;QAAEC,OAAO;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACH,MAAM;MAC/C,IAAI,CAACD,OAAO,GAAG,IAAI,CAACJ,OAAO,CAACa,QAAQ,CAAC,CAAC;MACtC,IAAI,CAACX,KAAK,GAAGJ,gBAAgB,CAACgB,SAAS,CAAC,IAAI,CAACZ,KAAK,EAAEU,QAAQ,EAAEN,MAAM,EAAEC,OAAO,CAAC;MAC9E,IAAI,CAACJ,YAAY,CAACY,OAAO,CAAEL,IAAI,IAAK;QAChC,IAAI,CAACR,KAAK,GAAGJ,gBAAgB,CAACkB,QAAQ,CAAC,IAAI,CAACd,KAAK,EAAEQ,IAAI,EAAEJ,MAAM,EAAEC,OAAO,CAAC;MAC7E,CAAC,CAAC;MACF,IAAI,CAACJ,YAAY,GAAG,EAAE;MACtBK,MAAM,CAAC,CAAC;IACZ,CAAC,CAAC;IACF,IAAI,CAACR,OAAO,CAACW,GAAG,CAACF,MAAM,CAACC,IAAI,EAAE,CAAC,CAAC,EAAGA,IAAI,IAAK;MACxC,MAAM;QAAEJ,MAAM;QAAEC,OAAO;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACH,MAAM;MAC/C,IAAI,IAAI,CAACY,kBAAkB,CAAC,CAAC,EAAE;QAC3B,IAAI,CAACd,YAAY,CAACe,IAAI,CAACR,IAAI,CAAC;MAChC,CAAC,MACI;QACD,IAAI,CAACR,KAAK,GAAGJ,gBAAgB,CAACkB,QAAQ,CAAC,IAAI,CAACd,KAAK,EAAEQ,IAAI,EAAEJ,MAAM,EAAEC,OAAO,CAAC;QACzEC,MAAM,CAAC,CAAC;MACZ;IACJ,CAAC,CAAC;IACF,IAAI,CAACF,MAAM,CAAC,CAACa,GAAG,EAAEC,gBAAgB,EAAEC,YAAY,KAAK;MACjD,IAAI,CAACrB,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAC9BC,KAAK,EAAE,MAAM;QACbJ,GAAG;QACHC,gBAAgB;QAChBC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACd,OAAO,CAAC,CAACY,GAAG,EAAEC,gBAAgB,EAAEI,aAAa,KAAK;MACnD,IAAI,CAACxB,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAC9BC,KAAK,EAAE,OAAO;QACdJ,GAAG;QACHC,gBAAgB;QAChBI;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAChB,MAAM,CAAC,MAAM;MACd,IAAI,CAACR,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAC,CAAC;IACxD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOT,SAASA,CAACW,YAAY,EAAEb,QAAQ,EAAEN,MAAM,EAAEC,OAAO,EAAE;IACtD,MAAML,KAAK,GAAG,IAAI,CAACwB,SAAS,CAACD,YAAY,CAAC;IAC1C,MAAME,gBAAgB,GAAG,IAAI,CAACC,cAAc,CAAChB,QAAQ,CAAC;IACtD,MAAMiB,KAAK,GAAG,CAAC,CAAC;IAChB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,GAAG,CAAC7B,KAAK,EAAE,CAACiB,GAAG,EAAEa,SAAS,KAAK;MAChC,IAAI,CAACL,gBAAgB,CAACR,GAAG,CAAC,EAAE;QACxBW,MAAM,CAACX,GAAG,CAAC,GAAGa,SAAS;MAC3B;IACJ,CAAC,CAAC;IACF,IAAI,CAACD,GAAG,CAACJ,gBAAgB,EAAE,CAACR,GAAG,EAAEE,YAAY,KAAK;MAC9C,MAAMD,gBAAgB,GAAGlB,KAAK,CAACiB,GAAG,CAAC;MACnC,IAAIC,gBAAgB,EAAE;QAClB,MAAMa,eAAe,GAAGZ,YAAY,CAACU,GAAG,CAAEG,CAAC,IAAKA,CAAC,CAACC,YAAY,CAAC;QAC/D,MAAMC,eAAe,GAAGhB,gBAAgB,CAACW,GAAG,CAAEG,CAAC,IAAKA,CAAC,CAACC,YAAY,CAAC;QACnE,MAAME,eAAe,GAAGhB,YAAY,CAACiB,MAAM,CAAEJ,CAAC,IAAKE,eAAe,CAACG,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC/F,MAAMX,aAAa,GAAGJ,gBAAgB,CAACkB,MAAM,CAAEJ,CAAC,IAAKD,eAAe,CAACM,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjG,IAAIE,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;UAC5BX,KAAK,CAACV,GAAG,CAAC,GAAGkB,eAAe;QAChC;QACA,IAAIb,aAAa,CAACgB,MAAM,GAAG,CAAC,EAAE;UAC1BV,MAAM,CAACX,GAAG,CAAC,GAAGK,aAAa;QAC/B;MACJ,CAAC,MACI;QACDK,KAAK,CAACV,GAAG,CAAC,GAAGE,YAAY;MAC7B;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAACL,QAAQ,CAACd,KAAK,EAAE;MAAE2B,KAAK;MAAEC;IAAO,CAAC,EAAExB,MAAM,EAAEC,OAAO,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOS,QAAQA,CAACd,KAAK,EAAEQ,IAAI,EAAEJ,MAAM,EAAEC,OAAO,EAAE;IAC1C,MAAM;MAAEsB,KAAK;MAAEC;IAAO,CAAC,GAAG;MACtBD,KAAK,EAAE,IAAI,CAACD,cAAc,CAAClB,IAAI,CAACmB,KAAK,CAAC;MACtCC,MAAM,EAAE,IAAI,CAACF,cAAc,CAAClB,IAAI,CAACoB,MAAM;IAC3C,CAAC;IACD,IAAI,CAACxB,MAAM,EAAE;MACTA,MAAM,GAAGA,CAAA,KAAM,CAAE,CAAC;IACtB;IACA,IAAI,CAACC,OAAO,EAAE;MACVA,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;IACvB;IACA,IAAI,CAACwB,GAAG,CAACF,KAAK,EAAE,CAACV,GAAG,EAAEE,YAAY,KAAK;MACnC,IAAIoB,EAAE;MACN,MAAMrB,gBAAgB,GAAG,CAACqB,EAAE,GAAGvC,KAAK,CAACiB,GAAG,CAAC,MAAM,IAAI,IAAIsB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;MAC9EvC,KAAK,CAACiB,GAAG,CAAC,GAAG,IAAI,CAACO,SAAS,CAACL,YAAY,CAAC;MACzC,IAAID,gBAAgB,CAACoB,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAME,kBAAkB,GAAGxC,KAAK,CAACiB,GAAG,CAAC,CAACY,GAAG,CAAEG,CAAC,IAAKA,CAAC,CAACC,YAAY,CAAC;QAChE,MAAMQ,YAAY,GAAGvB,gBAAgB,CAACkB,MAAM,CAAEJ,CAAC,IAAKQ,kBAAkB,CAACH,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;QACnGjC,KAAK,CAACiB,GAAG,CAAC,CAACyB,OAAO,CAAC,GAAGD,YAAY,CAAC;MACvC;MACArC,MAAM,CAACa,GAAG,EAAEC,gBAAgB,EAAEC,YAAY,CAAC;IAC/C,CAAC,CAAC;IACF,IAAI,CAACU,GAAG,CAACD,MAAM,EAAE,CAACX,GAAG,EAAEK,aAAa,KAAK;MACrC,IAAIJ,gBAAgB,GAAGlB,KAAK,CAACiB,GAAG,CAAC;MACjC,IAAI,CAACC,gBAAgB,EACjB;MACJ,MAAMyB,oBAAoB,GAAGrB,aAAa,CAACO,GAAG,CAAEG,CAAC,IAAKA,CAAC,CAACC,YAAY,CAAC;MACrEf,gBAAgB,GAAGA,gBAAgB,CAACkB,MAAM,CAAEJ,CAAC,IAAKW,oBAAoB,CAACN,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;MACnGjC,KAAK,CAACiB,GAAG,CAAC,GAAGC,gBAAgB;MAC7Bb,OAAO,CAACY,GAAG,EAAEC,gBAAgB,EAAEI,aAAa,CAAC;MAC7C,IAAIJ,gBAAgB,CAACoB,MAAM,KAAK,CAAC,EAC7B,OAAOtC,KAAK,CAACiB,GAAG,CAAC;IACzB,CAAC,CAAC;IACF,OAAOjB,KAAK;EAChB;EACA;EACA,OAAO6B,GAAGA,CAACe,GAAG,EAAEC,IAAI,EAAE;IAClB,OAAOC,MAAM,CAACC,mBAAmB,CAACH,GAAG,CAAC,CAACf,GAAG,CAAEZ,GAAG,IAAK4B,IAAI,CAAC5B,GAAG,EAAE2B,GAAG,CAAC3B,GAAG,CAAC,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOS,cAAcA,CAAC1B,KAAK,EAAE;IACzBA,KAAK,GAAG,IAAI,CAACwB,SAAS,CAACxB,KAAK,CAAC;IAC7B,OAAO8C,MAAM,CAACC,mBAAmB,CAAC/C,KAAK,CAAC,CAACgD,MAAM,CAAC,CAACtC,QAAQ,EAAEO,GAAG,KAAK;MAC/D,MAAMa,SAAS,GAAG9B,KAAK,CAACiB,GAAG,CAAC;MAC5B,IAAI,OAAO,IAAIa,SAAS,EAAE;QACtBpB,QAAQ,CAACO,GAAG,CAAC,GAAGa,SAAS,CAACmB,KAAK,CAACpB,GAAG,CAAEqB,QAAQ,IAAK;UAC9CA,QAAQ,CAAC,cAAc,CAAC,GAAGA,QAAQ,CAAC,SAAS,CAAC;UAC9C,OAAOA,QAAQ,CAAC,SAAS,CAAC;UAC1B,OAAOA,QAAQ,CAAC,cAAc,CAAC;UAC/B,OAAOA,QAAQ;QACnB,CAAC,CAAC;MACN,CAAC,MACI;QACDxC,QAAQ,CAACO,GAAG,CAAC,GAAGa,SAAS;MAC7B;MACA,OAAOpB,QAAQ;IACnB,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;EACA;EACA,OAAOc,SAASA,CAACoB,GAAG,EAAE;IAClB,OAAOO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACT,GAAG,CAAC,CAAC;EAC1C;EACA;EACAxC,MAAMA,CAACkD,QAAQ,EAAE;IACb,IAAI,CAACnD,MAAM,CAACC,MAAM,GAAGkD,QAAQ;EACjC;EACA;EACAjD,OAAOA,CAACiD,QAAQ,EAAE;IACd,IAAI,CAACnD,MAAM,CAACE,OAAO,GAAGiD,QAAQ;EAClC;EACA;EACAhD,MAAMA,CAACgD,QAAQ,EAAE;IACb,IAAI,CAACnD,MAAM,CAACG,MAAM,GAAGgD,QAAQ;EACjC;EACA;EACAvC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,CAAC,IAAI,CAACb,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,IAAI,CAACJ,OAAO,CAACa,QAAQ,CAAC,CAAC;EACpE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}